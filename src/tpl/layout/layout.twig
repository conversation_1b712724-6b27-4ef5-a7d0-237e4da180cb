<!DOCTYPE html>
<html lang="cs" class="no-js">
	<head>
		<meta charset="utf-8">
		<!--[if IE]><meta http-equiv="X-UA-Compatible" content="IE=edge"><![endif]-->
		<meta name="description" content="">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">

		<meta name="twitter:card" content="summary_large_image">
		<meta property="og:title" content="{{ pageTitle }} | Project">
		<meta property="og:description" content="Lorem ipsum dolor sit amet, consectetur adipisicing elit">
		<meta property="og:image" content="http://via.placeholder.com/1200x630">
		<meta property="og:site_name" content="Project">
		<meta property="og:url" content="http://www.example.com/">

		{# Change theme color if you can, default now is SK primary color #}
		<meta name="theme-color" content="#3cdbc0">
		{# You may specify color by theme #}
		{#	<meta name="theme-color" media="(prefers-color-scheme: light)" content="#3cdbc0"> #}
		{#	<meta name="theme-color" media="(prefers-color-scheme: dark)" content="#9b26b6"> #}

		<title>{{ pageTitle }} | Project</title>

		{# Use only when you need to polyfill something special. All listed features are supported by default now in our browserlist. #}
		{# https://cdn.polyfill.io/v3/polyfill.min.js?features=default,Array.prototype.includes,Object.values,Array.prototype.find,AbortController,fetch #}
		{% set scripts = [
			assets.scripts ~ 'app.js'
		] %}
		{% for script in scripts %}
		<link rel="preload" as="script" href="{{script}}">
		{% endfor %}

		{# use media="screen,handheld" if you need support opera mini #}
		<link rel="stylesheet" href="{{ assets.styles }}style.css">
		<link rel="stylesheet" href="{{ assets.styles }}print.css" media="print">

		<script>
			(function () {
				var className = document.documentElement.className;
				className = className.replace('no-js', 'js');

				(function() {
					var mediaHover = window.matchMedia('(hover: none), (pointer: coarse), (pointer: none)');
					mediaHover.addListener(function(media) {
						document.documentElement.classList[media.matches ? 'add' : 'remove']('no-hoverevents');
						document.documentElement.classList[!media.matches ? 'add' : 'remove']('hoverevents');
					});
					className += (mediaHover.matches ? ' no-hoverevents' : ' hoverevents');
				})();

				// var supportsCover = 'CSS' in window && typeof CSS.supports === 'function' && CSS.supports('object-fit: cover');
				// className += (supportsCover ? ' ' : ' no-') + 'objectfit';

				// fix iOS zoom issue: https://docs.google.com/document/d/1KclJmXyuuErcvit-kwCC6K2J7dClRef43oyGVCqWxFE/edit#heading=h.sgbqg5nzhvu9
				var ua = navigator.userAgent.toLowerCase();
				var isIOS = /ipad|iphone|ipod/.test(ua) && !window.MSStream;

				if (isIOS === true) {
					var viewportTag = document.querySelector("meta[name=viewport]");
					viewportTag.setAttribute("content", "width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no");
				}

				document.documentElement.className = className;
			}());
		</script>

		<link rel="shortcut icon" href="{{ assets.images }}favicon.ico">
	</head>
	<body>
		<p class="m-accessibility">
			<a title="Přejít k obsahu (Klávesová zkratka: Alt + 2)" accesskey="2" href="#main">Přejít k obsahu</a>
			<span class="hide">|</span>
			<a href="#menu-main">Přejít k hlavnímu menu</a>
			<span class="hide">|</span>
			<a href="#form-search">Přejít k vyhledávání</a>
		</p>

		<header class="header">
			<div class="row-main">
				{% if isHomepage is defined %}
					<h1 class="header__logo">
						<img src="{{ assets.images }}logo-superkoderi.svg" alt="SUPERKODERS" width="300" height="19">
					</h1>
				{% else %}
					<p class="header__logo">
						<a href="./">
							<img src="{{ assets.images }}logo-superkoderi.svg" alt="SUPERKODERS" width="300" height="19">
						</a>
					</p>
				{% endif %}

				<nav id="menu-main" class="m-main">
					<ul class="m-main__list">
						<li class="m-main__item">
							<a href="#" class="m-main__link">
								Úvod
							</a>
						</li>
						<li class="m-main__item">
							<a href="#" class="m-main__link">
								Kábrman roku
							</a>
						</li>
						<li class="m-main__item">
							<a href="#" class="m-main__link">
								Poradna
							</a>
						</li>
						<li class="m-main__item">
							<a href="#" class="m-main__link">
								Pro kutily
							</a>
						</li>
						<li class="m-main__item">
							<a href="#" class="m-main__link">
								Kontakt
							</a>
						</li>
					</ul>
				</nav>

				<form action="?" class="f-search"></form>
			</div>
		</header>

		{% block content %}{% endblock %}

		<footer class="footer">
			<div class="row-main">
				<p class="center">
					&copy; 2016 SUPERKODERS
				</p>
			</div>
		</footer>

		{% for script in scripts %}
		<script src="{{script}}"></script>
		{% endfor %}
		<script>
			App.run({})
		</script>
	</body>
</html>
