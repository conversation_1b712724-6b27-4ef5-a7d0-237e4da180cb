import { Application } from '@hotwired/stimulus';
import 'lite-youtube-embed';
import 'lite-vimeo-embed';
import { afterPaint } from '@superkoders/sk-tools/src/timer';

// Cookie
import * as cookie from '@superkoders/cookie/src/js/components/cookie.js';
window.SKcookieAPI = cookie;
cookie.init();

window.App = {
	run(options) {
		window.App.options = options;
		// Load controllers
		const context = require.context('./controllers', false, /\.(js|ts)$/);
		let controllers = context
			.keys()
			.map((key) => {
				const identifier = (key.match(/^(?:\.\/)?(.+)(?:\..+?)$/) || [])[1];
				const module = context(key);
				// export from module create method if you need pass options
				const controllerConstructor = module?.create?.(options) || module?.default;

				if (identifier && typeof controllerConstructor === 'function') {
					return { identifier, controllerConstructor };
				}
			})
			.filter((value) => value);

		// list of controllers
		// console.log(controllers);

		// you can filter only some by name
		// controllers = controllers.filter(({ identifier }) => ['test'].includes(identifier));

		// Long tasks split
		const longTaskControllerNames = ['embla', 'modal', 'tooltip'];
		var controllersNonLongTasks = controllers.filter(({ identifier }) => !longTaskControllerNames.includes(identifier));
		var controllersLongTasks = controllers.filter(({ identifier }) => longTaskControllerNames.includes(identifier));

		// Start aplication on next frame to control FID
		requestAnimationFrame(() => {
			const application = Application.start();
			application.load(controllersNonLongTasks);

			afterPaint(() => {
				application.load(controllersLongTasks);
			});
		});

		if (options.exponeaTrack) {
			window.SKcookieAPI.on('update', function (e) {
				let consent = window.SKcookieAPI.getCurrentState();
				exponea.track('consent', {
					category   : 'cookie',
					action     : consent.storages.ad_storage === 'granted' ? 'accept' : 'reject',
					valid_until: 'unlimited',
				});
			});
		}
	},
};
