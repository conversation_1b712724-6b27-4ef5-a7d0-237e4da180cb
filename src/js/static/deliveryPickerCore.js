(()=>{"use strict";let e={};window.markersCache=window.markersCache||{};const t=async(e,t)=>{let r=[];try{const i=await fetch(e,{method:"GET"});i.ok&&(r=await i.json(),t&&"object"==typeof r&&(n=r,r=t.split(".").reduce(((e,t)=>e[t]),n)),window.markersCache[e]=r)}catch(e){console.warn(e)}var n;return r};let r=null;const n={init:async n=>{document.addEventListener("skpickertriggerclick",(t=>{var n;const i=document.getElementById("sk-delivery-picker");var a,c;t.detail.pluginPath&&!i&&(null===(a=t.detail.pluginPath,n=r||(e[c=a]||(e[c]=new Promise(((e,t)=>{const r=document.createElement("script");r.type="text/javascript",r.async=!0,r.onload=e,r.onerror=t,r.src=c,document.head.appendChild(r)}))),r=e[c],r))||void 0===n||n.then((()=>{var e;null===(e=window.SkDeliveryPickerPlugin)||void 0===e||e.init({...t.detail,triggerElement:document.activeElement})})))})),n&&n.markersApiUrl&&(async e=>{const{markersApiUrl:r,pickupPointsResponsePath:n}=e;let i=[];if(Array.isArray(r))r.forEach((e=>t(e,n)));else{if(window.markersCache[r])return window.markersCache[r];i=await t(r,n)}})(n)}};window.SkDeliveryPicker=n})();