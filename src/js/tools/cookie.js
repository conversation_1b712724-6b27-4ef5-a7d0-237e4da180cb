export const setCookie = (name, value, options = {}) => {
	options = {
		path: '/',
		...options,
	};

	if (options.expires instanceof Date) {
		options.expires = options.expires.toUTCString();
	}

	let updatedCookie = encodeURIComponent(name) + '=' + encodeURIComponent(value);

	for (let optionKey in options) {
		updatedCookie += '; ' + optionKey;
		let optionValue = options[optionKey];
		if (optionValue !== true) {
			updatedCookie += '=' + optionValue;
		}
	}

	console.log(updatedCookie);

	document.cookie = updatedCookie;
};

export const deleteCookie = (name) => {
	setCookie(name, '', {
		'max-age': -1,
	});
};

export const hasCookie = (name) => {
	return document.cookie.indexOf(`${name}=`) > -1;
};

export const getCookie = (name) => {
	/* eslint-disable no-useless-escape */
	let matches = document.cookie.match(new RegExp('(?:^|; )' + name.replace(/([\.$?*|{}\(\)\[\]\\\/\+^])/g, '\\$1') + '=([^;]*)'));
	return matches ? decodeURIComponent(matches[1]) : undefined;
};
