import loadScript from '@superkoders/sk-tools/src/loadScript';

let api = null;

export default () => {
	if (api) return api;

	const { options } = window.App;

	api = new Promise((resolve, reject) => {
		window.initGmapsApi = () => {
			// utility libraries
			Promise.all([loadScript(`${options.assetsUrl}js/gmaps-markerclusterer.js`)])
				.then(() => {
					resolve(window.google.maps);
				})
				.catch(reject);

			// pokud neni potreba zadna utility library (clustery, infobox),
			// tak odstranit Promise.all(...) a odkomentovat nasledujici radek
			// resolve();
			delete window.initGmapsApi;
		};

		loadScript(`https://maps.googleapis.com/maps/api/js?key=${options.apiKey}&callback=initGmapsApi`).catch(reject);
	});

	return api;
};
