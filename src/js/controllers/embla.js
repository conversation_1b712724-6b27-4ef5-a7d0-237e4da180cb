import { Controller } from '@hotwired/stimulus';
import { useIntersection, useWindowResize } from 'stimulus-use';
import { disablePrevNextBtns } from './carousel/prevAndNextButtons';
import { setupDotBtns, generateDotBtns, selectDotBtn, disableDots } from './carousel/dotButtons';
import { MQ } from '../tools/MQ';

export const create = () => {
	return class extends Controller {
		static targets = ['prevButton', 'nextButton', 'viewport', 'dots', 'progress'];
		static values = { init: String, settings: Object, autoplay: Number };

		async connect() {
			this.emblaCarousel = (await import(/* webpackPrefetch: true */ 'embla-carousel')).default;
			useIntersection(this);
			useWindowResize(this);
		}

		appear = () => {
			var shouldInit = this.initValue ? MQ(this.initValue) : true;
			if (!this.carousel && shouldInit) {
				this.element.classList.add('is-initialized');

				const optionsDefault = {
					loop: false,
					align: 'start',
					containScroll: 'trimSnaps',
					skipSnaps: true,
					speed: 20,
					...this.settingsValue,
				};

				this.carousel = this.emblaCarousel(this.viewportTarget, optionsDefault);
				this.slides = this.carousel.slideNodes();

				const { update, setActiveSlides, loadNext, setActiveState, setProgress, startAutoplay, stopAutoplay } = this;
				this.carousel.on('init', () => update());
				this.carousel.on('reInit', () => update());
				this.carousel.on('select', () => {
					setActiveSlides();
					setProgress();
					loadNext();
					if (this.hasAutoplayValue) {
						this.startAutoplay();
					}
				});

				setActiveState();
				loadNext();

				if (this.hasAutoplayValue) {
					this.startAutoplay();
					// this.carousel.on('pointerDown', () => this.stopAutoplay());
					// this.carousel.on('pointerUp', () => this.startAutoplay());
				}
			} else if (this.carousel && !shouldInit) {
				// Destroy
				this.element.classList.remove('is-initialized');
				this.carousel.destroy();
				this.carousel = null;
			}
		};

		startAutoplay = () => {
			if (this.autoplayTimer) clearInterval(this.autoplayTimer);
			let startTime = Date.now();
			this.autoplayTimer = setInterval(() => {
				const elapsed = Date.now() - startTime;
				const progress = (elapsed / this.autoplayValue) * 100;
				this.element.style.setProperty('--slide-progress', `${Math.min(progress, 100).toFixed(2)}%`);

				if (elapsed >= this.autoplayValue) {
					startTime = Date.now();
					if (this.carousel.selectedScrollSnap() === this.carousel.scrollSnapList().length - 1) {
						this.carousel.scrollTo(0);
					} else {
						this.carousel.scrollNext();
					}
				}
			}, 100);
		};

		// stopAutoplay = () => {
		// 	if (this.autoplayTimer) clearInterval(this.autoplayTimer);
		// };

		windowResize() {
			if (this.carousel) {
				this.setActiveState();
			}
		}

		setActiveState = () => {
			this.isScrollable = this.carousel.internalEngine().scrollSnaps.length > 1;
			this.carousel.reInit({ active: this.isScrollable });
			this.element.classList[this.isScrollable ? 'remove' : 'add']('is-disabled');
		};
		setProgress = () => {
			if (this.hasProgressTarget) {
				const slideNodes = this.carousel.slideNodes(); // Všechny slide elementy
				const totalSlides = slideNodes.length; // Celkový počet slidů
				const visibleSlides = Math.round(this.carousel.rootNode().offsetWidth / slideNodes[0].offsetWidth); // Počet viditelných slidů
				const selectedIndex = this.carousel.selectedScrollSnap(); // Aktuální snap index

				// Výpočet progressu
				const progress = ((selectedIndex + visibleSlides) / totalSlides) * 100;
				this.progressTarget.style.width = `${progress}%`;
			}
		};
		update = () => {
			// arrows
			if (this.hasPrevButtonTarget && this.hasNextButtonTarget) {
				var disablePrevAndNextBtns = disablePrevNextBtns(this.prevButtonTarget, this.nextButtonTarget, this.carousel);
				disablePrevAndNextBtns();
				this.carousel.on('select', disablePrevAndNextBtns);
			}

			// dots
			if (this.hasDotsTarget) {
				var dotsArray = generateDotBtns(this.dotsTarget, this.carousel);
				var setSelectedDotBtn = selectDotBtn(dotsArray, this.carousel);
				var disableAllDots = disableDots(this.dotsTarget, this.carousel);
				setupDotBtns(dotsArray, this.carousel);
				setSelectedDotBtn();
				disableAllDots();
				this.carousel.on('select', setSelectedDotBtn);
			}

			// progress
			this.setProgress();
		};

		setActiveSlides = () => {
			this.slides.forEach((slide, i) => slide.classList[this.carousel.selectedScrollSnap() == i ? 'add' : 'remove']('is-active'));
		};

		loadNext = () => {
			this.slides[this.carousel.selectedScrollSnap() + 1]?.querySelector('[loading="lazy"]')?.removeAttribute('loading');
		};

		prev(event) {
			event.preventDefault();
			this.carousel.scrollPrev();
		}

		next(event) {
			event.preventDefault();
			this.carousel.scrollNext();
		}
	};
};
