import { Controller } from '@hotwired/stimulus';
import { throttle } from 'lodash';

export const create = () => {
	return class extends Controller {
		connect() {
			this.checkSticky = throttle(this.checkSticky.bind(this), 100, { leading: true, trailing: true });
			window.addEventListener('scroll', this.checkSticky);
			setTimeout(this.checkSticky, 0);
		}

		checkSticky() {
			const { top } = this.element.getBoundingClientRect();
			const isStickyTop = top <= 0;
			const isStickyLeft = window.scrollX > 0;

			this.element.classList.toggle('is-sticky-top', isStickyTop);
			this.element.classList.toggle('is-sticky-left', isStickyLeft);
		}
	};
};
