import { Controller } from '@hotwired/stimulus';
import { useWindowResize } from 'stimulus-use';

function isOverflowing(element, container = element.parentElement) {
	const contRect = container.getBoundingClientRect();
	const elRect = element.getBoundingClientRect();

	return (
		elRect.right > contRect.right || // Přesah vpravo
		elRect.top + elRect.height > contRect.bottom // Zalomený pod spodní hranici
	);
}

export const create = () => {
	return class extends Controller {
		static targets = ['list', 'showAllBtn'];

		connect = () => {
			this.items = Array.from(this.listTarget.children);
			useWindowResize(this);
		};
		windowResize = () => {
			this.element.classList.add('is-limited');
			this.items.forEach((item) => item.classList.remove('u-d-n'));
			this.showAllBtnTarget.classList.add('tw-invisible');
			this.hasHiddenItems = false;

			if (!isOverflowing(this.showAllBtnTarget, this.element)) {
				return;
			}

			for (let i = this.items.length - 1; i >= 0; i--) {
				this.items[i].classList.add('u-d-n');
				this.hasHiddenItems = true;

				if (!isOverflowing(this.showAllBtnTarget, this.element)) {
					if (i === this.items.length - 1) {
						const lastItem = this.items[i];
						lastItem.classList.remove('u-d-n');
						if (!isOverflowing(lastItem, this.element)) {
							this.hasHiddenItems = false;
						} else {
							lastItem.classList.add('u-d-n');
						}
					}
					break;
				}
			}

			if (this.hasHiddenItems) {
				this.showAllBtnTarget.classList.remove('tw-invisible');
			}
		};

		toggleTabs = () => {
			if (this.element.classList.contains('is-limited')) {
				this.items.forEach((item) => item.classList.remove('u-d-n'));
				this.hasHiddenItems = false;
				this.element.classList.remove('is-limited');
			} else {
				this.windowResize();
			}
		};
	};
};
