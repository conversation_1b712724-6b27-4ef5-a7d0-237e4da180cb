export const setupDotBtns = (dotsArray, embla) => {
	dotsArray.forEach((dotNode, i) => {
		dotNode.addEventListener('click', () => embla.scrollTo(i), false);
	});
};

export const generateDotBtns = (dots, embla) => {
	const template = document.querySelector('.embla-dot-template').innerHTML;
	dots.innerHTML = embla.scrollSnapList().reduce((acc, _, i) => {
		const buttonHTML = template.replace('%i', i + 1);
		return acc + buttonHTML;
	}, '');
	return [].slice.call(dots.querySelectorAll('.embla__dot'));
};

export const selectDotBtn = (dotsArray, embla) => () => {
	const previous = embla.previousScrollSnap();
	const selected = embla.selectedScrollSnap();
	dotsArray[previous]?.classList.remove('is-selected');
	dotsArray[selected]?.classList.add('is-selected');
};

export const disableDots = (dots, embla) => {
	return () => {
		if (!embla) {
			dots.classList.add('is-disabled');
		} else {
			if (embla.canScrollPrev() || embla.canScrollNext()) dots.classList.remove('is-disabled');
			else dots.classList.add('is-disabled');
		}
	};
};
