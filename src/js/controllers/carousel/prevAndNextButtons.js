export const setupPrevNextBtns = (prevBtn, nextBtn, embla) => {
	prevBtn.addEventListener('click', embla.scrollPrev, false);
	nextBtn.addEventListener('click', embla.scrollNext, false);
};

export const disablePrevNextBtns = (prevBtn, nextBtn, embla) => {
	return () => {
		if (!embla) {
			prevBtn?.classList.toggle('is-disabled', true);
			nextBtn?.classList.toggle('is-disabled', true);
			prevBtn.setAttribute('disabled', 'disabled');
			nextBtn.setAttribute('disabled', 'disabled');
		} else {
			if (embla.canScrollPrev()) prevBtn.removeAttribute('disabled');
			else prevBtn.setAttribute('disabled', 'disabled');

			if (embla.canScrollNext()) nextBtn.removeAttribute('disabled');
			else nextBtn.setAttribute('disabled', 'disabled');

			prevBtn?.classList.toggle('is-disabled', embla.canScrollPrev() === false);
			nextBtn?.classList.toggle('is-disabled', embla.canScrollNext() === false);
		}
	};
};
