import { Controller } from '@hotwired/stimulus';

export const create = () => {
	return class extends Controller {
		static targets = ['inp', 'range'];
		static outlets = ['mask'];

		updateRange(event) {
			if (event.target === this.inpTarget) {
				const rawValue = this.parseValue(event.target.value);
				this.rangeTarget.value = rawValue;
				this.inpTarget.value = this.maskOutlet.formatValue(rawValue);
			}
			this.updateProgress();
		}

		updateInput(event) {
			const rawValue = event.target.value;
			this.inpTarget.value = this.maskOutlet.formatValue(rawValue); // Použití formátování z mask_controlleru
			this.updateProgress();
		}

		updateProgress() {
			const min = parseInt(this.rangeTarget.min, 10);
			const max = parseInt(this.rangeTarget.max, 10);
			const value = parseInt(this.rangeTarget.value, 10);
			const percentage = ((value - min) / (max - min)) * 100;
			this.rangeTarget.style.setProperty('--progress', `calc(${percentage}% - 0.3rem)`);
		}

		parseValue(value) {
			return parseInt(value.replace(/\D/g, ''), 10) || 0;
		}
	};
};
