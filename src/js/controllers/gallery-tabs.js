import { Controller } from '@hotwired/stimulus';

export const create = () => {
	return class extends Controller {
		static targets = ['link', 'content', 'item'];
		static outlets = ['embla-gallery'];

		changeTab = (event) => {
			event.preventDefault();
			const selectedLink = event.currentTarget;

			this.toggleSelected(selectedLink);
		};

		changeTo = (event) => {
			const { tab } = event.detail;
			const selectedLink = this.element.querySelector(`[href="${tab}"]`);

			if (selectedLink) {
				this.toggleSelected(selectedLink);
			}
		};

		toggleSelected = (selectedLink) => {
			const type = selectedLink.dataset.type;

			this.itemTargets.forEach((item) => {
				if (type == item.dataset.type) {
					item.classList.add('is-visible');
				} else {
					item.classList.remove('is-visible');
				}
			});

			this.linkTargets.forEach((link) => {
				if (type == link.dataset.type) {
					link.classList.add('is-selected');
				} else {
					link.classList.remove('is-selected');
				}
			});

			if (this.hasEmblaGalleryOutlet) this.emblaGalleryOutlet.updateCarousel();
		};
	};
};
