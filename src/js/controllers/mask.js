import { Controller } from '@hotwired/stimulus';

export default class extends Controller {
	static values = { settings: Object };

	async connect() {
		this.VMasker = (await import(/* webpackPrefetch: true */ 'vanilla-masker')).default;

		this.maskSettings = {
			precision: 0,
			delimiter: ' ',
			separator: ',',
			...this.settingsValue,
		};

		this.VMasker(this.element).maskMoney(this.maskSettings);

		// P<PERSON>i ka<PERSON>ě kontroluj hodnotu
		this.element.addEventListener('change', this.limitInput.bind(this));
	}

	disconnect() {
		this.element.removeEventListener('change', this.limitInput);
	}

	limitInput() {
		const min = parseInt(this.element.getAttribute('min'), 10) || 0;
		const max = parseInt(this.element.getAttribute('max'), 10) || Infinity;
		let value = this.parseValue(this.element.value);

		if (value > max) {
			value = max;
		} else if (value < min) {
			value = min;
		}

		this.element.value = this.formatValue(value);
	}

	parseValue(value) {
		return parseInt(value.replace(/\D/g, ''), 10) || 0;
	}

	formatValue(value) {
		return this.VMasker.toMoney(value, this.maskSettings);
	}
}
