import { Controller } from '@hotwired/stimulus';

export const create = () => {
	return class extends Controller {
		static targets = ['inp'];

		toogleType = () => {
			return this.inpTarget.type === 'password' ? (this.inpTarget.type = 'text') : (this.inpTarget.type = 'password');
		};

		toggle = () => {
			this.toogleType();
			this.inpTarget.focus();
			this.inpTarget.setSelectionRange(this.inpTarget.value.length, this.inpTarget.value.length);
		};
	};
};
