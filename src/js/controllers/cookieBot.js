import { Controller } from '@hotwired/stimulus';

export const create = () => {
	return class extends Controller {
		connect() {
			let modalShowDetails = false;

			const showModalContentFooterGradient = () => {
				const detailsTab = document.getElementById('CybotCookiebotDialogTabContentDetails');
				if (detailsTab) {
					detailsTab.scrollTop = 1;
				}
			};

			const onDialogInit = () => {
				const changeConsentBtn = document.getElementById('CookieDeclarationChangeConsentChange');
				if (changeConsentBtn) {
					changeConsentBtn.addEventListener('click', () => {
						modalShowDetails = true;
					});
				}
			};

			const onDialogDisplay = () => {
				const cookieModal = document.getElementById('CybotCookiebotDialog');
				const customizeBtn = document.getElementById('CybotCookiebotDialogBodyLevelButtonCustomize');

				if (customizeBtn && cookieModal) {
					customizeBtn.addEventListener('click', () => {
						cookieModal.classList.add('cookie-modal-details');
						showModalContentFooterGradient();
					});
				}

				if (modalShowDetails && cookieModal) {
					cookieModal.classList.add('cookie-modal-details');
					modalShowDetails = false;
					showModalContentFooterGradient();

					setTimeout(() => {
						showModalContentFooterGradient();
					}, 150);
				}
			};

			window.addEventListener('CookiebotOnDialogInit', onDialogInit);
			window.addEventListener('CookiebotOnLoad', onDialogInit);
			window.addEventListener('CookiebotOnDialogDisplay', onDialogDisplay);
		}
	};
};
