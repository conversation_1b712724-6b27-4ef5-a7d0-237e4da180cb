import { Controller } from '@hotwired/stimulus';
import { copyToClipboard } from '../tools/copyToClipboard';

export const create = () => {
	return class extends Controller {
		static values = { content: String };

		copy = (e) => {
			e.preventDefault();
			copyToClipboard(this.contentValue);

			// active state
			this.element.classList.add('is-copied');
			setTimeout(() => {
				this.element.classList.remove('is-copied');
			}, 2000);
		};
	};
};
