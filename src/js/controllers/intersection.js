import { Controller } from '@hotwired/stimulus';
import { useIntersection, useDispatch } from 'stimulus-use';

export default class Intersection extends Controller {
	static targets = ['btn'];

	connect() {
		useDispatch(this);
		useIntersection(this);
	}
	appear = () => {
		if (!this.element.classList.contains('is-intersected') && this.hasBtnTarget) {
			this.btnTarget.click();
		}

		this.element.classList.add('is-intersected');
	};
}
