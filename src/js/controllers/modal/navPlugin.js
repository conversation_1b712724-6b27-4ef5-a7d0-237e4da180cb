import { ModalClasses, ModalMedium } from '@superkoders/modal';
import { iconSvg } from '../../tools/iconSvg';

const navItemTpl = (item, key, type) =>
	`<div class="b-modal__thumb ${type == 'photo' ? 'is-visible' : ''}" data-type="${type}" data-gallery-tabs-target="item"><img src="${
		item.thumbUrl
	}" class="b-modal__thumb-img ${ModalClasses.NAV_TRIGGER}" data-modal-index="${key}"></div>`;

export const navPlugin = {
	init: (modal) => {
		modal.emmiter.on('modalOpen', function () {
			if (modal.fragments.navElem) {
				var content = '';

				modal.items.forEach((item, i) => {
					if (item.medium == 'youtube' || item.medium == 'vimeo') {
						content += navItemTpl(item, i, 'video');
					} else {
						content += navItemTpl(item, i, 'photo');
					}
				});

				const photos = modal.items.filter((item) => item.medium === ModalMedium.IMAGE);
				const videos = modal.items.filter((item) => item.medium === ModalMedium.YOUTUBE);

				const nav = `<button class="b-modal__thumbs-prev" data-embla-gallery-target="prevButton" data-action="embla-gallery#prev">
				${iconSvg('arrow-up-long')}
				</button>
				<button class="b-modal__thumbs-next" class="embla" data-embla-gallery-target="nextButton" data-action="embla-gallery#next">
				${iconSvg('arrow-down-long')}
				</button>`;

				const contentTab = `<div id="modal-photos" class="tabs__content b-modal__tab-content is-active" data-gallery-tabs-target="content" data-controller="embla-gallery" data-embla-gallery-offset-value="0"><div class="b-modal__thumbs-wrap" data-embla-gallery-target="viewport"><div class="b-modal__thumbs">${content}</div></div>${nav}</div>`;
				// const videosTab = `<div id="modal-videos" class="tabs__content b-modal__tab-content" data-gallery-tabs-target="content" data-controller="embla-gallery" data-embla-gallery-offset-value="0"><div class="b-modal__thumbs-wrap" data-embla-gallery-target="viewport"><div class="b-modal__thumbs">${videosContent}</div></div>${nav}</div>`;

				modal.fragments.navElem.innerHTML = `<div class="tabs b-modal__tabs" data-controller="gallery-tabs" data-gallery-tabs-embla-gallery-outlet=".tabs__content" data-action="embla-gallery:changeTabTo->gallery-tabs#changeTo"><p class="tabs__menu tabs__menu--fill">${
					photos.length
						? `<a href="#modal-photos" class="tabs__link is-selected" type="button" data-action="gallery-tabs#changeTab" data-gallery-tabs-target="link" data-type="photo">Foto<span class="tabs__count">${photos.length}</span></a>`
						: ''
				}${
					videos.length
						? `<a href="#modal-videos" class="tabs__link" type="button" data-action="gallery-tabs#changeTab" data-gallery-tabs-target="link" data-type="video">Video<span class="tabs__count">${videos.length}</span></a>`
						: ''
				}</p>${contentTab}</div>`;
			}
		});
	},
};
