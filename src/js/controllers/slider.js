import { Controller } from '@hotwired/stimulus';

let noUiSlider,
	wNumb = null;

export default class <PERSON>lider extends Controller {
	static targets = ['inpFrom', 'inpTo', 'slider'];
	static outlets = ['filter'];

	async connect() {
		noUiSlider = (await import('nouislider')).default;
		wNumb = (await import('wnumb')).default;

		this.start = Number(this.sliderTarget.dataset.start);
		this.end = Number(this.sliderTarget.dataset.end);
		this.min = Number(this.sliderTarget.dataset.min);
		this.max = Number(this.sliderTarget.dataset.max);
		this.step = Number(this.sliderTarget.dataset.step);

		// range
		const { start, end, min, max, step } = this;

		this.slider = noUiSlider.create(this.sliderTarget, {
			start: [start, end],
			connect: true,
			step,
			range: {
				min,
				max,
			},
			format: wNumb({
				decimals: 0,
			}),
		});

		this.slider.on('slide', (values) => {
			this.inpFromTarget.value = values[0];
			this.inpToTarget.value = values[1];
		});

		this.slider.on('update', (values) => {
			this.inpFromTarget.value = values[0];
			this.inpToTarget.value = values[1];
		});

		this.slider.on('set', () => {
			this.submit();
		});
	}
	submit() {
		const form = this.element.closest('[data-naja]');
		this.filterOutlet.submitForm(form);
	}
	disconnect() {
		if (this.slider) {
			this.slider.destroy();
		}
	}
}
