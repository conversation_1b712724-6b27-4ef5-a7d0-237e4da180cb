import { Controller } from '@hotwired/stimulus';
import naja from 'naja';

export const create = () => {
	return class extends Controller {
		static targets = ['input'];
		static values = {
			handle: String,
			replace: String,
		};

		check() {
			document.querySelector('body').classList.add('is-loading');
			let handleUrl = this.handleValue.replace(this.replaceValue, this.inputTarget.value);

			return naja
				.makeRequest('GET', handleUrl, null, {
					history: false,
					forceRedirect: false,
				})
				.then((response) => {
					document.querySelector('body').classList.remove('is-loading');
					return response;
				});
		}
	};
};
