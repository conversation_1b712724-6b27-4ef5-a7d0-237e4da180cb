import { Controller } from '@hotwired/stimulus';
import { SuggestMenu } from '../class/Suggest/SuggestMenu';
import { Suggest } from '../class/Suggest/Suggest';
import naja from 'naja';
import { createPopper } from '@popperjs/core';

export const create = () => {
	return class extends Controller {
		static targets = ['input', 'wrap', 'box'];

		connect() {
			this.inputTargets.forEach((input) => {
				const suggest = new Suggest(input, {
					minLength: 3,
					typeInterval: 250,
					url: this.element.dataset.suggest,
				});

				const suggestMenu = new SuggestMenu(this.wrapTarget, suggest, {
					item: '.b-suggest__item',
				}).init();

				// menuselect
				suggestMenu.on('click', () => {
					const item = suggestMenu.items[suggestMenu.selectedIndex - 1];

					if (item.classList.contains('f-search__btn')) {
						this.submit();
					} else {
						const link = item.querySelector('a');
						if (link) {
							window.location = link.href;
						}
					}
				});

				input.addEventListener('click', () => {
					if (!input.dataset.opened) {
						document.querySelector('body').classList.add('is-loading');
						let handleUrl = input.dataset.open;
						return naja
							.makeRequest('GET', handleUrl, null, {
								history: false,
								forceRedirect: false,
							})
							.then((response) => {
								input.dataset.opened = 1;
								document.querySelector('body').classList.remove('is-loading');
								return response;
							})
							.catch((error) => {
								document.querySelector('body').classList.remove('is-loading');
								return [];
							});
					}
				});
			});
		}
	};
};
