import { Controller } from '@hotwired/stimulus';
import { useWindowResize, useClickOutside } from 'stimulus-use';
import { getOffsetTop } from '../tools/getOffsetTop';

const body = document.querySelector('body');

export const create = () => {
	return class extends Controller {
		static targets = ['holder', 'menuToggle'];

		connect = () => {
			// Scroll direction
			window.addEventListener('scroll', this.checkScrollPosition);
			useWindowResize(this);

			if (this.hasHolderTarget) useClickOutside(this, { element: this.holderTarget });
		};
		checkScrollPosition = () => {
			this.unpinTarget = document.querySelector('[data-header-unpin]');
			var offset = this.element.clientHeight;
			var direction = window.oldScroll > window.scrollY ? 'up' : 'down';
			var isOnTop = window.scrollY <= 1;
			window.oldScroll = window.scrollY;

			if (window.scrollY > offset) {
				this.element.parentNode.classList[direction == 'up' ? 'add' : 'remove']('is-pinned');
				this.element.parentNode.classList[direction == 'down' ? 'add' : 'remove']('is-unpinned');
			} else {
				this.element.parentNode.classList.add('is-pinned');
			}

			if (isOnTop) {
				this.element.parentNode.classList.remove('is-pinned', 'is-unpinned');
				this.element.parentNode.classList.add('is-top');
			} else {
				this.element.parentNode.classList.remove('is-top');
			}

			// Odepnutí hlavičky, pokud narazí na data-header-unpin
			this.element.parentNode.classList[
				this.unpinTarget &&
				window.scrollY + this.element.querySelector('.header__main').clientHeight >= getOffsetTop(this.unpinTarget)
					? 'add'
					: 'remove'
			]('is-hidden');
		};
		windowResize = () => {
			setTimeout(() => {
				this.checkScrollPosition(window);
				this.checkHeaderHeight();
			}, 0);
		};

		checkHeaderHeight = () => {
			var headerHeight = this.element.offsetHeight;
			document.documentElement.style.setProperty('--header-height', `${headerHeight}px`);
		};

		// menu
		toggleMenu = () => {
			this.element.classList.toggle('is-menu-open');
			body.classList[this.element.classList.contains('is-menu-open') ? 'add' : 'remove']('is-locked');
			this.menuToggleTarget.setAttribute('aria-expanded', this.element.classList.contains('is-menu-open'));

			if (!this.element.classList.contains('is-menu-open')) {
				setTimeout(() => {
					this.holderTarget.classList.remove('is-submenu-open');
				}, 300);
			}
		};

		// Submenu
		toggleSubmenu(e) {
			var target = e.currentTarget;
			target.closest('.m-main__item').classList.toggle('is-submenu-open');
		}
		toggleSubmenu2(e) {
			var target = e.currentTarget;
			target.closest('.m-submenu__item').classList.toggle('is-submenu2-open');
		}

		clickOutside = (e) => {
			if (e.srcElement != this.menuToggleTarget) {
				this.element.classList.remove('is-menu-open');
				this.menuToggleTarget.setAttribute('aria-expanded', false);
				setTimeout(() => {
					this.holderTarget.classList.remove('is-submenu-open');
				}, 300);
				body.classList.remove('is-locked');
			}
		};
	};
};
