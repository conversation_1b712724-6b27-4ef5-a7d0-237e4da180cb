import { Controller } from '@hotwired/stimulus';
import { getOffsetTop } from '../tools/getOffsetTop';
import { useIntersection } from 'stimulus-use';

export const create = () => {
	return class extends Controller {
		static targets = ['section', 'menu', 'item'];

		connect = () => {
			this.originalScrollOffset = getComputedStyle(document.documentElement).getPropertyValue('--scroll-offset');

			useIntersection(this);

			setTimeout(() => {
				this.handleScroll();
			}, 0);

			window.addEventListener('scroll', this.handleScroll);
		};
		appear = () => {
			document.documentElement.style.setProperty('--scroll-offset', this.menuTarget.clientHeight / 10 - 0.1 + 'rem');
		};
		disappear = () => {
			document.documentElement.style.setProperty('--scroll-offset', this.originalScrollOffset);
		};
		handleScroll = () => {
			this.dimensions = [];
			this.sectionTargets.forEach((section, i) => {
				const obj = { start: getOffsetTop(section), end: getOffsetTop(section) + section.clientHeight, index: i };

				this.dimensions.push(obj);
			});

			const fromTop = window.scrollY + this.menuTarget.clientHeight;

			// active section & menu item
			this.dimensions.forEach((section) => {
				if (section.start <= fromTop) {
					this.index = section.index;
					this.sectionTargets[section.index].classList.add('is-active');
				} else {
					this.sectionTargets[section.index].classList.remove('is-active');
				}

				this.itemTargets.forEach((item, i) => {
					item.classList.toggle('is-active', i == this.index);
				});
			});

			// disable first menu item when scrolled above
			if (fromTop < this.dimensions[0].start) {
				this.itemTargets[0].classList.remove('is-active');
			}
		};
	};
};
