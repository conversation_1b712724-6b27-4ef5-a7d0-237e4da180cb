import { Controller } from '@hotwired/stimulus';
import naja from 'naja';
// import { MQ } from '../tools/MQ';

export const create = () => {
	return class extends Controller {
		submit(e) {
			// Autosubmit pouze pro desktop
			// if (MQ('lgUp')) {
			e.preventDefault();
			const form = e.target.closest('[data-naja]');
			this.submitForm(form);
			// }
		}
		submitForm(form) {
			if (form) {
				naja.uiHandler.dispatchEvent(
					new CustomEvent('interaction', { cancelable: true, detail: { element: form, options: {} } }),
					// new CustomEvent('interaction', { cancelable: true, detail: { element: form, originalEvent: e, options: {} } }),
				);

				const method = form.getAttribute('method')?.toUpperCase() ?? 'GET';
				const url = form.getAttribute('action') ?? window.location.pathname + window.location.search;
				const data = new FormData(form);

				form.querySelectorAll('[data-filter-fulltext]').forEach((input) => {
					if (input.value === '') {
						data.delete(input.name);
					}
				});

				form.querySelectorAll('[data-range]').forEach((input) => {
					['min', 'max'].forEach((border) => {
						if (input.getAttribute('data-range') == border) {
							if (input.value == input.getAttribute(border)) {
								data.delete(input.name);
							}
						}
					});
				});
				return naja.makeRequest(method, url, data);
			}
		}
	};
};
