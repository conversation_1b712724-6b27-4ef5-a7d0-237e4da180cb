import { initModal } from '@superkoders/modal';
import { Controller } from '@hotwired/stimulus';
import { iconSvg } from '../tools/iconSvg';
import { navPlugin } from './modal/navPlugin';

const fetchLoader = async (url) =>
	await fetch(url, {
		headers: {
			'X-Requested-With': 'XMLHttpRequest',
		},
	});

const buildStructure = (modal, { header, titleElem, descElem, content, prevElem, nextElem, navElem, loader, bg }) => {
	const wrapper = document.createElement('div');
	wrapper.classList.add('b-modal__wrapper');

	wrapper.appendChild(header);
	wrapper.appendChild(titleElem);
	wrapper.appendChild(descElem);
	wrapper.appendChild(content);
	wrapper.appendChild(prevElem);
	wrapper.appendChild(nextElem);
	wrapper.appendChild(navElem);
	modal.appendChild(wrapper);
	modal.appendChild(loader);
	modal.appendChild(bg);
};

const options = {
	closeOnBgClick: true,
	modalSelector: '[data-modal-gallery]',
	customWrapperClass: 'b-modal--gallery',
	closeIcon: iconSvg('close'),
	nextIcon: iconSvg('arrow-right-long'),
	prevIcon: iconSvg('arrow-left-long'),
	loaderTpl: function () {
		return '<div class="body-loader__loader"></div>';
	},
	fetchLoader,
	buildStructure,
	plugins: [navPlugin],
};

export const create = () => {
	return class extends Controller {
		initialize() {
			window.App.modals = initModal(options);
		}
	};
};
