import { Controller } from '@hotwired/stimulus';
import { setCookie } from '../tools/cookie';
import { useWindowResize } from 'stimulus-use';

export const create = () => {
	return class extends Controller {
		static values = { id: String, calcHeight: Boolean };

		connect() {
			useWindowResize(this);
		}
		windowResize = () => {
			setTimeout(() => {
				document.documentElement.style.setProperty('--msgh', `${this.element.parentElement.parentElement.clientHeight}px`);
			}, 0);
		};
		close = () => {
			setCookie(this.idValue, true, {
				'max-age': 31536000, // 1 rok
			});

			this.element.parentNode.parentNode.removeChild(this.element.parentNode);
		};
		disconnect = () => {
			// remove the -msgh value when closed
			document.documentElement.style.removeProperty('--msgh');
		};
	};
};
