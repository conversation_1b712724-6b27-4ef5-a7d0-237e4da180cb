import { Controller } from '@hotwired/stimulus';

export const create = () => {
	return class extends Controller {
		static targets = ['days', 'hours', 'minutes', 'seconds', 'conjunctionDaysHours', 'conjunctionMinsSecs'];

		connect() {
			this.d = parseInt(this.daysTarget.getAttribute('data-value'));
			this.h = parseInt(this.hoursTarget.getAttribute('data-value'));
			this.m = parseInt(this.minutesTarget.getAttribute('data-value'));
			this.s = parseInt(this.secondsTarget.getAttribute('data-value'));

			var duration = this.d * 24 * 60 * 60 + this.h * 60 * 60 + this.m * 60 + this.s;
			this.startCountdown(duration, 1000);
		}
		writeRemaining = () => {
			this.setTime(this.daysTarget, this.d);
			this.setTime(this.hoursTarget, this.h);
			this.setTime(this.minutesTarget, this.m);
			this.setTime(this.secondsTarget, this.s);

			// show/hide d/h/m/s
			this.daysTarget.classList[this.d > 0 ? 'remove' : 'add']('u-d-n');
			this.hoursTarget.classList[this.d > 0 || this.h > 0 ? 'remove' : 'add']('u-d-n');
			// this.minutesTarget.classList[this.d == 0 && this.m > 0 ? 'remove' : 'add']('u-d-n');
			this.secondsTarget.classList[!(this.d > 0 || this.h > 0) ? 'remove' : 'add']('u-d-n');

			// // show/hide conjunctions
			//this.conjunctionDaysHoursTarget.classList[this.d > 0 ? 'remove' : 'add']('u-d-n');
			//this.conjunctionMinsSecsTarget.classList[!(this.d > 0 || this.h > 0) ? 'remove' : 'add']('u-d-n');
		};
		setTime = (target, val) => {
			var langs = JSON.parse(target.getAttribute('data-langs'));
			target.innerHTML = `<b>${val}</b> ${this.getLang(langs, val)}`;
		};
		getLang = (arr, val) => {
			if (val == 1) {
				return arr[0];
			} else if (val >= 2 && val <= 4) {
				return arr[1];
			} else {
				return arr[2];
			}
		};
		startCountdown = (duration, updateInterval) => {
			const { getRemaining, writeRemaining, element } = this;

			// Obsah Web Workera
			const worker = new Worker(
				URL.createObjectURL(
					new Blob(
						[
							`
							self.onmessage = function (event) {
								const { duration, updateInterval } = event.data;

								// Spuštění odpočítávání
								let timeLeft = duration;
								const countdownInterval = setInterval(() => {
									timeLeft--;
									if (timeLeft <= 0) {
									clearInterval(countdownInterval);
									self.postMessage({ done: true });
									} else {
									self.postMessage({ timeLeft, done: false });
									}
								}, updateInterval);
							};
						`,
						],
						{ type: 'application/javascript' },
					),
				),
			);

			// Poslání počátečních dat Web Workeru
			worker.postMessage({ duration, updateInterval });

			// Poslech zpráv od Web Workera
			worker.onmessage = function (event) {
				const { timeLeft, done } = event.data;
				if (done) {
					element.classList.add('u-d-n');
					worker.terminate(); // Ukončení Web Workera po dokončení odpočítávání
				} else {
					writeRemaining(getRemaining(timeLeft));
				}
			};
		};
		getRemaining = (totalSeconds) => {
			// Vypočítat počet dní
			this.d = Math.floor(totalSeconds / (3600 * 24));
			totalSeconds %= 3600 * 24;

			// Vypočítat počet hodin
			this.h = Math.floor(totalSeconds / 3600);
			totalSeconds %= 3600;

			// Vypočítat počet minut
			this.m = Math.floor(totalSeconds / 60);

			// Zbývající sekundy
			this.s = totalSeconds % 60;
		};
	};
};
