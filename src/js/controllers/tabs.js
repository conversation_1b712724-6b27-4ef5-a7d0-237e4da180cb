import { Controller } from '@hotwired/stimulus';

export const create = () => {
	return class extends Controller {
		static targets = ['link', 'content'];

		changeTab(event) {
			event.preventDefault();
			this.toggleTab(event.currentTarget);
		}

		changeTo(event) {
			const { tab } = event.detail;
			const selectedLink = this.element.querySelector(`[href="${tab}"]`);
			this.toggleTab(selectedLink);
		}

		toggleTab(selectedLink) {
			if (!selectedLink) return;

			const contentId = selectedLink.getAttribute('href');
			const activeContent = this.element.querySelector(contentId);

			if (!activeContent) return;

			this.linkTargets.forEach((link) => {
				link.classList[link.getAttribute('href') === contentId ? 'add' : 'remove']('is-selected');
			});

			this.contentTargets.forEach((content) => {
				content.classList.remove('is-active');
			});
			activeContent.classList.add('is-active');
		}
	};
};
