import { Controller } from '@hotwired/stimulus';
import { useClickOutside } from 'stimulus-use';

export const create = () => {
	return class extends Controller {
		static values = { initSize: String };

		connect() {
			useClickOutside(this);
		}
		clickOutside() {
			this.element.classList.remove('is-open');
			this.element.querySelector('[aria-expanded]')?.setAttribute('aria-expanded', 'false');
		}
		open = (e) => {
			var shouldInit = window.innerWidth >= this.initSizeValue;

			if (document.querySelector('html').classList.contains('no-hoverevents') && shouldInit) {
				if (!this.element.classList.contains('is-open')) {
					e.preventDefault();
					this.element.classList.add('is-open');
					this.element.querySelector('[aria-expanded]')?.setAttribute('aria-expanded', 'true');
				}
			}
		};
	};
};
