/* eslint-disable @babel/no-unused-expressions */
import { Controller } from '@hotwired/stimulus';

export const create = () => {
	return class extends Controller {
		static targets = ['inp'];
		static values = {
			triggerClass: { type: Boolean, default: true },
		};

		toggle = (e) => {
			e.preventDefault();

			this.trigger = e.currentTarget;
			this.class = this.trigger.getAttribute('data-toggle-class') ?? 'is-open';
			this.closest = this.trigger.getAttribute('data-toggle-closest');
			this.content = this.trigger.getAttribute('data-toggle-content');

			if (this.closest) {
				// Nejbližší element
				this.toggleEl(this.trigger.closest(this.closest));
				this.toggleTrigger();
			} else if (this.content) {
				// Vlastní selektor
				if (
					this.trigger.closest('[data-controller="toggle-class"]') &&
					this.trigger != this.trigger.closest('[data-controller="toggle-class"]')
				) {
					this.element.querySelectorAll(this.content).forEach((el) => this.toggleEl(el));
				} else {
					document.querySelectorAll(this.content).forEach((el) => this.toggleEl(el));
				}
				this.toggleTrigger();
			} else if (this.element != this.trigger) {
				// Element, na kterém je controler
				this.toggleEl(this.element);
				this.toggleTrigger();
			}

			if (this.hasInpTarget) {
				setTimeout(() => {
					this.inpTarget.focus({
						preventScroll: true,
					});
				}, 300);
			}
		};

		toggleEl = (el) => {
			el.classList.toggle(this.class);
		};

		toggleTrigger = () => {
			if (this.triggerClassValue) this.trigger.classList.toggle(this.class);
			// if (this.trigger.hasAttribute('aria-expanded')) {
			// 	this.trigger.setAttribute('aria-expanded', this.trigger.getAttribute('aria-expanded') == false ? 'true' : 'false');
			// }
		};

		toggleInp = (e) => {
			this.trigger = e.currentTarget;
			this.class = this.trigger.getAttribute('data-toggle-class') ?? 'is-open';
			this.closest = this.trigger.getAttribute('data-toggle-closest');
			this.content = this.trigger.getAttribute('data-toggle-content');

			if (this.closest) {
				this.trigger.closest(this.closest).classList[this.trigger.checked ? 'add' : 'remove'](this.class);
			} else if (this.content) {
				document.querySelectorAll(this.content).forEach((el) => this.toggleEl(el));
			} else {
				this.element.classList[this.trigger.checked ? 'add' : 'remove'](this.class);
			}
		};
	};
};
