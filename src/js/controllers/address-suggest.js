import { Controller } from '@hotwired/stimulus';
import loadSmartformApi from '../tools/loadSmartformApi';

export const create = () => {
	return class extends Controller {
		connect() {
			loadSmartformApi().then(() => {
				this.fixChromeAutocomplete();
			});
		}

		fixChromeAutocomplete = () => {
			this.smartInps = this.element.querySelectorAll('input[class*="smartform"]');
			this.smartInps.forEach((inp) => inp.addEventListener('focus', () => inp.setAttribute('autocomplete', 'chrome-off')));
		};
	};
};
