import { Controller } from '@hotwired/stimulus';

export const create = () => {
	return class extends Controller {
		static targets = ['hidden', 'showMoreBtn'];

		connect() {
			this.itemsToLoad = 6;
		}
		loadMore = (e) => {
			e.preventDefault();
			if (this.hasHiddenTarget) {
				if (this.getHiddenElements().length) {
					this.showItems(this.getHiddenElements());
				}
			}
		};

		getHiddenElements = () => {
			console.log(this.hiddenTargets.filter((item) => item.classList.contains('u-d-n')));

			return this.hiddenTargets.filter((item) => item.classList.contains('u-d-n'));
		};

		showItems = (hiddenElements) => {
			var itemsToShow = hiddenElements.slice(0, this.itemsToLoad);
			itemsToShow.forEach((item) => item.classList.remove('u-d-n'));

			if (this.getHiddenElements().length == 0) {
				this.showMoreBtnTarget.classList.add('u-d-n');
			}
		};
	};
};
