import { Controller } from '@hotwired/stimulus';

export default class extends Controller {
	static targets = ['range', 'speedButton', 'currentTime', 'totalTime'];

	connect() {
		this.speech = new SpeechSynthesisUtterance();
		this.speech.lang = document.documentElement.lang;
		this.speech.rate = 1.0;
		this.isPlaying = false;
		this.currentTime = 0;
		this.words = [];
		this.totalDuration = 0;

		// Načtení textu ze selektoru
		this.loadText();

		// Inicializace času a progressu
		this.updateTime();
		this.updateProgress();

		// Event listener pro konec přehrávání
		this.speech.onend = () => {
			this.isPlaying = false;
			this.currentTime = 0;
			this.updateState();
			this.updateProgress();
			this.updateTime();
			clearInterval(this.progressTimer);
		};

		// Inicializace stavu ikon
		this.updateState();
	}

	loadText() {
		const textElement = document.querySelector('.audio-content');
		if (textElement) {
			// Vyloučení textu s třídou .exclude-from-audio
			const clone = textElement.cloneNode(true);
			const excludedElements = clone.querySelectorAll('.exclude-from-audio');
			excludedElements.forEach((el) => (el.textContent = ''));
			this.speech.text = clone.textContent.trim();
			this.words = this.speech.text.split(/\s+/).filter(Boolean);
			this.totalDuration = this.estimateDuration();
		}
	}

	estimateDuration() {
		const wordsPerMinute = 130 * this.speech.rate;
		return (this.words.length / wordsPerMinute) * 60; // Odhad délky v sekundách
	}

	togglePlay() {
		if (this.isPlaying) {
			window.speechSynthesis.pause();
			this.isPlaying = false;
			clearInterval(this.progressTimer);
		} else {
			if (window.speechSynthesis.paused) {
				window.speechSynthesis.resume();
			} else {
				window.speechSynthesis.cancel();
				const startIndex = this.getWordIndexAtTime(this.currentTime);
				this.speech.text = this.words.slice(startIndex).join(' ');
				window.speechSynthesis.speak(this.speech);
			}
			this.isPlaying = true;
			this.startProgressTimer();
		}
		this.updateState();
	}

	rewindPrev() {
		const newTime = Math.max(0, this.currentTime - 15);
		this.setTime(newTime);
	}

	rewindNext() {
		const newTime = Math.min(this.totalDuration, this.currentTime + 15);
		this.setTime(newTime);
	}

	changeSpeed() {
		const speeds = [1.0, 1.25, 1.5, 2.0];
		const currentIndex = speeds.indexOf(this.speech.rate);
		const nextIndex = (currentIndex + 1) % speeds.length;
		this.speech.rate = speeds[nextIndex];
		this.speedButtonTarget.textContent = `${this.speech.rate.toFixed(2)} ×`;

		if (this.isPlaying) {
			const savedTime = this.currentTime;
			window.speechSynthesis.cancel();
			const startIndex = this.getWordIndexAtTime(savedTime);
			this.speech.text = this.words.slice(startIndex).join(' ');
			window.speechSynthesis.speak(this.speech);
			this.isPlaying = true;
			this.currentTime = savedTime;
			this.startProgressTimer();
		}
		this.totalDuration = this.estimateDuration();
		this.updateTime();
	}

	updateRange(event) {
		const value = parseInt(event.target.value);
		const newTime = (value / 100) * this.totalDuration;
		this.setTime(newTime);
	}

	setTime(seconds) {
		this.currentTime = seconds;
		const progress = (this.currentTime / this.totalDuration) * 100;
		this.rangeTarget.value = progress;
		this.rangeTarget.style.setProperty('--progress', `${progress}%`);

		if (this.isPlaying) {
			window.speechSynthesis.cancel();
			const startIndex = this.getWordIndexAtTime(this.currentTime);
			this.speech.text = this.words.slice(startIndex).join(' ');
			window.speechSynthesis.speak(this.speech);
			this.startProgressTimer();
		}
		this.updateTime();
	}

	getCurrentTime() {
		return this.currentTime;
	}

	getWordIndexAtTime(seconds) {
		const progress = seconds / this.totalDuration;
		return Math.floor(progress * this.words.length);
	}

	startProgressTimer() {
		const intervalMs = 20; // Jedno místo pro změnu intervalu (např. 20ms)
		const step = this.totalDuration / (this.totalDuration * (1000 / intervalMs)); // Dynamický krok

		clearInterval(this.progressTimer);
		if (this.isPlaying) {
			this.progressTimer = setInterval(() => {
				this.currentTime += step;
				if (this.currentTime >= this.totalDuration) {
					this.currentTime = this.totalDuration;
					this.isPlaying = false;
					clearInterval(this.progressTimer);
				}
				this.updateTime();
				this.updateProgress();
			}, intervalMs);
		}
	}

	updateState() {
		this.element.classList.toggle('is-playing', this.isPlaying);
		this.element.classList.toggle('is-paused', !this.isPlaying);

		const playIcon = this.element.querySelector('.b-audio__btn--play .btn__text > :nth-child(2)');
		const pauseIcon = this.element.querySelector('.b-audio__btn--play .btn__text > :nth-child(3)');

		playIcon.classList.toggle('u-d-n', this.isPlaying);
		pauseIcon.classList.toggle('u-d-n', !this.isPlaying);
	}

	updateTime() {
		const currentTime = this.getCurrentTime();
		this.currentTimeTarget.textContent = this.formatTime(currentTime);
		this.totalTimeTarget.textContent = this.formatTime(this.totalDuration);
	}

	updateProgress() {
		const progress = ((this.currentTime / this.totalDuration) * 100).toFixed(2);
		this.rangeTarget.value = progress;
		this.rangeTarget.style.setProperty('--progress', `${progress}%`);
	}

	formatTime(seconds) {
		const minutes = Math.floor(seconds / 60);
		const secs = Math.floor(seconds % 60);
		return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
	}
}
