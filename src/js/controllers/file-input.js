import { Controller } from '@hotwired/stimulus';
import { customAlphabet } from 'nanoid';

const nanoid = customAlphabet('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', 10);

export default class FileInputController extends Controller {
	static targets = ['input', 'dropzone', 'list', 'template'];

	getFileItem(file, id, error) {
		const el = this.templateTarget.content.firstElementChild.cloneNode(true);

		el.id = id;
		if (error) el.classList.add('f-file__item--error');

		const btn = el.querySelector('button');
		btn.dataset.fileInputIdParam = id;

		el.querySelector('.file-name').textContent = file.name;

		const err = el.querySelector('.file-input__error');
		if (error) {
			err.textContent = error;
			err.hidden = false;
		}

		return el;
	}

	addFile(file) {
		if (!this.hasInputTarget) return;

		let error = null;
		const fileId = nanoid();
		const maxFileSize = 20 * 1024 * 1024;
		const name = this.inputTarget.name;

		// check file size
		if (file.size > maxFileSize) {
			error = 'Maximum file size exceeded (20 MB)';
		}

		// inform parent
		if (!error) {
			this.dispatch('addFile', { detail: { file, name, fileId } });
		}

		if (this.hasListTarget) {
			// add file to list
			this.listTarget.appendChild(this.getFileItem(file, fileId, error));
		}
	}

	removeFile(event) {
		const { id } = event.params;

		// inform parent
		this.dispatch('removeFile', { detail: { fileId: id } });

		// remove the element
		event.target.closest(`#${id}`).remove();
	}

	fileInputChange(event) {
		const items = event.target.files;

		for (let i = 0; i < items.length; i++) {
			this.addFile(items[i]);
		}

		if (this.hasInputTarget) {
			this.inputTarget.value = '';
		}
	}

	drop(event) {
		event.preventDefault();
		event.stopPropagation();
		const { files } = event.dataTransfer;

		if (this.hasDropzoneTarget) {
			this.dropzoneTarget.classList.remove('is-dragging');
		}

		for (let i = 0; i < files.length; i++) {
			this.addFile(files[i]);
		}
	}

	dragover(event) {
		event.preventDefault();
		event.stopPropagation();
		if (this.hasDropzoneTarget) {
			this.dropzoneTarget.classList.add('is-dragging');
		}
	}

	dragend(event) {
		event.preventDefault();
		event.stopPropagation();
		if (this.hasDropzoneTarget) {
			this.dropzoneTarget.classList.remove('is-dragging');
		}
	}

	dragleave(event) {
		event.preventDefault();
		event.stopPropagation();
		if (this.hasDropzoneTarget) {
			this.dropzoneTarget.classList.remove('is-dragging');
		}
	}
}
