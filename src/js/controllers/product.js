import { Controller } from '@hotwired/stimulus';

export const create = () => {
	return class extends Controller {
		static targets = ['link', 'mainImg', 'hoverImg'];

		setVariant(e) {
			this.linkTargets.forEach((link) => link.setAttribute('href', e.currentTarget.dataset.url));
			this.mainImgTarget?.setAttribute('src', e.currentTarget.dataset.mainSrc);
			this.hoverImgTarget?.setAttribute('src', e.currentTarget.dataset.hoverSrc);

			// embla is-active
		}
	};
};
