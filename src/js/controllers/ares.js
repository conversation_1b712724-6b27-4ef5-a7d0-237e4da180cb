import { Controller } from '@hotwired/stimulus';

export const create = () => {
	return class extends Controller {
		static targets = ['ic', 'companyName', 'dic', 'street', 'city', 'zip', 'btn'];
		// static values = { value: String };

		connect = () => {
			this.inps = [this.companyNameTarget, this.dicTarget, this.streetTarget, this.cityTarget, this.zipTarget];
		};
		async fetch(ic) {
			this.btnTarget.classList.add('is-loading');
			var ic = this.icTarget.value;
			const url = `https://ares.gov.cz/ekonomicke-subjekty-v-be/rest/ekonomicke-subjekty/${ic}`;
			try {
				const response = await fetch(url, { headers: { Accept: 'application/json' } });
				if (!response.ok) {
					throw new Error(`Chyba při načítání dat: ${response.status}`);
				}
				const data = await response.json();
				this.btnTarget.classList.remove('is-loading');
				this.fillData(data);
				// return data;
			} catch (error) {
				console.error('Nepodařilo se načíst data z ARES:', error);
				this.btnTarget.classList.remove('is-loading');
				this.emptyData();
				return null;
			}
		}

		fillData = (data) => {
			// console.log(data);
			this.icTarget.parentNode.classList.add('is-ok');
			this.icTarget.classList.remove('has-error');
			this.fillInp(this.companyNameTarget, data.obchodniJmeno);
			this.fillInp(this.dicTarget, data.dic);
			this.fillInp(this.streetTarget, data.adresaDorucovaci.radekAdresy1);
			this.fillInp(this.cityTarget, data.adresaDorucovaci.radekAdresy2);
			this.fillInp(this.zipTarget, data.adresaDorucovaci.radekAdresy3?.split(' ')[0]);
		};

		emptyData = () => {
			this.icTarget.parentNode.classList.add('has-error');
			this.icTarget.parentNode.classList.remove('is-ok');

			var inps = [this.companyNameTarget, this.dicTarget, this.streetTarget, this.cityTarget, this.zipTarget];
			inps.forEach((inp) => {
				inp.value = '';
				inp.parentNode.classList.remove('is-ok');
			});
		};

		fillInp = (inp, value) => {
			if (value) {
				inp.value = value;
				inp.parentNode.classList.add('is-ok');
			}
		};
	};
};
