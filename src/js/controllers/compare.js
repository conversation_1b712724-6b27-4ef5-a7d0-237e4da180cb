import { Controller } from '@hotwired/stimulus';

export default class extends Controller {
	static targets = ['filterGroup', 'filterParam', 'filterEmpty', 'groupCheckbox', 'paramCheckbox', 'group', 'param', 'value'];

	filter(e) {
		const query = e.target.value.toLowerCase();
		let hasVisible = false;

		this.filterGroupTargets.forEach((group) => {
			const params = group.querySelectorAll("[data-compare-target='filterParam']");
			const visible = Array.from(params).some((param) => param.textContent.trim().toLowerCase().includes(query));

			group.style.display = visible ? '' : 'none';
			params.forEach((param) => (param.style.display = param.textContent.trim().toLowerCase().includes(query) ? '' : 'none'));
			if (visible) hasVisible = true;
		});

		this.filterEmptyTarget.classList.toggle('u-d-n', hasVisible);
	}

	toggleParam(e) {
		const checkbox = e.target;
		document.querySelectorAll(`#${checkbox.name}`).forEach((row) => row.classList.toggle('is-hidden', !checkbox.checked));
		this.updateGroupVisibility(checkbox.closest("[data-compare-target='filterGroup']"));
		this.preventScroll();
	}

	updateGroupVisibility(group) {
		const checked = Array.from(group.querySelectorAll("[data-compare-target='paramCheckbox']")).map((cb) => cb.checked);
		const groupCheckbox = group.querySelector("[data-compare-target='groupCheckbox']");

		groupCheckbox.checked = checked.every(Boolean);
		groupCheckbox.indeterminate = checked.some(Boolean) && !checked.every(Boolean);
		if (!checked.some(Boolean)) groupCheckbox.checked = groupCheckbox.indeterminate = false;
	}

	toggleGroup(e) {
		const group = e.target.closest("[data-compare-target='filterGroup']");
		const isVisible = e.target.checked;

		group.querySelectorAll("[data-compare-target='paramCheckbox']").forEach((cb) => {
			cb.checked = isVisible;
			this.toggleParam({ target: cb });

			// trigger change
			cb.dispatchEvent(new Event('change'));
		});
		this.preventScroll();
	}

	toggleSame(e) {
		e.preventDefault();
		const showOnlyDifferent = !e.target.checked;

		this.groupTargets.forEach((group) => {
			const params = Array.from(group.querySelectorAll('[data-compare-target="param"]'));
			params.forEach((param) => {
				const values = param.querySelectorAll('[data-compare-target="value"]');
				const isHidden = showOnlyDifferent && this.hasSameValues(values);
				param.classList.toggle('is-hidden-same', isHidden);
			});
		});
		this.preventScroll();
	}

	syncCheckbox(e) {
		const checkbox = e.target;
		console.log(document.querySelectorAll(`[name="${checkbox.name}"]`));

		document.querySelectorAll(`[name="${checkbox.name}"]`).forEach((cb) => (cb.checked = checkbox.checked));
	}

	hasSameValues(valueTargets) {
		const values = Array.from(valueTargets).map((el) => el.textContent.trim());
		return values.every((val) => val === values[0]);
	}

	preventScroll = () => {
		setTimeout(() => window.scrollTo(window.scrollX, window.scrollY), 0);
	};
}
