import { Controller } from '@hotwired/stimulus';

export const create = () => {
	return class extends Controller {
		static values = {
			config: Object,
		};

		selectedPickupPoint = null;

		onPickupPointSelect(event) {
			this.selectedPickupPoint = event.detail;

			// reset expanded and focus the trigger element
			this.element.setAttribute('aria-expanded', 'false');

			if (!event.detail) return;

			const inputEl = document.querySelector("input[name='pickupPointId']");

			if (inputEl && this.selectedPickupPoint.id && parseInt(inputEl.value) !== this.selectedPickupPoint.id) {
				inputEl.value = this.selectedPickupPoint.id;
			}
		}

		openPicker() {
			if (!this.hasConfigValue) {
				console.warn('No config for delivery picker plugin!!');
				return;
			}

			// create custom skpickertriggerclick event and pass picker config as event detail value
			const pickerEvent = new CustomEvent('skpickertriggerclick', {
				detail: {
					...this.configValue,
					initialSelectedPoint: this.selectedPickupPoint ?? this.configValue.initialSelectedPoint,
				},
			});
			document.dispatchEvent(pickerEvent);
			this.element.setAttribute('aria-expanded', 'true');
		}
	};
};
