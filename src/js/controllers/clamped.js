import { Controller } from '@hotwired/stimulus';
import { useWindowResize } from 'stimulus-use';

export default class Clamped extends Controller {
	static targets = ['content', 'btn'];

	connect() {
		setTimeout(() => {
			useWindowResize(this);
		}, 0);
	}
	windowResize = () => {
		if (this.hasContentTarget && this.hasBtnTarget) {
			if (
				this.contentTarget.offsetHeight < this.contentTarget.scrollHeight ||
				this.contentTarget.offsetWidth < this.contentTarget.scrollWidth
			) {
				this.contentTarget.classList.add('is-clamped');
				this.btnTarget.classList.remove('u-d-n');
			} else {
				this.contentTarget.classList.remove('is-clamped');
				this.btnTarget.classList.add('u-d-n');
			}
		}
	};
}
