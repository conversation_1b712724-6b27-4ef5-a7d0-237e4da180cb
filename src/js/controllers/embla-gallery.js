import { Controller } from '@hotwired/stimulus';
import EmblaCarousel from 'embla-carousel';
import { disablePrevNextBtns } from './carousel/prevAndNextButtons';
import { useDispatch, useIntersection } from 'stimulus-use';
import { MQ } from '../tools/MQ';
import { useWindowResize } from 'stimulus-use';

export const create = () => {
	return class extends Controller {
		static targets = ['viewport', 'prevButton', 'nextButton'];
		static values = {
			offset: Number,
		};

		carousel = null;

		connect() {
			const optionsDefault = {
				loop: false,
				align: 'start',
				axis: MQ('lgUp') ? 'y' : 'x',
				slidesToScroll: 2,
				inViewThreshold: 0.5,
				containScroll: 'trimSnaps',
				skipSnaps: true,
				slides: this.viewportTarget.querySelectorAll('.is-visible'),
			};

			if (!this.hasViewportTarget) return;

			this.carousel = EmblaCarousel(this.viewportTarget, optionsDefault);
			this.slides = this.carousel.slideNodes();

			const disablePrevAndNextBtns = disablePrevNextBtns(this.prevButtonTarget, this.nextButtonTarget, this.carousel);

			this.carousel.on('select', disablePrevAndNextBtns);
			this.carousel.on('init', disablePrevAndNextBtns);
			this.carousel.on('reInit', disablePrevAndNextBtns);

			if (window.App.modals) {
				const currentModal = window.App.modals.find((modal) => modal.isOpened);
				const t = this;

				if (currentModal) {
					currentModal.emmiter.on('modalSlideChanged', function (_event, data) {
						const { page } = data;
						const index = page - 1;

						if (currentModal.items[index].medium == 'youtube') {
							t.dispatch('changeTabTo', { tab: '#modal-videos' });
						} else {
							t.dispatch('changeTabTo', { tab: '#modal-photos' });
						}

						// t.carousel.reInit({ slides: t.viewportTarget.querySelectorAll('.is-visible') });

						if (t.element.offsetParent !== null) {
							t.carousel.scrollTo(Math.floor((index - t.offsetValue) / 2));
						}
					});
				}
			}

			useDispatch(this);
			useIntersection(this);
			useWindowResize(this);
		}

		windowResize() {
			this.carousel.reInit({
				axis: MQ('lgUp') ? 'y' : 'x',
			});
		}

		updateCarousel() {
			this.carousel.reInit({ slides: this.viewportTarget.querySelectorAll('.is-visible') });
		}

		prev(event) {
			event.preventDefault();
			this.carousel.scrollPrev();
		}

		next(event) {
			event.preventDefault();
			this.carousel.scrollNext();
		}

		appear() {
			this.carousel.reInit();
		}
	};
};
