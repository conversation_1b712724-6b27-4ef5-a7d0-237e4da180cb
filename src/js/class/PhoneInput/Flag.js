export class Flag {
	constructor(options, countryCode) {
		this.options = options;
		this.countryCode = countryCode;
	}

	get() {
		if (this.element) return this.element;
		return this.create();
	}

	create() {
		const { className } = this.options;
		this.element = document.createElement('span');
		this.element.classList.add(`${className}__flag`, 'inp-select');
		this.image = document.createElement('img');
		this.image.setAttribute('src', this.getFlagSrc().toLowerCase());
		this.element.appendChild(this.image);

		return this.element;
	}

	setCountry(countryCode) {
		this.countryCode = countryCode;
		this.image.setAttribute('src', this.getFlagSrc().toLowerCase());
	}

	getFlagSrc() {
		const { flagPath, flagExt } = this.options;
		return `${flagPath}${this.countryCode.toLowerCase()}${flagExt}`;
	}
}
