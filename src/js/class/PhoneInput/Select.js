import { Prefix } from './Prefix';

export class Select {
	constructor(options, countryCode) {
		this.options = options;
		this.countryCode = countryCode;
	}

	get() {
		if (this.element) return this.element;
		return this.create();
	}

	create() {
		const { className, supportedCountries } = this.options;
		this.element = document.createElement('select');
		this.element.value = this.countryCode;
		this.element.classList.add(`${className}__select`);
		this.element.setAttribute('title', supportedCountries[this.countryCode]);
		this.element.setAttribute('tabIndex', '-1');
		const options = Object.keys(supportedCountries)
			.sort((a, b) => supportedCountries[a].localeCompare(supportedCountries[b]))
			.map((countryCode) => {
				const option = document.createElement('option');
				option.textContent = supportedCountries[countryCode];
				if (this.options.showPrefix) {
					option.textContent += `(${Prefix.getPrefix(countryCode)})`;
				}
				option.setAttribute('value', countryCode);
				if (countryCode === this.countryCode) {
					option.setAttribute('selected', 'selected');
				}

				return option;
			});

		options.forEach((option) => this.element.appendChild(option));

		return this.element;
	}

	setCountry(countryCode) {
		this.element.value = countryCode;
		this.element.setAttribute('title', this.options.supportedCountries[countryCode]);
		this.element.querySelectorAll('option').forEach((option) => {
			if (option.getAttribute('value') === countryCode) {
				option.setAttribute('selected', 'selected');
			} else {
				option.removeAttribute('selected');
			}
		});
	}
}
