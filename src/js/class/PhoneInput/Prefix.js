import { getCountryCallingCode } from 'libphonenumber-js/min';

export class Prefix {
	constructor(options, countryCode) {
		this.options = options;
		this.countryCode = countryCode;
	}

	static getPrefix(countryCode) {
		return `+${getCountryCallingCode(countryCode)}`;
	}

	get() {
		if (this.element) return this.element;
		return this.create();
	}

	create() {
		const { className } = this.options;
		this.element = document.createElement('span');
		this.element.classList.add(`${className}__prefix`);
		this.setPrefix();

		return this.element;
	}

	setCountry(countryCode) {
		this.countryCode = countryCode;
		this.setPrefix();
	}

	setPrefix() {
		this.element.textContent = `${Prefix.getPrefix(this.countryCode)}`;
	}
}
