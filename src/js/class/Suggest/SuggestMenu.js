import { removeClass, addClass } from '@superkoders/sk-tools/src/className';
import { on, delegate, off } from '@superkoders/sk-tools/src/event';
import { create } from '@superkoders/sk-tools/src/emmiter';
import { query } from '@superkoders/sk-tools/src/selector';

export class SuggestMenu {
	constructor(element, Suggest, options) {
		this.element = element;
		this.Suggest = Suggest;

		this.options = {
			item: 'li',
			selectedClass: 'is-selected',
			visibleClass: 'is-visible',
			...options,
		};

		this.items = [];
		this.selectedIndex = 0;
		this.size = 0;
		this.isOpen = false;

		const emmitter = create({});
		this.on = emmitter.on;
		this.off = emmitter.off;
		this.trigger = emmitter.trigger;

		this.handleMouseEnter = delegate(this.options.item, this.handleMouseEnter.bind(this));
		this.handleMouseDown = delegate(this.options.item, this.handleMouseDown.bind(this));
		this.handleClick = delegate(this.options.item, this.handleClick.bind(this));

		this.handleSuggestStart = this.handleSuggestStart.bind(this);
		this.handleSuggestEnd = this.handleSuggestEnd.bind(this);
		this.handleSuccess = this.handleSuccess.bind(this);
		this.handleTypeStart = this.handleTypeStart.bind(this);
		this.handleTypeClear = this.handleTypeClear.bind(this);
	}

	init() {
		if (this.element == null || this.Suggest == null) return this;

		removeClass(this.element, this.options.visibleClass);
		this.element.addEventListener('mouseenter', this.handleMouseEnter, true);

		on(this.element, 'mousedown', this.handleMouseDown);
		on(this.element, 'click', this.handleClick);

		this.Suggest.on('suggeststart', this.handleSuggestStart)
			.on('suggestend', this.handleSuggestEnd)
			.on('success', this.handleSuccess)
			.on('typestart', this.handleTypeStart)
			.on('typeclear', this.handleTypeClear);

		this.Suggest.init();

		return this;
	}

	destroy() {
		if (this.element == null || this.Suggest == null) return this;

		removeClass(this.element, this.options.visibleClass);
		this.element.innerHTML = '';

		this.element.removeEventListener('mouseenter', this.handleMouseEnter);

		off(this.element, 'mousedown', this.handleMouseDown);
		off(this.element, 'click', this.handleClick);

		this.Suggest.off('suggeststart', this.handleSuggestStart)
			.off('suggestend', this.handleSuggestEnd)
			.off('success', this.handleSuccess)
			.off('typestart', this.handleTypeStart)
			.off('typeclear', this.handleTypeClear);

		this.Suggest.destroy();

		return this;
	}

	open() {
		if (!this.isOpen && this.element.innerHTML !== '') {
			this.trigger('beforeopen');
			addClass(this.element, this.options.visibleClass);
			this.trigger('afteropen');

			this.isOpen = true;
		}
	}

	hide() {
		if (!this.isOpen) return;

		this.trigger('beforehide');
		removeClass(this.element, this.options.visibleClass);
		this.trigger('afterhide');

		this.isOpen = false;
	}

	handleSuggestStart() {
		if (this.Suggest.isResult === false) return;

		this.clearIndex();
		this.open();
	}

	handleSuggestEnd() {
		// odděleno zkrz vyhledávání bez focusu, u menu nemá smysl
		this.Suggest.typeTimer = clearTimeout(this.Suggest.typeTimer);
		this.Suggest.ajaxAbort();
		removeClass(this.Suggest.input, this.Suggest.options.loadingClass);

		this.hide();
	}

	handleSuccess(e, { respond }) {
		let isDefaultPrevented = false;
		this.trigger('content', {
			respond,
			preventDefault() {
				isDefaultPrevented = true;
			},
		});

		if (!isDefaultPrevented) {
			this.element.innerHTML = respond;
		}

		this.items = query(this.options.item, this.element);
		this.size = this.items.length + 1;

		this.clearIndex();

		this.element.innerHTML === '' ? this.hide() : this.open();
	}

	handleTypeStart(e, { which, preventDefault }) {
		if (this.items.length < 1) return;

		if (which === 38) {
			this.open();
			this.handleArrUp({ preventDefault });
		} else if (which === 40) {
			this.open();
			this.handleArrDown({ preventDefault });
		} else if (which === 13 && this.selectedIndex) {
			this.handleEnter({ preventDefault });
		}
	}

	handleTypeClear() {
		this.hide();
		this.clearIndex();
		this.element.innerHTML = '';
		this.items = [];
	}

	handleMouseEnter(e, node) {
		if (e.target !== node) return true;
		this.setIndex(this.items.indexOf(node) + 1, 'enter');
	}

	setIndex(index, type) {
		if (index === this.selectedIndex) return;

		this.trigger('menuchange', { type, index });
		this.items.forEach((itemNode) => removeClass(itemNode, this.options.selectedClass));

		if (index) {
			addClass(this.items[index - 1], this.options.selectedClass);
		}

		this.selectedIndex = index;
	}

	clearIndex() {
		this.items.forEach((itemNode) => removeClass(itemNode, this.options.selectedClass));
		this.selectedIndex = 0;
	}

	handleArrUp(e) {
		e.preventDefault();

		this.setIndex((this.selectedIndex - 1 + this.size) % this.size, 'up');
	}

	handleArrDown(e) {
		e.preventDefault();

		this.setIndex((this.selectedIndex + 1) % this.size, 'down');
	}

	handleEnter(e) {
		e.preventDefault();

		this.trigger('menuselect');
	}

	handleMouseDown(e, node) {
		e.preventDefault();

		this.setIndex(this.items.indexOf(node) + 1, 'click');
		this.trigger('menuselect');
	}

	handleClick(e) {
		e.preventDefault();
	}
}
