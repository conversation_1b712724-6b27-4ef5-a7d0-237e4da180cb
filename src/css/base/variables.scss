@use 'config';

// Colors
$color-black: #010e47;
$color-white: #ffffff;
$color-white-hover: #f2f3ff;
$color-orange: #ffaa00;
$color-gray: #808080;
$color-gray-500: #9ea9bf;
$color-gray-900: #10161f;

$color-yellow-600: #fcc73e;
$color-yellow-150: #fff6e0;
$color-yellow-50: #fffaee;

$color-red-200: #ffedf3;
$color-red: #d0204c;

$color-violet-200: #f3e5ff;
$color-violet: #7d25c2;

$color-green-200: #e3faec;
$color-green: #1aad55;

$color-primary-150: #edeeff;
$color-primary-300: #bdc2fe;
$color-primary-700: #3e5efc;
$color-primary-750: #093ed8;
$color-primary: $color-primary-700;
$color-primary-hover: $color-primary-750;
$color-blue: $color-primary;

$color-superprimary: #24b95f;
$color-secondary: $color-primary-150;

$color-peach-800: #f3752d;
$color-peach-600: #fe995f;
$color-peach-300: #ffcbae;
$color-peach-150: #ffeadd;
$color-peach-100: #fdf0ed;
$color-peach: $color-peach-800;

$color-alert: $color-yellow-600;
$color-alert-light: $color-yellow-150;
$color-alert2: $color-peach-800;
$color-alert2-light: $color-peach-150;
$color-status-valid: $color-green;
$color-status-valid-light: $color-green-200;
$color-status-invalid: $color-red;
$color-status-invalid-light: $color-red-200;

$color-inverse-help: $color-gray-500;
$color-help: #5f708e;
$color-tile: #dce0e8;
$color-tile-light: #edeff3;
$color-surface: #f2f4f6;
$color-placeholder: #808faa;

$color-icon-minor: #b0b9cb;
$color-text-headline: $color-black;
$color-text: #2e3a4d;
$color-bd: #c0c7d5;
$color-bg: #f5f4fa;
$color-link: var(--color-link);
$color-inverse-link: var(--color-inverse-link);
$color-hover: var(--color-hover);

// $color-facebook: #3b5998;
// $color-twitter: #1da1f2;
// $color-google: #dd4b39;
// $color-youtube: #ff0000;
// $color-linkedin: #0077b5;
// $color-instagram: #c13584;
// $color-pinterest: #bd081c;

// Font
$font-system: -apple-system, blinkmacsystemfont, 'Segoe UI', roboto, helvetica, arial, sans-serif;
$font-primary: 'Inter', $font-system;
$font-secondary: 'Poppins', $font-primary;
$font-size: var(--font-size);
$line-height: 1.6;

// Typography
$typo-space-vertical: 1em;

// Focus
$focus-outline-color: $color-primary;
$focus-outline-style: solid;
$focus-outline-width: 0.1rem;

// Spacing
$utils-spacing: (
	'0': 0,
	'xxs': 0.4rem,
	'xs': 0.8rem,
	'sm': 2.4rem,
	'md': 4.8rem,
	'lg': 6.4rem,
	'xl': 8rem,
	'2xl': 10rem,
	'3xl': 13.2rem
);
$utils-spacing-grid: (
	'0': 0,
	'xs': 0.8rem,
	'sm': 2rem,
	'md': 4rem,
	'lg': 6rem,
	'xl': 8rem,
	'2xl': 10rem
);

// Grid
$grid-columns: 12;
$grid-gutter: var(--grid-gutter);
$row-main-width: var(--row-main-width);
$row-main-width-wide: var(--row-main-width-wide);
$row-main-gutter: var(--row-main-gutter);

// Paths
$img-path: map-get(config.$paths, 'images');
$fonts-path: map-get(config.$paths, 'fonts');

// Transitions
$t: 0.3s;

// Other
$border-radius-sm: 0.8rem;
$border-radius-md: 1.2rem;
$border-radius-lg: 1.6rem;
$border-radius-xl: 2rem;

// SVGs
$svg-bullet: 'data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%204%204%22%3E%0A%20%20%3Ccircle%20cx%3D%222%22%20cy%3D%222%22%20r%3D%222%22%20fill%3D%22%233e5efc%22%2F%3E%0A%3C%2Fsvg%3E';
$svg-select: 'data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2215%22%20height%3D%2216%22%20fill%3D%22none%22%20viewBox%3D%220%200%2015%2016%22%3E%0A%20%20%3Cpath%20stroke%3D%22%233E5EFC%22%20stroke-linecap%3D%22round%22%20stroke-width%3D%222%22%20d%3D%22M2%205.5%207.5%2011%2013%205.5%22%2F%3E%0A%3C%2Fsvg%3E';
$svg-select-disabled: 'data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2215%22%20height%3D%2216%22%20fill%3D%22none%22%20viewBox%3D%220%200%2015%2016%22%3E%0A%20%20%3Cpath%20stroke%3D%22%23808FAA%22%20stroke-linecap%3D%22round%22%20stroke-width%3D%222%22%20d%3D%22M2%205.5%207.5%2011%2013%205.5%22%2F%3E%0A%3C%2Fsvg%3E';
$svg-select-reversed: 'data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%2015%2016%22%3E%0A%20%20%3Cpath%20fill%3D%22none%22%20stroke%3D%22%233e5efc%22%20stroke-linecap%3D%22round%22%20stroke-width%3D%222%22%20d%3D%22M13%2011%207.5%205.5%202%2011%22%2F%3E%0A%3C%2Fsvg%3E';
$svg-close: 'data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2215%22%20height%3D%2216%22%20fill%3D%22none%22%20viewBox%3D%220%200%2015%2016%22%3E%0A%20%20%3Cpath%20stroke%3D%22%2310161F%22%20stroke-linecap%3D%22round%22%20stroke-width%3D%222%22%20d%3D%22m3%203.5%209%209M12%203.5l-9%209%22%2F%3E%0A%3C%2Fsvg%3E';
$svg-angle-right-primary: 'data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2212%22%20viewBox%3D%220%200%2012%2012%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Cpath%20d%3D%22M5%202.4L8.6%206L5%209.6%22%20stroke%3D%22%233E5EFC%22%20stroke-width%3D%221.5%22%20stroke-linecap%3D%22round%22%2F%3E%0A%3C%2Fsvg%3E%0A';
$svg-check: 'data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2216%22%20height%3D%2216%22%20fill%3D%22none%22%20viewBox%3D%220%200%2016%2016%22%3E%0A%20%20%3Cpath%20stroke%3D%22%233E5EFC%22%20stroke-linecap%3D%22round%22%20stroke-width%3D%222%22%20d%3D%22m2.75%207.861%203.462%203.462%207.038-7%22%2F%3E%0A%3C%2Fsvg%3E';

:root {
	// Colors
	--color-black: #{$color-black};
	--color-white: #{$color-white};
	--color-white-hover: #{$color-white-hover};
	--color-orange: #{$color-orange};
	--color-gray: #{$color-gray};
	--color-gray-500: #{$color-gray-500};
	--color-gray-900: #{$color-gray-900};
	--color-yellow-600: #{$color-yellow-600};
	--color-yellow-150: #{$color-yellow-150};
	--color-yellow-50: #{$color-yellow-50};
	--color-red-200: #{$color-red-200};
	--color-red: #{$color-red};
	--color-violet-200: #{$color-violet-200};
	--color-violet: #{$color-violet};
	--color-green-200: #{$color-green-200};
	--color-green: #{$color-green};
	--color-primary-150: #{$color-primary-150};
	--color-primary-300: #{$color-primary-300};
	--color-primary-700: #{$color-primary-700};
	--color-primary-750: #{$color-primary-750};
	--color-primary: #{$color-primary};
	--color-primary-hover: #{$color-primary-hover};
	--color-blue: #{$color-blue};
	--color-superprimary: #{$color-superprimary};
	--color-secondary: #{$color-secondary};
	--color-peach-800: #{$color-peach-800};
	--color-peach-600: #{$color-peach-600};
	--color-peach-300: #{$color-peach-300};
	--color-peach-150: #{$color-peach-150};
	--color-peach-100: #{$color-peach-100};
	--color-peach: #{$color-peach};
	--color-alert: #{$color-alert};
	--color-alert-light: #{$color-alert-light};
	--color-alert2: #{$color-alert2};
	--color-alert2-light: #{$color-alert2-light};
	--color-status-valid: #{$color-status-valid};
	--color-status-valid-light: #{$color-status-valid-light};
	--color-status-invalid: #{$color-status-invalid};
	--color-status-invalid-light: #{$color-status-invalid-light};
	--color-inverse-help: #{$color-inverse-help};
	--color-help: #{$color-help};
	--color-tile: #{$color-tile};
	--color-tile-light: #{$color-tile-light};
	--color-surface: #{$color-surface};
	--color-placeholder: #{$color-placeholder};
	--color-icon-minor: #{$color-icon-minor};
	--color-text-headline: #{$color-text-headline};
	--color-text: #{$color-text};
	--color-bd: #{$color-bd};
	--color-bg: #{$color-bg};
	--font-primary: #{$font-primary};
	--font-secondary: #{$font-secondary};

	// Border Radius
	--border-radius-sm: #{$border-radius-sm};
	--border-radius-md: #{$border-radius-md};
	--border-radius-lg: #{$border-radius-lg};
	--border-radius-xl: #{$border-radius-xl};

	// Other
	--grid-gutter: 4rem;
	--row-main-gutter: 1.2rem;
	--row-main-width: calc(154rem + 2 * var(--row-main-gutter));
	--row-main-width-wide: calc(184rem + 2 * var(--row-main-gutter));
	--font-size: 1.4rem;
	--color-link: #{$color-primary};
	--color-hover: #{$color-primary-750};
	--color-inverse-link: #{$color-bd};
	--vw: calc(100vw - var(--scrollbar-width, 0rem));

	// Tailwind defaults
	--tw-translate-x: 0;
	--tw-translate-y: 0;
	--tw-rotate: 0;
	--tw-skew-x: 0;
	--tw-skew-y: 0;
	--tw-scale-x: 1;
	--tw-scale-y: 1;
	--tw-gradient-from-position: 0%;
	--tw-gradient-to-position: 100%;
	--tw-blur: blur(0);
	--tw-brightness: brightness(1);
	--tw-contrast: contrast(1);
	--tw-hue-rotate: hue-rotate(0deg);
	--tw-invert: invert(0);
	--tw-saturate: saturate(1);
	--tw-sepia: sepia(0);
	--tw-grayscale: grayscale(0);
	--tw-drop-shadow: drop-shadow(0 0 #000000);
	--tw-backdrop-blur: blur(0);
	--tw-backdrop-brightness: brightness(1);
	--tw-backdrop-contrast: contrast(1);
	--tw-backdrop-grayscale: grayscale(0);
	--tw-backdrop-hue-rotate: hue-rotate(0deg);
	--tw-backdrop-invert: invert(0);
	--tw-backdrop-opacity: opacity(1);
	--tw-backdrop-saturate: saturate(1);
	--tw-backdrop-sepia: sepia(0);

	@media (config.$sm-up) {
		--row-main-gutter: 3.2rem;
	}
	@media (config.$md-up) {
		--font-size: 1.6rem;
	}
	@media (config.$lg-up) {
		--row-main-gutter: 4rem;
	}
}
