@use 'config';
@use 'base/variables';

html {
	color: variables.$color-text;
	font-size: 62.5%;
}

body {
	font-family: variables.$font-primary;
	font-size: variables.$font-size;
	line-height: variables.$line-height;
}

// Headings
.h0,
h1,
.h1,
h2,
.h2,
%h3,
h3,
.h3,
h4,
.h4,
h5,
.h5,
h6,
.h6 {
	margin: 2em 0 0.6em;
	color: variables.$color-text-headline;
	font-family: variables.$font-secondary;
	font-weight: bold;
	font-size: var(--font-size-mobile);
	@media (config.$md-down) {
		line-height: 1.5;
	}
	@media (config.$md-up) {
		font-size: var(--font-size-desktop);
	}
}
.h0 {
	--font-size-mobile: 4.2rem;
	--font-size-desktop: 10rem;
}
h1,
.h1 {
	--font-size-mobile: 2.4rem;
	--font-size-desktop: 4rem;
}
h2,
.h2 {
	--font-size-mobile: 2rem;
	--font-size-desktop: 3rem;
}
%h3,
h3,
.h3 {
	--font-size-mobile: 1.8rem;
	--font-size-desktop: 2.2rem;
}
h4,
.h4 {
	--font-size-mobile: 1.6rem;
	--font-size-desktop: 1.8rem;
	line-height: 1.6;
}
h5,
.h5 {
	--font-size-mobile: 1.4rem;
	--font-size-desktop: 1.6rem;
}
h6,
.h6 {
	--font-size-mobile: 1.2rem;
	--font-size-desktop: 1.4rem;
}

// Paragraph
p {
	margin: 0 0 variables.$typo-space-vertical;
}
hr {
	height: 0.1rem;
	margin: variables.$typo-space-vertical 0;
	border: solid variables.$color-tile-light;
	border-width: 0.1rem 0 0;
	overflow: hidden;
}

// Blockquote
blockquote {
	margin: 2em 0;
	padding: 2rem 4rem;
	border-radius: variables.$border-radius-xl;
	background: variables.$color-bg;
	& > *:last-child {
		margin-bottom: 0;
	}
}

// Links
a,
.as-link {
	--color-link: #{variables.$color-primary};
	--color-hover: #{variables.$color-primary-750};
	--color-link-decoration: var(--color-link);
	--color-hover-decoration: var(--color-hover);
	color: var(--color-link);
	text-decoration: underline;
	text-decoration-color: var(--color-link-decoration);
	transition: color variables.$t, text-decoration-color variables.$t;
	-webkit-tap-highlight-color: transparent;
	text-underline-offset: 0.3rem;
	&.tw-text-help,
	&.u-c-help {
		--color-link: #{variables.$color-help};
	}
	&.u-c-text {
		--color-link: #{variables.$color-text};
	}
	.hoverevents &:hover {
		color: var(--color-hover);
		text-decoration-color: var(--color-hover-decoration);
	}
}

.as-link {
	cursor: pointer;
}

// Lists
*:is(ul, ol, dl) {
	margin: 0 0 variables.$typo-space-vertical;
	padding: 0;
	list-style: none;
	& & {
		margin: calc(variables.$typo-space-vertical / 4) 0;
	}
}
li {
	margin: 0 0 calc(variables.$typo-space-vertical / 4);
	padding: 0 0 0 2.5rem;
}
ul {
	> li {
		background-image: url(variables.$svg-bullet);
		background-position: 0.8rem 0.6em;
		background-repeat: no-repeat;
		background-size: 0.5rem 0.5rem;
	}
}
ol {
	counter-reset: item;
	> li {
		position: relative;
		padding: 0 0 0 3.6rem;
		&::before {
			content: counter(item) ')';
			counter-increment: item;
			position: absolute;
			top: 0;
			left: 1rem;
			color: variables.$color-primary;
			font-variant-numeric: tabular-nums;
		}
	}
	ol {
		> li {
			padding: 0 0 0 2.5rem;
			&::before {
				content: counter(item, lower-alpha) ')';
				left: 0;
			}
		}
	}
}
dt {
	margin: 0;
	font-weight: bold;
}
dd {
	margin: 0 0 calc(variables.$typo-space-vertical / 2);
	padding: 0;
}

// Tables
table {
	--table-x-padding: 1.2rem;
	--table-y-padding: 1.2rem;
	--table-bd-color: #{variables.$color-tile};
	clear: both;
	border-collapse: collapse;
	border-spacing: 0;
	empty-cells: show;
	width: 100%;
	border: none;
	font-size: 1.4rem;
	@media (config.$md-up) {
		font-size: 1.5rem;
	}
}
caption {
	padding: 0 0 1rem;
	font-weight: bold;
	text-align: left;
	caption-side: top;
}
*:is(td, th) {
	vertical-align: top;
	padding: var(--table-y-padding) var(--table-x-padding);
	border: 0.1rem solid var(--table-bd-color);
	border-width: 0.1rem 0;
	&:first-child {
		padding-left: 0;
	}
	&:last-child {
		padding-right: 0;
	}
}
th {
	font-weight: bold;
	text-align: left;
}
tr:first-child > * {
	border-top: none;
}

// Image
figure {
	margin-bottom: variables.$typo-space-vertical;
	text-align: center;
	img {
		border-radius: variables.$border-radius-xl;
	}
}
figcaption {
	margin-top: 1.2rem;
	color: variables.$color-help;
	font-style: italic;
	font-size: 1.4rem;
}

img {
	max-width: 100%;
	height: auto;
}
