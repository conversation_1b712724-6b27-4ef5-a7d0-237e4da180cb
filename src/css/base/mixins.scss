@use 'config';
@use 'base/variables';

// Grid
@mixin generate-grid-size($breakpoints: config.$breakpoints, $columns: variables.$grid-columns, $auto: true, $auto-grow: true) {
	@if ($auto) {
		&--auto {
			width: auto;
		}
	}
	@if ($auto-grow) {
		&--autogrow {
			flex: 1 0 auto;
			width: auto;
		}
	}
	@for $column from 1 to ($columns + 1) {
		@at-root #{&}--#{$column}-#{$columns} {
			width: percentage(calc($column / $columns));
		}
	}
	@each $breakpoint, $value in $breakpoints {
		@include mq-checker($breakpoint) {
			@if ($auto) {
				&--auto {
					@include suffix($breakpoint) {
						width: auto;
					}
				}
			}
			@if ($auto-grow) {
				&--autogrow {
					@include suffix($breakpoint) {
						flex: 1 0 auto;
						width: auto;
					}
				}
			}
			@for $column from 1 to ($columns + 1) {
				@at-root #{&}--#{$column}-#{$columns} {
					@include suffix($breakpoint) {
						width: percentage(calc($column / $columns));
					}
				}
			}
		}
	}
}

@mixin generate-grid-order($breakpoints: config.$breakpoints, $columns: variables.$grid-columns) {
	@for $column from 1 to ($columns + 1) {
		@at-root #{&}--#{$column} {
			order: $column;
		}
	}
	@each $breakpoint, $value in $breakpoints {
		@include mq-checker($breakpoint) {
			@for $column from 1 to ($columns + 1) {
				@at-root #{&}--#{$column} {
					@include suffix($breakpoint) {
						order: $column;
					}
				}
			}
		}
	}
}

@mixin mq-checker($breakpoint, $type: 'up') {
	@if (map-has-key(config.$breakpoints-vars, '#{$breakpoint}-#{$type}')) {
		@if (
			($type == 'up' and ($breakpoint == 'sm' or $breakpoint == 'md' or $breakpoint == 'lg')) or
				($type == 'Down' and ($breakpoint == 'lg' or $breakpoint == 'xl'))
		) {
			@media (map-get(config.$breakpoints-vars, '#{$breakpoint}-#{$type}')), print {
				@content;
			}
		} @else {
			@media (map-get(config.$breakpoints-vars, '#{$breakpoint}-#{$type}')) {
				@content;
			}
		}
	} @else {
		@error 'Unfortunately, breakpoint `#{$breakpoint}` is not defined in config.js';
	}
}

// Suffix
@mixin suffix($suffix, $delimiter: '\\@') {
	&#{$delimiter}#{$suffix} {
		@content;
	}
}

// Breakpoints
@mixin generate-breakpoints($breakpoints: config.$breakpoints) {
	@content;
	@each $breakpoint, $value in $breakpoints {
		@include mq-checker($breakpoint) {
			@include suffix($breakpoint) {
				@content;
			}
		}
	}
}

@mixin breakpoint-up($breakpoint, $breakpoints: config.$breakpoints) {
	$bp: map-get($breakpoints, $breakpoint);
	$value: if($bp != 0, $bp, null);

	@if $value {
		@media (min-width: $value) {
			@content;
		}
	} @else {
		@content;
	}
}

// Utilities
@mixin generate-utilities($utilities: $utilities) {
	@each $breakpoint,
		$value
			in map-merge(
				(
					_: 0
				),
				config.$breakpoints
			)
	{
		@include breakpoint-up($breakpoint) {
			$suffix: if($value == 0, '', '\\@#{$breakpoint}');

			@each $key, $utility in $utilities {
				@if type-of($utility) == 'map' and (map-get($utility, responsive) or $suffix == '') {
					@include generate-utility($utility, $suffix);
				}
			}
		}
	}
}
@mixin generate-utility($utility, $suffix) {
	$values: map-get($utility, values);

	@each $key, $value in $values {
		$property: map-get($utility, property);
		$class: map-get($utility, class);

		.u-#{$class}-#{$key}#{$suffix} {
			#{$property}: $value;
		}
	}
}

// Line clamp
@mixin line-clamp($lines: 1) {
	overflow: hidden;
	@if ($lines == 1) {
		white-space: nowrap;
		text-overflow: ellipsis;
	} @else {
		display: -webkit-box;
		-webkit-line-clamp: #{$lines};
		/*! autoprefixer: ignore next */
		-webkit-box-orient: vertical;
	}
}

// Clearfix
@mixin clearfix() {
	&::before,
	&::after {
		content: '';
		display: table;
	}
	&::after {
		clear: both;
	}
}

// Hiding content
@mixin vhide() {
	position: absolute;
	width: 0.1rem;
	height: 0.1rem;
	margin: -0.1rem;
	padding: 0;
	border: 0;
	overflow: hidden;
	clip: rect(0 0 0 0);
}

@mixin button-reset {
	display: inline-block;
	padding: 0;
	border: none;
	border-radius: 0;
	background: none;
	color: inherit;
	line-height: inherit;
	text-align: inherit;
	cursor: pointer;
	appearance: none;
}

// AA button target size
@mixin target-size-button {
	position: relative;
	&::before {
		content: '';
		position: absolute;
		top: 50%;
		left: 50%;
		width: 24px;
		height: 24px;
		transform: translate(-50%, -50%);
	}
}

@mixin dialog-reset {
	max-width: none;
	max-height: none;
	margin: 0;
	padding: 0;
	border: none;
	background: none;
}

// Vertical scroll shadows
@mixin vertical-scroll-shadows($background-color: white, $shadow-color: black, $shadow-opacity: 0.05, $shadow-height: 1rem) {
	border-radius: 4px;
	background: linear-gradient($background-color 0%, rgba($background-color, 0)),
		linear-gradient(rgba($background-color, 0), $background-color 100%) 0 100%,
		linear-gradient(rgba($shadow-color, $shadow-opacity), rgba($shadow-color, 0)),
		linear-gradient(rgba($shadow-color, 0), rgba($shadow-color, $shadow-opacity)) 0 100%;
	background: {
		color: $background-color;
		repeat: no-repeat;
		size: 100% calc(5rem + #{$shadow-height}), 100% calc(5rem + #{$shadow-height}), 100% $shadow-height, 100% $shadow-height;
		attachment: local, local, scroll, scroll;
	}
}

@mixin list-check {
	li {
		padding: 0 0 0 3.1rem;
		background-image: url(variables.$svg-check);
		background-position: 0.8rem 0.4em;
		background-size: 1.5rem 1.5rem;
	}
}
