/* stylelint-disable selector-id-pattern */
@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

#snippet--header {
	position: sticky;
	top: 0;
	z-index: 10;
	transition: transform variables.$t;
	// MODIF
	// scroll dolů - schováí
	&.is-hidden,
	&.is-unpinned {
		transform: translateY(-100%);
	}
	&.is-pinned::before,
	&.is-unpinned::before {
		opacity: 0;
	}
	&:has(.header--fixed) {
		position: fixed;
		top: 0;
		right: 0;
		left: 0;
	}
}
.header {
	--link-item-icon-size: 2.5rem;
	--link-gap: 0.4rem;
	--link-w: 3.7rem;
	--link-h: 4.5rem;
	--header-top-padding: 0.4rem;
	--row-main-gutter: 0.4rem;
	--row-main-width: calc(182rem + 2 * var(--row-main-gutter));
	--header-bottom-padding: 0.4rem;
	$s: &;
	position: relative;
	margin-bottom: calc(var(--header-bottom-padding) * -1);
	padding: 0 0 var(--header-bottom-padding);
	border-bottom: 0.1rem solid transparent;
	transition: background-color variables.$t, border-color variables.$t;
	pointer-events: none;
	.row-main {
		position: static;
	}
	&__main {
		position: relative;
		padding-top: var(--header-top-padding);
		pointer-events: auto;
	}
	&__inner {
		display: flex;
		gap: var(--link-gap);
	}
	&__logo {
		flex: 0 0 auto;
		width: 12rem;
		margin-right: auto;
		font-size: 0;
	}
	&__logo-svg {
		width: 100%;
		height: auto;
		aspect-ratio: 192/62;
	}
	&__msg {
		padding-top: 0.4rem;
		pointer-events: auto;
	}
	&__burger {
		margin-left: 0.3rem;
	}

	// STATES
	.is-pinned:not(.is-hidden) & {
		border-bottom-color: variables.$color-tile-light;
		background: variables.$color-bg;
		// background: rgba(variables.$color-bg, 0.85);
		// backdrop-filter: blur(10rem); // breaks outline of header submenus
	}
	&.is-menu-open,
	&:has(.is-open),
	&:has(.is-hover),
	&:has(.f-search__inp:focus) {
		backdrop-filter: none;
	}

	// MQ
	@media (max-width: 899px) {
		&__inner {
			align-items: center;
			padding: 0.6rem;
			border-radius: 1.6rem;
			background: variables.$color-white;
		}
		&__top,
		&__center,
		&__right,
		&__bottom {
			display: contents;
		}
		&__logo {
			order: 1;
		}
		&__search {
			order: 2;
		}
		&__login {
			order: 3;
		}
		&__basket {
			order: 4;
		}
		&__menu-wrap {
			z-index: 1;
			order: 5;
		}
		&__burger {
			position: relative;
			z-index: 1;
		}
		&__menu {
			visibility: hidden;
			opacity: 0;
			transform: translateX(-5000px);
			transition: transform 0s variables.$t, opacity variables.$t, visibility variables.$t;
		}
		&__contact,
		&__top &__service {
			display: none;
		}

		// MODIF
		&--simple {
			#{$s}__contact {
				display: flex;
				order: 2;
				margin: 0 auto;
			}
			#{$s}__logo {
				margin: 0;
			}
		}

		// STATES
		&.is-menu-open &__menu {
			visibility: visible;
			opacity: 1;
			transform: translateX(0);
			transition: transform 0s 0s, opacity variables.$t, visibility variables.$t;
		}

		// MQ
		@media (min-width: 636px) {
			&__search {
				flex: 1;
			}
			&__login::after {
				border-left: none;
			}
		}
	}
	@media (config.$xs-up) {
		--link-w: 4.1rem;
		--link-item-padding: 1rem 0.8rem;
		&__logo {
			width: 13.4rem;
		}
	}
	@media (min-width: 636px) {
		--link-gap: 0.8rem;
		--header-top-padding: 0.8rem;
		--row-main-gutter: 0.8rem;
		&__logo {
			width: 15.4rem;
		}
		&__burger {
			margin-left: 0.5rem;
		}
	}
	@media (min-width: 900px) {
		--header-top-padding: 1.5rem;
		--row-main-gutter: 1.2rem;
		--link-w: 6.2rem;
		--link-h: 5rem;
		--link-item-icon-size: 3rem;
		--header-bottom-padding: 0.8rem;
		&__main {
			padding-top: 1.2rem;
			transition: padding-top variables.$t;
		}
		&__inner {
			gap: 1.2rem;
			flex-direction: column;
			transition: gap variables.$t;
		}
		&__top {
			z-index: 2;
			display: flex;
			gap: 2.4rem;
			align-items: center;
			margin: 0 0.8rem;
		}
		&__service {
			flex: 0 1 24%;
		}
		&__center {
			display: flex;
			gap: 1.5rem;
			flex: 1;
			align-items: center;
			margin: 0 auto;
		}
		&__contact {
			flex: 0 0 auto;
			.person__img {
				transition: width variables.$t;
			}
			.person__name {
				transition: font-size variables.$t;
			}
		}
		&__search {
			flex: 1;
			min-width: 30.5rem;
			max-width: 42.5rem;
		}
		&__right {
			z-index: 1;
			display: flex;
			gap: var(--link-gap);
			align-items: center;
		}
		&__bottom {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0.8rem;
			border-radius: variables.$border-radius-xl;
			background: variables.$color-white;
			transition: padding variables.$t;
		}
		&__logo {
			width: 11.7rem;
			margin-right: auto;
			transition: width variables.$t;
		}
		&__menu &__service,
		&__burger {
			display: none;
		}
		&__msg {
			transition: opacity variables.$t;
		}
		&__menu-wrap {
			display: contents;
		}

		// STATES
		&:has(.m-main__item.is-hover) &__msg,
		&:has(.m-main__item.is-open) &__msg {
			opacity: 0;
		}

		// MODIF
		&--simple {
			#{$s}__inner {
				flex-direction: row;
				align-items: center;
				padding: 0.6rem;
				border-radius: 1.6rem;
				background: variables.$color-white;
			}
			#{$s}__top,
			#{$s}__right,
			#{$s}__bottom {
				display: contents;
			}
			#{$s}__logo {
				order: 1;
				margin: 0;
			}
			#{$s}__contact {
				order: 2;
				margin: 0 auto;
			}
			#{$s}__login {
				order: 3;
			}
			#{$s}__basket {
				order: 4;
			}
		}

		// condensed sticky header
		.is-pinned &,
		.is-unpinned & {
			--link-h: 4rem;
			#{$s}__main {
				padding-top: 0.8rem;
			}
			#{$s}__inner {
				gap: 0.6rem;
			}
			#{$s}__contact {
				.person__img {
					width: 3.8rem;
				}
				.person__name {
					font-size: 0.9rem;
				}
			}
			#{$s}__search {
				.inp-text {
					--inp-padding-x: 2rem;
					--inp-padding-y: 0.8rem;
					--inp-h: 4.2rem;
				}
				.f-search__btn {
					--btn-h: 4.2rem;
				}
			}
			#{$s}__bottom {
				padding: 0.4rem;
			}
			#{$s}__logo {
				width: 14.8rem;
			}
			#{$s}__menu {
				.m-main__btn {
					--btn-h: 4.5rem;
				}
			}
		}
	}
	@media (config.$lg-up) {
		&__service {
			flex: 1;
		}
		&__center {
			flex: 0 0 51%;
		}
		&__logo {
			width: 15.5rem;
		}
	}
	@media (config.$xl-up) {
		&__logo {
			width: 19.2rem;
		}
		&__top {
			gap: 3.2rem;
		}
		&__center {
			gap: clamp(1.5rem, 2.9vw, 5.5rem);
		}
		&__search {
			flex: 0 0 auto;
			width: clamp(30.5rem, 27vw, 50rem);
			max-width: 48rem;
		}
	}
	@media (config.$xxxl-up) {
		&__top {
			margin: 0 4rem;
		}
	}
}
