@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.header-link {
	$s: &;
	padding: 0.8rem 1.5rem;
	border: none;
	border-radius: variables.$border-radius-sm;
	background: variables.$color-white;
	color: variables.$color-black;
	line-height: 1.3;
	text-align: left;
	text-decoration: none;
	transition: background-color variables.$t, color variables.$t;
	cursor: pointer;

	// MODIF
	&--icon {
		position: relative;
		display: inline-block;
		justify-content: center;
		width: 5.6rem;
		padding: 3rem 0.7rem 0;
		background: transparent;
		color: rgba(variables.$color-white, 0.8);
		font-weight: 400;
		font-size: 0.9rem;
		white-space: nowrap;
		text-align: center;
		text-overflow: ellipsis;
		overflow: hidden;
		#{$s}__icon {
			position: absolute;
			top: 0;
			left: 50%;
			width: 3rem;
			height: 3rem;
			transform: translateX(-50%);
		}
	}

	// HOVERS
	&.is-active,
	&.is-open,
	.is-open &,
	.is-hover &,
	.hoverevents &:hover {
		background: variables.$color-black;
		color: rgba(variables.$color-white, 0.8);
	}

	// MQ
	@media (config.$lg-down) {
		.header__bottom & {
			font-size: 1.4rem;
			#{$s}__secondary {
				display: block;
				color: variables.$color-black;
				font-size: 1.2rem;
			}
		}

		// STATES
		.header__bottom &.is-open,
		.header__bottom &.is-active,
		.hoverevents .header__bottom &:hover {
			background-color: variables.$color-bg;
			color: variables.$color-black;
		}
	}
	@media (config.$lg-up) {
		--icon-size: 1.2rem;
		--gap: 0.4rem;
		--icon-color: #{variables.$color-white};
		display: inline-flex;
		padding: 0.3rem 0.7rem;
		background: transparent;
		color: rgba(variables.$color-white, 0.8);
		font-weight: 600;
		font-size: 1.1rem;
		text-transform: uppercase;
		transition: background-color variables.$t, color variables.$t;
		&__secondary {
			color: variables.$color-green-gray;
		}

		// MODIF
		&--icon {
			width: 9.1rem;
			padding: 4.4rem 0.5rem 0.7rem;
			font-size: 1.2rem;
			text-transform: none;
			#{$s}__icon {
				position: absolute;
				top: 0.4rem;
				width: 4rem;
				height: 4rem;
			}
			#{$s}__secondary {
				display: inline-block;
				max-width: 100%;
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
			}
		}
	}
}
