@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.footer {
	$s: &;
	&__main {
		--color-text-headline: inherit;
		position: relative;
		width: 100%;
		max-width: 184rem;
		margin: 0 auto;
		padding: 3.6rem 0;
		border-radius: 3rem;
		border-radius: variables.$border-radius-md;
		background: variables.$color-black;
		color: variables.$color-white;
	}
	&__cell {
		display: flex;
		gap: 1.2rem;
		flex-direction: column;
		width: 50%;

		& > * {
			margin-bottom: 0;
		}
	}
	&__title {
		color: variables.$color-white;
	}
	&__socials {
		padding-top: 1rem;
	}
	&__store {
		max-width: 26.5rem;
	}
	&__logos {
		margin-top: 3.2rem;
		padding: 4rem 0;
		border: 0.1rem solid rgba(variables.$color-white, 0.1);
		border-width: 0.1rem 0;
	}
	&__awards {
		display: flex;
		flex-direction: row-reverse;
		justify-content: center;
	}
	&__logo {
		width: 12rem;
	}
	&__copy {
		padding-top: 3.2rem;
		color: variables.$color-inverse-help;
		font-size: 1.3rem;
	}
	&__top {
		position: absolute;
		bottom: 0;
		left: 50%;
		transform: translate(-50%, 50%);
	}

	// MODIF
	&__cell--contact {
		width: 100%;
	}
	&__cell--store {
		a {
			--color-link: #{variables.$color-inverse-help};
			--color-hover: #{variables.$color-white};
			text-decoration: none;
		}
	}
	&--simple &__cell {
		width: 100%;
	}

	// MQ
	@media (config.$xl-down) {
		&__grid {
			--grid-x-spacing: 2.8rem;
		}
		&__awards {
			img:not(:last-child) {
				margin-left: -1rem;
			}
		}
	}
	@media (config.$lg-down) {
		&__grid {
			--grid-y-spacing: 3.2rem;
		}
		&__person {
			justify-content: center;
			width: 100%;
		}
		&__logos .grid {
			--grid-x-spacing: 2.6rem;
			--grid-y-spacing: 3.4rem;
			justify-content: center;
		}
		&__awards {
			img {
				width: 5rem;
			}
		}

		// MODIF
		&__cell--contact {
			align-items: center;
			padding-bottom: 3.2rem;
			&::after {
				content: '';
				position: absolute;
				right: 0;
				bottom: 0;
				left: 0;
				height: 0.1rem;
				background-color: rgba(variables.$color-white, 0.1);
			}
		}
		&__cell--store {
			.item-icon__icon {
				display: none;
			}
		}
		&--simple &__title {
			text-align: center;
		}
	}
	@media (config.$sm-down) {
		&__main {
			--row-main-gutter: 2rem;
		}
	}

	@media (config.$sm-up) and (config.$lg-down) {
		&__cell {
			width: 33.33%;
		}
		&__store {
			max-width: 15.4rem;
			margin-right: auto;
			margin-left: auto;
		}

		// MODIF
		&__cell--contact,
		&__cell--store {
			width: 50%;
			padding-bottom: 7.2rem;
			text-align: center;
		}
		&__cell--store {
			position: relative;
			a {
				display: block;
			}
			&::after {
				content: '';
				position: absolute;
				right: 0;
				bottom: 0;
				left: calc(var(--grid-x-spacing) * -1);
				height: 0.1rem;
				background-color: rgba(variables.$color-white, 0.1);
			}
		}
	}
	@media (config.$xs-up) {
		&__logo {
			width: 15.7rem;
		}
	}
	@media (config.$md-up) {
		&__logos {
			.grid {
				justify-content: space-between;
			}
		}
	}
	@media (config.$lg-up) {
		&__main {
			padding: 11rem 0 7rem;
			border-radius: 3rem;
		}
		&__cell {
			gap: 2.8rem;
			width: 29%;
			padding-top: 0.8rem;
		}
		&__title {
			max-width: 25rem;
			text-wrap: balance;
		}
		&__socials {
			padding-top: 2rem;
		}
		&__logos {
			margin-top: 11rem;
		}
		&__logo {
			width: 17.9rem;
		}
		&__copy {
			padding-top: 4rem;
			font-size: 1.5rem;
		}

		// MODIF
		&__cell--menu {
			width: 16%;
			padding-top: 1rem;
		}
		&__cell--store {
			width: 23%;
			font-size: 1.5rem;
		}
		&--simple {
			#{$s}__main {
				padding: 5.6rem 0 4.8rem;
			}
			#{$s}__cell {
				display: flex;
				flex-direction: row;
				justify-content: center;
				align-items: center;
				padding: 0 0 4rem;
				&::after {
					content: '';
					position: absolute;
					right: 0;
					bottom: 0;
					left: 0;
					height: 0.1rem;
					background-color: rgba(variables.$color-white, 0.1);
				}
			}
			#{$s}__title {
				max-width: 17.5rem;
			}
		}
	}
	@media (config.$xl-up) {
		&__cell {
			width: 23%;
		}
		&__awards {
			gap: 2rem;
		}

		// MODIF
		&__cell--menu {
			width: 18%;
		}
	}
}
