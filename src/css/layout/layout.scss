@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

html {
	--scroll-offset: var(--header-height);
	box-sizing: border-box;
	scroll-behavior: smooth;
	scrollbar-gutter: stable;
	&.tracy-bs-visible.tracy-bs-visible {
		overflow: visible;
	}
}
*:target {
	scroll-margin-top: var(--scroll-offset);
}
*,
*::before,
*::after {
	box-sizing: inherit;
}
body {
	position: relative;
	display: flex;
	flex-direction: column;
	min-width: 32rem;
	min-height: 100dvh;
	overflow-y: scroll;
	// přechod pod hlavičkou
	&::before {
		content: '';
		position: absolute;
		top: 0;
		right: 0;
		left: 0;
		z-index: -1;
		max-width: 100%;
		height: 10.1rem;
		background: linear-gradient(180deg, rgba(255, 255, 255, 0) 57%, #ffffff 100%),
			linear-gradient(260.22deg, #e6f1ff 0%, #fdf0ed 29.77%, #f3e5ff 50%, #d3e7fe 100%);
		transition: opacity variables.$t;
		pointer-events: none;
	}
	// scrollbar style
	::-webkit-scrollbar {
		width: 0.6rem;
		height: 0.6rem;
	}
	::-webkit-scrollbar-track {
		background: transparent;
	}
	::-webkit-scrollbar-thumb {
		border-radius: 0.8rem;
		background: variables.$color-bg;
	}
	::-webkit-scrollbar-thumb:hover {
		background: variables.$color-bg;
	}
	// otevreny stavy (zamezeni scrollu na body)
	&:has(.b-pickup-modal:not(.u-js-hide)),
	&.is-modal-open,
	&:has(.b-modal.is-opened),
	&:has(.m-main__item.is-open),
	&:has(.m-main__item.is-hover),
	&.is-locked,
	&:has(.f-search.is-open),
	&:has(.b-login.is-open),
	&:has(.b-basket.is-open),
	&:has(.b-filter__helper.is-open),
	&.is-menu-open {
		overflow: clip;
		.header--fixed {
			padding-right: var(--scrollbar-width);
		}
	}

	// MODIF
	&.has-bg::before {
		top: 0;
		left: 50%;
		width: var(--vw);
		max-width: 192rem;
		height: auto;
		min-height: 28rem;
		background: url(variables.$img-path + 'illust/dronzone-bg-landing.webp');
		background-position: center bottom;
		background-size: cover;
		transform: translateX(-50%);
		aspect-ratio: 1920 / 470;
	}

	// MQ
	@media (min-width: 900px) {
		&::before {
			height: 25rem;
		}
	}
}
:first-child {
	margin-top: 0;
}
