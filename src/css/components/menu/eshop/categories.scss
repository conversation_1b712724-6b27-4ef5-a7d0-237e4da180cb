@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.m-categories {
	padding: 3rem 3.5rem 3rem 3rem;
	background: variables.$color-white;
	&__list {
		@extend %reset-ul;
	}
	&__item {
		@extend %reset-ul-li;
	}
	&__link {
		--icon-size: 1rem;
		--gap: 0.6rem;
		display: flex;
		gap: var(--gap);
		align-items: center;
		min-height: var(--link-size);
		padding: 0.3rem 1.2rem 0.3rem calc(var(--offset) + var(--gap) + var(--icon-size));
		border-radius: variables.$border-radius-sm;
		color: inherit;
		text-decoration: none;
		transition: background-color variables.$t, color variables.$t;
		.icon-svg {
			width: var(--icon-size);
			color: #{variables.$color-black};
			transition: transform variables.$t;
		}
		&--w-submenu {
			padding-left: var(--offset);
		}
	}

	// MODIF
	&__list--1 {
		--link-size: 3.8rem;
		--offset: 1.2rem;
		font-weight: bold;
		font-size: 1.6rem;
	}
	&__list--2 {
		--offset: 2.6rem;
		--link-size: 3.6rem;
		font-weight: 400;
	}
	&__list--3 {
		--link-size: 3.3rem;
		--offset: 3.8rem;
		font-size: 1.5rem;
	}
	&__list--4 {
		--link-size: 2.9rem;
		--offset: 6.8rem;
		font-size: 1.4rem;
	}
	&__list--4 &__link {
		position: relative;
		padding-left: var(--offset);
		&::before {
			content: '';
			position: absolute;
			top: 50%;
			left: 5.5rem;
			width: 0.4rem;
			height: 0.4rem;
			border-radius: 50%;
			background: variables.$color-black;
			transform: translateY(-50%);
		}
	}

	// STATES
	&__link.is-active {
		position: relative;
		color: variables.$color-primary;
		&::after {
			content: '';
			position: absolute;
			top: 0;
			right: -3.5rem;
			bottom: 0;
			width: 0.5rem;
			border-radius: variables.$border-radius-sm 0 0 variables.$border-radius-sm;
			background: variables.$color-primary;
		}
		.icon-svg {
			--icon-color: #{variables.$color-primary};
			transform: rotate(90deg);
		}
	}
	&__list--4 &__link.is-active {
		&::before {
			background: variables.$color-primary;
		}
	}

	// HOVERS
	.hoverevents &__link:hover {
		background-color: variables.$color-bg-light;
		color: inherit;
	}
	.hoverevents &__link.is-active:hover {
		color: variables.$color-primary;
	}
}
