@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.m-product-categories {
	font-size: 1.4rem;
	line-height: normal;
	&__title {
		font-size: 1.5rem;
		line-height: calc(23 / 15);
	}
	a {
		text-decoration: none;
	}
	.icon-svg {
		vertical-align: baseline;
		width: 1rem;
		color: variables.$color-black;
	}
	&__item {
		margin: 0 0 1rem;
	}

	// MQ
	@media (config.$md-up) {
		font-size: 1.5rem;
		line-height: calc(23 / 15);
		&__title {
			font-size: 1.6rem;
			line-height: calc(22 / 16);
		}
	}
}
