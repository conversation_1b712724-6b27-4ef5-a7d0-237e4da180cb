@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.m-product {
	font-size: 1.5rem;
	&__list {
		@extend %reset-ul;
	}
	&__item {
		@extend %reset-ul-li;
	}
	&__link {
		--color-link: #{variables.$color-text};
		--color-hover: #{variables.$color-primary};
		position: relative;
		display: flex;
		gap: 1rem;
		align-items: center;
		padding: 1.6rem 2rem;
		border-radius: variables.$border-radius-lg;
		text-decoration: none;
		overflow: clip;
		transition: color variables.$t, background-color variables.$t;
		&::before {
			content: '';
			position: absolute;
			top: 0;
			right: 0;
			bottom: 0;
			width: 0.4rem;
			background: variables.$color-primary;
			visibility: hidden;
			opacity: 0;
			transition: opacity variables.$t, visibility variables.$t;
		}
		.icon-svg--star {
			top: -0.05em;
			color: variables.$color-alert;
		}
	}

	// STATES
	&__link.is-active {
		background: variables.$color-bg;
		font-weight: bold;
		&::before {
			visibility: visible;
			opacity: 1;
		}
	}
}
