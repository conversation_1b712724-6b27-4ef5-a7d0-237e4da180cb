@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.m-tabs {
	display: flex;
	gap: 2rem;
	flex-direction: column;
	&__btn {
		--btn-icon-size: 1.5rem;
		--btn-gap: 0.6rem;
	}
	&__search {
		max-width: 35rem;
	}

	// STATES
	&.is-limited &__tabs {
		max-height: 11.3rem;
	}
	&:not(.is-limited) &__btn--more .btn__inner:nth-child(1),
	&.is-limited &__btn--more .btn__inner:nth-child(2) {
		display: none;
	}
	&:not(.is-limited) &__btn--more .btn__icon {
		transform: scale(-1);
	}

	// MQ
	@media (config.$md-down) {
		&__btn {
			--btn-h: 3.4rem;
			--btn-padding: 0.3rem 1.4rem;
		}
	}
	@media (config.$md-up) {
		gap: 2.4rem;
		&__search {
			max-width: 31rem;
		}
		// STATES
		&.is-limited {
			flex-direction: row;
		}
		&.is-limited &__tabs {
			max-height: 4.4rem;
		}
		&.is-limited &__search {
			flex: 1 0 31rem;
			max-width: none;
			padding: 0 2.4rem;
			border-left: 0.1rem solid variables.$color-tile;
		}
	}
}
