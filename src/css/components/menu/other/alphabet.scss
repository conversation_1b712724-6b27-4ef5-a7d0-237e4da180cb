@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.m-alphabet {
	.embla__btn {
		width: 4rem;
		height: 4rem;
		.icon-svg {
			width: 1.4rem;
			height: 1.4rem;
		}
	}
	&__list {
		--grid-x-spacing: 0.5rem;
	}
	&__link {
		display: inline-flex;
		justify-content: center;
		align-items: center;
		width: 2.8rem;
		height: 4.6rem;
		border: 0.1rem solid variables.$color-bg;
		border-radius: variables.$border-radius-sm;
		background: variables.$color-white;
		font-size: 1.5rem;
		text-decoration: none;
		transition: border-color variables.$t;
	}

	// STATES
	.hoverevents &__link:hover,
	&__link.is-active {
		border-color: variables.$color-bd;
	}

	// MQ
	@media (config.$lg-down) {
		--arrow-position: #{variables.$row-main-gutter};
		margin-right: calc(variables.$row-main-gutter * -1);
		margin-left: calc(variables.$row-main-gutter * -1);
		padding: 0 variables.$row-main-gutter;
	}
}
