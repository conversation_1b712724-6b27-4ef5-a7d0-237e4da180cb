@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.m-social {
	$s: &;
	margin: 0 0 3.2rem;
	font-size: 1.3rem;
	text-align: center;
	&__title {
		margin: 0 0 1.6rem;
	}
	&__list {
		--grid-gutter: 2rem;
	}
	&__item {
		flex: 1 1 auto;
		width: auto;
	}
	&__link {
		--color-link-decoration: transparent;
		--color-hover-decoration: var(--color-hover);
		display: flex;
		gap: 0.8rem;
		flex-direction: column;
		align-items: center;
	}
	&__img {
		width: 4.5rem;
		aspect-ratio: 1/1;
	}

	// MODIF
	&--bg {
		display: flex;
		flex-direction: column;
		justify-content: center;
		height: 100%;
		margin: 0;
		padding: 2rem;
		border-radius: variables.$border-radius-lg;
		background: variables.$color-bg;
		#{$s}__link {
			flex-direction: column;
		}
		#{$s}__nick {
			display: block;
		}
		#{$s}__item {
			flex: 0 0 auto;
		}
	}

	// MQ
	@media (config.$md-down) {
		&--bg {
			#{$s}__title {
				text-align: left;
			}
		}
	}
	@media (config.$md-up) {
		margin: 0 0 7.2rem;
		font-size: 1.4rem;
		&__title {
			margin: 0 0 1.2rem;
		}
		&__img {
			width: 5.3rem;
		}
		&__item {
			flex: 0 0 25%;
		}

		// MODIF
		&--bg {
			margin: 0;
			padding: 4rem;
			border-radius: variables.$border-radius-xl;
			#{$s}__img {
				width: 4.5rem;
			}
		}
	}
	@media (config.$lg-up) {
		&__link {
			flex-direction: row;
		}
		// &--bg {
		// 	#{$s}__item {
		// 		// flex: 0 0 25%;
		// 	}
		// }
	}
}
