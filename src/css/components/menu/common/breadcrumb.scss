@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.m-breadcrumb {
	--separator-color: #{variables.$color-icon-minor};
	$s: &;
	color: inherit;
	font-weight: bold;
	font-size: 1.2rem;
	&__link {
		--color-link: #{variables.$color-text};
		--color-link-decoration: var(--color-link);
		--color-hover: #{variables.$color-primary};
		--color-hover-decoration: var(--color-hover);
	}
	&__separator {
		position: relative;
		top: -0.1rem;
		width: 1.2rem;
		margin: 0 0.3rem;
		color: var(--separator-color);
	}

	// MODIF
	&--inverse {
		--separator-color: #{variables.$color-white};
		#{$s}__link {
			--color-link: #{variables.$color-white};
		}
	}
}
