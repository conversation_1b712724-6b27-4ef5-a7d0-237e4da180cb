@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.m-user {
	$s: &;
	font-weight: bold;
	font-size: 1.5rem;
	&__list {
		@extend %reset-ul;
	}
	&__item {
		@extend %reset-ul-li;
		display: flex;
	}
	&__link {
		--color-link: #{variables.$color-text};
		--color-hover: #{variables.$color-primary};
		position: relative;
		width: 100%;
		padding: 1rem 1.6rem;
		text-decoration: none;
		transition: color variables.$t, background-color variables.$t;
		&.item-icon {
			--icon-size: 2.5rem;
			--gap: 1.2rem;
			--icon-color: #{variables.$color-icon-minor};
			--icon-offset: -0.1em;
		}
		.item-icon__icon {
			transition: color variables.$t;
		}
		.icon-svg {
			width: 100%;
		}
	}
	&__icon-holder {
		position: relative;
	}
	&__notification {
		position: absolute;
		top: 0;
		left: 0;
		transform: translate(-50%, -50%);
	}

	// STATES
	&__link.is-active {
		--color-link: #{variables.$color-primary};
		--color-hover: #{variables.$color-primary-hover};
		--icon-color: var(--link-color);
		font-weight: bold;
	}

	// HOVERS
	.hoverevents &__link:hover {
		--icon-color: var(--link-color);
	}

	// MODIF
	&__item--logout {
		margin-top: 1.2rem;
		padding-top: 1.2rem;
		border-top: 0.1rem solid variables.$color-tile;
		font-weight: 400;
		.item-icon {
			--icon-size: 1.5rem;
			flex-direction: row-reverse;
			justify-content: space-between;
		}
	}
	&--side {
		font-weight: 400;
		#{$s}__link {
			padding: 1.5rem 2rem;
		}
	}
}
