@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.m-footer {
	&__title {
		margin: 0 0 1.2rem;
		font-family: variables.$font-primary;
	}
	&__list {
		@extend %reset-ul;
		display: flex;
		gap: 1.2rem;
		flex-direction: column;
	}
	&__item {
		@extend %reset-ul-li;
	}
	&__link {
		--color-link: var(--color-inverse-link);
		--color-hover: #{variables.$color-white};
		text-decoration: none;
	}

	// MQ
	@media (config.$md-up) {
		font-size: 1.5rem;
		&__title {
			margin: 0 0 2.8rem;
		}
		&__list {
			gap: 1.6rem;
		}
	}
	// font-size: 1.4rem;
	// &__btn.as-link {
	// 	display: flex;
	// 	justify-content: center;
	// 	align-items: center;
	// 	width: 3.8rem;
	// 	height: 3.8rem;
	// 	margin: 0 0 0 auto;
	// 	border-radius: variables.$border-radius-sm;
	// 	background: variables.$color-black;
	// 	color: inherit;
	// 	.icon-svg {
	// 		width: 1.5rem;
	// 		transition: transform variables.$t;
	// 	}
	// }
	// &__list {
	// 	margin: 0 0 0 1rem;
	// }
	// .footer &__link {
	// 	display: block;
	// 	padding: 1rem 1.5rem;
	// 	border-radius: variables.$border-radius-sm;
	// 	color: inherit;
	// 	color: variables.$color-white;
	// 	text-decoration: none;
	// 	transition: background-color variables.$t;
	// }

	// // HOVERS
	// .hoverevents .footer &__link:hover {
	// 	background: variables.$color-black;
	// 	color: variables.$color-white;
	// }

	// // MQ
	// @media (config.$md-down) {
	// 	&__title.footer__title {
	// 		display: flex;
	// 		align-items: center;
	// 		margin: 0;
	// 		padding: 0.4rem 0.4rem 0.4rem 1.5rem;
	// 		border-radius: variables.$border-radius-sm;
	// 		color: variables.$color-white;
	// 		text-align: left;
	// 		transition: background-color variables.$t;
	// 	}
	// 	&__list {
	// 		display: none;
	// 	}

	// 	// HOVERS
	// 	.is-open &__title,
	// 	.hoverevents &__title:hover {
	// 		background: variables.$color-black;
	// 		color: variables.$color-white;
	// 	}

	// 	// STATES
	// 	.is-open &__list {
	// 		display: block;
	// 	}
	// 	.is-open &__btn .icon-svg {
	// 		transform: scale(-1);
	// 	}
	// }
	// @media (config.$md-up) {
	// 	&__btn.as-link {
	// 		display: none;
	// 	}
	// 	&__list {
	// 		margin: 0 -1.5rem;
	// 	}
	// }
}
