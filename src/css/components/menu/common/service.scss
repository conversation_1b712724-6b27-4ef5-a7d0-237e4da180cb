@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.m-service {
	&__list {
		@extend %reset-ul;
	}
	&__item {
		@extend %reset-ul-li;
		display: flex;
	}
	&__link {
		--color-link: #{variables.$color-text};
		--color-hover: #{variables.$color-primary};
		--color-link-decoration: transparent;
		--color-hover-decoration: transparent;
	}

	// MQ
	@media (max-width: 899px) {
		font-size: 1.5rem;
		&__list {
			padding: 2rem 1.4rem 0;
		}
		&__link {
			padding: 0.7rem 1.6rem;
		}
	}
	@media (min-width: 900px) {
		font-size: 1.1rem;
		text-transform: uppercase;
		&__list {
			display: flex;
			gap: 0 0.5rem;
			flex-wrap: wrap;
			align-items: center;
		}
		&__link {
			padding: 0.5rem 0.8rem;
		}

		// MODIF
		.is-top > .header--inversed &__link {
			--color-link: #{variables.$color-white};
			--color-hover: #{variables.$color-white};
			--color-hover-decoration: var(--color-hover);
		}
	}
	@media (config.$xl-up) {
		&__link {
			padding: 0.5rem 1.2rem;
		}
	}
}
