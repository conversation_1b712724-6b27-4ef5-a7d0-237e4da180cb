@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.m-links {
	$s: &;
	font-size: 1.3rem;
	line-height: calc(15 / 13);
	&__list {
		--grid-x-spacing: 0.6rem;
		--grid-y-spacing: 0.6rem;
	}
	&__link {
		display: flex;
		gap: 0.6rem;
		align-items: center;
		padding: 0.4rem;
	}
	&__icon {
		display: flex;
		flex: 0 0 auto;
		justify-content: center;
		align-items: center;
		width: 3.5rem;
		height: 3.9rem;
		border-radius: variables.$border-radius-sm;
		background: #eff7f2;
		color: variables.$color-primary;
		img {
			width: 2.5rem;
		}
	}

	// MQ
	@media (config.$md-up) {
		font-size: 1.8rem;
		&__link {
			gap: 2rem;
			padding: 2rem;
		}

		// MODIF
		&--default {
			font-weight: bold;
			#{$s}__link {
				text-decoration: none;
			}
			#{$s}__icon {
				width: 7rem;
				height: 7rem;
				img {
					width: 4rem;
					height: 4rem;
				}
			}
		}
		&--hp {
			font-size: 1.5rem;
			line-height: calc(23 / 15);
			#{$s} {
				&__list {
					--grid-x-spacing: 1rem;
					--grid-y-spacing: 1rem;
				}
				&__link {
					gap: 1.6rem;
					padding: 0.7rem 0.8rem;
				}
				&__icon {
					width: 4.2rem;
					height: 4.2rem;
				}
			}
		}
	}
	@media (config.$xl-up) {
		&--default {
			#{$s} {
				&__list {
					--grid-x-spacing: 4rem;
					--grid-y-spacing: 4rem;
				}
			}
		}
	}
}
