@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.m-social-footer {
	font-size: 1.2rem;
	text-align: center;
	&__list {
		@extend %reset-ul;
		display: flex;
		gap: 1.6rem;
	}
	&__item {
		@extend %reset-ul-li;
		flex: 0 0 auto;
	}
	&__link {
		display: block;
		color: variables.$color-inverse-help;
		text-decoration: none;
	}
	&__img {
		display: block;
		margin: 0 0 0.4rem;
	}

	// HOVERS
	.hoverevents &__link:hover {
		color: variables.$color-white;
	}

	// MQ
	@media (config.$md-up) {
		font-size: 1.4rem;
		&__list {
			gap: 2rem;
		}
		&__img {
			margin: 0 0 0.8rem;
		}
	}
}
