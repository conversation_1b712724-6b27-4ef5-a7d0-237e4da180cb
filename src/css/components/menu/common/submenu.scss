@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.m-submenu {
	--box-p: 2rem 11rem 1.6rem 2rem;
	--box-br: #{variables.$border-radius-md};
	--box-fs: 1.3rem;
	--decor-w: 13.4rem;
	--decor-r: -3.5rem;
	$s: &;
	&__list {
		@extend %reset-ul;
	}
	&__item {
		@extend %reset-ul-li;
	}
	&__box {
		position: relative;
		z-index: 1;
		height: 100%;
		padding: var(--box-p);
		border: 0.1rem solid transparent;
		border-radius: var(--box-br);
		background: variables.$color-white;
		font-size: var(--box-fs);
		overflow: hidden;
		transition: border-color variables.$t;
	}
	&__title {
		margin: 0 0 0.4rem;
		font-size: 1.8rem;
		text-wrap: balance;
	}
	&__title &__link {
		--color-link: #{variables.$color-text};
		--color-hover: #{variables.$color-primary};
		text-decoration: none;
	}
	&__arrow {
		width: 1.2rem;
	}
	&__decor {
		position: absolute;
		top: 0;
		right: var(--decor-r);
		z-index: -1;
		width: var(--decor-w);
		height: 100%;
		max-height: 27rem;
		object-fit: contain;
		object-position: center right;
	}

	// MODIF
	&--standalone {
		#{$s}__box {
			border-color: variables.$color-tile;
		}
		#{$s}__list--1 {
			display: grid;
			grid-template-columns: repeat(1, minmax(0, 1fr));
			gap: 0.8rem;
		}
	}

	// MQ
	@media (max-width: 899px) {
		&__title img {
			width: auto;
			height: 2.4rem;
		}

		// MODIF
		&--menu {
			--box-p: 0;
			#{$s}__box {
				border: none;
			}
			#{$s}__title {
				display: flex;
				margin: 0 1.4rem;
				font-family: variables.$font-primary;
			}
			#{$s}__link {
				display: block;
				flex: 1;
				width: 100%;
				padding: 1rem 1.6rem;
				font-weight: 600;
				font-size: 1.6rem;
				text-decoration: none;
				&::after {
					content: '';
					position: absolute;
					right: 1rem;
					bottom: 0;
					left: 1rem;
					border-bottom: 0.1rem solid variables.$color-tile;
				}
			}
			#{$s}__decor,
			#{$s}__desc,
			#{$s}__more-wrap,
			#{$s}__arrow,
			#{$s}__all {
				display: none;
			}
			#{$s}__toggle {
				z-index: 1;
				padding: 1.6rem;
				font-size: 0;
			}

			// MODIF
			#{$s}__list--2 {
				padding: 0 1.4rem;
			}
			#{$s}__box[class*='u-bgc-'] {
				background: transparent;
			}
			#{$s}__holder {
				position: absolute;
				top: 0;
				left: 100%;
				display: none;
				width: 100%;
				max-height: calc(100dvh - 1rem);
				padding: var(--menu-padding-top) 0 1.6rem;
				overflow-x: hidden;
				overflow-y: auto;
			}
			#{$s}__item:last-child #{$s}__link::after {
				content: none;
			}
			#{$s}__link:has(+ #{$s}__toggle) {
				margin-right: -4.4rem;
			}

			// STATES
			#{$s}__item.is-submenu2-open #{$s}__holder {
				display: block;
			}
		}
	}
	@media (min-width: 636px) {
		&--standalone &__list--1 {
			grid-template-columns: repeat(2, minmax(0, 1fr));
		}
	}
	@media (min-width: 900px) {
		--box-p: 2.8rem 9.5rem 2.8rem 2.4rem;
		--box-br: #{variables.$border-radius-xl};
		--decor-r: -4.5rem;
		--decor-w: 15.5rem;
		&__more-wrap {
			margin: 1rem 0 0;
		}
		&__more {
			--color-link: #{variables.$color-help};
			--color-hover: var(--color-link);
			--color-link-decoration: transparent;
			--color-hover-decoration: var(--color-link);
			--icon-size: 1.2rem;
			--gap: 0.6rem;
		}
		&__desc {
			margin: 0;
		}
		&__all {
			--icon-size: 1.2rem;
			--gap: 0.8rem;
			--icon-offset: 0.1rem;
			--color-hover: var(--color-link);
			--color-link-decoration: transparent;
			--color-hover-decoration: var(--color-link);
			font-size: 1.5rem;
		}
		&__toggle {
			display: none;
		}

		// STATES
		&__item.is-hidden {
			display: none;
		}
		&.is-open {
			#{$s}__item.is-hidden {
				display: block;
			}
			#{$s}__more .icon-svg {
				transform: scale(-1);
			}
		}

		// MODIF
		&--standalone &__list--1 {
			grid-template-columns: repeat(2, minmax(0, 1fr));
			gap: 2rem;
		}
		&__list--1 {
			display: grid;
			grid-template-columns: repeat(3, minmax(0, 1fr));
			gap: 1.2rem;
		}
		&__list--2 {
			display: flex;
			gap: 0.6rem;
			flex-direction: column;
			padding-right: 9.5rem;
			#{$s}__link {
				--color-hover: var(--color-link);
				--color-link-decoration: transparent;
				--color-hover-decoration: var(--color-link);
			}
		}
		&--standalone,
		&__item:nth-child(-n + 3) {
			--box-p: 2rem 9.5rem 2rem 2.4rem;
			--box-fs: 1.4rem;
		}
		&__box--sm {
			--decor-r: 0;
			--decor-w: 10.8rem;
			padding-right: 11rem;
			#{$s}__title {
				padding-right: 0;
			}
		}
		&--courses &__item:nth-child(-n + 3) {
			--box-p: 4.5rem 9.5rem 4.5rem 2.4rem;
		}

		&--standalone,
		&--standalone &__item:nth-child(-n + 3) {
			--box-p: 5.2rem 12rem 5.2rem 5.2rem;
			--box-fs: 1.5rem;
			#{$s}__title {
				font-size: 2.2rem;
			}
		}

		// HOVERS
		.hoverevents &__box:hover {
			border-color: variables.$color-icon-minor;
		}
		.hoverevents &--standalone &__box:hover {
			border-color: variables.$color-gray-500;
		}
	}
	@media (config.$xxl-up) {
		&--standalone &__list--1 {
			grid-template-columns: repeat(3, minmax(0, 1fr));
		}
	}
	@media (config.$xxxl-up) {
		--box-p: 3.2rem 13.8rem 3.2rem 3.6rem;
		--box-fs: 1.4rem;
		&__title {
			margin: 0 0 0.8rem;
		}
		&__decor {
			max-height: 29rem;
		}

		// MODIF
		&__item:nth-child(-n + 3) {
			--box-p: 3.2rem 13.8rem 3.2rem 3.6rem;
		}
		&--courses &__item:nth-child(-n + 3) {
			--box-p: 5.5rem 13.8rem 5.5rem 4rem;
			--box-fs: 1.5rem;
			#{$s}__title {
				font-size: 2.2rem;
			}
		}
		&__box--sm {
			--decor-r: 0;
			--decor-w: 10.8rem;
			padding-right: 11rem;
		}
		&__list--2 {
			padding-right: 0;
		}
		&--standalone,
		&--standalone &__item:nth-child(-n + 3) {
			--box-p: 5.2rem 17rem 5.2rem 5.2rem;
		}
	}
}
