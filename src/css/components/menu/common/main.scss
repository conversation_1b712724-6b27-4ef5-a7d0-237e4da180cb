@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.m-main {
	$s: &;
	&__list {
		@extend %reset-ul;
	}
	&__item {
		@extend %reset-ul-li;
	}
	&__link {
		position: relative;
		outline: none;
		text-decoration: none;
	}

	// MQ
	@media (max-width: 899px) {
		--menu-padding-top: 5.5rem;
		position: absolute;
		top: 0.5rem;
		right: 0.5rem;
		width: 31rem;
		max-height: calc(100dvh - 1rem);
		padding: 0 0 1.6rem;
		border-radius: variables.$border-radius-md;
		background: variables.$color-white;
		overflow-x: hidden;
		overflow-y: auto;
		&__holder {
			transition: transform variables.$t;
		}
		&__list {
			padding-top: calc(1.6rem + var(--menu-padding-top));
		}
		&__item {
			display: flex;
			padding: 0 1.4rem;
		}
		&__link {
			--color-link: #{variables.$color-text};
			--color-hover: #{variables.$color-primary};
			flex: 1;
			width: 100%;
			padding: 1rem 1.6rem;
			font-weight: 600;
			font-size: 1.6rem;
			&::after {
				content: '';
				position: absolute;
				right: 1rem;
				bottom: 0;
				left: 1rem;
				border-bottom: 0.1rem solid variables.$color-tile;
			}
		}
		&__arrow {
			display: none;
		}
		&__toggle {
			z-index: 1;
			padding: 1rem 1.6rem;
			font-size: 0;
			.icon-svg {
				width: 1.2rem;
			}
		}
		&__btn {
			--btn-fs: 1.6rem;
			--btn-padding: 0.8rem 1.6rem 0.7rem;
			.btn__text {
				align-items: flex-start;
			}
		}
		&__icon {
			margin: 0 1.6rem 0 0;
			object-fit: contain;
		}

		// STATES
		&:has(.is-submenu-open) {
			overflow: hidden;
		}
		&__holder:has(.is-submenu-open) {
			transform: translateX(-100%);
		}
		&__holder:has(.is-submenu2-open) {
			transform: translateX(-200%);
		}

		// MODIF
		&__link:has(~ #{$s}__toggle) {
			margin-right: -4.4rem;
		}
		&__link:has(~ #{$s}__icon) {
			margin-right: -3.6rem;
		}
		&__item:has(+ #{$s}__item--btn) &__link::after,
		&__item--btn &__link::after {
			content: none;
		}
		&__item--btn {
			margin: 0.8rem 0 0;
		}

		// MQ
		@media (min-width: 636px) {
			--menu-padding-top: 6.2rem;
		}
	}
	@media (min-width: 900px) {
		display: contents;
		&__holder,
		&__list {
			display: contents;
		}
		&__item {
			align-self: stretch;
			max-width: calc(12.5rem + (max(1.2rem, 1.3vw) * 2));
		}
		&__link {
			--color-link: #{variables.$color-text};
			--color-hover: #{variables.$color-primary};
			--arrow-size: 1.5rem;
			--arrow-gap: 0.8rem;
			display: flex;
			align-items: center;
			height: 100%;
			padding: 0 max(1.2rem, 1.3vw);
			border-radius: variables.$border-radius-lg;
			font-weight: 600;
			font-size: 1.4rem;
			transition: color variables.$t, background-color variables.$t;
			&[class*='u-c'] {
				color: var(--color-link);
			}
			&::before {
				content: '';
				position: absolute;
				top: calc(100% + 1.2rem);
				left: 50%;
				width: 0;
				height: 0;
				border-width: 0 1rem 0.9rem;
				border-style: solid;
				border-color: transparent transparent variables.$color-bg;
				visibility: hidden;
				opacity: 0;
				transition: opacity variables.$t, visibility variables.$t;
			}
		}
		&__arrow {
			position: absolute;
			top: 50%;
			right: calc(max(1.2rem, 1.3vw));
			width: 1.5rem;
			transform: translateY(-50%);
			transition: transform variables.$t;
		}
		&__toggle,
		&__icon {
			display: none;
		}
		&__btn {
			--btn-h: 6.1rem;
			--btn-fs: 1.5rem;
			--btn-icon-size: 1.5rem;
			--btn-br: #{variables.$border-radius-lg};
			--btn-padding: 1.2rem;
		}

		// MODIF
		&__item--btn {
			flex: 0 1 12.4vw;
			min-width: 11.4rem;
			max-width: 23.8rem;
			margin-left: auto;
		}
		&__link:has(&__arrow) {
			padding-right: calc(max(1.2rem, 1.3vw) + var(--arrow-size) + var(--arrow-gap));
		}

		// STATES
		&__item.is-open,
		.hoverevents &__item.overlay-pseudo.is-hover,
		.no-js.hoverevents &__item.overlay-pseudo:hover {
			#{$s}__link {
				background-color: #f8f9ff;
				color: var(--color-hover);
			}
			#{$s}__link::before {
				visibility: visible;
				opacity: 1;
			}
			#{$s}__arrow {
				transform: translateY(-50%) scale(-1);
			}
		}
		.hoverevents &__link:hover {
			background-color: #f8f9ff;
		}
	}
	@media (config.$xl-up) {
		&__item {
			max-width: none;
		}
		&__item--btn {
			max-width: 23.8rem;
		}
	}
	@media (config.$xxxl-up) {
		&__link {
			padding: 0 clamp(1.2rem, 1.8vw, 3.6rem);
			font-size: 1.5rem;
		}
	}
}
