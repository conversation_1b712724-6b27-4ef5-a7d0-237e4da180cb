@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-services {
	$s: &;
	margin: 0 0 4rem;
	&__list {
		@extend %reset-ul;
		display: grid;
		grid-template-columns: repeat(2, minmax(0, 1fr));
		gap: 0.8rem;
	}
	&__item {
		@extend %reset-ul-li;
		position: relative;
		display: flex;
		width: 100%;
		height: 100%;
		min-height: 20rem;
		border-radius: variables.$border-radius-lg;
		background: variables.$color-bg;
		font-size: 1.3rem;
		overflow: hidden;
	}
	&__img {
		position: absolute;
		top: 0;
		right: 0;
		width: 70%;
		height: auto;
		max-height: 50%;
		object-fit: contain;
		object-position: top right;
	}
	&__content {
		position: relative;
		display: flex;
		flex-direction: column;
		width: 100%;
		min-height: 11rem;
		margin-top: auto;
		padding: 2.4rem 1.6rem;
		&:first-child {
			margin-top: auto;
		}
	}
	&__name {
		margin: 0 0 0.6rem;
	}
	&__link {
		--color-link-decoration: transparent;
		--color-hover-decoration: var(--color-hover);
	}

	// MODIF
	&__item--highlighted {
		grid-row: auto / span 2;
		grid-column: auto / span 2;
		min-height: 34rem;
		color: variables.$color-white;
		aspect-ratio: 375/500;
		#{$s}__name {
			color: inherit;
		}
		#{$s}__content {
			justify-content: flex-end;
			height: 100%;
			background: linear-gradient(180deg, rgba(62, 94, 252, 0) 42.37%, #0a1e83 76.8%);
		}
		#{$s}__img {
			width: 100%;
			max-width: none;
			height: 100%;
			max-height: none;
			object-position: center;
			inset: 0;
			object-fit: cover;
		}
	}

	// MQ
	@media (config.$xs-up) {
		&__item--highlighted {
			grid-column: auto / span 1;
		}
	}
	@media (config.$md-up) {
		margin: 0 0 6.8rem;
		&__title {
			text-align: center;
		}
		&__list {
			grid-template-columns: repeat(4, minmax(0, 1fr));
			grid-auto-flow: row dense;
		}
		&__item--highlighted {
			grid-column: auto / span 2;
		}
	}
	@media (config.$lg-up) {
		&__list {
			gap: 2rem;
		}
		&__item {
			min-height: 23.4rem;
			border-radius: variables.$border-radius-xl;
			font-size: 1.5rem;
		}
		&__content {
			min-height: 16rem;
			padding: 3.6rem 3rem;
		}
		&__img {
			width: 65%;
		}
		&__btn {
			--btn-fs: 1.5rem;
			--btn-h: 5.5rem;
			--btn-padding: 1.4rem 2.8rem 1.2rem;
			--btn-br: #{variables.$border-radius-lg};
		}

		// MODIF
		&:has(&__item--highlighted) &__list {
			grid-template-columns: repeat(2, minmax(0, 375fr)) repeat(2, minmax(0, 285fr));
		}
		&__item--highlighted {
			grid-column: auto / span 1;
			font-size: 1.6rem;
			#{$s}__name {
				--font-size-desktop: 2.8rem;
			}
		}
	}
	@media (config.$xl-up) {
		&__list {
			grid-template-columns: repeat(4, minmax(0, 1fr));
		}
	}
	@media (config.$xxxl-up) {
		&__item--highlighted {
			#{$s}__content {
				padding: 4.4rem 4rem;
			}
		}
	}
}
