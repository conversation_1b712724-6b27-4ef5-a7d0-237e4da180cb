@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-submenu-side {
	display: flex;
	gap: 6rem;
	justify-content: center;
	align-items: flex-start;
	font-size: 1.4rem;
	&__title {
		margin: 0 0 1.2rem;
	}
	&__list {
		@extend %reset-ul;
		display: flex;
		gap: 0.6rem;
		flex-direction: column;
		li {
			@extend %reset-ul-li;
		}
	}
	&__link {
		text-decoration: none;
	}
	&__divider {
		align-self: stretch;
		width: 0.1rem;
		background: rgba(variables.$color-gray-900, 0.15);
	}
	&__box {
		--color-link: #{variables.$color-text};
		--color-hover: #{variables.$color-text};
		position: relative;
		display: block;
		flex: 0;
		min-width: 21.8rem;
		padding: 4.6rem 2rem 2rem;
		border-radius: 1.6rem;
		background: linear-gradient(96.35deg, #621298 0%, #7d25c2 10.5%, #e83d5c 45%, #fe995f 100%);
		font-size: 1.4rem;
		text-decoration: none;
		&::before {
			content: '';
			position: absolute;
			border-radius: 1.4rem;
			background: #e2e5f1;
			inset: 0.2rem;
		}
		& > * {
			position: relative;
			z-index: 1;
		}
	}
	&__decor {
		position: absolute;
		top: -3.5rem;
		right: -1rem;
		width: 11.5rem;
	}
	&__zone {
		margin: 0 0 0.8rem;
	}
	&__text {
		padding-right: 4rem;
	}
	&__btn {
		--btn-br: 50%;
		--btn-h: 3.1rem;
		--btn-icon-size: 1.5rem;
		position: absolute;
		right: 0;
		bottom: 0;
		.btn__text {
			box-shadow: none;
		}
	}

	// MQ
	@media (config.$xl-up) {
		flex-direction: column;
		font-size: 1.5rem;
		&__title {
			font-size: 2.2rem;
		}
		&__divider {
			width: auto;
			height: 0.1rem;
		}
	}
	@media (config.$xxxl-up) {
		&__box {
			padding: 2.4rem 2.8rem;
		}
		&__decor {
			top: -4.5rem;
			right: -5.5rem;
			width: 15.4rem;
		}
	}
}
