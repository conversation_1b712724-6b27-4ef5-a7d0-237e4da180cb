@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-features {
	&__title {
		margin: 0 0 1.2rem;
	}
	&__list {
		@extend %reset-ul;
		display: grid;
		grid-template-columns: repeat(2, minmax(0, 1fr));
		gap: 0.4rem;
		font-weight: bold;
		font-size: 1.4rem;
	}
	&__item {
		--icon-color: #{variables.$color-primary};
		@extend %reset-ul-li;
		padding: 0.8rem;
		border-radius: variables.$border-radius-lg;
		background: variables.$color-bg;
	}

	// MQ
	@media (config.$md-down) {
		&__item {
			--gap: 0.4rem;
			flex-direction: column;
			align-items: flex-start;
		}
	}
	@media (config.$xs-up) {
		&__list {
			grid-template-columns: repeat(3, minmax(0, 1fr));
		}
	}
	@media (config.$md-up) {
		&__list {
			display: flex;
			flex-wrap: wrap;
		}
		&__item {
			padding: 1.2rem 1.4rem;
		}
	}
	@media (config.$lg-up) {
		display: flex;
		align-items: center;
		&__title {
			flex: 0 0 auto;
			margin: 0;
			padding: 0 2.4rem;
		}
	}
}
