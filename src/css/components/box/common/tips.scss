@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-tips {
	padding: 1.5rem;
	&__top {
		display: flex;
		gap: 0.5rem;
		align-items: flex-start;
		ul {
			margin: 0;
		}
	}
	&__img {
		flex: 0 0 auto;
		width: 3.2rem;
	}
	&__content {
		flex: 1;
		font-size: 1.6rem;
	}
	hr {
		margin: 2.5rem 0;
	}
	&__btns {
		display: flex;
		gap: 0.8rem;
		flex-wrap: wrap;
		.btn {
			--btn-padding: 0.5rem 1.5rem;
			--btn-color: #{variables.$color-primary};
			--btn-bd: #{variables.$color-bg};
		}
		.btn__text {
			line-height: 1.25;
			text-align: left;
		}
	}

	// MQ
	@media (config.$sm-down) {
		&__btns {
			margin-left: -3.7rem;
			.btn {
				width: 100%;
			}
		}
	}
	@media (config.$md-down) {
		li {
			margin: 0;
		}
	}
	@media (config.$md-up) {
		padding: 3rem;
		&__img {
			width: 4.2rem;
		}
		&__top {
			gap: 1.6rem;
		}
	}
}
