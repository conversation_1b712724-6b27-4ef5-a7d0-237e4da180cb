@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-eu {
	--row-main-width: calc(178rem + 2 * var(--row-main-gutter));
	margin: 6rem 0 3rem;
	color: variables.$color-help;
	font-size: 1.1rem;
	&__inner {
		display: flex;
		gap: 1.2rem 4rem;
		flex-direction: column;
		align-items: center;
		text-align: center;
	}
	&__img {
		flex: 0 0 auto;
		width: 17.3rem;
	}

	// MQ
	@media (config.$lg-up) {
		margin: 10rem 0 4rem;
		font-size: 1.4rem;
		&__inner {
			flex-direction: row;
			text-align: left;
		}
	}
}
