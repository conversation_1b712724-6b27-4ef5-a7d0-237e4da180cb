@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-barcode {
	position: fixed;
	top: 50dvh;
	left: 50%;
	z-index: 100;
	display: none;
	border-radius: variables.$border-radius-sm;
	outline: variables.$outline;
	overflow: hidden;
	transform: translate(-50%, -50%);
	pointer-events: none;
	video {
		background: variables.$color-black;
		object-fit: cover;
	}
	video,
	canvas {
		grid-area: 1/2/1/2;
		width: min(80rem, calc(var(--vw) - 2 * var(--row-main-gutter)));
		max-height: 80dvh;
		pointer-events: auto;
	}
	&__btn.as-link {
		position: relative;
		z-index: 1;
		display: flex;
		grid-area: 1/2/1/2;
		align-self: flex-start;
		margin-left: auto;
		padding: 0.8rem;
		color: variables.$color-white;
		pointer-events: auto;
		.icon-svg {
			width: 2rem;
			height: 2rem;
		}
	}

	// STATES
	&.is-open {
		display: grid;
		grid-template-columns: 1fr auto 1fr;
		justify-content: center;
		align-items: flex-start;
	}

	// HOVERS
	.hoverevents &__btn.as-link:hover {
		color: variables.$color-green-gray;
	}

	// MQ
	@media (config.$md-down) {
		video,
		canvas {
			height: calc(100dvh - 2 * var(--row-main-gutter));
			max-height: none;
		}
	}
}
