@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-zigzag {
	--img-offset: 2rem;
	--img-gap: 0.8rem;
	$s: &;
	position: relative;
	border-radius: variables.$border-radius-xl;
	overflow: hidden;
	&__list {
		display: flex;
		gap: 4.8rem;
		flex-direction: column;
	}
	&__grid {
		--grid-y-spacing: 1.6rem;
	}
	&__content {
		display: flex;
		gap: 0.8rem;
		flex-direction: column;
		margin: 0 0 1.2rem;
		& > *,
		&.b-content:not(:first-child) > *:is(h1, h2, h3, h4, h5, h6) {
			margin: 0;
		}
		.btn {
			--btn-h: 4.4rem;
			--btn-padding: 0.9rem 2.2rem 0.7rem;
		}
		p:has(.btn) {
			margin-top: 0.4rem;
		}
		ul {
			@include mixins.list-check;
		}
	}
	&__imgs {
		display: flex;
		gap: var(--img-gap);
		max-width: 24.2rem;
		margin: 0;
		padding-top: 2rem;
		aspect-ratio: 242 / 248;
	}
	&__img-col {
		display: flex;
		gap: var(--img-gap);
		flex-direction: column;
		&:nth-child(1) {
			padding-bottom: var(--img-offset);
		}
		&:nth-child(2) {
			padding-top: var(--img-offset);
		}
	}
	&__img {
		flex: 1;
		border-radius: variables.$border-radius-md;
	}
	&__flag {
		--flag-padding: 0.6rem 1.2rem;
		--flag-h: 3.4rem;
		--flag-fs: 1.4rem;
	}
	&__sublist {
		@extend %reset-ul;
		display: flex;
		gap: 0.4rem;
		flex-direction: column;
		font-weight: bold;
		li {
			@extend %reset-ul-li;
		}
	}
	&__link {
		--color-link-decoration: transparent;
		--color-hover: var(--color-link);
		display: flex;
		gap: 1rem;
		justify-content: space-between;
		align-items: center;
		padding: 1.6rem 2rem;
		border-radius: variables.$border-radius-md;
		background: variables.$color-bg;
	}

	// MODIF
	&__imgs--3 &__img:nth-child(1),
	&__imgs--3 &__img:nth-child(2),
	&__imgs--4 &__img {
		aspect-ratio: 5/4;
	}
	&__imgs--1 {
		padding-top: 0;
		aspect-ratio: 9/16;
	}
	@at-root ul#{&}__list {
		@extend %reset-ul;
	}
	@at-root ul#{&}__list &__item {
		@extend %reset-ul-li;
	}
	@at-root ol#{&}__list {
		@extend %reset-ol;
		counter-reset: item;
	}
	@at-root ol#{&}__list &__item {
		@extend %reset-ol-li;
	}
	@at-root ol#{&}__list &__grid {
		--grid-y-spacing: 3.4rem;
	}
	@at-root ol#{&}__list &__cell--content::before {
		content: counter(item);
		counter-increment: item;
		display: flex;
		justify-content: center;
		align-items: center;
		border-radius: 50%;
		background: variables.$color-primary-150;
		color: variables.$color-primary;
		font-family: variables.$font-secondary;
		font-weight: bold;
		aspect-ratio: 1/1;
	}

	// MQ
	@media (config.$md-down) {
		&__bg.b-popular__content-inner {
			padding: 2.8rem 2rem;
		}
		&__bg &__grid {
			flex-direction: column-reverse;
		}
		@at-root ol#{&}__list &__cell--content::before {
			position: absolute;
			top: calc(var(--grid-y-spacing) * -1);
			left: 0;
			width: 4rem;
			font-size: 1.8rem;
			transform: translateY(-50%);
		}
	}
	@media (config.$md-up) {
		--img-gap: 2rem;
		--img-offset: 6rem;
		&__grid {
			flex-wrap: nowrap;
		}
		&__cell {
			&--content {
				flex: 1;
				padding: 0 0 0 6rem;
			}
			&--img {
				width: min(50%, calc(49rem + var(--grid-x-spacing)));
			}
		}
		&__list {
			gap: 10rem;
		}
		&__content {
			gap: 1.2rem;
			margin: 0 0 2.4rem;
			.btn {
				--btn-fs: 1.5rem;
				--btn-h: 5.5rem;
				--btn-padding: 1.4rem 2.8rem 1.2rem;
				--btn-br: #{variables.$border-radius-lg};
			}
			p:has(.btn) {
				margin-top: 1.2rem;
			}
		}
		&__imgs {
			max-width: none;
			padding: 0;
			aspect-ratio: 490 / 478;
		}
		&__img {
			border-radius: variables.$border-radius-xl;
		}

		// MODIF
		&__item:nth-child(even) &__grid,
		&--reversed &__item:nth-child(odd) &__grid {
			flex-direction: row-reverse;
			#{$s}__cell--content {
				padding: 0 6rem 0 0;
			}
		}
		&--reversed &__item:nth-child(even) &__grid {
			flex-direction: row;
			#{$s}__cell--content {
				padding: 0 0 0 6rem;
			}
		}
		&__imgs--1 {
			aspect-ratio: 9/16;
		}
		&__item:has(&__imgs--0) &__cell--img,
		&__item:has(&__imgs--1) &__cell--img {
			width: min(50%, calc(24rem + var(--grid-x-spacing)));
		}
		@at-root ol#{&}__list &__cell--content::before {
			width: 5.4rem;
			margin: 0 0 2.4rem;
			font-size: 2.2rem;
		}
	}
}
