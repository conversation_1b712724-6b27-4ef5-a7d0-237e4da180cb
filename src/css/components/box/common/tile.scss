@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/typography';

.b-tile {
	display: flex;
	font-size: 1.3rem;
	&__img {
		flex: 0 0 100%;
		margin: 0;
		&::before {
			padding-top: percentage(calc(250 / 220));
		}
		&::after {
			content: '';
			position: absolute;
			top: 46%;
			right: 0;
			bottom: 0;
			left: 0;
			background: linear-gradient(to bottom, transparent, variables.$color-black);
			opacity: 0.7;
			transition: top variables.$t;
		}
	}
	&__content {
		position: relative;
		z-index: 1;
		display: flex;
		flex: 0 0 100%;
		flex-direction: column;
		justify-content: flex-end;
		margin-left: -100%;
		padding: 1.5rem;
		color: variables.$color-white;
		line-height: 1;
	}
	&__title {
		@extend %h6;
		margin: 0;
	}
	&__link {
		color: inherit;
		text-decoration: none;
	}
	&__arrow {
		display: none;
	}

	// HOVERS
	.hoverevents &--w-link:hover &__img::after {
		top: 0;
	}
	.hoverevents &__link:hover {
		color: inherit;
	}

	// MQ
	@media (config.$md-down) {
		&__title {
			font-size: 1.4rem;
			line-height: 1.4;
		}
	}
	@media (config.$md-up) {
		font-size: 1.4rem;
		&__content {
			padding: 2rem;
		}
		&__arrow {
			position: absolute;
			right: 2rem;
			bottom: 2rem;
			display: block;
			width: 1.4rem;
		}

		// MODIF
		&--w-link &__content {
			padding-right: 4rem;
		}
	}
}
