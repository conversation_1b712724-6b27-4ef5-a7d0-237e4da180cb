@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-locked {
	&__content {
		ul {
			@include mixins.list-check;
		}
	}
	&__btn--copy {
		.btn__inner {
			position: relative;
		}
		.btn__icon {
			transition: opacity variables.$t;
		}
		.btn__icon:nth-child(2) {
			position: absolute;
			top: 50%;
			left: 0;
			opacity: 0;
			transform: translateY(-50%);
		}
	}

	// STATES
	&__btn--copy.is-copied {
		.btn__icon:nth-child(1) {
			opacity: 0;
		}
		.btn__icon:nth-child(2) {
			opacity: 1;
		}
	}

	// MQ
	@media (config.$md-down) {
		&__btn {
			--btn-fs: 1.5rem;
			--btn-h: 5.5rem;
			--btn-padding: 1.4rem 2.8rem 1.2rem;
			--btn-br: #{variables.$border-radius-lg};
		}
	}
}
