@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-coverage {
	font-size: 1.4rem;
	.grid {
		--grid-y-spacing: 1.2rem;
	}
	&__items {
		margin: 0;
	}
	&__item {
		display: block;
		padding: 1.2rem 1.6rem;
		border-radius: variables.$border-radius-lg;
		background: variables.$color-status-valid-light;
		.icon-svg {
			color: variables.$color-status-valid;
		}
	}

	// MQ
	@media (config.$sm-down) {
		&__items {
			display: flex;
			gap: 0.4rem;
			flex-direction: column;
		}
		&__col {
			display: contents;
		}
		&__item {
			display: flex;
			gap: 0.4rem;
			align-items: flex-start;
			.icon-svg {
				position: relative;
				top: 0.05em;
				flex: 0 0 auto;
				width: 2rem;
			}
		}
	}
	@media (config.$sm-up) {
		--offset: 6rem;
		padding-bottom: var(--offset);
		&__items {
			display: grid;
			grid-template-columns: 1fr 1fr;
			gap: 2rem;
			align-items: flex-start;
			text-align: center;
		}
		&__col {
			display: grid;
			grid-template-rows: 1fr auto;
			gap: 2rem;
			&:nth-child(even) {
				position: relative;
				top: var(--offset);
			}
		}
		&__item {
			padding: 3.2rem;
			border-radius: variables.$border-radius-xl;
			.icon-svg {
				display: block;
				width: 3rem;
				margin: 0 auto 0.8rem;
			}
		}
	}
	@media (config.$md-up) {
		font-size: 1.7rem;
	}
	@media (config.$lg-up) {
		.grid {
			flex-direction: row-reverse;
		}
		&__title {
			padding-top: calc(var(--offset) / 2);
		}
	}
	@media (config.$xl-up) {
		.grid {
			--grid-x-spacing: 10rem;
		}
	}
}
