@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-smallbasket {
	display: flex;
	flex-direction: column;
	width: 100%;
	font-size: 1.3rem;
	&__main {
		display: flex;
		flex: 1 1 auto;
		flex-direction: column;
	}
	&__list {
		@extend %reset-ul;
		display: grid;
		grid-template-columns: 8.6rem minmax(10.5rem, auto) 1fr 4.5rem;
	}
	&__item {
		@extend %reset-ul-li;
		display: grid;
		grid-template-columns: subgrid;
		grid-template-rows: 1fr auto;
		grid-column: auto / span 4;
		gap: 0 1.2rem;
		align-items: center;
		padding: 1.2rem 0.8rem;
		border-bottom: 0.1rem solid variables.$color-tile;
		&:last-child {
			border: none;
		}
	}
	&__item:has(&__extras) {
		grid-template-rows: 1fr auto auto;
	}
	&__img {
		grid-area: 1/1/3/1;
	}
	&__name {
		grid-area: 1/2/1/4;
	}
	&__link {
		--color-link-decoration: transparent;
		--color-hover-decoration: var(--color-link);
		font-weight: bold;
		font-size: 1.4rem;
	}
	&__info {
		display: block;
		margin-top: -0.3rem;
	}
	&__price {
		grid-area: 2/2/2/2;
	}
	&__amount {
		grid-area: 2/3/2/4;
	}
	&__more {
		grid-area: 1/4/3/4;
	}
	&__remove {
		--color-link: #{variables.$color-text};
		--color-hover: #{variables.$color-primary};
		display: flex;
		padding: 0.8rem;
		border-radius: 50%;
		transition: background-color variables.$t;
		.icon-svg {
			width: 1.5rem;
			transition: opacity variables.$t;
		}
	}
	&__empty {
		flex: 1;
		align-content: center;
		padding: 7rem 4rem;
		color: variables.$color-help;
		outline: none;
		font-size: 1.5rem;
		text-align: center;
	}
	&__bottom {
		position: sticky;
		bottom: 0;
		.b-delivery {
			border-radius: 0;
		}
	}
	&__extras {
		grid-area: 3/1/3/5;
	}

	// STATES
	.hoverevents &__remove:hover {
		background: variables.$color-white-hover;
		color: variables.$color-text;
	}

	// MQ
	@media (config.$lg-down) {
		&__bnr {
			display: none;
		}
	}
}
