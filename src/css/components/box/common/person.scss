@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-person {
	$s: &;
	display: flex;
	gap: 0.8rem;
	flex-direction: column;
	margin: 0;
	&__img {
		border-radius: variables.$border-radius-lg;
		background: variables.$color-bg;
	}

	// MQ
	@media (config.$md-down) {
		&--horizontal {
			gap: 1.6rem;
			flex-direction: row;
			align-items: center;
			#{$s}__img {
				flex: 0 0 auto;
				width: 12rem;
			}
		}
	}
	@media (config.$md-up) {
		font-size: 1.5rem;
	}
	@media (config.$lg-up) {
		gap: 1.2rem;
	}
}
