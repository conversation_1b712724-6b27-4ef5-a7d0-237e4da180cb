/* stylelint-disable selector-id-pattern */
@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.b-basket {
	$s: &;
	font-size: 1.1rem;
	&__link {
		--color-link: #{variables.$color-text};
		--color-hover: #{variables.$color-primary};
		position: relative;
		display: flex;
		gap: 1.2rem;
		align-items: center;
		width: var(--link-w);
		height: var(--link-h);
		line-height: 1.3;
		text-decoration: none;
	}
	&__icon-holder {
		position: relative;
		margin: 0 auto;
	}
	&__icon {
		width: var(--link-item-icon-size);
	}
	&__count {
		position: absolute;
		top: 50%;
		left: 50%;
		align-content: center;
		width: 1.7rem;
		border-radius: 50%;
		background: linear-gradient(86.5deg, #75de98 0%, #15c284 100%);
		color: variables.$color-text;
		font-weight: bold;
		font-size: 0.9rem;
		text-align: center;
		transform: translate(-50%, -50%) translateX(-0.9rem) translateY(-1rem);
		aspect-ratio: 1/1;
	}
	&__total {
		font-family: variables.$font-secondary;
		font-weight: 600;
		font-size: 1.5rem;
	}
	&__box {
		position: absolute;
		display: flex;
		flex-direction: column;
		height: 100dvh;
		background: variables.$color-white;
		overflow: clip;
		visibility: hidden;
		opacity: 0;
		transform: translateX(-5000px);
		transition: transform 0s variables.$t, opacity variables.$t, visibility variables.$t;
		inset: 0;
	}
	&__top {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 1rem 0.8rem 1rem 1.6rem;
		border-bottom: 0.1rem solid variables.$color-tile;
	}
	&__btn--inner {
		margin-right: auto;
	}
	&__toggle {
		margin-left: 0.6rem;
		padding-left: 0.6rem;
		border-left: 0.1rem solid variables.$color-tile;
		.btn {
			--btn-bg: #{variables.$color-primary-150};
			--btn-c: #{variables.$color-text};
		}
	}
	&__main {
		display: flex;
		flex: 1 1 auto;
		min-height: 10rem;
		overflow-x: hidden;
		overflow-y: auto;
		#snippet-siteHeader-cart-headerCartContent {
			display: flex;
			width: 100%;
		}
		#snippet-siteHeader-cart-headerCartContent .block-loader {
			width: 100%;
		}
	}

	// HOVERS
	&:has(.tooltip__btn[aria-expanded='true']),
	*:is(.is-top, .is-pinned) &.is-open,
	.hoverevents *:is(.is-top, .is-pinned) &.is-hover,
	.no-js.hoverevents &:hover {
		z-index: 2;
		#{$s}__box {
			visibility: visible;
			opacity: 1;
			transform: translateX(0);
			transition: transform 0s 0s, opacity variables.$t, visibility variables.$t;
		}
	}

	// MQ
	@media (config.$xl-down) {
		&__btn--main &__content {
			display: none;
		}
	}
	@media (config.$sm-up) {
		position: relative;
		&__box {
			top: 0;
			right: 0;
			bottom: auto;
			left: auto;
			width: 37.8rem;
			height: auto;
			max-height: calc(100dvh - 2.4rem);
			border-radius: variables.$border-radius-xl;
		}
		&__toggle {
			display: none;
		}
	}
	@media (min-width: 900px) {
		&__count {
			width: 2.2rem;
			font-size: 1.1rem;
			transform: translate(-50%, -50%) translateX(-1.4rem) translateY(-0.8rem);
		}

		.is-top > .header--inversed &:not(.is-hover):not(.is-open) &__btn--main &__link {
			--color-link: #{variables.$color-white};
			--color-hover: #{variables.$color-icon-minor};
		}
	}
	@media (config.$xl-up) {
		&__link {
			width: auto;
			padding: 0 1.6rem;
		}
	}
}
