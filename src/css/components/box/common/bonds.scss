@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-bonds {
	&__inner {
		position: relative;
	}
	.grid {
		--grid-x-spacing: 4.8rem;
		--grid-y-spacing: 2.8rem;
	}
	&__box {
		padding: 2.8rem 2.8rem 5.2rem;
		border-radius: variables.$border-radius-xl variables.$border-radius-xl 0 0;
		background: rgba(#efeef5, 0.5);
		backdrop-filter: blur(10rem);
		& + & {
			padding: 2rem 2.8rem 2.4rem;
			border-radius: 0 0 variables.$border-radius-xl variables.$border-radius-xl;
			background: variables.$color-primary-150;
		}
	}
	&__title {
		margin: 0 0 1.6rem;
	}
	&__inp-wrap {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin: 0 0 1.6rem;
		font-size: 1.5rem;
		.inp-fix {
			flex: 0 1 14.8rem;
		}
		.inp-text {
			text-align: right;
		}
	}
	&__dl {
		display: flex;
		gap: 0.4rem 1.2rem;
		flex-wrap: wrap;
		justify-content: space-between;
		dt {
			flex: 0 0 50%;
			font-weight: 400;
			font-size: 1.3rem;
		}
		dd {
			margin: 0;
			font-weight: bold;
			font-size: 1.4rem;
		}
	}
	&__range-wrap {
		position: relative;
		margin: 0;
		.progress__point {
			top: 2.2rem;
			z-index: -1;
		}
	}
	&__range {
		width: 100%;
		background: transparent;
		touch-action: none;
		&,
		&::-webkit-slider-runnable-track,
		&::-webkit-slider-thumb {
			appearance: none;
		}
		&::-moz-range-track {
			height: 0.8rem;
			border-radius: 0.8rem;
			background: variables.$color-tile;
		}
		&::-webkit-slider-thumb {
			position: relative;
			top: -1.1rem;
			width: 3rem;
			height: 3rem;
			border: 0.7rem solid variables.$color-primary;
			border-radius: 50%;
			background: white;
		}
		&::-webkit-slider-runnable-track {
			height: 0.8rem;
			border-radius: 0.8rem;
			background: linear-gradient(to right, variables.$color-primary var(--progress), variables.$color-tile var(--progress));
		}
	}

	// MQ
	@media (config.$lg-down) {
		&__decor {
			display: none;
		}
		&__btn {
			text-align: center;
			.btn {
				--btn-fs: 1.5rem;
				--btn-h: 5.5rem;
				--btn-padding: 1.4rem 2.8rem 1.2rem;
				--btn-br: #{variables.$border-radius-lg};
			}
		}
	}
	@media (config.$lg-up) {
		&__decor {
			position: absolute;
			top: 4rem;
			right: 0;
			transform: translateX(60%);
			aspect-ratio: 460/343;
			object-fit: cover;
		}
		&__box {
			padding: 4rem 6rem 5.6rem;
			& + & {
				padding: 3.2rem 6rem 4rem;
			}
		}
		&__title {
			margin: 0 0 2.4rem;
		}
		&__inp-wrap {
			margin: 0 0 1.2rem;
			font-size: 1.8rem;
			.inp-fix {
				--inp-h: 6.3rem;
				--inp-fs: 1.8rem;
				--inp-padding-x: 1.6rem;
				--inp-padding-y: 0.6rem;
				flex: 0 1 18rem;
			}
		}
		&__range-wrap {
			.progress__point {
				top: 1.8rem;
			}
		}
		&__dl {
			display: grid;
			grid-template-columns: repeat(3, 1fr);
			grid-template-rows: repeat(2, 1fr);
			grid-auto-flow: column;
			dt {
				font-size: 1.5rem;
			}
			dd {
				font-size: 1.8rem;
			}
		}
	}
}
