@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-header {
	$s: &;
	display: grid;
	gap: 1.2rem;
	&__bg {
		--bg-width: min(var(--vw), 192rem);
		position: relative;
		left: 50%;
		grid-area: 1/1/1/1;
		width: var(--bg-width);
		max-width: 192rem;
		margin-left: calc(var(--bg-width) / -2);
		img {
			width: 100%;
			min-height: 20rem;
			max-height: 42rem;
			object-fit: cover;
			aspect-ratio: 2/1;
		}
		&::before {
			content: '';
			position: absolute;
			background: linear-gradient(360deg, rgba(13, 18, 40, 0.4) 0%, rgba(13, 18, 40, 0.2) 42.5%, rgba(13, 18, 40, 0) 100%),
				radial-gradient(
					38.74% 50% at 0% 100%,
					rgba(255, 123, 115, 0.6) 0%,
					rgba(249, 100, 134, 0.4) 28%,
					rgba(255, 123, 115, 0) 100%
				),
				linear-gradient(0deg, rgba(0, 0, 0, 0) 68.73%, rgba(0, 0, 0, 0.6) 100%);
			inset: 0;
		}
	}
	&__main {
		position: relative;
		grid-area: 1/1/1/1;
		align-self: flex-end;
	}
	&__title {
		margin: 0;
	}
	&__text {
		position: relative;
		&[data-controller] {
			@include mixins.line-clamp(4);
		}
	}
	&__more {
		position: absolute;
		right: 0;
		bottom: 0;
		padding-left: 5rem;
		background: linear-gradient(to right, transparent 0%, white 5rem);
	}
	&__video {
		display: block;
		border-radius: variables.$border-radius-lg;
		overflow: hidden;
	}
	&__decor {
		position: absolute;
		top: 0;
		right: 0;
		z-index: -1;
		display: block;
		width: 13rem;
		margin-right: -5rem;
	}

	// MODIF
	// &--w-img {
	// 	gap: 1.6rem;
	// 	#{$s}__main {
	// 		padding: 0 0 1.2rem;
	// 		color: variables.$color-white;
	// 	}
	// 	#{$s}__title {
	// 		color: variables.$color-white;
	// 	}
	// }
	&--w-img-v2 {
		#{$s}__bg {
			position: absolute;
			top: 0;
			&::before {
				background: linear-gradient(360deg, #ffffff 0%, rgba(255, 255, 255, 0) 53%),
					linear-gradient(0deg, rgba(106, 66, 8, 0) 68.73%, #6a4208 97.03%);
			}
		}
	}
	.row-main:has(&--w-img-v2) {
		position: static;
	}
	&--catalog &__title {
		--font-size-mobile: 2.6rem;
		--font-size-desktop: 6rem;
	}

	// STATES
	&__text.is-open {
		display: block;
		#{$s}__more {
			display: none;
		}
	}

	// MQ
	@media (config.$md-up) {
		gap: 1.2rem 5rem;
		&__decor {
			width: 20rem;
		}

		// MODIF
		// &--w-img {
		// 	gap: 4rem 5rem;
		// 	padding: 0 0 3.2rem;
		// }
	}
	@media (config.$lg-up) {
		grid-template-columns: 1fr minmax(0, 43rem);
		&__bg {
			grid-area: 1/1/1/3;
		}
		&__main {
			grid-area: 1/1/1/1;
		}
		&__content {
			grid-area: 2/1/2/1;
		}
		&__side {
			position: relative;
			grid-area: 1/2/3/2;
			margin-top: auto;
		}
		&__holder {
			position: absolute;
			right: 0;
			bottom: 0;
			left: 0;
		}
		&__decor {
			right: 50%;
			width: clamp(20rem, calc(40vw - 28rem), 46rem);
			max-width: none;
			margin-right: calc(var(--vw) / -2);
		}
		&__search.f-search {
			--search-height: 5.6rem;
		}

		// MODIF
		&--simple &__main,
		&--simple &__content {
			grid-area: auto / span 2;
		}
	}
	@media (min-width: 1920px) {
		&__decor {
			margin-right: -95.3rem;
		}
	}
}
