@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-suggest-results {
	overflow: clip;
	&__grid {
		--grid-x-spacing: 5rem;
		.grid__cell::before {
			content: '';
			position: absolute;
			top: 0;
			right: -2.5rem;
			bottom: 0;
			width: 0.1rem;
			background: variables.$color-tile-light;
		}
		.grid__cell:last-child::before {
			content: none;
		}
	}
	&__link {
		--color-link: #{variables.$color-text};
		--color-hover: #{variables.$color-primary};
		.icon-svg {
			opacity: 0;
			transition: opacity variables.$t;
		}
	}

	// HOVERS
	.hoverevents &__link:hover {
		background: variables.$color-white-hover;
		.icon-svg {
			opacity: 1;
		}
	}
}
