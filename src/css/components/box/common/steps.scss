@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-steps {
	--circle-size: 2.6rem;
	--circle-gap: 0.4rem;
	--steps-gap: 1.2rem;
	--steps-padding-top: 0.2rem;
	&__title {
		margin: 0 0 1.2rem;
	}
	&__list {
		@extend %reset-ol;
		counter-reset: item;
		display: flex;
		gap: var(--steps-gap);
		flex-direction: column;
	}
	&__item {
		@extend %reset-ol-li;
		position: relative;
		display: flex;
		gap: 1.2rem;
		align-items: flex-start;
		padding-top: var(--steps-padding-top);
		&::before {
			content: counter(item);
			counter-increment: item;
			position: relative;
			flex: 0 0 auto;
			align-content: center;
			width: var(--circle-size);
			margin-top: calc(var(--steps-padding-top) * -1);
			border-radius: 50%;
			background: variables.$color-primary-150;
			color: variables.$color-primary;
			font-family: variables.$font-secondary;
			font-weight: bold;
			font-size: 1.5rem;
			text-align: center;
			aspect-ratio: 1/1;
			font-variant-numeric: tabular-nums;
		}
	}
	&__arrow {
		position: absolute;
		top: 0;
		bottom: calc(var(--steps-gap) * -1);
		left: 0;
		width: var(--circle-size);
		&::before,
		&::after {
			content: '';
			position: absolute;
		}
		&::before {
			top: calc(var(--circle-size) + var(--circle-gap));
			bottom: var(--circle-gap);
			left: calc(var(--circle-size) / 2 - 0.05rem);
			width: 0.1rem;
			background: variables.$color-tile;
		}
		&::after {
			bottom: var(--circle-gap);
			left: calc((var(--circle-size) - 0.6rem) / 2);
			border-width: 0.6rem 0.3rem 0;
			border-style: solid;
			border-color: variables.$color-tile transparent transparent;
		}
	}

	// MQ
	@media (config.$md-up) {
		--circle-size: 3.6rem;
		--steps-gap: 1.8rem;
		--steps-padding-top: 0.4rem;
		&__inner {
			display: flex;
			gap: 4rem;
		}
		&__title {
			flex: 0 0 auto;
			width: 30rem;
			margin: 0;
		}
		&__step {
			gap: 1.6rem;
			&::before {
				font-size: 1.6rem;
			}
		}
	}
	@media (config.$lg-up) {
		// MODIF
		&--bd {
			padding: 6rem variables.$row-main-gutter;
			border: 0.1rem solid variables.$color-tile;
			border-radius: variables.$border-radius-xl;
		}
	}
}
