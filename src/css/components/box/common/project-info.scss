@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-project-info {
	display: flex;
	gap: 0.4rem;
	flex-direction: column;
	font-size: 1.2rem;
	&__progress {
		--progress-h: 0.8rem;
		--progress-bg: #{variables.$color-tile};
		--progress-bg-active: #{variables.$color-status-valid};
	}
	.item-icon {
		--icon-size: 1.5rem;
		--gap: 0.4rem;
	}

	// MQ
	@media (config.$md-up) {
		font-size: 1.3rem;
	}
}
