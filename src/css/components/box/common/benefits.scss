@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-benefits {
	margin: 0 0 3.2rem;
	font-size: 1.3rem;
	text-align: center;
	&__list {
		@extend %reset-ul;
		display: flex;
		gap: 1.6rem 0.4rem;
		flex-wrap: wrap;
		justify-content: center;
	}
	&__item {
		@extend %reset-ul-li;
		flex: 0 0 auto;
		width: 11.4rem;
	}
	&__icon {
		display: block;
		width: 4rem;
		margin: 0 auto 0.8rem;
		aspect-ratio: 1/1;
		object-fit: contain;
	}

	// MQ
	@media (config.$md-up) {
		margin: 0 0 5.2rem;
		font-size: 1.5rem;
		&__list {
			gap: 2rem;
		}
		&__item {
			width: 15.5rem;
		}
		&__icon {
			width: 5rem;
			margin: 0 auto 1rem;
		}
	}
}
