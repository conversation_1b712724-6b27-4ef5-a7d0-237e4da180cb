@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-bubble {
	position: relative;
	padding-top: 0.9rem;
	font-size: 1.4rem;
	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 50%;
		width: 0;
		height: 0;
		border-width: 0 1.8rem 1.8rem;
		border-style: solid;
		border-color: transparent transparent variables.$color-tile-light;
		transform: translateX(-50%);
	}
	&__box {
		padding: 1.6rem 2rem 2rem;
		border-radius: variables.$border-radius-xl;
		background-color: variables.$color-tile-light;
		ul {
			@extend %reset-ul;
			display: flex;
			gap: 0.4rem;
			flex-direction: column;
		}
		li {
			@extend %reset-ul-li;
			position: relative;
			padding-left: 2.2rem;
			&::before {
				content: '👉';
				position: absolute;
				top: 0.2em;
				left: 0;
				font-size: 1.1rem;
			}
		}
	}
}
