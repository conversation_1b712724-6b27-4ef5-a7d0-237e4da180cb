@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-bnr-dronzone {
	position: relative;
	margin: 0 0 4rem;
	padding: 0 2.4rem;
	border-radius: variables.$border-radius-xl;
	overflow: hidden;
	&__bg {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
	&__inner {
		position: relative;
		display: flex;
		gap: 1.7rem;
		justify-content: center;
		max-width: 94rem;
		margin: 0 auto;
	}
	&__main {
		flex: 1;
		padding: 4.8rem 0 4rem;
	}
	&__zone {
		margin: 0 0 0.8rem;
		img {
			width: 15.6rem;
		}
	}
	&__text {
		max-width: 55rem;
		margin: 0 0 1.6rem;
		line-height: 1.5;
	}
	&__img {
		flex: 0 0 auto;
		align-self: flex-end;
		img {
			width: 14.6rem;
			aspect-ratio: 266/418;
		}
	}

	// MQ
	@media (config.$sm-down) {
		&__img {
			margin-right: -6.5rem;
		}
	}
	@media (min-width: 636px) {
		margin: 0 0 5.2rem;
		padding: 0 3.6rem 0 4.8rem;
		&__inner {
			gap: 4rem;
		}
		&__main {
			padding: 6.2rem 0 5.4rem;
		}
		&__img img {
			width: 18.8rem;
		}
		&__text {
			--font-size-mobile: 2.1rem;
			--font-size-desktop: 2.1rem;
		}
		&__zone {
			margin: 0 0 1.2rem;
			img {
				width: 18.1rem;
			}
		}
		&__btn {
			--btn-fs: 1.5rem;
			--btn-h: 5.5rem;
			--btn-padding: 1.4rem 2.8rem 1.2rem;
			--btn-br: #{variables.$border-radius-lg};
		}
	}
	@media (config.$lg-up) {
		padding: 0 6rem 0 8rem;
		&__text {
			--font-size-desktop: 2.8rem;
			line-height: 1.6;
		}
		&__main {
			padding: 7.6rem 0 6.4rem;
		}
		&__img img {
			width: 26.6rem;
		}
		&__zone img {
			width: 29.7rem;
		}
	}
	@media (config.$xl-up) {
		padding: 0 variables.$row-main-gutter;
		&__main {
			padding: 9.9rem 0 8.5rem;
		}
	}
}
