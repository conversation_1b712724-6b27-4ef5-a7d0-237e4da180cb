@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-results {
	.grid {
		--grid-y-spacing: 1.2rem;
	}
	&__box {
		display: flex;
		gap: 1.2rem;
		flex-direction: column;
		height: 100%;
		padding: 2.4rem 2rem;
		border: 0.1rem solid variables.$color-tile;
		border-radius: variables.$border-radius-xl;
		& > * {
			margin: 0;
		}
	}
	&__text {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 1.2rem;
	}
	&__img {
		border-radius: variables.$border-radius-lg;
		background: variables.$color-bg;
	}
	&__persons {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 1.2rem;
	}

	// MQ
	@media (config.$lg-up) {
		&__box {
			gap: 2rem;
			padding: 4.4rem;
		}
		&__text {
			gap: 2rem;
		}
		&__persons {
			gap: 2rem;
		}
	}
}
