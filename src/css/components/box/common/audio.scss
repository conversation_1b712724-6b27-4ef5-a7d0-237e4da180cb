@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-audio {
	&__btn {
		--btn-h: 4.5rem;
		--btn-padding: 0;
		--btn-br: 50%;
		--btn-bg: #{variables.$color-white};
		--btn-c: #{variables.$color-text};
		--btn-lh: 1;
		--btn-icon-size: 2.5rem;
		--btn-hover-bg: #{variables.$color-primary};
		.btn__text {
			width: var(--btn-h);
		}
	}
	&__robot {
		--icon-offset: -0.2em;
		--icon-size: 2.5rem;
		display: flex;
		justify-content: center;
		align-items: flex-start;
	}
	&__inp {
		&::-webkit-slider-runnable-track {
			background: linear-gradient(to right, variables.$color-primary var(--progress), transparent var(--progress));
		}
		&::-moz-range-track {
			background: linear-gradient(to right, variables.$color-primary var(--progress), transparent var(--progress));
		}
	}

	// MODIF
	&__btn--play {
		--btn-c: #{variables.$color-primary};
		--btn-h: 6rem;
	}
}
