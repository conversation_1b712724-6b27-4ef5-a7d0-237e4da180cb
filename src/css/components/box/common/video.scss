@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-video {
	position: relative;
	display: block;
	overflow: hidden;
	&::before {
		content: '';
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		z-index: 1;
		background: linear-gradient(180deg, #000000 0%, rgba(0, 0, 0, 0) 100%);
		opacity: 0.6;
	}
	&__img {
		width: 100%;
		height: 100%;
		transition: transform variables.$t;
	}
	&--rounded {
		border-radius: variables.$border-radius-lg;
	}

	// HOVERS
	.hoverevents &:hover &__img {
		transform: scale3d(1.05, 1.05, 1);
	}
}
