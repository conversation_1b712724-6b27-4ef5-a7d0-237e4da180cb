@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-article {
	display: flex;
	flex-direction: column;
	.grid__cell > & {
		height: 100%;
	}
	&__img {
		position: relative;
		margin: 0;
		border-radius: variables.$border-radius-lg variables.$border-radius-lg 0 0;
		background: variables.$color-black;
		overflow: hidden;
	}
	&:has(&__author) &__img {
		position: relative;
		&::before {
			content: '';
			position: absolute;
			right: 0;
			bottom: 0;
			width: 11rem;
			border-radius: variables.$border-radius-lg variables.$border-radius-lg 0 0;
			background: linear-gradient(135.02deg, rgba(0, 0, 0, 0) 48.09%, #000000 89.62%);
			aspect-ratio: 1/1;
		}
	}
	&__flag {
		--flag-fs: 1.1rem;
		--flag-h: 2.2rem;
		position: absolute;
		bottom: 1rem;
		left: 1rem;
	}
	&__author {
		position: absolute;
		right: 1.7rem;
		bottom: 1rem;
		color: variables.$color-white;
	}
	&__badge {
		position: absolute;
		right: 2rem;
		bottom: 0;
		width: 5.8rem;
		transform: translateY(50%);
	}
	&__content {
		display: flex;
		flex: 1;
		flex-direction: column;
		padding: 2rem 2rem 2.4rem;
		border: 0.1rem solid variables.$color-tile;
		border-width: 0 0.1rem 0.1rem;
		border-radius: 0 0 variables.$border-radius-lg variables.$border-radius-lg;
		background: variables.$color-white;
	}
	&__title {
		margin: 0 0 0.8rem;
	}
	&__link {
		--color-hover: var(--color-link);
		--color-link-decoration: transparent;
		@include mixins.line-clamp(2);
	}
	&__desc {
		margin: 0 0 2rem;
	}
	&:has(&__project-info) &__desc {
		margin: 0 0 1.2rem;
	}
	&__project-info {
		margin: 0 0 1.6rem;
	}
	&__bottom {
		display: flex;
		gap: 1.6rem;
		align-items: center;
		margin-top: auto;
		color: variables.$color-help;
		font-size: 1.3rem;
		& > * {
			margin-bottom: 0;
		}
	}
	&__stats {
		--icon-color: #{variables.$color-icon-minor};
		--icon-size: 2rem;
		--gap: 0.4rem;
	}
	&__date {
		margin-left: auto;
	}

	// MQ
	@media (config.$md-up) {
		font-size: 1.5rem;
		&__badge {
			width: 6.8rem;
		}
		&__content {
			padding: 2.8rem 3.2rem 3.2rem;
		}
		&__desc {
			margin: 0 0 2.8rem;
		}
		&:has(&__project-info) &__desc {
			margin: 0 0 1.6rem;
		}
		&:has(&__bottom) &__desc {
			margin-bottom: 2.2rem;
		}
	}
}
