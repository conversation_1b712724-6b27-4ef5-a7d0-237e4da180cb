@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-help {
	$s: &;
	max-width: 28.2rem;
	margin: 0 auto;
	color: variables.$color-help;
	font-size: 1.2rem;
	line-height: 1.4;
	&__title {
		margin: 0 0 0.8rem;
	}
	&__box {
		display: flex;
		gap: 1.6rem;
		align-items: center;
		padding: 1.4rem 2rem 0.8rem;
		border-radius: variables.$border-radius-xl;
		background: variables.$color-bg;
		text-wrap: pretty;
	}
	&__person {
		flex: 0 0 auto;
		width: 6rem;
		.person__img {
			margin-bottom: -1.3rem;
		}
		.bubble {
			padding: 0.1rem 0.8rem;
		}
	}
	&__phone {
		--icon-size: 2rem;
		--gap: 0.4rem;
		font-weight: bold;
		font-size: 1.4rem;
		text-decoration: none;
	}
	&__decor {
		display: none;
	}

	// MQ
	@media (config.$md-up) {
		max-width: 31.8rem;
		font-size: 1.3rem;
		&__title {
			margin: 0 0 1.2rem;
		}
		&__person {
			width: 7rem;
		}
	}
	@media (config.$lg-up) {
		// MODIF
		&--big {
			position: relative;
			max-width: none;
			border-radius: variables.$border-radius-xl;
			font-size: 1.4rem;
			line-height: 1.6;
			overflow: hidden;
			transform: translateZ(0);
			#{$s}__helper {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				height: 100%;
				padding: 4rem;
				background: rgba(variables.$color-bg, 0.85);
				text-align: center;
			}
			#{$s}__decor {
				position: absolute;
				top: 50%;
				z-index: -1;
				display: block;
				width: auto;
				max-width: none;
				height: 60%;
				transform: translateY(-50%) translateX(-70%);
				filter: blur(8rem);
			}
			#{$s}__box {
				display: contents;
			}
			#{$s}__person {
				order: 1;
				width: 11rem;
				margin: 0 0 2rem;
			}
			#{$s}__title {
				order: 2;
				margin: 0 0 1.6rem;
			}
			#{$s}__contact {
				display: contents;
			}
			#{$s}__phone {
				order: 3;
				margin: 0 0 1.6rem;
				padding: 1.2rem 2rem;
				border-radius: 1.6rem;
				background: variables.$color-white;
			}
			#{$s}__hours {
				order: 4;
			}
		}
	}
}
