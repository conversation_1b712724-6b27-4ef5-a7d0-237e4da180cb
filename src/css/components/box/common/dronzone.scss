@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-dronzone {
	position: relative;
	margin: 0 0 8rem;
	&__bg {
		position: absolute;
		top: 50%;
		left: 50%;
		z-index: -1;
		width: var(--vw);
		max-width: 192rem;
		min-height: 100%;
		transform: translate(-50%, -50%);
	}
	&__title {
		margin: 0 0 1.2rem;
	}
	&__outline {
		display: block;
		width: 18.1rem;
		margin: 0 0 0.8rem;
	}
	&__list {
		@extend %reset-ul;
		display: flex;
		gap: 0.8rem;
		flex-wrap: wrap;
		margin: 0 0 2rem;
		li {
			@extend %reset-ul-li;
			flex: 0 0 auto;
		}
	}
	&__news {
		margin: 0 0 2rem;
	}

	// MQ
	@media (config.$md-down) {
		&__btn .btn {
			--btn-h: 5.5rem;
		}
	}
	@media (config.$md-up) {
		&__title {
			margin: 0 0 2.4rem;
			text-align: center;
		}
		&__outline {
			width: 38.5rem;
			margin: 0 auto 2.4rem;
		}
		&__list {
			justify-content: center;
			margin: 0 0 4rem;
		}
		&__news {
			margin: 0 0 4rem;
		}
		&__btn {
			text-align: center;
		}
	}
	@media (config.$lg-up) {
		margin: 0 0 13.2rem;
	}
}
