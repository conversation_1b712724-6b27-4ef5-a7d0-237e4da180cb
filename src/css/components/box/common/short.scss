@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-short {
	position: relative;
	margin: 0;
	border-radius: variables.$border-radius-lg;
	background: linear-gradient(180deg, rgba(1, 4, 20, 0.72) 4.6%, rgba(1, 4, 20, 0) 10.8rem);
	font-size: 1.4rem;
	overflow: hidden;
	aspect-ratio: 9/16;
	&__link {
		--color-link: #{variables.$color-white};
		--color-hover: var(--color-link);
		--color-link-decoration: transparent;
		display: block;
		height: 100%;
		padding: 1.2rem 1.6rem;
	}
	&__img {
		position: absolute;
		z-index: -1;
		background: variables.$color-black;
		inset: 0;
	}
	&__play {
		max-width: 6.5rem;
		background: rgba(white, 0.2);
	}
}
