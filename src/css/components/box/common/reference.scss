@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-reference {
	position: relative;
	height: 100%;
	padding: 3.2rem 2rem;
	border-radius: variables.$border-radius-xl;
	background: variables.$color-bg;
	&::before {
		content: '';
		position: absolute;
		top: 3.2rem;
		right: 2rem;
		width: 4rem;
		background: url(variables.$img-path + 'illust/quotes.svg');
		background-repeat: no-repeat;
		background-size: contain;
		aspect-ratio: 1/1;
	}
	&__imgs {
		display: flex;
		margin: 0 0 1.6rem;
		img {
			width: 6rem;
			background: variables.$color-white;
		}
		img:nth-child(1) {
			position: relative;
			margin-right: -1.8rem;
		}
	}
	&__text {
		margin: 0 0 1.2rem;
		font-size: 1.5rem;
	}

	// MQ
	@media (config.$md-up) {
		&__text {
			font-size: 1.7rem;
		}
	}
	@media (config.$xl-up) {
		&__box {
			padding: 6rem;
			&::before {
				top: 6rem;
				right: 6rem;
				width: 8rem;
			}
		}
		&__imgs {
			margin: 0 0 2.4rem;
			img {
				width: 10rem;
			}
			img:nth-child(1) {
				margin-right: -3rem;
			}
		}
		&__text {
			margin: 0 0 2.4rem;
		}
	}
}
