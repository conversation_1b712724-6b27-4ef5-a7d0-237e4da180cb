@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-bnr {
	padding: 2.1rem;
	.grid__cell > & {
		height: 100%;
	}
	&__inner {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		height: 100%;
		padding: 4rem 4.4rem;
		border-radius: variables.$border-radius-xl;
		background: variables.$color-bg;
		text-align: center;
	}
	&__link {
		--color-link: #{variables.$color-text};
		--color-hover: #{variables.$color-primary};
		text-decoration: none;
	}
	&__img {
		width: 100%;
		margin: 0 0 4rem;
	}
	&__tag {
		color: variables.$color-primary;
		font-weight: bold;
		font-size: 1.1rem;
		text-transform: uppercase;
	}
	&__content a {
		position: relative;
		z-index: 2;
	}
}
