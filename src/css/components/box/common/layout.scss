@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.b-layout {
	$s: &;
	overflow: hidden;

	// MQ
	@media (config.$lg-down) {
		background: variables.$color-white;
		&__nav {
			margin: 0 0 1.4rem;
		}
		&__menu {
			display: none;
			border-radius: 0 0 variables.$border-radius-sm variables.$border-radius-sm;
			background: variables.$color-bg-light;
		}
		&__main {
			margin: 0 0 4.8rem;
		}

		// STATES
		&__nav.is-open &__menu {
			display: block;
		}
	}
	@media (config.$lg-up) {
		--gap: var(--grid-gutter);
		position: relative;
		overflow: hidden;
		&__inner {
			display: grid;
			grid-template-columns: 29rem minmax(0, 1fr);
			grid-template-rows: auto 1fr;
			grid-template-areas:
				'nav bc'
				'nav main';
			gap: 0 var(--gap);
			&::before {
				content: '';
				position: absolute;
				top: 0;
				right: -500rem;
				bottom: 0;
				left: 32rem;
				z-index: -1;
				border-left: 0.1rem solid variables.$color-bg;
				background: variables.$color-white;
			}
		}
		&__nav {
			grid-area: nav;
			align-self: flex-start;
		}
		&__bc {
			grid-area: bc;
		}
		&__main {
			grid-area: main;
			padding: 0 0 11.3rem;
		}

		// MODIF
		&--catalog {
			#{$s} {
				&__inner {
					grid-template-rows: repeat(7, auto) 1fr;
					grid-template-areas:
						'nav bc'
						'nav title'
						'nav selected'
						'nav categories'
						'nav filter'
						'nav bestsellers'
						'nav sort'
						'nav main';
				}
				&__nav {
					margin-left: -2rem;
				}
				&__title {
					grid-area: title;
				}
				&__selected {
					grid-area: selected;
				}
				&__categories {
					grid-area: categories;
				}
				&__filter {
					grid-area: filter;
				}
				&__bestsellers {
					grid-area: bestsellers;
				}
				&__sort {
					grid-area: sort;
				}
			}
		}
	}
	@media (config.$xl-up) {
		--gap: 7rem;
	}
}
