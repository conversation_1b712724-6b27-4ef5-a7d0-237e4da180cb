@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.b-user {
	padding: 2.4rem 3rem;
	&__head {
		margin: 0 0 1.6rem;
		padding: 0 0 1.2rem;
		border-bottom: 0.1rem solid variables.$color-bg;
	}
	&__title {
		margin: 0;
	}
	&__membership {
		--icon-size: 1.5rem;
		--gap: 0.4rem;
		font-size: 1.4rem;
		.icon-svg {
			color: variables.$color-black;
		}
	}

	// MQ
	@media (config.$lg-up) {
		padding: 3.6rem 4rem;
		background: variables.$color-white;
	}
}
