@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-quote {
	padding: 2.4rem 2rem;
	border-radius: variables.$border-radius-xl;
	background: variables.$color-bg;
	&__person {
		display: flex;
		gap: 1.6rem;
		align-items: center;
		margin: 0 0 1.2rem;
	}
	&__img {
		flex: 0 0 auto;
		width: 5.3rem;
	}
	&__quotes {
		flex: 0 0 auto;
		width: 4rem;
		margin-left: auto;
	}

	// MQ
	@media (config.$md-up) {
		padding: 4rem;
	}
}
