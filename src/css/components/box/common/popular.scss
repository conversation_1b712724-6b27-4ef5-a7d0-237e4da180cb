@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-popular {
	display: grid;
	gap: 0.8rem;
	&__course {
		display: grid;
		border-radius: variables.$border-radius-xl;
		overflow: hidden;
	}
	&__img {
		grid-area: 1/1/1/1;
		img {
			aspect-ratio: 16/9;
		}
	}
	&__content {
		position: relative;
		grid-area: 2/1/2/1;
	}
	&__content-inner {
		padding: 5.2rem 2rem 3.6rem;
		background: rgba(variables.$color-bg, 0.85);
		backdrop-filter: blur(6rem);
	}
	&__decorations {
		position: absolute;
		overflow: hidden;
		inset: 0;
		&::before,
		&::after {
			content: '';
			position: absolute;
			z-index: -1;
			background: url(variables.$img-path + 'illust/dragonscale.webp') no-repeat center / cover;
		}
		&::before {
			top: 0;
			right: 0;
			width: 12.8rem;
			transform: translateX(50%);
			aspect-ratio: 128/155;
		}
		&::after {
			bottom: -12.3rem;
			left: 0;
			width: 44rem;
			aspect-ratio: 2/1;
		}
	}
	&__approved {
		margin: 0;
	}
	&__title {
		margin: 0 0 1rem;
	}
	&__annot {
		margin: 0 0 1rem;
	}
	&__info {
		margin: 0 0 1.2rem;
		font-size: 1.5rem;
		&--inline {
			display: flex;
			gap: 0 2rem;
			align-items: center;
		}
	}
	&__price {
		font-weight: bold;
	}
	&__video {
		border-radius: variables.$border-radius-xl;
		overflow: hidden;
		.b-video,
		img {
			height: 100%;
		}
	}
	&__quote {
		position: relative;
		display: flex;
		gap: 1.2rem;
		align-items: center;
		padding: 2rem 2.8rem;
		border-radius: variables.$border-radius-xl;
		background: variables.$color-bg;
		font-size: 1.3rem;
		&::before {
			content: '';
			position: absolute;
			bottom: 100%;
			left: 50%;
			border-width: 0 0.8rem 0.8rem;
			border-style: solid;
			border-color: transparent transparent variables.$color-bg transparent;
			transform: translateX(-50%);
		}
		img {
			flex: 0 0 auto;
			width: 5rem;
		}
	}

	// MODIF
	&__decorations--tip {
		&::before {
			content: none;
		}
		&::after {
			right: 0;
			bottom: 0;
			left: auto;
			width: 29.3rem;
			aspect-ratio: 293/147;
		}
	}

	// MQ
	@media (config.$xl-down) {
		&__content {
			font-size: 1.5rem;
		}
		&__approved {
			position: absolute;
			top: 0%;
			left: 2rem;
			transform: translateY(-50%);
		}
	}
	@media (config.$md-up) {
		grid-template-columns: 1fr 1fr;
		grid-template-rows: auto auto;
		gap: 2rem;
		&__course {
			grid-area: 1/1/3/1;
		}
		&__video {
			grid-area: 1/2/1/2;
		}
		&__quote {
			grid-area: 2/2/2/2;
		}
		&__info {
			font-size: 1.7rem;
		}
	}
	@media (config.$xl-up) {
		grid-template-columns: 7fr 5fr;
		&__course {
			grid-template-columns: 28rem 1fr;
		}
		&__img {
			grid-area: 1/1/1/1;
			width: 100%;
			height: 100%;
		}
		&__content {
			grid-area: 1/2/1/2;
			flex: 1;
		}
		&__content-inner {
			padding: 6rem;
		}
		&__approved {
			margin: 0 0 1.2rem;
		}
		&__annot {
			margin: 0 0 1.6rem;
		}
		&__info {
			margin: 0 0 2.4rem;
		}
		&__quote {
			padding: 1.8rem 6rem;
			font-size: 1.5rem;
		}
	}
}
