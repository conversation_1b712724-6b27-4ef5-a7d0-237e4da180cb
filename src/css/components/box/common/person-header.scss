@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-person-header {
	$s: &;
	display: flex;
	gap: 0.6rem;
	align-items: center;
	margin: 0;
	color: variables.$color-help;
	font-size: 0.9rem;
	transition: color variables.$t;
	&__person {
		flex: 0 0 auto;
		width: 2.9rem;
	}
	&__call {
		display: flex;
		gap: 0 0.4rem;
		align-items: center;
		transition: color variables.$t;
	}
	&__link {
		--color-link-decoration: transparent;
		font-weight: bold;
		font-size: 1.2rem;
	}

	// STATES
	.is-top > .header--inversed & {
		color: variables.$color-inverse-help;
		#{$s}__link {
			--color-link: #{variables.$color-white};
			--color-hover: #{variables.$color-white};
		}
	}

	// MQ
	@media (config.$md-down) {
		&__person {
			.bubble {
				padding: 0 0.3rem;
				font-size: 0.7rem;
			}
		}
		&__call {
			line-height: 1.2;
		}
	}
	@media (config.$md-up) {
		gap: 1.6rem;
		font-size: 1.2rem;
		&__person {
			width: 4.3rem;
		}
		&__link {
			font-size: 1.5rem;
		}
	}
	@media (config.$xl-down) {
		&__text {
			display: none;
		}
		&__call {
			flex-direction: column;
			align-items: flex-start;
		}
	}
}
