@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-faq {
	&__title {
		margin: 0 0 1.6rem;
	}
	&__list {
		@extend %reset-ul;
		margin: 0 0 -0.8rem;
	}
	&__item {
		@extend %reset-ul-li;
		margin: 0 0 0.8rem;
	}
	&__item--question {
		position: relative;
		display: block;
		padding: 1.6rem;
		border-radius: variables.$border-radius-xl;
		background: variables.$color-bg;
		break-inside: avoid;
	}
	&__bubble-ear {
		position: absolute;
		right: -0.3rem;
		bottom: -0.6rem;
		width: 1.4rem;
		color: variables.$color-bg;
		transform: scaleX(-1) rotate(-90deg);
	}
	&__icon {
		display: block;
		flex: 0 0 auto;
		align-content: center;
		width: 4rem;
		border-radius: 50%;
		background: variables.$color-primary-150;
		color: variables.$color-primary;
		text-align: center;
		aspect-ratio: 1/1;
		.icon-svg {
			width: 2.6rem;
		}
	}
	&__question {
		display: flex;
		gap: 0.8rem;
		justify-content: space-between;
		align-items: center;
		margin: 0 0 0.4rem;
	}
	&__btn {
		display: flex;
		gap: 1.6rem;
		flex-direction: column;
		align-items: center;
	}

	// MODIF
	&--bd {
		padding: 4.8rem 0;
		border: 0.1rem solid variables.$color-tile-light;
		border-width: 0.1rem 0;
	}

	// MQ
	@media (config.$md-up) {
		&__title {
			margin: 0 0 2.4rem;
		}
		&__item--question {
			padding: 3.2rem;
		}
		&__main &__list {
			margin: 0 0 -2.4rem;
		}
		&__main &__item {
			margin: 0 0 2.4rem;
		}
		&__question {
			flex-direction: column-reverse;
			align-items: flex-start;
		}
	}
	@media (config.$lg-up) {
		&__main {
			gap: 2.7rem;
			column-count: 2;
		}
		&__title {
			padding: 3rem 0;
		}

		// MODIF
		&--bd {
			margin: 0 0 5.2rem;
			padding: 8rem 0;
		}
	}
}
