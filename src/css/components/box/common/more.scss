/* stylelint-disable declaration-no-important */
@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-more {
	padding: 0.6rem;
	&__btn {
		--color-link: #{variables.$color-black};
		--color-hover: #{variables.$color-primary};
		display: flex;
	}
	&__close {
		--color-link: #{variables.$color-black};
		--color-hover: #{variables.$color-black};
		position: absolute;
		top: 0.4rem;
		right: 0.4rem;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 3.1rem;
		border-radius: 50%;
		transition: background-color variables.$t;
		aspect-ratio: 1/1;
		.icon-svg {
			width: 1.5rem;
		}
	}
	&__content {
		padding: 3.9rem 0 0;
	}
	&__menu {
		padding: 0 1.2rem 1.2rem;
		& > * {
			margin-bottom: 0;
		}
		[data-tippy-root]:has(&) {
			width: min(27.4rem, calc(100vw - (variables.$row-main-gutter * 2)));
		}
		.tippy-content:has(&) {
			padding: 0;
		}
		hr {
			margin: 0.8rem 0;
			border-color: variables.$color-tile-light;
		}
	}
	.tippy-content:has(&__menu),
	.tippy-content:has(&__remove) {
		padding: 0;
	}
	&__link {
		--color-link: #{variables.$color-text};
		--color-hover: #{variables.$color-primary};
		display: block;
		width: 100%;
		padding: 0.7rem 1.6rem;
		border-radius: variables.$border-radius-md;
		text-decoration: none;
		transition: color variables.$t, background-color variables.$t;
	}
	&__variant,
	&__remove {
		display: none;
		padding: 0 3.2rem 3.2rem;
	}
	&__btns {
		display: flex;
		gap: 0.8rem;
		justify-content: center;
	}

	// STATES
	&__content.is-open-variant &__variant,
	&__content.is-open-remove &__remove {
		display: block;
	}
	&__content.is-open-variant &__menu,
	&__content.is-open-remove &__menu {
		display: none;
	}

	// HOVERS
	.hoverevents &__close:hover {
		background-color: variables.$color-bg;
	}
	.hoverevents &__link:hover {
		background-color: #f8f9ff;
	}

	// MQ
	@media (config.$lg-up) {
		padding: 1.6rem;
	}
}
