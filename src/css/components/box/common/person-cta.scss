@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-person-cta {
	padding: 2rem 0;
	font-size: 1.5rem;
	&__person {
		width: 7rem;
		margin: 0 auto 1rem;
	}
	&__content {
		*:is(h1, h2, h3, h4, h5, h6) {
			--font-size-mobile: 2.4rem;
			--font-size-desktop: 4rem;
			margin: 0 0 0.4rem;
		}
	}

	// MQ
	@media (config.$md-up) {
		padding: 2rem 0 3rem;
		font-size: 1.7rem;
		&__person {
			width: 9rem;
			margin: 0 auto 2rem;
		}
		&__content {
			.btn {
				--btn-fs: 1.5rem;
				--btn-h: 5.5rem;
				--btn-padding: 0.5rem 2.8rem 0.3rem;
				--btn-br: #{variables.$border-radius-lg};
			}
		}
	}
}
