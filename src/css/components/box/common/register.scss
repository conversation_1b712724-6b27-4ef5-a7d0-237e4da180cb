@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.b-register {
	&__login {
		padding: 3.2rem 2rem 2.5rem;
	}
	&__register {
		padding: 3.2rem 2rem 2.5rem;
		border-radius: variables.$border-radius-sm;
		background: variables.$color-bg-light;
	}
	&__content {
		font-size: 1.5rem;
		p {
			margin-bottom: 1.3em;
		}
		ul {
			@extend %reset-ul;
			margin: 0 0 2rem;
		}
		li {
			@extend %reset-ul-li;
			margin: 0 0 0.8rem;
			padding-left: 3rem;
			background-image: url(variables.$svg-check-circle);
			background-position: 0.1rem 0.1em;
			background-repeat: no-repeat;
			background-size: 2rem 2rem;
		}
	}

	// MODIF
	.b-login &__register {
		border: 0.1rem solid variables.$color-bg;
		border-width: 0.1rem 0 0;
		border-radius: 0;
	}

	// MQ
	@media (config.$sm-up) {
		&__login,
		&__register {
			padding: 4rem 3rem;
		}
	}
	@media (config.$md-up) {
		display: flex;
		&__login {
			flex: 0 0 48.5%;
		}
		.b-login &__register {
			flex: 0 0 51.5%;
			border-width: 0 0 0 0.1rem;
		}
	}
	@media (config.$lg-up) {
		&__login,
		&__register,
		.b-login &__register {
			padding: 5rem 7rem;
		}
	}
}
