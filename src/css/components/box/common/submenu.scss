@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-submenu {
	@media (max-width: 899px) {
		position: absolute;
		top: 0;
		left: 100%;
		display: none;
		width: 100%;
		max-height: calc(100dvh - 1rem);
		padding: var(--menu-padding-top) 0 1.6rem;
		overflow-x: hidden;
		overflow-y: auto;
		&__side {
			display: none;
		}
		&__head {
			padding: 0 3rem;
		}
		&__back {
			margin: 0 0 1.2rem;
		}
		&__btn {
			--btn-icon-size: 1.3rem;
			--btn-gap: 0.4rem;
			--btn-h: 3.7rem;
			--btn-padding: 0.8rem 1.2rem;
			--btn-bg: #{variables.$color-primary-150};
			--btn-c: #{variables.$color-link};
			--btn-icon-offset: 0;
			// --btn-hover-c: #{variables.$color-link};
			// --btn-hover-bg: #{rgba(variables.$color-white, 0.7)};
		}
		&__title {
			display: flex;
			justify-content: space-between;
			align-items: baseline;
			margin: 0 0 1.6rem;
			a {
				--color-link-decoration: transparent;
				--color-hover: var(--color-link);
			}
		}

		// STATES
		.is-submenu-open & {
			display: block;
		}
		&:has(.is-submenu2-open) {
			overflow: visible;
		}
	}
	@media (min-width: 900px) {
		position: absolute;
		top: calc(100% - 1.2rem);
		left: 50%;
		z-index: 1;
		width: var(--vw);
		max-width: variables.$row-main-width;
		padding: 2.4rem var(--row-main-gutter) 0;
		font-size: 1.5rem;
		visibility: hidden;
		opacity: 0;
		transform: translateX(-5000px);
		transition: transform 0s variables.$t, opacity variables.$t, visibility variables.$t;
		&__inner {
			max-height: calc(100dvh - 18.2rem);
			border-radius: variables.$border-radius-xl;
			background: variables.$color-bg;
			overflow-x: hidden;
			overflow-y: auto;
			overscroll-behavior: none;
		}
		&__head {
			display: none;
		}
		&__main {
			padding: 2rem;
		}
		&__side {
			position: relative;
			padding: 4rem;
			background: #e2e5f1;
		}

		// STATES
		.is-open &,
		.is-hover & {
			visibility: visible;
			opacity: 1;
			transform: translateX(-50%);
			transition: transform 0s 0s, opacity variables.$t, visibility variables.$t;
		}
		.is-pinned &__inner {
			max-height: calc(100dvh - 13.7rem);
		}
	}
	// MQ
	@media (config.$xl-up) {
		&__wrap {
			display: flex;
		}
		&__main {
			flex: 1;
			padding: 2.8rem;
		}
		&__side {
			flex: 0 0 auto;
			align-content: center;
			width: 30rem;
			padding: 4rem;
		}
	}
	@media (config.$xxxl-up) {
		&__side {
			width: 40rem;
			padding: 6rem;
		}
		&__main {
			padding: 4rem;
		}
	}
}
