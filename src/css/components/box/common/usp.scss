@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-usp {
	&__title {
		margin: 0 0 1.2rem;
	}
	&__list {
		@extend %reset-ul;
		counter-reset: item;
		display: flex;
		gap: 1.2rem;
		flex-wrap: wrap;
	}
	&__item {
		@extend %reset-ul-li;
		position: relative;
		flex: 1 0 calc(50% - 0.6rem);
		padding-top: 2.3rem;
		&::before {
			content: '0' counter(item);
			position: absolute;
			top: 0;
			left: 1.2rem;
			font-family: variables.$font-secondary;
			font-weight: 800;
			font-size: 4rem;
			line-height: 1;
			-webkit-text-stroke: 0.1rem variables.$color-primary;
			-webkit-text-fill-color: transparent;
		}
	}
	&__box {
		position: relative;
		height: 100%;
		padding: 1.6rem;
		border-radius: variables.$border-radius-md;
		background: rgba(variables.$color-bg, 0.5);
		backdrop-filter: blur(1.2rem);
	}

	// MQ
	@media (config.$xl-down) {
		&__emoji {
			display: inline-block;
			transform: rotate(90deg);
		}
	}
	@media (config.$md-up) {
		&__item {
			flex: 1 0 calc(25% - 0.9rem);
			padding-top: 3rem;
			&::before {
				left: 50%;
				transform: translate(-50%);
			}
		}
		&__box {
			align-content: center;
			text-align: center;
		}
	}
	@media (config.$lg-up) {
		&__list {
			gap: 3.2rem;
			flex: 1;
		}
		&__item {
			flex: 1 0 calc(25% - 2.4rem);
			padding-top: 6.4rem;
			&::before {
				font-size: 9rem;
				-webkit-text-stroke: 0.15rem variables.$color-primary;
			}
		}
		&__box {
			padding: 3rem 2rem;
			border-radius: variables.$border-radius-lg;
			font-size: 1.7rem;
		}
	}
	@media (config.$xl-up) {
		display: flex;
		gap: 6rem;
		padding: 0 4rem;
		&__title {
			flex: 0 0 31rem;
			align-self: center;
			margin: 0;
		}
		&__inner &__title {
			margin: 0 0 2.4rem;
		}
		&__inner &__emoji {
			display: inline-block;
			transform: rotate(90deg);
		}
		&__box {
			padding: 4rem 2.4rem;
		}
	}
}
