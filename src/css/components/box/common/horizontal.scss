@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.b-horizontal {
	display: flex;
	gap: 2rem;
	align-items: center;
	margin: 0;
	padding: 1.2rem 2rem;
	border-radius: variables.$border-radius-sm;
	background: variables.$color-bg-light;
	font-size: 1.5rem;
	&__img {
		flex: 0 0 auto;
		width: 3.7rem;
	}
	&__link {
		color: inherit;
	}

	// MQ
	@media (config.$md-up) {
		gap: 2.5rem;
		padding: 1.2rem 3rem;
	}
}
