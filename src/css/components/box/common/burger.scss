@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/mixins';

.b-burger {
	$s: &;
	@include mixins.button-reset;
	z-index: 1;
	display: flex;
	gap: 0.1rem;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	width: 4.9rem;
	height: 4.9rem;
	padding: 0.8rem 0 0.6rem;
	border-radius: variables.$border-radius-md;
	background: variables.$color-primary;
	color: variables.$color-white;
	outline: none;
	font-family: variables.$font-secondary;
	font-weight: 600;
	font-size: 0.9rem;
	text-transform: uppercase;
	transition: background-color variables.$t;
	-webkit-tap-highlight-color: transparent;
	&__inner {
		position: relative;
		display: block;
		width: 2rem;
		height: 2rem;
	}
	&__inner span {
		position: absolute;
		left: 10%;
		display: block;
		width: 80%;
		height: 0.2rem;
		border-radius: 0.1rem;
		background: variables.$color-white;
		transition: transform variables.$t, top variables.$t, left, variables.$t, right variables.$t, width variables.$t,
			background-color variables.$t;
		&:nth-child(1) {
			top: 0.3rem;
		}
		&:nth-child(2),
		&:nth-child(3) {
			top: 0.9rem;
			width: 60%;
		}
		&:nth-child(4) {
			top: 1.5rem;
			width: 75%;
		}
	}

	// STATES
	.is-open &__inner span,
	.is-menu-open &__inner span {
		left: 5%;
		&:nth-child(1),
		&:nth-child(4) {
			right: 50%;
			width: 0;
		}
		&:nth-child(2) {
			width: 90%;
			transform: rotate(45deg);
		}
		&:nth-child(3) {
			width: 90%;
			transform: rotate(-45deg);
		}
	}

	// HOVERS
	.hoverevents &:hover {
		background: variables.$color-primary-hover;
	}

	// MQ
	@media (min-width: 636px) {
		gap: 0.6rem;
		flex-direction: row-reverse;
		width: auto;
		height: 5.2rem;
		padding: 1.5rem 1.4rem 1.5rem 2rem;
		border-radius: 1.6rem;
		font-size: 1.3rem;
	}
}
