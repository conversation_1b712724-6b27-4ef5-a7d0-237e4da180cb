@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-std {
	height: 100%;
	&__title {
		height: 100%;
		margin: 0;
		font-size: inherit;
	}
	&__link {
		display: flex;
		align-items: center;
		height: 100%;
		min-height: 5.6rem;
		padding: 0.8rem 1.5rem;
	}
	.item-icon {
		--gap: 1.2rem;
		--icon-size: 4rem;
		--icon-color: #{variables.$color-black};
	}

	// MQ
	@media (config.$md-up) {
		&__link {
			min-height: 10rem;
			padding: 1rem 3.2rem;
		}
	}
}
