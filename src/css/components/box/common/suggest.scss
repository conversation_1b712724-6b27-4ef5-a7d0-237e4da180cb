@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-suggest {
	display: none;
	&__box {
		align-content: center;
		padding: 1.2rem 1.2rem 1.6rem;
		border-radius: variables.$border-radius-xl;
		background: variables.$color-white;
	}
	// &__group {
	// 	margin: 0 0 0.8rem;
	// }
	// &__title {
	// 	display: flex;
	// 	gap: 0.8rem;
	// 	align-items: center;
	// 	margin: 0;
	// 	padding: 0.4rem 0;
	// 	font-weight: bold;
	// 	font-size: 1.4rem;
	// 	&::after {
	// 		content: '';
	// 		display: inline-block;
	// 		flex: 1 1 auto;
	// 		height: 0.1rem;
	// 		background: variables.$color-tile-light;
	// 	}
	// }
	// &__list {
	// 	@extend %reset-ul;
	// 	font-size: 1.4rem;
	// 	li {
	// 		@extend %reset-ul-li;
	// 	}
	// }
	// &__link {
	// 	--color-link: #{variables.$color-text};
	// 	--color-hover: #{variables.$color-primary};
	// 	position: relative;
	// 	display: flex;
	// 	gap: 1rem;
	// 	align-items: center;
	// 	padding: 0.4rem 3rem 0.4rem 0.8rem;
	// 	border-radius: variables.$border-radius-md;
	// 	text-decoration: none;
	// 	transition: color variables.$t, background-color variables.$t;
	// 	&::after {
	// 		content: '';
	// 		position: absolute;
	// 		top: 50%;
	// 		right: 0.8rem;
	// 		width: 1.2rem;
	// 		height: 1.2rem;
	// 		background: url(variables.$svg-angle-right-primary);
	// 		background-position: contain;
	// 		background-repeat: no-repeat;
	// 		visibility: hidden;
	// 		opacity: 0;
	// 		transform: translateY(-50%);
	// 		transition: opacity variables.$t, visibility variables.$t;
	// 	}
	// }
	&__info {
		color: variables.$color-help;
		font-size: 1.5rem;
	}
	// &__img {
	// 	flex: 0 0 auto;
	// 	width: 7.2rem;
	// 	border-radius: variables.$border-radius-sm;
	// 	background: variables.$color-white;
	// 	aspect-ratio: 72/54;
	// 	object-fit: contain;
	// }
	&__more {
		margin-top: 2rem;
		padding: 1.6rem 0 1.2rem;
		border-top: 0.1rem solid variables.$color-tile-light;
	}

	// MODIF
	// &__list--products &__link {
	// 	padding: 0.3rem 3rem 0.3rem 0.3rem;
	// }

	// STATES
	*:is(.is-top, .is-pinned) &.is-visible {
		display: block;
	}

	// HOVERS
	// &__link:hover {
	// 	background-color: #f8f9ff;
	// 	&::after {
	// 		visibility: visible;
	// 		opacity: 1;
	// 	}
	// }

	// MQ
	@media (min-width: 636px) {
		position: absolute;
		top: calc(100% - 0.8rem);
		left: 50%;
		width: calc(100% - var(--row-main-gutter) * 2);
		padding-top: 1.3rem;
		transform: translateX(-50%);
		&__box {
			min-height: 18.8rem;
			max-height: calc(100dvh - 8.7rem);
			padding: 2rem 2.4rem;
			overflow-x: hidden;
			overflow-y: auto;
			overscroll-behavior: none;
		}
	}
	@media (min-width: 900px) {
		top: 100%;
		width: 65rem;
	}
	@media (config.$xxl-up) {
		width: 121.6rem;
		transform: translateX(-50%) translateX(-21rem);
	}
	@media (min-width: 1920px) {
		transform: translateX(-50%);
	}
}
