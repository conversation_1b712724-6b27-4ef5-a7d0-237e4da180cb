@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.b-photos {
	--photos-gap: 1rem;
	margin: 0;
	&__helper,
	.embla__viewport {
		display: block;
		height: 100%;
	}
	.embla__container {
		display: flex;
		gap: var(--photos-gap);
		height: 100%;
	}
	img {
		width: auto;
		max-width: none;
		height: 100%;
		border-radius: variables.$border-radius-sm;
		&:last-child {
			border-right: var(--photos-gap) solid transparent;
		}
	}

	// MODIF
	&.full-width {
		height: 19.8rem;
	}

	// MQ
	@media (config.$md-up) {
		--photos-gap: 2rem;

		// MODIF
		&.full-width {
			height: 30rem;
		}
	}
	@media (config.$xl-up) {
		&.full-width {
			height: 38.7rem;
		}
	}
}
