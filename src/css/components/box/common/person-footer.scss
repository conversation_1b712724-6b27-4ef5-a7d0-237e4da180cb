@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-person-footer {
	display: flex;
	gap: 1.2rem;
	align-items: center;
	font-size: 1.5rem;
	&__person {
		flex: 0 0 8rem;
	}
	a {
		--color-link: #{variables.$color-white};
		--color-hover: #{variables.$color-white};
		text-decoration-color: transparent;
	}
	&__item {
		--icon-offset: 0.1em;
		&.item-icon {
			display: flex;
			align-items: flex-start;
		}
	}
	&__info {
		color: variables.$color-inverse-help;
		font-size: 1.3rem;
	}

	// HOVERS
	.hoverevents & a:hover {
		text-decoration-color: inherit;
	}

	// MQ
	@media (config.$md-down) {
		&__item {
			margin: 0 0 0.4rem;
			line-height: 1.2;
		}
	}
	@media (config.$md-up) {
		gap: 2rem;
		&__person {
			flex: 0 0 30%;
			max-width: 11rem;
		}
		&__item {
			margin: 0 0 0.8rem;
		}
		&__info {
			font-size: 1.4rem;
		}
	}
	@media (config.$xl-up) {
		&__person {
			flex: 0 0 40%;
			max-width: 11rem;
		}
	}
}
