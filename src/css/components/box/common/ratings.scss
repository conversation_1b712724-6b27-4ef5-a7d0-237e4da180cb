@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-ratings {
	display: flex;
	gap: 1.4rem;
	justify-content: center;
	color: variables.$color-inverse-help;
	font-size: 1rem;
	text-align: center;
	&__item {
		margin: 0;
	}
	&__img {
		display: block;
		height: 1.8rem;
		margin: 0 auto 0.4rem;
		object-fit: contain;
	}
	.item-icon {
		--icon-size: 1.4rem;
		--gap: 0.4rem;
		--icon-color: #{variables.$color-white};
	}
	b {
		color: variables.$color-white;
	}

	// MQ
	@media (config.$xs-up) {
		gap: 2rem;
		font-size: 1.3rem;
		&__img {
			height: 2rem;
		}
	}
	@media (config.$lg-up) {
		gap: 5rem;
		font-size: 1.5rem;
		&__img {
			height: 2.3rem;
		}
	}
}
