@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-saving {
	display: inline-flex;
	gap: 0 0.4rem;
	flex-wrap: wrap;
	align-items: center;
	margin: 0;
	color: variables.$color-red;
	font-size: 1.2rem;
	.tooltip {
		display: flex;
	}
	.tooltip__btn {
		vertical-align: text-bottom;
	}
	.tooltip__content {
		width: 36rem;
	}

	// MODIF
	&--detail {
		font-size: 1.3rem;
		.tooltip__btn .icon-svg {
			width: 1.5rem;
		}
	}

	// MQ
	@media (config.$md-up) {
		&--detail {
			font-size: 1.4rem;
		}
	}
}
