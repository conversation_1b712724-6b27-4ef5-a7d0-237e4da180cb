@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-delivery {
	--delivery-color-1: #{variables.$color-alert2-light}; // background
	--delivery-color-2: #{variables.$color-peach-300}; // progress color
	--delivery-color-3: #{variables.$color-alert2}; // active progress color
	--delivery-color-4: #{variables.$color-alert2}; // dot color
	$s: &;
	position: relative;
	padding: 1.2rem 2rem;
	border-radius: variables.$border-radius-lg;
	background: var(--delivery-color-1);
	font-size: 1.3rem;
	&__inner {
		max-width: 63rem;
		margin: 0 auto;
	}
	&__progress {
		--progress-bg: var(--delivery-color-2);
		--progress-bg-active: var(--delivery-color-3);
		--progress-point-bd: var(--delivery-color-1);
		--progress-point-bg: var(--delivery-color-4);
		position: relative;
		margin: 0 0 0.4rem;
	}
	&__top {
		margin: 0 0 0.8rem;
		.item-icon {
			--gap: 0.4rem;
			--icon-color: #{variables.$color-text};
		}
	}
	&__bottom {
		display: flex;
		align-items: flex-start;
		padding-right: 7.2rem;
	}
	&__link {
		--icon-size: 1.2rem;
		--gap: 0.4rem;
	}

	// MODIF
	&--free {
		--delivery-color-1: #{variables.$color-status-valid-light};
		--delivery-color-2: transparent;
		--delivery-color-3: #{variables.$color-status-valid};
	}
	&--partly-gift {
		--delivery-color-1: #{variables.$color-status-valid-light};
		--delivery-color-3: #{variables.$color-status-valid};
	}
	&__bottom--separated {
		margin-top: 6rem;
		padding: 0.8rem 0 0;
		border-top: 0.1rem solid rgba(variables.$color-gray-900, 0.1);
	}
	&__bottom--separated:has(.btn) {
		padding-top: 1.2rem;
	}
	&__bottom--empty {
		margin-top: 4.8rem;
		padding: 0;
		border: none;
	}

	// MQ
	@media (config.$md-up) {
		&--basket {
			padding: 2rem;
		}
	}
}
