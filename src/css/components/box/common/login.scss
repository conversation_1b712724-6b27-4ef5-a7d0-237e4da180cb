@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.b-login {
	$s: &;
	&__link {
		--color-link: #{variables.$color-text};
		--color-hover: #{variables.$color-primary};
		position: relative;
		display: flex;
		gap: 0.1rem;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		width: var(--link-w);
		height: var(--link-h);
		padding: 0 0.8rem;
		border-radius: variables.$border-radius-md;
		font-size: 1rem;
		text-decoration: none;
		transition: background-color variables.$t, color variables.$t;
		&::after {
			content: '';
			position: absolute;
			top: 50%;
			right: calc(var(--link-gap) / -2);
			left: calc(var(--link-gap) / -2);
			height: 3rem;
			border: 0.1rem solid variables.$color-tile-light;
			border-width: 0 0.1rem;
			transform: translateY(-50%);
			transition: border-color variables.$t;
			pointer-events: none;
		}
	}
	&__icon {
		width: var(--link-item-icon-size);
	}
	&__notification {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translateX(-1.5rem) translateY(-1.6rem);
	}
	&__name {
		max-width: 100%;
		white-space: nowrap;
		text-overflow: ellipsis;
		overflow: hidden;
	}
	&__box {
		position: absolute;
		top: 5.6rem;
		right: 0.5rem;
		left: 0.5rem;
		z-index: 1;
		display: flex;
		flex-direction: column;
		padding-top: 1.3rem;
		visibility: hidden;
		opacity: 0;
		transform: translateX(-5000px);
		transition: transform 0s variables.$t, opacity variables.$t, visibility variables.$t;
	}
	&__inner {
		max-height: calc(100dvh - 7.5rem);
		padding: 1.7rem 1.2rem;
		border-radius: variables.$border-radius-xl;
		background: variables.$color-white;
		overflow-x: hidden;
		overflow-y: auto;
	}

	// MODIF
	&__link--logged {
		&::before {
			content: '';
			position: absolute;
			top: 100%;
			left: 50%;
			z-index: -1;
			width: 0;
			height: 0;
			border-width: 0 1rem 0.9rem;
			border-style: solid;
			border-color: transparent transparent variables.$color-white;
			visibility: hidden;
			opacity: 0;
			transform: translateX(-50%) translateY(0.4rem);
			transition: opacity variables.$t, visibility variables.$t;
		}
		#{$s}__icon {
			width: 2rem;
		}
	}

	// STATES
	*:is(.is-top, .is-pinned) &.is-open,
	.hoverevents *:is(.is-top, .is-pinned) &.overlay-pseudo.is-hover,
	.no-js.hoverevents &.overlay-pseudo:hover {
		z-index: 2;
		#{$s}__link {
			background-color: #f8f9ff;
		}
		#{$s}__link::before {
			visibility: visible;
			opacity: 1;
		}
		#{$s}__box {
			visibility: visible;
			opacity: 1;
			transform: translateX(0);
			transition: transform 0s 0s, opacity variables.$t, visibility variables.$t;
		}
	}

	// MQ
	@media (config.$sm-up) {
		position: relative;
		&__box {
			top: 100%;
			right: -1rem;
			left: auto;
			width: 32rem;
		}
	}
	@media (min-width: 900px) {
		&__inner {
			max-height: calc(100dvh - 8.7rem);
		}
		&__icon {
			width: 3rem;
		}
		&__box {
			right: 0;
		}
		&__link::after {
			border-left: none;
			border-color: rgba(variables.$color-gray-900, 0.15);
		}

		// STATES
		.is-top > .header--inversed &:not(.is-hover):not(.is-open) &__link {
			--color-link: #{variables.$color-white};
			--color-hover: #{variables.$color-icon-minor};
		}
		.is-pinned &__inner {
			max-height: calc(100dvh - 7.5rem);
		}
	}
}
