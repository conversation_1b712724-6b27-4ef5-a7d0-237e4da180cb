@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-gallery {
	$s: &;
	&__grid {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 0.8rem;
	}
	&__content {
		position: relative;
		align-content: center;
		padding: 2.8rem 2.4rem;
		border-radius: variables.$border-radius-lg;
		background: variables.$color-bg;
	}
	&__imgs {
		display: contents;
	}
	&__link {
		position: relative;
	}
	&__img {
		width: 100%;
		height: 100%;
		border-radius: variables.$border-radius-lg;
		object-fit: cover;
		aspect-ratio: 172/263;
	}
	&__more {
		margin: 1.2rem 0 0;
		.btn {
			--btn-icon-size: 1.5rem;
			--btn-icon-gap: 0.6rem;
		}
	}

	// MQ
	@media (config.$lg-down) {
		&__content {
			grid-column: auto / span 2;
		}
		&__link:nth-child(3) {
			grid-column: auto / span 2;
			#{$s}__img {
				aspect-ratio: 351/263;
			}
		}
	}
	@media (config.$md-up) {
		&__content {
			padding: 6rem;
			border-radius: variables.$border-radius-xl;
		}
		&__img {
			border-radius: variables.$border-radius-xl;
			aspect-ratio: 1/1;
		}
		&__more {
			margin: 2.4rem 0 0;
		}
	}
	@media (config.$lg-up) {
		&__grid {
			grid-template-columns: repeat(3, 1fr);
			gap: 2rem;
			padding: 6rem 0;
		}
		&__content {
			top: -6rem;
			padding: 2.8rem 2.4rem;
		}
		&__img {
			aspect-ratio: 2/1;
		}
		&__link {
			top: 6rem;
		}
		&__more {
			margin: 0;
		}

		// MODIF
		&__link:nth-child(1) {
			top: 0;
			grid-row: auto / span 3;
			#{$s}__img {
				aspect-ratio: 447/798;
			}
		}
		&__link:nth-child(3) {
			top: -6rem;
			grid-row: auto / span 2;
			#{$s}__img {
				aspect-ratio: 447/512;
			}
		}
	}
	@media (config.$xl-up) {
		&__content {
			padding: 6rem;
		}
	}
}
