@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-content {
	--content-spacing: 2rem;
	--figure-spacing: 1.6rem;
	img:not([class]) {
		display: block;
		border-radius: variables.$border-radius-xl;
		aspect-ratio: auto 16/9;
	}
	*:not(figure) > img:not([class]) {
		margin: var(--content-spacing) 0;
	}
	figure,
	.b-quote,
	p:has(> span > .btn) {
		margin: var(--content-spacing) 0;
	}
	&:not(:first-child) *:is(h1, h2, h3, h4, h5, h6) {
		margin-top: var(--content-spacing);
	}

	// MQ
	@media (config.$md-up) {
		--content-spacing: 3.2rem;
		--figure-spacing: 2.8rem;
	}
}
