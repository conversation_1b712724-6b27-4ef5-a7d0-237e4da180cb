@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.b-user-review {
	display: grid;
	grid-template-columns: 5rem 1fr;
	grid-template-rows: 1fr auto auto;
	grid-template-areas:
		'img name'
		'img type'
		'img rating';
	gap: 0.8rem 2.2rem;
	padding: 1.2rem 0;
	font-size: 1.5rem;
	& > * {
		margin: 0;
	}
	&__img {
		grid-area: img;
	}
	&__name {
		grid-area: name;
	}
	&__name &__link {
		color: inherit;
	}
	&__author {
		display: block;
		font-size: 1.4rem;
	}
	&__rating {
		display: flex;
		grid-area: rating;
		gap: 1.8rem;
		align-items: center;
	}
	&__rating &__link {
		--icon-size: 2rem;
		--gap: 0.4rem;
		flex: 0 0 auto;
		font-weight: bold;
	}
	&__stars {
		--gap: 0.1rem;
	}

	// MQ
	@media (config.$sm-up) {
		grid-template-columns: 5rem auto 1fr;
		grid-template-rows: auto 1fr;
		grid-template-areas:
			'img name name'
			'img type rating';
		&__rating {
			justify-content: flex-end;
		}
	}
	@media (config.$md-up) {
		grid-template-columns: 5rem 35% 1fr auto;
		grid-template-rows: 1fr;
		grid-template-areas: 'img name type rating';
		align-items: center;
		&__type {
			font-size: 1.6rem;
		}
	}
	@media (config.$lg-up) {
		grid-template-columns: 5rem 30% 1fr auto;
	}
	@media (config.$xl-up) {
		grid-template-columns: 5rem 39% 1fr auto;
		&__rating {
			gap: 2.8rem;
		}
	}
}
