@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-library {
	&__tools {
		display: flex;
		gap: 0.8rem 2rem;
		flex-wrap: wrap;
		font-size: 1.5rem;
		line-height: calc(23 / 15);
		.item-icon {
			--icon-size: 2rem;
			--gap: 0.4rem;
			letter-spacing: -0.033em;
		}
	}
	&__copy {
		display: flex;
		gap: 0.4rem 0.8rem;
		flex-wrap: wrap;
	}
	&__copied.item-icon {
		--icon-size: 1.3rem;
		--gap: 0.4rem;
		--icon-offset: 0.08em;
		display: none;
	}

	// STATES
	&__copy.is-copied &__copied.item-icon {
		display: flex;
	}
}
