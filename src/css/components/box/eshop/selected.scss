@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-selected {
	@extend %reset-ul;
	color: variables.$color-gray;
	font-size: 1.4rem;
	overflow: hidden;
	li {
		@extend %reset-ul-li;
		display: inline;
		margin-right: 0.8rem;
		.item-icon {
			--icon-size: 1.8rem;
			--gap: 0.6rem;
		}
	}
	.flag {
		vertical-align: baseline;
	}
	a {
		color: inherit;
	}
	&__country-flag {
		position: relative;
		top: -0.2rem;
		width: auto;
		height: 1.3rem;
		border-radius: 0.3rem;
	}

	// MQ
	@media (config.$xxxl-up) {
		position: relative;
		display: flex;
		gap: 0 1.6rem;
		flex-wrap: nowrap;
		align-items: center;
		&::after {
			content: '';
			position: absolute;
			top: 0;
			right: 0;
			bottom: 0;
			width: 2.5rem;
			background: linear-gradient(to right, transparent, variables.$color-bg-light);
		}
		li {
			flex: 0 0 auto;
			margin: 0;
		}
		&__prevent {
			@include mixins.line-clamp(1);
			max-width: 17rem;
		}
	}
}
