@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-deal {
	position: relative;
	display: flex;
	gap: 0.4rem;
	align-items: flex-start;
	margin: 0 0 0.4rem;
	padding: 1.2rem 1.6rem 1.6rem 5.4rem;
	border-radius: variables.$border-radius-lg;
	background: rgba(variables.$color-bg, 0.85);
	font-size: 1.2rem;
	&__icon {
		flex: 0 0 auto;
		width: 3rem;
	}
	&__name {
		display: block;
		font-size: 1.3rem;
	}
	&__btn {
		--btn-padding: 0.4rem 0.8rem;
		.btn__text {
			text-align: left;
		}
	}

	// MQ
	@media (config.$xl-down) {
		flex-direction: column;
		&__icon {
			position: absolute;
			top: 1.2rem;
			left: 1.6rem;
		}
		&__btn {
			--btn-h: 2.9rem;
			--btn-fs: 1.2rem;
		}
	}
	@media (config.$xl-up) {
		gap: 1.2rem;
		align-items: center;
		padding: 1.6rem 2rem 1.6rem 2.8rem;
		border-radius: variables.$border-radius-xl;
		font-size: 1.3rem;
		&__main {
			flex: 1;
		}
		&__name {
			font-size: 1.4rem;
		}
		&__btn {
			--btn-padding: 0.6rem 1.2rem;
			max-width: 14rem;
		}
	}
}
