@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-product-action {
	$s: &;
	padding: 2rem 2rem 2.8rem;
	border: 0.1rem solid variables.$color-tile-light;
	border-radius: variables.$border-radius-xl;
	&__info {
		display: flex;
		gap: 0.4rem 1.6rem;
		flex-direction: column;
		margin-bottom: 1.6rem;
		padding-bottom: 1.6rem;
		border-bottom: 0.1rem solid variables.$color-tile-light;
	}
	&__name {
		flex: 0 0 33.33%;
	}
	&__flags {
		@extend %reset-ul;
		display: flex;
		gap: 0.4rem;
		flex-wrap: wrap;
		li {
			@extend %reset-ul-li;
		}
	}
	&__price-marketing {
		display: flex;
		gap: 1.6rem 2.4rem;
		flex-wrap: wrap;
		align-items: center;
		min-height: 7.9rem;
		margin: 0 0 1.6rem;
	}
	&__price {
		width: 100%;
	}
	&__marketing {
		display: flex;
		gap: 0.2rem;
		flex: 1 0 25rem;
		flex-direction: column;
		max-width: 39rem;
	}
	&__availability {
		display: flex;
		flex-direction: column;
		margin: 0 0 1.6rem;
	}
	&__variants {
		.btn {
			--btn-bdw: 0.1rem;
			--btn-hover-bdc: #{variables.$color-primary};
			--btn-hover-bg: transparent;
		}
		.btn img:first-child {
			height: 7.1rem;
		}
		.btn:has(img) {
			--btn-h: 7.3rem;
		}
		.btn.is-active {
			--btn-bdw: 0.2rem;
		}
		.btn.is-active img:first-child {
			height: 6.9rem;
		}
	}

	// HOVERS
	// .hoverevents &__variants:hover {
	// 	--btn-hover-c: #{variables.$color-link};
	// 	--btn-hover-bdc: #{variables.$color-link};
	// 	--btn-hover-bg: #{rgba(variables.$color-primary, 0.07)};
	// }

	// MODIF
	&__price:has(.price-special):has(.countdown) {
		position: relative;
		padding-top: 1.7rem;
		#{$s}__countdown {
			position: absolute;
			top: 0;
			left: 50%;
			white-space: nowrap;
			transform: translateX(-50%);
		}
	}

	// MQ
	@media (config.$sm-up) {
		&__price {
			flex: 0 0 auto;
			width: auto;
		}
	}
	@media (config.$xl-up) {
		padding: 2.8rem 4rem 4rem;
		&__info {
			flex-direction: row;
			margin-bottom: 2.4rem;
			padding-bottom: 2rem;
		}
		&__flags {
			flex-direction: column;
		}
		&__price-marketing {
			margin: 0 0 2.4rem;
		}
	}
}
