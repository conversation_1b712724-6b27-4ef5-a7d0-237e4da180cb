@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-soldout {
	display: flex;
	gap: 1.2rem;
	flex-direction: column;
	padding: 2rem 2rem 2.8rem;
	border: 0.1rem solid variables.$color-tile;
	border-radius: variables.$border-radius-xl;
	& > * {
		margin: 0;
	}
	&__btn {
		--btn-fs: 1.5rem;
		--btn-h: 5.5rem;
		--btn-padding: 1.4rem 2.8rem 1.2rem;
		--btn-br: #{variables.$border-radius-lg};
	}

	// MQ
	@media (config.$md-up) {
		gap: 2.4rem;
		padding: 2.8rem 4rem;
	}
}
