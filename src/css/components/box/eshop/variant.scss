@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-variant {
	height: 100%;
	padding: 1.2rem 1.2rem 2rem;
	border: 0.2rem solid transparent;
	border-radius: variables.$border-radius-xl;
	color: variables.$color-placeholder;
	font-size: 1.2rem;
	line-height: 1.4;
	&__img {
		margin: 0 0 0.8rem;
	}
	&__title {
		height: 4.2rem;
		margin: 0 0 0.4rem;
		font-family: variables.$font-primary;
		font-size: 1.4rem;
	}
	&__link {
		--color-link: #{variables.$color-text};
		--color-hover: #{variables.$color-primary};
		@include mixins.line-clamp(2);
		text-decoration: none;
	}
	&__price {
		margin: 0;
		b {
			color: variables.$color-text;
			font-size: 1.4rem;
		}
	}
	&__info {
		margin: 0;
	}
	&__availability {
		margin: 0 0 0.8rem;
	}
	&__parameters {
		display: flex;
		gap: 0.3rem;
		flex-direction: column;
		margin: 0 0 1.2rem;
		line-height: 1.6;
		dt {
			flex: 1;
			color: variables.$color-help;
			font-weight: 400;
		}
		dd {
			flex: 0 0 auto;
			margin: 0;
			color: variables.$color-text;
		}
	}
	&__row {
		display: flex;
		gap: 0.8rem;
	}
	&__btns {
		display: flex;
		gap: 0.6rem;
		align-items: flex-start;
		margin: 0;
		.btn {
			--btn-fs: 1.3rem;
			--btn-lh: 1.5;
			--btn-padding: 0.3rem 1rem;
			flex: 1;
		}
		.b-action__icon {
			width: 4.4rem;
		}
	}

	// STATES
	&.is-active {
		border-color: variables.$color-status-valid;
	}

	// MQ
	@media (config.$md-up) {
		padding: 1.2rem 2.4rem 2rem;
		&__title {
			height: 4.4rem;
		}
		&__img,
		&__availablity {
			margin: 0 0 1.2rem;
		}
	}
}
