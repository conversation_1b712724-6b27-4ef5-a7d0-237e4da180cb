@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-course-special {
	margin: 0 0 1.6rem;
	font-size: 1.3rem;
	&__grid {
		--grid-x-spacing: 2rem;
		--grid-y-spacing: 0.4rem;
	}
	&__box {
		display: flex;
		gap: 0.8rem;
		justify-content: space-between;
		align-items: flex-start;
		height: 100%;
		padding: 1.2rem;
		border-radius: variables.$border-radius-lg;
		background: variables.$color-bg;
		b {
			font-size: 1.4rem;
		}
	}
	&__btn {
		--btn-padding: 0.3rem 1rem 0.2rem;
		flex: 0 0 12rem;
		.btn__inner {
			justify-content: space-between;
			width: 100%;
			text-align: left;
		}
	}

	// MQ
	@media (config.$sm-down) {
		&__btn {
			--btn-h: 2.9rem;
			--btn-br: 0.8rem;
		}
	}
	@media (config.$sm-up) {
		font-size: 1.4rem;
		&__box {
			gap: 2rem;
			align-items: center;
			padding: 2rem 2.8rem;
			b {
				font-size: 1.6rem;
			}
		}
		&__btn {
			--btn-padding: 0.5rem 1.2rem 0.3rem;
			flex: 0 0 14rem;
		}
	}
	@media (config.$lg-up) {
		margin: 0 0 2.4rem;
	}
}
