@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-product-content {
	&__content {
		font-size: 1.5rem;
		line-height: calc(20 / 15);
	}
	&__btn {
		margin: 1.2rem 0 0;
	}

	// STATES
	&:not(.is-open) &__wrap {
		@include mixins.line-clamp(7);
	}
	&.is-open &__btn {
		display: none;
	}

	// MQ
	@media (config.$md-up) {
		&__content {
			font-size: 1.6rem;
			line-height: calc(22 / 16);
		}
	}
}
