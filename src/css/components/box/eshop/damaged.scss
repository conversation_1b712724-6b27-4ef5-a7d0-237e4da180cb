@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-damaged {
	position: relative;
	z-index: 1;
	margin: 0 0 -0.1rem;
	padding: 1.5rem 2rem;
	font-size: 1.4rem;
	line-height: normal;
	box-shadow: variables.$box-shadow;
	&::before {
		content: '';
		position: absolute;
		top: 100%;
		left: 2.5rem;
		width: 1.3rem;
		height: 1.3rem;
		border: 0.1rem solid transparent;
		border-color: transparent transparent variables.$color-orange-light variables.$color-orange-light;
		background: variables.$color-orange-light-2;
		transform: rotate(-45deg) translateY(-50%) translateY(-0.2rem);
	}
	&__title {
		--gap: 0.6rem;
		--icon-size: 1.5rem;
		display: flex;
		margin: 0 0 0.2rem;
		line-height: 1.2;
	}

	// MQ
	@media (config.$md-up) {
		font-size: 1.5rem;
		line-height: calc(23 / 15);
		&::before {
			left: 4.5rem;
		}
	}
}
