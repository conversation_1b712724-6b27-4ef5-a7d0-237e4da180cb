/* stylelint-disable selector-id-pattern */
@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-catalog {
	// MQ
	@media (config.$lg-up) {
		display: grid;
		grid-template-columns: 27.8rem 1fr;
		grid-template-rows: auto auto 1fr;
		gap: 0 3.2rem;
		&__filter {
			grid-area: 1/1/4/1;
		}
		&__sort {
			grid-area: 1/2/1/2;
		}
		&__selected {
			grid-area: 2/2/2/2;
		}
		&__main {
			grid-area: 3/2/3/2;
			margin: 1.6rem 0 0;
		}
	}
}
