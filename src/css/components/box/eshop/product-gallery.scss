@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-product-gallery {
	&__main {
		margin: 0 0 1.2rem;
	}
	&__list {
		--grid-x-spacing: 0.6rem;
		--grid-y-spacing: 0rem;
	}
	&__link {
		display: block;
	}
	&__item {
		width: max(calc(12.3rem + var(--grid-x-spacing)), 20%);
	}
	&__helper {
		border-radius: variables.$border-radius-lg;
		overflow: hidden;
	}
	&__nav {
		display: flex;
		gap: 1.2rem;
		justify-content: center;
		align-items: center;
		min-height: 3rem;
		padding: 1rem 1.2rem 0;
		font-size: 1.1rem;
		pointer-events: auto;
	}
	&__total {
		flex: 0 0 auto;
	}
	&__thumb-btn {
		display: block;
		border-radius: variables.$border-radius-lg;
		overflow: hidden;
		img {
			transition: transform variables.$t;
		}
		&::before {
			content: '';
			position: absolute;
			z-index: 1;
			border: 0.2rem solid variables.$color-primary;
			border-radius: variables.$border-radius-lg;
			opacity: 0;
			transition: opacity variables.$t;
			inset: 0;
		}
		.b-video {
			z-index: 0;
		}
	}
	&__soldout {
		position: relative;
		max-width: 44.4rem;
		margin: 0 auto;
		img {
			filter: grayscale(100%);
		}
		.flag {
			--flag-h: 2.4rem;
			--flag-fs: 1.3rem;
			--flag-padding: 0rem 0.8rem;
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
		}
	}

	// STATES
	&__item.is-active &__thumb-btn::before {
		opacity: 1;
	}

	// HOVERS
	.hoverevents &__item:hover img {
		transform: scale3d(1.05, 1.05, 1);
	}
	.hoverevents &__main .b-video:hover img {
		transform: none;
	}

	// MQ
	@media (config.$lg-down) {
		&__list {
			position: relative;
			display: flex;
			flex-wrap: nowrap;
			overflow: hidden;
			overflow-x: auto;
			.js & {
				overflow: visible;
			}
			.grid__cell {
				flex-shrink: 0;
			}
		}
		&__dots {
			padding-right: 0.6rem;
			border-right: 0.1rem solid variables.$color-tile;
			overflow: hidden;
			.embla__dots {
				justify-content: flex-start;
				margin: 0;
			}
		}
		&__thumbs {
			overflow: visible;
		}
	}
	@media (config.$lg-up) {
		&__list {
			--grid-x-spacing: 1.2rem;
			--grid-y-spacing: 1.2rem;
		}
		&__item {
			width: 25%;
		}
		&__nav {
			display: none;
		}
		&__soldout {
			--flag-h: 2.6rem;
			--flag-fs: 1.4rem;
			--flag-padding: 0.2rem 1rem;
		}
	}
	@media (config.$xl-up) {
		&__item {
			width: 20%;
		}
	}
}
