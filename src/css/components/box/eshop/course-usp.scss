@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-course-usp {
	@extend %reset-ul;
	display: flex;
	gap: 0.8rem;
	max-width: 63rem;
	font-weight: bold;
	font-size: 1.2rem;
	line-height: 1.4;
	container-type: inline-size;
	&__box {
		@extend %reset-ul-li;
		display: flex;
		gap: 0.4rem 0.8rem;
		flex: 0 1 33.33%;
		flex-direction: column;
		align-items: flex-start;
		padding: 1rem 0.8rem 0.8rem;
		border-radius: variables.$border-radius-lg;
		background: variables.$color-bg;
	}
	&__icon {
		flex: 0 0 1.9rem;
		color: variables.$color-icon-minor;
	}

	// MQ
	@media (config.$xs-down) {
		&__box {
			flex: 1 1 auto;
		}
	}
	@container (min-width: 630px) {
		font-size: 1.4rem;
		line-height: 1.6;
		&__box {
			flex: 1 1 auto;
			flex-direction: row;
			padding: 1rem 1.4rem;
		}
		&__icon {
			top: 0.4rem;
		}
	}
}
