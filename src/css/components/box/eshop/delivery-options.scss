@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-delivery-options {
	font-size: 1.5rem;
	&__group {
		padding: 2rem;
	}
	&__list {
		@extend %reset-ul;
		display: grid;
		grid-template-columns: max-content auto 1fr auto;
		gap: 1rem;
	}
	&__item {
		@extend %reset-ul-li;
		display: grid;
		grid-template-columns: subgrid;
		grid-column: auto / span 4;
		gap: 0.4rem 1.4rem;
		align-items: center;
	}
	&__img {
		display: flex;
		gap: 0.5rem;
		flex-direction: column;
		width: 4rem;
		img {
			background: variables.$color-white;
			aspect-ratio: 60 / 46;
			object-fit: contain;
		}
		&:has(img:nth-child(2)) img {
			aspect-ratio: 60 / 30;
		}
		&:has(img:nth-child(3)) img {
			height: 2.2rem;
			aspect-ratio: 60 / 22;
		}
	}
	&__desc {
		color: variables.$color-black;
		font-size: 1.2rem;
		&:not(:has(*)) {
			display: none;
		}
	}
	&__availability.availability {
		font-size: inherit;
		&::first-letter {
			text-transform: uppercase;
		}
	}
	&__price {
		text-align: right;
	}

	// MQ
	@media (config.$md-down) {
		&__title {
			grid-column: auto / span 2;
		}
		&__price {
			grid-area: 3/3/2/5;
		}
		&__availability {
			grid-area: 2/1/2/3;
		}
	}
	@media (config.$md-up) {
		&__group {
			padding: 2.3rem 3.8rem 3rem;
		}
		&__list {
			grid-template-columns: max-content auto 1fr repeat(3, max-content);
		}
		&__item {
			grid-column: auto / span 6;
		}
		&__img {
			width: 6rem;
		}
		&__desc {
			grid-area: 2/6/2/1;
			font-size: 1.4rem;
		}
		&__tooltip {
			margin-left: calc(var(--gap) * -1 + 0.8rem);
		}
	}
}
