/* stylelint-disable selector-id-pattern */
@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-filter {
	$s: &;

	// MQ
	@media (config.$lg-down) {
		#snippet--catalogFilter {
			display: flex;
			height: 100%;
		}
		&__box {
			display: grid;
			margin: 0 0 1.2rem;
			text-align: center;
			&::before {
				content: '';
				display: block;
				grid-area: 1/1/4/1;
				margin: 0;
				border-radius: variables.$border-radius-md;
				background: variables.$color-primary-150;
			}
		}
		&__title {
			grid-area: 1/1/1/1;
			margin: 0 0 0.8rem;
			padding: 2rem 2rem 0;
		}
		&__selected {
			grid-area: 2/1/2/1;
			margin: 0 0 1.6rem;
			padding: 0 2rem;
		}
		&__helper {
			display: inline-block;
			grid-area: 3/1/3/1;
			padding: 0 2rem 2rem;
		}
		&__filter {
			position: fixed;
			top: 0;
			right: 0;
			bottom: 0;
			width: min(var(--vw), 33rem);
			background: variables.$color-white;
			text-align: left;
			transform: translateX(100%);
			transition: transform variables.$t;
			overscroll-behavior: none;
		}
		&__btn-wrap {
			display: inline-block;
		}
		&__btn {
			--btn-icon-size: 1.6rem;
			--btn-gap: 0.6rem;
			--btn-icon-offset: 0rem;
		}
		&__sort {
			padding-top: 1.2rem;
		}

		// STATES
		&__helper.is-open {
			position: relative;
			z-index: 10;
			#{$s}__filter {
				transform: translateX(0%);
			}
			#{$s}__btn-wrap {
				position: relative;
				z-index: -2;
			}
		}
	}
	@media (config.$lg-up) {
		display: contents;
		&__box,
		&__helper {
			display: contents;
		}

		&__title,
		&__btn-wrap {
			display: none;
		}
	}
}
