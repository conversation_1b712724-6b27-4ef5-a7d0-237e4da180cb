@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-action {
	--color-link: #{variables.$color-text};
	--color-hover: var(--color-link);
	--color-link-decoration: transparent;
	display: flex;
	gap: 1.2rem;
	align-items: center;
	font-size: 1.4rem;
	text-decoration: none;
	&__icon {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 4rem;
		border: 0.2rem solid variables.$color-bd;
		border-radius: 1.2rem;
		color: variables.$color-text;
		transition: border-color variables.$t;
		aspect-ratio: 1/1;
		.icon-svg {
			width: 2rem;
		}
	}

	// HOVERS
	.hoverevents &:hover &__icon {
		border-color: variables.$color-placeholder;
	}
}
