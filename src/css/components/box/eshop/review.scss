@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-review {
	padding: 1.2rem 0 1.6rem;
	&__date {
		margin: 0;
		color: variables.$color-help;
		font-size: 1.3rem;
	}
	&__rating {
		display: flex;
		gap: 1rem;
		align-items: center;
		margin: 0 0 0.4rem;
		font-weight: bold;
	}
	&__stars {
		--icon-size: 1.6rem;
	}

	// MQ
	@media (config.$md-up) {
		padding: 2.4rem 0 2.8rem;
		&__date {
			margin: 0 0 0.4rem;
			font-size: 1.4rem;
		}
		&__rating {
			margin: 0 0 0.8rem;
		}
	}
}
