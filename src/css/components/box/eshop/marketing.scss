@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-marketing {
	display: flex;
	gap: 0.8rem;
	align-items: center;
	padding: 0.8rem;
	border-radius: 1.2rem;
	background: variables.$color-alert-light;
	font-size: 1.3rem;
	line-height: 1.4;
	&__icon-wrap {
		position: relative;
		flex: 0 0 auto;
		align-content: center;
		width: 4.1rem;
		border-radius: variables.$border-radius-sm;
		background: linear-gradient(88.35deg, variables.$color-yellow-600 0%, #ffe55a 98.5%);
		text-align: center;
		aspect-ratio: 1/1;
	}
	&__icon {
		width: 2.5rem;
	}
	&__selected {
		position: absolute;
		top: -0.2rem;
		left: -0.2rem;
		width: 1.2rem;
		color: variables.$color-black;
	}
	a {
		--color-link: #{variables.$color-gray};
		--color-hover: #{variables.$color-text};
	}
}
