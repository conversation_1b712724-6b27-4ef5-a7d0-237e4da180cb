/* stylelint-disable declaration-no-important */
/* stylelint-disable selector-id-pattern */
@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-book {
	display: inline-flex;
	justify-content: center;
	width: auto !important;
	height: 100%;
	margin: 0 auto;
	background: variables.$color-white;
	overflow: hidden;
	aspect-ratio: 4/3;
	img {
		max-width: 50%;
		background: variables.$color-white;
		pointer-events: auto;
		object-fit: contain;
		aspect-ratio: 2/3;
	}
	.b-modal:has(&) {
		.b-modal__wrapper {
			pointer-events: none;
		}
		.b-modal__content,
		.b-modal__inner {
			background: transparent;
		}
		.b-modal__inner {
			height: 100%;
		}
		.b-modal__close {
			padding: 1.5rem;
			border-radius: 50%;
			background: variables.$color-white;
			pointer-events: auto;
		}
	}
}

#snippet--productPreview {
	height: 100%;
}
