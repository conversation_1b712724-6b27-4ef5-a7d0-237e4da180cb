@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-dates {
	$s: &;
	margin: 0 0 1.6rem;
	container-type: inline-size;
	&__table {
		margin: 0;
		padding: 0 0.8rem;
		line-height: 1.3;
		tbody {
			display: contents;
		}
		tr {
			border-bottom: 0.1rem solid variables.$color-tile;
		}
		tr:last-child {
			border: none;
		}
	}
	&__cell {
		border: none;
		&--place {
			text-overflow: ellipsis;
			overflow: hidden;
		}
		&--buy {
			display: flex;
			align-items: center;
		}
	}
	&__old {
		color: variables.$color-help;
	}
	&__price {
		display: flex;
		gap: 0 1rem;
	}
	&__price &__old {
		font-size: 1.2rem;
	}
	&__time,
	&__address {
		color: variables.$color-help;
		font-size: 1.3rem;
	}
	&__address {
		white-space: nowrap;
	}
	&__price-buy-wrap {
		display: flex;
		gap: 0.4rem;
		flex-direction: column;
		align-items: center;
		align-self: stretch;
	}
	&__up-discount {
		display: flex;
		gap: 0.4rem;
		align-items: center;
		margin-bottom: -1.2rem;
		padding: 0.4rem 0.8rem 1.6rem;
		border-radius: variables.$border-radius-lg variables.$border-radius-lg 0 0;
		background: variables.$color-alert-light;
		font-size: 1.2rem;
		.badge {
			width: 3rem;
			padding: 0;
			.icon-svg {
				width: 2rem;
			}
		}
	}
	&__discount {
		display: flex;
		gap: 1rem;
		align-items: baseline;
	}

	// MODIF
	&__cell:has(&__up) {
		#{$s}__price {
			flex-direction: column;
		}
		#{$s}__discount {
			gap: 0.8rem;
		}
	}

	// MQ
	@media (config.$xs-up) {
		&__discount {
			gap: 1.6rem;
		}
		&__price {
			gap: 0 1.6rem;
		}
	}
	@container (max-width: 749px) {
		&__table {
			font-size: 1.4rem;
			tr {
				display: grid;
				grid-template-columns: auto auto 1fr;
				gap: 0.8rem 1.2rem;
				align-items: center;
				padding: 2rem 0;
			}
		}
		&__cell {
			padding: 0;
			&--date {
				white-space: nowrap;
			}
			&--buy {
				display: flex;
				grid-column: auto / span 3;
				gap: 1.2rem;
				justify-content: space-between;
				align-items: center;
				&::before {
					content: '';
					align-self: stretch;
					order: 2;
					width: 0.1rem;
					background: variables.$color-tile;
				}
			}
		}
		&__price {
			align-items: center;
			&:first-child {
				margin: auto 0;
			}
		}
		&__up {
			display: flex;
			flex: 1;
			flex-direction: column;
			order: 3;
		}

		// MODIF
		&__cell:not(:has(&__up)) {
			#{$s}__price-buy-wrap {
				flex: 1;
				flex-direction: row;
				justify-content: space-between;
				align-items: center;
			}
		}
		&__cell--buy:not(:has(&__up))::before {
			content: none;
		}

		// MQ
		@media (config.$sm-up) {
			&__cell--buy {
				justify-content: space-between;
				&::before {
					content: none;
				}
			}
			&__up {
				flex: 0 0 auto;
			}
		}
	}
	@container (min-width: 750px) {
		margin: 0 0 2.4rem;
		&__table {
			display: grid;
			grid-template-columns: auto auto 1fr auto;
			align-items: center;

			tr {
				display: grid;
				grid-template-columns: subgrid;
				grid-column: auto / span 4;
				align-items: center;
			}
		}
		&__cell {
			padding: 2.4rem 0.8rem;
			&:first-child {
				padding-left: 1.2rem;
			}
			&:last-child {
				padding-right: 1.2rem;
			}
			&--buy {
				gap: 1.6rem;
				justify-content: flex-end;
			}
		}
		&__price {
			flex: 0 0 auto;
			align-items: flex-end;
		}

		&__price-buy-wrap {
			justify-content: center;
			align-items: flex-end;
		}

		// MODIF
		&__cell:not(:has(&__up)) &__btn {
			width: 13rem;
		}
		&__cell:not(:has(&__up)) &__price-buy-wrap {
			gap: 2rem;
			flex-direction: row;
			align-items: center;
		}
	}
	@container (min-width: 1000px) {
		&__table {
			padding: 0 1.6rem;
		}
		&__price-buy-wrap {
			display: contents;
		}
		&__up {
			display: flex;
			flex: 0 0 auto;
			justify-content: flex-end;
			&::before {
				content: '';
				display: inline-block;
				flex: 0 0 auto;
				align-self: center;
				width: 0.1rem;
				height: 3rem;
				background: variables.$color-tile;
			}
		}
		&__up &__btn {
			--btn-fs: 1.3rem;
			--btn-padding: 0.3rem 1.2rem 0.2rem;
			--btn-h: 5rem;
			flex: 0 0 auto;
			width: 13rem;
		}
		&__up-discount {
			gap: 0.8rem;
			flex: 0 0 auto;
			width: 20.4rem;
			margin: 0 -1.2rem 0 1.6rem;
			margin-right: -1.2rem;
			padding: 1rem 2.4rem 1rem 1.2rem;
			border-radius: variables.$border-radius-lg 0 0 variables.$border-radius-lg;
			font-size: 1.3rem;
		}
	}
}
