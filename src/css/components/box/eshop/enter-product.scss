@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-enter-product {
	display: flex;
	gap: 1.8rem;
	justify-content: center;
	align-items: center;
	padding: 2rem variables.$row-main-gutter;
	border-radius: variables.$border-radius-sm;
	background: variables.$color-bg-light-3;
	&__img {
		width: 5rem;
		height: 8.1rem;
	}

	// MQ
	@media (config.$sm-up) {
		gap: 3.2rem;
		padding: 4rem variables.$row-main-gutter;
		&__img {
			width: 6.6rem;
			height: 10.8rem;
		}
	}
}
