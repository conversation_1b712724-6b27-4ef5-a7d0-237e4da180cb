@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-reviews-total {
	display: flex;
	gap: 2rem;
	align-items: center;
	margin: 0 0 0.8rem;
	padding: 1.2rem 2rem;
	border-radius: variables.$border-radius-md;
	background: variables.$color-alert-light;
	line-height: 1.4;
	&__rating {
		padding: 0.4rem 1.2rem;
		color: variables.$color-text-headline;
		font-family: variables.$font-secondary;
		font-size: 2.8rem;
	}
	&__stars {
		--icon-size: 1.6rem;
		margin: 0 0 0.4rem;
	}

	// MQ
	@media (config.$md-up) {
		margin: 0 0 1.2rem;
		padding: 2.2rem;
		border-radius: variables.$border-radius-lg;
		font-size: 1.4rem;
		&__rating {
			padding: 2rem 2.6rem;
			font-size: 3.9rem;
		}
		&__stars {
			--icon-size: 2.4rem;
		}
	}
}
