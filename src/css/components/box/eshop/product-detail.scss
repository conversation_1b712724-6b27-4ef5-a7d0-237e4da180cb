@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-product-detail {
	margin: 0 0 4rem;
	padding: 1.5rem 0 0;
	&__bc {
		margin: 0 0 0.6rem;
	}
	&__title {
		--font-size-mobile: 2.2rem;
		--font-size-desktop: 3.3rem;
		margin: 0 0 0.8rem;
	}
	&__info {
		display: flex;
		gap: 0.4rem 2rem;
		flex-wrap: wrap;
		align-items: center;
		margin: 0 0 1.2rem;
		font-size: 1.3rem;
	}
	&__duration {
		--flag-fs: 1.3rem;
		--flag-padding: 0.2rem 0.9rem;
	}
	&__rating {
		display: flex;
		gap: 0.4rem;
		align-items: center;
	}
	&__gallery {
		position: relative;
		margin: 0 0 1.2rem;
	}
	&__flags {
		position: absolute;
		top: 0.5rem;
		left: -0.5rem;
	}
	&__badge {
		position: absolute;
		top: 1rem;
		right: -1rem;
		width: 6.8rem;
		font-size: 1.1rem;
	}
	&__type + &__badge {
		top: 4.5rem;
	}
	&__content {
		display: flex;
		gap: 2rem;
		flex-direction: column;
		margin: 0 0 0.8rem;
	}
	&__annot {
		margin: 0 0 1.2rem;
		a {
			--color-link: #{variables.$color-help};
			--color-hover: #{variables.$color-text};
		}
	}
	&__usp {
		margin: 0 0 0.8rem;
	}
	&__action {
		display: flex;
		gap: 2.4rem;
		flex-wrap: wrap;
	}

	// MODIF
	&--soldout &__gallery {
		margin: 0 0 1.2rem;
	}

	// MQ
	@media (config.$md-down) {
		&__flags .flag {
			--flag-fs: 1.1rem;
			--flag-h: 2.2rem;
			--flag-padding: 0.3rem 0.8rem;
		}
		&__type {
			--flag-fs: 1.1rem;
			--flag-h: 2.3rem;
			--flag-padding: 0.5rem 0.7rem;
		}
	}
	@media (config.$md-up) {
		&__bc {
			margin: 0 0 1.2rem;
		}
		&__title {
			margin: 0 0 1.6rem;
			line-height: 1.4;
		}
		&__duration {
			--flag-fs: 1.4rem;
			--flag-padding: 0.2rem 0.9rem;
		}
		&__info {
			margin: 0 0 1.6rem;
			font-size: 1.4rem;
		}
		&__rating {
			gap: 1.2rem;
		}
		&__flags {
			position: absolute;
			top: 1rem;
			left: -1rem;
		}
		&__type {
			--flag-padding: 0.8rem 1rem 0.7rem 1.2rem;
		}
		&__annot {
			margin: 0 0 1.6rem;
			font-size: 1.5rem;
		}
		&__usp {
			margin: 0 0 2.4rem;
		}
		&__content {
			gap: 2.4rem;
			margin: 0 0 2.4rem;
		}
		&__gallery {
			margin: 0 0 5.2rem;
		}
	}
	@media (config.$lg-up) {
		display: grid;
		grid-template-columns: 1fr 1fr;
		grid-template-rows: auto auto 1fr;
		gap: 0 4rem;
		margin: 0 0 10rem;
		padding: 7.6rem 0 0;
		&__name {
			grid-area: 1/2/1/2;
		}
		&__gallery {
			grid-area: 1/1/3/1;
		}
		&__content {
			grid-area: 2/2/4/2;
			margin: 0;
		}
	}
	@media (config.$xl-up) {
		grid-template-columns: 1fr 63rem;
	}
	@media (config.$xxxl-up) {
		grid-template-columns: 81fr 63fr;
		gap: 0 10rem;
	}
}
