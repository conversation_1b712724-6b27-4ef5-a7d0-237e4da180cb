@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-author-more {
	--spacing: var(--grid-gutter);
	--cell-size: 50%;
	&__title {
		display: flex;
		gap: 1rem 2rem;
		flex-wrap: wrap;
		align-items: center;
		margin: 0 0 1.6rem;
	}
	&__desc {
		@include mixins.line-clamp(3);
	}
	&__carousel {
		--arrow-position: 0.4rem;
		margin: 0 calc(var(--row-main-gutter) * -1) -1rem;
		padding: 0 var(--row-main-gutter);
		.grid {
			--grid-x-spacing: var(--spacing);
		}
		.grid__cell {
			width: var(--cell-size);
		}
	}

	// MQ
	@media (config.$sm-up) {
		--cell-size: 33.33%;
	}
	@media (config.$md-up) {
		&__carousel {
			--arrow-position: var(--row-main-gutter);
		}
	}
	@media (config.$lg-up) {
		--cell-size: 25%;
	}
	@media (config.$xl-up) {
		--cell-size: 20%;
	}
	@media (config.$xxxl-up) {
		--cell-size: calc(100% / 6);
		&__carousel {
			margin: 0 0 -1rem;
			padding: 0;
		}
	}
	@media (config.$xxxxl-up) {
		&__carousel {
			--arrow-position: -8.6rem;
			overflow: visible;
			.embla__viewport {
				overflow: hidden;
			}
		}
	}
}
