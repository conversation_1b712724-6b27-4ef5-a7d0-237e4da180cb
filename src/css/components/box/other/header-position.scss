@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-header-position {
	margin: 0 0 2rem;
	&__title {
		margin: 0 0 0.8rem;
	}
	&__grid {
		--grid-y-spacing: 0.8rem;
	}
	&__box {
		display: flex;
		gap: 0.8rem 1.2rem;
		flex-wrap: wrap;
		padding: 1.6rem 2rem;
		border-radius: variables.$border-radius-xl;
		background: variables.$color-bg;
		.item-icon {
			--icon-color: #{variables.$color-primary};
			--icon-size: 3rem;
			--gap: 0.4rem;
		}
	}
	&__cell--side {
		width: auto;
	}

	// MQ
	@media (config.$md-up) {
		margin: 0 0 3.2rem;
		&__title {
			margin: 0 0 1.6rem;
		}
		&__grid {
			--grid-x-spacing: 2rem;
			flex-wrap: nowrap;
		}
		&__cell--main {
			flex: 1;
		}
		&__cell--side {
			width: calc(32rem + var(--grid-x-spacing));
		}
	}
	@media (config.$lg-up) {
		&__grid {
			--grid-x-spacing: 5rem;
		}
		&__cell--side {
			width: calc(37rem + var(--grid-x-spacing));
		}
		&__box {
			gap: 0.8rem 2.8rem;
			padding: 3.2rem;
			.item-icon {
				--gap: 1.2rem;
			}
		}
	}
}
