@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-compare-product {
	.btn {
		--btn-icon-size: 1.5rem;
		--btn-gap: 0.6rem;
	}
	&__link {
		--color-link: #{variables.$color-text};
		--color-hover: #{variables.$color-primary};
	}
	&__bottom &__link {
		--color-link: #{variables.$color-text};
		--color-hover: #{variables.$color-primary};
	}

	// MODIF
	@media (config.$md-up) {
		&:not(&--lg) .btn {
			--btn-fs: 1.3rem;
			--btn-h: 3.4rem;
			--btn-padding: 0.3rem 1.4rem 0.2rem;
			--btn-lh: 1.4;
			--btn-icon-size: 1.5rem;
			--btn-gap: 0.4rem;
			--btn-icon-offset: 0;
		}
	}
}
