@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-top {
	margin: 0;
	container-type: inline-size;
	&__inner {
		display: flex;
		gap: 1rem;
		margin: 0;
		padding: 1.6rem;
		border-radius: variables.$border-radius-xl;
		background: variables.$color-bg;
		font-size: 1.3rem;
	}
	&__btn {
		--btn-fs: 1.5rem;
		--btn-h: 5.5rem;
		--btn-padding: 1.4rem 2.8rem 1.2rem;
		--btn-br: #{variables.$border-radius-lg};
		flex: 0 0 auto;
	}
	&__items {
		display: flex;
		gap: 1rem;
	}
	&__item {
		--icon-color: #{variables.$color-icon-minor};
		position: relative;
		align-items: flex-start;
		padding: 1rem 1.4rem;
		padding: 0.6rem 0.8rem 0.8rem;
		font-weight: bold;
		&::after {
			content: '';
			position: absolute;
			background: variables.$color-tile;
		}
		&:last-child::after {
			content: none;
		}
	}
	&__arrow {
		display: none;
	}

	// MQ
	@container (max-width: 599px) {
		&__inner {
			flex-direction: column;
		}
		&__btn {
			align-self: flex-start;
			order: 1;
		}
		&__items {
			flex-direction: column;
		}
		&__item::after {
			right: 0;
			bottom: -0.4rem;
			left: 0;
			height: 0.1rem;
		}
	}
	@container (min-width: 600px) {
		&__inner {
			gap: 2.4rem;
			align-items: center;
			padding: 2rem 2.4rem;
			font-size: 1.4rem;
		}
		&__items {
			gap: 1.7rem;
			flex: 1;
		}
		&__item {
			flex: 0 1 33.33%;
			padding: 0;
			&::after {
				content: none;
			}
		}
	}
	@media (config.$xl-up) {
		&__item {
			padding: 1rem 1.4rem;
			&::after {
				content: '';
				top: 0.6rem;
				right: -0.8rem;
				bottom: 0.6rem;
				width: 0.1rem;
			}
		}
		&__arrow {
			display: block;
			flex: 0 0 auto;
			width: 1.6rem;
			margin-right: -1.6rem;
		}
	}
}
