@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-header-ebook {
	margin: 0 0 2rem;
	&__title {
		margin: 0 0 0.8rem;
	}
	&__holder {
		margin: 0 0 -5rem;
	}
	.grid {
		--grid-x-spacing: 1rem;
	}
	&__img {
		max-width: 49rem;
		margin: 0 auto;
	}
	&__top {
		position: relative;
	}

	// MQ
	@media (config.$md-up) {
		margin: 0 0 3.2rem;
		&__title {
			margin: 0 0 1.6rem;
		}
	}
	@media (config.$lg-up) {
		.grid {
			--grid-x-spacing: 3rem;
		}
		&__holder {
			margin: 0 0 2.4rem;
		}
	}
}
