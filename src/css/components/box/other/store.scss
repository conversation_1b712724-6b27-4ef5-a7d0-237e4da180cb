@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-store {
	height: 100%;
	container-type: inline-size;
	&__inner {
		height: 100%;
		border: 0.1rem solid variables.$color-tile;
		border-radius: variables.$border-radius-lg;
		font-size: 1.4rem;
		overflow: hidden;
	}
	&__main {
		display: flex;
		gap: 0.8rem;
		flex-direction: column;
		padding: 2.4rem 2rem;
		& > * {
			margin: 0;
		}
	}
	&__img {
		margin: 0;
		padding: 0 2rem 2.4rem;
		.img {
			border-radius: variables.$border-radius-md;
			aspect-ratio: 311/150;
		}
	}

	// MQ
	@container (min-width: 550px) {
		&__inner {
			display: flex;
			border-radius: variables.$border-radius-xl;
			font-size: 1.5rem;
		}
		&__main {
			flex: 1;
			padding: 4.4rem;
		}
		&__main:has(+ &__img) {
			align-self: center;
		}
		&__img {
			flex: 0 0 auto;
			width: 50%;
			padding: 0;
			.img {
				border-radius: 0;
			}
		}
	}
}
