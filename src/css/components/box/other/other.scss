@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-other {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	padding: 2.4rem;
	border: 0.1rem solid variables.$color-tile;
	border-radius: variables.$border-radius-xl;
	font-size: 1.5rem;
	overflow: hidden;
	aspect-ratio: 370 / 427;
	&__img {
		width: 100%;
		margin: 0 0 0.8rem;
	}
	&__flag {
		--flag-h: 2.2rem;
		--flag-fs: 1.1rem;
		margin: 0 0 0.8rem;
	}
	&__title {
		margin: 0 0 0.4rem;
	}
	&__link {
		--color-link-decoration: transparent;
		--color-hover: var(--color-link);
	}
	&__annot {
		overflow: hidden;
		mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 1) 70%, rgba(0, 0, 0, 0));
	}

	// MQ
	@media (config.$md-up) {
		padding: 3.2rem;
	}
}
