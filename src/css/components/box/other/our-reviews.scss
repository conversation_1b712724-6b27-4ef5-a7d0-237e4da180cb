@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.b-our-reviews {
	&__box {
		height: 100%;
		padding: 2.2rem 2rem 2.5rem;
	}
	&__count {
		margin: 0 0 1rem;
		color: variables.$color-black;
		font-size: 1.4rem;
	}
	&__rating {
		--gap: 1.5rem;
		--icon-size: 5rem;
		font-weight: 900;
		font-size: 1.9rem;
		line-height: 1.2;
		span.item-icon__icon {
			display: flex;
			justify-content: center;
			align-items: center;
			border: 0.1rem solid variables.$color-bg-light-3;
			border-radius: 50%;
			.icon-svg {
				width: 2.6rem;
				height: 2.6rem;
			}
		}
	}

	// MODIF
	&__box--facebook &__rating .item-icon__icon .icon-svg {
		color: variables.$color-facebook;
	}

	// MQ
	@media (config.$xxxl-down) {
		&__grid {
			--grid-x-spacing: 0.8rem;
			--grid-y-spacing: 0.8rem;
		}
	}
	@media (config.$md-up) {
		&__title {
			text-align: center;
		}
	}
	@media (config.$xl-up) {
		&__box {
			padding: 2.4rem 3rem 3.2rem;
		}
		&__count {
			margin: 0 0 1.5rem;
		}
		&__rating {
			--gap: 1.6rem;
			--icon-size: 6rem;
			font-size: 2.4rem;
		}
	}
}
