@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-header-about {
	display: grid;
	grid-template-rows: 1fr 4rem auto;
	grid-auto-flow: vertical;
	&__bg {
		--bg-width: min(var(--vw), 192rem);
		position: relative;
		left: 50%;
		grid-row: 1 / span 2;
		grid-column: 1;
		width: var(--bg-width);
		max-width: 192rem;
		margin: 0 0 0 calc(var(--bg-width) / -2);
		background: variables.$color-black;
		img {
			width: 100%;
			min-height: 27rem;
			max-height: 78rem;
			object-fit: cover;
			aspect-ratio: 2/1;
		}
		&::after {
			content: '';
			position: absolute;
			right: 0;
			bottom: 0;
			left: 0;
			height: 25rem;
			background: linear-gradient(360deg, rgba(0, 0, 0, 0.7) 15%, rgba(0, 0, 0, 0) 50%);
		}
	}
	&__content {
		display: flex;
		grid-row: 1;
		grid-column: 1;
		flex-direction: column;
		justify-content: flex-end;
		padding: 1rem 0;
		color: variables.$color-white;
	}
	&__title {
		--font-size-mobile: 2.6rem;
		--font-size-desktop: 6rem;
		color: inherit;
	}
	&__bottom {
		position: relative;
		grid-row: 2 / span 2;
		grid-column: 1;
	}
	&__grid {
		--grid-y-spacing: 3.3rem;
	}
	&__cell {
		display: flex;
		&:nth-child(2) {
			&::before {
				content: '';
				position: absolute;
				top: calc(var(--grid-y-spacing) / -2);
				right: 0;
				left: 0;
				height: 0.1rem;
				background: variables.$color-tile;
			}
		}
	}
	&__box {
		padding: 2.4rem 2rem;
		border-radius: variables.$border-radius-xl;
		background: variables.$color-bg;
	}
	&__links {
		padding: 0;
	}

	// MQ
	@media (config.$md-up) {
		&__content {
			padding: 5rem 0;
		}
		&__box {
			padding: 4rem 6rem;
		}
	}
	@media (config.$lg-up) {
		&__grid {
			flex-wrap: nowrap;
		}
		&__cell {
			display: flex;
			&:nth-child(1) {
				flex: 1;
			}
			&:nth-child(2) {
				flex: 0 0 auto;
				width: min(50%, calc(37.8rem + var(--grid-x-spacing)));
				&::before {
					top: 0;
					right: auto;
					bottom: 0;
					left: calc(var(--grid-x-spacing) / -2);
					width: 0.1rem;
					width: 336;
					height: auto;
				}
			}
		}
	}
}
