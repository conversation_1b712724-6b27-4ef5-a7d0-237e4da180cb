@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-compare {
	--offset: min(calc(var(--row-main-gutter) * -1), calc((var(--vw) - var(--row-main-width)) / -2 - var(--row-main-gutter)));
	&__group-toggle {
		margin: 0;
	}
	&__group-name {
		--color-link: #{variables.$color-text};
		--icon-size: 1.5rem;
		--gap: 1.2rem;
		--color-hover: #{variables.$color-primary};
		min-width: 26rem;
	}

	// STATES
	&__limiter:not(.is-open) {
		position: relative;
		max-height: 65rem;
		overflow: hidden;
		&::before {
			content: '';
			position: absolute;
			right: 0;
			bottom: 0;
			left: 0;
			z-index: 1;
			height: 1.8rem;
			background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #ffffff 100%);
		}
	}
	&__limiter.is-open + .btn {
		display: none;
	}
	&__limiter + .btn {
		--btn-icon-size: 1.5rem;
		--btn-gap: 0.6rem;
	}
	&__group:not(:has(tr:not(.is-hidden-same):not(.is-hidden))),
	&__table tr:is(.is-hidden, .is-hidden-same),
	&__group-toggle:not(.is-open) + &__table {
		display: none;
	}
	&__group-name.is-open .icon-svg {
		transform: scale(-1);
	}
	&__group-toggle.is-open {
		margin: 0 0 0.8rem;
	}

	// MQ
	@media (config.$md-down) {
		&__table {
			--table-x-padding: 0;
			td {
				padding: 0.4rem 0.8rem;
				border: none;
			}
			tr {
				padding: 1.2rem 0;
				border-bottom: 0.1rem solid variables.$color-tile;
			}
			tr:first-child {
				padding-top: 0;
			}
			tr:last-child {
				border: none;
			}
		}
	}
	@media (config.$md-up) {
		&__inner {
			grid-template-columns: 27rem repeat(var(--products-count), 28rem) 1fr;
		}
		&__toolbar {
			position: relative;
			visibility: hidden;
			opacity: 0;
			transition: opacity variables.$t, visibility variables.$t;
			&::before {
				content: '';
				position: absolute;
				top: 0;
				right: var(--offset);
				bottom: 0;
				left: var(--offset);
				background: variables.$color-white;
				box-shadow: 0 2px 2px 0 rgba(0, 14, 71, 0.04), 0 9px 14px 0 rgba(0, 14, 71, 0.1);
			}
		}
		&__sticky {
			position: sticky;
			left: 0;
			margin-left: var(--offset);
			padding-left: calc(var(--offset) * -1);
			&--mask {
				&::before {
					content: '';
					position: absolute;
					top: 0;
					right: 0;
					bottom: 0;
					left: var(--offset);
					z-index: -1;
					background: variables.$color-white;
					transition: box-shadow variables.$t;
				}
			}
		}
		&__top &__sticky,
		&__toolbar &__sticky {
			&::before {
				right: -1rem;
			}
		}
		&__table &__sticky {
			padding-left: calc(var(--offset) * -1);
			&::after {
				content: '';
				position: absolute;
				top: -0.1rem;
				right: calc(100% + var(--offset));
				bottom: -0.1rem;
				z-index: -1;
				width: calc(var(--offset) * -1);
				background: variables.$color-white;
			}
		}
		&__table {
			--table-x-padding: 1rem;
			td {
				border-width: 0 0 0.1rem;
			}
		}

		// STATES
		&__toolbar.is-sticky-top {
			visibility: visible;
			opacity: 1;
		}
		&__sticky--mask.is-sticky-left {
			&::before {
				box-shadow: 0 2px 2px 0 rgba(0, 14, 71, 0.04), 0 9px 14px 0 rgba(0, 14, 71, 0.1);
			}
		}
	}
}
