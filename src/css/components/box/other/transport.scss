@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.b-transport {
	display: flex;
	gap: 2.5rem 2rem;
	flex-direction: column;
	padding: 2rem;
	border-radius: variables.$border-radius-sm;
	background: variables.$color-bg-light-3;
	& > * {
		margin: 0;
	}
	.item-icon {
		--icon-size: 3rem;
		--gap: 1.2rem;
	}

	// MQ
	@media (config.$md-down) {
		font-size: 1.4rem;
		line-height: calc(19 / 14);
		.item-icon {
			margin-bottom: -1.2rem;
		}
	}
	@media (config.$md-up) {
		padding: 5.2rem;
	}
}
