@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.b-team {
	&__title {
		margin: 0 0 1.5rem;
	}
	&__inner {
		line-height: 1.3;
	}
	&__img {
		width: 7rem;
	}
	&__name {
		display: block;
		font-size: 1.5rem;
	}
	&__position {
		color: variables.$color-black;
		font-size: 1.3rem;
	}

	// MQ
	@media (config.$md-down) {
		&__inner {
			display: flex;
			gap: 1.2rem;
			align-items: center;
		}
	}
	@media (config.$md-up) {
		&__title {
			margin: 0 0 3.2rem;
			text-align: center;
		}
		&__grid {
			--grid-x-spacing: 7.8rem;
			justify-content: center;
		}
		&__inner {
			text-align: center;
		}
		&__img {
			width: 16rem;
			margin: 0 auto 2.4rem;
		}
		&__name {
			font-size: 1.8rem;
		}
		&__position {
			font-size: 1.4rem;
		}
	}
}
