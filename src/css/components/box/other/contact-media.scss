@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-contact-media {
	padding: 2rem 8rem 2rem 2rem;
	border-radius: variables.$border-radius-xl;
	background: variables.$color-bg;
	.grid__cell > & {
		height: 100%;
	}
	&__title {
		margin: 0 0 1.2rem;
	}
	&__person {
		width: 5rem;
	}
	&__phone {
		margin: 0 0 0.8rem;
		font-weight: bold;
	}
	&__phone &__link {
		--color-hover: var(--color-link);
		--color-link-decoration: transparent;
		padding: 0.8rem 1.6rem;
		border-radius: variables.$border-radius-md;
		background: variables.$color-white;
	}

	// MQ
	@media (config.$md-down) {
		&__person {
			position: absolute;
			right: 2rem;
			bottom: 2rem;
		}
	}
	@media (config.$xs-up) {
		&__person {
			width: 7rem;
		}
	}
	@media (config.$md-up) {
		display: flex;
		flex-direction: column;
		justify-content: center;
		padding: 4rem 2rem;
		font-size: 1.5rem;
		text-align: center;
		&__title {
			margin: 0 0 2rem;
		}
		&__person {
			width: 8rem;
			margin: 0 auto 1.4rem;
			.person__img {
				margin-bottom: -1.7rem;
			}
			.bubble {
				padding: 0.2rem 1.1rem 0.3rem;
				font-size: 1.2rem;
				line-height: 1.5;
			}
			.bubble__ear {
				bottom: 1.4rem;
			}
		}
		&__phone {
			margin: 0 0 1.2rem;
		}
		&__phone &__link {
			padding: 1.2rem 2rem;
			border-radius: variables.$border-radius-lg;
		}
	}
}
