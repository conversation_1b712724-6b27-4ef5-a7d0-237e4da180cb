@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.b-discount {
	position: relative;
	font-size: 1.5rem;
	line-height: 1.35;
	overflow: visible;
	&__img {
		margin: 0;
		border-radius: variables.$border-radius-sm variables.$border-radius-sm 0 0;
	}
	&__decor {
		position: absolute;
		top: -5%;
		right: -2%;
		width: 63%;
	}
	&__main {
		padding: 2rem 2rem 1.8rem;
	}
	&__title {
		margin: 0 0 0.8rem;
	}
	&__annot {
		margin: 0 0 1.2rem;
	}
	&__link {
		color: inherit;
		text-decoration: none;
	}
	&__bottom {
		display: flex;
		gap: 1.2rem;
		flex-wrap: wrap;
		justify-content: space-between;
		align-items: center;
		margin: auto 0 0;
	}
	&__countdown {
		color: variables.$color-gray;
		font-size: 1.3rem;
	}

	// MQ
	@media (config.$sm-up) {
		display: flex;
		flex-direction: column;
		height: 100%;
		&__main {
			display: flex;
			flex: 1 1 auto;
			flex-direction: column;
		}
	}
	@media (config.$md-up) {
		font-size: 1.6rem;
		line-height: 1.4;
		&__main {
			padding: 3rem 5rem 4rem;
		}
		&__title {
			margin: 0 0 1.2rem;
		}
		&__annot {
			margin: 0 0 2.4rem;
		}
		&__countdown {
			font-size: 1.5rem;
		}
	}
}
