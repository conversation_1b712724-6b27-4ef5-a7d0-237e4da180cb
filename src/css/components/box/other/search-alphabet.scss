@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.b-search-alphabet {
	&__grid {
		--grid-x-spacing: 3.5rem;
		--grid-y-spacing: 0.8rem;
	}

	// MQ
	@media (config.$lg-up) {
		&__cell--form {
			flex: 0 0 auto;
			width: 100%;
			max-width: 34.5rem;
		}
		&__cell--alphabet {
			flex: 0 0 auto;
			width: calc(100% - 34.5rem);
		}
	}
	@media (config.$xl-up) {
		&__grid {
			flex-wrap: nowrap;
		}
	}
}
