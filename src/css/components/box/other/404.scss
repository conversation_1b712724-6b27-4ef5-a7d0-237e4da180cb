@use 'config';
@use 'base/variables';

.b-404 {
	margin: 0 0 3rem;
	padding: 0 0 4.8rem;
	border-bottom: 0.1rem solid variables.$color-tile;
	&__title {
		margin: 0 0 1.2rem;
	}
	&__content {
		margin: 0 0 1.2rem;
		p {
			margin: 0 0 0.8rem;
		}
	}
	&__content--2 {
		margin: 0 0 0.5rem;
		color: variables.$color-help;
		font-size: 1.3rem;
	}

	// MQ
	@media (config.$lg-down) {
		&__img {
			margin: 0 auto;
		}
		&__btn {
			text-align: center;
		}
	}
	@media (config.$lg-up) {
		margin: 0 0 7rem;
		padding: 8rem 0 18rem;
		&__grid {
			display: grid;
			grid-template-columns: 1fr 1fr;
			gap: 0 2rem;
			align-items: center;
		}
		&__img {
			grid-row: 1 / span 2;
			grid-column: 2;
		}
		&__title {
			margin: 0 0 1.6rem;
		}
		&__content {
			margin: 0 0 1.6rem;
			p {
				margin: 0 0 1rem;
			}
		}

		// MODIF
		&__content--2 {
			margin: 0 0 2.4rem;
			font-size: 1.4rem;
		}
	}
}
