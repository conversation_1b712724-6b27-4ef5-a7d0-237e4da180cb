@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-join {
	// MQ
	@media (config.$md-down) {
		.b-usp__list {
			flex-wrap: nowrap;
		}
		.b-usp__item {
			flex: 1 0 calc(33.33% - 0.8rem);
		}
		.btn {
			--btn-h: 4.4rem;
			--btn-padding: 0.9rem 2.2rem 0.7rem;
		}
	}
	@media (config.$md-up) {
		padding: 6rem variables.$row-main-gutter;
		border: 0.1rem solid variables.$color-tile-light;
		border-radius: variables.$border-radius-xl;
	}
}
