@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.b-store-info {
	&__grid {
		--grid-x-spacing: 1.2rem;
		--grid-y-spacing: var(--grid-x-spacing);
	}
	&__cell {
		&--main,
		&--map,
		&--photo {
			flex: 0 0 100%;
		}
	}
	&__title {
		margin: 0 0 1rem;
	}
	&__contacts {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 1rem;
	}
	&__contact {
		--icon-size: 3rem;
		--gap: 1.2rem;
		--icon-color: #{variables.$color-primary};
		align-items: flex-start;
	}
	&__msg {
		margin: 0 0 1.2rem;
		border-radius: variables.$border-radius-xl;
		.message__emoji {
			align-self: center;
		}
		.item-icon {
			--icon-color: #{variables.$color-icon-minor};
			--icon-size: 2.5rem;
			align-items: flex-start;
		}
	}
	&__map {
		width: 100%;
		height: 24rem;
		border-radius: variables.$border-radius-xl;
	}
	&__img {
		height: 100%;
		img {
			height: 24rem;
			border-radius: variables.$border-radius-xl;
		}
	}

	// MODIF
	&__msg--transport {
		--message-padding-y: 2rem;
		--message-padding-x: 2rem;
		display: flex;
		gap: 0.8rem;
		flex-direction: column;
		font-size: 1.4rem;
		line-height: 1.6;
	}

	// MQ
	@media (config.$md-up) {
		&__grid {
			--grid-x-spacing: 2rem;
		}
		&__cell {
			&--map {
				flex: 1;
			}
			&--photo {
				flex: 0 0 auto;
				width: calc(23rem + var(--grid-x-spacing));
			}
		}
		&__title {
			margin: 0 0 2.4rem;
		}
		&__contacts {
			grid-template-columns: auto 1fr;
			gap: 3.2rem;
			margin: 0 0 2rem;
		}
		&__msg {
			--message-padding-y: 2.3rem;
			margin: 0 0 2rem;
		}
		&__map {
			height: 100%;
		}
		&__img img {
			height: 100%;
			aspect-ratio: 9/16;
		}

		// MODIF
		&__msg--transport {
			--message-padding-y: 3.2rem;
			--message-padding-x: 3.2rem;
			font-size: 1.6rem;
			line-height: 1.6;
		}
	}
	@media (config.$xl-up) {
		&__grid {
			flex-wrap: nowrap;
		}
		&__cell {
			&--main {
				flex: 1;
			}
		}
	}
	@media (config.$xxxl-up) {
		&__main {
			padding-right: 4rem;
		}
	}
}
