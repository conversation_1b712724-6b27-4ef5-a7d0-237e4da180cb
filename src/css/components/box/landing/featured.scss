@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-featured {
	margin: 0 0 8rem;
	&__product {
		position: relative;
		margin: 0 0 -2.4rem;
		.play {
			width: 14.8%;
		}
	}
	&__content {
		position: relative;
		.b-popular__decorations,
		.b-popular__content-inner {
			border-radius: variables.$border-radius-lg;
		}
	}
	&__ear {
		display: none;
	}

	// MQ
	@media (config.$md-up) {
		&__content {
			.b-popular__content-inner {
				padding: 6rem;
			}
		}
	}
	@media (config.$lg-up) {
		display: flex;
		align-items: center;
		margin: 0 0 7rem;
		&__product {
			flex: 1;
			margin: 0 -7rem 0 0;
		}
		&__ear {
			position: absolute;
			bottom: 0;
			left: 1.8rem;
			display: block;
			width: 2.1rem;
			color: #f5f5fa;
			transform: rotate(-90deg);
			transform-origin: bottom left;
		}
		&__content {
			flex: 0 0 auto;
			width: 54.7rem;
		}
	}
	@media (config.$xl-up) {
		padding: 0 4rem;
	}
}
