@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-hero {
	--bg-width: min(var(--vw), 192rem);
	$s: &;
	display: grid;
	grid-template-rows: 1fr 3.6rem auto;
	&__carousel {
		--arrow-position: 0.1rem;
		--dot-bg: #{rgba(variables.$color-white, 0.5)};
		--dot-bg-active: #{variables.$color-white};
		position: relative;
		left: 50%;
		grid-area: 1/1/3/1;
		width: var(--bg-width);
		max-width: 192rem;
		max-height: 78rem;
		margin-left: calc(var(--bg-width) / -2);
		background: variables.$color-black;
		.embla__btn {
			background: transparent;
		}
		.embla__dots {
			position: absolute;
			bottom: 4.5rem;
			left: 50%;
			z-index: 1;
			margin: -0.6rem;
			transform: translateX(-50%);
		}
	}
	&__slide {
		display: flex;
		height: 100%;
	}
	&__bg {
		flex: 0 0 auto;
		width: 100%;
		img {
			width: 100%;
			height: 100%;
			min-height: 20rem;
			max-height: 91rem;
			object-fit: cover;
			aspect-ratio: 3/1;
		}
		&::before {
			content: '';
			position: absolute;
			background: linear-gradient(360deg, rgba(13, 18, 40, 0.4) 0%, rgba(13, 18, 40, 0.2) 42.5%, rgba(13, 18, 40, 0) 100%),
				radial-gradient(
					38.74% 50% at 0% 100%,
					rgba(255, 123, 115, 0.6) 0%,
					rgba(249, 100, 134, 0.4) 28%,
					rgba(255, 123, 115, 0) 100%
				),
				linear-gradient(0deg, rgba(0, 0, 0, 0) 68.73%, rgba(0, 0, 0, 0.6) 100%);
			inset: 0;
		}
	}
	&__content {
		position: relative;
		flex: 0 0 auto;
		width: 100%;
		margin-left: -100%;
		padding: 18rem 0 8.4rem;
		text-align: center;
	}
	&__categories {
		position: relative;
		grid-area: 2/1/4/1;
	}
	&__title {
		--font-size-mobile: 3.7rem;
		margin: 0 0 0.6rem;
		color: variables.$color-white;
		line-height: 1.1;
		.outline {
			--outline-color: #{variables.$color-white};
		}
	}
	&__annot {
		--font-size-mobile: 1.4rem;
		--font-size-desktop: 2.2rem;
		margin: 0 0 1.2rem;
		color: variables.$color-white;
	}
	&__btns {
		display: flex;
		gap: 1.2rem;
		flex-wrap: wrap;
		justify-content: center;
	}
	&__btn {
		--btn-h: 4.4rem;
	}

	// MQ
	@media (config.$sm-down) {
		&__content .row-main {
			--row-main-gutter: 2rem;
		}
	}
	@media (min-width: 900px) {
		&__carousel {
			--arrow-position: 1rem;
			.embla__btn .icon-svg {
				width: 3rem;
			}
			.embla__dots {
				bottom: min(8vw, 14rem);
			}
		}
		&__content {
			align-content: center;
			padding: 29.6rem 0 min(14vw, 26.6rem);
			text-align: left;
		}
		&__title {
			margin: 0 0 2.4rem;
			line-height: 1;
		}
		&__annot {
			margin: 0 0 2.4rem;
		}
		&__btns {
			justify-content: flex-start;
		}
		&__btn {
			--btn-h: 6.5rem;
		}
	}
	@media (config.$lg-up) {
		grid-template-rows: 1fr auto auto;
		&__categories {
			.row-main {
				max-width: calc(182rem + 2 * var(--row-main-gutter));
			}
		}
	}
}
