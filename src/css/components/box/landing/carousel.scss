@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-carousel {
	font-size: 1.5rem;
	line-height: calc(23 / 15);
	&__carousel {
		.img::before {
			padding-top: percentage(calc(386 / 800));
		}
	}
	&__list {
		@extend %reset-ul;
		pointer-events: auto;
	}
	&__item {
		@extend %reset-ul-li;
	}

	// MQ
	@media (config.$md-down) {
		--arrow-position: 0.6rem;
		margin: 0 calc(var(--row-main-gutter) * -1);
		padding: 0 var(--row-main-gutter);
		.embla__viewport {
			position: relative;
			border-radius: variables.$border-radius-sm;
		}
		&__img {
			border-radius: variables.$border-radius-sm;
			overflow: hidden;
		}
		&__list {
			display: none;
		}
		&__grid {
			gap: var(--grid-gutter);
		}
	}
	@media (config.$md-up) {
		display: flex;
		.embla__btn,
		.embla__dots {
			display: none;
		}
		.embla__viewport,
		&__grid,
		&__img,
		&__link {
			height: 100%;
		}
		&__carousel {
			width: calc(
				(variables.$row-main-width - variables.$row-main-gutter) / variables.$grid-columns * 7 - variables.$row-main-gutter
			);
			border-radius: variables.$border-radius-sm 0 0 variables.$border-radius-sm;
			overflow: hidden;
		}
		&__list {
			--grid-x-spacing: 0rem;
			--grid-y-spacing: 0rem;
			width: calc((variables.$row-main-width - variables.$row-main-gutter) / variables.$grid-columns * 2);
			border: 0.1rem solid variables.$color-bg;
			border-radius: 0 variables.$border-radius-sm variables.$border-radius-sm 0;
		}
		&__item {
			position: relative;
			border: 0.1rem solid variables.$color-bg;
			border-width: 0 0 0.1rem;
			&:last-child {
				margin-bottom: -0.1rem;
			}
			&::before,
			&::after {
				position: absolute;
				visibility: hidden;
				opacity: 0;
				transition: opacity variables.$t, visibility variables.$t;
			}
			&::before {
				content: '';
				top: -0.1rem;
				right: -0.1rem;
				bottom: -0.1rem;
				left: -0.1rem;
				background: variables.$color-white;
			}
			&::after {
				content: '';
				top: -0.1rem;
				bottom: -0.1rem;
				left: -0.6rem;
				width: 0.5rem;
				border-radius: variables.$border-radius-sm 0 0 variables.$border-radius-sm;
				background: variables.$color-primary;
			}
		}
		&__link {
			position: relative;
			display: flex;
			align-items: center;
			width: 100%;
			min-height: 5.6rem;
			padding: 0.5rem 2rem;
			text-decoration-color: variables.$color-link;
			transition: text-decoration-color variables.$t, color variables.$t;
			span {
				@include mixins.line-clamp(2);
			}
		}

		// MODIF
		&__item:nth-child(1)::before {
			border-top-right-radius: variables.$border-radius-sm;
		}
		&__item:nth-child(6)::before {
			border-bottom-right-radius: variables.$border-radius-sm;
		}
		&__link--more {
			color: variables.$color-black;
			text-decoration-color: variables.$color-black;
		}

		// STATES
		&__item.is-active::before,
		&__item.is-active::after {
			visibility: visible;
			opacity: 1;
		}

		// HOVER
		&__item.is-active &__link,
		.hoverevents &__link:hover {
			color: variables.$color-text;
			text-decoration-color: transparent;
		}
	}
	@media (config.$lg-up) {
		&__link {
			min-height: 6.34rem;
		}
	}
}
