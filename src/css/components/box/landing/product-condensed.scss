@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-product-condensed {
	border: 0.1rem solid transparent;
	border-radius: variables.$border-radius-xl;
	background: variables.$color-white;
	transition: border-color variables.$t;
	&__img {
		margin: 0;
		padding: 1.2rem;
		img {
			width: 100%;
		}
	}
	&__content {
		margin: 0;
		padding: 0 1.6rem 1.6rem;
	}
	&__flag {
		--flag-fs: 1.1rem;
		--flag-h: 2.2rem;
		margin: 0 0 0.6rem;
	}
	&__title {
		font-weight: bold;
		font-size: 1.3rem;
	}
	&__link {
		--color-link: #{variables.$color-text};
		--color-hover: #{variables.$color-primary};
		text-decoration: none;
	}
	&__tooltip {
		top: -0.1em;
		font-weight: 400;
	}

	// HOVERS
	.hoverevents &:hover {
		border-color: variables.$color-icon-minor;
	}

	// MQ
	@media (config.$md-up) {
		&__img {
			padding: 2rem;
		}
		&__content {
			padding: 0 3.6rem 4.2rem;
		}
		&__title {
			font-size: 1.7rem;
		}
	}
}
