@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-header-dronzone {
	position: relative;
	margin: 0 0 2.8rem;
	padding: clamp(10.5rem, 25vw, 24.8rem) 0 0;
	.grid {
		--grid-y-spacing: 1rem;
	}
	&__bg {
		position: absolute;
		top: 0;
		left: 50%;
		z-index: -1;
		width: var(--vw);
		max-width: 192rem;
		min-height: 40rem;
		transform: translateX(-50%);
		object-fit: cover;
	}
	&__logo {
		margin: 0 auto 0.6rem;
		img {
			width: 18.1rem;
			width: clamp(18.1rem, 45vw, 49.4rem);
		}
	}
	&__annot {
		max-width: 30rem;
		margin: 0 auto;
	}
	&__list {
		@extend %reset-ul;
		display: flex;
		gap: 0.4rem;
		flex-wrap: wrap;
		justify-content: center;
		margin: 0 0 1.2rem;
		li {
			@extend %reset-ul-li;
		}
	}
	&__search {
		--search-height: 4.8rem;
		--search-bd: transparent;
		max-width: 54.5rem;
		margin: 0 auto;
		border-radius: var(--search-height);
		box-shadow: 0 2px 2px 0 rgba(0, 14, 71, 0.04), 0 5px 14px 0 rgba(0, 14, 71, 0.1);
		.f-search__inp-fix {
			--inp-fs: 1.4rem;
		}
	}

	// MQ
	@media (config.$md-down) {
		&__list .btn {
			--btn-h: 3.5rem;
			--btn-fs: 1.3rem;
			--btn-padding: 0.3rem 1.4rem;
			--btn-lh: 1.4;
		}
	}
	@media (config.$md-up) {
		margin: 0 0 3.6rem;
		&__logo {
			margin: 0 0 1.2rem;
		}
		&__annot {
			max-width: calc(
				(variables.$row-main-width - variables.$row-main-gutter) / variables.$grid-columns * 4 - variables.$row-main-gutter
			);
		}
		&__list {
			gap: 0.8rem;
			margin: 0 0 2.4rem;
			padding-top: 0.6rem;
		}
		&__search {
			--search-height: 6.4rem;
		}
	}
}
