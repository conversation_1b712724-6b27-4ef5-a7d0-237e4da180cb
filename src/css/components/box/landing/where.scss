@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-where {
	$s: &;
	display: grid;
	grid-template-columns: repeat(2, minmax(0, 1fr));
	gap: 0.8rem;
	font-size: 1.5rem;
	&__box {
		display: flex;
		gap: 0.8rem;
		flex-direction: column;
		padding: 2.8rem 2.4rem;
		border-radius: variables.$border-radius-xl;
		background: variables.$color-bg;
		& > * {
			margin: 0;
		}
	}
	&__location {
		--icon-size: 3rem;
		--icon-color: #{variables.$color-primary};
		--gap: 2.5rem;
		position: relative;
		&::before {
			content: '';
			position: absolute;
			top: 50%;
			left: calc(var(--gap) / 2 + var(--icon-size));
			width: 0.1rem;
			height: calc(100% - 1.6rem);
			min-height: 3rem;
			background: variables.$color-bd;
			transform: translateY(-50%);
		}
	}
	&__imgs {
		display: contents;
	}
	&__img {
		border-radius: variables.$border-radius-xl;
	}

	// MQ
	@media (config.$md-down) {
		&__box {
			grid-column: auto / span 2;
		}
		&__img {
			width: 100%;
			height: 100%;
			aspect-ratio: 172/263;
			&--3 {
				grid-column: auto / span 2;
				aspect-ratio: 351/263;
			}
		}
	}
	@media (config.$md-up) {
		grid-template-columns: repeat(6, minmax(0, 1fr));
		gap: 2rem;
		font-size: 1.7rem;
		&__box {
			grid-column: auto / span 6;
			gap: 1.6rem;
			padding: 6rem;
		}
		&__location {
			--gap: 4rem;
		}
		@media (config.$xl-down) {
			&__img {
				&--1,
				&--2,
				&--3 {
					grid-column: auto / span 2;
				}
				&--4,
				&--5 {
					grid-column: auto / span 3;
					aspect-ratio: 16/9;
				}
			}
		}
	}
	@media (config.$xl-up) {
		grid-template-columns: repeat(3, minmax(0, 1fr));
		&__box {
			grid-area: 1/1/4/1;
		}
		&__img {
			&--1 {
				grid-area: 4/1/6/1;
			}
			&--2 {
				grid-area: 2/2/7/2;
				height: 100%;
			}
			&--3 {
				grid-area: 3/3/3/3;
				aspect-ratio: 16/9;
			}
			&--4 {
				grid-area: 4/3/4/3;
				aspect-ratio: 16/9;
			}
			&--5 {
				grid-area: 5/3/8/3;
				aspect-ratio: 16/9;
			}
		}
	}
}
