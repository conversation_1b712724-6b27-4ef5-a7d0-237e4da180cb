@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-basket-products {
	// MQ
	@media (config.$md-down) {
		margin-right: calc(var(--row-main-gutter) * -1);
		margin-left: calc(var(--row-main-gutter) * -1);
		padding: 2.4rem var(--row-main-gutter);
	}
	@media (config.$md-up) {
		padding: 7rem;
		border-radius: variables.$border-radius-sm;
		.section__carousel {
			--hover-offset-x: 7rem;
			--arrow-position: -6rem;
		}
	}
	@media (config.$xl-up) {
		.section__carousel {
			--cell-size: calc(100% / 6);
			--arrow-position: -9.8rem;
		}
	}
}
