@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-discounted {
	padding: 2rem 1.6rem;
	border: 0.2rem solid transparent;
	border-radius: variables.$border-radius-lg;
	font-size: 1.3rem;
	&__top {
		display: flex;
		gap: 1.2rem;
		align-items: center;
	}
	&__flags {
		display: flex;
		gap: 0.3rem;
		flex-direction: column;
		align-items: flex-start;
		margin: 0 0 0.8rem;
	}
	&__flag {
		--flag-fs: 0.9rem;
		--flag-h: 1.8rem;
		margin: 0;
	}
	&__name {
		margin: 0;
		font-family: variables.$font-primary;
		font-size: 1.4rem;
	}
	&__link {
		--color-link-decoration: transparent;
		--color-hover: var(--color-link);
	}
	&__img {
		position: relative;
		flex: 0 0 auto;
		width: 10rem;
		border-radius: variables.$border-radius-md;
		overflow: hidden;
	}
	&__check {
		position: absolute;
		top: 50%;
		left: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 4rem;
		border-radius: 50%;
		background: variables.$color-status-valid;
		color: variables.$color-white;
		transform: translate(-50%, -50%);
		aspect-ratio: 1/1;
		.icon-svg {
			width: 1.5rem;
		}
	}
	&__desc {
		margin: 0 0 1.2rem;
		p {
			margin: 0 0 0.4rem;
		}
	}

	&__price-btn {
		display: flex;
		gap: 1.2rem;
		align-items: center;
	}
	&__price {
		flex: 1;
	}
	&__btn {
		flex: 0 0 auto;
		.btn {
			--btn-padding: 0.7rem 2rem 0.6rem;
			--btn-icon-size: 1.5rem;
			--btn-gap: 0.4rem;
		}
	}

	// STATES
	&.is-active {
		border-color: variables.$color-status-valid;
	}

	// MQ
	@media (config.$md-up) {
		padding: 3.2rem;
		border-radius: variables.$border-radius-xl;
		font-size: 1.4rem;
		&__flag {
			--flag-fs: 1.1rem;
			--flag-h: 2.2rem;
		}
		&__name {
			font-size: 1.5rem;
		}
		&__img {
			width: 13rem;
			border-radius: variables.$border-radius-xl;
		}
		&__desc {
			margin: 0 0 1.2rem;
			p {
				margin: 0 0 0.8rem;
			}
		}
		&__duration {
			font-size: 1.3rem;
		}
	}
}
