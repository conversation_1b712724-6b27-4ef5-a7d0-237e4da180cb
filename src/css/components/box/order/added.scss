@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-added {
	display: flex;
	gap: 2rem;
	margin: 0 0 3.2rem;
	padding: 2.4rem 1.6rem;
	border-radius: variables.$border-radius-xl;
	background: variables.$color-status-valid-light;
	&__left {
		flex: 1;
	}
	&__title {
		--font-size-desktop: 3rem;
		--font-size-mobile: 1.8rem;
		margin: 0 0 0.8rem;
		color: variables.$color-status-valid;
		&.item-icon {
			--icon-size: 3rem;
			--gap: 1rem;
		}
	}
	&__product {
		display: flex;
		gap: 1.2rem;
		margin: 0 0 2rem;
		padding: 1.2rem;
		border: 0.1rem solid variables.$color-tile;
		border-radius: variables.$border-radius-md;
		background: variables.$color-white;
		font-size: 1.2rem;
	}
	&__img {
		flex: 0 0 auto;
		width: 7.2rem;
	}
	&__name {
		font-size: 1.4rem;
	}
	&__link {
		--color-link-decoration: transparent;
		--color-hover: var(--color-link);
	}
	&__annot {
		margin: 0 0 0.4rem;
	}
	&__right {
		.b-delivery {
			margin: 0 0 2rem;
		}
		.b-delivery--free,
		.b-delivery--partly-gift {
			border: 0.1rem solid rgba(variables.$color-gray-900, 0.15);
		}
	}
	&__contains {
		margin: 0 0 0.8rem;
	}
	&__count {
		flex: 0 0 auto;
		margin: 0 0 0 auto;
		.icon-svg {
			width: 3rem;
		}
	}
	&__btns {
		display: flex;
		gap: 1rem;
	}
	&__btn {
		--btn-padding: 0.5rem 2.4rem 0.4rem;
		--btn-h: 6.4rem;
		flex: 1;
		&:nth-child(2) {
			--btn-fs: 1.6rem;
		}
	}

	// MQ
	@media (config.$xl-down) {
		&__arrow {
			display: none;
		}
	}
	@media (config.$lg-down) {
		flex-direction: column;
		&__title.item-icon {
			display: flex;
			justify-content: center;
		}
		&__contains {
			text-align: center;
		}
		&__count {
			display: none;
		}
	}
	@media (config.$md-up) {
		padding: 4.8rem 6rem;
		&__title {
			margin: 0 0 1.6rem;
		}
	}
	@media (config.$lg-up) {
		margin: 0 0 4rem;
		&__product {
			gap: 2.4rem;
			align-items: center;
			margin: 0 0 3.2rem;
			padding: 1.2rem 2rem;
			border-radius: variables.$border-radius-xl;
			font-size: 1.4rem;
		}
		&__img {
			width: 12.8rem;
		}
		&__name {
			font-size: 1.6rem;
		}
		&__arrow {
			flex: 0 0 auto;
			width: 4rem;
			color: rgba(variables.$color-black, 0.15);
		}
		&__right {
			flex: 0 0 auto;
			width: 32rem;
		}
		&__contains {
			display: flex;
			gap: 1.6rem;
			align-items: center;
			margin: 0 0 1.6rem;
		}
		&__btn {
			//
		}
	}
	@media (config.$xl-up) {
		gap: 4rem;
		&__right {
			width: 42rem;
		}
	}
}
