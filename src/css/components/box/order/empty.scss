@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-empty {
	margin: 0 0 0.8rem;
	padding: 2.8rem 2rem;
	border: 0.1rem solid variables.$color-tile;
	border-radius: variables.$border-radius-lg;
	&:first-child {
		margin-top: 2.6rem;
	}
	&__holder {
		max-width: 81.2rem;
		margin: 0 auto;
	}
	&__grid {
		display: grid;
		grid-template-columns: 1fr 8rem;
		gap: 0 0.8rem;
		align-items: center;
		margin: 0 0 1.6rem;
		color: variables.$color-help;
	}
	&__title {
		align-self: flex-end;
		margin: 0 0 0.8rem;
		color: inherit;
	}
	&__content {
		grid-column: auto / span 2;
		align-self: flex-start;
	}

	// MQ
	@media (config.$sm-up) {
		&__grid {
			grid-template-columns: 8fr 4fr;
		}
		&__img {
			grid-row: auto / span 2;
		}
		&__content {
			grid-column: auto / span 1;
		}
	}
	@media (config.$md-up) {
		padding: 6.6rem;
		border-radius: variables.$border-radius-xl;
		&:first-child {
			margin-top: 5.2rem;
		}
		&__grid {
			gap: 0 3.2rem;
			margin: 0 0 3.2rem;
			font-size: 1.7rem;
		}
	}
}
