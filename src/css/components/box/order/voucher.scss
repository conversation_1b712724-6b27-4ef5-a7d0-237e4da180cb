@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-voucher {
	font-size: 1.4rem;
	&__toggle {
		text-align: center;
	}
	&__wrap {
		display: none;
		max-width: 32rem;
		margin: 0 auto;
	}
	&__inp-fix.inp-fix {
		--inp-h: 4.4rem;
		--inp-icon-size: 2rem;
		--inp-padding-x: 1.2rem;
		--inp-padding-y: 0.8rem;
		display: flex;
	}
	&__inp {
		flex: 1;
		margin-right: -1rem;
		border-right: none;
		border-top-right-radius: 0;
		border-bottom-right-radius: 0;
	}
	&__code {
		display: flex;
		gap: 1rem;
		align-items: center;
		max-width: 32rem;
		height: 4.4rem;
		margin: 0 auto;
		padding: 1.2rem 1.6rem 1.2rem 1.2rem;
		border: 0.1rem solid variables.$color-bd;
		border-radius: 1.3rem;
		.item-icon {
			--gap: 0.4rem;
		}
	}
	&__remove {
		margin-left: auto;
		font-size: 1.3rem;
	}

	// STATES
	&.is-open &__toggle {
		display: none;
	}
	&.is-open &__wrap {
		display: block;
	}
}
