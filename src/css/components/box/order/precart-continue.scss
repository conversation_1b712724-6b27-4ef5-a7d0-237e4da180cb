@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-precart-continue {
	position: fixed;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 5;
	transition: transform variables.$t;
	&__inner {
		//
	}
	&__btns {
		display: flex;
		gap: 0.8rem;
	}
	&__btn {
		--btn-h: 5.6rem;
		--btn-lh: 1.5;
		&:nth-child(1) {
			--btn-padding: 0.6rem 1.4rem 0.4rem;
			--btn-fs: 1.3rem;
		}
		&:nth-child(2) {
			--btn-padding: 0.6rem 2.8rem 0.4rem;
			--btn-fs: 1.5rem;
		}
	}

	// STATES
	&:not(.is-visible) {
		transform: translateY(100%);
	}

	// MQ
	@media (config.$md-down) {
		--row-main-gutter: 0.4rem;
		&__inner {
			.b-delivery {
				border-radius: variables.$border-radius-lg variables.$border-radius-lg 0 0;
			}
		}
		&__btns {
			justify-content: center;
			padding: 0.8rem;
			background: variables.$color-status-valid-light;
		}
		&__btn {
			flex: 1;
		}
	}
	@media (config.$md-up) {
		&__inner {
			display: flex;
			gap: 2rem;
			justify-content: center;
			align-items: center;
			padding: 1.2rem;
			border-radius: variables.$border-radius-xl variables.$border-radius-xl 0 0;
			background: variables.$color-status-valid-light;
			.b-delivery--free,
			.b-delivery--partly-gift {
				border: 0.1rem solid rgba(variables.$color-gray-900, 0.15);
			}
		}
	}
	@media (config.$lg-up) {
		&__inner {
			gap: 4rem;
		}
		&__btns {
			gap: 1rem;
			flex: 0;
		}
		&__btn {
			&:nth-child(1) {
				flex: 0;
			}
			&:nth-child(2) {
				flex: 0 0 auto;
			}
		}
	}
}
