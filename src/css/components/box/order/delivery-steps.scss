@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-delivery-steps {
	--active-color: #{variables.$color-green};
	$s: &;
	display: flex;
	gap: 0.2rem;
	justify-content: center;
	align-items: center;
	font-size: 1.1rem;
	line-height: calc(14 / 11);
	text-align: center;
	&__step {
		display: inline-flex;
		gap: 1.2rem;
		flex-direction: column;
		align-items: center;
		font-weight: bold;
	}
	&__icon {
		width: 5rem;
		height: 5rem;
		color: var(--active-color);
	}
	&__arrow {
		flex: 0 0 auto;
		width: 1.4rem;
		color: var(--active-color);
	}

	// MODIF
	&--error {
		--active-color: #{variables.$color-orange};
	}

	// STATES
	&__step.is-active {
		#{$s}__icon {
			color: var(--active-color);
		}
	}
	&__step.is-active ~ &__step {
		color: variables.$color-black;
		font-weight: 400;
		#{$s}__icon {
			color: variables.$color-black;
		}
	}
	&__step.is-active + &__arrow ~ &__arrow {
		color: variables.$color-black;
	}

	// MQ
	@media (config.$sm-up) {
		gap: 1.2rem;
	}
	@media (config.$md-up) {
		gap: 1.8rem;
		font-size: 1.5rem;
		line-height: calc(23 / 15);
	}
}
