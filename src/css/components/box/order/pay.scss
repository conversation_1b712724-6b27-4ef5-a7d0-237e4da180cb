@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-pay {
	&__list {
		grid-template-columns: max-content 1fr;
		gap: 0 1.2rem;
		margin: 0;
		dt {
			color: inherit;
			font-weight: bold;
		}
	}

	// MODIF
	&__list:has(img) {
		grid-template-columns: 1fr max-content;
	}

	// MQ
	@media (config.$md-down) {
		&__list dd {
			text-align: right;
		}
	}
	@media (config.$md-up) {
		&__list {
			gap: 0 2.4rem;
		}
	}
}
