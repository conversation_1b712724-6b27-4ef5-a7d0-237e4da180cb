@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-order-steps {
	--gap: 0.4rem;
	--line-width: 1.2rem;
	$s: &;
	&__list {
		@extend %reset-ol;
		counter-reset: item;
		display: flex;
		gap: calc(var(--line-width) + 2 * var(--gap));
		justify-content: center;
	}
	&__item {
		@extend %reset-ol-li;
		position: relative;
		&::before {
			content: '';
			position: absolute;
			top: 50%;
			right: calc(100% + var(--gap));
			width: var(--line-width);
			height: 0.1rem;
			background: variables.$color-bd;
		}
		&:first-child::before {
			content: none;
		}
	}
	&__btn {
		--btn-padding: 0.5rem 1rem 0.4rem;
		--btn-br: var(--btn-h);
		--btn-bg: transparent;
		--btn-bdc: #{variables.$color-bd};
		--btn-c: #{variables.$color-help};
		--btn-lh: 1.4;
		--btn-fs: 1.1rem;
		--btn-h: 4.2rem;
		--btn-hover-c: #{variables.$color-white};
		--btn-hover-bg: #{variables.$color-primary};
		--btn-hover-bdc: #{variables.$color-primary};
		height: 100%;
		.btn__text {
			border-width: 0.1rem;
			font-family: variables.$font-primary;
			font-weight: 400;
			text-align: left;
		}
		.btn__inner::before {
			content: counter(item);
			counter-increment: item;
			position: static;
			display: inline-block;
			font-size: 1.5rem;
		}
	}

	// STATESS
	&__item--w-link {
		&::before {
			background: variables.$color-primary;
		}
		#{$s}__btn {
			--btn-c: #{variables.$color-primary};
			--btn-bdc: #{variables.$color-primary};
			.btn__text {
				font-weight: bold;
			}
		}
	}
	&__item--prev &__btn .btn__inner::before {
		position: absolute;
		opacity: 0;
	}

	// MQ
	@media (config.$xs-up) {
		&__btn {
			--btn-padding: 0.5rem 1.6rem 0.4rem;
		}
	}
	@media (config.$md-up) {
		--gap: 2rem;
		--line-width: 4rem;
		&__btn {
			--btn-h: 4.5rem;
			--btn-fs: 1.5rem;
			--btn-padding: 1rem 2.4rem 0.9rem;
		}
	}
}
