@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-else {
	padding: 1.4rem 1.2rem;
	font-size: 1.3rem;
	&__img {
		position: relative;
		img {
			border-radius: variables.$border-radius-md;
		}
	}
	&__flag {
		--flag-h: 2.2rem;
		--flag-fs: 1.1rem;
		white-space: nowrap;
	}
	&__main {
		display: flex;
		gap: 0.8rem;
		flex-direction: column;
		align-items: center;
		& > * {
			margin: 0;
		}
	}
	&__title {
		font-weight: bold;
	}
	&__link {
		--color-link-decoration: transparent;
		--color-hover: var(--color-link);
	}
	&__price {
		--price-fs: 1.1rem;
		--price-large-fs: 1.5rem;
		margin-top: auto;
	}

	// MQ
	@media (config.$lg-down) {
		text-align: center;
		&__img {
			margin: 0 0 0.8rem;
		}
		&__btn {
			width: 100%;
			.btn {
				width: 100%;
			}
		}
	}
	@media (config.$lg-up) {
		display: flex;
		gap: 2rem;
		padding: 2.4rem 2rem;
		border: 0.1rem solid transparent;
		border-radius: variables.$border-radius-xl;
		font-size: 1.4rem;
		transition: border-color variables.$t;
		&__img {
			flex: 0 0 auto;
			width: 12.7rem;
			img {
				border-radius: variables.$border-radius-lg;
			}
		}
		&__main {
			align-items: flex-start;
		}
		&__btn .btn {
			--btn-fs: 1.3rem;
			--btn-h: 3.4rem;
			--btn-padding: 0.3rem 1.4rem;
			--btn-lh: 1.4;
		}

		// HOVERS
		.hoverevents &:hover {
			border-color: variables.$color-icon-minor;
		}
	}
}
