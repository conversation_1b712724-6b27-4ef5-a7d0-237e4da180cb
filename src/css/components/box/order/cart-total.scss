@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-cart-total {
	--gap: 2rem;
	--padding: 2rem;
	padding: var(--padding);
	border-radius: variables.$border-radius-sm;
	background: variables.$color-bg-light-3;
	&__wrap {
		display: flex;
		gap: var(--gap);
		flex-direction: column;
	}
	&__voucher-gift {
		font-size: 1.4rem;
	}
	&__voucher {
		margin: 0 0 1.2rem;
	}
	&__gifts {
		margin: 0 calc(var(--padding) * -1);
	}
	&__delivery {
		padding-bottom: var(--padding);
		border-bottom: 0.1rem solid variables.$color-bg;
		.b-delivery__text {
			justify-content: center;
		}
	}

	// MQ
	@media (config.$sm-up) {
		@media (config.$lg-down) {
			&__total .b-total__table {
				margin: 0 auto;
			}
		}
	}
	@media (config.$md-up) {
		--gap: 4rem;
		--padding: 4rem;
		&__voucher-gift {
			font-size: 1.5rem;
		}
		&__voucher {
			margin: 0 0 1.6rem;
		}
		&__delivery .b-delivery {
			font-size: 1.5rem;
		}
	}
	@media (config.$lg-up) {
		&__wrap {
			flex-direction: row;
			flex-wrap: wrap;
		}
		&__voucher-gift {
			flex: 0 0 calc(50% - var(--gap) / 1.5);
		}
		&__total {
			flex: 0 0 calc(50% - var(--gap) / 1.5);
			align-content: center;
		}
		&__delivery {
			flex: 1;
			align-content: center;
			order: 1;
			padding: 0;
			border: none;
			.b-delivery {
				width: 100%;
				max-width: 39rem;
				margin: 0 auto;
			}
		}
		&__gifts {
			flex: 1 1 100%;
			order: 2;
			max-width: calc(100% + 2 * var(--padding));
			margin: 0 calc(var(--padding) * -1) calc(var(--padding) * -1);
		}
	}
	@media (config.$xl-up) {
		overflow: hidden;
		&__wrap {
			& > * {
				position: relative;
				&::after {
					content: '';
					position: absolute;
					top: 0;
					bottom: 0;
					left: calc(100% + var(--gap) / 2);
					width: 0.1rem;
					background: variables.$color-bg;
				}
				&:last-child::after {
					content: none;
				}
			}
		}
		&__voucher-gift {
			flex: 0 0 calc(30% - var(--gap) / 1.5);
		}
		&__delivery {
			flex: 0 0 calc(40% - var(--gap) / 1.5);
		}
		&__total {
			flex: 0 0 calc(30% - var(--gap) / 1.5);
			order: 1;
		}
	}
}
