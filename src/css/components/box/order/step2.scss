@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-step2 {
	&__inner {
		max-width: 40rem;
		margin: 0 auto;
		&--logged {
			max-width: 54rem;
		}
	}
	&__inp-group {
		margin: 0 0 2.4rem;
		&.f-open {
			margin: 2rem 0;
		}
		.grid {
			--grid-x-spacing: 1.2rem;
			--grid-y-spacing: 0;
		}
	}
	&__title {
		margin: 0 0 2rem;
	}
	&__title:has(+ .b-order-login) {
		margin: 0 0 0.8rem;
	}
	.b-layout-basket--step2 &__title {
		margin: 0 0 1.2rem;
	}
	&__inp {
		margin: 0 0 1.2rem;
	}
	&__state {
		display: flex;
		gap: 0.6rem;
		align-items: center;
		font-size: 1.4rem;
	}
	&__btn {
		padding-top: 2.64rem;
		.btn {
			--btn-padding: 0.4rem 1.4rem;
			--btn-fs: 1.3rem;
			--btn-lh: 1.4;
			--btn-h: 4.8rem;
		}
	}

	// MQ
	@media (config.$md-up) {
		padding: 5rem variables.$row-main-gutter;
		border: 0.1rem solid variables.$color-tile-light;
		border-radius: variables.$border-radius-xl;
		&__inp-group {
			margin: 0 0 3.2rem;
		}
	}
}
