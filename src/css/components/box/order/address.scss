@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-address {
	--inp-offset-top: 1.2rem;
	--inp-offset-left: 1.5rem;
	position: relative;
	margin: 0 0 -0.1rem;
	border: 0.1rem solid variables.$color-tile;
	border-radius: variables.$border-radius-md;
	font-size: 1.3rem;
	transition: border-color variables.$t;
	&:last-child {
		margin: 0;
	}
	&__label {
		width: 100%;
		padding: 1.4rem 1.6rem 1.4rem 5.2rem;
	}
	&__inner {
		display: flex;
		gap: 1.2rem;
		align-items: flex-start;
		font-size: inherit;
		&.inp-item__text::before {
			top: var(--inp-offset-top);
			left: var(--inp-offset-left);
		}
		&.inp-item__text::after {
			top: calc(var(--inp-offset-top) + 0.55rem);
			left: calc(var(--inp-offset-left) + 0.55rem);
		}
	}
	&__main {
		flex: 1;
	}
	&__name {
		display: block;
		margin: 0 0 0.3rem;
		font-weight: bold;
		font-size: 1.4rem;
	}
	&__change {
		--color-link: #{variables.$color-gray};
		--color-hover: #{variables.$color-text};
		&.item-icon {
			--icon-size: 1.5rem;
			--gap: 0.4rem;
		}
	}

	&__new {
		margin: 0 1.6rem;
		padding: 2.8rem 0;
		border-top: 0.1rem solid variables.$color-tile-light;
	}
	&__new-holder {
		max-width: 40.8rem;
		margin: 0 auto;
	}

	// STATES
	&:has(&__inp:checked) {
		z-index: 1;
		border-color: variables.$color-primary;
	}

	// HOVERS
	.hoverevents &:has(&__inp):hover {
		z-index: 1;
		border-color: variables.$color-primary;
	}

	// MQ
	@media (config.$md-up) {
		--inp-offset-top: 1.9rem;
		--inp-offset-left: 2.7rem;
		font-size: 1.4rem;
		&__label {
			padding: 1.9rem 1.8rem 1.9rem 6.4rem;
		}
		&__name {
			font-size: 1.5rem;
		}
		&__new {
			margin: 0 2.8rem;
			padding: 4.4rem 0;
		}

		// MODIF
		&__new &__title {
			text-align: center;
		}
	}
}
