/* stylelint-disable declaration-block-no-redundant-longhand-properties */
@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-pickup {
	--gap: 1rem;
	--search-filter-height: 6rem;
	--border-width: 0.2rem;
	--border-color: #{variables.$color-bd};
	--border-color-active: #{variables.$color-primary};

	&__inner {
		display: grid;
		gap: var(--gap);
	}
	&__head {
		display: flex;
		gap: 0.5rem var(--gap);
		justify-content: end;
		align-items: center;
		p {
			display: flex;
		}
	}
	&__search-form {
		display: flex;
		flex-direction: column;
		gap: calc(var(--gap) / 2);
		margin: 0;
	}
	&__autocomplete {
		flex: 1;
	}
	&__geolocation {
		align-self: end;
	}

	&__error {
		display: flex;
		gap: 1rem;
		justify-content: space-between;
		color: variables.$color-red;
		& .btn__icon {
			--btn-icon-size: 1.8rem;
		}
	}

	&__points {
		display: flex;
		flex-direction: column;
		/* stylelint-disable-next-line */
		flex-wrap: wrap;
		gap: var(--gap);
		margin: 0;
		li {
			@extend %reset-ol-li;
		}
	}
	&__map {
		position: relative;
		background-color: variables.$color-bg;
	}

	// MQ
	@media (config.$md-down) {
		&__map {
			display: none;
		}
		&__toggle {
			position: fixed;
			right: 0;
			bottom: 0;
			left: 0;
			z-index: 2;
			margin: 0;
			padding: 1rem 2rem;
			border-top: 1px solid variables.$color-bd;
			background-color: variables.$color-white;
		}

		// STATES
		&__map.is-open {
			position: fixed;
			top: 0;
			right: 0;
			bottom: 5rem;
			left: 0;
			z-index: 1;
			display: block;
		}
	}
	@media (config.$sm-up) {
		&__points {
			flex-direction: row;
		}
		&__points li {
			flex: 0 0 calc(50% - (var(--gap) / 2));
		}
	}
	@media (config.$md-up) {
		--gap: 2rem;
		&__toggle {
			display: none;
		}
		&__inner {
			grid-template-rows: auto minmax(0, 1fr);
			grid-template-columns: 2fr 3fr;
			grid-template-areas:
				'search points'
				'list map';
			flex: 1 1 auto;
			height: 100%;
			&.no-types {
				grid-template-areas:
					'search map'
					'list map';
			}
		}
		&__head {
			position: relative;
		}
		&__search {
			grid-area: search;
			align-items: center;
			.inp-fix {
				border: var(--border-width) solid var(--border-color);
			}
		}
		&__loading {
			position: relative;
			flex: 0 0 auto;
			&::after {
				content: '';
				position: absolute;
				top: 50%;
				left: 50%;
				width: 1.8rem;
				height: 1.8rem;
				margin: -0.9rem 0 0 -0.9rem;
				border: 0.2rem solid variables.$color-bd;
				border-top-color: transparent;
				border-radius: 0.9rem;
				opacity: 0;
				visibility: hidden;
				transition: opacity variables.$t, visibility variables.$t;
			}
			.icon-svg {
				color: variables.$color-bd;
				transition: opacity variables.$t, visibility variables.$t;
			}
		}
		&__geo {
			flex: 0 0 auto;
		}
		&__points {
			grid-area: points;
			flex-wrap: nowrap;
			li {
				flex: 1 1 auto;
			}
		}
		&__list {
			grid-area: list;
		}
		&__map {
			display: block;
			grid-area: map;
		}

		// STATES
		&__points:has(li:nth-child(3)) {
			flex-wrap: wrap;
			gap: calc(var(--gap) / 2);
			li {
				flex: 0 0 auto;
				width: calc(50% - (var(--gap) / 4));
			}
		}
		&__search:has(.is-loading) &__loading {
			&::after {
				opacity: 1;
				visibility: visible;
				animation: animation-rotate 0.8s infinite linear;
			}
			.icon-svg {
				opacity: 0;
				visibility: hidden;
			}
		}
	}
	@media (config.$xl-up) {
		&__inner {
			grid-template-columns: 4fr 7fr;
		}
		&__points:has(li:nth-child(3)) li {
			width: calc(33% - (var(--gap) / 4));
		}
	}
}

.c-points {
	$s: &;
	height: 100%;
	border: var(--border-width) solid var(--border-color);
	overflow-x: hidden;
	overflow-y: auto;
	&__last .h4 {
		padding: 1rem 2rem;
		border-bottom: var(--border-width) solid var(--border-color);
	}
	&__item {
		border: 1px solid transparent;
		transition: border-color variables.$t;
	}
	&__divider {
		display: block;
		width: 100%;
		height: var(--border-width);
		background-color: var(--border-color);
		&--big {
			/* stylelint-disable-next-line declaration-no-important */
			display: block !important;
			height: calc(var(--border-width) * 2);
		}
		&:last-child {
			display: none;
		}
	}
	&__head {
		display: flex;
		gap: 1rem;
		align-items: center;
		width: 100%;
		padding: 0.5rem 2rem;
		border: none;
		background-color: transparent;
		font-weight: bold;
		text-align: left;
		cursor: pointer;
	}
	&__arrow {
		flex: 0 0 auto;
		width: 1.2rem;
		height: 1.2rem;
		color: variables.$color-bd;
		transition: transform variables.$t;
	}
	&__name {
		flex: 1 1 auto;
	}
	&__distance {
		color: grey;
		font-weight: normal;
		font-size: 1.1rem;
	}
	&__price {
		flex: 0 0 auto;
	}
	&__img {
		flex: 0 0 auto;
		width: 2.5rem;
		height: 4rem;
		object-fit: contain;
	}
	&__content {
		padding: 0 2rem 2rem 4.2rem;
		.grid {
			box-sizing: border-box;
		}
	}
	&__hours {
		display: grid;
		grid-template-rows: auto;
		grid-template-columns: max-content auto;
		gap: 1rem;
		row-gap: 0.25rem;
		& dd {
			margin-bottom: 0;
		}
	}
	&__hours-item {
		white-space: nowrap;
	}
	&__methods {
		display: flex;
		flex-wrap: wrap;
		gap: 0.5rem 1rem;
		img {
			width: 2.5rem;
			height: 1.8rem;
			object-fit: contain;
		}
	}

	// STATES
	&__item--opened {
		border: 1px solid var(--border-color-active);
		#{$s}__arrow {
			transform: rotate(90deg);
		}
	}
}

.f-point {
	display: flex;
	height: 100%;
	min-height: var(--search-filter-height);
	padding: 0.5rem 1rem 0.5rem 4rem;
	border: var(--border-width) solid var(--border-color);
	font-weight: bold;
	transition: border-color variables.$t;
	&__inp {
		top: 50%;
		left: 1rem;
		z-index: 1;
		width: 2rem;
		height: 2rem;
		opacity: 0;
		transform: translateY(-50%);
	}
	&__inner {
		display: flex;
		gap: 1rem;
		align-items: center;
		width: 100%;
		&::before,
		&::after {
			top: 50%;
			left: 1rem;
			transform: translateY(-50%);
		}
		&::after {
			left: 1.5rem;
		}
	}
	&__img {
		width: 1.8rem;
		height: auto;
		object-fit: contain;
	}
	&__name {
		flex: 1 1 auto;
	}
	&__price {
		flex: 0 0 auto;
	}
	&__fake {
		position: fixed;
		top: 0;
		right: 0;
		left: 0;
		z-index: 102;
		opacity: 0;
	}

	// STATES
	&:has(.inp-item__inp:checked) {
		border-color: var(--border-color-active);
	}
}

.b-pickup-modal {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: 100;
	display: flex;
	justify-content: center;
	align-items: center;

	&__content {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		z-index: 101;
		display: grid;
		grid-template-rows: auto minmax(0, 1fr);
		width: 100%;
		padding: variables.$row-main-gutter;
		background-color: #ffffff;
		overflow-y: auto;
	}

	&__header {
		display: flex;
		justify-content: space-between;
		margin-bottom: 1rem;
	}

	&__bg {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		z-index: 100;
		background-color: rgba(0, 0, 0, 0.3);
	}

	&__close {
		button {
			display: flex;
			color: inherit;
		}
		.icon-svg {
			width: 2rem;
			height: 2rem;
		}
	}

	@media (config.$md-up) {
		&__content {
			position: relative;
			width: calc(100% - 2rem);
			height: calc(100% - 2rem);
		}
	}
}

// Autocomplete
.pac-item {
	display: flex;
	flex-wrap: wrap;
	align-items: center;
}
