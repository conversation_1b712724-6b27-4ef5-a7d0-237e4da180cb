@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-layout-basket {
	$s: &;
	display: grid;
	grid-template-columns: 1fr 1fr;
	margin: 0 0 4.8rem;
	&__main {
		grid-column: auto / span 2;
		margin: 0 0 0.8rem;
	}
	&__total,
	&__voucher,
	&__side {
		grid-column: auto / span 2;
		margin: 0 0 2.4rem;
	}
	&__prev {
		grid-column: auto / span 2;
		margin: 0 0 4.8rem;
		text-align: center;
	}
	&__next {
		grid-column: auto / span 2;
		margin: 0 0 1.2rem;
		text-align: center;
		.btn {
			--btn-padding: 0.9rem 3.4rem 0.7rem;
			--btn-fs: 1.6rem;
			--btn-br: #{variables.$border-radius-lg};
			--btn-h: 6.5rem;
			--btn-icon-size: 2.5rem;
		}
	}
	&__postponed {
		grid-row: 7;
		grid-column: auto / span 2;
	}

	// MODIF
	&--step1 &__prev,
	&--step1 &__main,
	&--step2 &__prev {
		margin: 0;
	}

	// MQ
	@media (config.$xs-down) {
		&__next .btn {
			--btn-padding: 0.9rem 2.5rem 0.7rem;
			width: 100%;
		}
	}
	@media (config.$md-up) {
		&__prev {
			grid-area: 4/1/4/1;
			align-self: center;
			text-align: left;
		}
		&__next {
			grid-area: 4/2/4/2;
			align-self: center;
			margin: 0 0 4.8rem;
			text-align: right;
		}

		&:has(.f-basket__bubble) &__prev {
			align-self: flex-end;
		}
		&--step1,
		&--step2 {
			#{$s}__prev,
			#{$s}__next {
				margin: 0;
			}
		}
	}
	@media (config.$lg-up) {
		grid-template-columns: auto 1fr auto 37rem;
		grid-template-rows: auto auto auto auto 1fr;
		gap: 0 2rem;
		margin: 0 0 10rem;
		&__main {
			grid-area: 1/1/1/4;
			margin: 0 0 1.2rem;
		}
		&__side {
			grid-area: 1/4/7/4;
			align-self: flex-start;
			margin: 0;
		}
		&__total {
			grid-area: 2/1/2/4;
		}
		&__voucher {
			grid-area: 3/1/3/4;
			align-self: center;
		}
		&__prev {
			grid-area: 4/1/4/1;
		}
		&__next {
			grid-area: 4/3/4/3;
		}
		&__postponed {
			grid-area: 5/1/5/4;
		}

		// MODIF
		&--step1,
		&--step2 {
			grid-template-columns: auto 1fr auto 40.8rem;
		}
		&--step2 &__main {
			margin: 0 0 2.4rem;
		}
	}
	@media (config.$xl-up) {
		grid-template-rows: auto auto auto 1fr;
		gap: 0 6rem;
		&__prev {
			grid-area: 3/1/3/1;
			margin: 0 0 8rem;
		}
		&__voucher {
			grid-area: 3/2/3/2;
			margin: 0 0 8rem;
		}
		&__next {
			grid-area: 3/3/3/3;
			margin: 0 0 8rem;
		}
		&__postponed {
			grid-area: 4/1/4/4;
		}
	}
}
