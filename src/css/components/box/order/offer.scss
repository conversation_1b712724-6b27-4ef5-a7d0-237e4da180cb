@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-offer {
	--box-padding-x: 1.6rem;
	--box-padding-y: 2.4rem;
	--border-width: 0.4rem;
	position: relative;
	margin: 0 0 3.2rem;
	padding: var(--box-padding-y) var(--box-padding-x);
	border: var(--border-width) solid variables.$color-status-invalid-light;
	border-radius: variables.$border-radius-xl;
	&__icon {
		position: absolute;
		top: 0;
		left: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 3.8rem;
		border-radius: 50%;
		background: variables.$color-status-invalid;
		color: variables.$color-white;
		transform: translate(-50%, -50%);
		aspect-ratio: 1/1;
		.icon-svg {
			width: 1.4rem;
		}
	}
	&__divider {
		margin: 2em 0;
		border-width: 0.2rem 0 0;
	}

	// MQ
	@media (config.$md-up) {
		--border-width: 1.2rem;
		--box-padding-x: 6.8rem;
		--box-padding-y: 6.8rem;
		margin: 0 0 8rem;
		&__icon {
			width: 5.2rem;
			.icon-svg {
				width: 1.8rem;
			}
		}
		&__divider {
			margin: 5.2rem 0;
		}
	}
}
