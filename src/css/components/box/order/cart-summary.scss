@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-cart-summary {
	$s: &;
	padding: 2rem;
	border-radius: variables.$border-radius-xl;
	background: variables.$color-bg;
	&__header {
		display: flex;
		gap: 1rem;
		justify-content: center;
		align-items: center;
		margin: 0 0 1rem;
	}
	&__toggle {
		margin: 0 0 0 auto;
		font-size: 1.3rem;
		.item-icon {
			--icon-size: 1.5rem;
			--gap: 0.4rem;
		}
	}
	&__toggle-text:nth-child(2) {
		display: none;
	}
	&__table {
		font-size: 1.3rem;
	}
	&__body {
		display: grid;
		grid-template-columns: auto 1fr auto;
	}
	&__row {
		display: grid;
		grid-template-columns: subgrid;
		grid-template-rows: 1fr auto;
		grid-column: auto / span 3;
		gap: 0 1.2rem;
		& > * {
			padding: 0;
			border: none;
		}
	}
	&__img {
		position: relative;
		grid-row: auto / span 2;
		width: 6.4rem;
		img {
			border-radius: variables.$border-radius-sm;
		}
	}
	&__flag {
		--flag-fs: 1.1rem;
		--flag-h: 2.2rem;
	}
	&__link {
		--color-link: #{variables.$color-black};
		--color-hover: #{variables.$color-primary};
		@include mixins.line-clamp(2);
		font-weight: bold;
		text-decoration: none;
	}
	&__variant {
		grid-column: auto / span 2;
		color: variables.$color-help;
		font-size: 1.2rem;
		line-height: 1.4;
	}
	&__price {
		color: variables.$color-black;
		text-align: right;
	}
	&__extra {
		display: contents;
		font-size: 1.2rem;
		line-height: 1.4;
	}
	&__extra-item {
		display: grid;
		grid-template-columns: subgrid;
		grid-column: 1 / span 3;
		margin: 0;
		padding: 0.6rem 0;
		border-top: 0.1rem solid variables.$color-tile-light;
		&:first-child {
			margin-top: 1rem;
		}
	}
	&__extra &__desc {
		grid-column: auto / span 2;
	}
	&__icon {
		display: block;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 3rem;
		border-radius: variables.$border-radius-sm;
		background: variables.$color-white;
		color: variables.$color-help;
		aspect-ratio: 1/1;
		.icon-svg {
			width: 2rem;
		}
	}
	&__address {
		font-size: 1.2rem;
	}
	&__divider {
		margin: 0 0 1.2rem;
		border-color: variables.$color-tile;
	}
	&__total {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin: 0;
		&--novat {
			font-size: 1.2rem;
		}
	}

	// MODIF
	&__table--main {
		width: auto;
		margin: 0 -1.2rem 1.2rem;
		#{$s}__body {
			gap: 0.2rem;
		}
		#{$s}__row {
			padding: 1.2rem 1.6rem;
			border-radius: variables.$border-radius-md;
			background: variables.$color-white;
		}
	}
	&__table--delivery {
		margin: 0 0 1.2rem;
		color: variables.$color-text;
		font-size: 1.3rem;
		line-height: 1.4;
		#{$s}__body {
			gap: 0.8rem;
		}
		#{$s}__desc,
		#{$s}__price {
			padding-top: 0.6rem;
		}
	}
	&__row--voucher &__desc {
		grid-column: auto / span 2;
	}

	// STATES
	&.is-open &__toggle .icon-svg {
		transform: scale(-1);
	}
	&.is-open &__toggle-text:nth-child(1) {
		display: none;
	}
	&.is-open &__toggle-text:nth-child(2) {
		display: block;
	}

	// MQ
	@media (config.$lg-down) {
		&__box {
			display: none;
		}

		// STATES
		&.is-open &__box {
			display: block;
		}
	}
	@media (config.$lg-up) {
		padding: 2rem 4rem;
		&__header {
			margin: 0 0 1.6rem;
		}
		&__toggle {
			display: none;
		}
		&__table {
			font-size: 1.4rem;
		}
		&__img {
			width: 7.2rem;
		}
		&__variant {
			font-size: 1.3rem;
		}
		&__extra {
			font-size: 1.3rem;
		}
		&__divider {
			margin: 0 0 1.6rem;
		}

		// MODIF
		&__table--main {
			margin: 0 -2rem 1.6rem;
		}
		&__table--delivery {
			margin: 0 0 1.6rem;
		}
	}
}
