// Box
@forward 'components/box/order/added';
@forward 'components/box/order/offer';
@forward 'components/box/order/discounted';
@forward 'components/box/order/order-steps';
@forward 'components/box/order/layout-basket';
@forward 'components/box/order/else';
@forward 'components/box/order/extra';
@forward 'components/box/order/total';
// @forward 'components/box/order/cart-total';
@forward 'components/box/order/voucher';
@forward 'components/box/order/address';
@forward 'components/box/order/cart';
// @forward 'components/box/order/basket-products';
@forward 'components/box/order/cart-summary';
// @forward 'components/box/order/delivery-steps';
// @forward 'components/box/order/pay';
@forward 'components/box/order/thankyou';
@forward 'components/box/order/pickup';
@forward 'components/box/order/empty';
@forward 'components/box/order/precart-continue';
@forward 'components/box/order/point';
@forward 'components/box/order/step2';

// Crossroad
@forward 'components/crossroad/order/discounted';
@forward 'components/crossroad/order/extra-services';
@forward 'components/crossroad/order/else';
// @forward 'components/crossroad/order/gifts';

// Form
@forward 'components/form/order/basket';
@forward 'components/form/order/method';
// @forward 'components/form/order/btns';
// @forward 'components/form/order/order-register';

// Menu
// @forward 'components/menu/order/component';
