@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.f-newsletter {
	$s: &;
	position: relative;
	margin: 0 0 4rem;
	border-radius: variables.$border-radius-xl;
	background: linear-gradient(90deg, #fdf0ed 0%, #f3e5ff 100%);
	font-size: 1.2rem;
	&__inner {
		padding: 2.8rem 2rem;
	}
	&__title {
		--font-size-mobile: 1.6rem;
		--font-size-desktop: 2.2rem;
		margin: 0 0 1.2rem;
	}
	&__inp-wrap {
		display: flex;
		gap: 0.8rem;
		margin: 0 0 1.2rem;
	}
	&__inp {
		flex: 1;
	}

	// MODIF
	&--article {
		margin: 0;
		#{$s}__title {
			--font-size-desktop: 1.8rem;
		}
	}

	// MQ
	@media (config.$md-down) {
		&__inp {
			.inp-fix {
				--inp-padding-y: 0.9rem;
				--inp-h: 4.4rem;
			}
			.inp-fix__icon {
				display: none;
			}
			.inp-fix:has(.inp-fix__icon) .inp-text {
				padding-left: var(--inp-padding-x);
			}
		}

		// MODIF
		&--default,
		&--article {
			#{$s}__title {
				min-height: 6rem;
				padding-right: 10rem;
			}
			#{$s}__img {
				position: absolute;
				top: 2.8rem;
				right: 2rem;
				img {
					width: 9.6rem;
				}
			}
		}
		&--article {
			margin: 0;
		}
		&--ebook {
			#{$s}__title {
				grid-area: 1/1/1/3;
				margin: 0 0 0.6rem;
			}
			#{$s}__inner {
				display: grid;
				grid-template-columns: 1fr 11.6rem;
				gap: 0 1rem;
			}
			#{$s}__main {
				display: contents;
			}
			#{$s}__img {
				margin: 0 0 1em;
				img {
					height: 100%;
					object-fit: contain;
				}
			}
			#{$s}__info {
				grid-area: 2/1/2/1;
			}
			#{$s}__inp-wrap {
				grid-area: 3/1/3/3;
			}
		}
		@media (min-width: 636px) {
			&--ebook {
				#{$s}__inner {
					grid-template-columns: 1fr 19rem;
					padding: 6rem 3.6rem;
				}
				#{$s}__title {
					grid-area: 1/1/1/1;
				}
				#{$s}__img {
					grid-area: 1/2/4/3;
					margin: 0;
				}
				#{$s}__inp-wrap {
					grid-area: 3/1/3/3;
				}
			}
		}
	}
	@media (min-width: 636px) {
		&__inp .inp-fix {
			--inp-h: 5.5rem;
		}
		&__btn {
			--btn-fs: 1.5rem;
			--btn-h: 5.5rem;
			--btn-padding: 1.4rem 2.8rem 1.2rem;
			--btn-br: #{variables.$border-radius-lg};
		}

		// MODIF
		&--ebook &__inp-wrap {
			max-width: 41.5rem;
		}
	}
	@media (config.$md-up) {
		font-size: 1.5rem;
		&__inner {
			display: flex;
			gap: 4rem;
			align-items: center;
			max-width: 91rem;
			margin: 0 auto;
			padding: 5.2rem variables.$row-main-gutter;
		}
		&__main {
			flex: 1;
		}
		&__info a {
			--color-link: #{variables.$color-help};
			--color-hover: #{variables.$color-text};
		}
		&__img {
			flex: 0 0 auto;
		}

		// MODIF
		&--default,
		&--article {
			#{$s}__main {
				text-align: center;
			}
			#{$s}__info {
				font-size: 1.3rem;
			}
		}
		&--article &__inner {
			padding: 4.3rem variables.$row-main-gutter;
		}
		&--ebook {
			#{$s}__inner {
				max-width: 110rem;
				padding: 4.6rem variables.$row-main-gutter;
			}
			#{$s}__title {
				margin: 0 0 0.8rem;
			}
		}
	}
}
