@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.f-review {
	$s: &;
	font-size: 1.5rem;
	& > * {
		margin: 0 0 1.8rem;
	}
	&__list {
		@extend %reset-ul;
	}
	&__item {
		@extend %reset-ul-li;
		position: relative;
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin: 0 0 0.8rem;
		padding: 0 0 0 2.4rem;
		&::before,
		&::after {
			content: '';
			position: absolute;
			top: 50%;
			left: 0;
			width: 1.2rem;
			height: 0.2rem;
			border-radius: 0.2rem;
			background: variables.$color-green;
			transform: translateY(-50%);
		}
		&::after {
			transform: translateY(-50%) rotate(90deg);
		}
	}
	&__inp {
		min-height: 0;
		min-height: 5rem;
		padding: 1rem 1.5rem;
		outline: none;
		font-size: 1.5rem;
		&::placeholder {
			color: variables.$color-text;
		}
	}
	&__remove {
		position: relative;
		flex: 0 0 auto;
		width: 2.6rem;
		height: 2.6rem;
		margin-right: 2rem;
		border: 0.1rem solid variables.$color-bg;
		border-radius: 50%;
		background: variables.$color-white;
		font-size: 0;
		&::before,
		&::after {
			content: '';
			position: absolute;
			top: 50%;
			left: 50%;
			width: 1.2rem;
			height: 0.2rem;
			background: variables.$color-gray;
			transform: translate(-50%, -50%) rotate(45deg);
		}
		&::after {
			transform: translate(-50%, -50%) rotate(-45deg);
		}
	}
	&__bottom {
		display: flex;
		gap: 2rem 4rem;
		justify-content: space-between;
		align-items: center;
		margin: 0;
		padding-top: 2.4rem;
		border-top: 0.1rem solid variables.$color-bg;
	}
	&__btn {
		flex: 0 0 auto;
	}

	// MODIF
	&__item:not(:first-child):not(#{$s}__item--add) {
		padding-left: 3.2rem;
		border-radius: variables.$border-radius-sm;
		background: variables.$color-bg-light;
		#{$s}__inp {
			padding: 1.2rem;
			border: none;
			background: transparent;
		}
		&::before,
		&::after {
			left: 2rem;
		}
	}
	&__item--add {
		padding: 0;
		&::before,
		&::after {
			content: none;
		}
	}
	&__items--negatives &__item {
		&::before {
			background: variables.$color-red;
		}
		&::after {
			content: none;
		}
	}

	// MQ
	@media (config.$sm-down) {
		& > * {
			margin: 0 0 2.4rem;
		}
		&__note {
			text-align: center;
		}
	}
}
