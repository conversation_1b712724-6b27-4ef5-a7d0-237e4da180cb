@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.f-contact {
	padding: 2.4rem 2rem 3rem;
	font-size: 1.4rem;
	&__item {
		margin: 0 0 0.8rem;
	}
	&__bottom {
		padding-top: 0.8rem;
	}
	&__agree {
		margin: 0 0 1.5rem;
	}

	// MQ
	@media (config.$md-down) {
		&__btn {
			margin: 0;
			.btn {
				width: 100%;
			}
		}
	}
	@media (config.$md-up) {
		padding: 5.6rem var(--row-main-gutter) 7rem;
		font-size: 1.5rem;
		&__item {
			margin: 0 0 1.5rem;
		}
		&__bottom {
			display: flex;
			gap: 3.3rem;
			justify-content: flex-end;
			align-items: center;
			padding-top: 2.8rem;
		}
		&__agree,
		&__btn {
			margin: 0;
		}

		// MODIF
		&__item--textarea {
			display: flex;
			flex-direction: column;
			height: 100%;
			.inp-fix {
				flex: 1;
			}
			textarea {
				height: 100%;
			}
		}
	}
	@media (config.$lg-up) {
		padding: 5.6rem 7rem 7rem;
	}
}
