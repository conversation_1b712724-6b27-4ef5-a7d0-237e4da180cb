@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.f-sort {
	$s: &;
	align-items: center;
	&__sort {
		--sort-height: 3.9rem;
		position: relative;
	}
	&__list {
		@extend %reset-ul;
	}
	&__item {
		@extend %reset-ul-li;
	}
	&__total {
		text-wrap: balance;
	}
	&__arrow {
		display: flex;
		width: 1.5rem;
		margin: 0 0 0 auto;
		color: variables.$color-link;
		transition: transform variables.$t;
	}

	// MQ
	@media (config.$xl-down) {
		display: flex;
		justify-content: space-between;
		&__info {
			display: none;
		}
		&__sort {
			display: flex;
			flex: 0 0 16.6rem;
			padding-top: var(--sort-height);
		}
		&__list {
			position: absolute;
			top: 100%;
			right: 0;
			left: 0;
			z-index: 3;
		}
		&__item {
			border: 0.1rem solid variables.$color-bd;
			border-width: 0 0.1rem;
			background: variables.$color-white;
			&:last-child {
				border-width: 0 0.1rem 0.1rem;
				border-radius: 0 0 1.3rem 1.3rem;
			}
		}
		&__link {
			display: flex;
			align-items: center;
			min-height: 3.9rem;
			padding: 0.8rem 1.4rem 0.6rem;
			text-decoration: none;
		}

		// STATES
		&__item.is-active {
			position: absolute;
			right: 0;
			bottom: 100%;
			left: 0;
			border: 0.1rem solid variables.$color-bd;
			border-radius: 1.3rem;
		}
		&__sort.is-open &__item.is-active {
			border-radius: 1.3rem 1.3rem 0 0;
			border-bottom-color: transparent;
		}
		&__sort.is-open &__arrow {
			transform: scale(-1);
		}
		&__sort:not(.is-open) &__item:not(.is-active) {
			display: none;
		}
	}
	@media (config.$xl-up) {
		display: grid;
		grid-template-columns: minmax(0, 1fr) max-content minmax(0, 1fr);
		padding: 1.2rem 3.2rem;
		border-radius: variables.$border-radius-xl;
		background: variables.$color-bg;
		font-size: 1.4rem;
		&__total {
			grid-area: 1/1/1/1;
			font-size: 1.3rem;
		}
		&__sort {
			grid-area: 1/2/1/2;
		}
		&__sort:has(&__list--btns) {
			#{$s}__list {
				display: flex;
				gap: 1.2rem;
				justify-content: center;
			}
			#{$s}__link {
				--color-link: #{variables.$color-text};
				--color-hover: #{variables.$color-primary};
				display: flex;
				align-items: center;
				min-height: 4.4rem;
				padding: 1rem 2.2rem 0.7rem;
				border: 0.2rem solid transparent;
				border-radius: variables.$border-radius-md;
				font-family: variables.$font-secondary;
				font-weight: 600;
				text-decoration: none;
				transition: color variables.$t, border-color variables.$t;
			}
			#{$s}__arrow {
				display: none;
			}
		}
		&__sort:has(&__list--select) {
			padding-top: var(--sort-height);
			#{$s}__list {
				position: absolute;
				top: 100%;
				right: 0;
				left: 0;
				z-index: 3;
			}
			#{$s}__item {
				border: 0.1rem solid variables.$color-bd;
				border-width: 0 0.1rem;
				background: variables.$color-white;
				&:last-child {
					border-width: 0 0.1rem 0.1rem;
					border-radius: 0 0 1.3rem 1.3rem;
				}
			}
			#{$s}__link {
				display: flex;
				align-items: center;
				min-height: 3.9rem;
				padding: 0.8rem 1.4rem 0.6rem;
				text-decoration: none;
			}

			// STATES
			#{$s}__item.is-active {
				position: absolute;
				right: 0;
				bottom: 100%;
				left: 0;
				border: 0.1rem solid variables.$color-bd;
				border-radius: 1.3rem;
			}
			&.is-open #{$s}__item.is-active {
				border-radius: 1.3rem 1.3rem 0 0;
				border-bottom-color: transparent;
			}
			&.is-open #{$s}__arrow {
				transform: scale(-1);
			}
			&:not(.is-open) #{$s}__item:not(.is-active) {
				display: none;
			}
		}

		&__info {
			grid-area: 1/3/1/3;
			text-align: right;
		}

		// STATES
		&__list--btns &__item.is-active &__link {
			border-color: variables.$color-primary;
			color: variables.$color-primary;
		}

		// MODIF
		.b-filters + &--btns {
			border-bottom-left-radius: 0;
			border-bottom-right-radius: 0;
		}

		// HOVERS
		.hoverevents &__list--btns &__item:not(.is-active) &__link:hover {
			border-color: variables.$color-inverse-help;
			color: variables.$color-primary;
		}
	}
}
