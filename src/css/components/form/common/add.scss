@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.f-add {
	display: flex;
	gap: 1.6rem;
	flex-direction: column;
	font-size: 1.3rem;
	& > * {
		margin-bottom: 0;
	}
	&__btns {
		display: flex;
		gap: 0.8rem;
	}
	&__btn--main {
		--btn-icon-size: 2.5rem;
	}

	// MODIF
	&--uncovered &__btn {
		--btn-padding: 1.2rem 1.7rem 1rem;
		flex: 1;
	}

	// MQ
	@media (config.$md-down) {
		&__btn {
			--btn-lh: 1.5;
			--btn-padding: 0.3rem 2rem 0.2rem;
		}
		&__btn--main {
			--btn-fs: 1.5rem;
			--btn-h: 5.5rem;
			--btn-padding: 1.2rem 2.4rem 1rem;
			--btn-br: #{variables.$border-radius-lg};
		}
	}
	@media (config.$sm-down) {
		&__pick {
			text-align: center;
		}
		&--uncovered &__btns {
			flex-direction: column;
		}
	}
	@media (config.$md-up) {
		gap: 2rem;
		font-size: 1.4rem;
		&__btns {
			gap: 0.8rem 1.6rem;
		}
	}
}
