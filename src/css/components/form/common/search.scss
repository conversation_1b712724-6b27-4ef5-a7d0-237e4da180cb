@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.f-search {
	--search-height: 4.4rem;
	--search-bg: #{variables.$color-white};
	--search-bd: #{variables.$color-bd};
	$s: &;
	&__wrap {
		display: flex;
		align-items: center;
	}
	&__inp-fix.inp-fix {
		--inp-h: var(--search-height);
		--inp-fs: 1.4rem;
		--inp-bd: var(--search-bd);
		--inp-padding-x: 2rem;
		--inp-padding-y: 0.9rem;
		flex: 1;
	}
	&__inp {
		border-right: none;
		border-radius: var(--search-height) 0 0 var(--search-height);
		background: var(--search-bg);
		outline: none;
		transition: padding variables.$t, min-height variables.$t, background-color variables.$t, color variables.$t;
		backdrop-filter: blur(10rem);
	}
	&__btn {
		--btn-icon-size: 2.4rem;
		--btn-fs: 0;
		--btn-br: 0 var(--search-height) var(--search-height) 0;
		--btn-h: var(--search-height);
		--btn-hover-bg: #{variables.$color-primary};
		--btn-bdc: var(--search-bd);
		.btn__text {
			border-width: 0.1rem 0.1rem 0.1rem 0;
			backdrop-filter: blur(10rem);
		}
		.btn__text::before {
			content: '';
			position: absolute;
			top: 50%;
			left: 50%;
			width: calc(var(--btn-h) / 3);
			height: calc(var(--btn-h) / 3);
			margin: calc(var(--btn-h) / -6) 0 0 calc(var(--btn-h) / -6);
			border: 0.2rem solid var(--btn-c);
			border-radius: 50%;
			border-top-color: transparent;
			visibility: hidden;
			opacity: 0;
			transition: opacity variables.$t, visibility variables.$t;
		}
		.icon-svg {
			transition: opacity variables.$t, visibility variables.$t;
		}
	}
	&__toggle {
		--color-link: #{variables.$color-text};
		--color-hover: #{variables.$color-primary};
		align-content: center;
		width: var(--link-w);
		height: var(--link-h);
		text-align: center;
	}
	&__icon {
		width: var(--link-item-icon-size);
	}
	&__arrow {
		position: absolute;
		top: calc(100% + 0.4rem);
		left: 1.8rem;
		z-index: -1;
		display: none;
		width: 0;
		height: 0;
		border-width: 0 1rem 0.9rem;
		border-style: solid;
		border-color: transparent transparent variables.$color-white;
	}
	&__before,
	&__searching {
		width: 100%;
	}

	// STATES
	*:is(.is-top, .is-pinned) &:has(&__inp:focus) {
		z-index: 2;
		animation: none;
		#{$s}__before,
		#{$s}__arrow {
			display: block;
		}
	}
	*:is(.is-top, .is-pinned) &:has(&__inp:focus):has(.is-typing),
	*:is(.is-top, .is-pinned) &:has(&__inp:focus):has(.is-loading),
	*:is(.is-top, .is-pinned) &:has(&__results.is-visible):has(.is-typing),
	*:is(.is-top, .is-pinned) &:has(&__results.is-visible):has(.is-loading),
	*:is(.is-top, .is-pinned) &:has(.is-typing),
	*:is(.is-top, .is-pinned) &:has(.is-loading) {
		#{$s}__before,
		#{$s}__results {
			display: none;
		}
		#{$s}__searching,
		#{$s}__arrow {
			display: block;
		}
	}
	*:is(.is-top, .is-pinned) &:has(.is-loading) &__btn,
	.hoverevents *:is(.is-top, .is-pinned) &:has(.is-loading) &__btn:hover {
		position: relative;
		pointer-events: none;
		.btn__text::before {
			visibility: visible;
			opacity: 1;
			animation: animation-rotate 0.8s infinite linear;
		}
		.icon-svg {
			visibility: hidden;
			opacity: 0;
		}
	}
	*:is(.is-top, .is-pinned) &:has(.is-loading) {
		#{$s}__before {
			display: none;
		}
		#{$s}__searching {
			display: block;
		}
	}
	*:is(.is-top, .is-pinned) &:has(&__results.is-visible) {
		#{$s}__before,
		#{$s}__searching {
			display: none;
		}
	}
	&:not(&--header):has(&__inp:not(:focus)) &__btn {
		--btn-bg: var(--search-bg);
		--btn-c: #{variables.$color-link};
	}
	&--header {
		--search-bd: transparent;
	}
	&--header &__inp-fix.inp-fix {
		--inp-fs: 1.5rem;
	}

	// MQ
	@media (max-width: 635px) {
		&__holder {
			position: absolute;
			top: 0;
			right: 0;
			left: 0;
			height: 100dvh;
			background: variables.$color-white;
			overflow-x: hidden;
			overflow-y: scroll;
			visibility: hidden;
			opacity: 0;
			transform: translateX(-5000px);
			transition: transform 0s variables.$t, opacity variables.$t, visibility variables.$t;
			overscroll-behavior: none;
		}
		&--header &__inp-fix.inp-fix {
			--inp-padding-y: 1.6rem;
		}
		&__close {
			--btn-icon-size: 2.4rem;
			--btn-bg: #{variables.$color-primary-150};
			--btn-c: #{variables.$color-text};
			margin-left: 0.5rem;
			padding-left: 0.5rem;
			border-left: 0.1rem solid variables.$color-tile;
		}
		&__before {
			display: block;
		}

		// STATES
		&--header &__wrap {
			position: sticky;
			top: 0;
			z-index: 1;
			padding-right: 0.5rem;
			border-bottom: 0.1rem solid variables.$color-bd;
			background: variables.$color-white;
		}
		&--header &__btn {
			--btn-h: 4.6rem;
			--btn-br: #{variables.$border-radius-md};
		}
		*:is(.is-top, .is-pinned) &.is-open {
			z-index: 2;
			#{$s}__holder {
				visibility: visible;
				opacity: 1;
				transform: translateX(0);
				transition: transform 0s 0s, opacity variables.$t, visibility variables.$t;
			}
		}
		*:is(.is-top, .is-pinned) &:has(&__results *) &__before {
			display: none;
		}
		*:is(.is-top, .is-pinned) &:has(&__results *) &__results {
			display: block;
		}
	}
	@media (min-width: 636px) {
		&__toggle,
		&__close {
			display: none;
		}
		&__before,
		&__searching {
			width: 100%;
			transform: translateX(-50%);
		}

		// STATES
		&--header {
			--search-height: 4.8rem;
		}
		&--header:has(&__inp:not(:focus)) &__btn {
			--btn-bg: var(--search-bg);
			--btn-c: #{variables.$color-link};
		}
		*:is(.is-top, .is-pinned) &:has(&__inp:focus) &__btn {
			--btn-hover-bg: #{variables.$color-primary-hover};
		}

		// MQ
		@media (max-width: 899px) {
			&--header {
				--search-bg: #{variables.$color-primary-150};
			}
		}
	}
	@media (min-width: 900px) {
		position: relative;

		// STATES
		.is-top .header--inversed &--header {
			--search-bg: #{rgba(variables.$color-white, 0.4)};
			#{$s}__inp,
			#{$s}__inp::placeholder {
				color: variables.$color-white;
			}
			#{$s}__btn {
				--btn-c: #{variables.$color-white};
			}
		}
	}
	@media (config.$md-up) {
		&__inp-fix.inp-fix {
			--inp-fs: 1.5rem;
		}
	}
}
