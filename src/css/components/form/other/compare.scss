@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.f-compare {
	&__filter {
		visibility: hidden;
		opacity: 0;
		transform: translateX(-5000px);
		transition: transform 0s variables.$t, opacity variables.$t, visibility variables.$t;
	}
	&__search {
		.inp-fix {
			--inp-h: 4.4rem;
		}
		.inp-text {
			outline: none;
		}
	}
	&__list {
		@extend %reset-ul;
		&--main {
			background: linear-gradient(white 30%, rgba(255, 255, 255, 0)), linear-gradient(rgba(255, 255, 255, 0), white 70%) 0 100%,
				linear-gradient(to bottom, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0)),
				linear-gradient(to top, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0)) 0 100%;
			background-attachment: local, local, scroll, scroll;
			background-color: white;
			background-repeat: no-repeat;
			background-size: 100% 4rem, 100% 4rem, 100% 1rem, 100% 1rem;
		}
		li {
			@extend %reset-ul-li;
		}
		& & {
			padding-left: 1.2rem;
		}
	}

	// STATES
	.is-open &__filter {
		visibility: visible;
		opacity: 1;
		transform: translateX(0);
		transition: transform 0s 0s, opacity variables.$t, visibility variables.$t;
	}
	.is-open &__toggle .inp-select {
		border-bottom-left-radius: 0;
		border-bottom-right-radius: 0;
		background-image: url(variables.$svg-select-reversed);
	}
}
