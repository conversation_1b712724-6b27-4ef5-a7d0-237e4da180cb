@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.f-order-register {
	padding: 2rem;
	background: variables.$color-bg-light;
	&__benefits {
		font-size: 1.3rem;
		line-height: calc(18 / 13);
		ul {
			@extend %reset-ul;
		}
		li {
			@extend %reset-ul-li;
			position: relative;
			padding-left: 1.8rem;
			&::before {
				content: '';
				position: absolute;
				top: 0.6rem;
				left: 0;
				width: 1.2rem;
				height: 0.5rem;
				border: 0.1rem solid variables.$color-green;
				border-width: 0 0 0.1rem 0.1rem;
				transform: rotate(-45deg);
			}
		}
	}

	// MQ
	@media (config.$xxxl-up) {
		&__benefits ul {
			margin-right: -12rem;
		}
	}
}
