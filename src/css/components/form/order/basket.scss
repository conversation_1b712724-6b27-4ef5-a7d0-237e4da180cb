@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.f-basket {
	display: contents;
	&__head {
		display: flex;
		gap: variables.$grid-gutter;
		justify-content: space-between;
		align-items: center;
		margin: 0 0 1.2rem;
		container-type: inline-size;
	}
	&__title {
		--font-size-mobile: 1.8rem;
		margin: 0;
	}
	&__bubble {
		display: block;
		.item-icon {
			--icon-color: #{variables.$color-help};
			--icon-size: 1.5rem;
			--gap: 0.4rem;
			margin: 0 0 1.2rem;
			padding: 0.8rem 1.2rem;
			border-radius: variables.$border-radius-md;
			background: variables.$color-alert-light;
			font-size: 1.3rem;
		}
		& + .btn {
			position: relative;
			&::before {
				content: '';
				position: absolute;
				top: -1.2rem;
				left: 50%;
				border-width: 1rem 1rem 0;
				border-style: solid;
				border-color: variables.$color-alert-light transparent transparent transparent;
				transform: translateX(-50%);
			}
		}
	}

	// MQ
	@media (config.$lg-down) {
		// MODIF
		&__head--center {
			justify-content: center;
		}
	}
}
