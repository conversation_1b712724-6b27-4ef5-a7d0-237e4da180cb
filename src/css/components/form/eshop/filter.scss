@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.f-filter {
	font-size: 1.4rem;
	&__name {
		margin: 0 0 0.4rem;
	}
	&__toggle {
		--color-link: #{variables.$color-text};
		--color-hover: #{variables.$color-text};
		display: flex;
		gap: 0.4rem;
		align-items: center;
		width: 100%;
		padding: 1rem 1.2rem;
		border-radius: variables.$border-radius-lg;
		outline: none;
		font-weight: 700;
		font-size: 1.5rem;
		text-decoration: none;
		transition: background-color variables.$t;
	}
	&__inner {
		padding: 0 1.2rem 1.6rem;
	}
	&__list {
		@extend %reset-ul;
		display: flex;
		gap: 0.8rem;
		flex-direction: column;
	}
	&__item {
		@extend %reset-ul-li;
	}
	&__inp-item {
		--inp-item-fs: inherit;
		width: 100%;
	}
	&__link {
		color: inherit;
		text-decoration-color: transparent;
	}
	&__arrow {
		width: 1.5rem;
		margin-left: auto;
		transition: transform variables.$t;
	}
	&__more {
		margin: 0.8rem 0 0;
		.as-link {
			--icon-offset: -0.05em;
			--icon-size: 1.2rem;
			--color-link-decoration: transparent;
			position: relative;
			padding-left: 2rem;
			&::before,
			&::after {
				content: '';
				position: absolute;
				top: 50%;
				left: 0;
				width: 1.2rem;
				height: 0.2rem;
				border-radius: 0.2rem;
				background: var(--color-link);
				transform: translateY(-50%);
				transition: background-color variables.$t;
			}
			&::after {
				transform: translateY(-50%) rotate(-90deg);
			}
		}
		.item-icon__text:nth-child(2) {
			display: none;
		}
	}
	&__count {
		color: variables.$color-help;
	}
	&__remove {
		--color-link-decoration: transparent;
	}
	&__tooltip {
		--tooltip-icon-size: 1.5rem;
	}

	// STATEs
	.js &__inner {
		display: none;
	}
	.js &__link {
		pointer-events: none;
	}
	&__group.is-open &__arrow {
		transform: scale(-1);
	}
	&__group.is-open &__inner {
		display: block;
	}
	&__group.is-expanded &__item.u-js-hide {
		display: block;
	}
	&__group.is-expanded &__more {
		.as-link {
			&::after {
				transform: translateY(-50%);
			}
		}
		.item-icon__text:nth-child(1) {
			display: none;
		}
		.item-icon__text:nth-child(2) {
			display: block;
		}
	}

	// HOVER
	.hoverevents &__toggle:hover {
		background: variables.$color-bg;
	}
	.hoverevents &__inp-item:hover &__link {
		text-decoration-color: inherit;
	}
	.hoverevents &__more .as-link:hover {
		&::before,
		&::after {
			background-color: var(--color-hover);
		}
	}

	// MQ
	@media (config.$lg-down) {
		display: flex;
		flex-direction: column;
		width: 100%;
		height: 100dvh;
		&__close {
			--color-link: #{variables.$color-text};
			--color-hover: #{variables.$color-primary};
			position: absolute;
			top: 0;
			right: 0;
			padding: 1.4rem;
			font-size: 0;
			.icon-svg {
				width: 2rem;
			}
		}
		&__title {
			margin: 0;
			padding: 2.8rem 2rem 1.2rem;
		}
		&__wrap {
			@include mixins.vertical-scroll-shadows();
			flex: 1;
			padding: 0 0.8rem;
			overflow-x: hidden;
			overflow-y: auto;
		}
		&__btns {
			display: flex;
			gap: 0.8rem;
			padding: 0.8rem;
			background: variables.$color-white;
		}
		&__btn {
			--btn-padding: 0.9rem 2rem 0.7rem;
			flex: 1 0 auto;
			.btn__info {
				font-weight: 400;
				font-size: 1.1rem;
				text-transform: none;
				opacity: 1;
			}
		}
		&__remove-all {
			display: none;
		}
	}
	@media (config.$lg-up) {
		&__title,
		&__close,
		&__btns {
			display: none;
		}
	}
}
