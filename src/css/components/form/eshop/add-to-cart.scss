@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.f-add-to-cart {
	position: relative;
	&__countdown {
		position: absolute;
		top: 0;
		left: 2rem;
		z-index: 1;
		transform: translateY(-50%);
	}
	&__inner.box {
		padding: 3rem 2rem;
		overflow: visible;
	}
	&__saving {
		margin: 0 0 0.2rem;
	}
	.flag {
		min-height: 1.8rem;
		padding: 0.2rem 0.3rem;
		font-size: 1rem;
	}
	&__availability {
		margin: 0;
	}
	&__delivery {
		margin: 0 0 1rem;
	}
	&__options {
		color: variables.$color-black;
		font-size: 1.4rem;
		line-height: normal;
	}
	&__btns {
		display: flex;
		gap: 0.8rem 2rem;
		flex-wrap: wrap;
	}
	.f-add__wrap {
		gap: 1.3rem;
	}

	// HOVERS
	.hoverevents &__options:hover {
		color: variables.$color-black;
	}

	// MQ
	@media (config.$xxxl-down) {
		&__left {
			width: 100%;
			margin: 0 0 0.4rem;
		}
		&__info {
			width: 100%;
			margin: 0 0 1.6rem;
		}
		&__btn {
			--btn-fs: 1.6rem;
			letter-spacing: -0.03em;
		}
		&:has(&__countdown) {
			margin-top: 2rem;
		}
		.b-damaged + & {
			margin-top: 0;
		}
	}
	@media (config.$md-up) {
		&__countdown {
			left: 4rem;
		}
		&__inner.box {
			padding: 3rem 4rem;
		}
		.f-add__wrap {
			gap: 0 3.2rem;
		}
	}
	@media (config.$xxxl-up) {
		&__inner.box {
			display: flex;
			gap: 3.2rem;
			align-items: center;
		}
		&__info {
			flex: 1;
		}
		&__availability {
			margin: 0 0 0.2rem;
		}
		&__delivery {
			margin: 0 0 0.8rem;
		}
		&__options {
			font-size: 1.5rem;
			line-height: calc(24 / 15);
		}
	}
}
