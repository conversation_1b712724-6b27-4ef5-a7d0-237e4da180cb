@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-table {
	--table-x-padding: 0rem;
	--table-y-padding: 0rem;
	display: grid;
	border-collapse: separate;
	tbody,
	thead,
	tr {
		display: grid;
		grid-template-columns: subgrid;
		grid-column: 1 / -1;
		align-items: center;
	}
	tr {
		border-radius: variables.$border-radius-md;
	}
	thead tr {
		padding: 0 2rem 0.3rem;
	}
	tbody tr {
		margin-bottom: -0.1rem;
		border: 0.1rem solid variables.$color-tile;
	}
	th {
		padding-top: 0;
		border: none;
		font-weight: 400;
	}
	td {
		border: none;
	}

	// MQ
	@media (config.$md-down) {
		thead {
			display: none;
		}
	}
	@media (config.$md-up) {
		tr {
			border-radius: variables.$border-radius-xl;
		}
	}
}
