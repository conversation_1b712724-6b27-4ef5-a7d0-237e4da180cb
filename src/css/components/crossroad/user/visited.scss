@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-visited {
	// MQ
	@media (config.$md-down) {
		&__carousel {
			--arrow-position: -0.7rem;
		}
	}
	@media (config.$lg-up) {
		&__carousel {
			--arrow-position: -2.6rem;
			.embla__viewport {
				margin-right: min(
					calc((var(--vw) - variables.$row-main-width) / -2 - variables.$row-main-gutter),
					calc(var(--row-main-gutter) * -1)
				);
				overflow: hidden;
			}
			.embla__btn--next {
				right: min(
					calc((var(--vw) - variables.$row-main-width) / -2 - variables.$row-main-gutter + 2rem),
					calc(var(--row-main-gutter) * -1 + 2rem)
				);
			}
		}
	}
}
