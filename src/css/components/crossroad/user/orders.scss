@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-orders {
	&__table {
		margin: 0;
		font-size: 1.4rem;
		line-height: 1;
	}
	&__id {
		display: flex;
		gap: 0 1rem;
		flex-wrap: wrap;
		align-items: center;
		margin: 0 0 0.8rem;
	}
	&__imgs {
		display: flex;
		gap: 0.8rem;
		flex-wrap: wrap;
		align-items: flex-end;
		max-width: 25rem;
		margin: 0;
		img {
			width: 3.4rem;
			height: 5.5rem;
			object-fit: contain;
			object-position: center bottom;
		}
	}
	&__price {
		font-weight: bold;
		text-align: right;
	}
	&__more {
		align-content: center;
		width: 3.4rem;
		height: 5.5rem;
		border-radius: variables.$border-radius-sm;
		background: variables.$color-bg-light-3;
		color: inherit;
		font-size: 1.2rem;
		text-align: center;
		text-decoration: none;
	}

	// MQ
	@media (config.$xxxl-down) {
		&__item {
			display: grid;
			grid-template-columns: 1fr max-content;
			grid-template-rows: 1fr auto auto auto;
			grid-template-areas:
				'info price'
				'links links'
				'status status'
				'detail detail';
			gap: 0.8rem 1rem;
			padding: 1.6rem 0;
			border: 0.1rem solid variables.$color-bg;
			border-width: 0 0 0.1rem;
			& > * {
				padding: 0;
				border: none;
			}
			&:first-child {
				border-width: 0.1rem 0;
			}
		}
		&__info {
			grid-area: info;
		}
		&__price {
			grid-area: price;
		}
		&__status {
			grid-area: status;
		}
		&__links {
			grid-area: links;
		}
		&__detail {
			grid-area: detail;
		}
	}
	@media (config.$sm-down) {
		&__detail {
			.btn {
				width: 100%;
			}
		}
	}
	@media (config.$sm-up) {
		&__item {
			grid-template-columns: 1fr auto max-content;
			grid-template-rows: 1fr auto auto;
			grid-template-areas:
				'info status price'
				'info links links'
				'info detail detail';
			align-items: center;
		}
		&__detail {
			text-align: right;
		}
	}
	@media (config.$md-up) {
		&__table {
			font-size: 1.5rem;
			line-height: calc(23 / 15);
		}
		&__item {
			grid-template-columns: 1fr auto max-content;
			grid-template-rows: 1fr auto;
			grid-template-areas:
				'info links status price'
				'info detail detail detail';
		}
	}
	@media (config.$xxxl-up) {
		&__table {
			--table-x-padding: 2.8rem;
			--table-y-padding: 1.2rem;
			--table-bd-color: #{variables.$color-bg};
		}
		&__item {
			display: table-row;
			& > * {
				vertical-align: middle;
			}
		}

		// MODIF
		&__table > *:first-child tr:first-child > *:is(td, th) {
			padding-top: var(--table-y-padding);
			border-top-width: 0.1rem;
		}
	}
}
