@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-else {
	&__title {
		margin: 0;
		font-size: 1.6rem;
	}
	&__grid {
		@extend %reset-ul;
	}
	&__cell {
		@extend %reset-ul-li;
	}

	// MQ
	@media (config.$lg-down) {
		max-width: calc(var(--vw) - 2 * variables.$row-main-gutter);
		padding: 1.6rem 1.2rem;
		border: 0.1rem solid variables.$color-tile-light;
		border-radius: variables.$border-radius-lg;
		&__carousel {
			--arrow-position: 0;
			margin: 0 -2.1rem;
			padding: 0 2.1rem;
			.embla__viewport {
				margin: 0 -1.2rem;
				padding: 0 1.2rem;
				overflow: hidden;
			}
		}
		&__cell {
			width: 15rem;
		}
	}
	@media (config.$lg-up) {
		padding-top: 0.8rem;
		&__title {
			margin: 0 0 1.2rem;
		}
		&__grid {
			flex-wrap: wrap;
		}
		&__cell {
			margin-bottom: -0.1rem;
			&:last-child {
				margin: 0;
			}
		}
	}
}
