@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-discounted {
	&__title {
		margin: 0 0 1.2rem;
	}
	&__head {
		margin: 0 0 1.2rem;
	}
	&__carousel {
		--arrow-position: -1rem;
		margin: 0 calc(var(--box-padding-x) * -1);
		overflow: visible;
		.embla__viewport {
			padding: 0 var(--box-padding-x);
			overflow: hidden;
		}
	}
	&__cell {
		width: 28rem;
		&::before {
			content: '';
			position: absolute;
			top: 3.2rem;
			right: 100%;
			bottom: 3.2rem;
			width: 0.1rem;
			background: variables.$color-tile-light;
		}
	}

	// STATES
	&__cell:first-child::before,
	&__cell:has(.is-active)::before,
	&__cell:has(.is-active) + &__cell::before {
		content: none;
	}

	// MQ
	@media (config.$md-up) {
		&__head {
			margin: 0 0 2.4rem;
		}
		&__cell {
			width: 40rem;
		}
		&__carousel {
			--arrow-position: -2.5rem;
		}
	}
}
