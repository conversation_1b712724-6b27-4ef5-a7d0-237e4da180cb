@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-gifts {
	position: relative;
	padding: var(--padding);
	background: variables.$color-white;
	&::before {
		content: '';
		position: absolute;
		top: -0.8rem;
		left: var(--padding);
		border-width: 0 1rem 0.8rem;
		border-style: solid;
		border-color: transparent transparent variables.$color-white;
		transform: rotate(0deg);
	}
	&__title {
		font-family: variables.$font-primary;
		font-size: 1.6rem;
	}
	&__list {
		--grid-x-spacing: 2rem;
	}
	&__item {
		order: 2;
		width: 50%;
	}

	// STATES
	&__item:has(.btn.is-disabled) {
		order: 1;
	}

	// MQ
	@media (config.$xs-up) {
		&__item {
			width: calc(100% / 3);
		}
	}
	@media (config.$sm-up) {
		&__item {
			width: calc(100% / 4);
		}
	}
	@media (config.$md-up) {
		&__item {
			width: calc(100% / 5);
		}
	}
	@media (config.$lg-up) {
		&__group {
			display: flex;
			align-items: center;
		}
		&__title {
			flex: 0 0 auto;
			width: 17rem;
			margin: 0;
			padding: 2rem;
		}
		&__carousel {
			flex: 1;
		}
		&__item {
			width: calc(100% / 5);
		}
	}
	@media (config.$xl-up) {
		&__item {
			width: calc(100% / 7);
		}
	}
	@media (config.$xxxl-up) {
		&__item {
			width: calc(100% / 8);
		}
	}
}
