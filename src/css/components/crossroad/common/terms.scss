@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-terms {
	&__carousel {
		--arrow-position: calc(var(--row-main-gutter) / -2);
		overflow: visible;
	}

	// MQ
	@media (min-width: 636px) {
		&__cell.grid__cell {
			width: 50%;
		}
	}
	@media (config.$md-up) {
		&__carousel {
			--arrow-position: calc((var(--vw) - 100%) / -2 + 2rem);
		}
		&__cell.grid__cell {
			width: max(33.33%, calc(29rem + var(--grid-x-spacing)));
		}
	}
	@media (config.$lg-up) {
		&__cell.grid__cell {
			width: 33.33%;
		}
	}
}
