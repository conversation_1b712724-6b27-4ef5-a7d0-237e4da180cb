@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-references {
	&__title {
		margin: 0 0 1.2rem;
		&.h1 {
			margin: 0 0 1.6rem;
		}
	}
	&__carousel {
		--arrow-position: -0.7rem;
		overflow: visible;
	}
	&__grid {
		--grid-x-spacing: 0.8rem;
	}
	&__cell {
		width: max(calc(31rem + var(--grid-x-spacing)), 33.33%);
	}

	// MQ
	@media (config.$md-up) {
		&__title {
			margin: 0 0 2.4rem;
			text-align: center;
			&.h1 {
				margin: 0 0 3.2rem;
			}
		}
		&__carousel {
			--arrow-position: calc((var(--vw) - 100%) / -2 + 2rem);
		}
		&__grid {
			--grid-x-spacing: 2rem;
		}
	}
}
