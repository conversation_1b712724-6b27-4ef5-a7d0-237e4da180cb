@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-address {
	--limit: 33.5rem;
	$s: &;
	&__limiter {
		position: relative;
		max-height: var(--limit);
		border-radius: variables.$border-radius-md;
		overflow: hidden;
		&::before {
			content: '';
			position: absolute;
			right: 0;
			bottom: 2.2rem;
			left: 0;
			height: 3.6rem;
			background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.1) 100%);
		}
	}
	&__more {
		position: absolute;
		right: 0;
		bottom: 0;
		left: 0;
		z-index: 1;
		margin: 0;
		font-size: 1.3rem;
		.item-icon {
			--icon-size: 1.5rem;
			--gap: 0.4rem;
			display: flex;
			justify-content: center;
			width: 100%;
			padding: 0.8rem;
			border: 0.1rem solid variables.$color-tile;
			border-radius: 0 0 variables.$border-radius-md variables.$border-radius-md;
			background: variables.$color-bg;
		}
	}

	// MODIF
	&--profile {
		--limit: 45.3rem;
	}

	// STATES
	&:has(.b-address > .f-open.is-open),
	&.is-open {
		#{$s}__limiter {
			max-height: none;
			overflow: visible;
		}
		#{$s}__limiter::before,
		#{$s}__more {
			display: none;
		}
	}

	// MQ
	@media (config.$md-up) {
		--limit: 38.8rem;
		&__limiter {
			&::before {
				bottom: 3rem;
			}
		}
		&__more {
			font-size: 1.4;
			.item-icon {
				padding: 1.2rem;
			}
		}

		&--profile {
			--limit: 50.8rem;
		}
	}
}
