@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-mentions {
	&__title {
		margin: 0 0 1.2rem;
	}
	&__annot {
		margin: 0 0 1.4rem;
	}
	&__logos {
		display: flex;
		gap: 0.6rem 1.2rem;
		flex-wrap: wrap;
		align-items: center;
		margin: 0 0 1.6rem;
	}
	&__logo {
		flex: 0 0 auto;
		width: 9.3rem;
	}
	&__grid {
		--grid-x-spacing: 0.8rem;
		--grid-y-spacing: var(--grid-x-spacing);
	}
	&__more {
		margin: 1.2rem 0 0;
		text-align: center;
	}

	// MQ
	@media (config.$md-up) {
		&__title {
			margin: 0 0 1.6rem;
			text-align: center;
		}
		&__annot {
			margin: 0 0 3.6rem;
			font-size: 1.7rem;
			text-align: center;
		}
		&__logos {
			gap: 1rem 4.4rem;
			justify-content: center;
			margin: 0 0 3.8rem;
		}
		&__logo {
			gap: 0 4.4rem;
			margin: 0 0 3.8rem;
		}
		&__grid {
			--grid-x-spacing: 2.4rem;
		}
		&__more {
			margin: 2.4rem 0 0;
		}
	}
}
