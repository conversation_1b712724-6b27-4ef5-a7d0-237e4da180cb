@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-categories {
	$s: &;
	margin: 0 0 1.2rem;
	font-weight: bold;
	&__list {
		@extend %reset-ul;
		display: grid;
		gap: 0.4rem;
	}
	&__item {
		@extend %reset-ul-li;
	}
	&__link {
		--color-link: #{variables.$color-text};
		--color-hover: #{variables.$color-text};
		display: flex;
		gap: 0.8rem;
		align-items: center;
		height: 100%;
		padding: 0.5rem 2rem 0.5rem 0.5rem;
		border: 0.1rem solid variables.$color-tile;
		border-radius: variables.$border-radius-md;
		text-decoration: none;
		transition: color variables.$t, border-color variables.$t;
	}
	&__img.img {
		flex: 0 0 auto;
		width: 6.4rem;
		border-radius: variables.$border-radius-md;
	}

	// HOVERS
	.hoverevents &__link:hover {
		border-color: variables.$color-icon-minor;
	}

	// MQ
	@media (config.$sm-up) {
		&__list {
			--grid-x-spacing: 0.8rem;
			--grid-y-spacing: 0.8rem;
			// grid-template-columns: repeat(auto-fit, minmax(12.8rem, 1fr));
			grid-template-columns: repeat(auto-fit, 12.8rem);
			gap: 0.8rem;
		}
		&__item {
			// width: calc(12.8rem + var(--grid-x-spacing));
		}
		&__link {
			gap: 0.8rem;
			flex-direction: column;
			justify-content: flex-start;
			padding: 1.2rem 1rem;
			border-radius: variables.$border-radius-lg;
			text-align: center;
		}
		&__img.img {
			width: 8.4rem;
			margin: 0 auto;
		}

		// MODIF
		&--horizontal {
			#{$s}__list {
				grid-template-columns: repeat(2, minmax(0, 1fr));
			}
			#{$s}__img.img {
				flex: 0 0 auto;
				margin: 0;
			}
			#{$s}__link {
				flex-direction: row;
				text-align: left;
			}
		}
	}
	@media (config.$md-up) {
		font-size: 1.4rem;
	}
	@media (config.$lg-up) {
		margin: 0 0 5.2rem;
		&--horizontal {
			#{$s}__list {
				grid-template-columns: repeat(3, minmax(0, 1fr));
			}
		}
	}
}
