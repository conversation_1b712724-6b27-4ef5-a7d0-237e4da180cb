@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-services {
	&__grid {
		--grid-x-spacing: 0.4rem;
		--grid-y-spacing: var(--grid-x-spacing);
	}
	&__inner {
		height: 100%;
		padding: 2.4rem 2rem;
		border-radius: variables.$border-radius-xl;
		background: variables.$color-bg;
	}
	&__link {
		--color-link-decoration: transparent;
		--color-link-hover: var(--color-link);
	}
	&__name {
		margin: 0 0 0.4rem;
	}

	// MQ
	@media (config.$md-down) {
		&__inner {
			position: relative;
			min-height: 14rem;
			padding-right: 12.3rem;
		}
		&__img {
			position: absolute;
			top: 2.4rem;
			right: 0;
			width: 12.3rem;
			margin: 0;
			img {
				aspect-ratio: 4/3;
			}
		}
	}
	@media (config.$md-up) {
		&__grid {
			--grid-x-spacing: 2rem;
		}
		&__img {
			margin: 0 0 1.8rem;
		}
	}
	@media (config.$lg-up) {
		&__inner {
			padding: 2.8rem 4rem 4rem;
		}
	}
}
