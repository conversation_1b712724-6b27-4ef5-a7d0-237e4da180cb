@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-links {
	padding: 2rem;
	border-radius: variables.$border-radius-xl;
	background: rgba(variables.$color-bg, 0.85);
	backdrop-filter: blur(10rem);
	&__title {
		margin: 0 0 0.8rem;
		font-size: 1.6rem;
	}
	&__list {
		@extend %reset-ul;
		display: flex;
		gap: 0.4rem;
		flex-direction: column;
		font-size: 1.3rem;
	}
	&__item {
		@extend %reset-ul-li;
	}
	&__link {
		--color-link-decoration: transparent;
		--icon-size: 2rem;
		--gap: 0.4rem;
		align-items: flex-start;
	}

	// MQ
	@media (config.$md-up) {
		padding: 2.4rem 3.2rem;
		&__title {
			font-size: 1.6rem;
		}
		&__list {
			font-size: 1.4rem;
		}
	}
}
