@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-posts {
	$s: &;
	background: variables.$color-white;
	font-size: 1.3rem;
	line-height: 1.4;
	container-type: inline-size;
	&__list {
		@extend %reset-ul;
	}
	&__item {
		@extend %reset-ul-li;
		display: flex;
		gap: 1.2rem;
		align-items: flex-start;
		padding: 1.1rem 0;
		border: 0.1rem solid variables.$color-tile;
		border-width: 0 0 0.1rem;
	}
	&__img {
		position: relative;
		flex: 0 0 auto;
		width: 8rem;
		border-radius: variables.$border-radius-md;
		background: variables.$color-black;
		overflow: hidden;
	}
	&__play {
		width: 3.1rem;
		background: rgba(white, 0.2);
	}
	&__content {
		flex: 1;
	}
	&__top {
		display: flex;
		gap: 0.4rem;
		justify-content: space-between;
		align-items: center;
		margin: 0 0 0.4rem;
	}
	&__link {
		--color-hover: var(--color-link);
		--color-link-decoration: transparent;
		@include mixins.line-clamp(2);
		font-weight: bold;
	}
	&__flags {
		@extend %reset-ul;
		position: relative;
		display: flex;
		gap: 0.4rem;
		flex: 1;
		overflow: hidden;
		&::after {
			content: '';
			position: absolute;
			top: 0;
			right: 0;
			bottom: 0;
			width: 1rem;
			background: linear-gradient(270deg, #ffffff 0%, rgba(255, 255, 255, 0) 100%);
		}
		li {
			@extend %reset-ul-li;
			display: flex;
		}
		.flag {
			--flag-h: 1.8rem;
			--flag-fs: 0.9rem;
		}
	}
	&__published {
		flex: 0 0 auto;
		color: variables.$color-help;
		font-size: 1rem;
	}

	// MODIF
	&__item:first-child {
		padding-top: 0;
	}
	&__item:last-child {
		padding-bottom: 0;
		border: none;
	}
	&--bd {
		border: 0.1rem solid variables.$color-tile;
		border-radius: variables.$border-radius-lg;
	}
	&--bd &__list {
		padding: 1.2rem;
	}

	// MQ
	@media (config.$md-down) {
		&--m-simple {
			padding-right: 0;
			padding-left: 0;
			border: none;
		}
	}
	@media (config.$md-up) {
		font-size: 1.4rem;
		line-height: 1.6;
		&__item {
			gap: 1.4rem;
			align-items: center;
		}
		&__img {
			width: 11.2rem;
		}
		&__flags {
			.flag {
				--flag-h: 2.2rem;
				--flag-fs: 1.1rem;
			}
		}
		&__published {
			font-size: 1.1rem;
		}

		// MODIF
		&--bd &__list {
			padding: 1.8rem 2.4rem;
		}
	}
	@container (min-width: 420px) {
		// MODIF
		&--horizontal {
			#{$s}__list {
				display: flex;
			}
			#{$s}__item {
				flex-direction: column;
				padding: 1.2rem clamp(1.2rem, calc(2vw - 1rem), 2.4rem);
				border-width: 0 0.1rem 0 0;
			}
			#{$s}__top {
				flex-direction: column-reverse;
				align-items: flex-start;
			}
			#{$s}__link {
				-webkit-line-clamp: 3;
			}
		}
		&--horizontal#{&}--bd &__list {
			padding: 1.2rem 0;
		}
	}
	@media (config.$xl-up) {
		&__item {
			padding: clamp(0.8rem, calc(4.72vw - 6rem), 1.6rem) 0;
		}
	}
}
