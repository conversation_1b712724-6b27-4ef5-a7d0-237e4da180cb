@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-articles-carousel {
	position: relative;
	&__title {
		margin: 0 0 1.2rem;
	}
	&__bg {
		position: absolute;
		top: 0;
		left: 50%;
		z-index: -1;
		width: var(--vw);
		max-width: 192rem;
		min-height: 55rem;
		transform: translateX(-50%);
	}
	&__btn {
		margin: 1.2rem 0 0;
	}

	// MQ
	@media (min-width: 636px) {
		&__grid:not(.grid--scroll) &__cell.grid__cell {
			width: 50%;
		}
	}
	@media (config.$md-up) {
		&__title {
			margin: 0 0 2.4rem;
			text-align: center;
		}
		&__carousel &__cell.grid__cell {
			width: max(33.33%, calc(29rem + var(--grid-x-spacing)));
		}
		&__btn {
			margin: 2.4rem 0 0;
			text-align: center;
		}
	}
	@media (config.$lg-up) {
		&__grid:not(.grid--scroll) &__cell.grid__cell {
			width: 33.33%;
		}
	}
}
