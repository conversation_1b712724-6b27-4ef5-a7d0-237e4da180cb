@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-dronzone-news {
	$s: &;
	&__title {
		margin: 0 0 0.8rem;
	}
	&__left {
		margin: 0 0 1.2rem;
	}
	&__middle > * {
		height: 32.5rem;
	}
	&__right,
	&__right > * {
		height: 100%;
	}
	&__more {
		margin: 1.2rem 0 0;
	}

	// MQ
	@media (config.$xl-down) {
		&__carousel {
			--arrow-position: 0;
			overflow: visible;
			.grid {
				--grid-x-spacing: 0.8rem;
			}
		}
		.grid__cell:has(&__middle) {
			width: auto;
		}
		.grid__cell:has(&__right) {
			flex: 1 0 26rem;
		}
		.grid__cell:has(.b-article-inside) {
			display: none;
		}
	}
	@media (config.$md-up) {
		&__title {
			margin: 0 0 2rem;
		}
		&__middle > * {
			height: 50rem;
		}
		&__more {
			margin: 2rem 0 0;
			text-align: center;
		}
	}
	@media (config.$lg-up) and (max-width: 1399px) {
		&:has(&__middle .b-article-inside) {
			#{$s}__grid {
				display: grid;
				grid-template-columns: 1fr 34.6rem;
				gap: 2rem;
			}
			#{$s}__left {
				margin: 0;
				aspect-ratio: unset;
			}
			#{$s}__right,
			#{$s}__right > * {
				height: 100%;
			}
			#{$s}__carousel {
				.embla__viewport,
				.grid {
					height: 100%;
				}
				.grid {
					--grid-y-spacing: 0;
				}
			}
			.c-posts__item {
				padding: 1.6rem 0;
			}
		}
	}
	@media (config.$xl-up) {
		&__grid {
			display: grid;
			grid-template-columns: repeat(12, 1fr);
			gap: 2rem;
		}
		&__left {
			grid-column: auto / span 6;
			margin: 0;
		}
		&__carousel {
			display: contents;
			.embla__viewport,
			.grid,
			.grid__cell {
				display: contents;
			}
		}
		&__middle {
			grid-row: 1;
			grid-column: 7 / span 3;
			height: 100%;
		}
		&__middle:has(.b-article-inside) {
			display: grid;
			grid-template-rows: 1fr 1fr;
			gap: 2rem;
			.b-article-inside {
				aspect-ratio: unset;
			}
		}
		&__right {
			grid-column: 10 / span 3;
			height: 100%;
		}
		&__middle > *,
		&__right > * {
			height: 100%;
		}

		// MODIF
		&:has(.b-short) {
			#{$s}__middle {
				grid-column: 7 / span 2;
			}
			#{$s}__right {
				grid-column: 9 / span 4;
			}
		}
	}
}
