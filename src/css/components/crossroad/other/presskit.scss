@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-presskit {
	padding: 2.4rem 2rem;
	border: 0.1rem solid variables.$color-tile-light;
	border-radius: variables.$border-radius-lg;
	.grid__cell > & {
		height: 100%;
	}
	&__title {
		margin: 0 0 1.2rem;
	}
	&__list {
		@extend %reset-ul;
		display: flex;
		gap: 0.8rem 2rem;
		flex-wrap: wrap;
		margin: 0 0 1.2rem;
	}
	&__item {
		--icon-offset: -0.1rem;
		--icon-size: 2.5rem;
		--icon-color: #{variables.$color-icon-minor};
		@extend %reset-ul-li;
		flex: 0 0 auto;
		align-items: flex-start;
		width: 100%;
		&.item-icon {
			display: flex;
		}
	}
	&__size {
		color: variables.$color-help;
		font-size: 1.4rem;
	}

	// MQ
	@media (config.$md-up) {
		padding: 4rem 6rem;
		border-radius: variables.$border-radius-xl;
		font-size: 1.5rem;
		&__title {
			margin: 0 0 2rem;
		}
		&__list {
			margin: 0 0 2rem;
		}
	}
	@media (config.$xl-up) {
		&__item {
			width: calc(50% - 1rem);
		}
	}
}
