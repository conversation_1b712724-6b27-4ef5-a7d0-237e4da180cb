@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-positions {
	&__list {
		@extend %reset-ul;
		display: flex;
		gap: 0.4rem;
		flex-direction: column;
		font-weight: bold;
	}
	&__item {
		@extend %reset-ul-li;
	}
	&__link {
		--color-link-decoration: transparent;
		--color-hover: var(--color-link);
		display: flex;
		gap: 1.6rem;
		justify-content: space-between;
		align-items: center;
		min-height: 5.1rem;
		padding: 1.4rem 1.6rem;
		border-radius: variables.$border-radius-md;
		background: variables.$color-bg;
		.icon-svg {
			width: 1.4rem;
		}
	}
	&__offer {
		padding: 2.4rem 2rem 2.8rem;
		border: 0.1rem dashed variables.$color-icon-minor;
		border-radius: variables.$border-radius-lg;
	}
	&__more {
		.btn {
			--btn-gap: 0.6rem;
			--btn-icon-size: 1.5rem;
		}
	}
	&__more:has(.u-d-n) {
		display: none;
	}

	// MQ
	@media (config.$md-up) {
		&__link {
			min-height: 5.9rem;
			padding: 1.6rem 2rem;
		}
		&__offer {
			padding: 4rem;
			border-radius: variables.$border-radius-xl;
			font-size: 1.5rem;
			text-align: center;
		}
	}
}
