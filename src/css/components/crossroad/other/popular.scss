@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.c-popular {
	padding: 2rem;
	font-weight: bold;
	&__list {
		--grid-y-spacing: 0.8rem;
	}
	&__item {
		display: flex;
	}
	&__person {
		--img-size: 3.6rem;
		--gap: 0.8rem;
	}

	// MQ
	@media (config.$md-down) {
		// STATES
		&__item.is-mobile-hidden {
			display: none;
		}
		&__btn:has(.btn.is-mobile-hidden) {
			display: none;
		}
	}
	@media (config.$sm-up) {
		&__item {
			width: 50%;
		}
	}
	@media (config.$md-up) {
		padding: 4.5rem 5rem;
		&__list {
			--grid-y-spacing: 2rem;
		}
		&__item {
			width: 33.33%;
		}
		&__person {
			--img-size: 5.6rem;
			--gap: 1.8rem;
			font-size: 1.6rem;
		}
		&__btn {
			display: none;
		}
	}
	@media (config.$lg-up) {
		&__item {
			width: 25%;
		}
	}
	@media (config.$xl-up) {
		&__item {
			width: 20%;
		}
	}
}
