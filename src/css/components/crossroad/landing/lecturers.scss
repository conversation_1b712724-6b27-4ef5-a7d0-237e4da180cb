@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-lecturers {
	&__head,
	&__title {
		margin: 0 0 0.8rem;
	}
	&__annot {
		font-size: 1.5rem;
	}
	&__carousel {
		--arrow-position: -0.7rem;
		overflow: visible;
	}
	&__grid {
		--grid-x-spacing: 0.8rem;
		font-size: 1.3rem;
	}
	&__cell {
		width: max(calc(20rem + var(--grid-x-spacing)), 20%);
	}
	&__item {
		display: flex;
		flex-direction: column;
		width: 100%;
		height: 100%;
		padding: 0.6rem;
		aspect-ratio: 200/366;
	}
	&__img {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		border-radius: variables.$border-radius-xl;
		object-fit: cover;
	}
	&__play {
		position: absolute;
		top: 2rem;
		left: 2rem;
		a {
			--color-link: #{variables.$color-white};
			--color-hover: #{variables.$color-white};
			display: flex;
			justify-content: center;
			align-items: center;
			width: 3.1rem;
			border: 0.1rem solid rgba(variables.$color-white, 0.2);
			border-radius: 50%;
			background: rgba(variables.$color-white, 0.2);
			text-decoration: none;
			transition: background-color variables.$t;
			aspect-ratio: 1/1;
			box-shadow: 0 0.2rem 0.2rem 0 rgba(0, 14, 71, 0.04), 0 0.6rem 0.7rem 0 rgba(0, 14, 71, 0.08);
			backdrop-filter: blur(2rem);
		}
		.icon-svg {
			width: 1.5rem;
		}
	}
	&__bubble {
		position: relative;
		margin-top: auto;
		padding: 2rem;
		border-radius: variables.$border-radius-lg;
		background: variables.$color-white;
	}
	&__bubble-ear {
		position: absolute;
		top: -1.4rem;
		left: -0.1rem;
		width: 1.6rem;
		color: variables.$color-white;
	}
	&__name {
		margin: 0 0 0.5rem;
	}

	// HOVERS
	.hoverevents &__play a:hover {
		background: variables.$color-text;
	}

	// MQ
	@media (config.$sm-up) {
		&__carousel {
			--arrow-position: -2.6rem;
		}
	}
	@media (config.$md-up) {
		&__head {
			margin: 0 0 2.4rem;
			text-align: center;
		}
		&__title {
			margin: 0 0 2.4rem;
		}
		&__annot {
			font-size: 1.7rem;
		}
		&__grid {
			--grid-x-spacing: 2rem;
			font-size: 1.4rem;
		}
	}
	@media (config.$lg-up) {
		&__item {
			padding: 1.2rem;
			aspect-ratio: 292/500;
		}
		&__play {
			a {
				width: 5.2rem;
			}
			.icon-svg {
				width: 2rem;
			}
		}
		&__bubble {
			padding: 2.8rem 3.2rem 3.2rem;
		}
	}
}
