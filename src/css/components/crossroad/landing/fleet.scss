@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-fleet {
	position: relative;
	&__bg {
		position: absolute;
		top: -15.2rem;
		bottom: -15.2rem;
		left: 50%;
		z-index: -1;
		width: var(--vw);
		margin-left: calc(var(--vw) / -2);
		&::before {
			content: '';
			position: absolute;
			background: linear-gradient(180deg, rgba(255, 255, 255, 0) 85.35%, #ffffff 100%),
				linear-gradient(180deg, #ffffff 0%, rgba(255, 255, 255, 0) 6.35%);
			inset: 0;
		}
		img {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}
	&__head,
	&__title {
		margin: 0 0 1.2rem;
	}
	&__annot {
		font-size: 1.5rem;
	}
	&__carousel {
		--arrow-position: -0.7rem;
		overflow: visible;
	}
	&__grid {
		--grid-x-spacing: 0.8rem;
	}
	&__cell {
		width: max(calc(16rem + var(--grid-x-spacing)), 16.67%);
	}

	// MQ
	@media (config.$sm-up) {
		&__carousel {
			--arrow-position: -2.6rem;
		}
	}
	@media (config.$md-up) {
		&__head {
			margin: 0 0 2.4rem;
			text-align: center;
		}
		&__title {
			display: flex;
			flex-direction: column-reverse;
			justify-content: center;
			margin: 0 0 2.4rem;
		}
		&__annot {
			font-size: 1.7rem;
		}
		&__grid {
			--grid-x-spacing: 2rem;
		}
		&__cell {
			width: max(calc(24rem + var(--grid-x-spacing)), 16.67%);
		}
	}
}
