@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-hero-categories {
	font-weight: bold;
	font-size: 1.4rem;
	&__list {
		@extend %reset-ul;
		display: flex;
		gap: 0.2rem;
		flex-direction: column;
	}
	&__item {
		@extend %reset-ul-li;
		flex: 0 1 calc(100% / 6 - 0.2rem);
	}
	&__link {
		--color-link: #{variables.$color-black};
		--color-hover: #{variables.$color-primary};
		position: relative;
		display: flex;
		gap: 0.8rem;
		align-items: center;
		padding: 0.5rem 2rem 0.5rem 0.5rem;
		border-radius: variables.$border-radius-lg;
		background: variables.$color-white;
		text-decoration: none;
		transition: color variables.$t, box-shadow variables.$t;
		box-shadow: 0 2px 2px 0 rgba(0, 14, 71, 0.04), 0 5px 14px 0 rgba(0, 14, 71, 0.1);
	}
	&__flag {
		--flag-padding: 0.2rem 0.8rem;
		--flag-h: 2.2rem;
		--flag-fs: 1.1rem;
	}
	&__img {
		flex: 0 0 auto;
		width: 8.3rem;
	}
	&__name {
		flex: 1;
	}
	&__arrow {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 1.1lh;
		border-radius: 50%;
		background: variables.$color-primary-150;
		color: variables.$color-primary;
		text-align: center;
		aspect-ratio: 1/1;
		.icon-svg {
			width: 48%;
		}
	}

	// HOVERS
	.hoverevents &__link:hover {
		box-shadow: 0 2px 2px 0 rgba(0, 14, 71, 0.15);
	}

	// MQ
	@media (config.$md-up) {
		&__list {
			flex-direction: row;
			flex-wrap: wrap;
		}
		&__item {
			flex: 0 1 calc(50% - 0.2rem);
		}
	}
	@media (config.$lg-up) {
		&__list {
			gap: 0.8rem;
		}
		&__item {
			flex: 0 1 calc(100% / 6 - 0.8rem);
		}
		&__link {
			gap: 0;
			flex-direction: column;
			height: 100%;
			padding: 1.5rem 2rem;
		}
		&__img {
			width: 100%;
			max-width: 19rem;
			margin: 0 auto 0.4rem;
		}
		&__name {
			display: flex;
			align-items: flex-end;
			margin: auto auto 0 0;
		}
		&__flag {
			position: absolute;
			top: 2rem;
			right: 2rem;
		}
		&__arrow {
			display: none;
		}
	}
	@media (config.$xl-up) {
		&__link {
			padding: 1.5rem 2.6rem 2rem;
		}
	}
	@media (config.$xl-up) {
		&__link {
			padding: 2rem 3.6rem 3rem;
		}
		&__name {
			padding-right: 1.6lh;
		}
		&__arrow {
			position: absolute;
			right: 3.6rem;
			bottom: 3rem;
			display: flex;
		}
	}
	@media (config.$xxxl-up) {
		font-size: 1.8rem;
	}
}
