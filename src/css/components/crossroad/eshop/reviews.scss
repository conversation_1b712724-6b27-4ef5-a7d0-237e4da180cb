@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-reviews {
	&__review {
		border-bottom: 0.1rem solid variables.$color-tile;
		&:last-child {
			border-bottom: none;
		}
	}
	&__more {
		margin-top: 0.8rem;
		.btn {
			--btn-gap: 0.6rem;
			--btn-icon-size: 1.5rem;
		}
	}

	// MQ
	@media (config.$md-up) {
		&__more {
			margin-top: 2.2rem;
		}
	}
}
