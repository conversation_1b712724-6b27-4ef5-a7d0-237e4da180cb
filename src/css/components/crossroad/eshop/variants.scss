@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-variants {
	&__title {
		margin: 0 0 1.2rem;
	}
	&__inner {
		display: flex;
		gap: 4rem;
		flex-direction: column-reverse;
	}
	&__carousel {
		flex: 1;
		overflow: visible;
		.embla__viewport {
			height: 100%;
		}
	}
	&__cell {
		width: 16rem;
	}

	// MQ
	@media (config.$md-up) {
		&__title {
			margin: 0 0 2rem;
			text-align: center;
		}
		&__cell {
			width: max(20%, 21.4rem);
		}
	}
	@media (config.$lg-up) {
		padding: 7rem 0;
		border: 0.1rem solid variables.$color-tile-light;
		border-width: 0.1rem 0;
		&__inner {
			gap: 4rem;
			flex-direction: row;
		}
		&__contact {
			flex: 0 0 auto;
			width: 29.5rem;
		}
		&__grid {
			height: 100%;
		}
	}
}
