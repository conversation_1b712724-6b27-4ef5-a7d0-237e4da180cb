@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.c-bestsellers {
	margin: 0 0 3.2rem;
	&__title {
		margin: 0 0 0.4rem;
		font-size: 1.5rem;
	}
	&__box {
		padding: 1.5rem;
		border: 0.5rem solid variables.$color-bg-light-3;
		border-width: 0.5rem 0 0.5rem 0.5rem;
		border-radius: variables.$border-radius-sm;
	}
	&__carousel {
		--hover-offset-x: 1.5rem;
	}

	// HOVESR
	.hoverevents &:hover {
		position: relative;
		z-index: 2;
	}

	// MQ
	@media (config.$md-down) {
		&__box {
			margin-right: calc(var(--row-main-gutter) * -1);
			padding-right: var(--row-main-gutter);
		}
	}
	@media (config.$sm-down) {
		&__carousel {
			--arrow-position: 0rem;
			.embla__btn {
				top: calc(50vw + 8rem);
				transform: none;
			}
		}
	}
	@media (config.$md-up) {
		margin: 0 0 functions.spacing('md');
		&__title {
			margin: 0 0 1rem;
			font-size: 1.8rem;
		}
		&__box {
			border-width: 0.7rem;
		}
		&__grid {
			--grid-x-spacing: #{functions.spacing('sm')};
		}
	}
	@media (config.$xl-up) {
		&__box {
			padding: 5rem 4.3rem 4.4rem;
		}
		&__carousel {
			--hover-offset-x: 4.3rem;
		}
	}
	@media (config.$xxxl-up) {
		&__grid {
			--grid-x-spacing: #{functions.spacing('lg')};
		}
	}
}
