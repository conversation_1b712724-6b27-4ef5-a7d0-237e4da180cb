@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-followup {
	font-size: 1.5rem;
	container-type: inline-size;
	&__items {
		display: grid;
		grid-template-columns: 13.2fr 16.8fr;
		line-height: 1.6;
	}
	&__item {
		display: grid;
		grid-template-columns: subgrid;
		grid-column: auto / span 2;
		gap: 1.2rem 1.6rem;
		align-items: flex-start;
		padding: 2rem 0.8rem;
		border-bottom: 0.1rem solid variables.$color-tile;
		&:last-child {
			border: none;
		}
	}
	&__img {
		position: relative;
		border-radius: variables.$border-radius-xl;
		background: variables.$color-black;
	}
	&__flags {
		display: none;
	}
	&__type {
		--flag-fs: 1.1rem;
		--flag-h: 2.3rem;
		--flag-padding: 0.3rem 0.8rem 0.2rem;
	}
	&__title {
		margin: 0 0 0.4rem;
	}
	&__link {
		--color-hover: var(--color-link);
		--color-link-decoration: transparent;
	}
	&__desc {
		font-size: 1.3rem;
	}
	&__helper {
		display: flex;
		grid-area: 2 / 1 / 2 / 3;
		gap: 2rem;
		justify-content: space-between;
		align-items: center;
		& > * {
			flex: 1 0 auto;
		}
	}
	&__label {
		display: inline-block;
		margin: 0 0 0.2rem;
		color: variables.$color-help;
		font-size: 1.2rem;
	}
	&__price,
	&__length {
		display: flex;
		flex-direction: column;
		line-height: 1.4;
	}
	&__old {
		color: variables.$color-placeholder;
		font-size: 1.1rem;
	}
	&__btn {
		flex: 0 1 auto;
	}
	&__more {
		margin: 1.2rem 0 0;
	}

	// MQ
	@container (min-width: 520px) {
		&__item {
			grid-template-rows: auto 1fr;
		}
		&__img {
			grid-area: 1/1/3/1;
		}
		&__main {
			grid-area: 1/2/1/2;
		}
		&__helper {
			grid-area: 2/2/2/2;
		}
	}
	@container (min-width: 850px) {
		font-size: 1.6rem;
		&__items {
			grid-template-columns: 24.8rem 1fr auto auto auto;
		}
		&__item {
			grid-column: auto / span 5;
			gap: 2rem;
			align-items: center;
			padding: 2.4rem;
		}
		&__flags {
			position: absolute;
			top: -0.5rem;
			left: -0.5rem;
			display: flex;
			.flag {
				--flag-fs: 1.1rem;
				--flag-h: 2.2rem;
				--flag-padding: 0.2rem 0.8rem;
			}
		}
		&__desc {
			font-size: 1.5rem;
		}
		&__helper {
			display: contents;
		}
		&__label {
			margin: 0 0 0.6rem;
			font-size: 1.3rem;
		}
		&__old {
			font-size: 1.2rem;
		}
		&__more {
			margin: 2.4rem 0 0;
		}
	}
	@container (min-width: 1000px) {
		&__item {
			gap: 4rem;
		}
	}
}
