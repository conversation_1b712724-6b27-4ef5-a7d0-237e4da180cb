@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.c-course-lecturers {
	// &__item {
	// 	border-radius: variables.$border-radius-md;
	// 	background: rgba(variables.$color-bg, 0.85);
	// 	overflow: hidden;
	// 	backdrop-filter: blur(10rem);
	// }
	// &__img {
	// 	margin: 0;
	// 	img {
	// 		aspect-ratio: 290/144;
	// 	}
	// }
	// &__content {
	// 	padding: 2rem 2rem 2.8rem;
	// 	p {
	// 		margin: 0 0 0.8rem;
	// 	}
	// }

	// MQ
	@media (config.$xl-up) {
		&__cell.grid__cell {
			width: min(100%, calc(90rem + var(--grid-x-spacing)));
		}
		// &__item {
		// 	display: flex;
		// 	border-radius: variables.$border-radius-xl;
		// }
		// &__img {
		// 	flex: 0 0 min(42%, 37rem);
		// 	img {
		// 		aspect-ratio: 370/407;
		// 	}
		// }
		// &__content {
		// 	flex: 1;
		// 	padding: 6rem;
		// 	p {
		// 		margin: 0 0 1.6rem;
		// 	}
		// }
	}
}
