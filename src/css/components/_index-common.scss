// Box
// @forward 'components/box/common/annot';
@forward 'components/box/common/header';
@forward 'components/box/common/audio';
// @forward 'components/box/common/cookie';
@forward 'components/box/common/usp';
@forward 'components/box/common/person-header';
@forward 'components/box/common/person-footer';
@forward 'components/box/common/ratings';
@forward 'components/box/common/eu';
@forward 'components/box/common/suggest';
@forward 'components/box/common/login';
@forward 'components/box/common/bubble';
@forward 'components/box/common/submenu-side';
@forward 'components/box/common/services';
@forward 'components/box/common/bnr-dronzone';
@forward 'components/box/common/video';
@forward 'components/box/common/faq';
@forward 'components/box/common/bnr';
@forward 'components/box/common/article';
@forward 'components/box/common/project-info';
@forward 'components/box/common/person-cta';
@forward 'components/box/common/zigzag';
@forward 'components/box/common/mention';
@forward 'components/box/common/bonds';
@forward 'components/box/common/coverage';
@forward 'components/box/common/results';
@forward 'components/box/common/suggest-results';
@forward 'components/box/common/order-login';
@forward 'components/box/common/dronzone';
// @forward 'components/box/common/std';
@forward 'components/box/common/content';
@forward 'components/box/common/quote';
@forward 'components/box/common/basket';
@forward 'components/box/common/gallery';
@forward 'components/box/common/burger';
// @forward 'components/box/common/user';
// @forward 'components/box/common/register';
@forward 'components/box/common/benefits';
// @forward 'components/box/common/photos';
// @forward 'components/box/common/tile';
// @forward 'components/box/common/book-review';
@forward 'components/box/common/product';
// @forward 'components/box/common/layout';
// @forward 'components/box/common/horizontal';
@forward 'components/box/common/smallbasket';
// @forward 'components/box/common/prebasket';
@forward 'components/box/common/delivery';
// @forward 'components/box/common/tips';
// @forward 'components/box/common/order-content';
// @forward 'components/box/common/barcode';
// @forward 'components/box/common/saving';
@forward 'components/box/common/submenu';
@forward 'components/box/common/more';
@forward 'components/box/common/payment-instructions';
@forward 'components/box/common/person';
@forward 'components/box/common/persons';
@forward 'components/box/common/cta-box';
@forward 'components/box/common/features';
@forward 'components/box/common/popular';
@forward 'components/box/common/steps';
@forward 'components/box/common/course-lecturer';
@forward 'components/box/common/locked';
@forward 'components/box/common/article-inside';
@forward 'components/box/common/reference';
@forward 'components/box/common/heureka-rating';
@forward 'components/box/common/help';
@forward 'components/box/common/short';

// Crossroad
@forward 'components/crossroad/common/categories';
@forward 'components/crossroad/common/insurance';
@forward 'components/crossroad/common/sub';
@forward 'components/crossroad/common/products';
@forward 'components/crossroad/common/address';
@forward 'components/crossroad/common/projects';
@forward 'components/crossroad/common/mentions';
@forward 'components/crossroad/common/links';
@forward 'components/crossroad/common/services';
@forward 'components/crossroad/common/references';
@forward 'components/crossroad/common/articles-carousel';
@forward 'components/crossroad/common/dronzone-news';
@forward 'components/crossroad/common/posts';
@forward 'components/crossroad/common/terms';
@forward 'components/crossroad/common/types';

// Form
@forward 'components/form/common/login';
@forward 'components/form/common/search';
@forward 'components/form/common/newsletter';
@forward 'components/form/common/sort';
// @forward 'components/form/common/contact';
// @forward 'components/form/common/review';
@forward 'components/form/common/open';
@forward 'components/form/common/add';

// Menu
@forward 'components/menu/common/accessibility';
@forward 'components/menu/common/main';
@forward 'components/menu/common/service';
@forward 'components/menu/common/submenu';
// @forward 'components/menu/common/currency';
@forward 'components/menu/common/user';
@forward 'components/menu/common/footer';
@forward 'components/menu/common/social-footer';
@forward 'components/menu/common/social';
@forward 'components/menu/common/breadcrumb';
// @forward 'components/menu/common/links';
