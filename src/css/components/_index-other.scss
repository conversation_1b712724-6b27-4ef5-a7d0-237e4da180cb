// Box
// @forward 'components/box/other/story';
// @forward 'components/box/other/our-reviews';
// @forward 'components/box/other/team';
// @forward 'components/box/other/authors';
// @forward 'components/box/other/store';
@forward 'components/box/other/store-info';
// @forward 'components/box/other/transport';
@forward 'components/box/other/contact';
@forward 'components/box/other/404';
@forward 'components/box/other/store';
@forward 'components/box/other/contact-media';
@forward 'components/box/other/other';
@forward 'components/box/other/search';
@forward 'components/box/other/top';
@forward 'components/box/other/join';
@forward 'components/box/other/header-ebook';
@forward 'components/box/other/file';
@forward 'components/box/other/ebook-download';
@forward 'components/box/other/header-about';
@forward 'components/box/other/header-position';
@forward 'components/box/other/compare';
@forward 'components/box/other/compare-product';
// @forward 'components/box/other/box';
// @forward 'components/box/other/bank-info';
// @forward 'components/box/other/search-alphabet';
// @forward 'components/box/other/theme';
// @forward 'components/box/other/discount';

// Crossroad
// @forward 'components/crossroad/other/contacts';
// @forward 'components/crossroad/other/popular';
// @forward 'components/crossroad/other/letters';
// @forward 'components/crossroad/other/tiles';
@forward 'components/crossroad/other/stores';
@forward 'components/crossroad/other/presskit';
@forward 'components/crossroad/other/positions';

// Form
@forward 'components/form/other/compare';

// Menu
@forward 'components/menu/other/tabs';
