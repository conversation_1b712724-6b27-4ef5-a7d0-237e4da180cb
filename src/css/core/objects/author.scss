@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.author {
	--author-img-size: 3.4rem;
	--author-fs: 1.2rem;
	--author-gap: 0.4rem;
	$s: &;
	display: inline-flex;
	gap: var(--author-gap);
	flex-direction: column;
	align-items: center;
	font-size: var(--author-fs);
	text-align: center;
	&__img.img {
		width: var(--author-img-size);
	}

	// MQ
	@media (config.$md-down) {
		// MODIF
		&--article {
			--author-img-size: 2.6rem;
			--author-gap: 0.8rem;
			--author-fs: 1.4rem;
			flex-direction: row-reverse;
			padding-top: 0.2rem;
			#{$s}__img {
				margin-top: -0.2rem;
			}
		}
	}
	@media (config.$md-up) {
		&--article {
			--author-img-size: 6rem;
		}
	}
}
