@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/mixins';
@use 'core/objects/inputs/extends';

.inp-fix {
	--inp-h: 4.8rem;
	--inp-bd: #{variables.$color-bd};
	--inp-padding-x: 1.6rem;
	--inp-padding-y: 1.1rem;
	--inp-fs: 1.4rem;
	--inp-icon-size: 2.5rem;
	--inp-icon-error-size: 1.5rem;
	$s: &;
	position: relative;
	display: block;
	&__link {
		--color-link: #{variables.$color-placeholder};
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		display: block;
		width: calc(var(--inp-icon-size) + var(--inp-padding-x) * 2);
	}
	&__icon {
		position: absolute;
		top: 50%;
		left: var(--inp-padding-x);
		width: var(--inp-icon-size);
		color: variables.$color-icon-minor;
		transform: translateY(-50%);
		&--ok {
			width: var(--inp-icon-error-size);
			color: variables.$color-green;
		}
		&--error {
			width: var(--inp-icon-error-size);
			color: variables.$color-red;
		}
	}

	// MODIF
	*:is(.js-phone-input__wrapper, .inp-text) ~ &__icon {
		right: var(--inp-padding-x);
		left: auto;
	}
	&:has(> #{$s}__icon:first-child) .inp-text {
		padding-left: calc(var(--inp-padding-x) + var(--inp-icon-size) + 0.4rem);
	}
	&:has(> *:is(.js-phone-input__wrapper, .inp-text) ~ #{$s}__icon) .inp-text,
	&:has(> #{$s}__link) .inp-text {
		padding-right: calc(var(--inp-padding-x) + var(--inp-icon-size) + 0.4rem);
	}
	&--xs {
		--inp-h: 3.4rem;
		--inp-fs: 1.3rem;
		--inp-padding-x: 1.2rem;
		--inp-padding-y: 0.6rem;
		& + .inp-error {
			--error-fs: 1.1rem;
		}
	}
	&--sm {
		--inp-h: 3.9rem;
		--inp-fs: 1.3rem;
		--inp-padding-x: 1.4rem;
		--inp-padding-y: 0.7rem;
		& + .inp-error {
			--error-fs: 1.1rem;
		}
	}
	&[data-controller='password'] {
		--inp-icon-size: 2.5rem;
		#{$s}__link::before {
			content: '';
			position: absolute;
			top: 50%;
			left: 50%;
			width: 0.2rem;
			height: 42%;
			background: transparent;
			transform: translateX(-50%) translateY(-50%) rotate(25deg);
			transition: background-color variables.$t;
		}
	}

	// STATEs
	.has-error & {
		color: variables.$color-status-invalid;
	}
	.has-warning & {
		color: variables.$color-alert;
	}
	.is-ok & {
		color: variables.$color-status-valid;
	}
	&:has(input:focus, input:not(:placeholder-shown)) &__link {
		--color-link: #{variables.$color-text};
	}
	&[data-controller='password'] .inp-text[type='text'] + &__link::before {
		background: var(--color-link);
	}

	// HOVERS
	.hoverevents &[data-controller='password'] &__link:hover {
		--color-link: #{variables.$color-primary};
		--color-hover: #{variables.$color-primary};
	}

	// MQ
	@media (config.$md-up) {
		&--lg {
			--inp-fs: 1.5rem;
			--inp-h: 5.8rem;
			--inp-padding-x: 1.6rem;
			--inp-padding-y: 1.5rem;
			& + .inp-error {
				--error-fs: 1.3rem;
			}
		}
	}
}
