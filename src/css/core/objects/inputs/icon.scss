.inp-icon {
	.icon-svg {
		display: block;
		width: 1.6rem;
	}
	.inp-text,
	.inp-select {
		padding-left: 4.6rem;
	}
	&__icon {
		position: absolute;
		top: 50%;
		left: 1.5rem;
		font-size: 0;
		transform: translateY(-50%);
		&:not(button) {
			pointer-events: none;
		}
	}

	// STATES
	&--after {
		.inp-text,
		.inp-select {
			padding-right: 4.6rem;
			padding-left: 1.5rem;
		}
		.inp-icon__icon {
			right: 1.5rem;
			left: auto;
		}
	}
}
