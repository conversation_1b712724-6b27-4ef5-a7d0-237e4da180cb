@use 'base/variables';
@use 'core/objects/inputs/extends';

.inp-select {
	@extend %inp-text;
	padding-right: 3.5rem;
	background-image: url(variables.$svg-select);
	background-position: top 50% right var(--inp-padding-x);
	background-repeat: no-repeat;
	background-size: 1.5rem;
	&::-ms-expand {
		display: none;
	}
	&.is-disabled,
	&:disabled {
		background-image: url(variables.$svg-select-disabled);
	}
	&.as-link {
		--color-link: #{variables.$color-text};
		--color-hover: var(--color-link);
		white-space: nowrap;
		text-decoration: none;
		text-overflow: ellipsis;
		overflow: hidden;
		cursor: auto;
	}
	&.as-link.is-open {
		background-image: url(variables.$svg-select-reversed);
	}
}
