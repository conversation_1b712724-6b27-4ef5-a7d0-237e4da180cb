@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.inp-item {
	--inp-item-fs: 1.4rem;
	position: relative;
	display: inline-block;
	vertical-align: top;
	padding-left: 3rem;
	cursor: pointer;
	-webkit-tap-highlight-color: transparent;
	&__inp {
		position: absolute;
		left: -500rem;
	}
	&__text {
		display: block;
		font-size: var(--inp-item-fs);
		&::before {
			content: '';
			position: absolute;
			top: -0.1rem;
			left: 0;
			width: 2.4rem;
			height: 2.4rem;
			border: 0.2rem solid variables.$color-bd;
			border-radius: 0.4rem;
			background: variables.$color-white;
			outline: 0.4rem solid transparent;
			transition: border-color variables.$t, background-color variables.$t, outline-color variables.$t;
		}
		&::after {
			content: '';
			position: absolute;
			opacity: 0;
			transition: opacity variables.$t;
		}
	}

	// VARIANTS
	&--checkbox &__text {
		&::after {
			top: 0.4rem;
			left: 0.9rem;
			display: inline-block;
			width: 0.6rem;
			height: 1.2rem;
			border-right: 0.2rem solid variables.$color-white;
			border-bottom: 0.2rem solid variables.$color-white;
			transform: rotate(45deg);
		}
	}
	&--radio &__text {
		&::before,
		&::after {
			border-radius: 50%;
		}
		&::after {
			top: 0.45rem;
			left: 0.55rem;
			width: 1.3rem;
			height: 1.3rem;
			background: variables.$color-primary;
		}
	}

	// HOVER
	.hoverevents &:hover &__text::before {
		border-color: variables.$color-primary-300;
	}

	// STATES
	// focus
	&__inp:focus + &__text {
		&::before {
			border-color: variables.$color-primary-300;
			outline-color: variables.$color-primary-150;
		}
	}
	// checked
	&__inp:checked + &__text::after {
		opacity: 1;
	}
	&--checkbox &__inp:checked + &__text {
		&::before {
			border-color: variables.$color-primary;
			background: variables.$color-primary;
		}
	}
	// disabled
	&:has(&__inp:disabled) {
		pointer-events: none;
	}
	&__inp:disabled + &__text {
		color: variables.$color-placeholder;
		cursor: default;
		&::before {
			border-color: variables.$color-tile;
			background: variables.$color-surface;
		}
	}
	&--checkbox &__inp:checked:disabled + &__text::before {
		border-color: variables.$color-placeholder;
		background: variables.$color-placeholder;
	}
	&--radio &__inp:checked:disabled + &__text::after {
		background: variables.$color-placeholder;
	}
	// error & ok
	&.has-error {
		--status-color: #{variables.$color-status-invalid};
		--status-color-focus: #{variables.$color-status-invalid-light};
	}
	&.is-ok {
		--status-color: #{variables.$color-status-valid};
		--status-color-focus: #{variables.$color-status-valid-light};
	}
	&:is(.has-error, .is-ok) &__text::before,
	.hoverevents &:is(.has-error, .is-ok):hover &__text::before {
		border-color: var(--status-color);
	}
	&:is(.has-error, .is-ok) &__text::before {
		border-color: var(--status-color);
	}
	&--checkbox:is(.has-error, .is-ok) &__inp:checked + &__text::before {
		border-color: var(--status-color);
		background: var(--status-color);
	}
	&--radio:is(.has-error, .is-ok) &__inp:checked + &__text {
		&::before {
			border-color: var(--status-color);
		}
		&::after {
			background: var(--status-color);
		}
	}
	&:is(.has-error, .is-ok) &__inp:focus + &__text::before {
		border-color: var(--status-color);
		outline-color: var(--status-color-focus);
	}
}
