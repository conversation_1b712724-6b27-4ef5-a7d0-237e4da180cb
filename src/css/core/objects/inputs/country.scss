@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.inp-country {
	display: flex;
	gap: 0.4rem;
	align-items: center;
	font-family: variables.$font-primary;
	font-weight: 400;
	font-size: 1.5rem;
	&__select {
		border: none;
		background-color: transparent;
		outline: none;
		font-size: inherit;
		text-decoration: underline;
		-webkit-appearance: none;
	}
}
