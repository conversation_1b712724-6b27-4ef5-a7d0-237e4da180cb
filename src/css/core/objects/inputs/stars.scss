@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.inp-stars {
	--size: 5rem;
	display: inline-flex;
	flex: 0;
	&__icon {
		width: var(--size);
		color: variables.$color-alert;
	}
	&__inp {
		@include mixins.vhide();
	}
	&__label {
		width: calc(var(--size) / 2);
		line-height: 1;
		text-align: center;
		overflow: hidden;
		cursor: pointer;
	}
	&__inp--empty {
		display: none;
	}

	// MODIF
	&__label--half &__icon {
		transform: translateX(-50%);
	}

	// STATES
	&__inp:checked ~ &__label &__icon {
		color: transparent;
	}

	// HOVERS
	.hoverevents &:hover &__label &__icon,
	.hoverevents &:hover &__label--half &__icon {
		color: variables.$color-alert;
	}
	.hoverevents &__label:hover ~ &__label &__icon,
	.hoverevents &__label:hover ~ &__label--half &__icon {
		color: transparent;
	}

	// MQ
	@media (config.$md-up) {
		--size: 6.4rem;
	}
}
