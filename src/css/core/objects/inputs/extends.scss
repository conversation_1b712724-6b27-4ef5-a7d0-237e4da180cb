@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

%inp-text {
	display: block;
	width: 100%;
	min-height: var(--inp-h);
	padding: var(--inp-padding-y) var(--inp-padding-x);
	border: 0.1rem solid var(--inp-bd);
	border-radius: 1.2rem;
	color: variables.$color-text;
	outline: variables.$focus-outline-width variables.$focus-outline-style transparent;
	outline-offset: -0.1rem;
	font-size: var(--inp-fs);
	line-height: variables.$line-height;
	transition: outline-color variables.$t, background-color variables.$t, border-color variables.$t;
	appearance: none;

	// search clear button
	&::-webkit-search-cancel-button {
		right: var(--padding-x);
		width: 1.5rem;
		height: 1.5rem;
		background-image: url(variables.$svg-close);
		background-repeat: no-repeat;
		background-size: contain;
		-webkit-appearance: none;
	}

	// hide number arrows
	&::-webkit-outer-spin-button,
	&::-webkit-inner-spin-button {
		margin: 0;
		-webkit-appearance: none;
	}
	&[type='number'] {
		-moz-appearance: textfield;
	}

	// STATE
	&:disabled {
		--inp-bd: #{variables.$color-tile};
		background-color: variables.$color-surface;
		pointer-events: none;
	}
	.has-error & {
		--inp-bd: #{variables.$color-status-invalid};
	}
	&:focus,
	.hoverevents &:hover {
		outline-color: variables.$color-icon-minor;
	}
	.has-error &:focus,
	.hoverevents .has-error &:hover {
		outline-color: variables.$color-status-invalid;
	}
}
