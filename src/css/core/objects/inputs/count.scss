@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.inp-count {
	position: relative;
	display: inline-block;
	width: 10.6rem;
	&__inp.inp-text {
		min-height: 4rem;
		padding: 0 3rem;
		border: 0.1rem solid variables.$color-bd;
		border-radius: variables.$border-radius-lg;
		font-size: 1.4rem;
		text-align: center;
	}
	&__tool {
		--position: 0.7rem;
		position: absolute;
		top: var(--position);
		bottom: var(--position);
		width: 2.4rem;
		border-radius: 50%;
		background: variables.$color-surface;
		transition: background-color variables.$t, color variables.$t;
		aspect-ratio: 1/1;
		-webkit-tap-highlight-color: transparent;
		&::before,
		&::after {
			content: '';
			position: absolute;
			top: 50%;
			left: 50%;
			width: 1rem;
			height: 0.167rem;
			border-radius: 0.2rem;
			background: variables.$color-placeholder;
			transform: translate(-50%, -50%);
			transition: background-color variables.$t;
		}
		&--minus {
			left: var(--position);
		}
		&--plus {
			right: var(--position);
			&::after {
				transform: translate(-50%, -50%) rotate(90deg);
			}
		}
		&.is-disabled {
			opacity: 0.5;
			cursor: default;
		}
	}

	// HOVERS
	.hoverevents &__tool:not(.is-disabled):hover {
		background: variables.$color-primary-150;
		&::before,
		&::after {
			background: variables.$color-primary;
		}
	}

	// MQ
	@media (config.$lg-up) {
		width: 12.2rem;
		&__inp.inp-text {
			min-height: 4.8rem;
			padding: 0 3.5rem;
		}
		&__tool {
			--position: 1.1rem;
		}
	}
}
