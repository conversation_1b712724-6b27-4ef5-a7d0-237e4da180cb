@use 'base/variables';
@use 'core/objects/inputs/extends';

.js-phone-input {
	$input-padding-horizontal: 1.6rem;
	$input-padding-vertical: 2rem;
	$flag-padding-horizontal: 2rem;
	$flag-padding-vertical: 1.6rem;
	&__base {
		position: absolute;
		left: 0;
		opacity: 0;
		pointer-events: none;
	}
	&__wrapper {
		position: relative;
		z-index: 0;
		display: flex;
		flex-direction: row-reverse;
		line-height: variables.$line-height;
	}
	&__flag {
		display: flex;
		gap: 0.4rem;
		flex: 0 0 12rem;
		align-items: center;
		padding-left: 1.4rem;
		border-right: none;
		border-top-right-radius: 0;
		border-bottom-right-radius: 0;
		pointer-events: none;
		img {
			width: 2.6rem;
			height: 2rem;
			object-fit: contain;
		}
	}
	&__input {
		border-top-left-radius: 0;
		border-bottom-left-radius: 0;
		background-color: transparent;
		text-shadow: 0 0.1rem 0.1rem variables.$color-white, 0.1rem 0 0.1rem variables.$color-white, 0 -0.1rem 0.1rem variables.$color-white,
			-0.1rem 0 0.1rem variables.$color-white, -0.1rem -0.1rem 0.1rem variables.$color-white,
			0.1rem 0.1rem 0.1rem variables.$color-white, 0.1rem -0.1rem 0.1rem variables.$color-white,
			-0.1rem 0.1rem 0.1rem variables.$color-white;
		font-variant-numeric: tabular-nums;
	}
	&__placeholder {
		z-index: -1;
		display: flex;
		align-items: center;
		width: 0;
		margin: 0 -1.6rem 0 1.6rem;
		color: rgba(variables.$color-text, 0.5);
		font-size: 1.4rem;
		line-height: variables.$line-height;
		white-space: nowrap;
		pointer-events: none;
		font-variant-numeric: tabular-nums;
	}
	&__select {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		z-index: -1;
		padding: 0 $flag-padding-horizontal;
		border: none;
		background-color: transparent;
		color: transparent;
		appearance: none;
		option {
			color: variables.$color-text;
		}
		&:focus {
			outline: 0;
		}
	}
}
