@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.img {
	$s: &;
	display: block;
	width: 100%;
	height: fit-content;
	aspect-ratio: 1/1;
	object-fit: cover;
	// position: relative;
	// display: block;
	// overflow: hidden;
	// &::before {
	// 	content: '';
	// 	display: block;
	// 	padding-top: 100%;
	// 	pointer-events: none;
	// }
	// > *:is(img, lite-youtube, lite-vimeo, video, iframe),
	// &__media {
	// 	position: absolute;
	// 	top: 0;
	// 	left: 0;
	// 	width: 100%;
	// 	height: 100%;
	// 	@supports (object-fit: cover) {
	// 		object-fit: cover;
	// 	}
	// }

	// MODIF
	&--2-1 {
		aspect-ratio: 2/1;
	}
	&--4-3 {
		aspect-ratio: 4/3;
	}
	&--9-16 {
		aspect-ratio: 9/16;
	}
	&--16-9 {
		aspect-ratio: 16/9;
	}
	&--7-3 {
		aspect-ratio: 7/3;
	}
	// &--4-3::before {
	// 	padding-top: percentage(calc(3 / 4));
	// }
	// &--9-16::before {
	// 	padding-top: percentage(calc(16 / 9));
	// }
	// &--16-9::before {
	// 	padding-top: percentage(calc(9 / 16));
	// }
	// &--21-9::before {
	// 	padding-top: percentage(calc(9 / 21));
	// }

	// &--contain > *:is(img, lite-youtube, lite-vimeo, video, iframe),
	// &--contain &__media {
	// 	object-fit: contain;
	// }
	&--contain {
		object-fit: contain;
	}
	&--circle {
		border-radius: 50%;
	}
	&--rounded {
		border-radius: variables.$border-radius-sm;
	}
	&--fit {
		width: 100%;
		height: 100%;
	}

	// MQ
	@media (config.$md-up) {
		&--rounded {
			border-radius: variables.$border-radius-xl;
		}
	}
}
