@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.paging {
	font-size: 1.2rem;
	&__row {
		display: grid;
		grid-template-columns: minmax(0, 1fr) max-content minmax(0, 1fr);
		align-items: center;
	}
	&__pages {
		grid-area: 1/1/1/1;
	}
	&__btn {
		grid-area: 1/2/1/3;
	}
	&__up {
		grid-area: 1/3/1/4;
		text-align: right;
	}
	&__count {
		margin: 1rem 0 0;
	}
}
