@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.progress {
	--progress-h: 0.6rem;
	--progress-bg: #{variables.$color-bg};
	--progress-bg-active: #{variables.$color-primary};
	--progress-point-bd: #{variables.$color-white};
	--progress-point-bg: #{variables.$color-primary};
	display: flex;
	&__bar {
		position: relative;
		flex: 1;
		height: var(--progress-h);
		border-radius: var(--progress-h);
		background: var(--progress-bg);
		&--active {
			position: absolute;
			top: 0;
			bottom: 0;
			left: 0;
			background: var(--progress-bg-active);
		}
	}
	&__point {
		position: absolute;
		top: 50%;
		z-index: 1;
		width: 1rem;
		height: 1rem;
		border: 0.275rem solid var(--progress-point-bd);
		border-radius: 50%;
		background: var(--progress-point-bg);
		transform: translate(-50%, -50%);
	}
	&__bubble {
		position: absolute;
		top: 1.5rem;
		right: -1.2rem;
		display: inline-block;
		min-width: 3rem;
		padding: 0.3rem 0.5rem;
		border-radius: 1rem;
		background: variables.$color-white;
		color: variables.$color-help;
		font-size: 1rem;
		line-height: 1.3;
		white-space: nowrap;
		text-align: right;
		box-shadow: 0 0.1rem 0.1rem 0 rgba(0, 14, 71, 0.1);
		&::before {
			content: '';
			position: absolute;
			top: -0.4rem;
			right: 1rem;
			z-index: -1;
			width: 0;
			height: 0;
			border-width: 0 0.5rem 0.5rem;
			border-style: solid;
			border-color: transparent transparent variables.$color-white;
		}
		.item-icon {
			--icon-size: 1.5rem;
			--gap: 0.6rem;
		}
	}

	// STATES
	&__bubble.is-flipped {
		right: auto;
		left: -1.2rem;
		&::before {
			right: auto;
			left: 1rem;
		}
	}
	&__point.is-active {
		background: var(--progress-bg-active);
	}
	&__point.is-active &__bubble {
		color: var(--progress-bg-active);
	}

	// MQ
	@media (config.$md-up) {
		&__bubble {
			padding: 0.5rem 0.6rem;
			font-size: 1.1rem;
		}
	}
}
