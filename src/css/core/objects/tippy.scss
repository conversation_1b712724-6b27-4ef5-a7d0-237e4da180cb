@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.tooltip {
	--tooltip-icon-size: 1.2rem;
	display: inline-flex;
	vertical-align: text-bottom;
	&__btn {
		--color-link: #{variables.$color-text};
		--color-hover: #{variables.$color-help};
		display: inline-flex;
	}
	&__icon {
		width: var(--tooltip-icon-size);
	}

	// MODIF
	&--gray &__btn {
		--color-link: #{variables.$color-help};
		--color-hover: #{variables.$color-text};
	}
	&--gray-light &__btn {
		--color-link: #{variables.$color-icon-minor};
		--color-hover: #{variables.$color-help};
	}
}
.tippy-box[data-animation='fade'][data-state='hidden'] {
	visibility: hidden;
	opacity: 0;
}
[data-tippy-root] {
	width: min(35rem, calc(100vw - (variables.$row-main-gutter * 2)));
}
.tippy-box {
	position: relative;
	width: 100%;
	max-width: none !important;
	border: 0.1rem solid variables.$color-tile-light;
	border-radius: variables.$border-radius-xl;
	background: variables.$color-white;
	color: variables.$color-text;
	font-size: 1.4rem;
	line-height: variables.$line-height;
	text-align: left;
	transition: opacity variables.$t, visibility variables.$t;
	filter: drop-shadow(0 0.9rem 1.4rem rgba(0, 14, 71, 0.1)) drop-shadow(0 0.2rem 0.2rem rgba(0, 14, 71, 0.04));
	backdrop-filter: blur(10rem);
}
.tippy-arrow {
	width: 1.3rem;
	height: 1.3rem;
	color: variables.$color-white;
	&::before,
	&::after {
		content: '';
		position: absolute;
		width: 100%;
		height: 100%;
		background: variables.$color-tile-light;
		transform: rotate(-45deg);
	}
	&::after {
		background: variables.$color-white;
	}
}
.tippy-content {
	position: relative;
	z-index: 1;
	padding: 3.2rem;
}

// MODIF
.tippy-box[data-placement^='top'] > .tippy-arrow {
	bottom: -0.7rem;
	&::after {
		bottom: 0.1rem;
	}
}
.tippy-box[data-placement^='bottom'] > .tippy-arrow {
	top: -0.7rem;
	&::after {
		top: 0.1rem;
	}
}
.tippy-box[data-placement^='left'] > .tippy-arrow {
	right: -0.7rem;
	&::after {
		right: 0.1rem;
	}
}
.tippy-box[data-placement^='right'] > .tippy-arrow {
	left: -0.7rem;
	&::after {
		left: 0.1rem;
	}
}
