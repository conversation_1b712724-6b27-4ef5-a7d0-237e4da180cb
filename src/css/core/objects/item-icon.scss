@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.item-icon {
	--gap: 0.8rem;
	--icon-size: 2rem;
	--icon-color: inherit;
	--icon-offset: 0rem;
	$s: &;
	display: inline-flex;
	gap: var(--gap);
	align-items: center;
	&__icon {
		top: var(--icon-offset);
		flex: 0 0 auto;
		width: var(--icon-size);
		height: var(--icon-size);
		color: var(--icon-color);
		transition: transform variables.$t;
		object-fit: cover;
	}

	// MODIF
	&.u-ta-c {
		display: flex;
		justify-content: center;
		text-align: left;
	}

	// VARIANTS
	&--arrow {
		--icon-size: 1rem;
		--gap: 0.6rem;
	}
	&--circle &__icon {
		border-radius: 50%;
	}
}
