@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.section {
	--cell-width: 29rem;
	--line-offset: 3.2rem;
	$s: &;
	&__carousel {
		--arrow-position: calc(var(--row-main-gutter) / -2);
		z-index: 1;
		overflow: visible;
	}
	&__cell {
		width: var(--cell-width);
		&::before {
			content: '';
			position: absolute;
			top: var(--line-offset);
			bottom: var(--line-offset);
			left: 100%;
			width: 0.1rem;
			background: variables.$color-tile-light;
		}
		&:last-child::before {
			content: none;
		}
	}
	// &__placeholder {
	// 	min-height: 36.4rem;
	// }

	// MODIF
	&--bd {
		padding: 3.2rem 0;
		border: 0.1rem solid variables.$color-tile-light;
		border-width: 0.1rem 0;
	}

	// MQ
	@media (config.$md-up) {
		--cell-width: 36rem;
		&__title {
			text-align: center;
		}
		&__carousel {
			--arrow-position: calc((var(--vw) - 100%) / -2 + 2rem);
		}
		// &__placeholder {
		// 	min-height: 40.2rem;
		// }

		// MODIF
		&--bd {
			padding: 7rem 0;
		}
	}
}
