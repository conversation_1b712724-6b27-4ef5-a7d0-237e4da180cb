@use 'base/variables';

.block-loader {
	position: relative;
	transition: opacity variables.$t;
	&__loader {
		position: absolute;
		top: 50%;
		left: 50%;
		width: 8rem;
		height: 8rem;
		border-radius: 4rem;
		background: rgba(#000000, 0.5);
		visibility: hidden;
		opacity: 0;
		transform: translate(-50%, -50%);
		transition: opacity variables.$t 0s, visibility variables.$t 0s;
		&::after {
			content: '';
			position: absolute;
			top: 50%;
			left: 50%;
			width: 4rem;
			height: 4rem;
			margin: -2rem 0 0 -2rem;
			border: 0.4rem solid #ffffff;
			border-radius: 2rem;
			border-top-color: transparent;
		}
	}

	// STATES
	&.is-loading {
		opacity: 0.75;
		pointer-events: none;
	}
	&.is-loading &__loader {
		visibility: visible;
		opacity: 1;
		transition-delay: 0s, 0s;
		&::after {
			animation: animation-rotate 0.8s infinite linear;
		}
	}
}

.body-loader {
	&__loader {
		position: fixed;
		top: 50%;
		left: 50%;
		z-index: 10000;
		width: 8rem;
		height: 8rem;
		border-radius: 4rem;
		background: rgba(#000000, 0.5);
		visibility: hidden;
		opacity: 0;
		transform: translate(-50%, -50%);
		transition: opacity variables.$t 0s, visibility variables.$t 0s;
		&::after {
			content: '';
			position: absolute;
			top: 50%;
			left: 50%;
			width: 4rem;
			height: 4rem;
			margin: -2rem 0 0 -2rem;
			border: 0.4rem solid #ffffff;
			border-radius: 2rem;
			border-top-color: transparent;
		}
	}

	// STATES
	.is-loading &__loader {
		visibility: visible;
		opacity: 1;
		transition-delay: 0s, 0s;
		&::after {
			animation: animation-rotate 0.8s infinite linear;
		}
	}
}
