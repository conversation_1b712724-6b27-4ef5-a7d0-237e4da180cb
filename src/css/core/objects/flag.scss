@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

@property --stop1 {
	syntax: '<color>';
	inherits: true;
	initial-value: rgba(0, 0, 0, 0);
}
@property --stop2 {
	syntax: '<color>';
	inherits: true;
	initial-value: rgba(0, 0, 0, 0);
}

.flags {
	@extend %reset-ul;
	display: flex;
	gap: 0.2rem;
	flex-direction: column;
	li {
		@extend %reset-ul-li;
		display: flex;
	}

	// MODIF
	&--row {
		flex-direction: row;
		flex-wrap: wrap;
	}
}
.flag {
	--flag-theme-color: #{variables.$color-black};
	--flag-hover-bg: oklch(from var(--flag-bg) calc(l * 0.75) c h);
	--flag-hover-color: #{variables.$color-white};
	--flag-bd: transparent;
	--flag-color: #{variables.$color-white};
	--flag-bg: var(--flag-theme-color);
	--flag-padding: 0.2rem 0.7rem;
	--flag-h: 2.4rem;
	--flag-gap: 0.2rem;
	--flag-fs: 1.3rem;
	--flag-icon-size: 1.5rem;
	$s: &;
	display: inline-flex;
	vertical-align: middle;
	gap: var(--flag-gap);
	align-items: center;
	min-height: var(--flag-h);
	padding: var(--flag-padding);
	border: 0.1rem solid var(--flag-bd);
	border-radius: 50rem;
	background: var(--flag-bg);
	color: var(--flag-color);
	font-weight: 700;
	font-size: var(--flag-fs);
	line-height: 1;
	text-decoration: none;
	transition: color variables.$t, background-color variables.$t, border-color variables.$t, --stop1 variables.$t, --stop2 variables.$t;
	&__icon {
		flex: 0 0 auto;
		width: var(--flag-icon-size);
		height: var(--flag-icon-size);
		transition: none;
	}

	// MODIF
	// color
	&--gray-light {
		--flag-theme-color: #{variables.$color-bg};
		--flag-color: #{variables.$color-text};
	}
	&--gray-light#{&}--bd {
		--flag-theme-color: #{variables.$color-bd};
		--flag-color: #{variables.$color-text};
	}
	&--rainbow {
		--stop1: #e83d5c;
		--stop2: #7d25c2;
		--flag-bg: linear-gradient(88.35deg, var(--stop1) 0%, var(--stop2) 100%);
		--flag-hover-bg: var(--flag-bg);
		border: none;
	}
	&--yellow-gradient {
		--flag-color: #{variables.$color-text};
		--flag-hover-color: #{variables.$color-text};
		--stop1: #{variables.$color-yellow-600};
		--stop2: #ffe55a;
		--flag-bg: linear-gradient(88.35deg, var(--stop1) 0%, var(--stop2) 100%);
		--flag-hover-bg: var(--flag-bg);
		border: none;
	}
	&--drak {
		--stop1: rgba(0, 0, 0, 0);
		--flag-bg: linear-gradient(90deg, var(--stop1) 40%, var(--stop2) 100%),
			#{url(variables.$img-path + 'illust/drak.jpg') no-repeat center / cover};
		--flag-hover-bg: var(--flag-bg);
		border: none;
	}
	&--purple-light {
		--flag-bg: #{variables.$color-violet-200};
		--flag-color: #{variables.$color-violet};
		--flag-hover-bg: #{variables.$color-violet};
	}
	&--green-light {
		--flag-bg: #{variables.$color-green-200};
		--flag-color: #{variables.$color-green};
		--flag-hover-bg: #{variables.$color-green};
	}
	&--blue-light {
		--flag-bg: #{variables.$color-primary-150};
		--flag-color: #{variables.$color-primary};
		--flag-hover-bg: #{variables.$color-primary};
	}
	&--orange-light {
		--flag-bg: #{variables.$color-peach-150};
		--flag-color: #{variables.$color-peach};
		--flag-hover-bg: #{variables.$color-peach};
	}
	&--red-light {
		--flag-bg: #{variables.$color-status-invalid-light};
		--flag-color: #{variables.$color-status-invalid};
		--flag-hover-bg: #{variables.$color-status-invalid};
	}
	&--green {
		--flag-theme-color: #{variables.$color-green};
	}
	&--blue {
		--flag-theme-color: #{variables.$color-blue};
	}
	&--red {
		--flag-theme-color: #{variables.$color-red};
	}
	&--white {
		--flag-theme-color: #{variables.$color-white};
		--flag-color: #{variables.$color-text};
	}
	&--gray {
		--flag-theme-color: #{variables.$color-bg};
		--flag-color: #{variables.$color-text};
	}
	&--remove {
		--flag-bg: #{variables.$color-white};
		--flag-color: #{variables.$color-text};
		--flag-hover-bg: #{variables.$color-red};
		--flag-bd: #{variables.$color-tile};
		font-weight: 400;
		#{$s}__icon {
			color: variables.$color-status-invalid;
		}
	}
	// style
	&--bd {
		--flag-bg: transparent;
		--flag-bd: var(--flag-theme-color);
		--flag-color: var(--flag-theme-color);
		--flag-hover-bg: var(--flag-theme-color);
		--flag-hover-color: #{variables.$color-white};
	}
	&--type {
		position: absolute;
		top: 0;
		right: 0;
		border-radius: 0 variables.$border-radius-lg 0 variables.$border-radius-lg;
		outline: 0.4rem solid variables.$color-white;
	}
	// size
	&--sm {
		--flag-fs: 1.1rem;
		--flag-h: 2.2rem;
	}

	// HOVERS
	@at-root a#{&} {
		.hoverevents &:hover {
			border-color: var(--flag-hover-bg);
			background: var(--flag-hover-bg);
			color: var(--flag-hover-color);
		}
		.hoverevents &--rainbow:hover {
			--stop2: #e83d5c;
		}
		.hoverevents &--yellow-gradient:hover {
			--stop2: #fcc73e;
		}
		.hoverevents &--remove:hover #{$s}__icon {
			color: variables.$color-white;
		}
		.hoverevents &--drak:hover {
			--stop1: rgba(0, 0, 0, 0.5);
		}
	}

	// MQ
	@media (config.$md-up) {
		// MODIF
		&--sm {
			--flag-fs: 1.3rem;
			--flag-h: 2.4rem;
		}
		&--md {
			--flag-h: 2.7rem;
			--flag-gap: 0.4rem;
			--flag-icon-size: 2rem;
			--flag-padding: 0.2rem 1rem;
		}
	}
}
