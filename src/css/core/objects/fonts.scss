@use 'base/variables';

// @font-face {
//     font-family: Poppins;
//     font-weight: 600;
//     font-style: normal;
//     src: url(variables.$fonts-path + 'poppins/poppins-semibold.woff2') format('woff2');
//     font-display: fallback;
// }
// @font-face {
//     font-family: Poppins;
//     font-weight: 700;
//     font-style: normal;
//     src: url(variables.$fonts-path + 'poppins/poppins-bold.woff2') format('woff2');
//     font-display: fallback;
// }
// @font-face {
//     font-family: Poppins;
//     font-weight: 800;
//     font-style: normal;
//     src: url(variables.$fonts-path + 'poppins/poppins-extrabold.woff2') format('woff2');
//     font-display: fallback;
// }

// @font-face {
//     font-family: Inter;
//     font-style: normal;
//     src: url(variables.$fonts-path + 'inter/inter.woff2') format('woff2');
//     font-display: fallback;
// }
