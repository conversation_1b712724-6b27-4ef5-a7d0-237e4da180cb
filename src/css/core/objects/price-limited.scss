@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.price-limited {
	--price-fs: 1.1rem;
	--price-large-fs: 1.5rem;
	font-size: var(--price-fs);
	line-height: 1.4;
	& > * {
		margin: 0;
	}
	&__price {
		display: block;
		font-family: variables.$font-secondary;
		font-size: var(--price-large-fs);
	}

	// MQ
	@media (config.$md-up) {
		--price-fs: 1.3rem;
		--price-large-fs: 1.8rem;
	}
}
