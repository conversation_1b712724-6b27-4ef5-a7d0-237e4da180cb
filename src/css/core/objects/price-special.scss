@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.price-special {
	--price-special-fs-top: 1rem;
	--price-special-fs-old: 1.2rem;
	--price-special-fs: 1.8rem;
	$s: &;
	margin: 0;
	&__box {
		display: block;
		border-radius: variables.$border-radius-md;
		overflow: clip;
	}
	&__top {
		display: block;
		padding: 0.2rem 1.2rem 0.1rem;
		background: linear-gradient(88.35deg, variables.$color-yellow-600 0%, #ffe55a 98.5%);
		font-size: var(--price-special-fs-top);
		text-transform: uppercase;
	}
	&__old {
		display: block;
		font-size: var(--price-special-fs-old);
	}
	&__main {
		display: block;
		padding: 0.4rem 1.2rem 0.6rem;
		background: variables.$color-black;
		color: variables.$color-white;
		line-height: 1.1;
		strong {
			display: block;
			font-size: var(--price-special-fs);
		}
	}
	&__notax {
		display: block;
		padding-top: 0.4rem;
		color: variables.$color-help;
		font-size: var(--price-special-fs-novat);
	}

	// MODIF
	&--bomb &__main {
		background: linear-gradient(88.35deg, variables.$color-yellow-600 0%, #e83d5c 50.5%);
	}
	&--detail {
		--price-special-fs-top: 1.2rem;
		--price-special-fs-old: 1.2rem;
		--price-special-fs: 2.8rem;
		--price-special-fs-novat: 1.2rem;
		min-width: 26rem;
		text-align: center;
		#{$s}__top {
			padding: 0.7rem 1.2rem 0.4rem;
			text-transform: none;
		}
		#{$s}__main {
			padding: 1.1rem 1.2rem;
		}
	}

	// MQ
	@media (config.$md-up) {
		&--detail {
			--price-special-fs-top: 1.3rem;
			--price-special-fs-old: 1.3rem;
			--price-special-fs: 3.4rem;
			--price-special-fs-novat: 1.3rem;
		}
	}
}
