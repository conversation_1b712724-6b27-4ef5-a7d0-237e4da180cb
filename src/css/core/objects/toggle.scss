@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.toggle {
	&__btn.as-link {
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 100%;
		min-height: 6.2rem;
		margin: 0;
		padding: 1.6rem 2rem;
		border-radius: variables.$border-radius-sm;
		background: variables.$color-bg-light;
		color: inherit;
		font-weight: inherit;
		font-size: 1.5rem;
		text-decoration: none;
		.item-icon {
			--gap: 1.2rem;
			--icon-size: 2rem;
			--icon-color: #{variables.$color-black};
		}
	}
	&__arrow {
		color: variables.$color-link;
		transition: transform variables.$t;
	}

	// MODIF
	&--filter &__btn {
		padding: 1.5rem;
	}

	// STATES
	.is-open &__btn {
		border-bottom: 0.1rem solid variables.$color-bg;
		border-radius: variables.$border-radius-sm variables.$border-radius-sm 0 0;
	}
	.is-open &__arrow {
		transform: scale(-1);
	}
	.is-open &--filter &__btn {
		position: relative;
		border: none;
		&::after {
			content: '';
			position: absolute;
			right: 1.5rem;
			bottom: 0;
			left: 1.5rem;
			height: 0.1rem;
			background: variables.$color-bg;
		}
	}
}
