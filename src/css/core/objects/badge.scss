@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.badge {
	position: relative;
	display: inline-block;
	align-content: center;
	width: 5.8rem;
	margin: 0;
	padding: 0.6rem;
	font-weight: bold;
	font-size: 1rem;
	line-height: 1.2;
	text-align: center;
	aspect-ratio: 1/1;
	mask: url(variables.$img-path + 'illust/badge-outline.svg');
	mask-repeat: no-repeat;
	mask-position: center;
	&__icon {
		position: absolute;
		z-index: -1;
		width: 100%;
		inset: 0;
	}

	// MODIF
	&--yellow {
		background: linear-gradient(88.35deg, variables.$color-yellow-600 0%, #ffe55a 98.5%);
	}
	&--red {
		background: variables.$color-red;
		color: variables.$color-white;
	}
	&--rainbow {
		background: linear-gradient(88.35deg, #e83d5c 0%, #7d25c2 100%);
		color: variables.$color-white;
	}
	&--outline {
		background: url(variables.$img-path + 'illust/badge-outline.svg');
		background-position: center;
		background-repeat: no-repeat;
		background-size: cover;
		color: variables.$color-primary;
		mask: none;
	}
}
