/* stylelint-disable declaration-no-important */
@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.nouislider {
	height: 0.4rem;
	padding: 0 1rem; // zarovnani hondle ke kraji
}
.nouislider-holder {
	margin: 0 0 1.2rem;
	padding: 0.8rem 0;
}
.noUi-horizontal {
	height: 0.4rem;
}
.noUi-base,
.noUi-connects {
	position: relative;
	z-index: 1;
	width: 100%;
	height: 100%;
}
.noUi-connect,
.noUi-origin {
	position: absolute;
	top: 0;
	right: 0;
	z-index: 1;
	width: 100%;
	height: 100%;
	transform-origin: 0 0;
	transform-style: flat;
	will-change: transform;
}
.noUi-txt-dir-rtl.noUi-horizontal .noUi-origin {
	right: auto;
	left: 0;
}
.noUi-horizontal .noUi-origin {
	height: 0;
}
.noUi-touch-area {
	width: 100%;
	height: 100%;
}
.noUi-state-tap .noUi-connect,
.noUi-state-tap .noUi-origin {
	transition: transform 0.3s;
}
.noUi-state-drag * {
	cursor: inherit !important;
}
.noUi-horizontal .noUi-handle {
	top: 0;
	right: -1rem;
	width: 2rem;
	height: 2rem;
	transform: translateY(-50%) translateY(0.2rem);
}
.noUi-target {
	border-radius: 1rem;
	background: variables.$color-bd;
}
.noUi-connects {
	border-radius: 3px;
}
.noUi-connect {
	background: variables.$color-primary;
}
.noUi-draggable {
	cursor: ew-resize;
}
.noUi-handle {
	position: absolute;
	border: 0.4rem solid variables.$color-primary;
	border-radius: 50%;
	background: variables.$color-white;
	backface-visibility: hidden;
}
[disabled] .noUi-connect {
	background: #b8b8b8;
}
[disabled] .noUi-handle,
[disabled].noUi-handle,
[disabled].noUi-target {
	cursor: not-allowed;
}
.noUi-pips,
.noUi-pips * {
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}
.noUi-pips {
	position: absolute;
	color: #999999;
}
.noUi-value {
	position: absolute;
	white-space: nowrap;
	text-align: center;
}
.noUi-value-sub {
	color: #cccccc;
	font-size: 10px;
}
.noUi-marker {
	position: absolute;
	background: #cccccc;
}
.noUi-marker-sub {
	background: #aaaaaa;
}
.noUi-marker-large {
	background: #aaaaaa;
}
.noUi-pips-horizontal {
	top: 100%;
	left: 0;
	width: 100%;
	height: 80px;
	padding: 10px 0;
}
.noUi-value-horizontal {
	transform: translate(-50%, 50%);
}
.noUi-rtl .noUi-value-horizontal {
	transform: translate(50%, 50%);
}
.noUi-marker-horizontal.noUi-marker {
	width: 2px;
	height: 5px;
	margin-left: -1px;
}
.noUi-marker-horizontal.noUi-marker-sub {
	height: 10px;
}
.noUi-marker-horizontal.noUi-marker-large {
	height: 15px;
}
.noUi-tooltip {
	display: none;
}
.noUi-horizontal .noUi-tooltip {
	bottom: 120%;
	left: 50%;
	transform: translate(-50%, 0);
}
.noUi-horizontal .noUi-origin > .noUi-tooltip {
	bottom: 10px;
	left: auto;
	transform: translate(50%, 0);
}
