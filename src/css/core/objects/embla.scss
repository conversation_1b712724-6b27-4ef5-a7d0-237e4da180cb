@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.embla {
	--arrow-position: 0.5rem;
	--dot-bg: #{variables.$color-icon-minor};
	--dot-bg-active: #{variables.$color-black};
	--btn-size: 4.1rem;
	$s: &;
	position: relative;
	overflow: hidden;
	&__viewport {
		min-width: 100%;
		&.is-draggable {
			cursor: move;
			cursor: grab;
		}
		&.is-dragging {
			cursor: grabbing;
			pointer-events: none;
		}
		& > * {
			pointer-events: auto;
		}
	}
	&__container {
		transform: translateZ(0);
		will-change: transform;
		.js &.grid--scroll {
			overflow: visible;
		}
	}
	&__btn {
		@include mixins.button-reset;
		position: absolute;
		top: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		width: var(--btn-size);
		height: var(--btn-size);
		border-radius: 50%;
		background: mix(variables.$color-black, variables.$color-white, 80%);
		color: variables.$color-white;
		overflow: hidden;
		transform: translateY(-50%);
		transition: opacity variables.$t, visibility variables.$t, background-color variables.$t, color variables.$t;
		cursor: pointer;
		&[disabled] {
			visibility: hidden;
			opacity: 0;
		}
		.icon-svg {
			width: 38%;
			aspect-ratio: 1/1;
		}
	}
	&__progress {
		position: relative;
		max-width: 102rem;
		height: 0.4rem;
		margin: 3.2rem auto 0;
		border-radius: 5rem;
		background: variables.$color-bg;
		overflow: hidden;
	}
	&__progress-inner {
		position: absolute;
		top: 0;
		bottom: 0;
		left: 0;
		border-radius: 5rem;
		background: variables.$color-black;
		transition: width variables.$t;
	}
	&__dots {
		--dot-gap: 0.6rem;
		display: flex;
		justify-content: center;
		margin: 1.1rem 0 calc(var(--dot-gap) * -1);
		font-size: 0;
	}
	&__dot {
		@include mixins.button-reset;
		flex: 0 0 auto;
		padding: var(--dot-gap);
		font-size: 0;
		cursor: pointer;
		-webkit-tap-highlight-color: transparent;
	}
	&__dot-inner {
		position: relative;
		display: block;
		width: 0.8rem;
		height: 0.8rem;
		border-radius: 0.8rem;
		overflow: hidden;
		transition: width variables.$t;
		&::before,
		&::after {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			border-radius: 0.8rem;
			background: var(--dot-bg);
			transition: background-color variables.$t;
		}
		&::after {
			content: none;
			transform: translateX(-100%);
		}
	}

	// VARIANTs
	&__btn--prev {
		left: var(--arrow-position);
	}
	&__btn--next {
		right: var(--arrow-position);
	}

	// STATES
	&__container.grid--center {
		justify-content: flex-start;
		& > *:first-child {
			margin-left: auto;
		}
		& > *:last-child {
			margin-right: auto;
		}
	}
	&__dots.is-disabled {
		visibility: hidden;
		opacity: 0;
	}
	&__dot.is-selected &__dot-inner {
		&::before,
		&::after {
			background: var(--dot-bg-active);
		}
	}
	&[data-embla-autoplay-value] &__dot.is-selected &__dot-inner {
		width: 2.4rem;
		&::before {
			background: var(--dot-bg);
		}
		&::after {
			content: '';
			// width: var(--slide-progress);
			transform: translateX(-100%) translateX(var(--slide-progress));
		}
	}
	&__btn:focus,
	.hoverevents &__btn:focus:hover {
		background: variables.$color-black;
	}
	.no-js &__btn,
	&:not(.is-initialized) &__btn,
	&.is-disabled &__btn {
		display: none;
	}

	// HOVERs
	.hoverevents &__btn:hover {
		background: variables.$color-black;
		color: variables.$color-white;
	}

	// MQ
	@media (config.$lg-down) {
		&__progress {
			display: none;
		}
	}
	@media (config.$md-up) {
		--arrow-position: 2rem;
		--btn-size: 5.2rem;
	}
}
