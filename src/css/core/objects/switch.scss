@use 'config';
@use 'base/variables';

.switch {
	--width: 2.8rem;
	--height: 1.8rem;
	--thumb-size: 1.4rem;
	--gap: 0.8rem;
	position: relative;
	display: flex;
	gap: var(--gap);
	align-items: center;
	color: variables.$color-placeholder;
	font-size: 1.3rem;
	cursor: pointer;
	user-select: none;
	&__inp {
		position: absolute;
		left: -500rem;
	}
	&__label {
		margin-right: auto;
		padding-right: 2rem;
		color: rgba(variables.$color-white, 0.6);
	}
	&__inner {
		position: relative;
		display: flex;
		border-radius: var(--height);
		background: variables.$color-placeholder;
		transition: background-color variables.$t;
		cursor: pointer;
	}
	&__text:first-child {
		text-align: right;
	}
	&__bg {
		display: flex;
		flex: 1 1 auto;
		justify-content: center;
		align-items: center;
		width: calc(var(--width) / 2);
		height: var(--height);
		text-align: center;
		opacity: 0;
		transition: opacity variables.$t;
		.icon-svg {
			position: relative;
			z-index: 1;
			svg {
				fill: currentcolor;
			}
		}
	}
	&__tool {
		position: absolute;
		top: 0.2rem;
		bottom: 0.2rem;
		left: 0.2rem;
		width: var(--thumb-size);
		border-radius: 50%;
		background: variables.$color-white;
		transition: transform variables.$t;
		box-shadow: 0 0.1rem 0.4rem 0 rgba(black, 0.1);
	}

	// MODIF
	&__bg--right {
		opacity: 1;
	}
	&--lg {
		--width: 4rem;
		--height: 2.6rem;
		--thumb-size: 2.2rem;
		--gap: 1.2rem;
	}

	// STATES
	&__inp:focus + &__inner {
		border-color: variables.$color-text;
	}
	&__inp:checked + &__inner {
		background-color: rgba(0, 148, 0, 1);
	}

	&__inp:checked + &__inner &__tool {
		transform: translateX(calc((var(--width) - var(--thumb-size)) - 0.4rem));
	}
	&__inp:checked + &__inner &__bg--left {
		opacity: 1;
	}
	&__inp:checked + &__inner &__bg--right {
		opacity: 0;
	}
	&__inp:disabled + &__inner {
		opacity: 0.5;
		pointer-events: none;
	}
}
