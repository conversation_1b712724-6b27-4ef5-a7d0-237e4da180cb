@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.tabs {
	$s: &;
	overflow-x: auto;
	&__menu {
		display: flex;
		gap: 0.4rem;
		margin: 0;
		border-bottom: 0.1rem solid variables.$color-tile;
		font-size: 1.3rem;
	}
	&__link {
		--color-link: #{variables.$color-primary};
		--color-hover: #{variables.$color-black};
		position: relative;
		bottom: -0.1rem;
		z-index: 1;
		display: flex;
		gap: 0.4rem;
		justify-content: center;
		align-items: center;
		min-height: 5.8rem;
		padding: 1.4rem;
		border-radius: variables.$border-radius-lg variables.$border-radius-lg 0 0;
		background: variables.$color-primary-150;
		font-weight: bold;
		text-align: center;
		text-decoration: none;
		overflow: clip;
		transition: border-color variables.$t, color variables.$t, background-color variables.$t;
		&::before,
		&::after {
			content: '';
			position: absolute;
			z-index: -1;
			inset: 0;
			pointer-events: none;
		}
		&::before {
			border: 0.1rem solid transparent;
			border-radius: variables.$border-radius-lg variables.$border-radius-lg 0 0;
			border-color: transparent transparent variables.$color-tile;
			transition: background-color variables.$t, border-color variables.$t;
		}
		&::after {
			height: 0.4rem;
			background: variables.$color-primary;
			opacity: 0;
			transition: opacity variables.$t;
		}
	}
	&__count {
		display: flex;
		justify-content: center;
		align-items: center;
		min-width: 2.2rem;
		height: 2.2rem;
		padding: 0 0.8rem;
		border-radius: 10rem;
		background: variables.$color-white;
		color: variables.$color-text;
		font-weight: 400;
		font-size: 1.1rem;
		transition: background-color variables.$t;
	}

	// MODIF
	&__menu--fill &__link {
		flex: 1;
	}
	&__link--zone {
		background: linear-gradient(90deg, #fdf0ed 0%, #f3e5ff 100%);
		&::after {
			background: linear-gradient(96.35deg, #621298 0%, #7d25c2 10.5%, #e83d5c 45%, #fe995f 100%);
		}
	}

	// STATES
	&__link.is-selected {
		// background: variables.$color-white;
		color: variables.$color-black;
		&::before {
			border-color: variables.$color-tile variables.$color-tile transparent;
			background: variables.$color-white;
		}
		&::after {
			opacity: 1;
		}
		#{$s}__count {
			background: variables.$color-primary-150;
		}
	}
	&__content:not(.is-active) {
		display: none;
	}

	// HOVERS
	.hoverevents &__link:hover {
		color: variables.$color-black;
		text-decoration: none;
	}

	// MQ
	@media (config.$md-up) {
		&__menu {
			gap: 0.8rem;
			font-size: 1.6rem;
		}
		&__link {
			padding: 1.6rem 3.2rem;
		}

		// MODIF
		&--lg &__link {
			padding: 2.3rem 3.2rem;
		}
	}
}
