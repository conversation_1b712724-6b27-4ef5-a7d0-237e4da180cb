@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.bubble {
	position: relative;
	display: inline-block;
	padding: 0 0.6rem;
	border-radius: 1rem 1.3rem 1.3rem;
	background: variables.$color-primary;
	color: variables.$color-white;
	font-size: 1rem;
	line-height: 1.8;
	&__ear {
		position: absolute;
		bottom: 50%;
		left: 0;
		width: 0.733rem;
		height: 1.4rem;
		color: variables.$color-primary;
	}
}
