@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.play {
	$s: &;
	position: absolute;
	top: 50%;
	left: 50%;
	z-index: 1;
	width: 38.5%;
	max-width: 7.3rem;
	transform: translate(-50%, -50%);
	&__icon {
		position: absolute;
		top: 50%;
		left: 50%;
		width: 100%;
		color: variables.$color-white;
		transform: translate(-50%, -50%);
	}

	// MODIF
	&--other,
	&--transparent {
		max-width: 9rem;
		border: 0.1rem solid rgba(variables.$color-white, 0.2);
		border-radius: 50%;
		background: rgba(variables.$color-primary-700, 0.8);
		aspect-ratio: 1/1;
		backdrop-filter: blur(1rem);
		#{$s}__icon {
			width: 44%;
			color: variables.$color-white;
			transform: translate(-50%, -50%);
		}
	}
	&--transparent {
		background: rgba(white, 0.2);
		box-shadow: 0 0.2rem 0.2rem 0 rgba(0, 14, 71, 0.04), 0 0.6rem 0.7rem 0 rgba(0, 14, 71, 0.08);
	}
}
