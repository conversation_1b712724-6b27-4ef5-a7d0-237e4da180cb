@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.row-main {
	@include mixins.clearfix();
	position: relative;
	width: 100%;
	max-width: variables.$row-main-width;
	margin: 0 auto;
	padding: 0 variables.$row-main-gutter;
	// .full-width {
	// 	position: relative;
	// 	left: 50%;
	// 	width: var(--vw, 100vw);
	// 	margin-left: calc(var(--vw, 100vw) * -0.5);
	// }
	&--wide {
		max-width: variables.$row-main-width-wide;
	}
}
.grid {
	--grid-x-spacing: #{variables.$grid-gutter};
	--grid-y-spacing: #{variables.$grid-gutter};
	@extend %reset-ol;
	@extend %grid;
	margin-bottom: calc(var(--grid-y-spacing) * -1);
	margin-left: calc(var(--grid-x-spacing) * -1);
	&__cell {
		@extend %reset-ol-li;
		@extend %grid__cell;
		position: relative;
		border: var(--grid-x-spacing) solid transparent;
		border-width: 0 0 var(--grid-y-spacing) var(--grid-x-spacing);

		// hide the border in MS high contrast mode
		border-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg'%3E%3C/svg%3E");
		&--top {
			align-self: flex-start;
		}
		&--middle {
			align-self: center;
		}
		&--bottom {
			align-self: flex-end;
		}
		&--eq {
			display: flex;
			> * {
				flex: 1 1 auto;
			}
		}
	}

	// VARIANTs
	&--scroll {
		@extend %grid--scroll;
	}
	&--nowrap {
		flex-wrap: nowrap;
	}
	&--middle {
		align-items: center;
	}
	&--bottom {
		align-items: flex-end;
	}
	&--center {
		justify-content: center;
	}
	&--right {
		justify-content: flex-end;
	}
	&--space-between {
		justify-content: space-between;
	}
	&--reverse {
		flex-direction: row-reverse;
	}
	@each $index, $value in variables.$utils-spacing-grid {
		&--x-#{$index} {
			--grid-x-spacing: #{$value};
		}
		&--y-#{$index} {
			--grid-y-spacing: #{$value};
		}
	}
	@media (config.$md-up) {
		@each $index, $value in variables.$utils-spacing-grid {
			&--x-#{$index} {
				@include mixins.suffix('md') {
					--grid-x-spacing: #{$value};
				}
			}
			&--y-#{$index} {
				@include mixins.suffix('md') {
					--grid-y-spacing: #{$value};
				}
			}
		}
	}
	@media (config.$lg-up) {
		@each $index, $value in variables.$utils-spacing-grid {
			&--x-#{$index} {
				@include mixins.suffix('lg') {
					--grid-x-spacing: #{$value};
				}
			}
			&--y-#{$index} {
				@include mixins.suffix('lg') {
					--grid-y-spacing: #{$value};
				}
			}
		}
	}
}
.size {
	@include mixins.generate-grid-size();
}

// .order {
// 	@include generate-grid-order();
// }
