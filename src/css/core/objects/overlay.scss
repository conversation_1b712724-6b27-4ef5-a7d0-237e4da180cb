@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

%overlay {
	background-color: rgba(variables.$color-black, 0.3);
	backdrop-filter: blur(0.5rem);
}

.overlay-pseudo {
	&::before {
		@extend %overlay;
		content: '';
		position: fixed;
		z-index: -1;
		visibility: hidden;
		opacity: 0;
		transition: opacity variables.$t, visibility variables.$t;
		inset: 0;
		pointer-events: none;
	}

	// STATES
	&:has(.tooltip__btn[aria-expanded='true'])::before,
	*:is(.is-top, .is-pinned) &:has(.f-search__inp:focus)::before,
	*:is(.is-top, .is-pinned) &.is-open::before,
	*:is(.is-top, .is-pinned) &.is-hover::before,
	*:is(.is-top, .is-pinned) .is-menu-open &.header__menu-wrap::before,
	&.b-filter__helper.is-open::before {
		visibility: visible;
		opacity: 1;
	}
}
