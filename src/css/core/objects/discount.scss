@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.discount {
	--size: 4.4rem;
	--fs1: 0.9rem;
	--fs2: 1.3rem;
	display: inline-flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	width: var(--size);
	height: var(--size);
	border-radius: 50%;
	background: variables.$color-red;
	color: variables.$color-white;
	font-weight: 700;
	font-size: var(--fs1);
	line-height: 1.2;
	white-space: nowrap;
	text-transform: uppercase;
	&__amount {
		font-weight: 900;
		font-size: var(--fs2);
	}

	// MODIF
	&--md {
		--size: 7.2rem;
		--fs1: 1.1rem;
		--fs2: 1.8rem;
	}
	&--arrow {
		position: relative;
		width: auto;
		height: 2rem;
		margin-left: 1rem;
		padding: 0.1rem 0.7rem 0.2rem 0.1rem;
		border-radius: 0 variables.$border-radius-sm variables.$border-radius-sm 0;
		font-size: 1.1rem;
		&::before {
			content: '';
			position: absolute;
			top: 0;
			right: 100%;
			bottom: 0;
			width: 1rem;
			background: url(variables.$svg-discount);
			background-position: center right;
			background-repeat: no-repeat;
			background-size: auto 2rem;
		}
	}
}
