@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.box {
	display: block;
	border-radius: variables.$border-radius-sm;
	background: variables.$color-white;
	overflow: hidden;
	transition: box-shadow variables.$t, color variables.$t;
	box-shadow: variables.$box-shadow;

	// MODIF
	&--sand {
		background: variables.$color-bg-light;
		box-shadow: none;
	}

	// HOVERS
	@at-root a#{&} {
		.hoverevents &:hover {
			box-shadow: variables.$box-shadow-hover;
		}
	}
}
