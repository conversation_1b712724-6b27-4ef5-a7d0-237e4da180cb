@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.btn {
	--btn-fs: 1.4rem;
	--btn-h: 4.4rem;
	--btn-padding: 0.9rem 2.2rem 0.7rem;
	--btn-bg: #{variables.$color-link};
	--btn-c: #{variables.$color-white};
	--btn-bdc: transparent;
	--btn-hover-bg: color-mix(in srgb, var(--btn-bg), #000000 8%);
	--btn-hover-c: #{variables.$color-white};
	--btn-hover-bdc: transparent;
	--btn-gap: 0.8rem;
	--btn-icon-size: 2rem;
	--btn-br: #{variables.$border-radius-md};
	--btn-icon-offset: -0.08em;
	--btn-lh: #{variables.$line-height};
	--btn-bdw: 0.2rem;
	$s: &;
	display: inline-block;
	vertical-align: middle;
	padding: 0;
	border: 0;
	background: none;
	text-decoration: none;
	&:has(*:is(.icon-svg--angle-left, .icon-svg--angle-right)) {
		--btn-gap: 0.6rem;
		--btn-icon-size: 1.5rem;
		--btn-icon-offset: 0;
	}
	&__text {
		position: relative;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		height: 100%;
		min-height: var(--btn-h);
		padding: var(--btn-padding);
		border: var(--btn-bdw) solid var(--btn-bdc);
		border-radius: var(--btn-br);
		background: var(--btn-bg);
		color: var(--btn-c);
		font-family: variables.$font-secondary;
		font-weight: 600;
		font-size: var(--btn-fs);
		line-height: var(--btn-lh);
		text-align: center;
		text-decoration: none;
		overflow: clip;
		transition: background-color variables.$t, border-color variables.$t, color variables.$t, box-shadow variables.$t,
			min-height variables.$t;
	}
	&__inner {
		display: flex;
		gap: var(--btn-gap);
		justify-content: center;
		align-items: center;
	}
	&__icon {
		top: var(--btn-icon-offset);
		flex: 0 0 auto;
		width: var(--btn-icon-size);
		height: var(--btn-icon-size);
	}
	&__info {
		font-weight: bold;
		font-size: 1rem;
		text-transform: uppercase;
		opacity: 0.7;
	}

	// VARIANTs
	// size
	&--xs,
	&--sm {
		--btn-fs: 1.3rem;
		--btn-h: 3.4rem;
		--btn-padding: 0.3rem 1.4rem 0.2rem;
		--btn-lh: 1.4;
		--btn-icon-size: 1.5rem;
		--btn-gap: 0.4rem;
		--btn-icon-offset: 0;
	}

	// color
	&--secondary {
		--btn-bg: #{variables.$color-superprimary};
	}
	&--white {
		--btn-bg: #{rgba(variables.$color-white, 0.7)};
		--btn-c: #{variables.$color-link};
		--btn-hover-c: #{variables.$color-link};
		--btn-hover-bg: #{rgba(variables.$color-white, 0.7)};
		#{$s}__text {
			box-shadow: 0 0.2rem 0.2rem 0 rgba(0, 14, 71, 0.04), 0 0.6rem 0.7rem 0 rgba(0, 14, 71, 0.08);
			backdrop-filter: blur(2rem);
		}
	}
	&--gray {
		--btn-c: #{variables.$color-primary};
		--btn-bg: #{variables.$color-bg};
		--btn-hover-c: #{variables.$color-primary};
	}
	&--micro {
		--btn-bg: #{variables.$color-white};
		--btn-hover-bg: #{variables.$color-white};
		--btn-padding: 0.5rem 0.8rem 0.3rem;
		--btn-h: 3.4rem;
		--btn-fs: 1.3rem;
		--btn-br: #{variables.$border-radius-md};
		--btn-c: #{variables.$color-text};
		--btn-hover-c: #{variables.$color-text};
		--btn-gap: 0.4rem;
		--btn-lh: 1.4;
		--btn-icon-size: 1.2rem;
		#{$s}__text {
			font-weight: 400;
			box-shadow: 0 0.2rem 0.2rem 0 rgba(0, 14, 71, 0.15);
		}
	}
	&--like {
		--btn-padding: 1rem;
		--btn-bg: #{variables.$color-superprimary};
	}
	&--dislike {
		--btn-padding: 1rem;
		--btn-bg: #{variables.$color-status-invalid};
		#{$s}__icon {
			transform: scale(-1);
		}
	}
	// style
	&--icon {
		--btn-icon-size: 2rem;
		--btn-padding: 0.8rem 0.8rem 0.7rem;
		#{$s}__text {
			min-width: var(--btn-h);
		}
		&:has(#{$s}__icon) #{$s}__text {
			padding-right: 0;
			padding-left: 0;
		}
	}
	&--bd {
		--btn-bg: transparent;
		--btn-bdc: #{variables.$color-link};
		--btn-c: #{variables.$color-link};
		--btn-hover-c: #{variables.$color-link};
		--btn-hover-bdc: #{variables.$color-link};
		--btn-hover-bg: #{rgba(variables.$color-primary, 0.07)};
	}
	&--bd#{&}--white {
		--btn-bdc: #{variables.$color-white};
		--btn-c: #{variables.$color-white};
		--btn-hover-c: #{variables.$color-white};
		--btn-hover-bdc: #{variables.$color-white};
		--btn-hover-bg: #{rgba(variables.$color-white, 0.07)};
	}
	&--bd#{&}--black {
		--btn-bdc: #{variables.$color-text};
		--btn-c: #{variables.$color-text};
		--btn-hover-c: #{variables.$color-text};
		--btn-hover-bdc: #{variables.$color-text};
		--btn-hover-bg: #{rgba(variables.$color-text, 0.07)};
	}
	&--bd#{&}--gray {
		--btn-bdc: #{variables.$color-tile};
		--btn-c: #{variables.$color-link};
		--btn-hover-bdc: #{variables.$color-tile};
		--btn-hover-bg: #{rgba(variables.$color-text, 0.07)};
	}
	&--rainbow &__text {
		z-index: 1;
		min-height: calc(var(--btn-h) + 0.2rem);
		border: none;
		background: linear-gradient(96.35deg, #3c1298 0%, #7d25c2 10.5%, #e83d5c 45%, #fe995f 100%);
		&::before {
			content: '';
			position: absolute;
			z-index: -1;
			background: linear-gradient(96.35deg, #3c1298 0%, #7d25c2 30%, #e83d5c 75%, #fe995f 100%);
			opacity: 0;
			transition: opacity variables.$t;
			inset: 0;
		}
	}
	&--block {
		display: block;
		min-width: 100%;
	}
	&--loader &__text::before {
		content: '';
		position: absolute;
		top: 50%;
		left: 50%;
		width: calc(var(--btn-h) / 3);
		height: calc(var(--btn-h) / 3);
		margin: calc(var(--btn-h) / -6) 0 0 calc(var(--btn-h) / -6);
		border: 0.2rem solid var(--btn-c);
		border-radius: 50%;
		border-top-color: transparent;
		visibility: hidden;
		opacity: 0;
		transition: opacity variables.$t, visibility variables.$t;
	}

	// STATEs
	&:disabled,
	&[disabled],
	&.is-disabled {
		--btn-bdc: #{variables.$color-bd};
		--btn-bg: #{variables.$color-bd};
		--btn-c: #{variables.$color-white};
		pointer-events: none;
	}
	.is-loading &--loader,
	.hoverevents .is-loading &--loader:hover,
	&--loader.is-loading,
	.hoverevents &--loader:hover.is-loading {
		position: relative;
		pointer-events: none;
		#{$s}__text,
		#{$s}__text .icon-svg {
			color: transparent;
		}
		#{$s}__text::before {
			visibility: visible;
			opacity: 1;
			animation: animation-rotate 0.8s infinite linear;
		}
	}

	// MODIF
	&:has(&__info) {
		--btn-h: 5.5rem;
		--btn-fs: 1.5rem;
	}
	&:is(a, button) {
		cursor: pointer;
		-webkit-tap-highlight-color: transparent;
	}

	// HOVERs
	.hoverevents &:is(a, button):hover &__text {
		border-color: var(--btn-hover-bdc);
		background-color: var(--btn-hover-bg);
		color: var(--btn-hover-c);
	}
	.hoverevents &--white:is(a, button):hover &__text {
		box-shadow: 0 0.2rem 0.2rem 0 rgba(0, 14, 71, 0.15);
	}
	.hoverevents &--micro:is(a, button):hover &__text {
		box-shadow: 0 0.1rem 0.1rem 0 rgba(0, 14, 71, 0.1);
	}
	.hoverevents &--rainbow:is(a, button):hover &__text {
		&::before {
			opacity: 1;
		}
	}

	// MQ
	@media (config.$md-up) {
		&--micro {
			--btn-h: 3.4rem;
			--btn-fs: 1.3rem;
		}
		&--xs {
			--btn-h: 3.4rem;
			--btn-padding: 0.3rem 1.4rem 0.2rem;
		}
		&--sm {
			--btn-h: 4rem;
			--btn-padding: 0.7rem 1.8rem 0.5rem;
		}
		&--lg {
			--btn-fs: 1.5rem;
			--btn-h: 5.5rem;
			--btn-padding: 0.9rem 2.8rem 0.7rem;
			--btn-br: #{variables.$border-radius-lg};
		}
		&--xl {
			--btn-fs: 1.6rem;
			--btn-br: #{variables.$border-radius-lg};
			--btn-h: 6.5rem;
			--btn-padding: 0.9rem 3.4rem 0.7rem;
			--btn-icon-size: 2.5rem;
		}
		&:has(&__info) {
			--btn-h: 7.1rem;
			--btn-fs: 1.8rem;
		}
	}
}
