@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';
@use 'core/objects/overlay';

.b-modal {
	--row-main-gutter: 2.8rem;
	$s: &;
	@include mixins.dialog-reset;
	position: fixed;
	z-index: -1;
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	height: 100%;
	visibility: hidden;
	opacity: 0;
	transition: opacity 0.3s, z-index 0s 0.3s, visibility 0s 0.3s;
	inset: 0;
	&__wrapper {
		position: relative;
		z-index: 2;
		display: grid;
		grid-template-columns: 1fr auto;
		grid-template-rows: auto 1fr;
		grid-template-areas:
			'content content'
			'content content';
		flex-grow: 1;
		max-width: variables.$row-main-width;
		max-height: calc(100dvh - 1rem);
		margin: 0 0.5rem;
		border-radius: variables.$border-radius-sm;
		overflow: hidden;
	}
	&__header {
		position: relative;
		z-index: 11;
		display: flex;
		grid-area: 1 / 2 / 2 / 2;
		justify-content: flex-end;
	}
	&__title {
		display: none;
		grid-area: title;
		& > * {
			margin: 0;
		}
	}
	&__description {
		display: none;
	}
	&__content {
		position: relative;
		z-index: 2;
		display: flex;
		grid-area: content;
		min-height: 14rem;
		background: variables.$color-white;
		overflow: hidden;
	}
	&__slide {
		display: flex;
		flex: 0 0 100%;
		align-items: flex-start;
		overflow: hidden;
		overflow-y: auto;
		visibility: hidden;
		opacity: 0;
		transition: opacity variables.$t, visibility variables.$t;
		overscroll-behavior: contain;
		&.is-active {
			visibility: visible;
			opacity: 1;
		}
		&:not(:first-child) {
			margin-left: -100%;
		}
	}
	&__image,
	&__video {
		display: flex;
		justify-content: center;
		align-items: center;
		align-self: center;
		width: 100%;
		height: 100%;
		text-align: center;
		> * {
			width: auto;
			max-width: 100%;
			height: auto;
			max-height: 100%;
			object-fit: contain;
		}
		&--multiple {
			> * {
				max-width: 50%;
			}
		}
	}
	&__image img {
		user-select: none;
	}
	&__inner {
		width: 100%;
		padding: 4rem 0;
	}
	&__main {
		padding: 4rem variables.$row-main-gutter;
	}
	&__iframe {
		height: 100%;
		padding: 2rem;
		background: #ffffff;
	}
	&__embed {
		width: 100%;
		height: 100%;
	}
	&__iframe iframe,
	&__embed iframe {
		width: 100%;
		height: 100%;
		aspect-ratio: 16/9;
	}
	&__nav {
		display: none;
		grid-area: nav;
	}
	&__loader {
		position: absolute;
		z-index: 10;
		display: none;
		align-items: center;
		color: #ffffff;
		inset: 0;
		justify-items: center;
	}
	&__loader-icon {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 4rem;
		height: 4rem;
		border-radius: 50%;
		background: rgba(0, 0, 0, 0.3);
		line-height: 0;
	}
	&__bg {
		@extend %overlay;
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		z-index: 1;
	}
	&__prev,
	&__next {
		position: absolute;
		top: 6rem;
		bottom: 0;
		left: 1.2rem;
		z-index: 3;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		pointer-events: none;
	}

	&__next {
		right: 1.2rem;
		left: auto;
		justify-content: flex-end;
	}
	&__prev-btn,
	&__next-btn {
		@include mixins.button-reset;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 4rem;
		border-radius: 50%;
		background: variables.$color-white;
		color: variables.$color-black;
		transition: border-color variables.$t, color variables.$t;
		cursor: pointer;
		pointer-events: auto;
		aspect-ratio: 1/1;
		.icon-svg {
			width: 40%;
			max-width: 2.5rem;
		}
	}
	&__close {
		@include mixins.button-reset;
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 1.5rem;
		color: variables.$color-text;
		outline: none;
		transition: color variables.$t;
		.icon-svg {
			width: 2.5rem;
			height: 2.5rem;
		}
	}

	// Product gallery
	&__tabs {
		padding: 0 1.2rem;
	}
	&__tab-content {
		position: relative;
		min-height: 0;
		padding: 2rem 0;
	}
	&__thumbs {
		display: flex;
		gap: 0.8rem;
	}
	&__thumb {
		flex: 0 0 auto;
		width: 13rem;
	}
	&__thumb-img {
		border: 0.2rem solid transparent;
		border-radius: variables.$border-radius-lg;
		transition: border-color variables.$t;
		aspect-ratio: 16/9;
		object-fit: cover;
	}
	&__thumbs-prev,
	&__thumbs-next {
		@include mixins.button-reset;
		position: absolute;
		top: 50%;
		left: 0;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 4rem;
		border-radius: 50%;
		background: mix(variables.$color-black, variables.$color-white, 80%);
		color: variables.$color-white;
		transform: translateY(-50%) rotate(-90deg);
		transition: opacity variables.$t, visibility variables.$t, background-color variables.$t;
		aspect-ratio: 1/1;
		&.is-disabled {
			visibility: hidden;
			opacity: 0;
		}
		.icon-svg {
			width: 40%;
		}
	}
	&__thumbs-next {
		right: 0;
		left: auto;
	}

	// MODIF
	&--xs &__wrapper {
		max-width: 40rem;
	}
	&--4-12 &__wrapper {
		max-width: calc(
			(variables.$row-main-width - variables.$row-main-gutter) / variables.$grid-columns * 4 - variables.$row-main-gutter
		);
	}
	&--gallery {
		--wrapper-gap-x: 2rem;
		--tabs-padding-x: 0rem;
		--tabs-width: 31.2rem;
		#{$s} {
			&__wrapper {
				grid-template-columns: 1fr max-content;
				grid-template-rows: auto 1fr auto;
				grid-template-areas:
					'title header'
					'content content'
					'nav nav';
				gap: 2.5rem 0;
				height: 100dvh;
				border-radius: variables.$border-radius-lg;
				background: variables.$color-white;
			}
			&__title {
				display: flex;
				align-items: center;
				padding: 1.6rem 1.6rem 0.8rem;
			}
			&__prev {
				bottom: 17rem;
				left: 1.2rem;
			}
			&__next {
				right: 1.2rem;
				bottom: 17rem;
			}
			&__nav {
				display: block;
			}
		}
	}

	// STATES
	&:has(.b-prebasket) {
		#{$s}__content,
		#{$s}__inner {
			background: transparent;
		}
	}
	body:not(.is-loading) &__inner {
		background: #ffffff;
	}
	&.is-opened {
		z-index: 1000;
		visibility: visible;
		opacity: 1;
		transition: opacity 0.3s, z-index 0s, visibility 0s;
	}
	&.is-loading &__loader {
		display: grid;
	}
	&.is-loading &__loader-icon {
		animation: animation-rotate 0.8s infinite linear;
	}
	&.is-first &__prev,
	&.is-last &__next {
		display: none;
	}
	&__thumb:not(.is-visible) {
		display: none;
	}

	// HOVERs
	.hoverevents &__close:hover {
		color: variables.$color-link;
	}
	.hoverevents &__prev-btn:hover,
	.hoverevents &__next-btn:hover {
		color: variables.$color-primary;
	}
	&__thumb-img.is-active,
	.hoverevents &__thumb-img:hover {
		border-color: variables.$color-primary;
	}
	.hoverevents &__thumbs-prev:hover,
	.hoverevents &__thumbs-next:hover {
		background: variables.$color-black;
		color: variables.$color-white;
	}

	// MQ
	@media (config.$sm-up) {
		&__main {
			padding: 4rem 5rem;
		}
	}
	@media (config.$md-up) {
		--row-main-gutter: 6rem;
		&__prev {
			top: 5rem;
			left: 4rem;
		}
		&__next {
			top: 5rem;
			right: 4rem;
		}
		&__prev-btn,
		&__next-btn {
			width: 6.5rem;
		}
		&__wrapper {
			max-height: calc(100dvh - 4rem);
			margin: 0 7rem;
		}
		&__close {
			padding: 2.1rem;
		}

		// Product gallery
		&__tabs {
			padding: 0;
		}
		&__tab-content {
			padding: 2.8rem 2rem 2rem;
			overflow: hidden;
		}
		&__thumbs-wrap {
			height: 100%;
		}
		&__thumbs {
			display: grid;
			grid-template-columns: 1fr 1fr;
			grid-template-rows: auto;
			gap: 1.2rem;
			max-height: 100%;
		}
		&__thumb {
			width: 100%;
		}
		&__thumb-img {
			aspect-ratio: 4/3;
		}
		&__thumbs-prev,
		&__thumbs-next {
			top: 2rem;
			left: 50%;
			width: 5.2rem;
			transform: translateX(-50%);
		}
		&__thumbs-next {
			top: auto;
			right: auto;
			bottom: 2rem;
			left: 50%;
		}

		// MDOIF
		&--gallery {
			--wrapper-gap-x: 2rem;
			--tabs-padding-x: 0rem;
			--tabs-width: 31.2rem;
			#{$s} {
				&__wrapper {
					grid-template-columns: 1fr calc(var(--tabs-width) + var(--tabs-padding-x));
					grid-template-rows: 6.5rem 1fr;
					grid-template-areas:
						'title header'
						'content nav';
					gap: 0 var(--wrapper-gap-x);
					flex-grow: 1;
					max-width: 188rem;
					max-height: calc(100dvh - 4rem);
					margin: 0 2rem;
					padding: 1.2rem;
					border-radius: variables.$border-radius-xl;
				}
				&__header {
					grid-area: header;
				}
				&__title {
					padding: 0;
					h3 {
						font-size: 1.8rem;
					}
				}
				&__prev {
					top: 7.7rem;
					bottom: 0;
					left: 2.4rem;
				}
				&__next {
					top: 7.7rem;
					right: calc(2.4rem + var(--tabs-width) + var(--tabs-padding-x) + var(--wrapper-gap-x));
					bottom: 0;
				}
				&__tabs {
					display: grid;
					grid-template-rows: auto 1fr;
					height: calc(100dvh - 11.7rem);
					padding: 0 var(--tabs-padding-x);
				}
			}
		}
		&:has(.b-prebasket) &__wrapper {
			margin: 0 var(--row-main-gutter);
		}
	}
	@media (config.$lg-up) {
		&--gallery {
			--wrapper-gap-x: 4rem;
			--tabs-padding-x: 4rem;
			--tabs-width: 35.2rem;
		}
	}
	@media (config.$xl-up) {
		&--gallery {
			#{$s} {
				&__title {
					padding: 0 2rem;
				}
				&__prev {
					left: 1.2rem;
				}
				&__next {
					right: calc(1.2rem + var(--tabs-width) + var(--tabs-padding-x) + var(--wrapper-gap-x));
				}
				&__content {
					padding: 0 10.5rem;
				}
			}
		}
	}
}
