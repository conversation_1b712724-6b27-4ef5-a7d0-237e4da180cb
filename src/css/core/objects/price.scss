@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.price {
	--price-fs-old: 1.3rem;
	--price-fs-novat: 1.3rem;
	--price-fs: 1.6rem;
	$s: &;
	margin: 0;
	color: variables.$color-help;
	font-family: variables.$font-secondary;
	font-size: var(--price-fs-main);
	line-height: 1.3;
	&__original {
		display: flex;
		gap: 0.8rem;
		align-items: center;
		margin: 0 0 0.4rem;
	}
	&__old {
		font-size: var(--price-fs-old);
	}
	&__main strong {
		color: variables.$color-black;
		font-family: variables.$font-secondary;
		font-size: var(--price-fs);
	}
	&__novat {
		font-size: var(--price-fs-novat);
	}

	// MODIf
	.countdown + * &__discount {
		position: relative;
		overflow: visible;
		&::after {
			content: '';
			position: absolute;
			bottom: calc(100% + 1.1rem);
			left: 50%;
			width: 0;
			height: 0;
			border-width: 0.5rem 0.6rem 0;
			border-style: solid;
			border-color: rgba(variables.$color-black, 0.8) transparent transparent;
			transform: translate(-50%);
		}
	}
	&--detail {
		--price-fs: 2.8rem;
		--price-fs-main: 1.2rem;
		--price-fs-old: 1.2rem;
		--price-fs-novat: 1.2rem;
		#{$s}__original {
			margin: 0;
		}
	}
	.countdown + &--detail &__discount::after {
		bottom: calc(100% + 0.3rem);
	}

	// MQ
	@media (config.$md-up) {
		--price-fs: 1.8rem;

		// MODIF
		&--detail {
			--price-fs: 3.4rem;
			--price-fs-main: 1.4rem;
			--price-fs-old: 1.3rem;
			--price-fs-novat: 1.3rem;
		}
	}
}
