@use 'config';
@use 'base/variables';

.text-divider {
	display: flex;
	gap: 0.8rem;
	color: variables.$color-help;
	font-size: 1rem;
	text-align: center;
	text-transform: uppercase;

	&::before,
	&::after {
		content: '';
		flex-grow: 1;
		height: 0.1rem;
		margin: 0.75em 0;
		border: solid variables.$color-tile-light;
		border-width: 0.1rem 0 0;
		overflow: hidden;
	}

	// MQ
	@media (config.$md-up) {
		margin: 1.8rem 0;
	}
}
