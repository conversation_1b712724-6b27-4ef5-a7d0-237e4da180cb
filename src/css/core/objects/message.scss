@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.message {
	--message-close-size: 2.4rem;
	--message-padding-y: 1rem;
	--message-emoji-fs: 2rem;
	--message-emoji-size: 2.6rem;
	--message-h: 4.1rem;
	--message-padding-x: 1.2rem;
	--message-fs: 1.3rem;

	$s: &;
	min-height: var(--message-h);
	padding: var(--message-padding-y) var(--message-padding-x);
	border-radius: variables.$border-radius-lg;
	background: variables.$color-bg;
	font-size: var(--message-fs);
	line-height: 1.4;
	&:has(&__content) {
		display: flex;
		gap: 0.8rem;
		align-items: center;
	}
	&__emoji {
		display: inline-flex;
		vertical-align: sub;
		justify-content: center;
		align-items: center;
		align-self: flex-start;
		width: var(--message-emoji-size);
		height: calc(var(--message-h) - 2 * var(--message-padding-y));
		font-size: var(--message-emoji-fs);
	}
	&__content {
		flex: 1;
	}
	&__close {
		--color-link: #{variables.$color-text};
		--color-hover: #{variables.$color-white};
		align-content: center;
		align-self: flex-start;
		width: var(--message-close-size);
		height: var(--message-close-size);
		border-radius: 50%;
		background: variables.$color-white;
		font-size: 0;
		text-align: center;
		transition: color variables.$t, background-color variables.$t;
		.icon-svg {
			width: 48%;
		}
	}

	// MODIF
	// style
	&--error {
		background: variables.$color-status-invalid-light;
	}
	&--ok {
		background: variables.$color-status-valid-light;
	}
	&--warning {
		background: variables.$color-alert-light;
	}
	&--warning2 {
		background: variables.$color-alert2-light;
	}
	&--purple {
		background: #f3e5ff;
	}
	// size
	// &--sm {
	// 	--message-emoji-fs: 2rem;
	// 	--message-emoji-size: 2.6rem;
	// 	--message-h: 4.1rem;
	// 	--message-padding-x: 1.2rem;
	// 	font-size: 1.3rem;
	// }

	// HOVERS
	.hoverevents &__close:hover {
		background: variables.$color-primary;
	}

	// MQ
	@media (config.$md-up) {
		--message-close-size: 3.1rem;
		--message-fs: 1.5rem;
		line-height: 1.6;

		// MODIF
		&--sm {
			--message-fs: 1.3rem;
			--message-h: 4.7rem;
			--message-padding-x: 2rem;
			--message-emoji-fs: 2.4rem;
			--message-emoji-size: 3rem;
		}
		&--md {
			--message-h: 6rem;
			--message-padding-x: 2rem;
			--message-padding-y: 1.2rem;
			--message-emoji-fs: 3rem;
			--message-emoji-size: 3.6rem;
			font-size: 1.5rem;
		}
		&--lg {
			--message-emoji-fs: 3.6rem;
			--message-emoji-size: 3.6rem;
			--message-h: 7.5rem;
			--message-padding-x: 3.2rem;
			--message-padding-y: 0rem;
			font-size: 1.6rem;
		}
	}
}
