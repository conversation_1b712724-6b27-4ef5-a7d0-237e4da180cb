@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.availability {
	--icon-size: 1.4rem;
	--gap: 0.4rem;
	margin: 0;
	font-size: 1.3rem;
	a {
		color: inherit;
	}

	// MODIF
	&--available {
		color: variables.$color-green;
	}
	&--prepare {
		color: variables.$color-primary;
	}
	&--unavailable {
		color: variables.$color-red;
	}
	&--delivery#{&}--unavailable {
		color: variables.$color-help;
		a {
			--color-hover: #{variables.$color-text};
		}
	}

	// MQ
	@media (config.$md-up) {
		font-size: 1.4rem;
	}
}
