@use 'config';
@use 'base/variables';
@use 'base/typography';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

#CybotCookiebotDialog {
	z-index: 1008 !important;
	width: calc(100% - 2rem) !important;
	max-width: 48.4rem !important;
	max-height: calc(100dvh - 2rem) !important;
	padding: 3.6rem 2.8rem 3.2rem !important;
	border: none !important;
	border-radius: variables.$border-radius-md !important;
	background-color: variables.$color-white !important;
	color: variables.$color-text;
	font-family: variables.$font-primary !important;
	font-size: 1.4rem !important;
	line-height: variables.$line-height !important;
	letter-spacing: normal !important;
	box-shadow: none !important;
	* {
		color: variables.$color-text;
		font-family: variables.$font-primary !important;
		font-size: 1.4rem !important;
		line-height: variables.$line-height !important;
		letter-spacing: normal !important;
	}
	@media (config.$md-up) {
		max-height: calc(100dvh - 4rem) !important;
		padding: 6rem !important;
		border-radius: variables.$border-radius-xl !important;
		font-size: 1.5rem !important;
		* {
			font-size: 1.5rem !important;
		}
	}
}

#CybotCookiebotDialogBodyUnderlay {
	z-index: 1007 !important;
	background: black;
	background-color: rgba(black, 0.2) !important;
	backdrop-filter: blur(0.8rem);
}
#CybotCookiebotDialog.CybotCookiebotDialogActive + #CybotCookiebotDialogBodyUnderlay {
	opacity: 1 !important;
}

#CybotCookiebotDialogBodyContent,
.CybotCookiebotScrollArea {
	padding: 0 !important;
}

#CybotCookiebotDialogBodyContentTitle {
	@extend %h3;
	display: flex;
	gap: 1.2rem;
	align-items: center;
	margin: 0 0 1.2rem !important;
	color: variables.$color-text-headline !important;
	font-family: variables.$font-secondary !important;
	font-weight: bold !important;
	font-size: var(--font-size-mobile) !important;
	&::before {
		content: '🍪';
		display: block;
		font-size: 2.4rem;
	}
	@media (config.$md-down) {
		line-height: 1.5 !important;
	}
	@media (config.$md-up) {
		margin: 0 0 2rem !important;
		font-size: var(--font-size-desktop) !important;
		&::before {
			font-size: 4rem;
		}
	}
}

#CybotCookiebotDialogHeader {
	display: none !important;
}

#CybotCookiebotDialogNav {
	position: absolute !important;
	top: -5000px !important;
	left: -5000px !important;
	z-index: -1 !important;
	width: 1px !important;
	height: 1px !important;
	visibility: hidden !important;
	opacity: 0 !important;
}

#CybotCookiebotDialogTabContent {
	margin: 0 0 1.6rem !important;
	@media (config.$md-up) {
		margin: 0 0 2rem !important;
	}
}
.CybotCookiebotScrollContainer {
	max-height: 100% !important;
	padding: 0 !important;
	border: none !important;
}

#CybotCookiebotDialogBodyButtonsWrapper {
	display: flex !important;
	gap: 0.8rem !important;
	flex-direction: column !important;
	& > * {
		width: 100% !important;
	}
	@media (config.$md-up) {
		gap: 1.2rem !important;
	}
}

#CybotCookiebotDialogBodyLevelButtonLevelOptinAllowAll {
	min-height: 4.4rem !important;
	padding: 0.9rem 2.2rem 0.7rem !important;
	border-radius: variables.$border-radius-md !important;
	border-color: transparent !important;
	background: variables.$color-primary !important;
	font-family: variables.$font-secondary;
	font-weight: 600 !important;
	@media (config.$md-up) {
		min-height: 5.5rem !important;
		padding: 1.4rem 2.8rem !important;
		border-radius: variables.$border-radius-lg !important;
	}
}

#CybotCookiebotDialogBodyLevelButtonLevelOptinAllowallSelection,
#CybotCookiebotDialogBodyLevelButtonCustomize {
	order: 1 !important;
	min-height: 4.4rem !important;
	margin: 0 !important;
	padding: 0.9rem 2.2rem 0.7rem !important;
	border-radius: variables.$border-radius-md !important;
	border-color: variables.$color-primary !important;
	color: variables.$color-primary !important;
	font-family: variables.$font-secondary;
	font-weight: 600 !important;
	@media (config.$md-up) {
		min-height: 5.5rem !important;
		padding: 1.4rem 2.8rem !important;
		border-radius: variables.$border-radius-lg !important;
	}
}

.CybotCookiebotDialogArrow,
#CybotCookiebotDialogBodyButtonDecline {
	display: none !important;
}

.CybotCookiebotDialogDetailBodyContentCookieContainerButton {
	position: relative;
	display: flex;
	gap: 1.2rem;
	align-items: center;
	padding: 0 !important;
	&::before {
		top: -0.1em !important;
		margin-right: 0 !important;
	}
}
.CybotCookiebotDialogDetailBodyContentCookieTypeIntro {
	padding: 0.8rem 0 0 !important;
}
#CybotCookiebotDialog .CookieCard {
	padding: 1.2rem 0 !important;
}
#CybotCookiebotDialogTabContent .CybotCookiebotDialogDetailBulkConsentCount {
	display: inline-flex !important;
	justify-content: center;
	align-items: center;
	min-width: 2.5rem;
	margin: 0 !important;
	padding: 0.1rem 0.5rem 0 !important;
	font-size: 1.2rem !important;
	aspect-ratio: 1/1;
}

.CybotCookiebotDialogBodyLevelButtonSliderWrapper {
	width: 2.8rem !important;
	height: 1.8rem !important;
	border-radius: 1.8rem !important;

	&.CybotCookiebotDialogBodyLevelButtonSliderWrapperDisabled {
		opacity: 0.5;
	}
}

.CybotCookiebotDialogBodyLevelButtonSlider {
	background: variables.$color-placeholder !important;
	&::before {
		top: 0.2rem !important;
		bottom: 0.2rem !important;
		left: 0.2rem !important;
		width: 1.4rem !important;
		height: auto !important;
	}
}
#CybotCookiebotDialog input:checked + .CybotCookiebotDialogBodyLevelButtonSlider {
	background-color: rgba(0, 148, 0, 1) !important;
	&::before {
		transform: translateX(1rem) !important;
	}
}
.CybotCookiebotScrollbarContainer {
	display: none !important;
}
#CybotCookiebotDialogDetailBodyContentCookieContainerTypes {
	padding-left: 0 !important;
}

#CybotCookiebotDialogDetailFooter {
	padding: 0 !important;
}
