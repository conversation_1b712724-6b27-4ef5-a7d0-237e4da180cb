/* stylelint-disable declaration-no-important */
@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

html,
body {
	width: 100%;
	// background: none !important;
}
* {
	text-shadow: none !important;
	box-shadow: none !important;
	-webkit-print-color-adjust: exact; // force background show

	// background: none !important; // optional
	// color: variables.$color-black !important; // optional: convert everything to BW, save print colors
	// font-family: variables.$font-system !important; // optional

	// Images, vectors and such
	// filter: gray(); // optional: convert everything to BW, save print colors
	// filter: grayscale(100%); // optional: convert everything to BW, save print colors
}
// a {
// 	text-decoration: underline !important; // optional
// }

// print URL next to the link. Optional
a[href^='http']::after {
	content: ' (' attr(href) ')';
}
// svg {
// 	fill: variables.$color-black !important;
// }
img {
	max-width: 100%;
	height: auto;
}

// Defining all page breaks
a,
table,
pre,
blockquote,
.b-aboutus__cloud-item {
	page-break-inside: avoid;
}
h1,
h2,
h3,
h4,
h5,
h6,
img,
svg,
figure,
.footer {
	page-break-after: avoid;
	page-break-inside: avoid;
}
ul,
ol,
dl {
	page-break-before: avoid;
}

// Hide redundants
nav,
form,
// iframe,
.u-no-print, // Util class for clients
.btn,
.header__msg1,
.header__msg2,
.header__search,
.header__more,
.header__burger,
.embla__btn,
.embla__dots,
.b-book-review__title a::after,
.footer__apps,
.b-layout__nav,
.c-categories__link .icon-svg,
.c-categories__btn,
.paging,
.b-cart-total__voucher-gift,
.f-btns,
.f-basket__agree {
	display: none;
}

html,
.header,
.c-categories__link,
.box,
.b-cart-total,
.b-cart-summary__top,
.b-cart-summary__divider,
.b-cart-summary__bottom {
	background: none;
}

// Components
.header {
	margin: 0 0 functions.spacing('xs');
	border-bottom: 0.1rem solid variables.$color-bg;
	&__inner {
		grid-template-rows: var(--header-main-height);
	}
	&__logo-recolor {
		fill: variables.$color-primary;
	}
}
.b-hp {
	.grid__cell {
		width: 100%;
	}
}
.b-book-review {
	&__content {
		padding: 3.6rem 0 0;
	}
}
.footer {
	padding-top: 0;
	border-top: 0.1rem solid variables.$color-bg;
	background: transparent;
	color: variables.$color-text;
	&__partners {
		border-top: none;
	}
	&__publishers {
		max-width: none;
		img {
			filter: saturate(0%) brightness(70%) contrast(100%);
		}
	}
}
.b-contact-info {
	padding: 0;
}
.b-store-info {
	margin: 0 0 functions.spacing('xs');
	padding: 0;
}
.box {
	padding: 0;
}
.c-contacts {
	margin: 0 0 functions.spacing('md');
}
.b-annot {
	&__desc {
		min-height: 0;
		padding: 0;
	}
}
.b-team {
	.grid__cell {
		width: 33.33%;
	}
}
.b-benefits {
	&__item {
		width: 33.33%;
	}
}
.c-categories {
	margin: 0 0 functions.spacing('sm');
	&__link {
		min-height: 0;
		padding: 0;
		color: inherit;
	}
	&__cell:nth-child(n + 9) {
		display: block;
	}
}
.c-products {
	&__item.grid__cell {
		width: 20%;
	}
}
.section {
	&--products,
	&--recommended {
		--cell-size: 20%;
	}
}

.b-layout {
	&__main {
		margin: 0 0 functions.spacing('sm');
	}
}
.f-address {
	display: block;
}
.f-basket {
	display: block;

	// MODIF
	&--step2 .grid__cell,
	&--step3 .grid__cell {
		width: 100%;
	}
}
.b-cart-total {
	padding: 0;
	&__wrap {
		flex-direction: row;
		justify-content: space-between;
	}
	&__delivery {
		padding: 0;
		border: none;
	}
}
.b-basket-products {
	margin: 0;
	padding: 0;
}
.b-cart {
	&__item {
		grid-template-columns: 4.6rem 1fr max-content max-content max-content;
		grid-template-rows: auto auto 1fr;
		grid-template-areas:
			'img name count price remove'
			'img type count price remove'
			'img availability count price remove';
		padding: 0;
	}
	&__price,
	&__count,
	&__remove {
		align-self: center;
		margin: 0;
	}
}
.b-cart-summary {
	&__inner {
		--padding-x: 0;
	}
	&__top {
		padding-top: 0;
	}
	&__bottom {
		padding-bottom: 0;
	}
}
.b-aboutus {
	$s: &;
	grid-template-columns: minmax(0, 465px) minmax(60%, 1fr);
	gap: 2rem;
	&__cloud {
		--gap: 2rem;
		grid-template-rows: 1fr 0.8fr min-content 0.8fr;
	}
	&__cloud-item {
		display: flex;
		justify-content: flex-end;
		align-items: flex-start;
		&:nth-child(-n + 3) img {
			max-width: 100%;
		}
		&:nth-child(1) {
			--img-width: 220px;
			--aspect-ratio: 220/144;
			grid-row: 3 / span 1;
			grid-column: 1 / span 3;
		}
		&:nth-child(2) {
			--img-width: 180px;
			--aspect-ratio: 180/104;
			grid-row: 4 / span 1;
			grid-column: 1 / span 3;
			#{$s}_img {
				margin-right: 0;
			}
		}
		&:nth-child(3) {
			--img-width: 240px;
			--aspect-ratio: 240/125;
			grid-row: 1 / span 1;
			grid-column: 9 / -1;
			justify-content: flex-start;
			align-items: flex-end;
			#{$s}_img {
				margin-right: 0;
			}
		}
		&:nth-child(4) {
			--img-width: 523px;
			--aspect-ratio: 523/324;
			grid-row: 1 / span 2;
			grid-column: 1 / span 8;
			align-items: flex-end;
		}
		&:nth-child(5) {
			--img-width: 370px;
			--aspect-ratio: 340/307;
			grid-row: 3 / span 2;
			grid-column: 4 / 9;
		}
		&:nth-child(6) {
			--img-width: 320px;
			--aspect-ratio: 320/387;
			grid-row: 2 / span 3;
			grid-column: 9 / -1;
		}
	}
}
