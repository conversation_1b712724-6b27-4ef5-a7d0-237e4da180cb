@forward 'utilities/clearfix';
@forward 'utilities/image';
@forward 'utilities/screen-reader';
@forward 'utilities/text';
@forward 'utilities/tracy';
@forward 'utilities/visibility';
@forward 'utilities/sizing';
@use 'base/mixins';
@use 'base/variables';
@use 'base/functions';

// Inspirujeme se z emmetího cheat-sheetu: https://docs.emmet.io/cheat-sheet/

$utilities: (
	'align': (
		property: vertical-align,
		class: va,
		values: (
			t: top,
			m: middle,
			b: bottom
		)
	),
	'display': (
		responsive: true,
		property: display,
		class: d,
		values: (
			n: none,
			ib: inline-block,
			b: block
		)
	),
	'float': (
		property: float,
		class: fl,
		values: (
			l: left,
			r: right
		)
	),
	// 'font-size': (
	// 	property: font-size,
	// 	class: fz,
	// 	values: (
	// 		'14': 1.4rem
	// 	)
	// ),
	'font-style':
		(
			property: font-style,
			class: fs,
			values: (
				i: italic
			)
		),
	'font-weight': (
		property: font-weight,
		class: fw,
		values: (
			l: 300,
			n: normal,
			b: bold
		)
	),
	'text-align': (
		property: text-align,
		class: ta,
		values: (
			l: left,
			r: right,
			c: center,
			j: justify
		)
	),
	'text-transform': (
		property: text-transform,
		class: tt,
		values: (
			l: lowercase,
			u: uppercase,
			c: capitalize
		)
	),
	'white-space': (
		property: white-space,
		class: whs,
		values: (
			nw: nowrap
		)
	),
	'padding-top': (
		property: padding-top,
		class: pt,
		responsive: true,
		values: (
			'0': functions.spacing('0'),
			'xxs': functions.spacing('xxs'),
			'xs': functions.spacing('xs'),
			'sm': functions.spacing('sm'),
			'md': functions.spacing('md'),
			'lg': functions.spacing('lg'),
			'xl': functions.spacing('xl'),
			'2xl': functions.spacing('2xl'),
			'3xl': functions.spacing('3xl')
		)
	),
	'margin-bottom': (
		property: margin-bottom,
		class: mb,
		responsive: true,
		values: (
			'0': functions.spacing('0'),
			'xxs': functions.spacing('xxs'),
			'xs': functions.spacing('xs'),
			'sm': functions.spacing('sm'),
			'md': functions.spacing('md'),
			'lg': functions.spacing('lg'),
			'xl': functions.spacing('xl'),
			'2xl': functions.spacing('2xl'),
			'3xl': functions.spacing('3xl')
		)
	),
	'background-color': (
		property: background-color,
		class: bgc,
		values: (
			'default': variables.$color-bg,
			'dark': variables.$color-black,
			'alert-light': variables.$color-alert-light
		)
	),
	'color': (
		property: color,
		class: c,
		values: (
			'primary': variables.$color-primary,
			'text': variables.$color-text,
			'red': variables.$color-red,
			'help': variables.$color-help,
			'green': variables.$color-green
		)
	)
);

@include mixins.generate-utilities($utilities);

.u-mb-last-0 {
	> *:last-child:not(.grid),
	> *[id*='snippet']:last-child > *:last-child {
		margin-bottom: 0;
	}
}
.u-round {
	border-radius: variables.$border-radius-sm;
}
.u-mt-0 {
	margin-top: 0;
}
.u-ml-auto {
	margin-left: auto;
}
.u-no-decoration {
	text-decoration: none;
}
.u-bgc-violet-gradient {
	background: linear-gradient(90deg, #fdf0ed 0%, #f3e5ff 100%);
}

.u-reset-ul {
	@extend %reset-ul;
	& > li {
		@extend %reset-ul-li;
	}
}
