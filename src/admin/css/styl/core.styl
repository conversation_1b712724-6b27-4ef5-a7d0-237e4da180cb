@import "core/grid.styl"
@import "core/icon.styl"
// @import "core/message.styl"

[data-toggle]
	position relative
	margin-bottom 0
	text-decoration underline
	color $colorLink
	cursor pointer
	&:after
		content ''
		height 0
		width 0
		position absolute
		top 50%
		margin-left 8px
		margin-top -2px
		overflow hidden
		border-width 6px 6px 0 6px
		border-color $colorLink transparent transparent transparent
		border-style solid
	&:hover
		color $colorHover
		&:after
			border-color $colorHover transparent transparent transparent
	&.open
		&:after
			border-width 0 6px 6px 6px
			border-color transparent transparent $colorLink transparent
		&:hover:after
			border-color transparent transparent $colorHover transparent

[data-toggle] + * > :first-child
	padding-top 12px



.fixed-bar
	position fixed
	bottom 0
	right 0
	z-index 99
	left $colSideWidthS
	max-width 1400px + ( $colSideWidth - $colSideWidthS )
	padding 10px ($gutter*1.5)
	background hexa(#fff, .85)
	border-top 1px solid $colorGray
	box-sizing border-box
	transition left $colSideDuration ease, max-width $colSideDuration ease
	.menu-hover &
		left $colSideWidth
		max-width 1400px
	.col-content &
		left (521px - ( $colSideWidth - $colSideWidthS ) )
		max-width (1400px - 321px) + ( $colSideWidth - $colSideWidthS )
		.menu-hover &
			left 521px
			max-width (1400px - 321px)
	.skbox-window &
		display none

.paging
	overflow hidden
	.icon
		position relative
		top 2px
	a
		margin 0 2px
	.prev
		margin 0 13px 0 0
	.next
		margin 0 0 0 13px
	.disabled
	.disabled:hover
		color $colorGray
		cursor default
	.active
		color $color
		text-decoration none

/*!
 *	Forms
 */
@import "core/forms/input.styl"
@import "core/forms/buttons.styl"
@import "core/forms/datepicker.styl"

/*!
 *	Plugins
 */
@import "core/skbox.styl"
@import "core/uploadify.styl"
