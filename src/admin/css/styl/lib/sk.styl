inline-block($align = top, $isBlock = true)
	display inline-block
	vertical-align $align
	if $isBlock && $support-for-ie
		.ie7 &
			display inline
			zoom 1

inline-list($width, $font)
	font-family 'Courier New', monospace
	letter-spacing -0.63em
	word-spacing -0.63em
	> *
		inline-block()
		width $width
		font-family $font
		letter-spacing 0px
		word-spacing 0px

inline-list-scroll($width, $font)
	inline-list($width, $font)
	position relative
	white-space nowrap
	overflow visible
	overflow-x hidden
	> * > *
		white-space normal
	.no-js &
		overflow-x scroll

// Clearování flotů
clearfix()
	zoom 1
	&:after
	&:before
		content ''
		display table
		clear both

// Skrytí textu
text-hide()
	font 0px/0px a
	color transparent
	text-decoration none

// Rozměry
size($width = false, $height = false)
	if unit($width) is ''
		$width = unit($width, 'px')

	if $height
		if unit($height) is ''
			$height = unit($height, 'px')
		width $width
		height $height
	else
		width $width
		height $width

// RGBA s hexa hodnoty
hexa($color=#000, $opacity = 1)
	rgba( red($color), green($color), blue($color), $opacity)

// PX do EM
pxToEm($from, $to)
	unit(($to/$from), em)

// Čtverec - hodí se pro ikonky
square($size)
	size($size, $size)

// Vertikální zarovnání obsahu
box-vertical($align = middle)
	&:before
	.before
		content ''
		display inline-block
		height 100%
		margin-right -0.25em
		vertical-align $align