@import "content/basic-class.styl"

/*!
 * Typo
 */
body
	font normal $fontSize/24px $font
	color #000

/* Titles*/
h1
h2
h3
h4
h5
h6
	font 1em/1.2 $fontTitles
	color $colorTitles
	margin 1.3em 0 .5em

h1
.h1
	font-size $fontSizeH1
	margin-top 0
h2
	font-size $fontSizeH2
	margin-bottom .7em
h3
	font-size $fontSizeH3

h4
h5
h6
	font-size 1em

/*!
 *	Margins
 */
.margin-h1
	padding $fontSizeH1
	margin ($fontSizeH1 * 1.2) 0 ($fontSizeH1 * .5)
.margin-h2
	margin ($fontSizeH2 * 1.2) 0 ($fontSizeH2 * .5)
.margin-h3
	margin ($fontSizeH3 * 1.2) 0 ($fontSizeH3 * .5)

.margin-h4
.margin-h5
.margin-h6
	margin 1.2em 0 .5em

.margin-p
	margin 0 0 1em

.margin-ul
.margin-ol
	margin 0 0 1em


/* Paragraph */
p
	margin 0 0 1em

/* Blockquote */
blockquote
	margin .8em 0 .3em
	p
		margin 0

/* Links */
a
	color $colorLink
	tap-highlight-color rgba(0,0,0,0);
	text-decoration underline
	&:hover
		color $colorHover
		text-decoration none


/* Lists */
ul
ol
	margin 0 0 1em
	list-style none
	padding 0
li
	padding 0 0 0 20px
	margin 0 0 .5em
	ol
	ul
		margin .75em 0 0
ul
	li
		&:before
			content ''
			float left
			margin 8px 0 0 -20px
			size(8)
			border-radius 5px
			background $colorMain
ol
	counter-reset item
	li
		background none
		&:before
		.ie-counter
			content counter(item)"."
			counter-increment item
			float left
			margin-left -20px
			width 18px
			color $colorMain
			font-weight bold

dl
	margin 0 0 1.5em
dt
	font-weight:bold
	text-transform uppercase
	margin 0 0 .4em
dd
	margin 0 0 1em
	padding 0

/* Tables */
table
	width 100%
	//clear both
	margin 0 0 1.5em
	empty-cells show
	border-collapse separate
	border-spacing 0
	border 0px
	p + &
		margin-top 1.2em

caption
	font-weight bold
	text-align left
	padding 0px 0px 10px
	caption-side top
td
th
	vertical-align top
	padding 7px 20px 6px 20px
	border 1px solid $colorGray
	border-width 0 0px 1px
	+ *
		padding-left 0
th
	font-weight normal
	background $colorMain
	color $colorMainText
	border-bottom 0px

tfoot
	td
	th
		background $colorGray

tr.clickable
	cursor pointer
	&:hover
		background $colorLight

th.status
td.status
	width 5px
	padding 0
	+ td,
	+ th
		padding-left 20px
td.status
	background $color
	&.green
		background $colorGreen
	&.red
		background $colorRed
	&.yellow
		background $colorYellow
