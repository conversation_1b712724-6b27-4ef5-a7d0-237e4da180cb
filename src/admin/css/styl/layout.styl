html
	height 100%
	background #eee
body
	position relative
	color $color
	min-height 100%
	// max-width 1400px + ( $colSideWidth - $colSideWidthS )
	padding-left $colSideWidthS// solid $colorDark
	background #fff
	// box-shadow 0 0 15px hexa(#000, .2)
	// transition padding-left $colSideDuration ease, max-width $colSideDuration ease
	&:before
		content ''
		position absolute
		top 0
		left 0
		bottom 0
		width $colSideWidthS
		background $colorDark
		// transition width $colSideDuration ease
	// hover menu
	&.menu-hover
		// padding-left $colSideWidth
		// max-width 1400px
		// &:before
		// 	width $colSideWidth

	&.page-login
		background $colorMain
		padding 0
		max-width 1400px + $colSideWidth
		&:before
			display none

.form-login
	margin-top 10px
	padding 25px 30px 22px
	background #fff
	.center
		margin-top 1.5em


.app-status
	position fixed
	height 0
	top 0
	left 0
	right 0
	max-width 1400px + $colSideWidth
	text-align center
	z-index 102
	.bar
		position relative
		inline-block()
		background $colorDark
		color $colorDarkText
	.text
		padding 5px 20px


.crossroad-attached
	.grid-row
		margin-left -10px
		> *
			padding-left 10px
	.hd
		padding-left 30px
		padding-right 28px
		margin 0 0 3px
		p
			margin 0
	.bd
		margin 0 0 15px
		p
			margin 0
	ul
		transition height .2s ease
	li
		position relative
	.inner
		position relative
		padding 7px 28px 7px 30px
		border 1px solid transparent
		margin 0 -1px
		border-radius 3px
		background #fff
		min-height 36px
	.uploadify-progress
		position static
		margin-top 9px
	// hover
	li:hover
		.inner
			border 1px solid $colorLight
			margin 0 -6px
			padding 7px 33px 7px 35px
			.drag-area
				left 5px
			.remove
				right 8px

	.drag-area
		position absolute
		top 7px
		left 0
		bottom 7px
		width 20px
		background $colorGray
		border-radius 3px
		cursor pointer
		&:after
			content ''
			position absolute
			left 5px
			right 5px
			top 50%
			margin-top -7px
			height 1px
			background darken($colorGray, 20%)
			box-shadow 0 1px 0 lighten($colorGray, 50%), 0 3px 0 darken($colorGray, 20%), 0 4px 0 lighten($colorGray, 50%), 0 6px 0 darken($colorGray, 20%), 0 7px 0 lighten($colorGray, 50%), 0 9px 0 darken($colorGray, 20%), 0 10px 0 lighten($colorGray, 50%), 0 12px 0 darken($colorGray, 20%), 0 13px 0 lighten($colorGray, 50%)
	.remove
		position absolute
		right 3px
		top 50%
		margin-top -8px
		color $colorRed

	// move
	.ui-sortable-helper
		box-shadow 0 0 10px hexa(#000, .15)

.btns-attached
	em
		margin-left 1em

.crossroad-images
	margin 20px 0 0
	position relative
	ul
		inline-list(160px, $font)
		margin-left -10px
	li
		@media(min-width:1200px)
			width 25%
		@media(min-width:1400px)
			width 16.666%
	.inner
		margin-bottom 10px
		.name
			display block
			white-space nowrap
			text-overflow ellipsis
			overflow hidden
	.thumb
		position relative
		z-index 2
		border 1px solid $colorLight
		padding 10px
		margin-left 10px
		margin-bottom 10px
		background #fff
		overflow hidden
	.remove
		position absolute
		// schování pro animaci
		right -50px
		top -50px
		opacity 0
		//
		color $colorRed
		transition opacity .5s ease
	.img
		position relative
		display block
		width 100%
		padding-top 100%

	img
		max-width 100%
		max-height 100%
		width auto
		height auto
		position absolute
		margin auto
		top 0
		bottom 0
		right 0
		left 0

	// loading
	.loading img
		opacity 0

	.detail
		position absolute
		left 0
		width 100%
		overflow hidden
		height 0px
		/*&:after
			content ''
			position absolute
			bottom 0
			left 0
			right 0
			height 2px
			background $colorGray*/

	.detail-holder
		margin-top 10px
		border 2px solid $colorGray
		padding 18px 20px 20px


	// hover
	li:hover
		.thumb
			border 2px solid $colorGray
			padding 9px
			cursor pointer
		.remove
			opacity 1
			right 6px
			top 6px
	li.selected
	li.selected:hover
		.thumb
			border 2px solid $colorMain
			padding 9px
			&:before
				content ''
				position absolute
				z-index 5
				size 30px
				background $colorMain
				top 0
				left 0
			&:after
				@extend .icon
				content '\e186'
				position absolute
				top 6px
				left 6px
				z-index 6
				color #fff
		.remove
			right 6px
			top 6px

	// move
	.ui-sortable-helper
		.thumb
			box-shadow 0 0 10px hexa(#000, .15)
	// detail
	.active-detail
		li
			opacity .5
	.active
	.active:hover
		opacity 1
		.thumb
			border 2px solid $colorGray
			padding 9px
			padding-bottom 23px
			margin-bottom -12px
			border-bottom-width 0px
			&:after
				//content ''
				position absolute
				bottom -12px
				left 50%
				margin-left -10px
				height 0
				width 0
				overflow hidden
				border-width 0 10px 10px 10px
				border-color transparent transparent $colorLight transparent
				border-style solid
/*!
 *	Base layout
 */
@import 'layout/header.styl'
@import 'layout/main.styl'
@import 'layout/footer.styl'

/*!
 *	Menu
 */
@import "layout/menu/accessibility.styl"
@import "layout/menu/main.styl"
@import "layout/menu/tabs.styl"
@import "layout/menu/tree.styl"


/*!
 *	Crossroads
 */
@import "layout/crossroad/custom-fields.styl"
.crossroad-params-table
	margin -1.1em 0 0
	td
		padding 10px 0 0 10px
	td:first-child
		padding-left 0
		label
			min-height 30px

	.title
		span
			margin-top 15px
			display block
			color $colorTitles
			font-size: 1.28571em;
			padding-top 10px
			padding-bottom 8px
			border-top 1px solid $colorLight

/*!
 *	Box
 */
.box-param-type h2
	margin .65em 0

.box-title
	margin 0 0 18px
	line-height 44px
	h1
	h2
		line-height 44px
		overflow hidden
		zoom 1
	.r
		margin-left 30px
		margin 0

.box-detail-table
	margin-top 2.5em
	margin-bottom 2.5em
	td
		padding-top 6px
	tr:first-child td
		padding-top 0
	td:first-child
		width 0
		white-space nowrap
		padding-right 20px


/*!
 *	Form
 */
@import "layout/form/search.styl"

.form-filter
	position relative
	margin 25px -30px
	padding 17px 90px 8px 30px
	background $colorLight
	p
		margin-bottom 12px
	.btns
		position absolute
		right 30px
		bottom 15px
		.icon
			display inline-block
			vertical-align middle
			margin-left 10px


:first-child
	margin-top 0
:last-child:not([class*="u-mb-"])
	margin-bottom 0
