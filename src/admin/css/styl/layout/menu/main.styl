.menu-main
	font 300 15px/21px $fontTitles
	margin 0 0 30px
	h2
		font-size 11px
		text-transform uppercase
		font-weight bold
		color darken($colorDarkText, 73.5%)
		margin 0 0 1em
		// opacity 0
		// transition opacity $colSideDuration ease
		// .menu-hover &
		// 	opacity 1
	a
		position relative
		display block
		padding 7px ($gutter) 8px ($gutter + 25)
		margin 0 (-($gutter))
		text-decoration none
		color darken($colorDarkText, 60%)
		transition color $t ease, background $t ease
		.icon
			position absolute
			left $gutter
			top 10px
		.name
			// opacity 0
			// transition opacity $colSideDuration ease
			// .menu-hover &
			// 	opacity 1
		&:hover
			color $colorDarkText
		&.active
			background darken($colorDarkText, 85%)
			color $colorDarkText


