.menu-tree
	overflow hidden
	overflow-x auto
	padding-bottom 50px
	li
		&:before
			display none

// DEFAULT (from js)
.jstree ul
.jstree li
	display block
	margin 0 0 0 0
	padding 0 0 0 0
	list-style-type none

.jstree
	li
		display block
		min-height 18px
		line-height 18px
		white-space nowrap
		margin-left 18px
		min-width 18px
	& > ul
		& > li
			margin-left 0px
	ins
		display inline-block
		text-decoration none
		width 18px
		height 24px
		vertical-align middle
		margin 0 0 0 0
		padding 0
	a
		display inline-block
		line-height 16px
		height 16px
		color $color
		vertical-align middle
		white-space nowrap
		text-decoration none
		padding 4px 4px
		margin 0
		&:focus
			outline none
		& > ins
			height 16px
			width 16px
		& > .jstree-icon
			margin-right 3px

.jstree-rtl
	li
		margin-left 0
		margin-right 18px
	& > ul
		& > li
			margin-right 0px
	a
		& > .jstree-icon
			margin-left 3px
			margin-right 0

li
	&.jstree-open
		& > ul
			display block
	&.jstree-closed
		& > ul
			display none


// THEME
.jstree
	li
	ins
		background-image url($img + 'ico/tree/d.png')
		background-repeat no-repeat
		background-color transparent
		color $colorMain

	li
		background-position -90px 1px
		background-repeat repeat-y
		&.jstree-last
			background transparent
	.jstree-open
		& > ins
			background-position -72px -101px
	.jstree-closed
		& > ins
			background-position -54px -101px
	.jstree-leaf
		& > ins
			background-position -36px -101px
	.jstree-hovered
		background lighten($colorMain, 80%)

	.jstree-clicked
		background $colorMain
		color $colorMainText

		> ins
			color $colorMainText
	a
		// sk
		> .jstree-icon
			background none
			vertical-align middle
			position relative
			top -1px
			margin-right 5px
			@extend .icon
			@extend .icon-folder


		&.jstree-loading
			.jstree-icon
				background url($img + 'ico/tree/throbber.gif') center center no-repeat !important
		&.jstree-search
			color aqua
	// sk
	.jstree-open
		> a > ins
			@extend .icon-folder-open
	// sk
	.jstree-leaf
		> a > ins
			@extend .icon-file
			//color $color
			//font-size 14px

	// Strom s ikonou složky v první úrovni
	&.one-level > ul > .jstree-leaf > a > ins
		@extend .icon-folder

	.jstree-no-dots
		.jstree-open
			& > ins
				background-position -18px 0
		.jstree-closed
			& > ins
				background-position 0 0
	.jstree-no-icons
		a
			.jstree-icon
				display none
		.jstree-checkbox
			display inline-block
	.jstree-search
		font-style italic
	.jstree-no-checkboxes
		.jstree-checkbox
			display none !important
	.jstree-checked
		& > a
			& > .jstree-checkbox
				background-position -38px -19px
				&:hover
					background-position -38px -37px
	.jstree-unchecked
		& > a
			& > .jstree-checkbox
				background-position -2px -19px
				&:hover
					background-position -2px -37px

	.jstree-undetermined
		& > a
			& > .jstree-checkbox
				background-position -20px -19px
				&:hover
					background-position -20px -37px

	.jstree-locked
		a
			color silver
			cursor default

.jstree .jstree-no-dots li
.jstree .jstree-no-dots .jstree-leaf > ins
	background transparent

#vakata-dragged
	&.jstree
		ins
			background transparent !important
		.jstree-ok
			background url($img + 'ico/tree/d.png') -2px -53px no-repeat !important
		.jstree-invalid
			background url($img + 'ico/tree/d.png') -18px -53px no-repeat !important

#jstree-marker
	&.jstree
		background url($img + 'ico/tree/d.png') -41px -57px no-repeat !important
		text-indent -100px

body
	#vakata-contextmenu
		background #fff
		border 1px solid $colorGray
		box-shadow 0 0 10px hexa(#000, .15)
		border-radius 3px
		padding 5px
		li
			zoom 1
			&:before
				display none
			&.vakata-separator
				background white
				border-top 1px solid #e0e0e0
				margin 0
			a
				color $color
				line-height 20px
				padding 1px 10px
			ins
				display none
		li a:hover
		.vakata-hover > a
			color $color
			padding 1px 10px
			background lighten($colorMain, 80%)
			border none
			color black
			border-radius 2px


