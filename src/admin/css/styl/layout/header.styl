// Header
#header
	clearfix()
	background $colorMain
	color $colorMainText
	position relative
	z-index 10
	padding 0 0 0 $gutter
	margin-left -($colSideWidthS)
	min-height 50px
	transition margin-left $colSideDuration ease
	a
		color $colorMainText
	.page-login &
		padding-top 100px
		padding-bottom 15px
		margin-left 0
		text-align center

	// hover menu
	.menu-hover &
		margin-left -($colSideWidth)

#user
	float right
	position relative
	padding 0 70px 0 25px
	margin 0
	line-height 48px

	> .icon
		position absolute
		top 16px
		left 0
	.logout
		position absolute
		right 0
		top 0
		text-decoration none
		background hexa(#000, .2)
		width 50px
		line-height 50px
		text-align center
		transition background .2s ease
		&:hover
			background hexa(#000, .3)

#logo
	float left
	font 20px/46px $font
	margin 1px 0 0
	padding 0
	.icon
		margin-right 10px
	.page-login &
		float none
		font-size 32px