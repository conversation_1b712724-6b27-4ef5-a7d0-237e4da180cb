.skbox-overlay
	position fixed
	z-index 100
	left 0
	top 0
	width 100%
	height 100%
	background #000
	opacity 0.4

.skbox-window
	position absolute
	z-index 101
	//background #e1e1e1
	color #000
	//padding 8px
	border-radius 7px
	box-shadow 0 0 20px rgba(0,0,0,.5)
	&:focus
		outline none
	.skbox-content
		position relative
	.skbox-spc
		position absolute
		left 20px
		top 20px
		bottom 20px
		right 20px
		overflow auto
	.has-scroll
		padding-right 20px
	.type-image
		.skbox-content
			text-align center
			span
				display inline-block
				height 100%
				vertical-align middle
		.skbox-spc
		img
			max-width 100%
			max-height 100%
			vertical-align middle
	& > .skbox-close
		//background url($img'lb/lb-close.png') 0 0 no-repeat
		cursor pointer
		//height 31px
		//width 31px
		text-decoration none
		//color transparent
		//font 0px/0px a
		color $colorRed
		position absolute
		right 14px
		top 14px
		margin 0

.skbox-window-fixed
	position fixed

.skbox-title
	padding 15px 40px 0 20px
	font $fontSizeH2/1.2 $fontTitles
	color $colorTitles
	display block

.no-title
	.skbox-title
		display none

.skbox-inner
	position relative
	height 100%
	background #fff

.skbox-window > .skbox-prev,
.skbox-window > .skbox-next,
.skbox-window > .skbox-pages
	display none

.skbox-window-group
	.skbox-pages
		display block
	.skbox-inner
		margin-right 110px

.skbox-slides
	position relative
	height 100%

.skbox-slide
	position absolute
	left 0
	top 0
	width 100%
	height 100%
	background #fff

.skbox-pages
	width 102px
	position absolute
	right 8px
	top 60px
	bottom 8px
	text-align center
	overflow hidden
	a
		display block
		font bold 16px/32px arial, helvetica, sans-serif
		color #333
		width auto
		margin 0 0 10px
		border 1px solid #a3a3a3
		padding 1px
		background #fff
		text-decoration none
		&:hover
			border-color #2886ca
			color #2886ca
	.active
		border-width 2px
		padding 0
		border-color #2886ca

.skbox-iframe
	position absolute
	top 0
	left 0
	width 100%
	height 100%

.has-loading
	background url($img'lb/lb-loading.gif') no-repeat 50% 50%




