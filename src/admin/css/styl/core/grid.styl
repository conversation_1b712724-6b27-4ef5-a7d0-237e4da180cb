/*!
 *	Grid
 */

.row
	position relative
	margin 0
	padding 0
	clearfix()

.row-main
	@extend .row
	margin 0 auto
	width 960px

// Cols
.col-side
	position relative
	float left
	background $colorDark
	margin-left -($colSideWidthS)
	padding ($gutter*1.5) $gutter
	width $colSideWidthS - $gutter * 2
	overflow hidden
	transition margin-left $colSideDuration ease, width $colSideDuration ease
	.menu-hover &
		width $colSideWidth - $gutter * 2
		margin-left -($colSideWidth)

.col-main
	padding ($gutter*1.5)
	overflow hidden
	&::after
		content ''
		display block
		height 70vh

.col-tree
	float left
	width 280px
	margin-left -10px
	padding-right 20px
	&:before
		content ''
		position absolute
		top 0
		bottom 0
		left ( 520px - ( $colSideWidth - $colSideWidthS ) )
		width 1px
		background $colorGray
		transition left $colSideDuration ease
		.menu-hover &
			left 520px

	.skbox-window &
		margin-left 0
		&:before
			display none

.col-content
	margin-left 320px

// Grid row
.grid-row
	inline-list(100%, $font)
	margin-left -($gutter)
	zoom 1
	> *
		padding-left ($gutter)
		box-sizing border-box
	> p
		margin-bottom 14px
		&.reset
			margin-bottom 0

.grid-1
	width 100%
.grid-1-2
	width 50%
.grid-1-3
	width 33.333%
.grid-2-3
	width 66.666%
.grid-1-4
	width 25%
.grid-2-4
	width 50%
.grid-3-4
	width 75%
.grid-1-5
	width 20%
.grid-2-5
	width 40%
.grid-3-5
	width 60%
.grid-4-5
	width 80%
