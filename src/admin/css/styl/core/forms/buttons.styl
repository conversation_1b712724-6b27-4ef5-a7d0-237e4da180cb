/* BUTTON */

.btn
	inline-block(middle)
	margin 0
	padding 0
	border none
	background none
	text-decoration none
	overflow visible
	cursor pointer
	position relative
	> span
		position relative
		display block
		padding 0 20px
		background $colorMain
		color $colorMainText
		font bold $fontSize/36px $font
		border-radius 3px
		transition background .2s ease

	&:not(.btn-disabled):hover
		> span
			background darken($colorMain, 10%)

// green
.btn-green
	> span
		background $colorGreen
		color #fff
	&:not(.btn-disabled):hover
		> span
			background darken($colorGreen, 10%)

// red
.btn-red
	> span
		background $colorRed
		color #fff
	&:not(.btn-disabled):hover
		> span
			background darken($colorRed, 10%)

// red
.btn-dark
	> span
		background $colorDark
		color $colorDarkText
	&:not(.btn-disabled):hover
		> span
			background darken($colorDark, 10%)

// margin
.btn + .btn
	margin-left 6px

// disabled
.btn-disabled
	opacity .5
	cursor default

// actived
.btn:not(.btn-disabled):active
	> span
		top 1px
		box-shadow inset 0 1px 1px hexa(#000, .5)

// icons
.btn-icon-before
	> span
		padding-right 15px
		padding-left 35px
	.icon
		position absolute
		left 11px
		top 50%
		margin-top -8px

.btn-icon-after
	> span
		padding-left 15px
		padding-right 35px
	.icon
		position absolute
		right 11px
		top 50%
		margin-top -8px

.icon-checkmark.r
	margin-left 10px
	margin-top 12px
