.ui-datepicker
	display none
	background $colorLight
	box-sizing border-box
	padding 15px 18px 12px
	// box-shadow 0 0 5px rgba(#000, .25)
	width 270px
	min-height 213px
	z-index 2 !important // overrides inline style, prevents overflowing uploaded images
	@media(min-width:651px)
		width 419px
		padding-right 189px
		&:after
			content ''
			position absolute
			right 170px
			top 56px
			bottom 20px
			width 1px
			background $colorGray

.ui-datepicker-header
	position relative
	margin 0 0 17px

.ui-datepicker-calendar
	margin 0
	border none
	font-size 12px
	font-weight 600
	td
	th
		background none
		padding 0
		border none
		text-align center
		text-transform uppercase
		color #a8a8a8
		padding-bottom 4px
		+ td
		+ th
			padding-left 15px
		a
			text-decoration none
			color #565656
		.ui-state-highlight
			color $colorBrand
	th
		font-size 10px
		font-weight bold
		padding-bottom 9px

.ui-datepicker-prev
.ui-datepicker-next
	position absolute
	top 2px
	left 0
	@extend .icon
	font-size 14px
	margin-left -7px
	color $colorTitles
	text-decoration none
	cursor pointer
	.ui-icon
		display none
.ui-datepicker-prev
	@extend .icon-arrow-left-2
.ui-datepicker-next
	left auto
	right 0
	@extend .icon-arrow-right-2

.ui-datepicker-title
.ui_tpicker_time
	text-align center
	font bold 14px/18px $font
	color $colorBrand
	text-transform uppercase
.ui_tpicker_time
	margin-bottom 24px
.ui-datepicker-year
.ui-datepicker-current
.ui_tpicker_time_label
.ui_tpicker_minute_label
.ui_tpicker_hour_label
.ui_tpicker_second_label
	display none

.ui-timepicker-div
	width 134px
	padding-top 15px
	@media(min-width:651px)
		position absolute
		right 0
		top 0
		padding-right 18px


.ui_tpicker_minute
.ui_tpicker_hour
.ui_tpicker_second
	position relative
	padding 18px 0 44px
	width 36px
	float left
	.ui-spinner-up
	.ui-spinner-down
		position absolute
		left 50%
		top 50px
		@extend .icon
		font-size 14px
		margin-left -7px
		color $colorTitles
		text-decoration none
		cursor pointer
		.ui-button-text
			display none
	.ui-spinner-up
		top 0
		@extend .icon-arrow-up-2
	.ui-spinner-down
		@extend .icon-arrow-down-2

.ui_tpicker_hour
.ui_tpicker_minute
.ui_tpicker_second
	margin 0
	&:after
		content 'hod.'
		position absolute
		bottom 0
		left 0
		right -3px
		text-align center
		color $color
		font-size 12px

.ui_tpicker_minute
.ui_tpicker_second
	// float right
	margin-left 13px
	&:before
		content ':'
		position absolute
		left -8px
		top 19px
		color #c5c5c5
	&:after
		content 'min.'
.ui_tpicker_second
	&:after
		content 'sec.'

.ui-timepicker-input
	padding 5px 5px 3px
	text-align center
	width 100% !important
	height 28px

.ui-datepicker-buttonpane
	display none
	position absolute
	bottom 17px
	right 15px

button.ui-datepicker-close
	//reset
	inline-block(middle)
	text-hide()
	margin 0
	padding 0
	border none
	background none
	text-decoration none
	// style (buttons copy)
	border-top 1px solid #7ac53b
	border-bottom 2px solid #63933d
	border-radius 3px
	height 36px
	width 70px
	line-height 36px
	padding 2px 0 0
	background #76ad48
	color #fff
	text-decoration none
	transition background-color .2s, border-color .2s
	text-align center
	&:after
		content ''
		inline-block(middle)
		position relative
		top -3px
		sprite-btn-tick-size()
	&:hover
		background lighten(#76ad48, 15%)
