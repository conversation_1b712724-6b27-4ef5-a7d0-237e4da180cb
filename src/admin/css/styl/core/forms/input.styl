// input text
.inp-text
	display block
	border 2px solid $colorGray
	padding 6px 10px
	background #fff
	color $colorTitles
	font-family $font
	font-size 14px
	line-height 20px
	height 36px
	box-sizing border-box
	border-radius 3px
	&:focus
		border-color darken($colorGray, 20%)
	&.error
		border-color $colorRed

// textarea
textarea.inp-text
	height auto

// select
select.inp-text
	&[multiple]
		height auto


@media (-webkit-min-device-pixel-ratio:0)
	.inp-fix-select
		select.inp-text
			padding-right 25px
			appearance none
		&:after
			content ''
			position absolute
			top 16px
			right 10px
			height 0
			width 0
			overflow hidden
			border-width 6px 6px 0 6px
			border-color $colorTitles transparent transparent transparent
			border-style solid
			pointer-events none
	// webkit
	// hack aby se na multiselectu zobrazil scrollbar
	// akutálně žádný vnitřní padding
	.inp-fix-multiselect
		select
			padding 0
			//border 0px

.w-full
	width 100%

label + br ~ *
label + br ~ .btn
	margin-top 3px

.inp-fix
	display block
	position relative
	overflow hidden
	color $colorTitles
	.inp-text
		width 100%


.btn.r
	+ .inp-fix
		padding-right 10px

.inp-item
	position relative
	padding-left 25px
	display inline-block
	+ .inp-item
		margin-left 20px
	input[type="checkbox"],
	input[type="radio"]
		position absolute
		left -5000px
		+ label
		+ span
			&:before
				content ''
				position absolute
				top 0
				left 0
				margin-top 3px
				size 12
				border 2px solid $colorGray
				border-radius 3px
				background #fff
		&:focus
			+ label
			+ span
				&:before
					border-color darken($colorGray, 20%)

	input[type="checkbox"]
		&:checked
			+ label
			+ span
				&:after
					@extend .icon
					content '\e186'
					position absolute
					left 4px
					top 1px
					margin-top 3px
					font-size 12px

	input[type="radio"]
		+ label
		+ span
			&:before
				border-radius 8px
				left 1px
		&:checked
			+ label
			+ span
				&:after
					content ''
					position absolute
					top 5px
					left 6px
					margin-top 3px
					size(6)
					background $color
					border-radius 3px

.inp-list
	.inp-item
		display block
		margin 0 0 2px

label
	cursor pointer

// icon
.inp-icon-after
	.inp-text
		padding-right 35px
	.icon
		position absolute
		right 10px
		top 10px
	span.icon
		pointer-events none

.inp-icon-before
	.inp-text
		padding-left 35px
	.icon
		position absolute
		left 10px
		top 10px
	span.icon
		pointer-events none


// Special rules
.no-label
	// 24px je line-height labelu + 3px mezera od inputu
	padding-top 27px
.inp-center
.inp-center:first-child
	// zarovnat na vertikální střed inputu
	margin-top 6px
	display inline-block
