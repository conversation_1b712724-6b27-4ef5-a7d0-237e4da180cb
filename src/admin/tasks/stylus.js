var stylExists = require('./helpers/stylExists.js');

var gulp = require('gulp');
var stylus = require('gulp-stylus');
var poststylus = require('poststylus');
var autoprefixer = require('autoprefixer');
var notify = require('gulp-notify');
var plumber = require('gulp-plumber');
var sourcemaps = require('gulp-sourcemaps');

module.exports = gulp.task('default', function() {
	var onError = function(error) {
		notify.onError({
			title: 'Stylus error!',
			message: '<%= error.message %>',
			sound: 'Beep',
		})(error);

		return this.emit('end');
	};

	var settings = {
		paths: ['bower_components', 'node_modules'],
		use: [poststylus([autoprefixer()]), stylExists],
		// rawDefine: {
		// 	data: config,
		// },
		// compress: isProduction(),
		linenos: false,
		firebug: false,
		'include css': true,
	};

	var stream = gulp.src(['style.styl'], {
		cwd: 'css/styl/',
	});

	stream
		.pipe(
			plumber({
				errorHandler: onError,
			}),
		)
		.pipe(sourcemaps.init())
		.pipe(stylus(settings))
		.pipe(sourcemaps.write('./'))
		.pipe(gulp.dest('../../www/admin/css'));

	return stream;
});
