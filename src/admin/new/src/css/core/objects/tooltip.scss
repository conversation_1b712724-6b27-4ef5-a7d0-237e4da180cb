.tooltip {
	position: relative;
	&__content {
		position: absolute;
		top: 100%;
		left: 50%;
		z-index: 30;
		margin: 0;
		padding: $xxs $xs;
		border-radius: $radius;
		background: $colorBlack;
		color: $colorWhite;
		font-size: $fontSizeSm;
		line-height: $lineHeight;
		white-space: nowrap;
		visibility: hidden;
		opacity: 0;
		transform: translateX(-50%);
		transition: opacity $t, margin $t, visibility 0s $t;
		pointer-events: none;
		&::before {
			content: '';
			position: absolute;
			bottom: 100%;
			left: 50%;
			margin-left: $xxs * -1;
			border-width: 0 $xxs $xxs;
			border-style: solid dashed;
			border-color: $colorBlack transparent;
		}
	}

	&:hover &__content {
		margin-top: 10px;
		visibility: visible;
		opacity: 1;
		transition-delay: 0s, 0s, 0s;
	}
}
