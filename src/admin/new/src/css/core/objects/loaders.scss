.btn-loader {
	.btn__text {
		&::after {
			content: '';
			position: absolute;
			top: 50%;
			left: 50%;
			width: 16px;
			height: 16px;
			margin: -8px 0 0 -8px;
			border: 2px solid;
			border-radius: 8px;
			border-top-color: transparent;
			opacity: 0;
			transition: opacity $t;
		}
	}

	// STATES
	&.is-loading {
		pointer-events: none;
	}
	&.is-loading .btn__text {
		text-indent: -5000px;
		&::after {
			opacity: 1;
			animation: animation-rotate 0.8s infinite linear;
		}
	}
	&.is-loading .icon-svg {
		opacity: 0;
	}
}

.block-loader {
	$s: '.block-loader';
	position: relative;
	&__loader {
		position: absolute;
	  	z-index: 100;
		top: 50%;
		left: 50%;
		width: 80px;
		height: 80px;
		border-radius: 40px;
		background: rgba(#000000, 0.5);
		visibility: hidden;
		opacity: 0;
		transform: translate(-50%, -50%);
		transition: opacity $t 0s, visibility $t 0s;
		&::after {
			content: '';
			position: absolute;
			top: 50%;
			left: 50%;
			width: 40px;
			height: 40px;
			margin: -20px 0 0 -20px;
			border: 4px solid #ffffff;
			border-radius: 20px;
			border-top-color: transparent;
			box-sizing: border-box;
		}
	}

	// STATES
	&.is-loading {
		pointer-events: none;
		#{$s}__loader {
			visibility: visible;
			opacity: 1;
			transition-delay: 0s, 0s;
			&::after {
				animation: animation-rotate 0.8s infinite linear;
			}
		}
		#{$s} #{$s}__loader {
			visibility: hidden;
			opacity: 0;
			&::after {
				animation: none;
			}
		}
	}
}

.body-loader {
	&__loader {
		position: fixed;
		top: 50%;
		left: 50%;
		width: 80px;
		height: 80px;
		border-radius: 40px;
		background: rgba(#000000, 0.5);
		visibility: hidden;
		opacity: 0;
		transform: translate(-50%, -50%);
		transition: opacity $t 0s, visibility $t 0s;
		&::after {
			content: '';
			position: absolute;
			top: 50%;
			left: 50%;
			width: 40px;
			height: 40px;
			margin: -20px 0 0 -20px;
			border: 4px solid #ffffff;
			border-radius: 20px;
			border-top-color: transparent;
		}
	}

	// STATES
	&.is-loading {
		pointer-events: none;
	}
	&.is-loading &__loader {
		visibility: visible;
		opacity: 1;
		transition-delay: 0s, 0s;
		&::after {
			animation: animation-rotate 0.8s infinite linear;
		}
	}
}

// suggest loader
.inp-text {
	&.is-loading ~ &__holder {
		&::after {
			content: '';
			position: absolute;
			top: 50%;
			right: 30px;
			width: 16px;
			height: 16px;
			margin: -8px 0 0 -8px;
			border: 2px solid;
			border-radius: 8px;
			border-top-color: transparent;
			animation: animation-rotate 0.8s infinite linear;
			box-sizing: border-box;
		}
	}
}
