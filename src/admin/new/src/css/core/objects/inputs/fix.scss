.inp-fix {
	position: relative;
	display: flex;
	.btn {
		white-space: nowrap;
	}
	.btn-icon {
		height: 38px;
		&:not(.b-list__mask) {
			z-index: 0;
		}
		svg {
			height: 14px;
		}
	}
	&__sufix,
	&__prefix {
		min-width: 40px;
		padding: 0 $xs;
		background: $colorBgLighten;
		line-height: 38px;
		text-align: center;
	}

	// STATEs
	.has-error & {
		color: $colorRed;
	}
	.has-warning & {
		color: $colorOrange;
	}
	.has-ok & {
		color: $colorGreen;
	}
}
