.inp-item {
	position: relative;
	display: block;
	padding-left: 30px;
	cursor: pointer;
	&__inp {
		position: absolute;
		left: -5000px;
	}
	&__text {
		display: block;
		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			width: 20px;
			height: 20px;
			border: 1px solid $colorBd;
			border-radius: $radius;
			background: #ffffff;
			transition: border-color $t;
			box-shadow: $shadow;
		}
		&::after {
			content: '';
			position: absolute;
			top: 5px;
			left: 5px;
			width: 10px;
			height: 10px;
			background: $colorPrimary;
			opacity: 0;
			transition: opacity $t;
		}
	}

	// VARIANTS
	&--radio &__text {
		&::before {
			border-radius: 10px;
		}
		&::after {
			border-radius: 5px;
		}
	}
	&--checkbox &__text {
		&::after {
			width: 11px;
			height: 6px;
			border: 2px solid $colorPrimary;
			border-width: 0 0 2px 2px;
			background: none;
			transform: rotate(-45deg);
		}
	}
	&--icon {
		padding: 0;
	}
	&--icon &__text {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 24px;
		height: 24px;
		border: 1px solid $colorBd;
		border-radius: $radius;
		line-height: 1;
		transition: color $t, border-color $t;
		box-shadow: $shadow;
		&::before {
			top: -5px;
			left: -5px;
			width: 10px;
			height: 10px;
			border-radius: 10px;
			background: $colorPrimary;
			opacity: 0;
			transition: opacity $t;
		}
		&::after {
			top: -2px;
			left: -2px;
			width: 5px;
			height: 3px;
			border: 1px solid $colorWhite;
			border-width: 0 0 1px 1px;
		}
		.icon {
			width: 14px;
			height: 14px;
		}
	}

	// STATES
	.has-error &__text {
		&::before {
			border-color: $colorRed;
		}
	}

	&__inp:focus + &__text {
		&::before {
			border-color: $colorPrimary;
		}
	}

	&__inp:disabled + &__text {
		color: rgba($colorText, 0.5);
		cursor: default;
		&::before {
			background: $colorBg;
		}
	}

	&__inp:checked + &__text {
		&::before,
		&::after {
			opacity: 1;
		}
	}

	&--icon &__inp:checked + &__text {
		color: $colorPrimary;
	}
	&--icon:hover &__text {
		border-color: $colorPrimary;
	}
}
