.btn-icon {
	display: inline-flex;
	vertical-align: middle;
	justify-content: center;
	align-items: center;
	width: 24px;
	height: 24px;
	padding: 0;
	border: 0;
	border-radius: $radius;
	background: none;
	background: $colorBgLight;
	color: $colorTextLight;
	text-decoration: none;
	transition: background-color $t, color $t;
	cursor: pointer;
	svg {
		width: 60%;
		height: 60%;
	}

	// VARIANTs
	&[disabled],
	&--disabled {
		color: $colorTextLighten;
		cursor: not-allowed;
	}
	&--grab {
		background: none;
		cursor: move;
		cursor: grab;
		svg {
			width: 16px;
			height: 16px;
		}
	}
	&--checkbox {
		border: 1px solid $colorBd;
		background: $colorWhite;
		box-shadow: $shadow;
		&::before {
			content: '';
			position: absolute;
			top: -5px;
			left: -5px;
			width: 10px;
			height: 10px;
			border-radius: 10px;
			background: $colorPrimary;
			opacity: 0;
			transition: opacity $t;
		}
		&::after {
			content: '';
			position: absolute;
			top: -2px;
			left: -2px;
			width: 5px;
			height: 3px;
			border: 1px solid $colorWhite;
			border-width: 0 0 1px 1px;
			background: none;
			opacity: 0;
			transform: rotate(-45deg);
			transition: opacity $t;
		}
	}

	// STATEs
	.hoverevents &:not([disabled]):not(&--disabled):hover {
		color: $colorPrimary;
	}
	.hoverevents &--remove:not([disabled]):not(&--disabled):hover {
		color: $colorRed;
	}

	.hoverevents &--checkbox:not([disabled]):not(&--disabled):hover {
		border-color: $colorPrimary;
		background: $colorWhite;
		color: $colorTextLight;
	}
	&--checkbox.is-active {
		color: $colorPrimary;
		&::before,
		&::after {
			opacity: 1;
		}
	}
}
