.row-main {
	@include clearfix();
	max-width: $rowMainWidth;
	margin: 0 auto;
	padding: 0 $rowMainGutter;
}
.row-main-big {
	@include clearfix();
	margin: 0 auto;
	padding: 0 $rowMainGutter;
}
.grid-inline {
	display: flex;
	align-items: center;
	margin-left: $xs * -1;
	> *:not(.grid-inline) {
		margin-left: $xs;
	}
}
.flex-grow {
	flex: 1;
}
.grid {
	@extend %reset-ol;
	@extend %grid;
	margin-bottom: -($gridGutter);
	margin-left: -($gridGutter);
	&__cell {
		@extend %reset-ol-li;
		@extend %grid__cell;
		position: relative;
		border: $gridGutter solid transparent;
		border-width: 0 0 $gridGutter $gridGutter;

		// hide the border in MS high contrast mode
		border-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg'%3E%3C/svg%3E");
		&--top {
			align-self: flex-start;
		}
		&--middle {
			align-self: center;
		}
		&--bottom {
			align-self: flex-end;
		}
		&--eq {
			display: flex;
			> * {
				flex: 1 1 auto;
			}
		}
	}

	// VARIANTs
	&--scroll {
		@extend %grid--scroll;
	}
	&--nowrap {
		flex-wrap: nowrap;
	}
	&--middle {
		align-items: center;
	}
	&--bottom {
		align-items: flex-end;
	}
	&--center {
		justify-content: center;
	}
	&--right {
		justify-content: flex-end;
	}
	&--space-between {
		justify-content: space-between;
	}

	&--y-0 {
		margin-bottom: 0;
	}
	&--y-0 &__cell {
		border-bottom-width: 0;
	}
	&--y-xs {
		margin-bottom: $xs * -1;
	}
	&--y-xs &__cell {
		border-bottom-width: $xs;
	}
	&--y-md {
		margin-bottom: $md * -1;
	}
	&--y-md &__cell {
		border-bottom-width: $md;
	}

	&--x-0 {
		margin-left: 0;
	}
	&--x-0 &__cell {
		border-left-width: 0;
	}
	&--x-xs {
		margin-left: $xs * -1;
	}
	&--x-xs &__cell {
		border-left-width: $xs;
	}
	&--x-md {
		margin-left: $md * -1;
	}
	&--x-md &__cell {
		border-left-width: $md;
	}
}
.size {
	@include generateGridSize();

	&--side {
		width: 300px;
	}
	&--content {
		flex: 1 1 auto;
	}

	@media (max-width: 1799.98px) {
		&--lang {
			width: 100%;
		}
	}
}

// .push {
// 	@include generateGridPush();
// }
// .pull {
// 	@include generateGridPull();
// }
// .order {
// 	@include generateGridOrder();
// }
