// Colors
$colorPrimary: #3cdbc0;
$colorSecondary: #9b26b6;

$colorBlack: #000000;
$colorWhite: #ffffff;
$colorRed: #ff0000;
$colorGreen: #008800;
$colorOrange: #ffaa00;

$colorText: #222222;
$colorTextLight: rgba($colorText, 0.75);
$colorTextLighten: rgba($colorText, 0.5);
$colorBd: #cccccc;
$colorBg: #eeeeee;
$colorBgPrimary: rgba($colorPrimary, 0.2);
$colorBgLight: lighten(#eeeeee, 1%);
$colorBgLighten: lighten(#eeeeee, 3%);
$colorLink: $colorTextLighten;
$colorHover: $colorPrimary;

$colorFacebook: #3b5998;
$colorTwitter: #1da1f2;
$colorGoogle: #dd4b39;
$colorYoutube: #ff0000;
$colorLinkedIn: #0077b5;
$colorInstagram: #c13584;
$colorPinterest: #bd081c;

// Font
$fontSystem: -apple-system, blinkmacsystemfont, 'Segoe UI', roboto, helvetica, arial, sans-serif;
$fontPrimary: Arial, Helvetica, sans-serif;
$fontSecondary: $fontPrimary;
$fontSizeXs: 11px;
$fontSizeSm: 12px;
$fontSize: 14px;
$fontSizeMd: 16px;
$lineHeight: (20 / 14);

// Spacing
$xxs: 5px;
$xs: 10px;
$sm: 20px;
$md: 40px;
$lg: 60px;
$xl: 80px;

// Typography
$typoSpaceVertical: $sm;

// Grid
$gridColumns: 12;
$gridGutter: 20px;
$rowMainWidth: 900px;
$rowMainGutter: $gridGutter;

// Paths
$imgPath: map-get($paths, 'images');
$fontsPath: map-get($paths, 'fonts');

// Transitions
$t: 0.15s;

// Radius
$radius: 2px;
$radiusSm: 4px;
$radiusMd: 6px;
$radiusLg: 8px;

// Shadows
$shadow: 0 2px 4px rgba($colorBlack, 0.05);


$svgActiveColor: rgba($colorText, 0.9);
$svgInActiveColor: rgba($colorWhite, 0.9);

// SVGs
$svgBullet: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3E%3Cpath d='M0 0h4v4H0z'/%3E%3C/svg%3E%0A";
$svgSelect: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 5'%3E%3Cpath d='M10 0L5 5 0 0'/%3E%3C/svg%3E%0A";
$iconFolder: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='"+$svgActiveColor+"' d='M1,14.5h14l1-10H0L1,14.5z M8,2.5l-0.5-1H2l-1,2h14l-0.5-1H8z' /%3E%3C/svg%3E%0A";
$iconFolderOpen: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='"+$svgActiveColor+"' d='M0,14.5l3-8h13l-3,8H0z M13,5.5v-2H6.5l-2-2H0v13l2-9H13z' /%3E%3C/svg%3E%0A";
$iconFile: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='"+$svgActiveColor+"' d='M15.5,1.5v13c0,0.8-0.7,1.5-1.5,1.5H2c-0.8,0-1.5-0.7-1.5-1.5v-13C0.5,0.7,1.2,0,2,0h12C14.8,0,15.5,0.7,15.5,1.5z M13.5,2 h-11v12h11V2z M4.5,8h7V7h-7V8z M4.5,10h7V9h-7V10z M4.5,12h7v-1h-7V12z M4.5,6h7V5h-7V6z' /%3E%3C/svg%3E%0A";
$iconFolderWhite: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='"+$svgInActiveColor+"' d='M1,14.5h14l1-10H0L1,14.5z M8,2.5l-0.5-1H2l-1,2h14l-0.5-1H8z' /%3E%3C/svg%3E%0A";
$iconFolderOpenWhite: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='"+$svgInActiveColor+"' d='M0,14.5l3-8h13l-3,8H0z M13,5.5v-2H6.5l-2-2H0v13l2-9H13z' /%3E%3C/svg%3E%0A";
$iconFileWhite: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='"+$svgInActiveColor+"' d='M15.5,1.5v13c0,0.8-0.7,1.5-1.5,1.5H2c-0.8,0-1.5-0.7-1.5-1.5v-13C0.5,0.7,1.2,0,2,0h12C14.8,0,15.5,0.7,15.5,1.5z M13.5,2 h-11v12h11V2z M4.5,8h7V7h-7V8z M4.5,10h7V9h-7V10z M4.5,12h7v-1h-7V12z M4.5,6h7V5h-7V6z' /%3E%3C/svg%3E%0A";
