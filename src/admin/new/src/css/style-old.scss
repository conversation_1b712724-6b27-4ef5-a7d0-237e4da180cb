@import 'base/variables';
@import 'base/mixins';
@import 'base/extends';

@import 'core/objects/buttons/icon';
@import 'core/objects/inputs/default';
@import 'core/objects/inputs/text';
@import 'core/objects/inputs/select';
@import 'core/objects/inputs/label';
@import 'core/objects/tooltip';
@import 'core/objects/grid';
@import 'core/objects/title';
@import 'core/objects/choices';
@import 'core/objects/item-icon';
@import 'core/objects/animations';
@import 'core/objects/loaders';
@import 'core/objects/tag';
@import 'core/generated/icons';
@import 'core/objects/cc-search';

@import 'components/box/std';
@import 'components/box/list';
@import 'components/box/imgs';
@import 'components/box/suggest';
@import 'components/box/toggle';
@import 'components/box/custom-field';
@import 'components/box/overlay';
@import 'components/box/library';
@import 'components/box/header';
@import 'components/menu/icons';

@import 'utilities/spacing';
@import 'utilities/visibility';

// fix for old superadmin
/* stylelint-disable declaration-no-important */
.inp {
	.inp-fix {
		display: flex;
		overflow: visible;
		&__sufix {
			min-width: 20px;
			padding: 0 $xs;
			border: 2px solid #dddddd;
			border-left: none;
			background: $colorBgLighten;
			line-height: 32px;
			text-align: center;
		}
	}
	.inp-text,
	.inp-select {
		box-sizing: border-box;
	}
	.inp-text__holder {
		display: none;
	}
	.inp-select {
		&--simple {
			height: auto;
			border: 1px solid $colorBd;
			border-radius: $radius;
			outline: none;
			transition: border-color $t;
			box-shadow: $shadow;
			&:focus {
				border-color: $colorPrimary;
			}
		}
		// &--multiple {
		// }
	}
	.choices__inner {
		border-color: $colorBd;
		.choices.is-open &,
		.choices.is-focused &,
		.choices.is-focused.is-open & {
			border-color: $colorPrimary;
		}
	}
	.choices__inner,
	.choices__list--dropdown,
	.choices__list[aria-expanded] {
		font-size: 14px;
		line-height: 18px;
		box-sizing: border-box;
	}
	.choices__list--multiple .choices__item {
		&:last-child:not([class*='u-mb-']) {
			margin-bottom: 5px;
		}
	}
	.choices__list--multiple:empty + .choices__input--cloned[placeholder] {
		margin-bottom: 1px;
	}
}
.btn {
	box-sizing: border-box;
	& > .item-icon {
		display: inline-flex;
	}
}
.b-overlay--library {
	position: fixed;
	top: 0;
	right: 0;
	.b-overlay__inner {
		position: fixed;
		overflow-y: auto;
	}
	.form-filter {
		margin: 0;
		padding: 0;
		background: none;
	}
}
.icon svg {
	width: 24px !important;
	height: 24px !important;
}
.b-imgs__name {
	color: #222222;
}
.tracy-row {
	li {
		&::before {
			display: none;
		}
	}
}

// suggest loader
.inp-text {
	&.is-loading ~ &__holder {
		display: block;
	}
}

// suggest clear button
.inp-fix {
	.btn-icon {
		height: 36px;
		svg {
			height: 14px;
		}
	}
}
