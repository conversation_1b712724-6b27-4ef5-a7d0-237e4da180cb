.main {
	display: grid;
	grid-template-columns: 220px 1fr;
	grid-template-rows: 1fr;
	grid-template-areas: 'side content';
	height: calc(100% - 50px);
	&__side {
		grid-area: side;
		padding: $sm;
		background: lighten($colorBlack, 10%);
	}
	&__main {
		position: relative;
		display: grid;
		grid-template-columns: 1fr 300px;
		grid-template-rows: auto 1fr;
		grid-template-areas:
			'header side'
			'content side';
		grid-area: content;
		min-height: 0;
	  	height: 100%;
	}
  	&__main--multi {
	  	grid-template-columns: 300px 1fr;
	  	display: flex;
	  	&--left {
			flex: 0 0 auto;
			width: 300px;
		  	padding: 20px;
		}
	  	&--right {
		  	flex: 1 1 auto;
		}
	}
	&__header {
		position: relative;
		z-index: 10;
		grid-area: header;
		min-width: 0;
	}
	&__content {
		grid-area: content;
		padding: $sm;
		&::after {
			content: '';
			display: block;
			height: 80vh;
		}
	}
	&__content-side {
		grid-area: side;
		padding: $sm;
		background: $colorBgLighten;
	}

	// VARIANTs
	&__main--one-column {
		// TODO: rozhodnout zda-li jsou potreba obe dve deklarace
		grid-template-columns: 1fr;
		grid-template-rows: auto 1fr auto;
		grid-template-areas:
			'header'
			'content'
			'side';
	}
	&__main--noside {
		// TODO: rozhodnout zda-li jsou potreba obe dve deklarace
		grid-template-columns: 1fr;
		grid-template-rows: 1fr auto;
		grid-template-areas:
			'header'
			'content'
			'side';
	}
	&__main--nospacing &__content,
	&__content--nospacing {
		&::after {
			display: none;
		}
	}
	&__main--nopadding &__content,
	&__content--nopadding {
		padding: 0;
	}
	&__main--valign-top {
		align-self: start;
	}
}
