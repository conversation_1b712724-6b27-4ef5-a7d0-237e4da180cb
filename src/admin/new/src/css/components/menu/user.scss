.m-user {
	&__list {
		display: flex;
	}
	&__link {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 100%;
		padding: 15px;
		color: $colorWhite;
		text-decoration: none;
		transition: background-color $t;
	}

	// VARIANTs
	&__link--logout {
		width: 50px;
		background: rgba($colorWhite, 0.3);
	}

	// STATEs
	.hoverevents &__link:hover {
		background: rgba($colorWhite, 0.15);
		color: $colorWhite;
	}
}
