.m-sections {
	&__item {
		@extend %reset-ul-li;
		margin-bottom: $xxs;
	}
	&__link {
		position: relative;
		display: flex;
		align-items: center;
		padding: 12px $sm;
		border: 1px solid $colorBd;
		border-top: 1px solid $colorBd;
		border-radius: $radius;
		color: $colorTextLight;
		text-decoration: none;
		transition: color $t, background-color $t;
		box-shadow: $shadow;
	}
	&__text {
		flex: 1;
		margin: 0 $xs;
	}
	&__title {
		display: block;
	}
	&__desc {
		font-size: $fontSizeSm;
	}

	// VARIANTs
	&__item--inactive &__link {
		color: $colorTextLighten;
	}

	// STATEs
	.hoverevents &__link:hover {
		background: $colorBg;
		color: $colorText;
	}
}
