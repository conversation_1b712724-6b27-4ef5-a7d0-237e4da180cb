.m-main {
	&__title {
		margin: 0 0 $xxs;
		color: rgba($colorWhite, 0.5);
		font-size: $fontSizeSm;
		text-transform: uppercase;
	}
	&__list {
		@extend %reset-ul;
		margin: 0 $sm * -1 $sm;
		font-size: $fontSizeMd;
	}
	&__item {
		@extend %reset-ul-li;
		margin-top: 1px;
	}
	&__link {
		display: flex;
		padding: $xs $sm;
		color: rgba($colorWhite, 0.5);
		text-decoration: none;
		transition: background-color $t, color $t;
	}

	// VARIANTs
	&__link--sub {
		padding-left: $sm + 26px;
		font-size: $fontSize;
	}

	// STATEs
	&__link.is-active,
	.hoverevents &__link:hover {
		background: rgba($colorWhite, 0.1);
		color: $colorWhite;
	}
}
