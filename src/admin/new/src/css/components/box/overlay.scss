.b-overlay {
	position: absolute;
	top: 0;
	right: 300px;
	left: 0;
	z-index: 20;
	height: 100%;
	overflow: hidden;
	visibility: hidden;
	opacity: 0;
	transition: opacity $t, visibility 0s $t;
	&__bg {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		background: rgba($colorBlack, 0.5);
		cursor: pointer;
	}
	&__inner {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 100px;
		display: flex;
		flex-direction: column;
		border-left: 1px solid $colorBd;
		background: $colorWhite;
		transform: translateX(100%);
		transition: transform $t;
	}
	&__content {
		flex: 1;
		padding: $sm $sm $sm 52px;
	}
	&__title {
		margin: 0 0 $md;
	}
	&__close {
		position: absolute;
		top: 22px;
		left: $sm;
		width: 24px;
		height: 24px;
		color: $colorTextLight;
	}

	// STATEs
	&.is-visible {
		visibility: visible;
		opacity: 1;
		transition-delay: 0s, 0s;
	}
	&.is-visible &__inner {
		transform: translateX(0);
	}

	// Variants
	.main__main--one-column & {
		right: 0;
	}
	&--full {
		top: 50px;
		right: 0;
	}
	&--library {
		top: 50px;
		bottom: 0;
		z-index: 1500;
		height: auto;
		.row-main {
			max-width: 1200px;
		}
	}
}
