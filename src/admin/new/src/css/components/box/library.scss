.b-library {
	display: grid;
	grid-template-columns: 250px 1fr;
	grid-template-rows: 1fr;
	&__menu {
		padding-right: 20px;
	}
	&__tree {
		margin: 0;
		.b-library__tree {
			padding-left: 20px;
		}
	}
	&__tree-wrapper {
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 100%;
	}
	&__actions {
		display: flex;
		align-items: center;
		.icon {
			margin: 0 3px;
			color: rgba(34, 34, 34, 0.5);
			cursor: pointer;
			&:hover {
				color: $colorPrimary;
			}
		}
	}
	&__tree-item {
		margin: 0;
		padding: 0;
		background: none;
		list-style-type: none;
		&::before {
			display: none;
		}
	}
	&__tree-link {
		padding: 5px;
		&.is-selected {
			background-color: $colorBgPrimary;
		}
	}

	@media (max-width: 1399.98px) {
		&__filter {
			&-global {
				width: 100%;
			}
		}
	}
}
