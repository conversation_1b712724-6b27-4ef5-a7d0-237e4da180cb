.b-toggle {
	position: relative;
	min-height: 20px;
	scroll-margin: $sm;
	&::after {
		content: '';
		// display: table;
		clear: both;
	}
	&__tool {
		position: absolute;
		top: 0;
		left: -32px;
		width: 20px;
		height: 20px;
	}
	&__content {
		position: absolute;
		top: -5000px;
		left: -5000px;
		margin-bottom: $sm * -1;
		padding: $xs 0 0;
		opacity: 0;
		transition: opacity $t;
		&--static {
			position: relative;
			top: auto;
			left: auto;
			display: none;
			opacity: 1;
		}
	}
	&__dragdrop {
		position: absolute;
		top: 0;
		left: 20px;
	}

	// STATEs
	.hoverevents &__tool:hover {
		background: $colorBg;
	}
	&.is-open > &__content {
		position: relative;
		top: auto;
		left: auto;
		opacity: 1;
		&--static {
			display: block;
		}
	}
	&.is-open > &__header &__tool svg {
		transform: rotate(90deg);
	}
	&--default + &--default {
		margin-top: $lg;
	}
	.inp + &--default {
		margin-top: $md - $xs;
	}
	&--default + .inp {
		margin-top: $lg - $xs;
	}
	// .hoverevents &:hover {
	// 	&::before {
	// 		opacity: 1;
	// 	}
	// }

	&--main > &__header {
		position: relative;
		padding: $sm $sm $sm 60px;
		background: $colorBg;
		overflow: hidden;
	}
	&--main > &__header &__tool {
		top: $sm;
		left: $sm;
		background: $colorWhite;
		&::before {
			content: '';
			position: absolute;
			top: -20px;
			right: -2000px;
			bottom: -20px;
			left: -20px;
		}
	}
	&--main > &__content {
		padding: $sm 0;
	}

	&--default {
		margin-left: $sm * -1;
		margin-left: 32px;
		padding: 0 0 0 $sm;
		border-left: 2px solid $colorBd;
		&::before {
			content: '';
			position: absolute;
			top: $xs * -1;
			right: $xs * -1;
			bottom: $xs * -1;
			left: -42px;
			border: 1px dashed $colorBd;
			border-radius: $radiusSm;
			pointer-events: none;
		}
	}
	&--default > &__header &__tool {
		&::before {
			content: '';
			position: absolute;
			top: 0;
			right: -830px;
			bottom: 0;
			left: 0;
		}
	}

	&--draggable {
		.b-toggle__header {
			padding-left: 30px;
		}
	}
}
