.b-suggest {
	position: absolute;
	top: 100%;
	right: -1px;
	left: -1px;
	z-index: 15;
	display: none;
	border: 1px solid $colorBd;
	border-radius: 0 0 $radius $radius;
	background: $colorBgLighten;
	box-shadow: $shadow;
	max-height: 350px;
	overflow: auto;
	&__list {
		@extend %reset-ul;
	}
	&__item {
		@extend %reset-ul-li;
		border-top: 1px solid $colorBd;
		&:first-child {
			border-top-width: 0;
		}
	}
	&__label {
		padding: 8px 10px;
		font-weight: bold;
	}
	&__link {
		display: block;
		padding: 8px 10px;
		color: $colorText;
		text-decoration: none;
		transition: background-color $t, color $t;
		cursor: pointer;
	}

	// STATEs
	.hoverevents &__link:hover,
	&__item.is-selected &__link {
		background: $colorPrimary;
		color: $colorWhite;
	}
	&.is-visible {
		display: block;
	}
}
