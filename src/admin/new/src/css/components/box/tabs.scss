.b-tabs {
	&__nav {
		display: flex;
		align-items: flex-end;
	}
	&__list {
		display: flex;
		margin: 0 -5px;
		padding: 0 5px 0 4px;
		border-bottom: 1px solid $colorBd;
	}
	&__item {
		margin: 0 0 -1px 1px;
	}
	&__link {
		display: block;
		padding: 8px $sm;
		border: 1px solid $colorBd;
		border-radius: 2px;
		color: $colorTextLight;
		text-decoration: none;
	}
	&__tools {
		margin: 0 0 2px;
	}
	&__fragment {
		position: absolute;
		left: -5000px;
		padding: $sm 0 0;
		opacity: 0;
		transition: opacity $t;
	}

	// VARIANTs
	.hoverevents &__link:hover {
		color: $colorText;
	}
	&__link.is-active {
		border-bottom-color: $colorWhite;
		color: $colorText;
	}
	&__fragment.is-active {
		position: relative;
		left: auto;
		opacity: 1;
	}
}
