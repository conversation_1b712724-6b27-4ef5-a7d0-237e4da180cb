.b-header {
	display: flex;
	align-items: center;
	min-height: 70px;
	padding: $sm $sm;
	border-bottom: 1px solid $colorBd;
	box-shadow: $shadow;
	&__img {
		margin-left: $xs;
		img {
			width: auto;
			max-height: 50px;
		}
	}
	&__content {
		flex: 1;
		min-width: 0;
		padding: 0 $sm 0 $xs;
	}
	&__title {
		@include line-clamp(1);
		margin: 0 0 $xxs;
		font-size: 20px;
	}
	&__back {
		width: 24px;
		height: 24px;
		color: $colorTextLight;
	}
	&__menu {
		position: absolute;
		right: 0;
		bottom: 0;
		left: 0;
		border-top: 1px solid $colorBd;
		.m-icons {
			border: none;
			border-radius: 0;
			&__list {
				margin-top: -1px;
				padding: 0;
			}
			&__item {
				margin: 0 -1px 0 1px;
				border-width: 0 1px 0 0;
			}
		}
	}

	// VARIANTs
	&--menu {
		position: relative;
		padding-bottom: 55px;
	}
}
