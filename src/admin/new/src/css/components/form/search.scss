.f-search {
	position: relative;
	display: flex;
	&__inp {
		flex: 1 1 auto;
		border-radius: 0;
		background: transparent;
		color: $colorWhite;
		transition: background-color $t;
	}
	&__btn {
		position: relative;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 50px;
		height: 100%;
		padding: 15px;
		background: rgba($colorWhite, 0.3);
		color: $colorWhite;
		text-decoration: none;
		transition: background-color $t;
		svg {
			transition: opacity $t;
		}
		&::after {
			content: '';
			position: absolute;
			top: 50%;
			left: 50%;
			width: 16px;
			height: 16px;
			margin: -8px 0 0 -8px;
			border: 2px solid $colorWhite;
			border-radius: 8px;
			border-top-color: transparent;
			opacity: 0;
			transition: opacity $t;
		}
	}

	// STATEs
	&__inp:focus {
		background: rgba($colorWhite, 0.3);
	}
	&__inp.is-loading + &__btn {
		svg {
			opacity: 0;
		}
		&::after {
			opacity: 1;
			animation: animation-rotate 0.8s infinite linear;
		}
	}
	.hoverevents &__btn:hover {
		background: rgba($colorWhite, 0.15);
		color: $colorWhite;
	}
}
