import { Application } from 'stimulus';

import Templates from './components/Templates';
import choices from './components/Choices';
import Toggle from './components/Toggle';
import RemoveItem from './components/RemoveItem';
import Tiny from './components/Tiny';
import SuggestInp from './components/SuggestInp';
import File from './components/File';
import List from './components/List';
import ImageList from './components/ImageList';
import ImageLibrary from './components/ImageLibrary';
import CustomFields from './components/customFields/CustomFields';
import CustomField from './components/customFields/CustomField';
import CustomFieldFile from './components/customFields/CustomFieldFile';
import CustomFieldList from './components/customFields/CustomFieldList';
import CustomFieldImage from './components/customFields/CustomFieldImage';
import ModularContent from './components/ModularContent';
import ToggleAll from './components/ToggleAll';
import Tree from './components/Tree';
import MenuTree from './components/MenuTree';
import contenttoggle from './components/contenttoggle';

const components = {
	Templates,
	choices,
	Toggle,
	RemoveItem,
	Tiny,
	SuggestInp,
	File,
	List,
	ImageList,
	ImageLibrary,
	CustomFields,
	CustomField,
	CustomFieldFile,
	CustomFieldList,
	CustomFieldImage,
	ModularContent,
	ToggleAll,
	Tree,
	MenuTree,
	contenttoggle,
};

const AppCf = {
	run() {
		const application = Application.start();

		Object.keys(components).forEach((component) => {
			application.register(component, components[component]);
		});
	},
};

window.AppCf = AppCf;
