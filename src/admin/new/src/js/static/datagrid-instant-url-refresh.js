var dataGridRegisterAjaxCall;

if (typeof naja !== 'undefined') {
	dataGridRegisterAjaxCall = function(params) {
		var method = params.type || 'GET';
		var data = params.data || null;

		// eslint-disable-next-line
		naja.makeRequest(method, params.url, data, {
			history: 'replace',
		})
			.then(params.success)
			.catch(params.error);
	};
} else {
	dataGridRegisterAjaxCall = function(params) {
		// eslint-disable-next-line
		$.nette.ajax(params);
	};
}

// document.addEventListener('DOMContentLoaded', function() {
var element = document.querySelector('.datagrid');

if (element !== null) {
	dataGridRegisterAjaxCall({
		type: 'GET',
		url: element.getAttribute('data-refresh-state'),
	});
}
// });
