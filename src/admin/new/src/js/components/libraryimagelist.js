import { Controller } from 'stimulus';
import Sortable from 'sortablejs';

export default class libraryimagelist extends Controller {

	static values = {
		url: String
	}

	connect() {
		new Sortable(this.element, {
			handle: '[data-drag-handle]',
			animation: 150,
			onUpdate: (event) => {
				this.updateListSort(event.item, event.to, event.newIndex);
			}
		});
	}

	updateListSort(item, list, index) {
		const prevSibling = list.children[index - 1];
		const nextSibling = list.children[index + 1];
		const newPosition = nextSibling ? parseInt(nextSibling.dataset.sort) - 1 : prevSibling?.dataset.sort ? parseInt(prevSibling.dataset.sort) + 1 : null;
		const id = item.dataset.id;

		if (id && newPosition) {
			const url = new URL(this.urlValue);

			const oldparams = new URLSearchParams(url.search);
			oldparams.append('imageId', id);
			oldparams.append('do', 'moveImage');
			oldparams.append('position', newPosition);

			url.search = new URLSearchParams(oldparams);

			naja.makeRequest('GET', url.toString(), null, { history: false, loader: '[data-block-loader]' });

		} else {
			console.warn('Image move error: no id or position');
		}
	}
}
