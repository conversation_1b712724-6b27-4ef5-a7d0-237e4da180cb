import { ApplicationController } from 'stimulus-use';
import { hasSessionStorage } from '../tools/utils';

export default class LangToggles extends ApplicationController {
	static targets = ['button'];
	static values = {
		defaultLangs: String,
	};

	maxSelectedLangsCount = 2;
	state = [];

	connect() {
		const defaultLangs = this.hasDefaultLangsValue ? this.defaultLangsValue.split(' ') : [];

		if (hasSessionStorage) {
			const savedToggles = sessionStorage.getItem('superadminToggles');
			if (savedToggles) {
				const toggles = JSON.parse(savedToggles);
				if (toggles && toggles.length) {
					if (this.buttonTargets) {
						this.buttonTargets.forEach((button) => {
							const lang = button.getAttribute('data-toggle-name-value');
							const savedToggle = toggles.find((item) => item.name === lang);
							if ((!savedToggle && (defaultLangs.includes(lang) || !this.hasDefaultLangsValue)) || (savedToggle && savedToggle.opened)) {
								if (this.state.length < this.maxSelectedLangsCount) {
									this.state.push(lang);
								} else {
									setTimeout(() => {
										this.dispatch('removeToggleSelection', { lang });
									});
								}
							}
						});
					}
				}
			} else {
				if (this.buttonTargets) {
					this.buttonTargets.forEach((button) => {
						const lang = button.getAttribute('data-toggle-name-value');
						if (defaultLangs.includes(lang) || !this.hasDefaultLangsValue) {
							this.state.push(lang);
						}
					});
				}
			}
		}
	}

	langToggleSelected(event) {
		const lang = event.detail['lang'];
		const selected = event.detail['selected'];

		if (!this.state.includes(lang) && selected) {
			if (this.state.length === this.maxSelectedLangsCount) {
				const removedLang = this.state.shift();

				this.dispatch('removeToggleSelection', {
					lang: removedLang,
				});
			}
			this.state.push(lang);
		} else if (this.state.includes(lang) && !selected) {
			this.state = this.state.filter((item) => item !== lang);
		}
	}
}
