import { ApplicationController } from 'stimulus-use';
import { hasSessionStorage } from '../tools/utils';

export default class Toggle extends ApplicationController {
	static values = {
		target: String,
		targetClass: String,
		class: String,
		name: String,
		langToggle: Boolean,
		defaultLangs: String,
	};

	getSavedToggles() {
		const savedToggles = sessionStorage.getItem('superadminToggles');

		if (!savedToggles) {
			return null;
		} else {
			return JSON.parse(savedToggles);
		}
	}

	getSavedToggle() {
		if (hasSessionStorage && this.hasNameValue) {
			const savedToggles = this.getSavedToggles();

			return savedToggles && savedToggles.length ? savedToggles.find((item) => item.name === this.nameValue) || null : null;
		} else {
			return null;
		}
	}

	saveToggle(opened) {
		if (hasSessionStorage && this.hasNameValue) {
			const savedToggles = this.getSavedToggles();
			let itemFound = false;
			const updatedToggles =
				savedToggles && savedToggles.length
					? savedToggles.map((item) => {
							if (item.name === this.nameValue) {
								item.opened = opened;
								itemFound = true;
							}

							return item;
					  })
					: [
							{
								opened,
								name: this.nameValue,
							},
					  ];

			if (savedToggles && savedToggles.length && !itemFound) {
				updatedToggles.push({
					opened,
					name: this.nameValue,
				});
			}

			sessionStorage.setItem('superadminToggles', JSON.stringify(updatedToggles));

			if (this.hasLangToggleValue && this.langToggleValue) {
				this.dispatch('langToggleSelected', {
					lang: this.nameValue,
					selected: opened,
				});
			}
		}
	}

	connect() {
		const savedToggle = this.getSavedToggle();

		if (savedToggle) {
			this.setOpened(savedToggle.opened);
		} else {
			if (this.hasDefaultLangsValue && this.nameValue) {
				const defaultLangs = this.defaultLangsValue.split(' ');
				if (defaultLangs.includes(this.nameValue)) {
					this.setOpened(true);
				} else {
					this.setOpened(false);
				}
			}
		}
	}

	setOpened(opened) {
		if (this.hasTargetValue) {
			document.querySelector(this.targetValue).classList.toggle(this.targetClassValue, opened);
			if (this.hasClassValue) {
				this.element.classList.toggle(this.classValue, opened);
			}
		} else {
			this.element.classList.toggle(this.targetClassValue, opened);
		}
	}

	changeClass(e) {
		e.preventDefault();
		const target = this.hasTargetValue
			? document.querySelector(this.targetValue)
			: document.querySelector(e.currentTarget.getAttribute('href'));

		if (!target) return;

		target.classList.toggle(this.targetClassValue);
		if (this.hasClassValue) {
			e.currentTarget.classList.toggle(this.classValue);
		}

		this.saveToggle(target.classList.contains(this.targetClassValue));
	}

	addClass(e) {
		// e.preventDefault(); - zakomentováno, protože pak nefunguje scroll v .m-icons__link
		const target = this.hasTargetValue
			? document.querySelector(this.targetValue)
			: document.querySelector(e.currentTarget.getAttribute('href'));

		if (!target) return;

		target.classList.add(this.targetClassValue);
		if (this.hasClassValue) {
			e.currentTarget.classList.add(this.classValue);
		}
	}

	toggle(e) {
		const { parent, opened } = e.detail;

		if (parent && parent.contains(this.element)) {
			this.setOpened(opened);
		}
	}

	removeToggleSelection(event) {
		const lang = event.detail['lang'];

		if (this.hasLangToggleValue && this.langToggleValue) {
			if (this.hasNameValue && this.nameValue === lang) {
				this.element.click();
			}
		}
	}
}
