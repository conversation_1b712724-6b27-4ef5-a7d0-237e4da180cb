import $ from 'jquery';
import { Controller } from 'stimulus';
import { useDispatch } from 'stimulus-use';
import { handleSnippetResponse } from '../tools/utils';

export default class Tree extends Controller {
	connect() {
		useDispatch(this);
	}

	createTreeItem(id, folderName) {
		const newItem = document.createElement('li');
		newItem.classList.add('b-library__tree-item');
		newItem.id = id;
		newItem.innerHTML = `
<span class="b-library__tree-wrapper">
	<a href="/superadmin/library/?id=${id}&thickbox=1" data-id="${id}" class="b-library__tree-link" data-action="Tree#goTo">
		<span class="item-icon">
			<span class="item-icon__icon icon">
				<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M464 128H272l-54.63-54.63c-6-6-14.14-9.37-22.63-9.37H48C21.49 64 0 85.49 0 112v288c0 26.51 21.49 48 48 48h416c26.51 0 48-21.49 48-48V176c0-26.51-21.49-48-48-48zm0 272H48V112h140.12l54.63 54.63c6 6 14.14 9.37 22.63 9.37H464v224z"/></svg>
			</span>
			<span class="item-icon__text">
				${folderName}
			</span>
		</span>
	</a>
	<span class="b-library__actions">
		<span class="item-icon__icon icon" title="Nová složka" data-action="click->Tree#add">
			<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8zm144 276c0 6.6-5.4 12-12 12h-92v92c0 6.6-5.4 12-12 12h-56c-6.6 0-12-5.4-12-12v-92h-92c-6.6 0-12-5.4-12-12v-56c0-6.6 5.4-12 12-12h92v-92c0-6.6 5.4-12 12-12h56c6.6 0 12 5.4 12 12v92h92c6.6 0 12 5.4 12 12v56z"/></svg>
		</span>
		<span class="item-icon__icon icon" title="Smazat složku" data-action="click->Tree#delete">
			<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M432 32H312l-9.4-18.7A24 24 0 0 0 281.1 0H166.8a23.72 23.72 0 0 0-21.4 13.3L136 32H16A16 16 0 0 0 0 48v32a16 16 0 0 0 16 16h416a16 16 0 0 0 16-16V48a16 16 0 0 0-16-16zM53.2 467a48 48 0 0 0 47.9 45h245.8a48 48 0 0 0 47.9-45L416 128H32z"/></svg>
		</span>
	</span>
</span>
		`;

		return newItem;
	}

	add(event) {
		event.preventDefault();

		const link = event.target.closest('a');
		if (link) {
			const folderName = prompt('Zadejte název nové složky', 'Nová složka');
			if (!folderName) return;
			this.dispatch('treeAdjustStart');
			link.href = link.href.replace(encodeURIComponent('##replace##'), encodeURIComponent(folderName))

			naja.uiHandler.clickElement(link, { history: false }).then(() => {
				this.dispatch('treeAdjustEnd');
			});


		}
	}

	delete(event) {
		event.preventDefault();

		const link = event.target.closest('a');
		this.dispatch('treeAdjustStart');

		if (link && confirm('Opravdu smazat složku?')) {
			naja.uiHandler.clickElement(link, { history: false }).then(() => {
				this.dispatch('goToEnd');
			});

		} else {
			this.dispatch('treeAdjustEnd');
		}
	}

	goToByLink(link) {
		if (!link) return;

		const links = this.element.querySelectorAll('a');
		this.dispatch('goToStart');
		links.forEach((item) => item.classList.remove('is-selected'));
		link.classList.add('is-selected');

		const history = (link.dataset?.najaHistory === 'false') ? false : true

		naja.uiHandler.clickElement(link, { history: history})
			.then(() => {
				this.dispatch('goToEnd');
		});
	}

	goTo(event) {
		event.preventDefault();
		const link = event.target.closest('a');
		this.goToByLink(link);
	}
}
