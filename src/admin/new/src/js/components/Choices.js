import { Controller } from 'stimulus';
import { useIntersection } from 'stimulus-use';
import choicesLibrary from 'choices.js';
import naja from 'naja';
import debounce from 'lodash.debounce';

export default class choices extends Controller {
	intersectoinOptions = {
		rootMargin: '0px 0px 300px 0px',
	};
	connect() {
		useIntersection(this, this.intersectoinOptions);
	}
	appear() {
		if (this.element.closest('[data-Templates-target$="Item"], [data-Templates-target$="Overlay"]')) return;

		let config = {
			placeholderValue: 'Nezvoleno',
			noResultsText: 'Nebyly nalezeny odpovídající <PERSON>',
			loadingText: 'Načítání...',
			noChoicesText: 'Žádné možnosti na výběr',
			itemSelectText: 'Enter pro přidání',
			removeItemButton: true,
			allowHTML: true,
			searchResultLimit: 20,
			searchEnabled: (typeof this.element.dataset.searchurl !== "undefined"),
		};

		if (this.element.dataset.asyncurl !== "undefined") {
			config.shouldSort = false;
		}

		if (this.element.dataset.removeitembutton !== "undefined") {
			config.removeItemButton = this.element.dataset.removeitembutton;
		}

		const choices = new choicesLibrary(this.element,  config);
		if (typeof this.element.dataset.searchurl !== "undefined") {
			if (this.element.dataset.asyncurl !== "undefined") {

				choices.clearChoices();
				const debouncedSearch = debounce((event) => {
					const searchTerm = event.detail.value;
					const selectedValues = choices.getValue(true);
					this.getOptions(this.element.dataset.searchurl, searchTerm, selectedValues).then(options => {
						if (Array.isArray(options)) {

							const filteredOptions = options.filter(option => !selectedValues.includes(option.value));
							choices.setChoices(filteredOptions, 'value', 'label', true);
						}
					})
				}, 500)
				choices.passedElement.element.addEventListener('search', debouncedSearch);

			} else {
				// load "static" json from url
				choices.setChoices(async () => {
					try {
						const items = await fetch(this.element.dataset.searchurl);
						const loadedStateInput = document.getElementById(this.element.id + '-loaded');
						if (loadedStateInput !== null) {
							loadedStateInput.value = 1;
						}
						return items.json();
					} catch (err) {
						console.error(err);
					}
				});
			}
		}
	}

	getOptions(url, searchTerm, selectedValues) {
		return naja.makeRequest('GET', url + '?searchTerm=' + searchTerm + '&selectedValues=' + selectedValues.join(','), null, { history: false}).then((payload) => {
			return payload

		}).catch((error) => {
			console.log(error);
			return []
		});
	}

}
