import { ApplicationController } from 'stimulus-use';

export default class File extends ApplicationController {
	static targets = ['nameInput', 'fileId', 'tags', 'progress'];

	static values = {
		id: String,
		url: String,
		tags: Object,
	};

	connect() {
		if (this.hasTagsValue && this.hasTagsTarget) {
			this.tagsTarget.innerHTML = `${this.createTag(this.tagsValue.type)}${this.createTag(this.tagsValue.size)}`;
		}
	}

	createTag(value) {
		return `<span class="tag">${value}</span>`;
	}

	updateProgress(completed) {
		if (this.hasProgressTarget) {
			this.progressTarget.style.width = `${completed}%`;
		}
	}

	handleLoaded(success) {
		if (success) {
			this.element.classList.add('is-success');
			if (this.hasNameInputTarget) {
				this.nameInputTarget.disabled = false;
			}
		} else {
			this.element.classList.add('is-error');
		}
	}

	upload(e) {
		const { file, id } = e.detail;

		if (id !== this.idValue || !this.hasUrlValue || !file) return;

		const data = new FormData();
		const request = new XMLHttpRequest();
		let completed = 0;

		data.append('file', file, file.name);

		// show loader for image
		this.element.classList.add('is-loading');

		request.upload.addEventListener('progress', (event) => {
			completed = Math.ceil((event.loaded / (event.total || 1)) * 100);
			this.updateProgress(completed);
		});
		request.addEventListener('load', () => {
			completed = 100;
			this.updateProgress(completed);
			this.element.classList.remove('is-loading');

			if (request.status === 200) {
				const responseData = JSON.parse(request.response);
				if (responseData.id) {
					this.element.dataset.id = responseData.id;
					if (this.hasFileIdTarget) {
						this.fileIdTarget.value = responseData.id;
					}
					if (this.element.dataset.imagelibraryTarget && this.element.dataset.imagelibraryTarget === 'img') {
						const src = URL.createObjectURL(file);
						this.element.dataset.src = src;
					}
					this.handleLoaded(true);
					this.dispatch('fileUploaded', { id: responseData.id.toString(), oldId: id });
				} else {
					this.handleLoaded(false);
				}
			} else {
				this.handleLoaded(false);
			}
		});

		request.open('POST', this.urlValue);
		request.send(data);

		if (this.hasNameInputTarget) {
			this.nameInputTarget.value = file.name;
		}

		if (this.hasTagsTarget) {
			const fileNameChunks = file.name.split('.');
			const fileSize = Math.ceil((file.size / 1024 / 1024) * 100) / 100;
			const typeTag = this.createTag(fileNameChunks[fileNameChunks.length - 1]);
			const sizeTag = this.createTag(`${fileSize} MB`);
			this.tagsTarget.innerHTML = `${typeTag}${sizeTag}`;
		}
	}
}
