import { Controller } from '@stimulus/core';

export default class contenttoggle extends Controller {
	static targets = ['toggle'];

	connect() {
		this.toggle();
	}

	toggle() {
		if (!this.hasToggleTarget) return;

		this.toggleTargets.forEach((toggle) => {
			const { targetId } = toggle.dataset;

			if (!targetId) return;

			const targetEl = this.element.querySelector(`[data-customfield-key-value="${targetId}"]`);

			if (targetEl) {
				targetEl.classList.toggle('u-hide', !toggle.checked);
			}
		});
	}
}
