import { Controller } from 'stimulus';
import { useDispatch } from 'stimulus-use';

export default class ProductVariant extends Controller {
	static targets = ['name', 'price', 'langs'];
	static values = {
		id: String,
	};

	connect() {
		useDispatch(this);
	}

	edit() {
		this.dispatch('edit', { id: this.idValue });
	}

	remove() {
		this.dispatch('remove', { id: this.idValue });
	}

	updateValues(e) {
		const { values } = e.detail;

		if (values.id !== this.idValue) {
			return;
		}

		const texts = [];
		let langsTpl = '';
		values.lang.forEach((lang) => {
			const price = values.price.find((item) => item.lang === lang).value;
			texts.push(price);
			langsTpl += `<span class="tag js-lang js-lang--${lang.toLowerCase()}">${lang}</span>`;
		});

		if (this.nameTarget && values.name) {
			this.nameTarget.innerHTML = `${values.name}`;
		}

		if (this.priceTarget) {
			this.priceTarget.innerHTML = `${texts.length ? ' - ' : ''}${texts.join(', ')}`;
		}

		if (this.hasLangsTarget) {
			this.langsTarget.innerHTML = langsTpl;
		}
	}
}
