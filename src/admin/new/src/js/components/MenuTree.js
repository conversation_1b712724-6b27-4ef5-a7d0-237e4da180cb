import $ from 'jquery';
import { Controller } from 'stimulus';
import naja from 'naja';

export default class MenuTree extends Controller {
	static values = {
		// click: String,
		create: String,
		move: String,
		moveparametername: String,
		disablehistory: Boolean,
		targetparametername: String,
		actionparametername: String,
		nameparametername: String,
	};

	connect() {
		const plugins = ['html_data', 'ui', 'crrm', 'cookies', 'types', 'contextmenu'];
		// const clickValue = this.clickValue;
		const createValue = this.createValue;
		const moveValue = this.moveValue;
		const moveParameterNameValue = this.moveparameternameValue;
		const disableHistory = this.disablehistoryValue;
		const targetParameterNameValue = this.targetparameternameValue;
		const actionParameterNameValue = this.actionparameternameValue;
		const nameParameterNameValue = this.nameparameternameValue;

		if (!$(this.element).closest('.skbox-window').length) {
			plugins.push('dnd');
		}

		// $('.menu-tree', document).each(function()
		// {
		const id = this.element.id;
		const $tree = $(this.element);

		const contextmenuitems = {
			create: {
				// The item label
				label: 'Vložit',
				// The function to execute upon a click
				action: function(obj) {
					//console.log(obj);

					$tree.jstree('create', '#' + obj.attr('id'), 'last', 'Nová stránka', false, true);

					//return true;
				},
				// All below are optional
				_class: 'class', // class is applied to the item LI node
				separator_before: false, // Insert a separator before the item
				separator_after: false, // Insert a separator after the item
				// false or string - if does not contain `/` - used as classname
				icon: false,
			},
			rename: false,
			remove: false,
			ccp: false,
		};

		// if($('#parametertree').length)
		// {
		// 	contextmenuitems.createcat = {
		// 		// The item label
		// 		'label'				: 'Vložit kategorii',
		// 		// The function to execute upon a click
		// 		'action'			: function (obj) {
		// 			//console.log(obj);
		//
		// 			$tree.jstree('create', '#'+obj.attr('id'), 'last', 'Enter a new category name', false, true);
		//
		// 			//return true;
		// 		},
		// 		// All below are optional
		// 		'_class'			: 'class',	// class is applied to the item LI node
		// 		'separator_before'	: false,	// Insert a separator before the item
		// 		'separator_after'	: false,		// Insert a separator after the item
		// 		// false or string - if does not contain `/` - used as classname
		// 		'icon'				: false
		// 	};
		// }


		const requestOptions = {
			loader: '[data-block-loader]',
		};
		if (disableHistory) {
			requestOptions.history = false
		}

		$tree
			.jstree({
				core: {
					animation: 400,
					initially_open: ['t1'],
				},
				ui: {
					select_limit: 1,
				},
				cookies: {
					save_selected: false,
					save_opened: 'node_opened_' + id,
				},
				/*
					'types': {
					'max_depth': 2
					},
					'crrm': {
					'move': {
					'check_move' : function(data) {

					console.log(data);
					console.log(this.get_path(data.np[0]));
					// TODO  - dodelat omezeni u parametru
					/*
					var p = this._get_parent(data.o);
					//You cannot move a node with no parents
					if(p == -1) {
					return false;
					}
					//You cannot move a child to the root
					else if(!this.get_path(data.np[0])) {
					return false;
					}
					//You cannot move a node deeper than 1 level into the tree
					else if(this.get_path(data.np[0]).length > 1) {
					return false;
					}
					//* /

					//You cannot move a child to the root
					if(!this.get_path(data.np[0]) && this._get_parent(data.o) != -1) {
					return false;
					}
					//You cannot move a node deeper than 1 level into the tree
					else if(this._get_parent(data.o) == -1 && this.get_path(data.np[0]).length >= 1) {
					return false;
					}

					return true;

					}
					}
					},
					*/

				contextmenu: {
					items: contextmenuitems,
				},
				plugins: plugins,
			})
			.bind('loaded.jstree', function(event, data) {
				$('.m-tree').css('visibility', 'visible')
			})
			.bind('select_node.jstree', function(event, data) {
				var link = $('a', data.rslt.obj);



				naja.makeRequest('GET', link.attr('href'), null, requestOptions);
			})
			.bind('move_node.jstree', function(event, data) {
				var movedNodeId = data.rslt.o.attr('id').slice(1);
				naja.makeRequest('GET', moveValue, {
					[moveParameterNameValue]: movedNodeId,
					[targetParameterNameValue]: data.rslt.r.attr('id').slice(1),
					[actionParameterNameValue]: data.rslt.p,
				}, requestOptions).catch(() => {
					$.jstree.rollback(data.rlbk);
				})
			})
			.bind('after_close.jstree', function(event, data) {
				// FIX zavirani pod polozek stromu

				var nodeArray;
				if ($.cookie('node_opened_')) {
					nodeArray = $.cookie('node_opened_').split(',');
				} else if ($.cookie('node_opened_librarytree')) {
					nodeArray = $.cookie('node_opened_librarytree').split(',');
				}
				$(data.rslt.obj)
					.find('.jstree-open').each(function() {
						// najdi vsechny otevrene potomky -> smaz jejich cookies

						var idToClose = '#' + ($(this).attr('id'));
						nodeArray = nodeArray.filter(function(currentValue) {
							if (currentValue == idToClose) {
								return false;
							}
							return true;
						})
					})
				if (nodeArray) {
					$.cookie('node_opened_', nodeArray.join(','));
				}
			})

			.bind('create_node.jstree', function (event, data) {
				var name = $.trim(data.rslt.obj.text());
				var parentId = data.rslt.parent != -1 ? data.rslt.parent.attr('id').slice(1) : '';
				if ($(document).closest('.skbox-window').length) {
					name = prompt('Zadejte jméno složky:', 'Nová složka');
				}

				if (name) {
					naja.makeRequest('GET', createValue, {
						[targetParameterNameValue]: parentId,
						[nameParameterNameValue]: name,
					}, requestOptions).catch(() => {
						$.jstree.rollback(data.rlbk);
					})

				} else {
					$.jstree.rollback(data.rlbk);
				}
			});
		// });
	}
}
