export const listItem = (parent, id, isGroup) => {
	const tplElement = document.createElement('div');
	tplElement.innerHTML = `
<div class="b-custom-field b-std u-mb-sm" data-list-item data-controller="RemoveItem" data-removeitem-target="item" data-customfieldlist-target="item" data-id="${id}">
	<div class="b-custom-field__inner b-std__content">
		${isGroup ? '' : `<button type="button" class="b-custom-field__dragdrop btn-icon btn-icon--grab" data-drag-handle>
			<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path d="M96 32H32C14.33 32 0 46.33 0 64v64c0 17.67 14.33 32 32 32h64c17.67 0 32-14.33 32-32V64c0-17.67-14.33-32-32-32zm0 160H32c-17.67 0-32 14.33-32 32v64c0 17.67 14.33 32 32 32h64c17.67 0 32-14.33 32-32v-64c0-17.67-14.33-32-32-32zm0 160H32c-17.67 0-32 14.33-32 32v64c0 17.67 14.33 32 32 32h64c17.67 0 32-14.33 32-32v-64c0-17.67-14.33-32-32-32zM288 32h-64c-17.67 0-32 14.33-32 32v64c0 17.67 14.33 32 32 32h64c17.67 0 32-14.33 32-32V64c0-17.67-14.33-32-32-32zm0 160h-64c-17.67 0-32 14.33-32 32v64c0 17.67 14.33 32 32 32h64c17.67 0 32-14.33 32-32v-64c0-17.67-14.33-32-32-32zm0 160h-64c-17.67 0-32 14.33-32 32v64c0 17.67 14.33 32 32 32h64c17.67 0 32-14.33 32-32v-64c0-17.67-14.33-32-32-32z"></path></svg>
		</button>`}
		<div class="b-custom-field__content" data-item-content></div>
		${isGroup ? '' : `<button type="button" class="b-custom-field__delete btn-icon btn-icon--remove tooltip" data-action="RemoveItem#remove CustomFieldList#remove">
			<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M432 32H312l-9.4-18.7A24 24 0 0 0 281.1 0H166.8a23.72 23.72 0 0 0-21.4 13.3L136 32H16A16 16 0 0 0 0 48v32a16 16 0 0 0 16 16h416a16 16 0 0 0 16-16V48a16 16 0 0 0-16-16zM53.2 467a48 48 0 0 0 47.9 45h245.8a48 48 0 0 0 47.9-45L416 128H32z"></path></svg>
			<span class="tooltip__content">
				Odstranit
			</span>
		</button>`}
	</div>
</div>
`;

	return new Promise((resolve) => {
		setTimeout(() => {
			const itemContent = tplElement.querySelector('[data-item-content]');
			parent.append(tplElement.querySelector('[data-list-item]'));
			resolve(itemContent);
		}, 0);
	});
}
