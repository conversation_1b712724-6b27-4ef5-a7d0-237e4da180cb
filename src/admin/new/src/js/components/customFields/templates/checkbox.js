export const checkbox = (parent, field, value, scheme) => {
	const { id: key } = field;
	const tplElement = document.createElement('div');
	const keyChunks = key.split('-');
	keyChunks.pop();
	parent.append(tplElement);
	tplElement.outerHTML = `
<label class="inp-item inp-item--checkbox u-mb-sm" data-controller="CustomField" data-customfield-key-value='${key}'>
	<input class="inp-item__inp" type="checkbox" name="custom-${key}" id="custom-${key}"${
		value === true ? ' checked' : ''
	} data-action="change->CustomField#updateCheckboxValue${scheme.toggleTarget ? ' change->contenttoggle#toggle' : ''}"${
		scheme.toggleTarget ? ` data-contenttoggle-target="toggle" data-target-id="${keyChunks.join('-')}-${scheme.toggleTarget}"` : ''
	}>
	${scheme.label ? `<span class="inp-item__text">${scheme.label}</span>` : ''}
</label>
`;
};
