export const list = (parent, key, scheme, order) => {
	const tplElement = document.createElement('div');
	tplElement.innerHTML = `
<div id="list-${key}" class="b-toggle b-toggle--default b-list u-mb-md is-open${
		scheme.draggable ? ' b-toggle--draggable' : ''
	}" data-list data-controller="Toggle CustomFieldList RemoveItem${
		scheme.hasContentToggle ? ' contenttoggle' : ''
	}" data-removeitem-target="item" data-toggle-target-value="#list-${key}" data-toggle-target-class-value="is-open" data-toggle-name-value="list-${key}" data-customfieldlist-scheme-value='${JSON.stringify(
		scheme,
	)}' data-customfieldlist-id-value='${key}' data-customfield-key-value='${key}' data-customfieldlist-order-value='${order}' data-action="ToggleAll:toggle@window->Toggle#toggle${
		scheme.hasContentToggle ? ' CustomField:connected->contenttoggle#toggle' : ''
	}"${scheme.draggable ? ` data-id="${key}"` : ''}>
	<div class="b-toggle__header">
		<button class="b-toggle__tool btn-icon" data-action="Toggle#changeClass" type="button">
			<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path d="M285.476 272.971L91.132 467.314c-9.373 9.373-24.569 9.373-33.941 0l-22.667-22.667c-9.357-9.357-9.375-24.522-.04-33.901L188.505 256 34.484 101.255c-9.335-9.379-9.317-24.544.04-33.901l22.667-22.667c9.373-9.373 24.569-9.373 33.941 0L285.475 239.03c9.373 9.372 9.373 24.568.001 33.941z"></path></svg>
		</button>
		${
			scheme.label
				? `<div class="grid-inline">
			<strong class="flex-grow">${scheme.label}</strong>
		</div>`
				: ''
		}
	</div>
	<div id="list-content-${key}" class="b-toggle__content b-toggle__content--static">
		<div data-list-content data-customfieldlist-target="content"></div>
		${
			scheme.type === 'group'
				? ''
				: `<button type="button" class="b-list__add btn u-mb-sm" data-action="CustomFieldList#add">
			<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M416 208H272V64c0-17.67-14.33-32-32-32h-32c-17.67 0-32 14.33-32 32v144H32c-17.67 0-32 14.33-32 32v32c0 17.67 14.33 32 32 32h144v144c0 17.67 14.33 32 32 32h32c17.67 0 32-14.33 32-32V304h144c17.67 0 32-14.33 32-32v-32c0-17.67-14.33-32-32-32z"></path></svg>
		</button>`
		}
	</div>
	${
		scheme.draggable
			? `<button type="button" class="b-toggle__dragdrop btn-icon btn-icon--grab" data-drag-group-handle>
		<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path d="M96 32H32C14.33 32 0 46.33 0 64v64c0 17.67 14.33 32 32 32h64c17.67 0 32-14.33 32-32V64c0-17.67-14.33-32-32-32zm0 160H32c-17.67 0-32 14.33-32 32v64c0 17.67 14.33 32 32 32h64c17.67 0 32-14.33 32-32v-64c0-17.67-14.33-32-32-32zm0 160H32c-17.67 0-32 14.33-32 32v64c0 17.67 14.33 32 32 32h64c17.67 0 32-14.33 32-32v-64c0-17.67-14.33-32-32-32zM288 32h-64c-17.67 0-32 14.33-32 32v64c0 17.67 14.33 32 32 32h64c17.67 0 32-14.33 32-32V64c0-17.67-14.33-32-32-32zm0 160h-64c-17.67 0-32 14.33-32 32v64c0 17.67 14.33 32 32 32h64c17.67 0 32-14.33 32-32v-64c0-17.67-14.33-32-32-32zm0 160h-64c-17.67 0-32 14.33-32 32v64c0 17.67 14.33 32 32 32h64c17.67 0 32-14.33 32-32v-64c0-17.67-14.33-32-32-32z"></path></svg>
	</button>
	<button type="button" class="b-list__move-up btn-icon btn-icon--arrow-up tooltip" data-action="CustomFields#moveGroupUp">
		<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M34.9 289.5l-22.2-22.2c-9.4-9.4-9.4-24.6 0-33.9L207 39c9.4-9.4 24.6-9.4 33.9 0l194.3 194.3c9.4 9.4 9.4 24.6 0 33.9L413 289.4c-9.5 9.5-25 9.3-34.3-.4L264 168.6V456c0 13.3-10.7 24-24 24h-32c-13.3 0-24-10.7-24-24V168.6L69.2 289.1c-9.3 9.8-24.8 10-34.3.4z"/></svg>
		<span class="tooltip__content">Posunout nahoru</span>
	</button>
	<button type="button" class="b-list__move-down btn-icon btn-icon--arrow-down tooltip" data-action="CustomFields#moveGroupDown">
		<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M413.1 222.5l22.2 22.2c9.4 9.4 9.4 24.6 0 33.9L241 473c-9.4 9.4-24.6 9.4-33.9 0L12.7 278.6c-9.4-9.4-9.4-24.6 0-33.9l22.2-22.2c9.5-9.5 25-9.3 34.3.4L184 343.4V56c0-13.3 10.7-24 24-24h32c13.3 0 24 10.7 24 24v287.4l114.8-120.5c9.3-9.8 24.8-10 34.3-.4z"/></svg>
		<span class="tooltip__content">Posunout dolů</span>
	</button>`
			: ''
	}
	${
		scheme.deletable
			? `<button type="button" class="b-list__delete btn-icon btn-icon--remove tooltip" data-action="RemoveItem#remove CustomFields#removeFromScheme">
		<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M432 32H312l-9.4-18.7A24 24 0 0 0 281.1 0H166.8a23.72 23.72 0 0 0-21.4 13.3L136 32H16A16 16 0 0 0 0 48v32a16 16 0 0 0 16 16h416a16 16 0 0 0 16-16V48a16 16 0 0 0-16-16zM53.2 467a48 48 0 0 0 47.9 45h245.8a48 48 0 0 0 47.9-45L416 128H32z"></path></svg>
		<span class="tooltip__content">
			Odstranit
		</span>
	</button>`
			: ''
	}
</div>
`;

	return new Promise((resolve) => {
		setTimeout(() => {
			const listContent = tplElement.querySelector('[data-list-content]');
			parent.append(tplElement.querySelector('[data-list]'));
			resolve(listContent);
		}, 0);
	});
};
