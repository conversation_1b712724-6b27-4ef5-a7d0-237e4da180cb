export const image = (parent, field, value, scheme) => {
	const { id: key } = field;
	const tplElement = document.createElement('div');
	parent.append(tplElement);
	tplElement.outerHTML = `
<div class="b-std u-mb-sm">
	${scheme.label ? `<h3 class="b-std__title title">${scheme.label}</h3>` : ''}
	<div class="b-std__content">
		<div class="b-imgs u-mb-sm" data-controller="CustomFieldImage" data-customfieldimage-key-value='${key}' data-customfieldimage-images-value='${JSON.stringify(value.map((img) => ({ id: img.id.toString(), src: img.src })))}' data-customfieldimage-multiple-value='${JSON.stringify(scheme.multiple || false)}'>
			<div class="b-imgs__list grid grid--x-xs grid--x-xs" data-customfieldimage-target="content">
				${value.map((img) => {
					const { id, src } = img;

					return imageItem(id, src);
				}).join('')}
			</div>
			<div class="u-mt-xs${!scheme.multiple && value.length > 0 ? ' u-hide' : ''}" data-customfieldimage-target="add">
				<button class="b-imgs__add btn" type="button" data-action="CustomFieldImage#openLibrary">
					<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M416 208H272V64c0-17.67-14.33-32-32-32h-32c-17.67 0-32 14.33-32 32v144H32c-17.67 0-32 14.33-32 32v32c0 17.67 14.33 32 32 32h144v144c0 17.67 14.33 32 32 32h32c17.67 0 32-14.33 32-32V304h144c17.67 0 32-14.33 32-32v-32c0-17.67-14.33-32-32-32z"></path></svg>
				</button>
			</div>
		</div>
	</div>
</div>
`;
}

export const imageItem = (id, src) => `
<div class="b-imgs__item grid__cell size--3-12" data-controller="RemoveItem" data-removeitem-target="item" data-removeitem-animation-value="fade" data-customfieldimage-target="img" data-id="${id}">
	<div class="b-imgs__inner">
		<span class="b-imgs__img">
			<img src="${src}" alt="">
		</span>
		<button class="b-imgs__dragdrop btn-icon btn-icon--grab tooltip" type="button" data-drag-handle>
			<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 512"><path d="M96 32H32C14.33 32 0 46.33 0 64v64c0 17.67 14.33 32 32 32h64c17.67 0 32-14.33 32-32V64c0-17.67-14.33-32-32-32zm0 160H32c-17.67 0-32 14.33-32 32v64c0 17.67 14.33 32 32 32h64c17.67 0 32-14.33 32-32v-64c0-17.67-14.33-32-32-32zm0 160H32c-17.67 0-32 14.33-32 32v64c0 17.67 14.33 32 32 32h64c17.67 0 32-14.33 32-32v-64c0-17.67-14.33-32-32-32zM288 32h-64c-17.67 0-32 14.33-32 32v64c0 17.67 14.33 32 32 32h64c17.67 0 32-14.33 32-32V64c0-17.67-14.33-32-32-32zm0 160h-64c-17.67 0-32 14.33-32 32v64c0 17.67 14.33 32 32 32h64c17.67 0 32-14.33 32-32v-64c0-17.67-14.33-32-32-32zm0 160h-64c-17.67 0-32 14.33-32 32v64c0 17.67 14.33 32 32 32h64c17.67 0 32-14.33 32-32v-64c0-17.67-14.33-32-32-32z"></path></svg>
			<span class="tooltip__content">Přesunout</span>
		</button>
		<div class="b-imgs__btns">
			<button class="b-imgs__btn btn-icon btn-icon--remove tooltip" type="button" data-action="RemoveItem#remove CustomFieldImage#removeImg">
				<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M432 32H312l-9.4-18.7A24 24 0 0 0 281.1 0H166.8a23.72 23.72 0 0 0-21.4 13.3L136 32H16A16 16 0 0 0 0 48v32a16 16 0 0 0 16 16h416a16 16 0 0 0 16-16V48a16 16 0 0 0-16-16zM53.2 467a48 48 0 0 0 47.9 45h245.8a48 48 0 0 0 47.9-45L416 128H32z"></path></svg>
				<span class="tooltip__content">Odstranit</span>
			</button>
		</div>
	</div>
</div>
`;
