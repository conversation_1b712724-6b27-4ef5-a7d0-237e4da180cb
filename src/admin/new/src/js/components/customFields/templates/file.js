export const file = (parent, field, value, scheme, uploadUrl) => {
	const { id: key } = field;
	const tplElement = document.createElement('div');
	parent.append(tplElement);
	tplElement.outerHTML = `
<div class="b-std u-mb-sm">
	${scheme.label ? `<h3 class="b-std__title title">${scheme.label}</h3>` : ''}
	<div class="b-std__content">
		<div class="b-list u-mb-sm" data-controller="CustomFieldFile" data-action="File:fileUploaded->CustomFieldFile#fileUploaded" data-customfieldfile-key-value='${key}' data-customfieldfile-multiple-value='${JSON.stringify(scheme.multiple || false)}' data-customfieldfile-ids-value='${JSON.stringify(value.map((file) => file.id.toString()))}' data-customfieldfile-uploadurl-value="${uploadUrl}">
			<div class="b-list__list" data-customfieldfile-target="content">
				${value.map((file) => {
					const { id } = file;

					return fileItem(id, file, uploadUrl);
				}).join('')}
			</div>
			<div class="${!scheme.multiple && value.length > 0 ? ' u-hide' : ''}" data-customfieldfile-target="add">
				<button type="button" class="b-list__add btn b-list__add--file">
					<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M416 208H272V64c0-17.67-14.33-32-32-32h-32c-17.67 0-32 14.33-32 32v144H32c-17.67 0-32 14.33-32 32v32c0 17.67 14.33 32 32 32h144v144c0 17.67 14.33 32 32 32h32c17.67 0 32-14.33 32-32V304h144c17.67 0 32-14.33 32-32v-32c0-17.67-14.33-32-32-32z"></path></svg>
					<input type="file" data-action="change->CustomFieldFile#addFile"${scheme.multiple ? ' multiple' : ''}>
				</button>
			</div>
		</div>
	</div>
</div>
`;
}

export const fileItem = (id, file, uploadUrl, isNew) => `
<div class="b-list__item" data-controller="RemoveItem${isNew ? ' File' : ''}" data-removeitem-target="item" data-customfieldfile-target="file" data-id="${id}" ${isNew ? `data-action="CustomFieldFile:uploadFile@window->File#upload" data-file-id-value="${id}" data-file-url-value="${uploadUrl}"` : ''}>
	<div class="b-list__progress" data-file-target="progress"></div>
	<span class="b-list__inp ">
		<input class="inp-text" placeholder="Zadejte název souboru" type="text" name="file-${id}" value="${file.name}" data-file-target="nameInput" data-action="blur->CustomFieldFile#updateFilesValue">
	</span>
	<span class="b-list__inp u-hide">
		<input class="inp-text" type="hidden" name="file-id-${id}" value="${id}" data-file-target="fileId">
	</span>
	<span class="b-list__tags" data-file-target="tags">
		${file.type ? `<span class="tag">${file.type}</span>` : ''}
		${file.size ? `<span class="tag">${file.size}</span>` : ''}
	</span>
	<button type="button" class="b-list__btn btn-icon  btn-icon--remove tooltip" data-action="RemoveItem#remove CustomFieldFile#removeFile">
		<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M432 32H312l-9.4-18.7A24 24 0 0 0 281.1 0H166.8a23.72 23.72 0 0 0-21.4 13.3L136 32H16A16 16 0 0 0 0 48v32a16 16 0 0 0 16 16h416a16 16 0 0 0 16-16V48a16 16 0 0 0-16-16zM53.2 467a48 48 0 0 0 47.9 45h245.8a48 48 0 0 0 47.9-45L416 128H32z"></path></svg>
		<span class="tooltip__content">
			Odstranit
		</span>
	</button>
</div>
`;
