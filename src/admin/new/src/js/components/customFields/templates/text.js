export const text = (parent, field, value, scheme, mutationId) => {
	const tplElement = document.createElement('div');
	const { id: key } = field;
	const placeholder = scheme.placeholder ? ` placeholder="${scheme.placeholder}"` : '';
	const inputType = scheme.inputType ? ` type="${scheme.inputType}"` : ' type="text"';

	if (scheme.type === 'suggest') {
		scheme.url = {
			...scheme.url,
			params: {
				...scheme.url.params,
				...(mutationId ? { mutationId } : {}),
			},
		};
	}

	parent.append(tplElement);
	tplElement.outerHTML = `
<div class="inp u-mb-sm" data-controller="CustomField" data-customfield-key-value='${key}'${scheme.type === 'suggest' ? ' data-action="SuggestInp:updateSuggestValue->CustomField#updateSuggestValue"' : ''}>
	${scheme.label ? `<label class="inp-label title" for="custom-${key}">${scheme.label}</label>` : ''}
	<div class="inp-fix"${
		scheme.type === 'suggest'
			? ` data-controller="SuggestInp" data-suggestinp-target="wrapper" data-suggestinp-url-value='${JSON.stringify(scheme.url)}'`
			: ''
	}>
		${
			scheme.type === 'text' || scheme.type === 'suggest'
				? `<input${placeholder}${inputType} class="inp-text" name="custom-${key}" id="custom-${key}" value="${
						scheme.type === 'suggest' ? value.value || '' : value
				  }" data-action="blur->CustomField#${scheme.type === 'suggest' ? 'updateSuggestValue' : 'updateValue'}"${
						scheme.type === 'suggest' ? ' data-suggestinp-target="input"' : ''
				  }>`
				: `<textarea${placeholder} class="inp-text" cols="40" rows="${
						scheme.type === 'tinymce' ? 20 : 4
				  }" name="custom-${key}" id="custom-${key}" data-action="blur->CustomField#${
						scheme.type === 'tinymce' ? 'updateEditorValue' : 'updateValue'
				  }"${scheme.type === 'tinymce' ? ' data-controller="Tiny" data-tiny-target="item"' : ''}>${value}</textarea>`
		}
		${scheme.type === 'suggest' ? `<input type="hidden" data-suggestinp-target="idInput" value="${value.id || ''}">` : ''}
		${scheme.type === 'suggest' ? `<button class="btn-icon tooltip" type="button" data-action="click->SuggestInp#clear"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 352 512"><path d="M242.72 256l100.07-100.07c12.28-12.28 12.28-32.19 0-44.48l-22.24-22.24c-12.28-12.28-32.19-12.28-44.48 0L176 189.28 75.93 89.21c-12.28-12.28-32.19-12.28-44.48 0L9.21 111.45c-12.28 12.28-12.28 32.19 0 44.48L109.28 256 9.21 356.07c-12.28 12.28-12.28 32.19 0 44.48l22.24 22.24c12.28 12.28 32.2 12.28 44.48 0L176 322.72l100.07 100.07c12.28 12.28 32.2 12.28 44.48 0l22.24-22.24c12.28-12.28 12.28-32.19 0-44.48L242.72 256z"/></svg><span class="tooltip__content">Vymazat</span></button>` : ''}
		<div class="inp-text__holder"></div>
	</div>
</div>
`;
};
