import Sortable from "sortablejs";
import { ApplicationController } from "stimulus-use";
import { imageItem } from "./templates/image";

export default class CustomFieldImage extends ApplicationController {
	static targets = ['content', 'img', 'add'];
	static values = {
		key: String,
		multiple: Boolean,
		images: Array,
	};

	connect() {
		if (this.hasContentTarget) {
			new Sortable(this.contentTarget, {
				handle: '[data-drag-handle]',
				animation: 150,
				onUpdate: () => {
					this.updateImagesOrder();
				}
			});
		}
	}

	openLibrary() {
		this.dispatch('openLibrary', { variant: 'product', ids: this.multipleValue ? this.imagesValue.map((image) => parseInt(image.id)) : [], multiple: this.multipleValue, shouldOpen: true, addCallback: this.add.bind(this), removeCallback: this.remove.bind(this) });
	}

	add(id, src) {
		if (this.hasContentTarget) {
			const imgEl = document.createElement('div');
			this.contentTarget.append(imgEl);
			imgEl.outerHTML = imageItem(id, src);
		}

		this.imagesValue = [...this.imagesValue, { id: id.toString(), src }];
		this.updateImagesValue();

		if (!this.multipleValue && this.hasAddTarget && this.imagesValue.length > 0) {
			this.addTarget.classList.add('u-hide');
		}
	}

	remove(imgId) {
		this.imagesValue = this.imagesValue.filter((image) => image.id !== imgId);
		this.updateImagesValue();

		if (!this.multipleValue && this.hasAddTarget && this.imagesValue.length === 0) {
			this.addTarget.classList.remove('u-hide');
		}

		const imgToDelete = this.element.querySelector(`[data-id="${imgId}"]`);

		if (imgToDelete) {
			imgToDelete.remove();
		}
	}

	removeImg(e) {
		const item = e.target.closest('[data-id]');

		if (!item) return;

		this.remove(item.dataset.id);
	}

	updateImagesValue() {
		this.dispatch('updateValue', { value: this.imagesValue, key: this.keyValue });
	}

	updateImagesOrder() {
		if (!this.hasImgTarget) return;

		this.imagesValue = this.imgTargets.map((img) => this.imagesValue.find((image) => image.id === img.dataset.id));
		this.updateImagesValue();
	}
}
