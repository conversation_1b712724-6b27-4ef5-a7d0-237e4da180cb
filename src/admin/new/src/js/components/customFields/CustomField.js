import { ApplicationController } from 'stimulus-use';

export default class CustomField extends ApplicationController {
	static values = {
		key: String,
	};

	connect() {
		this.dispatch('connected');
	}

	updateValue(e) {
		const { value } = e.target;
		this.dispatch('updateValue', { value, key: this.keyValue });
	}

	updateCheckboxValue(e) {
		const { checked } = e.target;
		this.dispatch('updateValue', { value: checked, key: this.keyValue });
	}

	updateMultiselectValue(e) {
		this.dispatch('updateValue', {
			value: Array.from(e.target.selectedOptions)
				.map((item) => item.value)
				.join(','),
			key: this.keyValue,
		});
	}

	updateEditorValue(e) {
		// eslint-disable-next-line
		this.dispatch('updateValue', { value: tinymce.get(e.target.id).getContent(), key: this.keyValue });
	}

	updateSuggestValue(e) {
		const suggest = e.target.closest('[data-controller="SuggestInp"]');

		if (!suggest) return;

		const suggestInp = suggest.querySelector('[data-suggestinp-target="input"]');
		const idInp = suggest.querySelector('[data-suggestinp-target="idInput"]');

		if (suggestInp && idInp) {
			this.dispatch('updateValue', { value: suggestInp.value.trim() === '' ? '' : idInp.value, key: this.keyValue });
		}
	}
}
