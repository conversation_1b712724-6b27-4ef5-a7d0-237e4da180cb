import Sortable from "sortablejs";
import { ApplicationController } from "stimulus-use";

export default class CustomFieldList extends ApplicationController {
	static targets = ['content', 'item'];
	static values = {
		id: String,
		scheme: Object,
		order: Number,
	};

	connect() {
		if (this.hasContentTarget) {
			new Sortable(this.contentTarget, {
				handle: '[data-drag-handle]',
				animation: 150,
				onUpdate: () => {
					this.updateListSort();
				}
			});
		}
	}

	add() {
		this.dispatch('addListItem', { id: this.idValue, scheme: this.schemeValue, order: this.orderValue, parent: this.contentTarget });
		this.orderValue = this.orderValue + 1;
	}

	remove(e) {
		const btn = e.target.closest('[data-id]');

		if (btn) {
			this.dispatch('removeListItem', { id: btn.dataset.id })
		}
	}

	updateListSort() {
		if (this.hasItemTarget) {
			this.dispatch('updateListOrder', { values: this.itemTargets.map((item, i) => ({ id: item.dataset.id, order: i })) });
		}
	}
}
