import { Controller } from 'stimulus';
import { useDispatch } from 'stimulus-use';
import Sortable from 'sortablejs';

export default class ImageList extends Controller {
	static targets = ['list'];
	static values = {
		ids: Array,
	};

	connect() {
		useDispatch(this);
		if (this.hasListTarget) {
			new Sortable(this.listTarget, {
				handle: '[data-drag-handle]',
				animation: 150,
			});
		}
	}

	updateLibrary() {
		this.dispatch('updateSelectedImages', { ids: this.idsValue });
	}

	add(id, src, alt) {
		this.idsValue = [...this.idsValue, id];
		this.dispatch('newItem', { name: 'image', id: `image_${id}`, listTarget: this.listTarget, src, alt });
	}

	remove(id) {
		this.idsValue = this.idsValue.filter((item) => id !== item);
		this.dispatch('remove', { id: `image_${id}` });
		this.updateLibrary();

		const imgToDelete = this.element.querySelector(`[data-id="${id}"]`);

		if (imgToDelete) {
			imgToDelete.remove();
		}
	}

	removeImg(e) {
		const imgTarget = e.target.closest('[data-removeitem-target="item"]');

		if (imgTarget) {
			this.remove(parseInt(imgTarget.dataset.id));
		}
	}

	newImg() {
		this.dispatch('openLibrary', {
			variant: 'product',
			ids: this.idsValue,
			addCallback: this.add.bind(this),
			removeCallback: this.remove.bind(this),
		});
	}
}
