import $ from 'jquery';
import { Controller } from 'stimulus';

export default class Alias extends Controller {
	static targets = ['inp'];
	static values = {
		url: String,
		name: String,
		lang: String,
		id: String,
	};

	generate(event) {
		if (event.detail && event.detail.lang && event.detail.lang !== this.langValue) return;

		if (event.detail && event.detail.title) {
			this.nameValue = event.detail.title;
		}

		// Do not regenerate alias after title change and if alias already exists
		if (event.detail && event.detail.title && this.inpTarget.value !== '') return;

		const data = {
			name: this.nameValue,
			lang: this.langValue,
			id: this.idValue,
		};

		$.ajax({
			url: this.urlValue,
			type: 'GET',
			data: data,
		}).done((response) => {
			$(this.inpTarget).val(response);
		});
	}
}
