import { Controller } from 'stimulus';
import { useDispatch } from 'stimulus-use';
import { nanoid } from 'nanoid';
import Sortable from 'sortablejs';

export default class List extends Controller {
	static targets = ['list'];
	static values = {
		name: String,
		newitem: String,
		mutationid: String,
	};

	updateId() {
		// eslint-disable-next-line
		this.newitemValue = `${this.nameValue}_${nanoid().replace(/\-/gm, '_')}`;
	}

	connect() {
		useDispatch(this);
		this.updateId();
		if (this.hasListTarget) {
			new Sortable(this.listTarget, {
				handle: '[data-drag-handle]',
				animation: 150,
			});
		}
	}

	add() {
		this.dispatch('newItem', {
			name: this.nameValue,
			id: this.newitemValue,
			mutationId: this.mutationidValue,
			listTarget: this.listTarget,
		});
		this.updateId();
	}

	addFile(e) {
		if (e.target.files) {
			Array.from(e.target.files).forEach((file) => {
				this.dispatch('newItem', {
					name: this.nameValue,
					id: this.newitemValue,
					mutationId: this.mutationidValue,
					listTarget: this.listTarget,
					file,
				});
				this.updateId();
			});
			// clear the file list
			e.target.type = 'text';
			e.target.type = 'file';
		}
	}
}
