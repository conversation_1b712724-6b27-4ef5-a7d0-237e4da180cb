import $ from 'jquery';
import { Controller } from 'stimulus';

export default class ToggleCheckbox extends Controller {
	static values = {
		target: String,
		targetClass: String,
		class: String,
	};

	changeClass(e) {
		const $this = $(e.currentTarget);
		const $target = this.hasTargetValue ? $(this.targetValue) : $($this.attr('href'));

		$target.toggleClass(this.targetClassValue);

		if (this.hasClassValue) {
			$this.toggleClass(this.classValue);
		}
	}

	// addClass(e) {
	// 	const $this = $(e.currentTarget);
	// 	const $target = this.hasTargetValue ? $(this.targetValue) : $($this.attr('href'));

	// 	$target.addClass(this.targetClassValue);
	// 	if (this.hasClassValue) {
	// 		$this.addClass(this.classValue);
	// 	}
	// }
}
