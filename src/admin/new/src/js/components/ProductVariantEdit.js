import { Controller } from 'stimulus';
import { useDispatch } from 'stimulus-use';

export default class ProductVariantEdit extends Controller {
	static targets = ['price', 'lang', 'name'];
	static values = {
		id: String,
	};

	variantState = {
		id: null,
		name: undefined,
		price: [],
		lang: [],
	};

	connect() {
		useDispatch(this);
		this.variantState.id = this.idValue;
	}

	edit() {
		if (this.hasNameTarget) {
			this.variantState.name = this.nameTarget.value;
		}
		if (this.hasPriceTarget) {
			this.variantState.price = this.priceTargets.map((item) => ({
				lang: item.dataset.lang,
				value: `${item.dataset.lang} (${item.value} ${item.dataset.currency})`,
			}));
		}
		if (this.hasLangTarget) {
			this.variantState.lang = this.langTargets.filter((item) => item.checked).map((item) => item.dataset.lang);
		}

		this.dispatch('updateValues', { values: this.variantState });
	}
}
