import { Controller } from 'stimulus';

export default class pagemenu extends Controller {
	static targets = ['item', 'menu'];

	connect() {
		this.generateMenu();
	}

	generateMenu() {
		if (!this.hasItemTarget || !this.hasMenuTarget) return;

		const menu = `
<div class="m-icons scroll scroll--horizontal">
	<ul class="m-icons__list">
		${this.itemTargets.map(this.generateMenuItem.bind(this)).join('')}
	</ul>
</div>`;

		this.menuTarget.innerHTML = menu;
	}

	generateMenuItem(itemElement) {
		const { title, iconUrl } = itemElement.dataset;

		const parts = iconUrl.split('/');
		const icon = `/admin/new/dist/img/icons/${parts[parts.length - 1]}`;

		return `
<li class="m-icons__item">
	<a
		href="#${itemElement.id}"
		class="m-icons__link item-icon"
		data-controller="Toggle"
		data-action="Toggle#addClass"
		data-toggle-target-class-value="is-open"
	>
		${iconUrl ? `<span class="item-icon__icon icon" data-controller="icon" data-icon-url-value="${icon}"></span>` : ''}
		<span class="item-icon__text">
			${title}
		</span>
	</a>
</li>`;
	}
}
