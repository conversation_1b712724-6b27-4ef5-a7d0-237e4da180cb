import { Controller } from 'stimulus';
import { useDispatch } from 'stimulus-use';

export default class ProductTitle extends Controller {
	static values = {
		lang: String,
	}

	currentValue = this.element.value;

	connect() {
		useDispatch(this);
	}

	changeTitle(e) {
		const value = e.currentTarget.value;

		this.dispatch('changeTitle', { value, lang: this.langValue, oldValue: this.currentValue });
		this.currentValue = value;
	}

	generateAlias(e) {
		const value = e.target.value;
		this.dispatch('generateAlias', { lang: this.langValue, title: value });
	}
}
