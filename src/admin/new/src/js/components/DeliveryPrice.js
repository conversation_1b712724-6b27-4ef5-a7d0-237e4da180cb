import { Controller } from 'stimulus';
import { useDispatch } from 'stimulus-use';
import {val} from "cheerio/lib/api/attributes";

export default class DeliveryPrice extends Controller {
	static targets = ['name', 'price', 'priceLevel', 'freeFrom'];
	static values = {
		id: String,
	};

	connect() {
		useDispatch(this);
	}

	edit() {
		this.dispatch('edit', { id: this.idValue });
	}

	remove() {
		console.log(this.idValue);
		this.dispatch('remove', { id: this.idValue });
	}

	updateValues(e) {

		const { values } = e.detail;

		if (values.id !== this.idValue) {
			return;
		}

		if (this.nameTarget && values.name) {
			this.nameTarget.innerHTML = `${values.name}`;
		}

		if (this.priceTarget) {
			this.priceTarget.innerHTML = `${values.price}`;
		}

		if (this.priceLevelTarget) {
			this.priceLevelTarget.innerHTML = `<span class="tag">${values.tag}</span>`;
		}

		if (this.freeFromTarget && values.freeFrom !== null) {
			this.freeFromTarget.innerHTML = `, zdarma od ${values.freeFrom}`;
		}
	}
}
