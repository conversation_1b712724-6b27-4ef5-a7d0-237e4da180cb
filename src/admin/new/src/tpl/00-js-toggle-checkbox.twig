{% extends '@layout/layout.twig' %}
{% set pageTitle = 'Toggle Checkbox' %}

{% block content %}

	<div class="main__content">
		<h1>
			{{ pageTitle }}
		</h1>

		{% include '@components/core/checkbox.twig' with {
			props: {
				label: 'Upravit společné nastavení variant',
				data: {
					'controller': 'ToggleCheckbox',
					'action': 'ToggleCheckbox#changeClass',
					'togglecheckbox-target-value': '#checkbox-content',
					'togglecheckbox-target-class-value': 'is-open'
				},
			}
		} %}

		<div id="checkbox-content" class="js-toggle-checkbox__content">
			S<PERSON><PERSON><PERSON><PERSON> obsah
		</div>
	</div>

{% endblock %}
