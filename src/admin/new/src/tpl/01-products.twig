{% extends '@layout/layout-edit.twig' %}
{% set pageTitle = 'Products' %}

{% block content %}

	<div class="main__header">
		{% include '@components/box/header.twig' with {
			props: {
				img: '../img/illust/sample.jpg',
				title: '<PERSON><PERSON><PERSON><PERSON> produktu, k<PERSON><PERSON> mů<PERSON>e být hodně dlouhý Lorem ipsum dolor sit amet consectetur adipisicing elit. Iusto quia commodi eligendi, omnis porro quibusdam exercitationem, quis numquam sequi consequatur excepturi ullam pariatur dolorem magnam ipsam amet velit minima ducimus.',
				public: true,
				menu: true,
				isPageTitle: true,
			}
		} %}
	</div>
	<div class="main__content scroll">
		<div class="row-main">
			{% include '@components/parts/content-content.twig' %}
			{% include '@components/parts/content-categories.twig' %}
			{% include '@components/parts/content-variants.twig' %}
			{% include '@components/parts/content-imgs.twig' %}
			{% include '@components/parts/content-videos.twig' %}
			{% include '@components/parts/content-files.twig' %}
			{% include '@components/parts/content-pages.twig' %}
			{% include '@components/parts/content-links.twig' %}
			{% include '@components/parts/content-other.twig' %}
		</div>
	</div>
	<div class="main__content-side scroll">
		{% include '@components/parts/side-btns.twig' %}
		{% include '@components/parts/side-state.twig' %}
		{% include '@components/parts/side-lang.twig' %}
		{% include '@components/parts/side-template.twig' %}
		{% include '@components/parts/side-edits.twig' %}
	</div>
	<div data-Templates-target="overlays">
		{% include '@components/parts/overlay-variants.twig' with { props: { id: 'variant-1' } } %}
		{% include '@components/parts/overlay-params.twig' %}
		{% include '@components/parts/overlay-seo.twig' %}
		{% include '@components/parts/overlay-validity.twig' %}
		{% include '@components/parts/overlay-imgs.twig' %}
		{% include '@components/parts/overlay-imgs-edit.twig' %}
	</div>

{% endblock %}
