{% extends '@layout/layout.twig' %}
{% set pageTitle = 'Forms' %}

{% block content %}

	<div class="main__content">
		<h1>
			{{ pageTitle }}
		</h1>

		<h2>
			Inputy
		</h2>
		{% include '@components/core/inp.twig' with {
			props: {
				id: 'name',
				label: 'Název',
				value: 'Název produktu'
			}
		} %}
		{% include '@components/core/inp.twig' with {
			props: {
				id: 'annotation',
				label: 'Anotace',
				type: 'textarea',
				info: 'Form<PERSON>t odkazu: "jméno":http://link.cz'
			}
		} %}
		{% include '@components/core/inp.twig' with {
			props: {
				id: 'wysiwyg',
				label: 'Obsah',
				rows: '8',
				type: 'textarea'
			}
		} %}

		<h2>
			H1 input
		</h2>
		{% include '@components/core/inp.twig' with {
			props: {
				id: 'internal-name',
				value: 'Název produktu',
				variant: 'h1',
				classes: ['']
			}
		} %}

		<h2>
			Checkboxy
		</h2>
		{% include '@components/core/checkbox.twig' with {
			props: {
				label: 'Samostatný checkbox'
			}
		} %}

		{% include '@components/core/checkboxes.twig' with {
			props: {
				items: [
					{
						label: 'Pole checkboxů'
					},
					{
						label: 'Další checkbox'
					}
				]
			}
		} %}
	</div>

{% endblock %}
