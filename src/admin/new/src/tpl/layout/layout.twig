<!DOCTYPE html>
<html lang="cs" class="no-js">
	<head>
		<meta charset="utf-8">
		<!--[if IE]><meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><![endif]-->
		<meta name="description" content="">
		<meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no">

		<meta name="twitter:card" content="summary">
		<meta name="twitter:title" content="{{ pageTitle }} | Project">
		<meta name="twitter:description" content="">
		<meta name="twitter:image" content="http://www.example.com/image.jpg">

		<meta property="og:title" content="{{ pageTitle }} | Project">
		<meta property="og:description" content="">
		<meta property="og:image" content="http://www.example.com/image.jpg">
		<meta property="og:site_name" content="Project">
		<meta property="og:url" content="http://www.example.com/">

		<title>{{ pageTitle }} | Project</title>

		<link rel="stylesheet" href="{{ assets.styles }}style.css">
		<link rel="stylesheet" href="{{ assets.styles }}print.css" media="print">


		{% set scripts = [
			assets.scripts ~ 'jquery-3.4.1.min.js',
			assets.scripts ~ 'tinymce/tinymce.min.js',
			assets.scripts ~ 'tinymce/jquery.tinymce.min.js',
			'https://cdn.polyfill.io/v3/polyfill.min.js?features=default,Array.prototype.includes,Object.values,Array.prototype.find,AbortController,fetch',
			assets.scripts ~ 'app.js'
		] %}

		{% for script in scripts %}
		<link rel="preload" as="script" href="{{script}}">
		{% endfor %}

		<link rel="shortcut icon" href="{{ assets.images }}favicon.ico">

		<script>
			(function () {
				var className = document.documentElement.className;
				className = className.replace('no-js', 'js');

				(function() {
					var mediaHover = window.matchMedia('(hover: none), (pointer: coarse), (pointer: none)');
					mediaHover.addListener(function(media) {
						document.documentElement.classList[media.matches ? 'add' : 'remove']('no-hoverevents');
						document.documentElement.classList[!media.matches ? 'add' : 'remove']('hoverevents');
					});
					className += (mediaHover.matches ? ' no-hoverevents' : ' hoverevents');
				})();

				// var supportsCover = 'CSS' in window && typeof CSS.supports === 'function' && CSS.supports('object-fit: cover');
				// className += (supportsCover ? ' ' : ' no-') + 'objectfit';

				// fix iOS zoom issue: https://docs.google.com/document/d/1KclJmXyuuErcvit-kwCC6K2J7dClRef43oyGVCqWxFE/edit#heading=h.sgbqg5nzhvu9
				var ua = navigator.userAgent.toLowerCase();
				var isIOS = /ipad|iphone|ipod/.test(ua) && !window.MSStream;

				if (isIOS === true) {
					var viewportTag = document.querySelector("meta[name=viewport]");
					viewportTag.setAttribute("content", "width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no");
				}

				document.documentElement.className = className;
			}());
		</script>

	</head>
	<body class="lang-cz">
		<header class="header">
			<p class="header__logo">
				<span class="item-icon">
					<span class="item-icon__icon icon">
						{% include '@icons/desktop.svg' %}
					</span>
					<span class="item-icon__text">
						SuperAdmin
					</span>
				</span>
			</p>
			<div class="header__search">
				{% include '@components/form/search.twig' %}
			</div>
			<div class="header__user">
				{% include '@components/menu/user.twig' %}
			</div>
		</header>

		<main id="main" class="main">
			<div class="main__side scroll scroll--white">
				{% include '@components/menu/main.twig' with {
					props: {
						title: 'Moduly',
						items: [
							{
								icon: '@icons/images.svg',
								text: 'Knihovna'
							},
							{
								icon: '@icons/file-alt.svg',
								text: 'Stránky',
							},
							{
								icon: '@icons/file-invoice.svg',
								text: 'Články',
							},
							{
								active: true,
								icon: '@icons/file.svg',
								text: 'Produkty'
							},
							{
								icon: '@icons/ad.svg',
								text: 'Bannery'
							}
						]
					}
				} %}
				{% include '@components/menu/main.twig' with {
					props: {
						title: 'Přehledy',
						items: [
							{
								icon: '@icons/shopping-cart.svg',
								text: 'Objednávky'
							},
							{
								icon: '@icons/envelope.svg',
								text: 'E-maily'
							},
							{
								icon: '@icons/comments.svg',
								text: 'Komentáře'
							}
						]
					}
				} %}
				{% include '@components/menu/main.twig' with {
					props: {
						title: 'Nastavení',
						items: [
							{
								icon: '@icons/globe.svg',
								text: 'Systémové texty'
							},
							{
								icon: '@icons/users.svg',
								text: 'Uživatelé'
							},
							{
								icon: '@icons/sliders-h.svg',
								text: 'Parametry'
							},
							{
								icon: '@icons/percent.svg',
								text: 'Slevové poukazy'
							},
							{
								icon: '@icons/search.svg',
								text: 'Elastic search'
							}
						]
					}
				} %}
				{% include '@components/menu/main.twig' with {
					props: {
						items: [
							{
								icon: '@icons/question.svg',
								text: 'Nápověda'
							},
							{
								icon: '@icons/info.svg',
								text: 'O aplikaci'
							}
						]
					}
				} %}
			</div>
			<div class="main__main">
				{% block content %}{% endblock %}
			</div>
		</main>

		{% for script in scripts %}
			<script src="{{script}}"></script>
		{% endfor %}
		<script>
			App.run()
		</script>
	</body>
</html>
