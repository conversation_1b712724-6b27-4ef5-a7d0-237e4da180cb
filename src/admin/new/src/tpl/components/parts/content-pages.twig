{% embed '@components/box/toggle.twig' with {
	props: {
		title: '<PERSON>r<PERSON>ky / Produkty / <PERSON>',
		id: 'pages',
		icon: '@icons/file.svg',
		tags: [
			{
				text: 'Lokalizované'
			}
		]
	}
} %}{% block content %}
	{% set content %}
		{% include '@components/box/list.twig' with {
			props: {
				data: {
					'controller': 'List',
					'List-name-value': 'page',
					'List-newitem-value': 3,
				},
				listData: {
					'List-target': 'list',
				},
				addData: {
					'action': 'List#add',
				},
				add: true,
				dragdrop: true,
				type: 'input',
				items: [
					{
						inps: [
							{
								id: 'page-1',
								placeholder: '<PERSON>ade<PERSON><PERSON> název stránky',
								value: 'Název vyplněné stránky'
							}
						],
						btnsAfter: [
							{
								icon: '@icons/trash.svg',
								tooltip: 'Odstranit',
								variant: 'remove',
								data: {
									'action': 'RemoveItem#remove'
								},
							}
						],
						data: {
							'controller': 'RemoveItem',
							'removeitem-target': 'item'
						}
					},
					{
						inps: [
							{
								id: 'page-2',
								placeholder: '<PERSON>ade<PERSON><PERSON> náze<PERSON> strán<PERSON>',
								value: 'Název vyplněné stránky'
							}
						],
						btnsAfter: [
							{
								icon: '@icons/trash.svg',
								tooltip: 'Odstranit',
								variant: 'remove',
								data: {
									'action': 'RemoveItem#remove'
								},
							}
						],
						data: {
							'controller': 'RemoveItem',
							'removeitem-target': 'item',
						},
					},
				]
			}
		} %}
	{% endset %}


	<div class="js-lang js-lang--cz">
		<div class="u-mb-xxs tag">
			CZ
		</div>
		{{ content }}
	</div>
	<div class="js-lang js-lang--en">
		{% include '@components/core/checkbox.twig' with {
			props: {
				label: '<span class="grid-inline"><span class="tag">EN</span> <span>přebírá nastavení z výchozí verze</span>',
				checked: true,
				attrsInp: [
					'data-controller="ToggleCheckbox"',
					'data-action="ToggleCheckbox#changeClass"',
					'data-togglecheckbox-target-value="#checkbox-pages"',
					'data-togglecheckbox-target-class-value="is-open"'
				],
			}
		} %}
		<div id="checkbox-pages" class="js-toggle-checkbox__content u-mt--xs">
			{{ content }}
		</div>
	</div>
{% endblock %}{% endembed %}
