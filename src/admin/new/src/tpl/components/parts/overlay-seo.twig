{% embed '@components/box/overlay.twig' with {
	props: {
		id: 'overlay-seo',
		size: 'md',
		title: 'SEO'
	}
} %}{% block content %}
	{% embed '@components/box/toggle.twig' with {
		props: {
			title: '<PERSON><PERSON>',
			id: 'overlay-content-alias',
			open: true,
			tags: [
				{
					text: 'Povinné'
				},
				{
					text: 'Lokalizované'
				},
				{
					text: 'Unikátní'
				}
			]
		}
	} %}{% block content %}
		<div class="js-lang js-lang--cz">
			{% include '@components/core/inp.twig' with {
				props: {
					id: 'cz-alias',
					label: 'CZ',
					classesLabel: ['tag'],
					value: 'nazev-produktu',
					btn: 'Z názvu',
					data: {
						'controller': 'Alias',
						'alias-url-value': '/ajax/alias.txt',
						'alias-data-value': '{
							"lang": "cz",
							"id": "id-elementu"
						}',
						'alias-target-value': '#cz-name'
					},
					dataInp: {
						'alias-target': 'inp'
					},
					dataBtn: {
						'action': '<PERSON>as#generate',
					}
				}
			} %}
		</div>
		<div class="js-lang js-lang--en">
			{% include '@components/core/inp.twig' with {
				props: {
					id: 'en-alias',
					label: 'EN',
					classesLabel: ['tag'],
					value: 'product-name',
					btn: 'Z názvu'
				}
			} %}
		</div>
	{% endblock %}{% endembed %}

	{% embed '@components/box/toggle.twig' with {
		props: {
			title: 'Název v odkazu',
			id: 'overlay-content-name-anchor',
			open: true,
			tags: [
				{
					text: 'Lokalizované'
				}
			]
		}
	} %}{% block content %}
		<div class="js-lang js-lang--cz">
			{% include '@components/core/inp.twig' with {
				props: {
					id: 'cz-name-anchor',
					label: 'CZ',
					classesLabel: ['tag'],
					dataInp: {
						'controller': 'ProductTitleSeo',
						'action': 'ProductTitle:changeTitle@window->ProductTitleSeo#updateValue',
						'producttitleseo-lang-value': 'cz'
					}
				}
			} %}
		</div>
		<div class="js-lang js-lang--en">
			{% include '@components/core/inp.twig' with {
				props: {
					id: 'en-name-anchor',
					label: 'EN',
					classesLabel: ['tag'],
					dataInp: {
						'controller': 'ProductTitleSeo',
						'action': 'ProductTitle:changeTitle@window->ProductTitleSeo#updateValue',
						'producttitleseo-lang-value': 'en'
					}
				}
			} %}
		</div>
	{% endblock %}{% endembed %}

	{% embed '@components/box/toggle.twig' with {
		props: {
			title: 'Název v titulku',
			id: 'overlay-content-name-title',
			open: true,
			tags: [
				{
					text: 'Lokalizované'
				}
			]
		}
	} %}{% block content %}
		<div class="js-lang js-lang--cz">
			{% include '@components/core/inp.twig' with {
				props: {
					id: 'cz-name-title',
					label: 'CZ',
					classesLabel: ['tag'],
					dataInp: {
						'controller': 'ProductTitleSeo',
						'action': 'ProductTitle:changeTitle@window->ProductTitleSeo#updateValue',
						'producttitleseo-lang-value': 'cz'
					}
				}
			} %}
		</div>
		<div class="js-lang js-lang--en">
			{% include '@components/core/inp.twig' with {
				props: {
					id: 'en-name-title',
					label: 'EN',
					classesLabel: ['tag'],
					dataInp: {
						'controller': 'ProductTitleSeo',
						'action': 'ProductTitle:changeTitle@window->ProductTitleSeo#updateValue',
						'producttitleseo-lang-value': 'en'
					}
				}
			} %}
		</div>
	{% endblock %}{% endembed %}

	{% embed '@components/box/toggle.twig' with {
		props: {
			title: 'Description',
			id: 'overlay-content-description',
			open: true,
			tags: [
				{
					text: 'Lokalizované'
				}
			]
		}
	} %}{% block content %}
		<div class="js-lang js-lang--cz">
			{% include '@components/core/inp.twig' with {
				props: {
					id: 'cz-description',
					label: 'CZ',
					classesLabel: ['tag'],
					value: 'Anotace produktu',
					type: 'textarea',
					info: 'Pokud není vyplněno tak se využívá anotace'
				}
			} %}
		</div>
		<div class="js-lang js-lang--en">
			{% include '@components/core/inp.twig' with {
				props: {
					id: 'en-description',
					label: 'EN',
					classesLabel: ['tag'],
					value: 'Product annotation',
					type: 'textarea'
				}
			} %}
		</div>
	{% endblock %}{% endembed %}

	{% embed '@components/box/toggle.twig' with {
		props: {
			title: 'Historie aliasů',
			id: 'overlay-content-alias-history',
			open: true,
			tags: [
				{
					text: 'Lokalizované'
				}
			]
		}
	} %}{% block content %}
		<div class="js-lang js-lang--cz">
			{% include '@components/core/inp.twig' with {
				props: {
					id: 'cz-alias-history',
					type: 'textarea',
					label: 'CZ',
					classesLabel: ['tag']
				}
			} %}
		</div>
		<div class="js-lang js-lang--en">
			{% include '@components/core/inp.twig' with {
				props: {
					id: 'en-alias-history',
					type: 'textarea',
					label: 'EN',
					classesLabel: ['tag']
				}
			} %}
		</div>
	{% endblock %}{% endembed %}
{% endblock %}{% endembed %}
