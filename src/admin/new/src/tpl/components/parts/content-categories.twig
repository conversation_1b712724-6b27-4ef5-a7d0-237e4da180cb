{% embed '@components/box/toggle.twig' with {
	props: {
		title: 'Kate<PERSON>ie',
		id: 'categories',
		icon: '@icons/folder.svg',
		open: true,
		tags: [
			{
				text: 'Povinné'
			},
			{
				text: 'Lokalizované'
			}
		]
	}
} %}{% block content %}
	{% set content %}
		{% include '@components/box/list.twig' with {
			props: {
				data: {
					'controller': 'List',
					'List-name-value': 'category',
					'List-newitem-value': 3,
				},
				listData: {
					'List-target': 'list',
				},
				addData: {
					'action': 'List#add',
				},
				add: true,
				dragdrop: true,
				items: [
					{
						data: {
							'controller': 'RemoveItem SuggestInp',
							'removeitem-target': 'item',
							'suggestinp-target': 'wrapper',
							'suggestinp-url-value': '/ajax/suggest.html',
						},
						inps: [
							{
								id: 'category-1',
								placeholder: 'Zadejte název kategorie',
								value: 'Zbraně / Nože',
								data: {
									'suggestinp-target': 'input',
								}
							}
						],
						btnsAfter: [
							{
								icon: '@icons/trash.svg',
								tooltip: 'Odstranit',
								variant: 'remove',
								data: {
									'action': 'RemoveItem#remove'
								},
							}
						]
					},
					{
						inps: [
							{
								placeholder: 'Zadejte název kategorie',
								value: 'Myslivost a lovectví / Nože'
							}
						],
						btnsAfter: [
							{
								icon: '@icons/trash.svg',
								tooltip: 'Odstranit',
								variant: 'remove',
								data: {
									'action': 'RemoveItem#remove'
								},
							}
						],
						data: {
							'controller': 'RemoveItem',
							'removeitem-target': 'item'
						}
					},
				]
			}
		} %}
	{% endset %}

	<div class="js-lang js-lang--cz">
		<div class="u-mb-xxs tag">
			CZ
		</div>
		{{ content }}
	</div>
	<div class="js-lang js-lang--en">
		{% include '@components/core/checkbox.twig' with {
			props: {
				label: '<span class="grid-inline"><span class="tag">EN</span> <span>přebírá nastavení z výchozí verze</span>',
				checked: true,
				data: {
					'controller': 'ToggleCheckbox',
					'action': 'ToggleCheckbox#changeClass',
					'togglecheckbox-target-value': '#checkbox-categories',
					'togglecheckbox-target-class-value': 'is-open'
				},
			}
		} %}
		<div id="checkbox-categories" class="js-toggle-checkbox__content u-mt--xs">
			{{ content }}
		</div>
	</div>
{% endblock %}{% endembed %}
