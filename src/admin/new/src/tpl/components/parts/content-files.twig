{% embed '@components/box/toggle.twig' with {
	props: {
		title: 'Soubory',
		id: 'files',
		icon: '@icons/file-archive.svg',
		tags: [
			{
				text: 'Lokalizované'
			}
		]
	}
} %}{% block content %}
	{% set addData = {'action': 'List#add'} %}
	{% set content %}
		{% include '@components/box/list.twig' with {
			props: {
				data: {
					'controller': 'List',
					'List-name-value': 'file',
					'List-mutationid-value': '1',
				},
				listData: {
					'List-target': 'list',
				},
				file: true,
				add: true,
				dragdrop: true,
				items: [
					{
						inps: [
							{
								placeholder: 'Zadejte název souboru',
								value: 'Vyplněná hodnota'
							}
						],
						tags: [
							{
								text: 'PDF',
							},
							{
								text: '15.5 MB',
							}
						],
						btnsAfter: [
							{
								icon: '@icons/trash.svg',
								tooltip: 'Odstranit',
								variant: 'remove',
								data: {
									'action': 'RemoveItem#remove'
								},
							}
						],
						data: {
							'controller': 'RemoveItem',
							'removeitem-target': 'item'
						}
					},
					{
						inps: [
							{
								placeholder: 'Zadejte název souboru',
								value: 'Vyplněná hodnota'
							}
						],
						tags: [
							{
								text: 'JPG',
							},
							{
								text: '1.5 MB',
							}
						],
						btnsAfter: [
							{
								icon: '@icons/trash.svg',
								tooltip: 'Odstranit',
								variant: 'remove',
								data: {
									'action': 'RemoveItem#remove'
								},
							}
						],
						data: {
							'controller': 'RemoveItem',
							'removeitem-target': 'item'
						}
					},
				]
			}
		} %}
	{% endset %}

	<div class="js-lang js-lang--cz">
		<div class="u-mb-xxs tag">
			CZ
		</div>
		{{ content }}
	</div>

	<div class="js-lang js-lang--en">
		{% include '@components/core/checkbox.twig' with {
			props: {
				label: '<span class="grid-inline"><span class="tag">EN</span> <span>přebírá nastavení z výchozí verze</span>',
				checked: true,
				attrsInp: [
					'data-controller="ToggleCheckbox"',
					'data-action="ToggleCheckbox#changeClass"',
					'data-togglecheckbox-target-value="#checkbox-files"',
					'data-togglecheckbox-target-class-value="is-open"'
				],
			}
		} %}
		<div id="checkbox-files" class="js-toggle-checkbox__content u-mt--xs">
			{{ content }}
		</div>
	</div>
{% endblock %}{% endembed %}
