{% set variantId = props.id | default %}

{% embed '@components/box/overlay.twig' with {
	props: {
		id: 'overlay-' ~ props.id,
		size: 'md',
		title: 'Editace / Přidání varianty',
		data: {
			'controller': 'ProductVariantEdit',
			'ProductVariantEdit-id-value': props.id,
		},
	}
} %}{% block content %}
	{% embed '@components/box/toggle.twig' with {
		props: {
			title: 'Publikace v modifikacích',
			id: 'overlay-content-variant-public',
			open: true,
			tags: [
				{
					text: 'Povinné'
				},
				{
					text: 'Lokalizované'
				}
			]
		}
	} %}{% block content %}
		<div class="inp js-lang js-lang--cz">
			{% include '@components/core/checkbox.twig' with {
				props: {
					id: variantId ~ '-published-cz',
					label: '<span class="grid-inline"><span class="tag">CZ</span> <span>Aktivováno</span></span>',
					checked: true,
					data: {
						'action': 'change->ProductVariantEdit#edit',
						'ProductVariantEdit-target': 'lang',
						'lang': 'CZ',
					}
				}
			} %}
		</div>
		<div class="inp js-lang js-lang--en">
			{% include '@components/core/checkbox.twig' with {
				props: {
					id: variantId ~ '-published-en',
					label: '<span class="grid-inline"><span class="tag">EN</span> <span>Aktivováno</span></span>',
					data: {
						'action': 'change->ProductVariantEdit#edit',
						'ProductVariantEdit-target': 'lang',
						'lang': 'EN',
					}
				}
			} %}
		</div>
	{% endblock %}{% endembed %}

	{% embed '@components/box/toggle.twig' with {
		props: {
			title: 'Název varianty',
			id: 'overlay-content-variant-name',
			open: true,
			tags: [
				{
					text: 'Povinné'
				},
				{
					text: 'Lokalizované'
				}
			]
		}
	} %}{% block content %}
		<div class="js-lang js-lang--cz">
			{% include '@components/core/inp.twig' with {
				props: {
					id: variantId ~ '-name-cz',
					label: 'CZ',
					classesLabel: ['tag'],
					value: 'Název varianty',
					dataInp: {
						'action': 'input->ProductVariantEdit#edit',
						'ProductVariantEdit-target': 'name',
						'lang': 'CZ',
					}
				}
			} %}
		</div>
		<div class="js-lang js-lang--en">
			{% include '@components/core/inp.twig' with {
				props: {
					id: variantId ~ '-name-en',
					label: 'EN',
					classesLabel: ['tag'],
					value: 'Variant name',
					dataInp: {
						'action': 'input->ProductVariantEdit#edit',
						'ProductVariantEdit-target': 'name',
						'lang': 'EN',
					}
				}
			} %}
		</div>
	{% endblock %}{% endembed %}

	{% embed '@components/box/toggle.twig' with {
		props: {
			title: 'Cena s DPH',
			id: 'overlay-content-variant-price',
			open: true,
			tags: [
				{
					text: 'Povinné'
				},
				{
					text: 'Lokalizované'
				}
			]
		}
	} %}{% block content %}
		<div class="js-lang js-lang--cz">
			{% include '@components/core/inp.twig' with {
				props: {
					id: variantId ~ '-price-cz',
					label: 'CZ',
					classesLabel: ['tag'],
					value: '1500',
					prefix: 'Kč',
					dataInp: {
						'ProductVariantEdit-target': 'price',
						'action': 'input->ProductVariantEdit#edit',
						'lang': 'CZ',
						'currency': 'Kč',
						'action': 'input->ProductVariantEdit#edit'
					}
				}
			} %}
		</div>
		<div class="js-lang js-lang--en">
			{% include '@components/core/inp.twig' with {
				props: {
					id: variantId ~ '-price-en',
					label: 'EN',
					classesLabel: ['tag'],
					value: '60',
					prefix: '€',
					dataInp: {
						'ProductVariantEdit-target': 'price',
						'action': 'input->ProductVariantEdit#edit',
						'lang': 'EN',
						'currency': '€',
						'action': 'input->ProductVariantEdit#edit'
					}
				}
			} %}
		</div>
	{% endblock %}{% endembed %}

	{% embed '@components/box/toggle.twig' with {
		props: {
			title: 'Kusy',
			id: 'overlay-content-variant-amount',
			open: true,
			tags: [
				{
					text: 'Lokalizované'
				}
			]
		}
	} %}{% block content %}
		<div class="js-lang js-lang--cz">
			{% include '@components/core/inp.twig' with {
				props: {
					id: variantId ~ '-amount-cz',
					label: 'CZ',
					classesLabel: ['tag'],
					value: '100',
					prefix: 'ks',
					dataInp: {
						'ProductVariantEdit-target': 'amount',
						'action': 'input->ProductVariantEdit#edit',
						'lang': 'CZ',
						'action': 'input->ProductVariantEdit#edit'
					}
				}
			} %}
		</div>
		<div class="js-lang js-lang--en">
			{% include '@components/core/inp.twig' with {
				props: {
					id: variantId ~ '-amount-en',
					label: 'EN',
					classesLabel: ['tag'],
					value: '100',
					prefix: 'ks',
					dataInp: {
						'ProductVariantEdit-target': 'amount',
						'action': 'input->ProductVariantEdit#edit',
						'lang': 'EN',
						'action': 'input->ProductVariantEdit#edit'
					}
				}
			} %}
		</div>
	{% endblock %}{% endembed %}

	{% embed '@components/box/toggle.twig' with {
		props: {
			title: 'Definice varianty',
			id: 'overlay-content-variant-defs',
			open: true,
			tags: [
				{
					text: 'Povinné'
				}
			]
		}
	} %}{% block content %}
		<div class="grid grid--y-0">
			<div class="grid__cell size--6-12">
				{% include '@components/core/inp.twig' with {
					props: {
						id: variantId ~ '-color',
						label: 'Barva',
						classesLabel: ['title'],
						type: 'select',
						options: [
							{
								text: 'Vyberte šablonu'
							},
							{
								text: 'Defaultní'
							},
							{
								text: 'O nás'
							},
							{
								text: 'Kontakt'
							}
						]
					}
				} %}
			</div>
			<div class="grid__cell size--6-12">
				{% include '@components/core/inp.twig' with {
					props: {
						id: variantId ~ '-size',
						label: 'Velikost',
						classesLabel: ['title'],
						type: 'select',
						options: [
							{
								text: 'Vyberte šablonu'
							},
							{
								text: 'Defaultní'
							},
							{
								text: 'O nás'
							},
							{
								text: 'Kontakt'
							}
						]
					}
				} %}
			</div>
		</div>
	{% endblock %}{% endembed %}

	{% embed '@components/box/toggle.twig' with {
		props: {
			title: 'Akční produkt',
			id: 'overlay-content-variant-action',
			tags: [
				{
					text: 'Lokalizované'
				}
			]
		}
	} %}{% block content %}
		<div class="js-lang js-lang--cz">
			<div class="u-mb-xxs tag">
				CZ
			</div>
			{% set lang = 'cz' %}
			<div class="u-mb-sm">
				<div class="grid grid--y-0">
					<div class="grid__cell size--6-12">
						{% include '@components/core/inp.twig' with {
							props: {
								id: variantId ~ '-discount-amount-' ~ lang,
								label: 'Velikost slevy',
								classesLabel: ['title'],
								value: '0',
								prefix: 'Kč',
								type: 'number'
							}
						} %}
					</div>
					<div class="grid__cell size--6-12">
						{% include '@components/core/inp.twig' with {
							props: {
								id: variantId ~ 'discount-percentage-' ~ lang,
								label: 'Použijte jednu z možností % nebo pevná částka',
								classesLabel: ['u-font-regular', 'u-text-right', 'u-font-sm'],
								value: '0',
								prefix: '%',
								type: 'number'
							}
						} %}
					</div>
					<div class="grid__cell size--6-12">
						{% include '@components/core/inp.twig' with {
							props: {
								id: variantId ~ 'discount-from-' ~ lang,
								label: 'Platnost od',
								classesLabel: ['title'],
								type: 'datetime-local',
								classes: ['']
							}
						} %}
					</div>
					<div class="grid__cell size--6-12">
						{% include '@components/core/inp.twig' with {
							props: {
								id: variantId ~ 'discount-to-' ~ lang,
								label: 'do',
								classesLabel: ['title'],
								type: 'datetime-local',
								classes: ['']
							}
						} %}
					</div>
				</div>
			</div>
		</div>
		<div class="js-lang js-lang--en">
			{% include '@components/core/checkbox.twig' with {
				props: {
					label: '<span class="grid-inline"><span class="tag">EN</span> <span>přebírá nastavení z výchozí verze</span>',
					checked: true,
					data: {
						'controller': 'ToggleCheckbox',
						'action': 'ToggleCheckbox#changeClass',
						'togglecheckbox-target-value': '#checkbox-variants',
						'togglecheckbox-target-class-value': 'is-open'
					},
				}
			} %}
			<div id="checkbox-variants" class="js-toggle-checkbox__content u-mt--xs">
				{% set lang = 'en' %}
				<div class="u-mb-sm">
					<div class="grid grid--y-0">
						<div class="grid__cell size--6-12">
							{% include '@components/core/inp.twig' with {
								props: {
									id: variantId ~ '-discount-amount-' ~ lang,
									label: 'Velikost slevy',
									classesLabel: ['title'],
									value: '0',
									prefix: 'Kč',
									type: 'number'
								}
							} %}
						</div>
						<div class="grid__cell size--6-12">
							{% include '@components/core/inp.twig' with {
								props: {
									id: variantId ~ 'discount-percentage-' ~ lang,
									label: 'Použijte jednu z možností % nebo pevná částka',
									classesLabel: ['u-font-regular', 'u-text-right', 'u-font-sm'],
									value: '0',
									prefix: '%',
									type: 'number'
								}
							} %}
						</div>
						<div class="grid__cell size--6-12">
							{% include '@components/core/inp.twig' with {
								props: {
									id: variantId ~ 'discount-from-' ~ lang,
									label: 'Platnost od',
									classesLabel: ['title'],
									type: 'datetime-local',
									classes: ['']
								}
							} %}
						</div>
						<div class="grid__cell size--6-12">
							{% include '@components/core/inp.twig' with {
								props: {
									id: variantId ~ 'discount-to-' ~ lang,
									label: 'do',
									classesLabel: ['title'],
									type: 'datetime-local',
									classes: ['']
								}
							} %}
						</div>
					</div>
				</div>
			</div>
		</div>
	{% endblock %}{% endembed %}

	{% include '@components/core/inp.twig' with {
		props: {
			id: variantId ~ 'ean',
			label: 'EAN',
		}
	} %}

	{% include '@components/core/inp.twig' with {
		props: {
			id: variantId ~ 'code',
			label: 'Kód produktu',
		}
	} %}

	<p>
		TODO: Obrázek(y) pro variantu?
	</p>
{% endblock %}{% endembed %}
