{% embed '@components/box/toggle.twig' with {
	props: {
		title: 'Odka<PERSON>',
		id: 'links',
		icon: '@icons/link.svg',
		tags: [
			{
				text: 'Lokalizované'
			}
		]
	}
} %}{% block content %}
	{% set content %}
		{% include '@components/box/list.twig' with {
			props: {
				data: {
					'controller': 'List',
					'List-name-value': 'link',
					'List-newitem-value': 1,
				},
				listData: {
					'List-target': 'list',
				},
				addData: {
					'action': 'List#add',
				},
				add: true,
				dragdrop: true,
				items: []
			}
		} %}
	{% endset %}

	<div class="js-lang js-lang--cz">
		<div class="u-mb-xxs tag">
			CZ
		</div>
		{{ content }}
	</div>
	<div class="js-lang js-lang--en">
		{% include '@components/core/checkbox.twig' with {
			props: {
				label: '<span class="grid-inline"><span class="tag">EN</span> <span>přeb<PERSON><PERSON><PERSON> nastavení z výchozí verze</span>',
				checked: true,
				attrsInp: [
					'data-controller="ToggleCheckbox"',
					'data-action="ToggleCheckbox#changeClass"',
					'data-togglecheckbox-target-value="#checkbox-links"',
					'data-togglecheckbox-target-class-value="is-open"'
				],
			}
		} %}
		<div id="checkbox-links" class="js-toggle-checkbox__content u-mt--xs">
			{{ content }}
		</div>
	</div>
{% endblock %}{% endembed %}
