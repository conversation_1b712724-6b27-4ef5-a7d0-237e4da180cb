{% embed '@components/box/toggle.twig' with {
	props: {
		title: 'Ceny / Sklady / Varianty',
		id: 'variants',
		icon: '@icons/coins.svg',
		open: true,
		tags: [
			{
				text: 'Povinné'
			},
			{
				text: 'Lokalizované'
			}
		]
	}
} %}{% block content %}
	{% include '@components/box/list.twig' with {
		props: {
			data: {
				'controller': 'List',
				'List-name-value': 'variant',
				'List-newitem-value': 2,
			},
			listData: {
				'List-target': 'list',
			},
			addData: {
				'action': 'List#add',
			},
			add: true,
			dragdrop: true,
			items: [
				{
					texts: [
						{
							text: '<span data-ProductVariant-target="text">Název varianty (1 500 Kč / 100 ks)</span>'
						}
					],
					langs: ['CZ', 'EN'],
					btnsAfter: [
						{
							icon: '@icons/pencil-alt.svg',
							tooltip: 'Editovat',
							data: {
								'controller': 'Toggle',
								'action': 'Toggle#changeClass ProductVariant#edit',
								'toggle-target-value': '#overlay-variant-1',
								'toggle-target-class-value': 'is-visible'
							},
						},
						{
							icon: '@icons/trash.svg',
							tooltip: 'Odstranit',
							variant: 'remove',
							data: {
								'action': 'RemoveItem#remove'
							},
						}
					],
					data: {
						'controller': 'ProductVariant RemoveItem',
						'action': 'ProductVariantEdit:updateValues@window->ProductVariant#updateValues',
						'ProductVariant-id-value': 'variant-1',
						'RemoveItem-target': 'item',
					}
				}
			]
		}
	} %}
{% endblock %}{% endembed %}
