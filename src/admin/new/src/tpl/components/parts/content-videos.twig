{% embed '@components/box/toggle.twig' with {
	props: {
		title: 'Videa <span class="u-color-text-lighten">(YouTube / Vimeo)</span>',
		id: 'videos',
		icon: '@icons/youtube.svg',
		tags: [
			{
				text: 'Lokalizované'
			}
		]
	}
} %}{% block content %}
	{% set content %}
		{% include '@components/box/list.twig' with {
			props: {
				data: {
					'controller': 'List',
					'List-name-value': 'video',
					'List-mutationid-value': '1',
					'List-newitem-value': 2,
				},
				listData: {
					'List-target': 'list',
				},
				addData: {
					'action': 'List#add',
				},
				add: true,
				dragdrop: true,
				items: [
					{
						inps: [
							{
								id: 'video-1-url',
								placeholder: 'Url videa',
								value: 'https://www.youtube.com/watch?v=5i8iMGQU_jw'
							},
							{
								id: 'video-1-name',
								placeholder: 'Název',
								value: 'Super video'
							}
						],
						btnsAfter: [
							{
								icon: '@icons/trash.svg',
								tooltip: 'Odstranit',
								variant: 'remove',
								data: {
									'action': 'RemoveItem#remove'
								},
							}
						],
						data: {
							'controller': 'RemoveItem',
							'removeitem-target': 'item'
						}
					}
				]
			}
		} %}
	{% endset %}
	<div class="js-lang js-lang--cz">
		<div class="u-mb-xxs">
			<div class="tag">
				CZ
			</div>
		</div>
		{{ content }}
	</div>
	<div class="js-lang js-lang--en">
		{% include '@components/core/checkbox.twig' with {
			props: {
				label: '<span class="grid-inline"><span class="tag">EN</span> <span>přebírá nastavení z výchozí verze</span>',
				checked: true,
				data: {
					'controller': 'ToggleCheckbox',
					'action': 'ToggleCheckbox#changeClass',
					'togglecheckbox-target-value': '#checkbox-videos',
					'togglecheckbox-target-class-value': 'is-open'
				},
			}
		} %}
		<div id="checkbox-videos" class="js-toggle-checkbox__content u-mt--xs">
			{{ content }}
		</div>
	</div>
{% endblock %}{% endembed %}
