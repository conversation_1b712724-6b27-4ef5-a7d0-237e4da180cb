{% embed '@components/box/toggle.twig' with {
	props: {
		title: 'Obsah',
		id: 'content',
		icon: '@icons/align-left.svg',
		open: true,
		tags: [
			{
				text: 'Povinné'
			},
			{
				text: 'Lokalizované'
			}
		]
	}
} %}{% block content %}
	<div class="js-lang js-lang--cz">
		<div class="tag u-mb-xs">
			CZ
		</div>
		{% include '@components/core/inp.twig' with {
			props: {
				id: 'cz-name',
				label: 'Název produktu',
				classesLabel: ['title'],
				value: 'Název produktu',
				dataInp: {
					'controller': 'ProductTitle',
					'action': 'input->ProductTitle#changeTitle',
					'producttitle-lang-value': 'cz'
				}
			}
		} %}
		{% include '@components/core/inp.twig' with {
			props: {
				id: 'cz-annotation',
				label: 'Anotace',
				classesLabel: ['title'],
				value: 'Anotace produktu',
				type: 'textarea'
			}
		} %}
		{% include '@components/core/inp.twig' with {
			props: {
				id: 'cz-wysiwyg',
				label: 'Obsah',
				classesLabel: ['title'],
				value: '',
				type: 'textarea',
				rows: 12,
				dataInp: {
					'controller': 'Tiny',
					'tiny-target': 'item'
				}
			}
		} %}
	</div>
	<div class="js-lang js-lang--en">
		<div class="tag u-mb-xs">
			EN
		</div>
		{% include '@components/core/inp.twig' with {
			props: {
				id: 'en-name',
				label: 'Název produktu',
				classesLabel: ['title'],
				value: 'Product name',
				dataInp: {
					'controller': 'ProductTitle',
					'action': 'input->ProductTitle#changeTitle',
					'producttitle-lang-value': 'en'
				}
			}
		} %}
		{% include '@components/core/inp.twig' with {
			props: {
				id: 'en-annotation',
				label: 'Anotace',
				classesLabel: ['title'],
				value: 'Product annotation',
				type: 'textarea'
			}
		} %}
		{% include '@components/core/inp.twig' with {
			props: {
				id: 'en-wysiwyg',
				label: 'Obsah',
				classesLabel: ['title'],
				value: '',
				type: 'textarea',
				rows: 12,
				dataInp: {
					'controller': 'Tiny',
					'tiny-target': 'item'
				}
			}
		} %}
	</div>
{% endblock %}{% endembed %}
