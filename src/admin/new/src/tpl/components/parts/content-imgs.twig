{% embed '@components/box/toggle.twig' with {
	props: {
		id: 'images',
		open: true,
		title: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
		icon: '@icons/images.svg',
		tags: [
			{
				text: 'Lokalizované'
			}
		]
	}
} %}{% block content %}
	{% include '@components/core/checkbox.twig' with {
		props: {
			label: 'Skrýt první obrázek na detailu',
			classes: ['u-mb-xs']
		}
	} %}
	{% include '@components/box/imgs.twig' with {
		props: {
			add: true,
			dataAdd: {
				'controller': 'Toggle',
				'action': 'Toggle#changeClass',
				'toggle-target-value': '#overlay-imgs',
				'toggle-target-class-value': 'is-visible'
			},
			dragdrop: true,
			items: [
				{
					data: {
						'controller': 'RemoveItem',
						'removeitem-target': 'item',
						'removeitem-animation-value': 'fade'
					},
					img: '../img/illust/sample.jpg',
					inps: [
						{
							placeholder: 'Zadejte alternat<PERSON><PERSON><PERSON> n<PERSON> obr<PERSON>zku',
							value: 'Název obrázku'
						}
					],
					btns: [
						{
							icon: '@icons/pencil-alt.svg',
							tooltip: 'Editovat',
							data: {
								'controller': 'Toggle',
								'action': 'Toggle#changeClass',
								'toggle-target-value': '#overlay-imgs-edit',
								'toggle-target-class-value': 'is-visible'
							},
						},
						{
							icon: '@icons/trash.svg',
							tooltip: 'Odstranit',
							variant: 'remove',
							data: {
								'action': 'RemoveItem#remove'
							},
						}
					]
				},
				{
					data: {
						'controller': 'RemoveItem',
						'removeitem-target': 'item',
						'removeitem-animation-value': 'fade'
					},
					img: '../img/illust/sample.jpg',
					inps: [
						{
							placeholder: 'Zadejte alternativní název obrázku',
							value: 'Název obrázku'
						}
					],
					btns: [
						{
							icon: '@icons/pencil-alt.svg',
							tooltip: 'Editovat',
							data: {
								'controller': 'Toggle',
								'action': 'Toggle#changeClass',
								'toggle-target-value': '#overlay-imgs-edit',
								'toggle-target-class-value': 'is-visible'
							},
						},
						{
							icon: '@icons/trash.svg',
							tooltip: 'Odstranit',
							variant: 'remove',
							data: {
								'action': 'RemoveItem#remove'
							},
						}
					]
				},
				{
					data: {
						'controller': 'RemoveItem',
						'removeitem-target': 'item',
						'removeitem-animation-value': 'fade'
					},
					img: '../img/illust/sample-vertical.jpg',
					inps: [
						{
							placeholder: 'Zadejte alternativní název obrázku',
							value: 'Název obrázku'
						}
					],
					btns: [
						{
							icon: '@icons/pencil-alt.svg',
							tooltip: 'Editovat',
							data: {
								'controller': 'Toggle',
								'action': 'Toggle#changeClass',
								'toggle-target-value': '#overlay-imgs-edit',
								'toggle-target-class-value': 'is-visible'
							},
						},
						{
							icon: '@icons/trash.svg',
							tooltip: 'Odstranit',
							variant: 'remove',
							data: {
								'action': 'RemoveItem#remove'
							},
						}
					]
				},
				{
					data: {
						'controller': 'RemoveItem',
						'removeitem-target': 'item',
						'removeitem-animation-value': 'fade'
					},
					img: '../img/illust/sample.jpg',
					inps: [
						{
							placeholder: 'Zadejte alternativní název obrázku',
							value: 'Název obrázku'
						}
					],
					btns: [
						{
							icon: '@icons/pencil-alt.svg',
							tooltip: 'Editovat',
							data: {
								'controller': 'Toggle',
								'action': 'Toggle#changeClass',
								'toggle-target-value': '#overlay-imgs-edit',
								'toggle-target-class-value': 'is-visible'
							},
						},
						{
							icon: '@icons/trash.svg',
							tooltip: 'Odstranit',
							variant: 'remove',
							data: {
								'action': 'RemoveItem#remove'
							},
						}
					]
				}
			]
		}
	} %}
{% endblock %}{% endembed %}
