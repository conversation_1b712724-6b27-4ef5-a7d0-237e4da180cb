{% set props = {
	items: props.items | default([]),
	classes: props.classes | default([]),
} %}

{% set classes = [
	'm-icons'
] | merge(props.classes) | filter(i => i) | join(' ') %}

<div class="{{ classes }}">
	<ul class="m-icons__list">
		{% for item in props.items %}
			{% set item = {
				icon: item.icon | default('@icons/file.svg'),
				linkType: item.linkType | default in ['overlay', 'toggle'] ? item.linkType,
				href: item.href | default,
				tooltip: item.tooltip | default
			} %}
			<li class="m-icons__item">
				<a href="{{ item.href }}" class="m-icons__link{% if item.tooltip %} tooltip{% endif %}"{% if item.linkType == 'overlay' %} data-controller="Toggle" data-action="Toggle#changeClass" data-toggle-target-class-value="is-visible"{% endif %}{% if item.linkType == 'toggle' %} data-controller="Toggle" data-action="Toggle#addClass" data-toggle-target-class-value="is-open"{% endif %}>
					<span class="icon">
						{% include item.icon %}
					</span>
					{% if item.tooltip %}
						<span class="tooltip__content">
							{{ item.tooltip }}
						</span>
					{% endif %}
				</a>
			</li>
		{% endfor %}
	</ul>
</div>
