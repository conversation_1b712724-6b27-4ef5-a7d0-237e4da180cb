{% set props = {
	items: props.items | default([]),
	classes: props.classes | default(['u-mb-sm']),
} %}

{% set classes = [
	'm-sections'
] | merge(props.classes) | filter(i => i) | join(' ') %}

<div class="{{ classes }}">
	<ul class="m-sections__list">
		{% for item in props.items %}
			{% set item = {
				icon: item.icon | default('@icons/file.svg'),
				iconAfter: item.iconAfter | default('@icons/chevron-right.svg'),
				href: item.href | default('#'),
				classes: item.classes | default([]),
				title: item.title | default,
				variant: item.variant | default in ['inactive'] ? item.variant,
				desc: item.desc | default,
			} %}

			{% set classesItem = [
				'm-sections__item',
				item.variant ? 'm-sections__item--' ~ item.variant,
			] | merge(item.classes) | filter(i => i) | join(' ') %}

			<li class="{{ classesItem }}">
				<a href="{{ item.href }}" class="m-sections__link" data-controller="Toggle" data-action="Toggle#changeClass" data-toggle-target-class-value="is-visible">
					<span class="m-sections__icon icon">
						{% include item.icon %}
					</span>
					<span class="m-sections__text">
						<strong class="m-sections__title">
							{{ item.title }}
						</strong>
						{% if item.desc %}
							<span class="m-sections__desc">
								{{ item.desc|default }}
							</span>
						{% endif %}
					</span>
					<span class="m-sections__icon icon">
						{% include item.iconAfter %}
					</span>
				</a>
			</li>
		{% endfor %}
	</ul>
</div>
