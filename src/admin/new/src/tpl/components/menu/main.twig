{% set props = {
	title: props.title | default,
	items: props.items | default([]),
	classes: props.classes | default([]),
} %}

{% set classes = [
	'm-main'
] | merge(props.classes) | filter(i => i) | join(' ') %}

<div class="{{ classes }}">
	{% if props.title %}
		<h2 class="m-main__title">
			{{ props.title }}
		</h2>
	{% endif %}
	<ul class="m-main__list">
		{% for item in props.items %}
			{% set item = {
				text: item.text | default,
				active: item.active | default(false),
				icon: item.icon | default('@icons/file.svg')
			} %}
			<li class="m-main__item">
				<a href="#" class="m-main__link item-icon{% if item.active %} is-active{% endif %}">
					<span class="item-icon__icon icon">
						{% include item.icon %}
					</span>
					<span class="item-icon__text">
						{{ item.text }}
					</span>
				</a>
			</li>
		{% endfor %}
	</ul>
</div>
