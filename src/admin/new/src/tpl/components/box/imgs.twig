{% set props = {
	items: props.items | default([]),
	title: props.title | default,
	dragdrop: props.dragdrop | default(false),
	add: props.add | default(false),
	dataAdd: props.dataAdd | default(null),
	classes: props.classes | default(['u-mb-sm']),
} %}

{% set classes = [
	'b-imgs'
] | merge(props.classes) | filter(i => i) | join(' ') %}

<div class="{{ classes }}">
	{% if props.title %}
		<h2 class="b-std__title title">
			{{ props.title }}
		</h2>
	{% endif %}
	<div class="b-imgs__list grid grid--x-xs grid--x-xs">
		{% for item in props.items %}
			{% set item = {
				img: item.img | default,
				btns: item.btns | default([]),
				data: item.data | default(null),
			} %}

			<div class="b-imgs__item grid__cell size--3-12"
				{% if item.data is not null %}
					{% for key, value in item.data %}
						data-{{ key }}="{{ value }}"
					{% endfor %}
				{% endif %}
			>
				<div class="b-imgs__inner">
					{% if item.img %}
						<span class="b-imgs__img">
							<img src="{{ item.img }}" alt="">
						</span>
					{% endif %}
					{% if props.dragdrop %}
						<button class="b-imgs__dragdrop btn-icon btn-icon--grab tooltip" type="button">
							{% include '@icons/grip-vertical.svg' %}
							<span class="tooltip__content">
								Přesunout
							</span>
						</button>
					{% endif %}
					{% if item.btns %}
						<div class="b-imgs__btns">
							{% for btn in item.btns %}
								{% set btn = {
									icon: btn.icon | default('@icons/user.svg'),
									tooltip: btn.tooltip | default,
									type: btn.type | default in ['checkbox'] ? btn.type,
									variant: btn.variant | default in ['remove'] ? btn.variant,
									classes: btn.classes | default(['']),
									data: btn.data | default(null),
								} %}

								{% set classesBtn = [
									'b-imgs__btn',
									'btn-icon',
									btn.type ? 'btn-icon--' ~ btn.type,
									btn.variant ? 'btn-icon--' ~ btn.variant,
									btn.tooltip ? 'tooltip',
								] | merge(btn.classes) | filter(i => i) | join(' ') %}

								<button class="{{ classesBtn }}"
									type="button"
									{% if btn.data is not null %}
										{% for key, value in btn.data %}
											data-{{ key }}="{{ value }}"
										{% endfor %}
									{% endif %}
								>
									{% include btn.icon %}
									{% if btn.tooltip %}
										<span class="tooltip__content">
											{{ btn.tooltip }}
										</span>
									{% endif %}
								</button>
							{% endfor %}
						</div>
					{% endif %}
				</div>
			</div>
		{% endfor %}
	</div>
	{% if props.add %}
		<div class="u-mt-xs">
			<button class="b-imgs__add btn"
				type="button"
				{% if props.dataAdd is not null %}
					{% for key, value in props.dataAdd %}
						data-{{ key }}="{{ value }}"
					{% endfor %}
				{% endif %}>
				{% include '@icons/plus.svg' %}
			</button>
		</div>
	{% endif %}
</div>

