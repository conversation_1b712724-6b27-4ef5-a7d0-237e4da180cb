{% set props = {
	title: props.title | default,
	classes: props.classes | default(['u-mb-sm']),
} %}

{% set blocks = {
	content: block('content') is defined ? block('content'),
} %}

{% set classes = [
	'b-std'
] | merge(props.classes) | filter(i => i) | join(' ') %}

<div class="{{ classes }}">
	{% if props.title %}
		<h2 class="b-std__title title">
			{{ props.title }}
		</h2>
	{% endif %}
	<div class="b-std__content">
		{{ blocks.content|raw }}
	</div>
</div>

