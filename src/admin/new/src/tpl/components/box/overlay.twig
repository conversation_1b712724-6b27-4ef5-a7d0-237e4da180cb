{% set props = {
	id: props.id | default,
	title: props.title | default,
	classes: props.classes | default([]),
	data: props.data | default(null)
} %}

{% set blocks = {
	content: block('content') is defined ? block('content'),
} %}

{% set classes = [
	'b-overlay',
] | merge(props.classes) | filter(i => i) | join(' ') %}

<div
	id="{{ props.id }}"
	class="{{ classes }}"
	{% if props.data is not null %}
		{% for key, value in props.data %}
			data-{{ key }}="{{ value }}"
		{% endfor %}
	{% endif %}
>
	<div class="b-overlay__bg" data-controller="Toggle" data-action="click->Toggle#changeClass" data-toggle-target-value="#{{ props.id }}" data-toggle-target-class-value="is-visible"></div>
	<div class="b-overlay__inner">
		<div class="b-overlay__header">
			{% include '@components/box/header.twig' with {
				props: {
					title: props.title,
					variant: 'overlay',
					hrefClose: props.id
				}
			} %}
		</div>
		<div class="b-overlay__content scroll">
			<div class="row-main">
				{{ blocks.content|raw }}
			</div>
		</div>
	</div>
</div>
