{% set props = {
	title: props.title | default,
	img: props.img | default,
	hrefClose: props.hrefClose | default,
	published: props.published | default(false),
	menu: props.menu | default(false),
	public: props.public | default(false),
	variant: props.variant | default in ['overlay'] ? props.variant,
	classes: props.classes | default(['']),
	isPageTitle: props.isPageTitle | default(false),
} %}

{% set classes = [
	'b-header'
] | merge(props.classes) | filter(i => i) | join(' ') %}

<div class="{{ classes }}">
	<a href="#{{ props.hrefClose }}" class="b-header__back icon"{% if props.variant == 'overlay' %} data-controller="Toggle" data-action="Toggle#changeClass" data-toggle-target-class-value="is-visible"{% endif %}>
		{% include '@icons/chevron-left.svg' %}
	</a>
	{% if props.menu %}
		<div class="b-header__img">
			<img src="{{ props.img }}" alt="">
		</div>
	{% endif %}
	<div class="b-header__content u-mb-last-0">
		<h1
			class="b-header__title"
			{% if props.isPageTitle %}
				data-controller="PageTitle"
				data-action="ProductTitle:changeTitle@window->PageTitle#updateTitle"
				data-PageTitle-lang-value="cz"
			{% endif %}
		>
			{{ props.title }}
		</h1>
		{% if props.public %}
			<p class="grid-inline">
				<span>
					Publikováno
				</span>
				<span class="tag">
					CZ
				</span>
				<span class="tag">
					EN
				</span>
			</p>
		{% endif %}
	</div>
	{% if props.menu %}
		<div class="b-header__menu">
			{% include '@components/menu/icons.twig' with {
				props: {
					items: [
						{
							href: '#content',
							icon: '@icons/align-left.svg',
							tooltip: 'Obsah',
							linkType: 'toggle'
						},
						{
							href: '#categories',
							icon: '@icons/folder.svg',
							tooltip: 'Kategorie',
							linkType: 'toggle'
						},
						{
							href: '#variants',
							icon: '@icons/coins.svg',
							tooltip: 'Varianty',
							linkType: 'toggle'
						},
						{
							href: '#images',
							icon: '@icons/images.svg',
							tooltip: 'Obrázky',
							linkType: 'toggle'
						},
						{
							href: '#videos',
							icon: '@icons/youtube.svg',
							tooltip: 'Videa',
							linkType: 'toggle'
						},
						{
							href: '#files',
							icon: '@icons/file-archive.svg',
							tooltip: 'Soubory',
							linkType: 'toggle'
						},
						{
							href: '#pages',
							icon: '@icons/file.svg',
							tooltip: 'Stránky',
							linkType: 'toggle'
						},
						{
							href: '#links',
							icon: '@icons/link.svg',
							tooltip: 'Odkazy',
							linkType: 'toggle'
						},
						{
							icon: '@icons/sliders-h.svg',
							tooltip: 'Parametry',
							href: '#overlay-params',
							linkType: 'overlay'
						},
						{
							icon: '@icons/google.svg',
							tooltip: 'SEO',
							href: '#overlay-seo',
							linkType: 'overlay'
						},
						{
							icon: '@icons/calendar-alt.svg',
							tooltip: 'Platnost',
							href: '#overlay-validity',
							linkType: 'overlay'
						}
					]
				}
			} %}
		</div>
	{% endif %}
</div>
