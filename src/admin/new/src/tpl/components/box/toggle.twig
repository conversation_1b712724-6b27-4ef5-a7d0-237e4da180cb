{% set props = {
	title: props.title | default,
	id: props.id | default,
	icon: props.icon | default,
	tags: props.tags | default([]),
	open: props.open | default(false),
	classes: props.classes | default(['u-mb-md']),
} %}

{% set blocks = {
	content: block('content') is defined ? block('content'),
} %}

{% set classes = [
	'b-toggle',
	props.open ? 'is-open'
] | merge(props.classes) | filter(i => i) | join(' ') %}

<div
	id="{{ props.id }}"
	class="{{ classes }}"
	data-controller="Toggle"
	data-toggle-target-value="#{{ props.id }}"
	data-toggle-target-class-value="is-open"
	data-toggle-name-value="{{ props.id }}"
>
	<div class="b-toggle__header{% if props.open %} is-active{% endif %}">
		<button class="b-toggle__tool btn-icon" data-action="Toggle#changeClass" type="button">
			{% include '@icons/chevron-right.svg' %}
		</button>
		<div class="grid-inline">
			<strong class="flex-grow">
				{% if props.icon %}
					<span class="item-icon">
						<span class="item-icon__icon icon">
							{% include props.icon %}
						</span>
						<span class="item-icon__text">
							{{ props.title|raw }}
						</span>
					</span>
				{% else %}
					{{ props.title }}
				{% endif %}
			</strong>
			{% if props.tags|length %}
				<span class="grid-inline">
					{% for tag in props.tags %}
						<span class="tag">
							{{ tag.text }}
						</span>
					{% endfor %}
				</span>
			{% endif %}
		</div>
	</div>
	<div class="b-toggle__content">
		{{ blocks.content|raw }}
	</div>
</div>

