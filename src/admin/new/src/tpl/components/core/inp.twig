{% set props = {
	id: props.id | default,
	label: props.label | default,
	labelIcon: props.labelIcon | default,
	value: props.value | default,
	cols: props.cols | default('40'),
	rows: props.rows | default('4'),
	value: props.value | default,
	info: props.info | default,
	btn: props.btn | default,
	sufix: props.sufix | default,
	prefix: props.prefix | default,
	type: props.type | default(['text']) in ['textarea', 'select', 'text', 'datetime-local'] ? props.type|default('text'),
	options: props.options | default(['']),
	variant: props.variant | default in ['h1'] ? props.variant,
	classes: props.classes | default(['u-mb-sm']),
	classesLabel: props.classesLabel | default(['']),
	multiple: props.multiple | default(false),
	data: props.data | default(null),
	dataInp: props.dataInp | default(null),
	dataBtn: props.dataBtn | default(null),
} %}

{% set classes = [
	'inp',
	props.variant ? 'inp--' ~ props.variant,
] | merge(props.classes) | filter(i => i) | join(' ') %}

{% set classesLabel = [
	'inp-label',
] | merge(props.classesLabel) | filter(i => i) | join(' ') %}

<div class="{{ classes }}"
	{% if props.data is not null %}
		{% for key, value in props.data %}
			data-{{ key }}="{{ value }}"
		{% endfor %}
	{% endif %}>
	{% if props.label %}
		<label for="{{ props.id }}" class="{{ classesLabel }}">
			{% if props.labelIcon %}
				<span class="item-icon">
					<span class="item-icon__icon icon">
						{% include props.labelIcon %}
					</span>
					<span class="item-icon__text">
						{{ props.label|raw }}
					</span>
				</span>
			{% else %}
				{{ props.label|raw }}
			{% endif %}
		</label>
	{% endif %}
	<div class="inp-fix">
		{% if props.prefix %}
			<span class="inp-fix__prefix">
				{{ props.prefix }}
			</span>
		{% endif %}
		{% if props.type == 'textarea' %}
			<textarea
				name="{{ props.id }}"
				id="{{ props.id }}"
				class="inp-text"
				cols="{{ props.cols }}"
				rows="{{ props.rows }}"
				{% if props.dataInp is not null %}
					{% for key, value in props.dataInp %}
						data-{{ key }}="{{ value }}"
					{% endfor %}
				{% endif %}
			>{{ props.value }}</textarea>
		{% elseif props.type == 'select'  %}
			<select
				name="{{ props.id }}"
				id="{{ props.id }}"
				class="inp-select"
				{% if props.multiple %} multiple{% endif %}
				{% if props.dataInp is not null %}
					{% for key, value in props.dataInp %}
						data-{{ key }}="{{ value }}"
					{% endfor %}
				{% endif %}
			>
				{% for option in props.options %}
					<option>{{ option.text }}</option>
				{% endfor %}
			</select>
		{% else %}
			<input
				type="{{ props.type }}"
				name="{{ props.id }}"
				id="{{ props.id }}"
				class="inp-text"
				value="{{ props.value }}"
				{% if props.dataInp is not null %}
					{% for key, value in props.dataInp %}
						data-{{ key }}="{{ value }}"
					{% endfor %}
				{% endif %}
			>
		{% endif %}
		{% if props.sufix %}
			<span class="inp-fix__sufix">
				{{ props.sufix }}
			</span>
		{% endif %}
		{% if props.btn %}
			<button
				class="btn btn--grey"
				type="button"
				{% if props.dataBtn is not null %}
					{% for key, value in props.dataBtn %}
						data-{{ key }}="{{ value }}"
					{% endfor %}
				{% endif %}
			>
				<span class="btn__text">
					{{ props.btn }}
				</span>
			</button>
		{% endif %}
		<div class="inp-text__holder"></div>
	</div>
	{% if props.info %}
		<div class="inp-info">
			{{ props.info|raw }}
		</div>
	{% endif %}
</div>
