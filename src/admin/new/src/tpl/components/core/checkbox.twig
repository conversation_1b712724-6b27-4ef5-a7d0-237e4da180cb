{% set props = {
	id: props.id | default,
	label: props.label | default,
	tooltip: props.tooltip | default,
	icon: props.icon | default,
	checked: props.checked | default(false),
	variant: props.variant | default([]) in ['checkbox', 'radio'] ? props.variant : 'checkbox',
	classes: props.classes | default(['u-mb-sm']),
	data: props.data | default(null),
	dataInp: props.dataInp | default(null),
	classesInp: props.classesInp | default([])
} %}

{% set classes = [
	'inp-item',
	props.variant ? 'inp-item--' ~ props.variant,
	props.icon ? 'inp-item--icon',
	props.tooltip ? 'tooltip',
] | merge(props.classes) | filter(i => i) | join(' ') %}

{% set classesInp = [
	'inp-item__inp',
] | merge(props.classesInp) | filter(i => i) | join(' ') %}

<label
	class="{{ classes }}"
	{% if props.data is not null %}
		{% for key, value in props.data %}
			data-{{ key }}="{{ value }}"
		{% endfor %}
	{% endif %}
>
	<input
		type="checkbox"
		name="{{ props.id }}"
		value="1"
		class="{{ classesInp }}"
		{% if props.checked %} checked{% endif %}
		{% if props.dataInp is not null %}
			{% for key, value in props.dataInp %}
				data-{{ key }}="{{ value }}"
			{% endfor %}
		{% endif %}
	>
	<span class="inp-item__text">
		{% if props.icon %}
			<span class="icon">
				{% include props.icon %}
			</span>
			{% if props.tooltip %}
				<span class="tooltip__content">
					{{ props.tooltip }}
				</span>
			{% endif %}
		{% else %}
			{{ props.label|raw }}
		{% endif %}
	</span>
</label>

