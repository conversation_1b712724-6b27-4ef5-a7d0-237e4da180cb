{% set props = {
	icon: props.size | default('@icons/info-circle.svg'),
	classes: props.classes | default(['u-mb-sm']),
} %}

{% set blocks = {
	content: block('content') is defined ? block('content'),
} %}

{% set classes = [
	'message'
] | merge(props.classes) | filter(i => i) | join(' ') %}

<div class="{{ classes }}">
	<div class="message__icon icon">
		{% include props.icon %}
	</div>
	<div class="message__content">
		{{ block('content') }}
	</div>
</div>
