{% set props = {
	id: props.id | default,
	items: props.items | default([]),
	labelIcon: props.labelIcon | default,
	label: props.label | default,
	variant: props.variant | default([]) in ['inline'] ? props.variant,
	classes: props.classes | default(['u-mb-sm']),
	classesLabel: props.classesLabel | default(['']),
} %}

{% set className = [
	'inp-items'
] | merge(props.classes) | filter(i => i) | join(' ') %}

{% set classesLabel = [
	'inp-label',
] | merge(props.classesLabel) | filter(i => i) | join(' ') %}

<div class="{{ className }}">
	{% if props.label %}
		<div class="{{ classesLabel }}">
			{% if props.labelIcon %}
				<span class="item-icon">
					<span class="item-icon__icon icon">
						{% include props.labelIcon %}
					</span>
					<span class="item-icon__text">
						{{ props.label|raw }}
					</span>
				</span>
			{% else %}
				{{ props.label|raw }}
			{% endif %}
		</div>
	{% endif %}
	<ul class="inp-items__list{% if props.variant == 'inline' %} grid{% endif %}">
		{% for item in props.items %}
			<li class="inp-items__item{% if props.variant == 'inline' %} grid__cell size--auto{% endif %}">
				{% include '@components/core/checkbox.twig' with {
					props: item
				} %}
			</li>
		{% endfor %}
	</ul>
</div>
