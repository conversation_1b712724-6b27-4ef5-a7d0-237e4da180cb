{% include '@components/parts/list-item.twig' with {
	props: {
		dragdrop: true,
		inps: [
			{
				id: '[categories][newItemMarker][name]',
				placeholder: '<PERSON><PERSON><PERSON><PERSON> n<PERSON> kategorie',
			}
		],
		btnsAfter: [
			{
				icon: '@icons/trash.svg',
				tooltip: 'Odstranit',
				variant: 'remove',
				data: {
					'action': 'RemoveItem#remove'
				},
			}
		],
		data: {
			'controller': 'RemoveItem',
			'RemoveItem-target': 'item',
		},
	}
} %}
