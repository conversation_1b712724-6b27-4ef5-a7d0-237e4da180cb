{% include '@components/parts/list-item.twig' with {
	props: {
		dragdrop: true,
		texts: [
			{
				text: '<span data-ProductVariant-target="text"></span>'
			}
		],
		langs: ['CZ', 'EN'],
		btnsAfter: [
			{
				icon: '@icons/pencil-alt.svg',
				tooltip: 'Editovat',
				data: {
					'controller': 'Toggle',
					'action': 'Toggle#changeClass ProductVariant#edit',
					'toggle-target-value': '#overlay-newItemMarker',
					'toggle-target-class-value': 'is-visible'
				},
			},
			{
				icon: '@icons/trash.svg',
				tooltip: 'Odstranit',
				variant: 'remove',
				data: {
					'action': 'RemoveItem#remove ProductVariant#remove'
				},
			}
		],
		data: {
			'controller': 'ProductVariant RemoveItem',
			'action': 'ProductVariantEdit:updateValues@window->ProductVariant#updateValues',
			'ProductVariant-id-value': 'newItemMarker',
			'RemoveItem-target': 'item',
		}
	}
} %}
