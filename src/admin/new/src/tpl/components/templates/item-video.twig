{% include '@components/parts/list-item.twig' with {
	props: {
		dragdrop: true,
		inps: [
			{
				id: '[videos][newItemMarker][url]',
				placeholder: 'Url videa',
			},
			{
				id: '[videos][newItemMarker][name]',
				placeholder: '<PERSON><PERSON><PERSON><PERSON>',
			}
		],
		btnsAfter: [
			{
				icon: '@icons/trash.svg',
				tooltip: 'Odstranit',
				variant: 'remove',
				data: {
					'action': 'RemoveItem#remove'
				},
			}
		],
		data: {
			'controller': 'RemoveItem',
			'RemoveItem-target': 'item',
		},
	}
} %}
