{% include '@components/parts/list-item.twig' with {
	props: {
		dragdrop: true,
		progress: true,
		inps: [
			{
				id: '[files][newItemMarker][name]',
				placeholder: '<PERSON><PERSON><PERSON><PERSON> n<PERSON>zev souboru',
				disabled: true,
				data: {
					'file-target': 'nameInput',
				},
			}
		],
		btnsAfter: [
			{
				icon: '@icons/trash.svg',
				tooltip: 'Odstranit',
				variant: 'remove',
				data: {
					'action': 'RemoveItem#remove'
				},
			}
		],
		data: {
			'controller': 'RemoveItem File',
			'action': 'Templates:uploadFile@window->File#upload',
			'file-id-value': 'newItemMarker',
			'file-url-value': '../ajax/upload-file.json',
			'removeitem-target': 'item',
		},
	}
} %}
