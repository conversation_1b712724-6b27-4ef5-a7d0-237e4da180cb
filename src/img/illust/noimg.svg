<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 26.2.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 800 800" style="enable-background:new 0 0 800 800;" xml:space="preserve">
<style type="text/css">
	.st0{clip-path:url(#SVGID_00000113332510178698640240000000246536570636350616_);}
	.st1{fill:#F7F7F7;stroke:#E0E0E0;stroke-miterlimit:10;}
	.st2{fill:#F7F7F7;stroke:#E0E0E0;stroke-width:15;stroke-linecap:round;stroke-linejoin:round;}
	.st3{fill:#F7F7F7;stroke:#E0E0E0;stroke-width:15;stroke-linecap:round;}
	.st4{fill:#F7F7F7;stroke:#E0E0E0;stroke-width:15;}
</style>
	<g>
	<defs>
		<rect id="SVGID_1_" width="800" height="800"/>
	</defs>
		<use xlink:href="#SVGID_1_"  style="overflow:visible;fill:#F7F7F7;"/>
		<clipPath id="SVGID_00000021804184117372812630000009216204772960233629_">
		<use xlink:href="#SVGID_1_"  style="overflow:visible;"/>
	</clipPath>
		<g id="Web_1920_1" style="clip-path:url(#SVGID_00000021804184117372812630000009216204772960233629_);">
		<rect class="st1" width="800" height="800"/>
			<g id="Rectangle_1" transform="translate(144 197)">
			<rect class="st1" width="512" height="407"/>
				<rect x="7.5" y="7.5" class="st2" width="497" height="392"/>
		</g>
			<line id="Line_1" class="st3" x1="203.5" y1="542.5" x2="356.5" y2="410.5"/>
			<g id="Ellipse_1" transform="translate(237 293)">
			<circle class="st1" cx="48" cy="48" r="48"/>
				<circle class="st4" cx="48" cy="48" r="40.5"/>
		</g>
			<line id="Line_2" class="st3" x1="356.5" y1="410.5" x2="454.5" y2="508.5"/>
			<line id="Line_3" class="st3" x1="405.5" y1="457.5" x2="498.5" y2="356.5"/>
			<line id="Line_4" class="st3" x1="498.5" y1="356.5" x2="605.5" y2="453.5"/>
	</g>
</g>
</svg>
