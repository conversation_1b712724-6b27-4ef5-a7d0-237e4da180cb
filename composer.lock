{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "7d3fbda67c028e530c15a4667e8ac913", "packages": [{"name": "abraham/twitteroauth", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/abraham/twitteroauth.git", "reference": "5a424e80a1200674451844fbaae8a0098a316a01"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/abraham/twitteroauth/zipball/5a424e80a1200674451844fbaae8a0098a316a01", "reference": "5a424e80a1200674451844fbaae8a0098a316a01", "shasum": ""}, "require": {"composer/ca-bundle": "^1.2", "ext-curl": "*", "php": "^7.3 || ^7.4 || ^8.0"}, "require-dev": {"php-vcr/php-vcr": "^1", "php-vcr/phpunit-testlistener-vcr": "dev-php-8", "phpmd/phpmd": "^2", "phpunit/phpunit": "^8 || ^9", "squizlabs/php_codesniffer": "^3"}, "type": "library", "autoload": {"psr-4": {"Abraham\\TwitterOAuth\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://abrah.am", "role": "Developer"}], "description": "The most popular PHP library for use with the Twitter OAuth REST API.", "homepage": "https://twitteroauth.com", "keywords": ["Twitter API", "Twitter oAuth", "api", "o<PERSON>h", "rest", "social", "twitter"], "support": {"issues": "https://github.com/abraham/twitteroauth/issues", "source": "https://github.com/abraham/twitteroauth"}, "time": "2021-06-11T02:56:14+00:00"}, {"name": "a<PERSON><PERSON><PERSON><PERSON>/yasumi", "version": "2.7.0", "source": {"type": "git", "url": "https://github.com/azuyalabs/yasumi.git", "reference": "37d1215d4f4012d3185bb9990c76ca17a4ff1c30"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/azuyalabs/yasumi/zipball/37d1215d4f4012d3185bb9990c76ca17a4ff1c30", "reference": "37d1215d4f4012d3185bb9990c76ca17a4ff1c30", "shasum": ""}, "require": {"ext-json": "*", "php": ">=8.0"}, "require-dev": {"ext-intl": "*", "friendsofphp/php-cs-fixer": "^2.19 || ^3.40", "mikey179/vfsstream": "^1.6", "phan/phan": "^5.4", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^8.5 || ^9.6", "vimeo/psalm": "^5.16"}, "suggest": {"ext-calendar": "For calculating the date of Easter"}, "type": "library", "autoload": {"psr-4": {"Yasumi\\": "src/<PERSON><PERSON><PERSON>/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Sacha <PERSON>", "email": "<EMAIL>", "role": "Maintainer"}], "description": "The easy PHP Library for calculating holidays.", "homepage": "https://www.yasumi.dev", "keywords": ["Bank", "calculation", "calendar", "celebration", "date", "holiday", "holidays", "national", "time"], "support": {"docs": "https://www.yasumi.dev", "issues": "https://github.com/azuyalabs/yasumi/issues", "source": "https://github.com/azuyalabs/yasumi"}, "funding": [{"url": "https://www.buymeacoffee.com/sachatelgenhof", "type": "other"}], "time": "2024-01-07T14:12:44+00:00"}, {"name": "bacon/bacon-qr-code", "version": "v3.0.1", "source": {"type": "git", "url": "https://github.com/Bacon/BaconQrCode.git", "reference": "f9cc1f52b5a463062251d666761178dbdb6b544f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Bacon/BaconQrCode/zipball/f9cc1f52b5a463062251d666761178dbdb6b544f", "reference": "f9cc1f52b5a463062251d666761178dbdb6b544f", "shasum": ""}, "require": {"dasprid/enum": "^1.0.3", "ext-iconv": "*", "php": "^8.1"}, "require-dev": {"phly/keep-a-changelog": "^2.12", "phpunit/phpunit": "^10.5.11 || 11.0.4", "spatie/phpunit-snapshot-assertions": "^5.1.5", "squizlabs/php_codesniffer": "^3.9"}, "suggest": {"ext-imagick": "to generate QR code images"}, "type": "library", "autoload": {"psr-4": {"BaconQrCode\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON> 'DASPRiD'", "email": "<EMAIL>", "homepage": "https://dasprids.de/", "role": "Developer"}], "description": "BaconQrCode is a QR code generator for PHP.", "homepage": "https://github.com/Bacon/BaconQrCode", "support": {"issues": "https://github.com/Bacon/BaconQrCode/issues", "source": "https://github.com/Bacon/BaconQrCode/tree/v3.0.1"}, "time": "2024-10-01T13:55:55+00:00"}, {"name": "brick/math", "version": "0.11.0", "source": {"type": "git", "url": "https://github.com/brick/math.git", "reference": "0ad82ce168c82ba30d1c01ec86116ab52f589478"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brick/math/zipball/0ad82ce168c82ba30d1c01ec86116ab52f589478", "reference": "0ad82ce168c82ba30d1c01ec86116ab52f589478", "shasum": ""}, "require": {"php": "^8.0"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^9.0", "vimeo/psalm": "5.0.0"}, "type": "library", "autoload": {"psr-4": {"Brick\\Math\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Arbitrary-precision arithmetic library", "keywords": ["Arbitrary-precision", "BigInteger", "BigRational", "arithmetic", "bigdecimal", "bignum", "brick", "math"], "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.11.0"}, "funding": [{"url": "https://github.com/BenMorel", "type": "github"}], "time": "2023-01-15T23:15:59+00:00"}, {"name": "brick/money", "version": "0.8.1", "source": {"type": "git", "url": "https://github.com/brick/money.git", "reference": "25f484a347756b7f3fbe7ad63ed9ad2d87b20004"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brick/money/zipball/25f484a347756b7f3fbe7ad63ed9ad2d87b20004", "reference": "25f484a347756b7f3fbe7ad63ed9ad2d87b20004", "shasum": ""}, "require": {"brick/math": "~0.10.1 || ~0.11.0", "ext-json": "*", "php": "^8.0"}, "require-dev": {"brick/varexporter": "~0.3.0", "ext-dom": "*", "ext-pdo": "*", "php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^9.4.3", "vimeo/psalm": "5.14.1"}, "suggest": {"ext-intl": "Required to format Money objects"}, "type": "library", "autoload": {"psr-4": {"Brick\\Money\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Money and currency library", "keywords": ["brick", "currency", "money"], "support": {"issues": "https://github.com/brick/money/issues", "source": "https://github.com/brick/money/tree/0.8.1"}, "funding": [{"url": "https://github.com/BenMorel", "type": "github"}], "time": "2023-09-23T21:17:11+00:00"}, {"name": "comgate/sdk", "version": "v1.2.0", "source": {"type": "git", "url": "https://github.com/comgate-payments/sdk-php.git", "reference": "db3008f760feaa9f89daaeaf0186ed2f51a0f062"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/comgate-payments/sdk-php/zipball/db3008f760feaa9f89daaeaf0186ed2f51a0f062", "reference": "db3008f760feaa9f89daaeaf0186ed2f51a0f062", "shasum": ""}, "require": {"php": "^7.3 || ^8.0", "psr/http-message": "^1.0.1|^2", "psr/log": "^1.1|^2|^3"}, "require-dev": {"codeception/codeception": "^4.2 || ^5.0", "codeception/module-asserts": "^1.3 || ^2.0 || ^3.0", "codeception/module-phpbrowser": "^1.0 || ^2.0", "codeception/stub": "^3.0 || ^4.0", "friendsofphp/php-cs-fixer": "^3.0", "mockery/mockery": "^1.5", "phpstan/phpstan": "^1.11", "phpstan/phpstan-deprecation-rules": "^1.1", "phpstan/phpstan-strict-rules": "^1.5", "squizlabs/php_codesniffer": "^3.7", "vlucas/phpdotenv": "^5.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.2.x-dev"}}, "autoload": {"psr-4": {"Comgate\\SDK\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Comgate PHP SDK", "homepage": "https://github.com/comgate/sdk-php", "keywords": ["api", "comgate", "money", "payment", "php"], "support": {"issues": "https://github.com/comgate-payments/sdk-php/issues", "source": "https://github.com/comgate-payments/sdk-php/tree/v1.2.0"}, "time": "2024-12-04T06:43:13+00:00"}, {"name": "composer/ca-bundle", "version": "1.5.5", "source": {"type": "git", "url": "https://github.com/composer/ca-bundle.git", "reference": "08c50d5ec4c6ced7d0271d2862dec8c1033283e6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/ca-bundle/zipball/08c50d5ec4c6ced7d0271d2862dec8c1033283e6", "reference": "08c50d5ec4c6ced7d0271d2862dec8c1033283e6", "shasum": ""}, "require": {"ext-openssl": "*", "ext-pcre": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.10", "phpunit/phpunit": "^8 || ^9", "psr/log": "^1.0 || ^2.0 || ^3.0", "symfony/process": "^4.0 || ^5.0 || ^6.0 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\CaBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Lets you find a path to the system CA bundle, and includes a fallback to the Mozilla CA bundle.", "keywords": ["cabundle", "cacert", "certificate", "ssl", "tls"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/ca-bundle/issues", "source": "https://github.com/composer/ca-bundle/tree/1.5.5"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2025-01-08T16:17:16+00:00"}, {"name": "contributte/apitte", "version": "v0.12.3", "source": {"type": "git", "url": "https://github.com/contributte/apitte.git", "reference": "f6f7d56265003cf9484051035f5647bf7b9ef977"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/apitte/zipball/f6f7d56265003cf9484051035f5647bf7b9ef977", "reference": "f6f7d56265003cf9484051035f5647bf7b9ef977", "shasum": ""}, "require": {"contributte/middlewares": "^0.11.0 || ^0.12.0", "contributte/openapi": "^0.1.0", "contributte/psr7-http-message": "^0.9.0 || ^0.10.0", "doctrine/annotations": "^1.14.3 || ^2.0.0", "ext-json": "*", "koriym/attributes": "^1.0.5", "nette/di": "^3.1.8", "nette/utils": "^4.0.0", "php": ">=8.1"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"contributte/dev": "^0.4", "contributte/phpstan": "^0.1", "contributte/qa": "^0.4", "contributte/tester": "^0.3", "mockery/mockery": "^1.6.6", "nette/application": "^3.1.4", "nette/di": "^3.1.8", "nette/http": "^3.2.3", "psr/log": "^2.0.0 || ^3.0.0", "symfony/console": "^6.4.0 || ^7.0.0", "symfony/translation": "^6.4.0 | ^7.0.0", "symfony/validator": "^6.4.0 || ^7.0.0", "symfony/yaml": "^6.4.0 || ^7.0.0", "tracy/tracy": "^2.10.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.13.x-dev"}}, "autoload": {"psr-4": {"Apitte\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "An opinionated and enjoyable API framework based on Nette Framework. Supporting content negotiation, debugging, middlewares, attributes, annotations and loving openapi/swagger.", "homepage": "https://github.com/contributte/apitte", "keywords": ["annotation", "api", "apitte", "http", "nette", "rest"], "support": {"issues": "https://github.com/contributte/apitte/issues", "source": "https://github.com/contributte/apitte/tree/v0.12.3"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2024-05-24T17:07:04+00:00"}, {"name": "contributte/application", "version": "v0.5.2", "source": {"type": "git", "url": "https://github.com/contributte/application.git", "reference": "f5f8637bd54eacd1cc45792f23bb8831873ddb35"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/application/zipball/f5f8637bd54eacd1cc45792f23bb8831873ddb35", "reference": "f5f8637bd54eacd1cc45792f23bb8831873ddb35", "shasum": ""}, "require": {"nette/application": "^3.0.0 || ^4.0", "php": ">=7.2"}, "require-dev": {"nette/http": "~2.4.8 || ^3.0.0", "ninjify/nunjuck": "^0.3.0", "ninjify/qa": "^0.13.0", "phpstan/phpstan": "^1.0", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-nette": "^1.0", "phpstan/phpstan-strict-rules": "^1.0", "psr/http-message": "~1.0.1", "tracy/tracy": "~2.9.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.6.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Application\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Extra contrib to nette/application", "homepage": "https://github.com/contributte/application", "keywords": ["application", "component", "control", "nette", "presenter"], "support": {"issues": "https://github.com/contributte/application/issues", "source": "https://github.com/contributte/application/tree/v0.5.2"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2023-09-27T20:32:37+00:00"}, {"name": "contributte/console", "version": "v0.10.1", "source": {"type": "git", "url": "https://github.com/contributte/console.git", "reference": "dc2b84fb8dd795ea9988f396311aeed435aed495"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/console/zipball/dc2b84fb8dd795ea9988f396311aeed435aed495", "reference": "dc2b84fb8dd795ea9988f396311aeed435aed495", "shasum": ""}, "require": {"nette/di": "^3.1.8", "php": ">=8.1", "symfony/console": "^6.4.2 || ^7.0.2"}, "require-dev": {"contributte/phpstan": "^0.1", "contributte/qa": "^0.4", "contributte/tester": "^0.4", "mockery/mockery": "^1.6.7", "nette/http": "^3.2.3", "symfony/event-dispatcher": "^6.4.2 || ^7.0.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.11.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Console\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Best Symfony Console for Nette Framework", "homepage": "https://github.com/contributte/console", "keywords": ["console", "nette", "symfony"], "support": {"issues": "https://github.com/contributte/console/issues", "source": "https://github.com/contributte/console/tree/v0.10.1"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2024-01-04T20:10:58+00:00"}, {"name": "contributte/console-extra", "version": "v0.8.0", "source": {"type": "git", "url": "https://github.com/contributte/console-extra.git", "reference": "09e14dcde8676828976c4c86bd912c8a67835975"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/console-extra/zipball/09e14dcde8676828976c4c86bd912c8a67835975", "reference": "09e14dcde8676828976c4c86bd912c8a67835975", "shasum": ""}, "require": {"nette/di": "^3.1.8", "php": ">=8.1", "symfony/console": "^6.4.2 || ^7.0.2"}, "conflict": {"nette/schema": "<1.0.1"}, "require-dev": {"contributte/console": "~0.10", "contributte/phpstan": "^0.1", "contributte/qa": "^0.4", "contributte/tester": "^0.4", "latte/latte": "^3.0.12", "mockery/mockery": "^1.5.0", "nette/application": "^3.1.14", "nette/bootstrap": "^3.2.1", "nette/caching": "^3.2.3", "nette/security": "^3.1.8"}, "suggest": {"contributte/console": "Symfony\\Console for Nette"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.9.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Console\\Extra\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Nette-based console commands for latte, DIC, security, utils and many others", "homepage": "https://github.com/contributte/console-extra", "keywords": ["console", "nette", "symfony"], "support": {"issues": "https://github.com/contributte/console-extra/issues", "source": "https://github.com/contributte/console-extra/tree/v0.8.0"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2024-01-04T12:17:40+00:00"}, {"name": "contributte/di", "version": "v0.5.6", "source": {"type": "git", "url": "https://github.com/contributte/di.git", "reference": "49d6b93d46f57be319b1e811cd983bfed0c90979"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/di/zipball/49d6b93d46f57be319b1e811cd983bfed0c90979", "reference": "49d6b93d46f57be319b1e811cd983bfed0c90979", "shasum": ""}, "require": {"nette/di": "^3.1.0", "nette/utils": "^3.2.8 || ^4.0", "php": ">=7.2"}, "conflict": {"nette/schema": "<1.1.0"}, "require-dev": {"nette/bootstrap": "^3.1.4", "nette/robot-loader": "^3.4.2 || ^4.0", "ninjify/nunjuck": "^0.4", "ninjify/qa": "^0.13", "phpstan/phpstan": "^1.9.11", "phpstan/phpstan-deprecation-rules": "^1.1.1", "phpstan/phpstan-nette": "^1.2.0", "phpstan/phpstan-strict-rules": "^1.4.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.6.x-dev"}}, "autoload": {"psr-4": {"Contributte\\DI\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Extra contrib to nette/di", "homepage": "https://github.com/contributte/di", "keywords": ["dependency", "inject", "nette"], "support": {"issues": "https://github.com/contributte/di/issues", "source": "https://github.com/contributte/di/tree/v0.5.6"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2023-09-05T08:23:55+00:00"}, {"name": "contributte/elastica", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/contributte/elastica.git", "reference": "49ff2e875043453813438da7c0763acefd4bfe19"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/elastica/zipball/49ff2e875043453813438da7c0763acefd4bfe19", "reference": "49ff2e875043453813438da7c0763acefd4bfe19", "shasum": ""}, "require": {"nette/di": "^3.1.8", "nette/utils": "^4.0.3", "php": ">=8.1", "ruflin/elastica": "^7.3.1"}, "conflict": {"nette/schema": "<1.2.0"}, "require-dev": {"contributte/phpstan": "^0.1", "contributte/qa": "^0.4", "contributte/tester": "^0.4", "mockery/mockery": "^1.6.6", "nette/bootstrap": "^3.2.1", "nette/http": "^3.2.3", "tracy/tracy": "^2.10.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1.0-dev"}}, "autoload": {"psr-4": {"Contributte\\Elastica\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Elastica implementation for Nette Framework", "homepage": "https://github.com/contributte/elastica", "keywords": ["elastic", "elastica", "elasticsearch", "es", "nette", "search"], "support": {"issues": "https://github.com/contributte/elastica/issues", "source": "https://github.com/contributte/elastica/tree/v2.0.1"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2023-11-28T19:48:56+00:00"}, {"name": "contributte/event-dispatcher", "version": "v0.9.1", "source": {"type": "git", "url": "https://github.com/contributte/event-dispatcher.git", "reference": "00c44782f8e4c16c8c97859d83d42e7e6bdf063e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/event-dispatcher/zipball/00c44782f8e4c16c8c97859d83d42e7e6bdf063e", "reference": "00c44782f8e4c16c8c97859d83d42e7e6bdf063e", "shasum": ""}, "require": {"nette/di": "^3.1.8", "php": ">=8.1", "symfony/event-dispatcher": "^6.4.3 || ^7.0.3"}, "require-dev": {"contributte/phpstan": "^0.1", "contributte/qa": "^0.4", "contributte/tester": "^0.3", "mockery/mockery": "^1.5.0", "psr/log": "^2.0.0 || ^3.0.0", "tracy/tracy": "^2.10.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.10.x-dev"}}, "autoload": {"psr-4": {"Contributte\\EventDispatcher\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Best event dispatcher / event manager / event emitter for Nette Framework", "homepage": "https://github.com/contributte/event-dispatcher", "keywords": ["dispatcher", "emitter", "event", "nette", "symfony"], "support": {"issues": "https://github.com/contributte/event-dispatcher/issues", "source": "https://github.com/contributte/event-dispatcher/tree/v0.9.1"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2024-02-05T16:00:14+00:00"}, {"name": "contributte/event-dispatcher-extra", "version": "v0.10.1", "source": {"type": "git", "url": "https://github.com/contributte/event-dispatcher-extra.git", "reference": "a4180b3683082b1840478a28717ff09611d1ff31"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/event-dispatcher-extra/zipball/a4180b3683082b1840478a28717ff09611d1ff31", "reference": "a4180b3683082b1840478a28717ff09611d1ff31", "shasum": ""}, "require": {"nette/di": "^3.1.9", "php": ">=8.1", "symfony/event-dispatcher": "^6.4.3 || ^7.0.3"}, "conflict": {"latte/latte": "<3.0.13"}, "require-dev": {"contributte/event-dispatcher": "^0.9.0", "contributte/phpstan": "^0.1", "contributte/qa": "^0.4", "contributte/tester": "^0.3", "latte/latte": "^3.0.13", "nette/application": "^3.1.10", "nette/http": "^3.2.1", "nette/security": "^3.1.7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.11.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Events\\Extra\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Nette-based Symfony events for application, presenter, form, latte, templates, security and many others.", "homepage": "https://github.com/contributte/event-dispatcher-extra", "keywords": ["Bridge", "application", "dispatcher", "event", "form", "latte", "nette", "security", "template"], "support": {"issues": "https://github.com/contributte/event-dispatcher-extra/issues", "source": "https://github.com/contributte/event-dispatcher-extra/tree/v0.10.1"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2024-02-05T19:02:34+00:00"}, {"name": "contributte/logging", "version": "v0.6.3", "source": {"type": "git", "url": "https://github.com/contributte/logging.git", "reference": "2cc959bcfbd05cf2946b6711432d14fc0deed418"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/logging/zipball/2cc959bcfbd05cf2946b6711432d14fc0deed418", "reference": "2cc959bcfbd05cf2946b6711432d14fc0deed418", "shasum": ""}, "require": {"php": ">=7.2", "tracy/tracy": "~2.5.5|~2.6.2|~2.7.0|~2.8.0|~2.9.0|~2.10.0"}, "conflict": {"nette/di": "<3.0"}, "require-dev": {"ext-json": "*", "nette/di": "^3.0.0", "ninjify/nunjuck": "^0.4", "ninjify/qa": "^0.12", "phpstan/phpstan": "^1.0", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-nette": "^1.0", "phpstan/phpstan-strict-rules": "^1.0", "sentry/sdk": "^3.0.0"}, "suggest": {"nette/di": "to use TracyLoggingExtension", "sentry/sdk": "to use SentryLoggingExtension"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.6.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Logging\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Plug-in support logging for Tracy / Nette Framework", "homepage": "https://github.com/contributte/logging", "keywords": ["logging", "monolog", "nette", "plugins", "tracy"], "support": {"issues": "https://github.com/contributte/logging/issues", "source": "https://github.com/contributte/logging/tree/v0.6.3"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2023-04-03T15:20:33+00:00"}, {"name": "contributte/messenger", "version": "v0.1", "source": {"type": "git", "url": "https://github.com/contributte/messenger.git", "reference": "f19020bb1e12cbb1d31ceff82ec86c5fe2b47ac7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/messenger/zipball/f19020bb1e12cbb1d31ceff82ec86c5fe2b47ac7", "reference": "f19020bb1e12cbb1d31ceff82ec86c5fe2b47ac7", "shasum": ""}, "require": {"ext-json": "*", "nette/di": "^3.1.2", "php": ">=8.0", "psr/cache": "^3.0.0", "psr/container": "^2.0.2", "psr/log": "^3.0.0", "symfony/console": "^6.0.19 || ^6.2.10", "symfony/event-dispatcher": "^6.0.19 || ^6.2.8", "symfony/messenger": "^6.0.19 || ^6.2.8", "symfony/var-dumper": "^6.0.19 || ^6.2.10"}, "require-dev": {"contributte/console": "^0.10.0", "contributte/event-dispatcher": "^0.9.0", "contributte/phpstan": "^0.1.0", "contributte/qa": "^0.4.0", "contributte/tester": "^0.3.0", "mockery/mockery": "^1.3.3", "psr/container": "^2.0.2", "symfony/amqp-messenger": "^6.0.19 || ^6.2.8", "symfony/doctrine-messenger": "^6.0.19 || ^6.2.10", "symfony/redis-messenger": "^6.0.19 || ^6.2.10", "tracy/tracy": "^2.10.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.1.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Messenger\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Best Symfony Messenger for Nette framework", "homepage": "https://github.com/contributte/messenger", "keywords": ["<PERSON>", "async", "consumer", "nette", "publisher", "symfony"], "support": {"issues": "https://github.com/contributte/messenger/issues", "source": "https://github.com/contributte/messenger/tree/v0.1"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2023-07-31T14:31:59+00:00"}, {"name": "contributte/middlewares", "version": "v0.12.1", "source": {"type": "git", "url": "https://github.com/contributte/middlewares.git", "reference": "0b6c80bf0601d9a4788b24178e34014e51596e13"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/middlewares/zipball/0b6c80bf0601d9a4788b24178e34014e51596e13", "reference": "0b6c80bf0601d9a4788b24178e34014e51596e13", "shasum": ""}, "require": {"contributte/psr7-http-message": "^0.9.0 || ^0.10.0", "nette/di": "^3.1.8", "php": ">=8.1"}, "conflict": {"nette/application": "<3.2.0"}, "require-dev": {"contributte/phpstan": "^0.1", "contributte/qa": "^0.4", "contributte/tester": "^0.4", "nette/application": "^3.2.0", "nette/http": "^3.2.3", "psr/log": "^2.0|^3.0", "tracy/tracy": "^2.10.5"}, "suggest": {"nette/application": "to use PresenterMiddleware", "nette/http": "to use NetteMiddlewareExtension & NetteMiddlewareApplication", "tracy/tracy": "to use TracyMiddleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.13.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Middlewares\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Middleware / Relay / PSR-7 support to Nette Framework", "homepage": "https://github.com/contributte/middlewares", "keywords": ["<PERSON><PERSON>", "middleware", "nette"], "support": {"issues": "https://github.com/contributte/middlewares/issues", "source": "https://github.com/contributte/middlewares/tree/v0.12.1"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2024-02-23T15:28:39+00:00"}, {"name": "contributte/monolog", "version": "v0.5.2", "source": {"type": "git", "url": "https://github.com/contributte/monolog.git", "reference": "85511b300e5c6ac12b25bf09f5ece16de2fd2c18"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/monolog/zipball/85511b300e5c6ac12b25bf09f5ece16de2fd2c18", "reference": "85511b300e5c6ac12b25bf09f5ece16de2fd2c18", "shasum": ""}, "require": {"contributte/di": "^0.5.3", "monolog/monolog": "^2.0.0 || ^3.0.0", "nette/utils": "^3.0.0 || ^4.0.0", "php": ">=7.2"}, "require-dev": {"ninjify/nunjuck": "^0.4", "ninjify/qa": "^v0.13.0", "phpstan/phpstan": "^1.8.11", "phpstan/phpstan-deprecation-rules": "^1.0.0", "phpstan/phpstan-nette": "^1.1.0", "phpstan/phpstan-strict-rules": "^1.4.4", "tracy/tracy": "^v2.9.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.6.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Monolog\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Monolog integration into Nette Framework", "homepage": "https://github.com/contributte/monolog", "keywords": ["logging", "monolog", "nette"], "support": {"issues": "https://github.com/contributte/monolog/issues", "source": "https://github.com/contributte/monolog/tree/v0.5.2"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2023-07-21T08:28:12+00:00"}, {"name": "contributte/openapi", "version": "v0.1.0", "source": {"type": "git", "url": "https://github.com/contributte/openapi.git", "reference": "a918d6235f7466c4bc6335ee9397613b7797f1e6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/openapi/zipball/a918d6235f7466c4bc6335ee9397613b7797f1e6", "reference": "a918d6235f7466c4bc6335ee9397613b7797f1e6", "shasum": ""}, "require": {"nette/utils": "^4.0.0", "php": ">=8.1"}, "require-dev": {"contributte/phpstan": "^0.1", "contributte/qa": "^0.4", "contributte/tester": "^0.3", "symfony/yaml": "^6.4.0 || ^7.0.0", "tracy/tracy": "^2.10.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.1.x-dev"}}, "autoload": {"psr-4": {"Contributte\\OpenApi\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Pure OpenAPI implementation for your API applications. Great for documentation.", "homepage": "https://github.com/contributte/openapi", "keywords": ["api", "http", "nette", "openapi", "rest", "swagger"], "support": {"issues": "https://github.com/contributte/openapi/issues", "source": "https://github.com/contributte/openapi/tree/v0.1.0"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2023-12-01T18:20:22+00:00"}, {"name": "contributte/psr7-http-message", "version": "v0.10.0", "source": {"type": "git", "url": "https://github.com/contributte/psr7-http-message.git", "reference": "d8d59f429dec7c7eaf093173077a098b004fd1d5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/psr7-http-message/zipball/d8d59f429dec7c7eaf093173077a098b004fd1d5", "reference": "d8d59f429dec7c7eaf093173077a098b004fd1d5", "shasum": ""}, "require": {"guzzlehttp/psr7": "^2.6.1", "nette/utils": "^v4.0.2", "php": ">=8.1"}, "conflict": {"nette/di": "<3.0.7", "nette/http": "<3.0.5"}, "require-dev": {"contributte/phpstan": "^0.1", "contributte/qa": "^0.3.1", "contributte/tester": "^0.2.0", "nette/application": "^v3.1.14", "nette/di": "^3.1.8", "nette/http": "^3.2.3"}, "suggest": {"nette/application": "to use $request->withApplicationRequest[Nette\\Application\\Request]", "nette/di": "to use Psr7HttpExtension[CompilerExtension]", "nette/http": "to use $request->withHttpRequest[Nette\\Http\\Request], to use Psr7RequestFactory, to use Psr7ServerRequestFactory"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.10.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Psr7\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "PSR-7 (HTTP Message Interface) to Nette Framework", "homepage": "https://github.com/contributte/psr7-http-message", "keywords": ["http", "message", "nette", "psr7", "request", "response"], "support": {"issues": "https://github.com/contributte/psr7-http-message/issues", "source": "https://github.com/contributte/psr7-http-message/tree/v0.10.0"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2023-12-15T15:54:41+00:00"}, {"name": "contributte/redis", "version": "v0.5.4", "source": {"type": "git", "url": "https://github.com/contributte/redis.git", "reference": "a754af3f520ba1ac801aaa6267053ddc1e99520e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/redis/zipball/a754af3f520ba1ac801aaa6267053ddc1e99520e", "reference": "a754af3f520ba1ac801aaa6267053ddc1e99520e", "shasum": ""}, "require": {"ext-json": "*", "nette/di": "^2.4.17 || ^3.0.1", "php": ">=7.2", "predis/predis": "^1.1.6 || ^2.0.0"}, "require-dev": {"mockery/mockery": "^1.3.3", "nette/caching": "^2.5.0 || ^3.1.3", "nette/http": "^2.4.0 || ^3.0.1", "ninjify/nunjuck": "^0.4", "ninjify/qa": "^0.12", "phpstan/phpstan": "^1.0", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-nette": "^1.0", "phpstan/phpstan-strict-rules": "^1.0", "tracy/tracy": "^2.7.0"}, "suggest": {"ext-igbinary": "For better serialization"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.6.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Redis\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Redis client integration into Nette framework", "homepage": "https://github.com/contributte/redis", "keywords": ["cache", "nette", "predis", "redis"], "support": {"issues": "https://github.com/contributte/redis/issues", "source": "https://github.com/contributte/redis/tree/v0.5.4"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2023-07-27T10:08:54+00:00"}, {"name": "contributte/validator", "version": "v1.1.1", "source": {"type": "git", "url": "https://github.com/contributte/validator.git", "reference": "57739f9e033e0f5d55412c6bbeb63850e504db99"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/validator/zipball/57739f9e033e0f5d55412c6bbeb63850e504db99", "reference": "57739f9e033e0f5d55412c6bbeb63850e504db99", "shasum": ""}, "require": {"nette/di": "^3.0.1", "php": ">=7.4", "symfony/cache": "^5.0 || ^6.0", "symfony/config": "^5.0 || ^6.0", "symfony/validator": "^5.2 || ^6.0"}, "require-dev": {"doctrine/annotations": "^1.8", "doctrine/cache": "^1.10", "nette/bootstrap": "^3.0", "nette/tester": "^2.4", "ninjify/nunjuck": "^0.3.0", "ninjify/qa": "^0.12", "phpstan/phpstan": "^1.0", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-nette": "^1.0", "phpstan/phpstan-strict-rules": "^1.0", "symfony/translation": "^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"Contributte\\Validator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://jiripudil.cz"}], "description": "Symfony/Validator integration for Nette Framework.", "homepage": "https://github.com/contributte/validator", "keywords": ["nette", "symfony", "validator"], "support": {"issues": "https://github.com/contributte/validator/issues", "source": "https://github.com/contributte/validator/tree/v1.1.1"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2022-05-24T12:34:07+00:00"}, {"name": "cweagans/composer-patches", "version": "1.7.3", "source": {"type": "git", "url": "https://github.com/cweagans/composer-patches.git", "reference": "e190d4466fe2b103a55467dfa83fc2fecfcaf2db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cweagans/composer-patches/zipball/e190d4466fe2b103a55467dfa83fc2fecfcaf2db", "reference": "e190d4466fe2b103a55467dfa83fc2fecfcaf2db", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=5.3.0"}, "require-dev": {"composer/composer": "~1.0 || ~2.0", "phpunit/phpunit": "~4.6"}, "type": "composer-plugin", "extra": {"class": "cweagans\\Composer\\Patches"}, "autoload": {"psr-4": {"cweagans\\Composer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a way to patch Composer packages.", "support": {"issues": "https://github.com/cweagans/composer-patches/issues", "source": "https://github.com/cweagans/composer-patches/tree/1.7.3"}, "time": "2022-12-20T22:53:13+00:00"}, {"name": "dasprid/enum", "version": "1.0.6", "source": {"type": "git", "url": "https://github.com/DASPRiD/Enum.git", "reference": "8dfd07c6d2cf31c8da90c53b83c026c7696dda90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/DASPRiD/Enum/zipball/8dfd07c6d2cf31c8da90c53b83c026c7696dda90", "reference": "8dfd07c6d2cf31c8da90c53b83c026c7696dda90", "shasum": ""}, "require": {"php": ">=7.1 <9.0"}, "require-dev": {"phpunit/phpunit": "^7 || ^8 || ^9 || ^10 || ^11", "squizlabs/php_codesniffer": "*"}, "type": "library", "autoload": {"psr-4": {"DASPRiD\\Enum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON> 'DASPRiD'", "email": "<EMAIL>", "homepage": "https://dasprids.de/", "role": "Developer"}], "description": "PHP 7.1 enum implementation", "keywords": ["enum", "map"], "support": {"issues": "https://github.com/DASPRiD/Enum/issues", "source": "https://github.com/DASPRiD/Enum/tree/1.0.6"}, "time": "2024-08-09T14:30:48+00:00"}, {"name": "doctrine/annotations", "version": "1.14.4", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "253dca476f70808a5aeed3a47cc2cc88c5cab915"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/253dca476f70808a5aeed3a47cc2cc88c5cab915", "reference": "253dca476f70808a5aeed3a47cc2cc88c5cab915", "shasum": ""}, "require": {"doctrine/lexer": "^1 || ^2", "ext-tokenizer": "*", "php": "^7.1 || ^8.0", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^1.11 || ^2.0", "doctrine/coding-standard": "^9 || ^12", "phpstan/phpstan": "~1.4.10 || ^1.10.28", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "symfony/cache": "^4.4 || ^5.4 || ^6.4 || ^7", "vimeo/psalm": "^4.30 || ^5.14"}, "suggest": {"php": "PHP 8.0 or higher comes with attributes, a native replacement for annotations"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.14.4"}, "time": "2024-09-05T10:15:52+00:00"}, {"name": "doctrine/cache", "version": "1.13.0", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "56cd022adb5514472cb144c087393c1821911d09"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/56cd022adb5514472cb144c087393c1821911d09", "reference": "56cd022adb5514472cb144c087393c1821911d09", "shasum": ""}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "cache/integration-tests": "dev-master", "doctrine/coding-standard": "^9", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "predis/predis": "~1.0", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/cache": "^4.4 || ^5.4 || ^6", "symfony/var-exporter": "^4.4 || ^5.4 || ^6"}, "suggest": {"alcaeus/mongo-php-adapter": "Required to use legacy MongoDB driver"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/1.13.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2022-05-20T20:06:54+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "31610dbb31faa98e6b5447b62340826f54fbc4e9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/31610dbb31faa98e6b5447b62340826f54fbc4e9", "reference": "31610dbb31faa98e6b5447b62340826f54fbc4e9", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12", "phpstan/phpstan": "1.4.10 || 2.0.3", "phpstan/phpstan-phpunit": "^1.0 || ^2", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psr/log": "^1 || ^2 || ^3"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.4"}, "time": "2024-12-07T21:18:45+00:00"}, {"name": "doctrine/lexer", "version": "2.1.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6", "reference": "861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^4.11 || ^5.21"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/2.1.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2024-02-05T11:35:39+00:00"}, {"name": "dragonmantank/cron-expression", "version": "v3.4.0", "source": {"type": "git", "url": "https://github.com/dragonmantank/cron-expression.git", "reference": "8c784d071debd117328803d86b2097615b457500"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/8c784d071debd117328803d86b2097615b457500", "reference": "8c784d071debd117328803d86b2097615b457500", "shasum": ""}, "require": {"php": "^7.2|^8.0", "webmozart/assert": "^1.0"}, "replace": {"mtdowling/cron-expression": "^1.0"}, "require-dev": {"phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.0", "phpunit/phpunit": "^7.0|^8.0|^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Cron\\": "src/Cron/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/dragonmantank"}], "description": "CRON for PHP: Calculate the next or previous run date and determine if a CRON expression is due", "keywords": ["cron", "schedule"], "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/v3.4.0"}, "funding": [{"url": "https://github.com/dragonmantank", "type": "github"}], "time": "2024-10-09T13:47:03+00:00"}, {"name": "elasticsearch/elasticsearch", "version": "v7.17.2", "source": {"type": "git", "url": "https://github.com/elastic/elasticsearch-php.git", "reference": "2d302233f2bb0926812d82823bb820d405e130fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/elastic/elasticsearch-php/zipball/2d302233f2bb0926812d82823bb820d405e130fc", "reference": "2d302233f2bb0926812d82823bb820d405e130fc", "shasum": ""}, "require": {"ext-json": ">=1.3.7", "ezimuel/ringphp": "^1.1.2", "php": "^7.3 || ^8.0", "psr/log": "^1|^2|^3"}, "require-dev": {"ext-yaml": "*", "ext-zip": "*", "mockery/mockery": "^1.2", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^9.3", "squizlabs/php_codesniffer": "^3.4", "symfony/finder": "~4.0"}, "suggest": {"ext-curl": "*", "monolog/monolog": "Allows for client-level logging and tracing"}, "type": "library", "autoload": {"files": ["src/autoload.php"], "psr-4": {"Elasticsearch\\": "src/Elasticsearch/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0", "LGPL-2.1-only"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "description": "PHP Client for Elasticsearch", "keywords": ["client", "elasticsearch", "search"], "support": {"issues": "https://github.com/elastic/elasticsearch-php/issues", "source": "https://github.com/elastic/elasticsearch-php/tree/v7.17.2"}, "time": "2023-04-21T15:31:12+00:00"}, {"name": "endroid/qr-code", "version": "6.0.3", "source": {"type": "git", "url": "https://github.com/endroid/qr-code.git", "reference": "bdbb06e767efe9abe3c00461662b4059a6cd0b55"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/endroid/qr-code/zipball/bdbb06e767efe9abe3c00461662b4059a6cd0b55", "reference": "bdbb06e767efe9abe3c00461662b4059a6cd0b55", "shasum": ""}, "require": {"bacon/bacon-qr-code": "^3.0", "php": "^8.2"}, "require-dev": {"endroid/quality": "dev-main", "ext-gd": "*", "khanamiryan/qrcode-detector-decoder": "^2.0.2", "setasign/fpdf": "^1.8.2"}, "suggest": {"ext-gd": "Enables you to write PNG images", "khanamiryan/qrcode-detector-decoder": "Enables you to use the image validator", "roave/security-advisories": "Makes sure package versions with known security issues are not installed", "setasign/fpdf": "Enables you to use the PDF writer"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.x-dev"}}, "autoload": {"psr-4": {"Endroid\\QrCode\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Endroid QR Code", "homepage": "https://github.com/endroid/qr-code", "keywords": ["code", "endroid", "php", "qr", "qrcode"], "support": {"issues": "https://github.com/endroid/qr-code/issues", "source": "https://github.com/endroid/qr-code/tree/6.0.3"}, "funding": [{"url": "https://github.com/endroid", "type": "github"}], "time": "2024-10-29T19:28:52+00:00"}, {"name": "ezimuel/guzzlestreams", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/ezimuel/guzzlestreams.git", "reference": "b4b5a025dfee70d6cd34c780e07330eb93d5b997"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezimuel/guzzlestreams/zipball/b4b5a025dfee70d6cd34c780e07330eb93d5b997", "reference": "b4b5a025dfee70d6cd34c780e07330eb93d5b997", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Stream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Fork of guzzle/streams (abandoned) to be used with elasticsearch-php", "homepage": "http://guzzlephp.org/", "keywords": ["Guzzle", "stream"], "support": {"source": "https://github.com/ezimuel/guzzlestreams/tree/3.1.0"}, "time": "2022-10-24T12:58:50+00:00"}, {"name": "ezimuel/ringphp", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/ezimuel/ringphp.git", "reference": "648a9c3c8b5f2591a317d31aa7a18a784011b00d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezimuel/ringphp/zipball/648a9c3c8b5f2591a317d31aa7a18a784011b00d", "reference": "648a9c3c8b5f2591a317d31aa7a18a784011b00d", "shasum": ""}, "require": {"ezimuel/guzzlestreams": "^3.0.1", "php": ">=5.4.0", "react/promise": "~2.0"}, "replace": {"guzzlehttp/ringphp": "self.version"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "~9.0"}, "suggest": {"ext-curl": "Guzzle will use specific adapters if cURL is present"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Ring\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Fork of guzzle/RingPHP (abandoned) to be used with elasticsearch-php", "support": {"source": "https://github.com/ezimuel/ringphp/tree/1.2.3"}, "time": "2025-01-14T12:59:43+00:00"}, {"name": "firebase/php-jwt", "version": "v6.11.0", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "8f718f4dfc9c5d5f0c994cdfd103921b43592712"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/8f718f4dfc9c5d5f0c994cdfd103921b43592712", "reference": "8f718f4dfc9c5d5f0c994cdfd103921b43592712", "shasum": ""}, "require": {"php": "^8.0"}, "require-dev": {"guzzlehttp/guzzle": "^7.4", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^9.5", "psr/cache": "^2.0||^3.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0"}, "suggest": {"ext-sodium": "Support EdDSA (Ed25519) signatures", "paragonie/sodium_compat": "Support EdDSA (Ed25519) signatures when libsodium is not present"}, "type": "library", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "support": {"issues": "https://github.com/firebase/php-jwt/issues", "source": "https://github.com/firebase/php-jwt/tree/v6.11.0"}, "time": "2025-01-23T05:11:06+00:00"}, {"name": "google/apiclient", "version": "v2.18.2", "source": {"type": "git", "url": "https://github.com/googleapis/google-api-php-client.git", "reference": "d8d201ba8a189a3cd7fb34e4da569f2ed440eee7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-api-php-client/zipball/d8d201ba8a189a3cd7fb34e4da569f2ed440eee7", "reference": "d8d201ba8a189a3cd7fb34e4da569f2ed440eee7", "shasum": ""}, "require": {"firebase/php-jwt": "^6.0", "google/apiclient-services": "~0.350", "google/auth": "^1.37", "guzzlehttp/guzzle": "^7.4.5", "guzzlehttp/psr7": "^2.6", "monolog/monolog": "^2.9||^3.0", "php": "^8.0", "phpseclib/phpseclib": "^3.0.36"}, "require-dev": {"cache/filesystem-adapter": "^1.1", "composer/composer": "^1.10.23", "phpcompatibility/php-compatibility": "^9.2", "phpspec/prophecy-phpunit": "^2.1", "phpunit/phpunit": "^9.6", "squizlabs/php_codesniffer": "^3.8", "symfony/css-selector": "~2.1", "symfony/dom-crawler": "~2.1"}, "suggest": {"cache/filesystem-adapter": "For caching certs and tokens (using Google\\Client::setCache)"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"files": ["src/aliases.php"], "psr-4": {"Google\\": "src/"}, "classmap": ["src/aliases.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Client library for Google APIs", "homepage": "http://developers.google.com/api-client-library/php", "keywords": ["google"], "support": {"issues": "https://github.com/googleapis/google-api-php-client/issues", "source": "https://github.com/googleapis/google-api-php-client/tree/v2.18.2"}, "time": "2024-12-16T22:52:40+00:00"}, {"name": "google/apiclient-services", "version": "v0.391.0", "source": {"type": "git", "url": "https://github.com/googleapis/google-api-php-client-services.git", "reference": "911ebf7a6b570780fb994c007344cf3da4187de6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-api-php-client-services/zipball/911ebf7a6b570780fb994c007344cf3da4187de6", "reference": "911ebf7a6b570780fb994c007344cf3da4187de6", "shasum": ""}, "require": {"php": "^8.0"}, "require-dev": {"phpunit/phpunit": "^9.6"}, "type": "library", "autoload": {"files": ["autoload.php"], "psr-4": {"Google\\Service\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Client library for Google APIs", "homepage": "http://developers.google.com/api-client-library/php", "keywords": ["google"], "support": {"issues": "https://github.com/googleapis/google-api-php-client-services/issues", "source": "https://github.com/googleapis/google-api-php-client-services/tree/v0.391.0"}, "time": "2025-01-20T01:04:13+00:00"}, {"name": "google/auth", "version": "v1.45.1", "source": {"type": "git", "url": "https://github.com/googleapis/google-auth-library-php.git", "reference": "86e9d937632a96b09dc40444c7cb68eb778a6ea6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-auth-library-php/zipball/86e9d937632a96b09dc40444c7cb68eb778a6ea6", "reference": "86e9d937632a96b09dc40444c7cb68eb778a6ea6", "shasum": ""}, "require": {"firebase/php-jwt": "^6.0", "guzzlehttp/guzzle": "^7.4.5", "guzzlehttp/psr7": "^2.4.5", "php": "^8.0", "psr/cache": "^2.0||^3.0", "psr/http-message": "^1.1||^2.0", "psr/log": "^3.0"}, "require-dev": {"guzzlehttp/promises": "^2.0", "kelvinmo/simplejwt": "0.7.1", "phpseclib/phpseclib": "^3.0.35", "phpspec/prophecy-phpunit": "^2.1", "phpunit/phpunit": "^9.6", "sebastian/comparator": ">=1.2.3", "squizlabs/php_codesniffer": "^3.5", "symfony/process": "^6.0||^7.0", "webmozart/assert": "^1.11"}, "suggest": {"phpseclib/phpseclib": "May be used in place of OpenSSL for signing strings or for token management. Please require version ^2."}, "type": "library", "autoload": {"psr-4": {"Google\\Auth\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Google Auth Library for PHP", "homepage": "http://github.com/google/google-auth-library-php", "keywords": ["Authentication", "google", "oauth2"], "support": {"docs": "https://googleapis.github.io/google-auth-library-php/main/", "issues": "https://github.com/googleapis/google-auth-library-php/issues", "source": "https://github.com/googleapis/google-auth-library-php/tree/v1.45.1"}, "time": "2025-01-22T23:27:35+00:00"}, {"name": "gopay/payments-sdk-php", "version": "1.10.3", "source": {"type": "git", "url": "https://github.com/gopaycommunity/gopay-php-api.git", "reference": "2fa4d6223978d0d7077fe1d7dfc0561046c5684a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/gopaycommunity/gopay-php-api/zipball/2fa4d6223978d0d7077fe1d7dfc0561046c5684a", "reference": "2fa4d6223978d0d7077fe1d7dfc0561046c5684a", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^7.7.0", "php": ">=8.1", "symfony/deprecation-contracts": "^3.3.0"}, "require-dev": {"hamcrest/hamcrest-php": "*", "phpspec/prophecy": "~1.0", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "9.3.7"}, "type": "library", "autoload": {"files": ["factory.php"], "psr-4": {"GoPay\\": "src/"}, "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "GoPay", "homepage": "https://github.com/gopaycommunity/gopay-php-api/contributors"}], "description": "GoPay's PHP SDK for Payments REST API", "keywords": ["api", "gopay", "payments", "rest", "sdk"], "support": {"issues": "https://github.com/gopaycommunity/gopay-php-api/issues", "source": "https://github.com/gopaycommunity/gopay-php-api/tree/1.10.3"}, "time": "2025-06-05T07:26:24+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.9.2", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "d281ed313b989f213357e3be1a179f02196ac99b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/d281ed313b989f213357e3be1a179f02196ac99b", "reference": "d281ed313b989f213357e3be1a179f02196ac99b", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^2.7.0", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.9.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2024-07-24T11:22:20+00:00"}, {"name": "guzzlehttp/promises", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "f9c436286ab2892c7db7be8c8da4ef61ccf7b455"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/f9c436286ab2892c7db7be8c8da4ef61ccf7b455", "reference": "f9c436286ab2892c7db7be8c8da4ef61ccf7b455", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.0.4"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2024-10-17T10:06:22+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.7.0", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "a70f5c95fb43bc83f07c9c948baa0dc1829bf201"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/a70f5c95fb43bc83f07c9c948baa0dc1829bf201", "reference": "a70f5c95fb43bc83f07c9c948baa0dc1829bf201", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.7.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2024-07-18T11:15:46+00:00"}, {"name": "heureka/overeno-zakazniky", "version": "v4.0.2", "source": {"type": "git", "url": "https://github.com/heureka/overeno-zakazniky.git", "reference": "30eb6f7ab47ee3068f71efbdbde4a0d4b019a0a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/heureka/overeno-zakazniky/zipball/30eb6f7ab47ee3068f71efbdbde4a0d4b019a0a2", "reference": "30eb6f7ab47ee3068f71efbdbde4a0d4b019a0a2", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"mockery/mockery": "~1.4.3", "phpunit/phpunit": "~9.5.0"}, "suggest": {"ext-curl": "Simplifies the library usage (you don't have to provide your own requester)"}, "type": "library", "autoload": {"psr-4": {"Heureka\\": ["src/", "tests"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Heureka.cz", "email": "<EMAIL>"}], "description": "Heureka 'Ov<PERSON><PERSON>eno zákazníky' (ShopCertification) service API implementation", "support": {"issues": "https://github.com/heureka/overeno-zakazniky/issues", "source": "https://github.com/heureka/overeno-zakazniky/tree/v4.0.2"}, "time": "2023-10-16T07:06:56+00:00"}, {"name": "jaybizzle/crawler-detect", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/JayBizzle/Crawler-Detect.git", "reference": "e5ebe17f5a30a7840d059206447adfe0db60d15f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/JayBizzle/Crawler-Detect/zipball/e5ebe17f5a30a7840d059206447adfe0db60d15f", "reference": "e5ebe17f5a30a7840d059206447adfe0db60d15f", "shasum": ""}, "require": {"php": ">=7.1.0"}, "require-dev": {"phpunit/phpunit": "^4.8|^5.5|^6.5|^9.4"}, "type": "library", "autoload": {"psr-4": {"Jaybizzle\\CrawlerDetect\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "CrawlerDetect is a PHP class for detecting bots/crawlers/spiders via the user agent", "homepage": "https://github.com/JayBizzle/Crawler-Detect/", "keywords": ["crawler", "crawler detect", "crawler detector", "crawlerdetect", "php crawler detect"], "support": {"issues": "https://github.com/JayBizzle/Crawler-Detect/issues", "source": "https://github.com/JayBizzle/Crawler-Detect/tree/v1.3.1"}, "time": "2025-01-20T19:31:14+00:00"}, {"name": "jean85/pretty-package-versions", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/Jean85/pretty-package-versions.git", "reference": "3c4e5f62ba8d7de1734312e4fff32f67a8daaf10"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Jean85/pretty-package-versions/zipball/3c4e5f62ba8d7de1734312e4fff32f67a8daaf10", "reference": "3c4e5f62ba8d7de1734312e4fff32f67a8daaf10", "shasum": ""}, "require": {"composer-runtime-api": "^2.1.0", "php": "^7.4|^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "jean85/composer-provided-replaced-stub-package": "^1.0", "phpstan/phpstan": "^1.4", "phpunit/phpunit": "^7.5|^8.5|^9.6", "vimeo/psalm": "^4.3 || ^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Jean85\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to get pretty versions strings of installed dependencies", "keywords": ["composer", "package", "release", "versions"], "support": {"issues": "https://github.com/Jean85/pretty-package-versions/issues", "source": "https://github.com/Jean85/pretty-package-versions/tree/2.1.0"}, "time": "2024-11-18T16:19:46+00:00"}, {"name": "koriym/attributes", "version": "1.0.5", "source": {"type": "git", "url": "https://github.com/koriym/Koriym.Attributes.git", "reference": "7f636a3a04a746ed03f6a36be644539e0d77edbb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/koriym/Koriym.Attributes/zipball/7f636a3a04a746ed03f6a36be644539e0d77edbb", "reference": "7f636a3a04a746ed03f6a36be644539e0d77edbb", "shasum": ""}, "require": {"doctrine/annotations": "^1.12 || ^2.0", "php": "^7.2 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4", "ext-pdo": "*", "phpunit/phpunit": "^8.5.24 || ^9.5"}, "suggest": {"koriym/param-reader": "An attribute/annotation reader for parameters"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": true, "target-directory": "vendor-bin"}}, "autoload": {"psr-4": {"Koriym\\Attributes\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An annotation/attribute reader", "keywords": ["annotation", "attribute"], "support": {"issues": "https://github.com/koriym/Koriym.Attributes/issues", "source": "https://github.com/koriym/Koriym.Attributes/tree/1.0.5"}, "time": "2023-07-08T09:09:20+00:00"}, {"name": "latte/latte", "version": "v3.0.20", "source": {"type": "git", "url": "https://github.com/nette/latte.git", "reference": "4db7a5502f8cef02fffa84fc9c34a635d9c79d4d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/latte/zipball/4db7a5502f8cef02fffa84fc9c34a635d9c79d4d", "reference": "4db7a5502f8cef02fffa84fc9c34a635d9c79d4d", "shasum": ""}, "require": {"ext-json": "*", "ext-tokenizer": "*", "php": "8.0 - 8.4"}, "conflict": {"nette/application": "<3.1.7", "nette/caching": "<3.1.4"}, "require-dev": {"nette/php-generator": "^4.0", "nette/tester": "^2.5", "nette/utils": "^4.0", "phpstan/phpstan": "^1", "tracy/tracy": "^2.10"}, "suggest": {"ext-fileinfo": "to use filter |datastream", "ext-iconv": "to use filters |reverse, |substring", "ext-intl": "to use Latte\\Engine::setLocale()", "ext-mbstring": "to use filters like lower, upper, capitalize, ...", "nette/php-generator": "to use tag {templatePrint}", "nette/utils": "to use filter |webalize"}, "bin": ["bin/latte-lint"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "☕ Latte: the intuitive and fast template engine for those who want the most secure PHP sites. Introduces context-sensitive escaping.", "homepage": "https://latte.nette.org", "keywords": ["context-sensitive", "engine", "escaping", "html", "nette", "security", "template", "twig"], "support": {"issues": "https://github.com/nette/latte/issues", "source": "https://github.com/nette/latte/tree/v3.0.20"}, "time": "2024-10-08T00:58:27+00:00"}, {"name": "league/csv", "version": "9.21.0", "source": {"type": "git", "url": "https://github.com/thephpleague/csv.git", "reference": "72196d11ebba22d868954cb39c0c7346207430cc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/csv/zipball/72196d11ebba22d868954cb39c0c7346207430cc", "reference": "72196d11ebba22d868954cb39c0c7346207430cc", "shasum": ""}, "require": {"ext-filter": "*", "php": "^8.1.2"}, "require-dev": {"ext-dom": "*", "ext-xdebug": "*", "friendsofphp/php-cs-fixer": "^3.64.0", "phpbench/phpbench": "^1.3.1", "phpstan/phpstan": "^1.12.11", "phpstan/phpstan-deprecation-rules": "^1.2.1", "phpstan/phpstan-phpunit": "^1.4.1", "phpstan/phpstan-strict-rules": "^1.6.1", "phpunit/phpunit": "^10.5.16 || ^11.4.3", "symfony/var-dumper": "^6.4.8 || ^7.1.8"}, "suggest": {"ext-dom": "Required to use the XMLConverter and the HTMLConverter classes", "ext-iconv": "Needed to ease transcoding CSV using iconv stream filters", "ext-mbstring": "Needed to ease transcoding CSV using mb stream filters"}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"League\\Csv\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/nyamsprod/", "role": "Developer"}], "description": "CSV data manipulation made easy in PHP", "homepage": "https://csv.thephpleague.com", "keywords": ["convert", "csv", "export", "filter", "import", "read", "transform", "write"], "support": {"docs": "https://csv.thephpleague.com", "issues": "https://github.com/thephpleague/csv/issues", "rss": "https://github.com/thephpleague/csv/releases.atom", "source": "https://github.com/thephpleague/csv"}, "funding": [{"url": "https://github.com/sponsors/nyamsprod", "type": "github"}], "time": "2025-01-08T19:27:58+00:00"}, {"name": "league/oauth2-client", "version": "2.8.0", "source": {"type": "git", "url": "https://github.com/thephpleague/oauth2-client.git", "reference": "3d5cf8d0543731dfb725ab30e4d7289891991e13"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/oauth2-client/zipball/3d5cf8d0543731dfb725ab30e4d7289891991e13", "reference": "3d5cf8d0543731dfb725ab30e4d7289891991e13", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/guzzle": "^6.5.8 || ^7.4.5", "php": "^7.1 || >=8.0.0 <8.5.0"}, "require-dev": {"mockery/mockery": "^1.3.5", "php-parallel-lint/php-parallel-lint": "^1.4", "phpunit/phpunit": "^7 || ^8 || ^9 || ^10 || ^11", "squizlabs/php_codesniffer": "^3.11"}, "type": "library", "autoload": {"psr-4": {"League\\OAuth2\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.alexbilbie.com", "role": "Developer"}, {"name": "<PERSON>", "homepage": "https://github.com/shadowhand", "role": "Contributor"}], "description": "OAuth 2.0 Client Library", "keywords": ["Authentication", "SSO", "authorization", "identity", "idp", "o<PERSON>h", "oauth2", "single sign on"], "support": {"issues": "https://github.com/thephpleague/oauth2-client/issues", "source": "https://github.com/thephpleague/oauth2-client/tree/2.8.0"}, "time": "2024-12-11T05:05:52+00:00"}, {"name": "league/oauth2-facebook", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/thephpleague/oauth2-facebook.git", "reference": "ec6d62a00b548c6cd56d7b734346b9e6befbfbbb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/oauth2-facebook/zipball/ec6d62a00b548c6cd56d7b734346b9e6befbfbbb", "reference": "ec6d62a00b548c6cd56d7b734346b9e6befbfbbb", "shasum": ""}, "require": {"league/oauth2-client": "^2.0", "php": ">=7.3"}, "require-dev": {"ext-json": "*", "mockery/mockery": "~1.3.0", "phpunit/phpunit": "^9.4", "squizlabs/php_codesniffer": "~3.0"}, "type": "library", "autoload": {"psr-4": {"League\\OAuth2\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.sammyk.me"}], "description": "Facebook OAuth 2.0 Client Provider for The PHP League OAuth2-Client", "keywords": ["Authentication", "authorization", "client", "facebook", "o<PERSON>h", "oauth2"], "support": {"issues": "https://github.com/thephpleague/oauth2-facebook/issues", "source": "https://github.com/thephpleague/oauth2-facebook/tree/2.2.0"}, "time": "2022-02-24T18:45:07+00:00"}, {"name": "league/oauth2-google", "version": "4.0.1", "source": {"type": "git", "url": "https://github.com/thephpleague/oauth2-google.git", "reference": "1b01ba18ba31b29e88771e3e0979e5c91d4afe76"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/oauth2-google/zipball/1b01ba18ba31b29e88771e3e0979e5c91d4afe76", "reference": "1b01ba18ba31b29e88771e3e0979e5c91d4afe76", "shasum": ""}, "require": {"league/oauth2-client": "^2.0", "php": "^7.3 || ^8.0"}, "require-dev": {"eloquent/phony-phpunit": "^6.0 || ^7.1", "phpunit/phpunit": "^8.0 || ^9.0", "squizlabs/php_codesniffer": "^3.0"}, "type": "library", "autoload": {"psr-4": {"League\\OAuth2\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://shadowhand.com"}], "description": "Google OAuth 2.0 Client Provider for The PHP League OAuth2-Client", "keywords": ["Authentication", "authorization", "client", "google", "o<PERSON>h", "oauth2"], "support": {"issues": "https://github.com/thephpleague/oauth2-google/issues", "source": "https://github.com/thephpleague/oauth2-google/tree/4.0.1"}, "time": "2023-03-17T15:20:52+00:00"}, {"name": "marc-mabe/php-enum", "version": "v4.7.1", "source": {"type": "git", "url": "https://github.com/marc-mabe/php-enum.git", "reference": "7159809e5cfa041dca28e61f7f7ae58063aae8ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/marc-mabe/php-enum/zipball/7159809e5cfa041dca28e61f7f7ae58063aae8ed", "reference": "7159809e5cfa041dca28e61f7f7ae58063aae8ed", "shasum": ""}, "require": {"ext-reflection": "*", "php": "^7.1 | ^8.0"}, "require-dev": {"phpbench/phpbench": "^0.16.10 || ^1.0.4", "phpstan/phpstan": "^1.3.1", "phpunit/phpunit": "^7.5.20 | ^8.5.22 | ^9.5.11", "vimeo/psalm": "^4.17.0 | ^5.26.1"}, "type": "library", "extra": {"branch-alias": {"dev-3.x": "3.2-dev", "dev-master": "4.7-dev"}}, "autoload": {"psr-4": {"MabeEnum\\": "src/"}, "classmap": ["stubs/Stringable.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://mabe.berlin/", "role": "Lead"}], "description": "Simple and fast implementation of enumerations with native PHP", "homepage": "https://github.com/marc-mabe/php-enum", "keywords": ["enum", "enum-map", "enum-set", "enumeration", "enumerator", "enummap", "enumset", "map", "set", "type", "type-hint", "<PERSON><PERSON>t"], "support": {"issues": "https://github.com/marc-mabe/php-enum/issues", "source": "https://github.com/marc-mabe/php-enum/tree/v4.7.1"}, "time": "2024-11-28T04:54:44+00:00"}, {"name": "monolog/monolog", "version": "3.8.1", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "aef6ee73a77a66e404dd6540934a9ef1b3c855b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/aef6ee73a77a66e404dd6540934a9ef1b3c855b4", "reference": "aef6ee73a77a66e404dd6540934a9ef1b3c855b4", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "3.0.0"}, "require-dev": {"aws/aws-sdk-php": "^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "ext-json": "*", "graylog2/gelf-php": "^1.4.2 || ^2.0", "guzzlehttp/guzzle": "^7.4.5", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "php-console/php-console": "^3.1.8", "phpstan/phpstan": "^2", "phpstan/phpstan-deprecation-rules": "^2", "phpstan/phpstan-strict-rules": "^2", "phpunit/phpunit": "^10.5.17 || ^11.0.7", "predis/predis": "^1.1 || ^2", "rollbar/rollbar": "^4.0", "ruflin/elastica": "^7 || ^8", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/3.8.1"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2024-12-05T17:15:07+00:00"}, {"name": "mpdf/mpdf", "version": "v8.2.5", "source": {"type": "git", "url": "https://github.com/mpdf/mpdf.git", "reference": "e175b05e3e00977b85feb96a8cccb174ac63621f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/mpdf/zipball/e175b05e3e00977b85feb96a8cccb174ac63621f", "reference": "e175b05e3e00977b85feb96a8cccb174ac63621f", "shasum": ""}, "require": {"ext-gd": "*", "ext-mbstring": "*", "mpdf/psr-http-message-shim": "^1.0 || ^2.0", "mpdf/psr-log-aware-trait": "^2.0 || ^3.0", "myclabs/deep-copy": "^1.7", "paragonie/random_compat": "^1.4|^2.0|^9.99.99", "php": "^5.6 || ^7.0 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "psr/http-message": "^1.0 || ^2.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "setasign/fpdi": "^2.1"}, "require-dev": {"mockery/mockery": "^1.3.0", "mpdf/qrcode": "^1.1.0", "squizlabs/php_codesniffer": "^3.5.0", "tracy/tracy": "~2.5", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"ext-bcmath": "Needed for generation of some types of barcodes", "ext-xml": "Needed mainly for SVG manipulation", "ext-zlib": "Needed for compression of embedded resources, such as fonts"}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"Mpdf\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-only"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>, maintainer"}, {"name": "<PERSON>", "role": "<PERSON><PERSON><PERSON> (retired)"}], "description": "PHP library generating PDF files from UTF-8 encoded HTML", "homepage": "https://mpdf.github.io", "keywords": ["pdf", "php", "utf-8"], "support": {"docs": "https://mpdf.github.io", "issues": "https://github.com/mpdf/mpdf/issues", "source": "https://github.com/mpdf/mpdf"}, "funding": [{"url": "https://www.paypal.me/mpdf", "type": "custom"}], "time": "2024-11-18T15:30:42+00:00"}, {"name": "mpdf/psr-http-message-shim", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/mpdf/psr-http-message-shim.git", "reference": "f25a0153d645e234f9db42e5433b16d9b113920f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/psr-http-message-shim/zipball/f25a0153d645e234f9db42e5433b16d9b113920f", "reference": "f25a0153d645e234f9db42e5433b16d9b113920f", "shasum": ""}, "require": {"psr/http-message": "^2.0"}, "type": "library", "autoload": {"psr-4": {"Mpdf\\PsrHttpMessageShim\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Shim to allow support of different psr/message versions.", "support": {"issues": "https://github.com/mpdf/psr-http-message-shim/issues", "source": "https://github.com/mpdf/psr-http-message-shim/tree/v2.0.1"}, "time": "2023-10-02T14:34:03+00:00"}, {"name": "mpdf/psr-log-aware-trait", "version": "v3.0.0", "source": {"type": "git", "url": "https://github.com/mpdf/psr-log-aware-trait.git", "reference": "a633da6065e946cc491e1c962850344bb0bf3e78"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/psr-log-aware-trait/zipball/a633da6065e946cc491e1c962850344bb0bf3e78", "reference": "a633da6065e946cc491e1c962850344bb0bf3e78", "shasum": ""}, "require": {"psr/log": "^3.0"}, "type": "library", "autoload": {"psr-4": {"Mpdf\\PsrLogAwareTrait\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Trait to allow support of different psr/log versions.", "support": {"issues": "https://github.com/mpdf/psr-log-aware-trait/issues", "source": "https://github.com/mpdf/psr-log-aware-trait/tree/v3.0.0"}, "time": "2023-05-03T06:19:36+00:00"}, {"name": "myclabs/deep-copy", "version": "1.12.1", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "123267b2c49fbf30d78a7b2d333f6be754b94845"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/123267b2c49fbf30d78a7b2d333f6be754b94845", "reference": "123267b2c49fbf30d78a7b2d333f6be754b94845", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3 <3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpspec/prophecy": "^1.10", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.12.1"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2024-11-08T17:47:46+00:00"}, {"name": "nette/application", "version": "v3.2.6", "source": {"type": "git", "url": "https://github.com/nette/application.git", "reference": "9c288cc45df467dc012504f4ad64791279720af8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/application/zipball/9c288cc45df467dc012504f4ad64791279720af8", "reference": "9c288cc45df467dc012504f4ad64791279720af8", "shasum": ""}, "require": {"nette/component-model": "^3.1", "nette/http": "^3.3", "nette/routing": "^3.1", "nette/utils": "^4.0", "php": "8.1 - 8.4"}, "conflict": {"latte/latte": "<2.7.1 || >=3.0.0 <3.0.18 || >=3.1", "nette/caching": "<3.2", "nette/di": "<3.2", "nette/forms": "<3.2", "nette/schema": "<1.3", "tracy/tracy": "<2.9"}, "require-dev": {"jetbrains/phpstorm-attributes": "dev-master", "latte/latte": "^2.10.2 || ^3.0.18", "mockery/mockery": "^2.0", "nette/di": "^3.2", "nette/forms": "^3.2", "nette/robot-loader": "^4.0", "nette/security": "^3.2", "nette/tester": "^2.5", "phpstan/phpstan-nette": "^1.0", "tracy/tracy": "^2.9"}, "suggest": {"latte/latte": "Allows using Latte in templates", "nette/forms": "Allows to use Nette\\Application\\UI\\Form"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🏆 Nette Application: a full-stack component-based MVC kernel for PHP that helps you write powerful and modern web applications. Write less, have cleaner code and your work will bring you joy.", "homepage": "https://nette.org", "keywords": ["Forms", "component-based", "control", "framework", "mvc", "mvp", "nette", "presenter", "routing", "seo"], "support": {"issues": "https://github.com/nette/application/issues", "source": "https://github.com/nette/application/tree/v3.2.6"}, "time": "2024-09-10T10:08:04+00:00"}, {"name": "nette/bootstrap", "version": "v3.2.5", "source": {"type": "git", "url": "https://github.com/nette/bootstrap.git", "reference": "91d08432cb33d6c08d58b215c769d04f20580624"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/bootstrap/zipball/91d08432cb33d6c08d58b215c769d04f20580624", "reference": "91d08432cb33d6c08d58b215c769d04f20580624", "shasum": ""}, "require": {"nette/di": "^3.1", "nette/utils": "^3.2.1 || ^4.0", "php": "8.0 - 8.4"}, "conflict": {"tracy/tracy": "<2.6"}, "require-dev": {"latte/latte": "^2.8 || ^3.0", "nette/application": "^3.1", "nette/caching": "^3.0", "nette/database": "^3.0", "nette/forms": "^3.0", "nette/http": "^3.0", "nette/mail": "^3.0 || ^4.0", "nette/robot-loader": "^3.0 || ^4.0", "nette/safe-stream": "^2.2", "nette/security": "^3.0", "nette/tester": "^2.4", "phpstan/phpstan-nette": "^1.0", "tracy/tracy": "^2.9"}, "suggest": {"nette/robot-loader": "to use Configurator::createRobotLoader()", "tracy/tracy": "to use Configurator::enableTracy()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🅱  Nette Bootstrap: the simple way to configure and bootstrap your Nette application.", "homepage": "https://nette.org", "keywords": ["bootstrapping", "configurator", "nette"], "support": {"issues": "https://github.com/nette/bootstrap/issues", "source": "https://github.com/nette/bootstrap/tree/v3.2.5"}, "time": "2024-11-14T00:49:46+00:00"}, {"name": "nette/caching", "version": "v3.3.1", "source": {"type": "git", "url": "https://github.com/nette/caching.git", "reference": "b37d2c9647b41a9d04f099f10300dc5496c4eb77"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/caching/zipball/b37d2c9647b41a9d04f099f10300dc5496c4eb77", "reference": "b37d2c9647b41a9d04f099f10300dc5496c4eb77", "shasum": ""}, "require": {"nette/utils": "^4.0", "php": "8.0 - 8.4"}, "conflict": {"latte/latte": ">=3.0.0 <3.0.12"}, "require-dev": {"latte/latte": "^2.11 || ^3.0.12", "nette/di": "^3.1 || ^4.0", "nette/tester": "^2.4", "phpstan/phpstan": "^1.0", "psr/simple-cache": "^2.0 || ^3.0", "tracy/tracy": "^2.9"}, "suggest": {"ext-pdo_sqlite": "to use SQLiteStorage or SQLiteJournal"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "⏱ Nette Caching: library with easy-to-use API and many cache backends.", "homepage": "https://nette.org", "keywords": ["cache", "journal", "memcached", "nette", "sqlite"], "support": {"issues": "https://github.com/nette/caching/issues", "source": "https://github.com/nette/caching/tree/v3.3.1"}, "time": "2024-08-07T00:01:58+00:00"}, {"name": "nette/component-model", "version": "v3.1.1", "source": {"type": "git", "url": "https://github.com/nette/component-model.git", "reference": "fb7608fd5f1c378ef9ef8ddc459c6ef0b63e9d77"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/component-model/zipball/fb7608fd5f1c378ef9ef8ddc459c6ef0b63e9d77", "reference": "fb7608fd5f1c378ef9ef8ddc459c6ef0b63e9d77", "shasum": ""}, "require": {"nette/utils": "^4.0", "php": "8.1 - 8.4"}, "require-dev": {"nette/tester": "^2.5", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "⚛ Nette Component Model", "homepage": "https://nette.org", "keywords": ["components", "nette"], "support": {"issues": "https://github.com/nette/component-model/issues", "source": "https://github.com/nette/component-model/tree/v3.1.1"}, "time": "2024-08-07T00:35:59+00:00"}, {"name": "nette/di", "version": "v3.2.4", "source": {"type": "git", "url": "https://github.com/nette/di.git", "reference": "57f923a7af32435b6e4921c0adbc70c619625a17"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/di/zipball/57f923a7af32435b6e4921c0adbc70c619625a17", "reference": "57f923a7af32435b6e4921c0adbc70c619625a17", "shasum": ""}, "require": {"ext-ctype": "*", "ext-tokenizer": "*", "nette/neon": "^3.3 || ^4.0", "nette/php-generator": "^4.1.6", "nette/robot-loader": "^4.0", "nette/schema": "^1.2.5", "nette/utils": "^4.0", "php": "8.1 - 8.4"}, "require-dev": {"nette/tester": "^2.5.2", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "💎 Nette Dependency Injection Container: Flexible, compiled and full-featured DIC with perfectly usable autowiring and support for all new PHP features.", "homepage": "https://nette.org", "keywords": ["compiled", "di", "dic", "factory", "ioc", "nette", "static"], "support": {"issues": "https://github.com/nette/di/issues", "source": "https://github.com/nette/di/tree/v3.2.4"}, "time": "2025-01-10T04:57:37+00:00"}, {"name": "nette/finder", "version": "v3.0.0", "source": {"type": "git", "url": "https://github.com/nette/finder.git", "reference": "027395c638637de95c8e9fad49a7c51249404ed2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/finder/zipball/027395c638637de95c8e9fad49a7c51249404ed2", "reference": "027395c638637de95c8e9fad49a7c51249404ed2", "shasum": ""}, "require": {"nette/utils": "^4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🔍 Nette Finder: find files and directories with an intuitive API.", "homepage": "https://nette.org", "keywords": ["filesystem", "glob", "iterator", "nette"], "support": {"issues": "https://github.com/nette/finder/issues", "source": "https://github.com/nette/finder/tree/v3.0.0"}, "time": "2022-12-14T17:05:54+00:00"}, {"name": "nette/forms", "version": "v3.2.5", "source": {"type": "git", "url": "https://github.com/nette/forms.git", "reference": "7e59cee3a16e0382f83680c94babb85a0a167dd0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/forms/zipball/7e59cee3a16e0382f83680c94babb85a0a167dd0", "reference": "7e59cee3a16e0382f83680c94babb85a0a167dd0", "shasum": ""}, "require": {"nette/component-model": "^3.1", "nette/http": "^3.3", "nette/utils": "^4.0.4", "php": "8.1 - 8.4"}, "conflict": {"latte/latte": ">=3.0.0 <3.0.12 || >=3.1"}, "require-dev": {"latte/latte": "^2.10.2 || ^3.0.12", "nette/application": "^3.0", "nette/di": "^3.0", "nette/tester": "^2.5.2", "phpstan/phpstan-nette": "^1", "tracy/tracy": "^2.9"}, "suggest": {"ext-intl": "to use date/time controls"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "📝 Nette Forms: generating, validating and processing secure forms in PHP. Handy API, fully customizable, server & client side validation and mature design.", "homepage": "https://nette.org", "keywords": ["Forms", "bootstrap", "csrf", "javascript", "nette", "validation"], "support": {"issues": "https://github.com/nette/forms/issues", "source": "https://github.com/nette/forms/tree/v3.2.5"}, "time": "2024-10-22T18:42:14+00:00"}, {"name": "nette/http", "version": "v3.3.2", "source": {"type": "git", "url": "https://github.com/nette/http.git", "reference": "3e2587b34beb66f238f119b12fbb4f0b9ab2d6d1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/http/zipball/3e2587b34beb66f238f119b12fbb4f0b9ab2d6d1", "reference": "3e2587b34beb66f238f119b12fbb4f0b9ab2d6d1", "shasum": ""}, "require": {"nette/utils": "^4.0.4", "php": "8.1 - 8.4"}, "conflict": {"nette/di": "<3.0.3", "nette/schema": "<1.2"}, "require-dev": {"nette/di": "^3.0", "nette/security": "^3.0", "nette/tester": "^2.4", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.8"}, "suggest": {"ext-fileinfo": "to detect MIME type of uploaded files by Nette\\Http\\FileUpload", "ext-gd": "to use image function in Nette\\Http\\FileUpload", "ext-intl": "to support punycode by Nette\\Http\\Url", "ext-session": "to use Nette\\Http\\Session"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🌐 Nette Http: abstraction for HTTP request, response and session. Provides careful data sanitization and utility for URL and cookies manipulation.", "homepage": "https://nette.org", "keywords": ["cookies", "http", "nette", "proxy", "request", "response", "security", "session", "url"], "support": {"issues": "https://github.com/nette/http/issues", "source": "https://github.com/nette/http/tree/v3.3.2"}, "time": "2025-01-12T16:27:57+00:00"}, {"name": "nette/mail", "version": "v4.0.3", "source": {"type": "git", "url": "https://github.com/nette/mail.git", "reference": "d99839701c48031d6f35e3be95bdd9418f66ad2d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/mail/zipball/d99839701c48031d6f35e3be95bdd9418f66ad2d", "reference": "d99839701c48031d6f35e3be95bdd9418f66ad2d", "shasum": ""}, "require": {"ext-iconv": "*", "nette/utils": "^4.0", "php": "8.0 - 8.4"}, "require-dev": {"nette/di": "^3.1 || ^4.0", "nette/tester": "^2.4", "phpstan/phpstan-nette": "^1.0", "tracy/tracy": "^2.8"}, "suggest": {"ext-fileinfo": "to detect type of attached files", "ext-openssl": "to use Nette\\Mail\\DkimSigner"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "📧 Nette Mail: A handy library for creating and sending emails in PHP.", "homepage": "https://nette.org", "keywords": ["mail", "mailer", "mime", "nette", "smtp"], "support": {"issues": "https://github.com/nette/mail/issues", "source": "https://github.com/nette/mail/tree/v4.0.3"}, "time": "2024-10-05T03:15:12+00:00"}, {"name": "nette/neon", "version": "v3.4.4", "source": {"type": "git", "url": "https://github.com/nette/neon.git", "reference": "3411aa86b104e2d5b7e760da4600865ead963c3c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/neon/zipball/3411aa86b104e2d5b7e760da4600865ead963c3c", "reference": "3411aa86b104e2d5b7e760da4600865ead963c3c", "shasum": ""}, "require": {"ext-json": "*", "php": "8.0 - 8.4"}, "require-dev": {"nette/tester": "^2.4", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.7"}, "bin": ["bin/neon-lint"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🍸 Nette NEON: encodes and decodes NEON file format.", "homepage": "https://ne-on.org", "keywords": ["export", "import", "neon", "nette", "yaml"], "support": {"issues": "https://github.com/nette/neon/issues", "source": "https://github.com/nette/neon/tree/v3.4.4"}, "time": "2024-10-04T22:00:08+00:00"}, {"name": "nette/php-generator", "version": "v4.1.7", "source": {"type": "git", "url": "https://github.com/nette/php-generator.git", "reference": "d201c9bc217e0969d1b678d286be49302972fb56"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/php-generator/zipball/d201c9bc217e0969d1b678d286be49302972fb56", "reference": "d201c9bc217e0969d1b678d286be49302972fb56", "shasum": ""}, "require": {"nette/utils": "^3.2.9 || ^4.0", "php": "8.0 - 8.4"}, "require-dev": {"jetbrains/phpstorm-attributes": "dev-master", "nette/tester": "^2.4", "nikic/php-parser": "^4.18 || ^5.0", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.8"}, "suggest": {"nikic/php-parser": "to use ClassType::from(withBodies: true) & ClassType::fromCode()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🐘 Nette PHP Generator: generates neat PHP code for you. Supports new PHP 8.4 features.", "homepage": "https://nette.org", "keywords": ["code", "nette", "php", "scaffolding"], "support": {"issues": "https://github.com/nette/php-generator/issues", "source": "https://github.com/nette/php-generator/tree/v4.1.7"}, "time": "2024-11-29T01:41:18+00:00"}, {"name": "nette/robot-loader", "version": "v4.0.3", "source": {"type": "git", "url": "https://github.com/nette/robot-loader.git", "reference": "45d67753fb4865bb718e9a6c9be69cc9470137b7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/robot-loader/zipball/45d67753fb4865bb718e9a6c9be69cc9470137b7", "reference": "45d67753fb4865bb718e9a6c9be69cc9470137b7", "shasum": ""}, "require": {"ext-tokenizer": "*", "nette/utils": "^4.0", "php": "8.0 - 8.4"}, "require-dev": {"nette/tester": "^2.4", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🍀 Nette RobotLoader: high performance and comfortable autoloader that will search and autoload classes within your application.", "homepage": "https://nette.org", "keywords": ["autoload", "class", "interface", "nette", "trait"], "support": {"issues": "https://github.com/nette/robot-loader/issues", "source": "https://github.com/nette/robot-loader/tree/v4.0.3"}, "time": "2024-06-18T20:26:39+00:00"}, {"name": "nette/routing", "version": "v3.1.1", "source": {"type": "git", "url": "https://github.com/nette/routing.git", "reference": "5b0782d3b50af68614253a373fa663ed03206a3f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/routing/zipball/5b0782d3b50af68614253a373fa663ed03206a3f", "reference": "5b0782d3b50af68614253a373fa663ed03206a3f", "shasum": ""}, "require": {"nette/http": "^3.2 || ~4.0.0", "nette/utils": "^4.0", "php": "8.1 - 8.4"}, "require-dev": {"nette/tester": "^2.5", "phpstan/phpstan": "^1", "tracy/tracy": "^2.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "Nette Routing: two-ways URL conversion", "homepage": "https://nette.org", "keywords": ["nette"], "support": {"issues": "https://github.com/nette/routing/issues", "source": "https://github.com/nette/routing/tree/v3.1.1"}, "time": "2024-11-04T11:59:47+00:00"}, {"name": "nette/safe-stream", "version": "v3.0.2", "source": {"type": "git", "url": "https://github.com/nette/safe-stream.git", "reference": "49a4924a761b053259a720633808305178376443"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/safe-stream/zipball/49a4924a761b053259a720633808305178376443", "reference": "49a4924a761b053259a720633808305178376443", "shasum": ""}, "require": {"php": "8.0 - 8.4"}, "require-dev": {"nette/tester": "^2.4", "phpstan/phpstan": "^2", "tracy/tracy": "^2.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"files": ["src/loader.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "Nette SafeStream: provides isolation for thread safe manipulation with files via native PHP functions.", "homepage": "https://nette.org", "keywords": ["atomic", "filesystem", "isolation", "nette", "safe", "thread safe"], "support": {"issues": "https://github.com/nette/safe-stream/issues", "source": "https://github.com/nette/safe-stream/tree/v3.0.2"}, "time": "2025-01-01T21:32:26+00:00"}, {"name": "nette/schema", "version": "v1.3.2", "source": {"type": "git", "url": "https://github.com/nette/schema.git", "reference": "da801d52f0354f70a638673c4a0f04e16529431d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/schema/zipball/da801d52f0354f70a638673c4a0f04e16529431d", "reference": "da801d52f0354f70a638673c4a0f04e16529431d", "shasum": ""}, "require": {"nette/utils": "^4.0", "php": "8.1 - 8.4"}, "require-dev": {"nette/tester": "^2.5.2", "phpstan/phpstan-nette": "^1.0", "tracy/tracy": "^2.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "📐 Nette Schema: validating data structures against a given Schema.", "homepage": "https://nette.org", "keywords": ["config", "nette"], "support": {"issues": "https://github.com/nette/schema/issues", "source": "https://github.com/nette/schema/tree/v1.3.2"}, "time": "2024-10-06T23:10:23+00:00"}, {"name": "nette/security", "version": "v3.2.1", "source": {"type": "git", "url": "https://github.com/nette/security.git", "reference": "6e19bf604934aec0cd3343a307e28fd997e40e96"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/security/zipball/6e19bf604934aec0cd3343a307e28fd997e40e96", "reference": "6e19bf604934aec0cd3343a307e28fd997e40e96", "shasum": ""}, "require": {"nette/utils": "^4.0", "php": "8.1 - 8.4"}, "conflict": {"nette/di": "<3.0-stable", "nette/http": "<3.1.3"}, "require-dev": {"mockery/mockery": "^1.5", "nette/di": "^3.1", "nette/http": "^3.2", "nette/tester": "^2.5", "phpstan/phpstan-nette": "^1.0", "tracy/tracy": "^2.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🔑 Nette Security: provides authentication, authorization and a role-based access control management via ACL (Access Control List)", "homepage": "https://nette.org", "keywords": ["Authentication", "acl", "authorization", "nette"], "support": {"issues": "https://github.com/nette/security/issues", "source": "https://github.com/nette/security/tree/v3.2.1"}, "time": "2024-11-04T12:25:05+00:00"}, {"name": "nette/tokenizer", "version": "v3.1.1", "source": {"type": "git", "url": "https://github.com/nette/tokenizer.git", "reference": "370c5e4e2e10eb4d3e406d3a90526f821de98190"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/tokenizer/zipball/370c5e4e2e10eb4d3e406d3a90526f821de98190", "reference": "370c5e4e2e10eb4d3e406d3a90526f821de98190", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"nette/tester": "~2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "<PERSON><PERSON>", "homepage": "https://nette.org", "support": {"source": "https://github.com/nette/tokenizer/tree/v3.1.1"}, "abandoned": true, "time": "2022-02-09T22:28:54+00:00"}, {"name": "nette/utils", "version": "v4.0.5", "source": {"type": "git", "url": "https://github.com/nette/utils.git", "reference": "736c567e257dbe0fcf6ce81b4d6dbe05c6899f96"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/utils/zipball/736c567e257dbe0fcf6ce81b4d6dbe05c6899f96", "reference": "736c567e257dbe0fcf6ce81b4d6dbe05c6899f96", "shasum": ""}, "require": {"php": "8.0 - 8.4"}, "conflict": {"nette/finder": "<3", "nette/schema": "<1.2.2"}, "require-dev": {"jetbrains/phpstorm-attributes": "dev-master", "nette/tester": "^2.5", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.9"}, "suggest": {"ext-gd": "to use Image", "ext-iconv": "to use Strings::webalize(), to<PERSON>cii(), chr() and reverse()", "ext-intl": "to use Strings::webalize(), toAscii(), normalize() and compare()", "ext-json": "to use Nette\\Utils\\Json", "ext-mbstring": "to use Strings::lower() etc...", "ext-tokenizer": "to use Nette\\Utils\\Reflection::getUseStatements()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🛠  Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "homepage": "https://nette.org", "keywords": ["array", "core", "datetime", "images", "json", "nette", "paginator", "password", "slugify", "string", "unicode", "utf-8", "utility", "validation"], "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v4.0.5"}, "time": "2024-08-07T15:39:19+00:00"}, {"name": "nettrine/annotations", "version": "v0.7.0", "source": {"type": "git", "url": "https://github.com/contributte/doctrine-annotations.git", "reference": "fbb06d156a4edcbf37e4154e5b4ede079136388b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/doctrine-annotations/zipball/fbb06d156a4edcbf37e4154e5b4ede079136388b", "reference": "fbb06d156a4edcbf37e4154e5b4ede079136388b", "shasum": ""}, "require": {"contributte/di": "^0.5.0", "doctrine/annotations": "^1.6.1", "nettrine/cache": "^0.3.0", "php": ">=7.2"}, "require-dev": {"ninjify/qa": "^0.10.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan-deprecation-rules": "^0.11.0", "phpstan/phpstan-nette": "^0.11.0", "phpstan/phpstan-shim": "^0.11.5", "phpstan/phpstan-strict-rules": "^0.11.0", "phpunit/phpunit": "^8.1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.8.x-dev"}}, "autoload": {"psr-4": {"Nettrine\\Annotations\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}, {"name": "<PERSON><PERSON>", "homepage": "https://github.com/mabar"}], "description": "Doctrine Annotations for Nette Framework", "homepage": "https://github.com/nettrine/annotations", "keywords": ["annotations", "doctrine", "nette", "nettrine"], "support": {"issues": "https://github.com/contributte/doctrine-annotations/issues", "source": "https://github.com/contributte/doctrine-annotations/tree/v0.7.0"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2020-12-10T18:01:50+00:00"}, {"name": "nettrine/cache", "version": "v0.3.0", "source": {"type": "git", "url": "https://github.com/contributte/doctrine-cache.git", "reference": "8a58596de24cdd61e45866ef8f35788675f6d2bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/doctrine-cache/zipball/8a58596de24cdd61e45866ef8f35788675f6d2bc", "reference": "8a58596de24cdd61e45866ef8f35788675f6d2bc", "shasum": ""}, "require": {"contributte/di": "^0.5.0", "doctrine/cache": "^1.8.0", "php": ">=7.2"}, "require-dev": {"ninjify/qa": "^0.9.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan-deprecation-rules": "^0.11.0", "phpstan/phpstan-nette": "^0.11.0", "phpstan/phpstan-shim": "^0.11.5", "phpstan/phpstan-strict-rules": "^0.11.0", "phpunit/phpunit": "^8.1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.3.x-dev"}}, "autoload": {"psr-4": {"Nettrine\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MPL-2.0"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://github.com/mabar"}], "description": "Doctrine Cache for Nette Framework", "homepage": "https://github.com/nettrine/cache", "keywords": ["cache", "doctrine", "nette", "nettrine"], "support": {"issues": "https://github.com/contributte/doctrine-cache/issues", "source": "https://github.com/contributte/doctrine-cache/tree/v0.3.0"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2020-12-10T17:56:32+00:00"}, {"name": "nextras/dbal", "version": "v5.0.1", "source": {"type": "git", "url": "https://github.com/nextras/dbal.git", "reference": "8f2e51c40e7eaba562c3b97231cd77c62474071b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nextras/dbal/zipball/8f2e51c40e7eaba562c3b97231cd77c62474071b", "reference": "8f2e51c40e7eaba562c3b97231cd77c62474071b", "shasum": ""}, "require": {"ext-json": "*", "php": ">=8.1"}, "require-dev": {"mockery/mockery": "~1.5", "nette/caching": "~3.0", "nette/di": "~3.0", "nette/finder": "~2.5 || ~3.0", "nette/neon": "~3.0", "nette/tester": "~2.4", "nette/utils": "~3.0 || ~4.0", "nextras/multi-query-parser": "~1.0", "phpstan/extension-installer": "1.4.3", "phpstan/phpstan": "1.12.7", "phpstan/phpstan-deprecation-rules": "1.2.1", "phpstan/phpstan-strict-rules": "1.6.1", "symfony/config": "~4.4 || ~5.0", "symfony/dependency-injection": "~4.4 || ~5.0", "symfony/http-kernel": "~4.4 || ~5.0", "tracy/tracy": "~2.7"}, "suggest": {"nextras/multi-query-parser": "Install this to support SQL file import."}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"psr-4": {"Nextras\\Dbal\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Nextras Project", "homepage": "https://github.com/nextras/dbal/graphs/contributors"}], "description": "Nextras database abstraction layer", "homepage": "https://github.com/nextras/dbal", "keywords": ["database", "dbal", "nextras"], "support": {"issues": "https://github.com/nextras/dbal/issues", "source": "https://github.com/nextras/dbal/tree/v5.0.1"}, "funding": [{"url": "https://github.com/hrach", "type": "github"}], "time": "2024-11-09T19:05:50+00:00"}, {"name": "nextras/migrations", "version": "v3.3.1", "source": {"type": "git", "url": "https://github.com/nextras/migrations.git", "reference": "c1a201d8b845edfc3bb210a9c5f19375743af4cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nextras/migrations/zipball/c1a201d8b845edfc3bb210a9c5f19375743af4cf", "reference": "c1a201d8b845edfc3bb210a9c5f19375743af4cf", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"dibi/dibi": "~3.0 | ~4.0 | ~5.0", "doctrine/cache": "~1.11 | ~2.0", "doctrine/dbal": "~2.5 | ~3.0", "doctrine/orm": "~2.5", "ext-openssl": "*", "mockery/mockery": "~1.3", "nette/database": "~2.4 | ~3.0", "nette/di": "~2.4.10 | ~3.0", "nette/tester": "~2.3", "nette/utils": "~2.3 | ~3.0 | ~4.0", "nextras/dbal": "~1.0 | ~2.0 | ~3.0 | ~4.0 | ~5.0@dev", "symfony/config": "~2.6 | ~3.0 | ~4.0 | ~5.0 | ~6.0 | ~7.0", "symfony/console": "~2.6 | ~3.0 | ~4.0 | ~5.0 | ~6.0 | ~7.0", "symfony/dependency-injection": "~2.6 | ~3.0 | ~4.0 | ~5.0 | ~6.0 | ~7.0", "symfony/framework-bundle": "~2.6 | ~3.0 | ~4.0 | ~5.0 | ~6.0 | ~7.0", "symfony/http-kernel": "~2.6 | ~3.0 | ~4.0 | ~5.0 | ~6.0 | ~7.0", "tracy/tracy": "~2.6"}, "suggest": {"dibi/dibi": "to use DibiAdapter", "doctrine/dbal": "to use DoctrineAdapter", "doctrine/orm": "to generate migrations with Doctrine SchemaTool", "nette/database": "to use NetteAdapter", "nextras/dbal": "to use NextrasAdapter", "symfony/console": "to use Symfony commands"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Nextras\\Migrations\\": "src/"}, "classmap": ["src/exceptions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Database migrations runner", "support": {"issues": "https://github.com/nextras/migrations/issues", "source": "https://github.com/nextras/migrations/tree/v3.3.1"}, "time": "2024-01-25T13:14:56+00:00"}, {"name": "nextras/orm", "version": "v5.0.1", "source": {"type": "git", "url": "https://github.com/nextras/orm.git", "reference": "4e9970096d15e73e8d8bf823e2b030c8c0372c60"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nextras/orm/zipball/4e9970096d15e73e8d8bf823e2b030c8c0372c60", "reference": "4e9970096d15e73e8d8bf823e2b030c8c0372c60", "shasum": ""}, "require": {"ext-ctype": "*", "ext-json": "*", "nette/caching": "~3.2 || ~3.1.3", "nette/utils": "~3.0 || ~4.0", "nextras/dbal": "~5.0", "php": ">=8.1", "phpstan/phpdoc-parser": "^1.33.0 || ^2.0.0"}, "require-dev": {"doctrine/sql-formatter": "^1.5.1", "mockery/mockery": ">=1.5.1", "nette/bootstrap": "~3.1", "nette/di": "^3.1", "nette/neon": "~3.0", "nette/tester": "~2.5", "nextras/multi-query-parser": "~1.0", "nextras/orm-phpstan": "^2.0.0", "phpstan/extension-installer": "1.4.3", "phpstan/phpstan": "2.1.1", "phpstan/phpstan-deprecation-rules": "2.0.1", "phpstan/phpstan-mockery": "2.0.0", "phpstan/phpstan-nette": "2.0.1", "phpstan/phpstan-strict-rules": "2.0.1", "tracy/tracy": "~2.3"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"psr-4": {"Nextras\\Orm\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Nextras Project", "homepage": "https://github.com/nextras/orm/graphs/contributors"}], "description": "Nextras Orm framework", "homepage": "https://github.com/nextras/orm", "keywords": ["database", "nextras", "orm"], "support": {"issues": "https://github.com/nextras/orm/issues", "source": "https://github.com/nextras/orm/tree/v5.0.1"}, "funding": [{"url": "https://github.com/hrach", "type": "github"}], "time": "2025-01-05T19:51:58+00:00"}, {"name": "nyholm/dsn", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/Nyholm/dsn.git", "reference": "9445621b426bac8c0ca161db8cd700da00a4e618"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Nyholm/dsn/zipball/9445621b426bac8c0ca161db8cd700da00a4e618", "reference": "9445621b426bac8c0ca161db8cd700da00a4e618", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"symfony/phpunit-bridge": "^5.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"Nyholm\\Dsn\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Parse your DSN strings in a powerful and flexible way", "homepage": "http://tnyholm.se", "keywords": ["database", "dsn", "dsn parser", "parser"], "support": {"issues": "https://github.com/Nyholm/dsn/issues", "source": "https://github.com/Nyholm/dsn/tree/2.0.1"}, "funding": [{"url": "https://github.com/Nyholm", "type": "github"}], "time": "2021-11-18T09:23:29+00:00"}, {"name": "paragonie/constant_time_encoding", "version": "v3.0.0", "source": {"type": "git", "url": "https://github.com/paragonie/constant_time_encoding.git", "reference": "df1e7fde177501eee2037dd159cf04f5f301a512"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/df1e7fde177501eee2037dd159cf04f5f301a512", "reference": "df1e7fde177501eee2037dd159cf04f5f301a512", "shasum": ""}, "require": {"php": "^8"}, "require-dev": {"phpunit/phpunit": "^9", "vimeo/psalm": "^4|^5"}, "type": "library", "autoload": {"psr-4": {"ParagonIE\\ConstantTime\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com", "role": "Maintainer"}, {"name": "<PERSON> 'Sc00bz' <PERSON>", "email": "<EMAIL>", "homepage": "https://www.tobtu.com", "role": "Original Developer"}], "description": "Constant-time Implementations of RFC 4648 Encoding (Base-64, Base-32, Base-16)", "keywords": ["base16", "base32", "base32_decode", "base32_encode", "base64", "base64_decode", "base64_encode", "bin2hex", "encoding", "hex", "hex2bin", "rfc4648"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/constant_time_encoding/issues", "source": "https://github.com/paragonie/constant_time_encoding"}, "time": "2024-05-08T12:36:18+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "time": "2020-10-15T08:29:30+00:00"}, {"name": "pelago/emogrifier", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/MyIntervals/emogrifier.git", "reference": "6e00d9d8235e8cc8eec857e8dcd6cfeefdfd0cd6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MyIntervals/emogrifier/zipball/6e00d9d8235e8cc8eec857e8dcd6cfeefdfd0cd6", "reference": "6e00d9d8235e8cc8eec857e8dcd6cfeefdfd0cd6", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "~7.3.0 || ~7.4.0 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "sabberworm/php-css-parser": "^8.7.0", "symfony/css-selector": "^4.4.23 || ^5.4.0 || ^6.0.0 || ^7.0.0"}, "require-dev": {"php-parallel-lint/php-parallel-lint": "1.4.0", "phpstan/extension-installer": "1.4.3", "phpstan/phpstan": "1.12.7", "phpstan/phpstan-phpunit": "1.4.0", "phpstan/phpstan-strict-rules": "1.6.1", "phpunit/phpunit": "9.6.21", "rawr/cross-data-providers": "2.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "8.0.x-dev"}}, "autoload": {"psr-4": {"Pelago\\Emogrifier\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "description": "Converts CSS styles into inline style attributes in your HTML code", "homepage": "https://www.myintervals.com/emogrifier.php", "keywords": ["css", "email", "pre-processing"], "support": {"issues": "https://github.com/MyIntervals/emogrifier/issues", "source": "https://github.com/MyIntervals/emogrifier"}, "time": "2024-10-28T16:12:26+00:00"}, {"name": "php-curl-class/php-curl-class", "version": "9.19.2", "source": {"type": "git", "url": "https://github.com/php-curl-class/php-curl-class.git", "reference": "c41efeb4ea2dc3cf8f90f8f967b0fcf45a41e294"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-curl-class/php-curl-class/zipball/c41efeb4ea2dc3cf8f90f8f967b0fcf45a41e294", "reference": "c41efeb4ea2dc3cf8f90f8f967b0fcf45a41e294", "shasum": ""}, "require": {"ext-curl": "*", "php": ">=7.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "*", "ext-gd": "*", "friendsofphp/php-cs-fixer": "*", "phpcompatibility/php-compatibility": "dev-develop", "phpcsstandards/phpcsutils": "@alpha", "phpunit/phpunit": "*", "squizlabs/php_codesniffer": "*", "vimeo/psalm": ">=0.3.63"}, "suggest": {"ext-mbstring": "*"}, "type": "library", "autoload": {"psr-4": {"Curl\\": "src/Curl/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Unlicense"], "authors": [{"name": "<PERSON>"}, {"name": "Contributors", "homepage": "https://github.com/php-curl-class/php-curl-class/graphs/contributors"}], "description": "PHP Curl Class makes it easy to send HTTP requests and integrate with web APIs.", "homepage": "https://github.com/php-curl-class/php-curl-class", "keywords": ["API-Client", "api", "class", "client", "curl", "framework", "http", "http-client", "http-proxy", "json", "php", "php-curl", "php-curl-library", "proxy", "requests", "restful", "web-scraper", "web-scraping ", "web-service", "xml"], "support": {"issues": "https://github.com/php-curl-class/php-curl-class/issues", "source": "https://github.com/php-curl-class/php-curl-class/tree/9.19.2"}, "time": "2024-04-09T18:03:13+00:00"}, {"name": "phpseclib/phpseclib", "version": "3.0.43", "source": {"type": "git", "url": "https://github.com/phpseclib/phpseclib.git", "reference": "709ec107af3cb2f385b9617be72af8cf62441d02"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/709ec107af3cb2f385b9617be72af8cf62441d02", "reference": "709ec107af3cb2f385b9617be72af8cf62441d02", "shasum": ""}, "require": {"paragonie/constant_time_encoding": "^1|^2|^3", "paragonie/random_compat": "^1.4|^2.0|^9.99.99", "php": ">=5.6.1"}, "require-dev": {"phpunit/phpunit": "*"}, "suggest": {"ext-dom": "Install the DOM extension to load XML formatted public keys.", "ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations."}, "type": "library", "autoload": {"files": ["phpseclib/bootstrap.php"], "psr-4": {"phpseclib3\\": "phpseclib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "PHP Secure Communications Library - Pure-PHP implementations of RSA, AES, SSH2, SFTP, X.509 etc.", "homepage": "http://phpseclib.sourceforge.net", "keywords": ["BigInteger", "aes", "asn.1", "asn1", "blowfish", "crypto", "cryptography", "encryption", "rsa", "security", "sftp", "signature", "signing", "ssh", "twofish", "x.509", "x509"], "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.43"}, "funding": [{"url": "https://github.com/terrafrost", "type": "github"}, {"url": "https://www.patreon.com/phpseclib", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/phpseclib/phpseclib", "type": "tidelift"}], "time": "2024-12-14T21:12:59+00:00"}, {"name": "phpstan/phpdoc-parser", "version": "1.33.0", "source": {"type": "git", "url": "https://github.com/phpstan/phpdoc-parser.git", "reference": "82a311fd3690fb2bf7b64d5c98f912b3dd746140"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/82a311fd3690fb2bf7b64d5c98f912b3dd746140", "reference": "82a311fd3690fb2bf7b64d5c98f912b3dd746140", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/annotations": "^2.0", "nikic/php-parser": "^4.15", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.5", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.0", "phpunit/phpunit": "^9.5", "symfony/process": "^5.2"}, "type": "library", "autoload": {"psr-4": {"PHPStan\\PhpDocParser\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPDoc parser with support for nullable, intersection and generic types", "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.33.0"}, "time": "2024-10-13T11:25:22+00:00"}, {"name": "predis/predis", "version": "v2.3.0", "source": {"type": "git", "url": "https://github.com/predis/predis.git", "reference": "bac46bfdb78cd6e9c7926c697012aae740cb9ec9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/predis/predis/zipball/bac46bfdb78cd6e9c7926c697012aae740cb9ec9", "reference": "bac46bfdb78cd6e9c7926c697012aae740cb9ec9", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.3", "phpstan/phpstan": "^1.9", "phpunit/phpunit": "^8.0 || ^9.4"}, "suggest": {"ext-relay": "Faster connection with in-memory caching (>=0.6.2)"}, "type": "library", "autoload": {"psr-4": {"Predis\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://till.im", "role": "Maintainer"}], "description": "A flexible and feature-complete Redis client for PHP.", "homepage": "http://github.com/predis/predis", "keywords": ["nosql", "predis", "redis"], "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v2.3.0"}, "funding": [{"url": "https://github.com/sponsors/tillkruss", "type": "github"}], "time": "2024-11-21T20:00:02+00:00"}, {"name": "prewk/xml-string-streamer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/prewk/xml-string-streamer.git", "reference": "a1f4a5250d548d06fd385fa86241c0c2d7d607fd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/prewk/xml-string-streamer/zipball/a1f4a5250d548d06fd385fa86241c0c2d7d607fd", "reference": "a1f4a5250d548d06fd385fa86241c0c2d7d607fd", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"mockery/mockery": "^1.3.0", "phpunit/phpunit": "^8.5.8 || ^9.5.0", "prewk/xml-faker": "^0.0.2"}, "type": "library", "autoload": {"psr-4": {"Prewk\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "prewk", "email": "<EMAIL>"}], "description": "Stream large XML files with low memory consumption", "homepage": "https://github.com/prewk/xml-string-streamer", "support": {"issues": "https://github.com/prewk/xml-string-streamer/issues", "source": "https://github.com/prewk/xml-string-streamer/tree/1.2.3"}, "time": "2023-03-03T19:01:39+00:00"}, {"name": "psr/cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/3.0.0"}, "time": "2021-02-03T23:26:27+00:00"}, {"name": "psr/clock", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/clock.git", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/clock/zipball/e41a24703d4560fd0acb709162f73b8adfc3aa0d", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for reading the clock.", "homepage": "https://github.com/php-fig/clock", "keywords": ["clock", "now", "psr", "psr-20", "time"], "support": {"issues": "https://github.com/php-fig/clock/issues", "source": "https://github.com/php-fig/clock/tree/1.0.0"}, "time": "2022-11-25T14:36:26+00:00"}, {"name": "psr/container", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "2.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "time": "2023-04-04T09:54:51+00:00"}, {"name": "psr/log", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.2"}, "time": "2024-09-11T13:17:53+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "react/promise", "version": "v2.11.0", "source": {"type": "git", "url": "https://github.com/reactphp/promise.git", "reference": "1a8460931ea36dc5c76838fec5734d55c88c6831"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/promise/zipball/1a8460931ea36dc5c76838fec5734d55c88c6831", "reference": "1a8460931ea36dc5c76838fec5734d55c88c6831", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"React\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "A lightweight implementation of CommonJS Promises/A for PHP", "keywords": ["promise", "promises"], "support": {"issues": "https://github.com/reactphp/promise/issues", "source": "https://github.com/reactphp/promise/tree/v2.11.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2023-11-16T16:16:50+00:00"}, {"name": "rikudou/czqrpayment", "version": "v5.3.1", "source": {"type": "git", "url": "https://github.com/RikudouSage/QrPaymentCZ.git", "reference": "f8e0ecbbdb6d30bafb50a833cc7cfe4f575b82a4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/RikudouSage/QrPaymentCZ/zipball/f8e0ecbbdb6d30bafb50a833cc7cfe4f575b82a4", "reference": "f8e0ecbbdb6d30bafb50a833cc7cfe4f575b82a4", "shasum": ""}, "require": {"php": "^7.3 | ^8.0", "rikudou/iban": "^1.1.1", "rikudou/qr-payment-interface": "^1.0", "rikudou/qr-payment-qr-code-provider": "^1.2"}, "require-dev": {"endroid/qr-code": "^3.2", "friendsofphp/php-cs-fixer": "^2.18", "php-coveralls/php-coveralls": "^2.1", "phpstan/phpstan": "^0.12.82", "phpunit/phpunit": "^9.5"}, "suggest": {"endroid/qr-code": "For getting the qr code image"}, "type": "library", "autoload": {"psr-4": {"Rikudou\\CzQrPayment\\": "src/", "rikudou\\CzQrPayment\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Dominik Chrástecký", "email": "<EMAIL>"}], "description": "QR payment library for Czech accounts", "homepage": "https://github.com/RikudouSage/QrPaymentCZ", "keywords": ["payment", "qr"], "support": {"issues": "https://github.com/RikudouSage/QrPaymentCZ/issues", "source": "https://github.com/RikudouSage/QrPaymentCZ/tree/v5.3.1"}, "funding": [{"url": "https://ko-fi.com/dominik_ch", "type": "ko_fi"}, {"url": "https://liberapay.com/dominik_ch", "type": "liberapay"}], "time": "2024-12-20T23:39:10+00:00"}, {"name": "rikudou/iban", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/RikudouSage/IBAN.git", "reference": "7fe69bf9274792c37d5a8d9d38ef5cb000f8377a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/RikudouSage/IBAN/zipball/7fe69bf9274792c37d5a8d9d38ef5cb000f8377a", "reference": "7fe69bf9274792c37d5a8d9d38ef5cb000f8377a", "shasum": ""}, "require": {"php": "^7.3 | ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0", "php-coveralls/php-coveralls": "^2.1", "phpstan/phpstan": "^0.12.63", "phpunit/phpunit": "^9"}, "type": "library", "autoload": {"psr-4": {"Rikudou\\Iban\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["WTFPL"], "authors": [{"name": "Dominik Chrástecký", "email": "<EMAIL>"}], "description": "Library for working with IBANs", "homepage": "https://github.com/RikudouSage/IBAN", "keywords": ["IBAN"], "support": {"issues": "https://github.com/RikudouSage/IBAN/issues", "source": "https://github.com/RikudouSage/IBAN/tree/v1.3.0"}, "funding": [{"url": "https://ko-fi.com/dominik_ch", "type": "ko_fi"}, {"url": "https://liberapay.com/dominik_ch", "type": "liberapay"}], "time": "2023-09-11T07:54:00+00:00"}, {"name": "rikudou/qr-payment-interface", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/RikudouSage/QrPaymentInterface.git", "reference": "752f7a6bf1190c7d65ead90b5989f61927436c89"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/RikudouSage/QrPaymentInterface/zipball/752f7a6bf1190c7d65ead90b5989f61927436c89", "reference": "752f7a6bf1190c7d65ead90b5989f61927436c89", "shasum": ""}, "require": {"php": ">=7.1", "rikudou/iban": "^1.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.16"}, "type": "library", "autoload": {"psr-4": {"Rikudou\\QrPayment\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Dominik Chrástecký", "email": "<EMAIL>"}], "description": "Common interface for my qr payment libraries", "keywords": ["payment", "qr"], "support": {"issues": "https://github.com/RikudouSage/QrPaymentInterface/issues", "source": "https://github.com/RikudouSage/QrPaymentInterface/tree/v1.1.0"}, "time": "2021-04-27T20:05:08+00:00"}, {"name": "rikudou/qr-payment-qr-code-provider", "version": "v1.2.0", "source": {"type": "git", "url": "https://github.com/RikudouSage/QrPaymentQrCodeProvider.git", "reference": "d233c4bedeecf2ff7cd7e7d4ec7f4ad4a5eb4b64"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/RikudouSage/QrPaymentQrCodeProvider/zipball/d233c4bedeecf2ff7cd7e7d4ec7f4ad4a5eb4b64", "reference": "d233c4bedeecf2ff7cd7e7d4ec7f4ad4a5eb4b64", "shasum": ""}, "require": {"php": ">=7.3", "rikudou/qr-payment-interface": "^1.0"}, "require-dev": {"bacon/bacon-qr-code": "^3.0", "chillerlan/php-qrcode": "^5.0", "endroid/qr-code": "^6.0", "friendsofphp/php-cs-fixer": "^3.1", "phpstan/phpstan": "^0.12.99"}, "type": "library", "autoload": {"psr-4": {"Rikudou\\QrPaymentQrCodeProvider\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "QR code provider for QR payment libraries", "support": {"issues": "https://github.com/RikudouSage/QrPaymentQrCodeProvider/issues", "source": "https://github.com/RikudouSage/QrPaymentQrCodeProvider/tree/v1.2.0"}, "time": "2024-12-20T23:25:11+00:00"}, {"name": "rikudou/skqrpayment", "version": "v4.2.2", "source": {"type": "git", "url": "https://github.com/RikudouSage/QrPaymentSK.git", "reference": "777fa98caaff3f10fb43f3cf67a8464c547e0550"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/RikudouSage/QrPaymentSK/zipball/777fa98caaff3f10fb43f3cf67a8464c547e0550", "reference": "777fa98caaff3f10fb43f3cf67a8464c547e0550", "shasum": ""}, "require": {"php": "^7.3 || ^8.0", "rikudou/iban": "^1.0", "rikudou/qr-payment-interface": "^1.0", "rikudou/qr-payment-qr-code-provider": "^1.2"}, "require-dev": {"endroid/qr-code": "^3.9", "friendsofphp/php-cs-fixer": "^2.16", "phpstan/phpstan": "^0.12.32", "phpunit/phpunit": "^8.5"}, "suggest": {"endroid/qr-code": "For getting QR code image", "rikudou/pay-by-square-decoder": "If you want to decode Pay By Square encoded data"}, "type": "library", "autoload": {"psr-4": {"rikudou\\SkQrPayment\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Dominik Chrástecký", "email": "<EMAIL>"}], "description": "QR payment library for Slovak accounts", "homepage": "https://github.com/RikudouSage/QrPaymentSK", "keywords": ["payment", "qr"], "support": {"issues": "https://github.com/RikudouSage/QrPaymentSK/issues", "source": "https://github.com/RikudouSage/QrPaymentSK/tree/v4.2.2"}, "funding": [{"url": "https://ko-fi.com/dominik_ch", "type": "ko_fi"}, {"url": "https://liberapay.com/dominik_ch", "type": "liberapay"}], "time": "2024-12-20T23:34:26+00:00"}, {"name": "ruflin/elastica", "version": "7.3.2", "source": {"type": "git", "url": "https://github.com/ruflin/Elastica.git", "reference": "84ba137678707a1aa4242d12bad891dc38fa2608"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ruflin/Elastica/zipball/84ba137678707a1aa4242d12bad891dc38fa2608", "reference": "84ba137678707a1aa4242d12bad891dc38fa2608", "shasum": ""}, "require": {"elasticsearch/elasticsearch": "^7.10", "ext-json": "*", "nyholm/dsn": "^2.0.0", "php": "^7.2 || ^8.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "symfony/deprecation-contracts": "^2.2 || ^3.0", "symfony/polyfill-php73": "^1.19"}, "require-dev": {"aws/aws-sdk-php": "^3.155", "guzzlehttp/guzzle": "^6.3 || ^7.2", "phpstan/phpstan": "^1.5", "phpstan/phpstan-phpunit": "^1.1", "phpunit/phpunit": "^8.5.8 || ^9.4", "symfony/phpunit-bridge": "^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow using IAM authentication with Amazon ElasticSearch Service", "guzzlehttp/guzzle": "Allow using guzzle as transport", "monolog/monolog": "Logging request"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.0.x-dev"}}, "autoload": {"psr-4": {"Elastica\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://ruflin.com/"}], "description": "Elasticsearch Client", "homepage": "http://elastica.io/", "keywords": ["client", "search"], "support": {"issues": "https://github.com/ruflin/Elastica/issues", "source": "https://github.com/ruflin/Elastica/tree/7.3.2"}, "time": "2024-03-11T14:11:50+00:00"}, {"name": "sabberworm/php-css-parser", "version": "v8.7.0", "source": {"type": "git", "url": "https://github.com/MyIntervals/PHP-CSS-Parser.git", "reference": "f414ff953002a9b18e3a116f5e462c56f21237cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MyIntervals/PHP-CSS-Parser/zipball/f414ff953002a9b18e3a116f5e462c56f21237cf", "reference": "f414ff953002a9b18e3a116f5e462c56f21237cf", "shasum": ""}, "require": {"ext-iconv": "*", "php": "^5.6.20 || ^7.0.0 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "require-dev": {"phpunit/phpunit": "5.7.27 || 6.5.14 || 7.5.20 || 8.5.40"}, "suggest": {"ext-mbstring": "for parsing UTF-8 CSS"}, "type": "library", "extra": {"branch-alias": {"dev-main": "9.0.x-dev"}}, "autoload": {"psr-4": {"Sabberworm\\CSS\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Parser for CSS Files written in PHP", "homepage": "https://www.sabberworm.com/blog/2010/6/10/php-css-parser", "keywords": ["css", "parser", "stylesheet"], "support": {"issues": "https://github.com/MyIntervals/PHP-CSS-Parser/issues", "source": "https://github.com/MyIntervals/PHP-CSS-Parser/tree/v8.7.0"}, "time": "2024-10-27T17:38:32+00:00"}, {"name": "sentry/sdk", "version": "4.0.0", "source": {"type": "git", "url": "https://github.com/getsentry/sentry-php-sdk.git", "reference": "fcbca864e8d1dc712f3ecfaa95ea89d024fb2e53"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/getsentry/sentry-php-sdk/zipball/fcbca864e8d1dc712f3ecfaa95ea89d024fb2e53", "reference": "fcbca864e8d1dc712f3ecfaa95ea89d024fb2e53", "shasum": ""}, "require": {"sentry/sentry": "^4.0"}, "type": "metapackage", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Sentry", "email": "<EMAIL>"}], "description": "This is a meta package of sentry/sentry. We recommend using sentry/sentry directly.", "homepage": "http://sentry.io", "keywords": ["crash-reporting", "crash-reports", "error-handler", "error-monitoring", "log", "logging", "sentry"], "support": {"issues": "https://github.com/getsentry/sentry-php-sdk/issues", "source": "https://github.com/getsentry/sentry-php-sdk/tree/4.0.0"}, "funding": [{"url": "https://sentry.io/", "type": "custom"}, {"url": "https://sentry.io/pricing/", "type": "custom"}], "time": "2023-11-06T10:23:19+00:00"}, {"name": "sentry/sentry", "version": "4.10.0", "source": {"type": "git", "url": "https://github.com/getsentry/sentry-php.git", "reference": "2af937d47d8aadb8dab0b1d7b9557e495dd12856"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/getsentry/sentry-php/zipball/2af937d47d8aadb8dab0b1d7b9557e495dd12856", "reference": "2af937d47d8aadb8dab0b1d7b9557e495dd12856", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "guzzlehttp/psr7": "^1.8.4|^2.1.1", "jean85/pretty-package-versions": "^1.5|^2.0.4", "php": "^7.2|^8.0", "psr/log": "^1.0|^2.0|^3.0", "symfony/options-resolver": "^4.4.30|^5.0.11|^6.0|^7.0"}, "conflict": {"raven/raven": "*"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.4", "guzzlehttp/promises": "^2.0.3", "guzzlehttp/psr7": "^1.8.4|^2.1.1", "monolog/monolog": "^1.6|^2.0|^3.0", "phpbench/phpbench": "^1.0", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^8.5|^9.6", "symfony/phpunit-bridge": "^5.2|^6.0|^7.0", "vimeo/psalm": "^4.17"}, "suggest": {"monolog/monolog": "Allow sending log messages to Sentry by using the included Monolog handler."}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"Sentry\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Sentry", "email": "<EMAIL>"}], "description": "PHP SDK for Sentry (http://sentry.io)", "homepage": "http://sentry.io", "keywords": ["crash-reporting", "crash-reports", "error-handler", "error-monitoring", "log", "logging", "profiling", "sentry", "tracing"], "support": {"issues": "https://github.com/getsentry/sentry-php/issues", "source": "https://github.com/getsentry/sentry-php/tree/4.10.0"}, "funding": [{"url": "https://sentry.io/", "type": "custom"}, {"url": "https://sentry.io/pricing/", "type": "custom"}], "time": "2024-11-06T07:44:19+00:00"}, {"name": "setasign/fpdi", "version": "v2.6.4", "source": {"type": "git", "url": "https://github.com/Setasign/FPDI.git", "reference": "4b53852fde2734ec6a07e458a085db627c60eada"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Setasign/FPDI/zipball/4b53852fde2734ec6a07e458a085db627c60eada", "reference": "4b53852fde2734ec6a07e458a085db627c60eada", "shasum": ""}, "require": {"ext-zlib": "*", "php": "^7.1 || ^8.0"}, "conflict": {"setasign/tfpdf": "<1.31"}, "require-dev": {"phpunit/phpunit": "^7", "setasign/fpdf": "~1.8.6", "setasign/tfpdf": "~1.33", "squizlabs/php_codesniffer": "^3.5", "tecnickcom/tcpdf": "^6.8"}, "suggest": {"setasign/fpdf": "FPDI will extend this class but as it is also possible to use TCPDF or tFPDF as an alternative. There's no fixed dependency configured."}, "type": "library", "autoload": {"psr-4": {"setasign\\Fpdi\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}], "description": "FPDI is a collection of PHP classes facilitating developers to read pages from existing PDF documents and use them as templates in FPDF. Because it is also possible to use FPDI with TCPDF, there are no fixed dependencies defined. Please see suggestions for packages which evaluates the dependencies automatically.", "homepage": "https://www.setasign.com/fpdi", "keywords": ["fpdf", "fpdi", "pdf"], "support": {"issues": "https://github.com/Setasign/FPDI/issues", "source": "https://github.com/Setasign/FPDI/tree/v2.6.4"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/setasign/fpdi", "type": "tidelift"}], "time": "2025-08-05T09:57:14+00:00"}, {"name": "symfony/amqp-messenger", "version": "v7.2.3", "source": {"type": "git", "url": "https://github.com/symfony/amqp-messenger.git", "reference": "a90a95cef8e8290157aa97a74e4f03c34b575e12"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/amqp-messenger/zipball/a90a95cef8e8290157aa97a74e4f03c34b575e12", "reference": "a90a95cef8e8290157aa97a74e4f03c34b575e12", "shasum": ""}, "require": {"ext-amqp": "*", "php": ">=8.2", "symfony/messenger": "^6.4|^7.0"}, "require-dev": {"symfony/event-dispatcher": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/property-access": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0"}, "type": "symfony-messenger-bridge", "autoload": {"psr-4": {"Symfony\\Component\\Messenger\\Bridge\\Amqp\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony AMQP extension Messenger Bridge", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/amqp-messenger/tree/v7.2.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-17T10:56:55+00:00"}, {"name": "symfony/cache", "version": "v6.4.16", "source": {"type": "git", "url": "https://github.com/symfony/cache.git", "reference": "70d60e9a3603108563010f8592dff15a6f15dfae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache/zipball/70d60e9a3603108563010f8592dff15a6f15dfae", "reference": "70d60e9a3603108563010f8592dff15a6f15dfae", "shasum": ""}, "require": {"php": ">=8.1", "psr/cache": "^2.0|^3.0", "psr/log": "^1.1|^2|^3", "symfony/cache-contracts": "^2.5|^3", "symfony/service-contracts": "^2.5|^3", "symfony/var-exporter": "^6.3.6|^7.0"}, "conflict": {"doctrine/dbal": "<2.13.1", "symfony/dependency-injection": "<5.4", "symfony/http-kernel": "<5.4", "symfony/var-dumper": "<5.4"}, "provide": {"psr/cache-implementation": "2.0|3.0", "psr/simple-cache-implementation": "1.0|2.0|3.0", "symfony/cache-implementation": "1.1|2.0|3.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/dbal": "^2.13.1|^3|^4", "predis/predis": "^1.1|^2.0", "psr/simple-cache": "^1.0|^2.0|^3.0", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/filesystem": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "classmap": ["Traits/ValueWrapper.php"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides extended PSR-6, PSR-16 (and tags) implementations", "homepage": "https://symfony.com", "keywords": ["caching", "psr6"], "support": {"source": "https://github.com/symfony/cache/tree/v6.4.16"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-20T10:10:54+00:00"}, {"name": "symfony/cache-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/cache-contracts.git", "reference": "15a4f8e5cd3bce9aeafc882b1acab39ec8de2c1b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache-contracts/zipball/15a4f8e5cd3bce9aeafc882b1acab39ec8de2c1b", "reference": "15a4f8e5cd3bce9aeafc882b1acab39ec8de2c1b", "shasum": ""}, "require": {"php": ">=8.1", "psr/cache": "^3.0"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Cache\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to caching", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/cache-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/clock", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/clock.git", "reference": "b81435fbd6648ea425d1ee96a2d8e68f4ceacd24"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/clock/zipball/b81435fbd6648ea425d1ee96a2d8e68f4ceacd24", "reference": "b81435fbd6648ea425d1ee96a2d8e68f4ceacd24", "shasum": ""}, "require": {"php": ">=8.2", "psr/clock": "^1.0", "symfony/polyfill-php83": "^1.28"}, "provide": {"psr/clock-implementation": "1.0"}, "type": "library", "autoload": {"files": ["Resources/now.php"], "psr-4": {"Symfony\\Component\\Clock\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Decouples applications from the system clock", "homepage": "https://symfony.com", "keywords": ["clock", "psr20", "time"], "support": {"source": "https://github.com/symfony/clock/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/config", "version": "v6.4.14", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "4e55e7e4ffddd343671ea972216d4509f46c22ef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/4e55e7e4ffddd343671ea972216d4509f46c22ef", "reference": "4e55e7e4ffddd343671ea972216d4509f46c22ef", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/filesystem": "^5.4|^6.0|^7.0", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/finder": "<5.4", "symfony/service-contracts": "<2.5"}, "require-dev": {"symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps you find, load, combine, autofill and validate configuration values of any kind", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/config/tree/v6.4.14"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-04T11:33:53+00:00"}, {"name": "symfony/console", "version": "v6.4.17", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "799445db3f15768ecc382ac5699e6da0520a0a04"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/799445db3f15768ecc382ac5699e6da0520a0a04", "reference": "799445db3f15768ecc382ac5699e6da0520a0a04", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^5.4|^6.0|^7.0"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/dotenv": "<5.4", "symfony/event-dispatcher": "<5.4", "symfony/lock": "<5.4", "symfony/process": "<5.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v6.4.17"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-07T12:07:30+00:00"}, {"name": "symfony/css-selector", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "601a5ce9aaad7bf10797e3663faefce9e26c24e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/601a5ce9aaad7bf10797e3663faefce9e26c24e2", "reference": "601a5ce9aaad7bf10797e3663faefce9e26c24e2", "shasum": ""}, "require": {"php": ">=8.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts CSS selectors to XPath expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/css-selector/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6", "reference": "74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/event-dispatcher", "version": "v6.4.13", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e", "reference": "0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e", "shasum": ""}, "require": {"php": ">=8.1", "symfony/event-dispatcher-contracts": "^2.5|^3"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/service-contracts": "<2.5"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/error-handler": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.4.13"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:18:03+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "7642f5e970b672283b7823222ae8ef8bbc160b9f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/7642f5e970b672283b7823222ae8ef8bbc160b9f", "reference": "7642f5e970b672283b7823222ae8ef8bbc160b9f", "shasum": ""}, "require": {"php": ">=8.1", "psr/event-dispatcher": "^1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/filesystem", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "b8dce482de9d7c9fe2891155035a7248ab5c7fdb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/b8dce482de9d7c9fe2891155035a7248ab5c7fdb", "reference": "b8dce482de9d7c9fe2891155035a7248ab5c7fdb", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8"}, "require-dev": {"symfony/process": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-25T15:15:23+00:00"}, {"name": "symfony/lock", "version": "v6.4.13", "source": {"type": "git", "url": "https://github.com/symfony/lock.git", "reference": "a69c3dd151ab7e14925f119164cfdf65d55392a4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/lock/zipball/a69c3dd151ab7e14925f119164cfdf65d55392a4", "reference": "a69c3dd151ab7e14925f119164cfdf65d55392a4", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"doctrine/dbal": "<2.13", "symfony/cache": "<6.2"}, "require-dev": {"doctrine/dbal": "^2.13|^3|^4", "predis/predis": "^1.1|^2.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Lock\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Creates and manages locks, a mechanism to provide exclusive access to a shared resource", "homepage": "https://symfony.com", "keywords": ["cas", "flock", "locking", "mutex", "redlock", "semaphore"], "support": {"source": "https://github.com/symfony/lock/tree/v6.4.13"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-25T15:19:46+00:00"}, {"name": "symfony/messenger", "version": "v6.4.16", "source": {"type": "git", "url": "https://github.com/symfony/messenger.git", "reference": "b20092876c3d23c172a6469f9c0d7ef1de445257"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/messenger/zipball/b20092876c3d23c172a6469f9c0d7ef1de445257", "reference": "b20092876c3d23c172a6469f9c0d7ef1de445257", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^1|^2|^3", "symfony/clock": "^6.3|^7.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"symfony/console": "<6.3", "symfony/event-dispatcher": "<5.4", "symfony/event-dispatcher-contracts": "<2.5", "symfony/framework-bundle": "<5.4", "symfony/http-kernel": "<5.4", "symfony/serializer": "<5.4"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/console": "^6.3|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/property-access": "^5.4|^6.0|^7.0", "symfony/rate-limiter": "^5.4|^6.0|^7.0", "symfony/routing": "^5.4|^6.0|^7.0", "symfony/serializer": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/validator": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Messenger\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps applications send and receive messages to/from other applications or via message queues", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/messenger/tree/v6.4.16"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-25T02:02:03+00:00"}, {"name": "symfony/options-resolver", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "7da8fbac9dcfef75ffc212235d76b2754ce0cf50"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/7da8fbac9dcfef75ffc212235d76b2754ce0cf50", "reference": "7da8fbac9dcfef75ffc212235d76b2754ce0cf50", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an improved replacement for the array_replace PHP function", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "support": {"source": "https://github.com/symfony/options-resolver/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-20T11:17:29+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/a3cc8b044a6ea513310cbd48ef7333b384945638", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/85181ba99b2345b0ef10ce42ecac37612d9fd341", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "0f68c03565dcaaf25a890667542e8bd75fe7e5bb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/0f68c03565dcaaf25a890667542e8bd75fe7e5bb", "reference": "0f68c03565dcaaf25a890667542e8bd75fe7e5bb", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php83", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php83.git", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php83/zipball/2fb86d65e2d424369ad2905e83b236a8805ba491", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php83\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php83/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/property-access", "version": "v6.4.13", "source": {"type": "git", "url": "https://github.com/symfony/property-access.git", "reference": "8cc779d88d12e440adaa26387bcfc25744064afe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-access/zipball/8cc779d88d12e440adaa26387bcfc25744064afe", "reference": "8cc779d88d12e440adaa26387bcfc25744064afe", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/property-info": "^5.4|^6.0|^7.0"}, "require-dev": {"symfony/cache": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyAccess\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides functions to read and write from/to an object or array using a simple string notation", "homepage": "https://symfony.com", "keywords": ["access", "array", "extraction", "index", "injection", "object", "property", "property-path", "reflection"], "support": {"source": "https://github.com/symfony/property-access/tree/v6.4.13"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:18:03+00:00"}, {"name": "symfony/property-info", "version": "v7.2.2", "source": {"type": "git", "url": "https://github.com/symfony/property-info.git", "reference": "1dfeb0dac7a99f7b3be42db9ccc299c5a6483fcf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-info/zipball/1dfeb0dac7a99f7b3be42db9ccc299c5a6483fcf", "reference": "1dfeb0dac7a99f7b3be42db9ccc299c5a6483fcf", "shasum": ""}, "require": {"php": ">=8.2", "symfony/string": "^6.4|^7.0", "symfony/type-info": "~7.1.9|^7.2.2"}, "conflict": {"phpdocumentor/reflection-docblock": "<5.2", "phpdocumentor/type-resolver": "<1.5.1", "symfony/dependency-injection": "<6.4"}, "require-dev": {"phpdocumentor/reflection-docblock": "^5.2", "phpstan/phpdoc-parser": "^1.0|^2.0", "symfony/cache": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyInfo\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Extracts information about PHP class' properties using metadata of popular sources", "homepage": "https://symfony.com", "keywords": ["doctrine", "phpdoc", "property", "symfony", "type", "validator"], "support": {"source": "https://github.com/symfony/property-info/tree/v7.2.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-31T11:04:50+00:00"}, {"name": "symfony/redis-messenger", "version": "v5.4.48", "source": {"type": "git", "url": "https://github.com/symfony/redis-messenger.git", "reference": "a097e8c6529a7179a732161bd5368629c6319899"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/redis-messenger/zipball/a097e8c6529a7179a732161bd5368629c6319899", "reference": "a097e8c6529a7179a732161bd5368629c6319899", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/messenger": "^5.1|^6.0"}, "require-dev": {"symfony/property-access": "^4.4|^5.0|^6.0", "symfony/serializer": "^4.4|^5.0|^6.0"}, "type": "symfony-messenger-bridge", "autoload": {"psr-4": {"Symfony\\Component\\Messenger\\Bridge\\Redis\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Redis extension Messenger Bridge", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/redis-messenger/tree/v5.4.48"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-13T13:58:00+00:00"}, {"name": "symfony/scheduler", "version": "v7.2.2", "source": {"type": "git", "url": "https://github.com/symfony/scheduler.git", "reference": "78add01da6cbbbdcc95494bcd30e4658441475e1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/scheduler/zipball/78add01da6cbbbdcc95494bcd30e4658441475e1", "reference": "78add01da6cbbbdcc95494bcd30e4658441475e1", "shasum": ""}, "require": {"php": ">=8.2", "symfony/clock": "^6.4|^7.0"}, "require-dev": {"dragonmantank/cron-expression": "^3.1", "symfony/cache": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/lock": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Scheduler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides scheduling through Symfony Messenger", "homepage": "https://symfony.com", "keywords": ["cron", "schedule", "scheduler"], "support": {"source": "https://github.com/symfony/scheduler/tree/v7.2.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-19T14:25:03+00:00"}, {"name": "symfony/service-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "e53260aabf78fb3d63f8d79d69ece59f80d5eda0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/e53260aabf78fb3d63f8d79d69ece59f80d5eda0", "reference": "e53260aabf78fb3d63f8d79d69ece59f80d5eda0", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/string", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "446e0d146f991dde3e73f45f2c97a9faad773c82"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/446e0d146f991dde3e73f45f2c97a9faad773c82", "reference": "446e0d146f991dde3e73f45f2c97a9faad773c82", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.5"}, "require-dev": {"symfony/emoji": "^7.1", "symfony/error-handler": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/translation-contracts": "^2.5|^3.0", "symfony/var-exporter": "^6.4|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-13T13:31:26+00:00"}, {"name": "symfony/translation-contracts", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "4667ff3bd513750603a09c8dedbea942487fb07c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/4667ff3bd513750603a09c8dedbea942487fb07c", "reference": "4667ff3bd513750603a09c8dedbea942487fb07c", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:20:29+00:00"}, {"name": "symfony/type-info", "version": "v7.2.2", "source": {"type": "git", "url": "https://github.com/symfony/type-info.git", "reference": "3b5a17470fff0034f25fd4287cbdaa0010d2f749"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/type-info/zipball/3b5a17470fff0034f25fd4287cbdaa0010d2f749", "reference": "3b5a17470fff0034f25fd4287cbdaa0010d2f749", "shasum": ""}, "require": {"php": ">=8.2", "psr/container": "^1.1|^2.0"}, "require-dev": {"phpstan/phpdoc-parser": "^1.0|^2.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\TypeInfo\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Baptiste LEDUC", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Extracts PHP types information.", "homepage": "https://symfony.com", "keywords": ["PHPStan", "phpdoc", "symfony", "type"], "support": {"source": "https://github.com/symfony/type-info/tree/v7.2.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-20T13:38:37+00:00"}, {"name": "symfony/validator", "version": "v6.4.17", "source": {"type": "git", "url": "https://github.com/symfony/validator.git", "reference": "a3c19a0e542d427c207e22242043ef35b5b99a2c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/validator/zipball/a3c19a0e542d427c207e22242043ef35b5b99a2c", "reference": "a3c19a0e542d427c207e22242043ef35b5b99a2c", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php83": "^1.27", "symfony/translation-contracts": "^2.5|^3"}, "conflict": {"doctrine/annotations": "<1.13", "doctrine/lexer": "<1.1", "symfony/dependency-injection": "<5.4", "symfony/expression-language": "<5.4", "symfony/http-kernel": "<5.4", "symfony/intl": "<5.4", "symfony/property-info": "<5.4", "symfony/translation": "<5.4.35|>=6.0,<6.3.12|>=6.4,<6.4.3|>=7.0,<7.0.3", "symfony/yaml": "<5.4"}, "require-dev": {"doctrine/annotations": "^1.13|^2", "egulias/email-validator": "^2.1.10|^3|^4", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/config": "^5.4|^6.0|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/http-client": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/intl": "^5.4|^6.0|^7.0", "symfony/mime": "^5.4|^6.0|^7.0", "symfony/property-access": "^5.4|^6.0|^7.0", "symfony/property-info": "^5.4|^6.0|^7.0", "symfony/translation": "^5.4.35|~6.3.12|^6.4.3|^7.0.3", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Validator\\": ""}, "exclude-from-classmap": ["/Tests/", "/Resources/bin/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to validate values", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/validator/tree/v6.4.17"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-29T12:50:19+00:00"}, {"name": "symfony/var-dumper", "version": "v6.4.15", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "38254d5a5ac2e61f2b52f9caf54e7aa3c9d36b80"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/38254d5a5ac2e61f2b52f9caf54e7aa3c9d36b80", "reference": "38254d5a5ac2e61f2b52f9caf54e7aa3c9d36b80", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/console": "<5.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^5.4|^6.0|^7.0", "symfony/error-handler": "^6.3|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/uid": "^5.4|^6.0|^7.0", "twig/twig": "^2.13|^3.0.4"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v6.4.15"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-08T15:28:48+00:00"}, {"name": "symfony/var-exporter", "version": "v7.2.0", "source": {"type": "git", "url": "https://github.com/symfony/var-exporter.git", "reference": "1a6a89f95a46af0f142874c9d650a6358d13070d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-exporter/zipball/1a6a89f95a46af0f142874c9d650a6358d13070d", "reference": "1a6a89f95a46af0f142874c9d650a6358d13070d", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"symfony/property-access": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\VarExporter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows exporting any serializable PHP data structure to plain PHP code", "homepage": "https://symfony.com", "keywords": ["clone", "construct", "export", "hydrate", "instantiate", "lazy-loading", "proxy", "serialize"], "support": {"source": "https://github.com/symfony/var-exporter/tree/v7.2.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-18T07:58:17+00:00"}, {"name": "texy/texy", "version": "v3.2.3", "source": {"type": "git", "url": "https://github.com/dg/texy.git", "reference": "a441883938f1ab79f4e22c949e6a1169a119a078"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dg/texy/zipball/a441883938f1ab79f4e22c949e6a1169a119a078", "reference": "a441883938f1ab79f4e22c949e6a1169a119a078", "shasum": ""}, "require": {"php": "8.1 - 8.4"}, "conflict": {"latte/latte": ">=3.0.0 <3.0.14"}, "replace": {"dg/texy": "*"}, "require-dev": {"latte/latte": "^2.6 || ^3.0.14", "nette/tester": "^2.5", "phpstan/phpstan": "^1", "tracy/tracy": "^2.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}], "description": "Texy converts plain text in easy to read Texy syntax into structurally valid (X)HTML. It supports adding of images, links, nested lists, tables and has full support for CSS. Texy supports hyphenation of long words (which reflects language rules), clickable emails and URL (emails are obfuscated against spambots), national typographic single and double quotation marks, ellipses, em dashes, dimension sign, nonbreakable spaces (e.g. in phone numbers), acronyms, arrows and many others. Texy code can optionally contain HTML tags.", "homepage": "https://texy.info", "keywords": ["html", "markdown", "markup language", "plain text", "text", "textile", "texy", "wiki"], "support": {"source": "https://github.com/dg/texy/tree/v3.2.3"}, "time": "2024-10-05T15:06:56+00:00"}, {"name": "tracy/tracy", "version": "v2.10.9", "source": {"type": "git", "url": "https://github.com/nette/tracy.git", "reference": "e7af75205b184ca8895bc57fafd331f8d5022d26"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/tracy/zipball/e7af75205b184ca8895bc57fafd331f8d5022d26", "reference": "e7af75205b184ca8895bc57fafd331f8d5022d26", "shasum": ""}, "require": {"ext-json": "*", "ext-session": "*", "php": "8.0 - 8.4"}, "conflict": {"nette/di": "<3.0"}, "require-dev": {"latte/latte": "^2.5 || ^3.0", "nette/di": "^3.0", "nette/http": "^3.0", "nette/mail": "^3.0 || ^4.0", "nette/tester": "^2.2", "nette/utils": "^3.0 || ^4.0", "phpstan/phpstan": "^1.0", "psr/log": "^1.0 || ^2.0 || ^3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.10-dev"}}, "autoload": {"files": ["src/Tracy/functions.php"], "classmap": ["src"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "😎  Tracy: the addictive tool to ease debugging PHP code for cool developers. Friendly design, logging, profiler, advanced features like debugging AJAX calls or CLI support. You will love it.", "homepage": "https://tracy.nette.org", "keywords": ["Xdebug", "debug", "debugger", "nette", "profiler"], "support": {"issues": "https://github.com/nette/tracy/issues", "source": "https://github.com/nette/tracy/tree/v2.10.9"}, "time": "2024-11-07T14:48:00+00:00"}, {"name": "ublaboo/datagrid", "version": "v6.10.0", "source": {"type": "git", "url": "https://github.com/contributte/datagrid.git", "reference": "1a63088529f47692d57c186f43a9e084f4b52e88"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/datagrid/zipball/1a63088529f47692d57c186f43a9e084f4b52e88", "reference": "1a63088529f47692d57c186f43a9e084f4b52e88", "shasum": ""}, "require": {"contributte/application": "^0.5.0", "nette/di": "^3.0.0", "nette/forms": "^3.1.3", "nette/utils": "^3.0.1 || ^4.0.0", "php": ">=7.2", "symfony/property-access": "^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0"}, "require-dev": {"contributte/code-rules": "^1.1.0", "dibi/dibi": "^3.0.0 || ^4.0.0", "doctrine/annotations": "^1.12.1", "doctrine/cache": "^1.11.0", "doctrine/orm": "^2.11.1", "elasticsearch/elasticsearch": "^7.1", "mockery/mockery": "^1.3.3", "nette/database": "^3.0.2", "nette/tester": "^2.3.4", "nextras/dbal": "^3.0.1 || ^4.0", "nextras/orm": "^3.1.0 || ^4.0", "ninjify/coding-standard": "^0.12.1", "phpstan/phpstan-nette": "^1.0.0", "tharos/leanmapper": "^3.4.2 || ^4.0.0", "tracy/tracy": "^2.6.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.10.x-dev"}}, "autoload": {"psr-4": {"Ublaboo\\DataGrid\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://paveljanda.com"}, {"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "DataGrid for Nette Framework: filtering, sorting, pagination, tree view, table view, translator, etc", "keywords": ["contributte", "data", "datagrid", "grid", "nette", "table"], "support": {"issues": "https://github.com/contributte/datagrid/issues", "source": "https://github.com/contributte/datagrid/tree/v6.10.0"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2024-03-03T16:52:06+00:00"}, {"name": "venca-x/social-login", "version": "1.2.15", "source": {"type": "git", "url": "https://github.com/venca-x/social-login.git", "reference": "52fd3d6965f7b5f56398857a6e100e514b100aee"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/venca-x/social-login/zipball/52fd3d6965f7b5f56398857a6e100e514b100aee", "reference": "52fd3d6965f7b5f56398857a6e100e514b100aee", "shasum": ""}, "require": {"abraham/twitteroauth": "^2.0.0", "google/apiclient": "^2.8", "league/oauth2-facebook": "^2.0", "nette/application": "^3.0", "php": ">=7.2"}, "conflict": {"google/apiclient": "<2.8.0"}, "require-dev": {"nette/bootstrap": "^3.0", "nette/robot-loader": "^3.0", "nette/tester": "^2.0", "tracy/tracy": "^2.4"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "vEnCa-X", "email": "<EMAIL>", "homepage": "http://www.venca-x.cz"}], "description": "Nette addon. Login with social networks ( Faceboook, Google, Twitter )", "keywords": ["Facebook login", "Google login", "Social Login", "Twitter login", "nette", "php"], "support": {"issues": "https://github.com/venca-x/social-login/issues", "source": "https://github.com/venca-x/social-login/tree/1.2.15"}, "time": "2022-03-15T18:55:06+00:00"}, {"name": "webchemistry/oauth2-seznam", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/WebChemistry/oauth2-seznam.git", "reference": "72438fc2e44a6883a71982ec650be82b75977a14"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/WebChemistry/oauth2-seznam/zipball/72438fc2e44a6883a71982ec650be82b75977a14", "reference": "72438fc2e44a6883a71982ec650be82b75977a14", "shasum": ""}, "require": {"league/oauth2-client": "^2.0", "php": ">= 8.0"}, "type": "library", "autoload": {"psr-4": {"WebChemistry\\OAuth2\\Client\\Seznam\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Seznam Provider for the OAuth 2.0 Client", "keywords": ["Authentication", "authorization", "client", "o<PERSON>h", "oauth2", "seznam"], "support": {"issues": "https://github.com/WebChemistry/oauth2-seznam/issues", "source": "https://github.com/WebChemistry/oauth2-seznam/tree/v1.0.0"}, "time": "2021-11-14T13:37:46+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "time": "2022-06-03T18:03:27+00:00"}], "packages-dev": [{"name": "dealerdirect/phpcodesniffer-composer-installer", "version": "v0.7.2", "source": {"type": "git", "url": "https://github.com/Dealerdirect/phpcodesniffer-composer-installer.git", "reference": "****************************************"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Dealerdirect/phpcodesniffer-composer-installer/zipball/****************************************", "reference": "****************************************", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=5.3", "squizlabs/php_codesniffer": "^2.0 || ^3.1.0 || ^4.0"}, "require-dev": {"composer/composer": "*", "php-parallel-lint/php-parallel-lint": "^1.3.1", "phpcompatibility/php-compatibility": "^9.0"}, "type": "composer-plugin", "extra": {"class": "Dealerdirect\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\Plugin"}, "autoload": {"psr-4": {"Dealerdirect\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.frenck.nl", "role": "Developer / IT Manager"}, {"name": "Contributors", "homepage": "https://github.com/Dealerdirect/phpcodesniffer-composer-installer/graphs/contributors"}], "description": "PHP_CodeSniffer Standards Composer Installer Plugin", "homepage": "http://www.dealerdirect.com", "keywords": ["PHPCodeSniffer", "PHP_CodeSniffer", "code quality", "codesniffer", "composer", "installer", "phpcbf", "phpcs", "plugin", "qa", "quality", "standard", "standards", "style guide", "stylecheck", "tests"], "support": {"issues": "https://github.com/dealerdirect/phpcodesniffer-composer-installer/issues", "source": "https://github.com/dealerdirect/phpcodesniffer-composer-installer"}, "time": "2022-02-04T12:51:07+00:00"}, {"name": "deployer/deployer", "version": "v7.5.8", "source": {"type": "git", "url": "https://github.com/deployphp/deployer.git", "reference": "4900fe799ce5566d54a14103cdfd6e865b7c5d72"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/deployphp/deployer/zipball/4900fe799ce5566d54a14103cdfd6e865b7c5d72", "reference": "4900fe799ce5566d54a14103cdfd6e865b7c5d72", "shasum": ""}, "require": {"ext-json": "*", "php": "^8.0|^7.3"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.64", "pestphp/pest": "^3.3", "phpstan/phpstan": "^1.4", "phpunit/php-code-coverage": "^11.0", "phpunit/phpunit": "^11.4"}, "bin": ["bin/dep"], "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Deployment Tool", "homepage": "https://deployer.org", "support": {"docs": "https://deployer.org/docs", "issues": "https://github.com/deployphp/deployer/issues", "source": "https://github.com/deployphp/deployer"}, "funding": [{"url": "https://github.com/sponsors/antonmedv", "type": "github"}], "time": "2024-11-27T21:35:20+00:00"}, {"name": "hamcrest/hamcrest-php", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/hamcrest/hamcrest-php.git", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hamcrest/hamcrest-php/zipball/8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "shasum": ""}, "require": {"php": "^5.3|^7.0|^8.0"}, "replace": {"cordoval/hamcrest-php": "*", "davedevelopment/hamcrest-php": "*", "kodova/hamcrest-php": "*"}, "require-dev": {"phpunit/php-file-iterator": "^1.4 || ^2.0", "phpunit/phpunit": "^4.8.36 || ^5.7 || ^6.5 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["hamcrest"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "This is the PHP port of Hamcrest Matchers", "keywords": ["test"], "support": {"issues": "https://github.com/hamcrest/hamcrest-php/issues", "source": "https://github.com/hamcrest/hamcrest-php/tree/v2.0.1"}, "time": "2020-07-09T08:09:16+00:00"}, {"name": "mockery/mockery", "version": "1.6.12", "source": {"type": "git", "url": "https://github.com/mockery/mockery.git", "reference": "1f4efdd7d3beafe9807b08156dfcb176d18f1699"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mockery/mockery/zipball/1f4efdd7d3beafe9807b08156dfcb176d18f1699", "reference": "1f4efdd7d3beafe9807b08156dfcb176d18f1699", "shasum": ""}, "require": {"hamcrest/hamcrest-php": "^2.0.1", "lib-pcre": ">=7.0", "php": ">=7.3"}, "conflict": {"phpunit/phpunit": "<8.0"}, "require-dev": {"phpunit/phpunit": "^8.5 || ^9.6.17", "symplify/easy-coding-standard": "^12.1.14"}, "type": "library", "autoload": {"files": ["library/helpers.php", "library/Mockery.php"], "psr-4": {"Mockery\\": "library/Mockery"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/padraic", "role": "Author"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://davedevelopment.co.uk", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/ghostwriter", "role": "Lead Developer"}], "description": "Mockery is a simple yet flexible PHP mock object framework", "homepage": "https://github.com/mockery/mockery", "keywords": ["BDD", "TDD", "library", "mock", "mock objects", "mockery", "stub", "test", "test double", "testing"], "support": {"docs": "https://docs.mockery.io/", "issues": "https://github.com/mockery/mockery/issues", "rss": "https://github.com/mockery/mockery/releases.atom", "security": "https://github.com/mockery/mockery/security/advisories", "source": "https://github.com/mockery/mockery"}, "time": "2024-05-16T03:13:13+00:00"}, {"name": "nette/tester", "version": "v2.5.4", "source": {"type": "git", "url": "https://github.com/nette/tester.git", "reference": "c11863785779e87b40adebf150364f2e5938c111"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/tester/zipball/c11863785779e87b40adebf150364f2e5938c111", "reference": "c11863785779e87b40adebf150364f2e5938c111", "shasum": ""}, "require": {"php": "8.0 - 8.4"}, "require-dev": {"ext-simplexml": "*", "phpstan/phpstan": "^1.0"}, "bin": ["src/tester"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "<PERSON><PERSON>", "homepage": "https://github.com/milo"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "Nette Tester: enjoyable unit testing in PHP with code coverage reporter. 🍏🍏🍎🍏", "homepage": "https://tester.nette.org", "keywords": ["Xdebug", "assertions", "clover", "code coverage", "nette", "pcov", "phpdbg", "phpunit", "testing", "unit"], "support": {"issues": "https://github.com/nette/tester/issues", "source": "https://github.com/nette/tester/tree/v2.5.4"}, "time": "2024-10-23T23:57:10+00:00"}, {"name": "nextras/orm-phpstan", "version": "v2.0.0", "source": {"type": "git", "url": "https://github.com/nextras/orm-phpstan.git", "reference": "f1b4d682034c22f3650c15deebac740e33a9cdd1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nextras/orm-phpstan/zipball/f1b4d682034c22f3650c15deebac740e33a9cdd1", "reference": "f1b4d682034c22f3650c15deebac740e33a9cdd1", "shasum": ""}, "require": {"php": ">=8.1", "phpstan/phpstan": "^1.10.12 || ^2.0.0"}, "conflict": {"nextras/orm": "<5.0"}, "require-dev": {"nette/tester": "^2.3.1", "nextras/orm": "~5.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan-deprecation-rules": "^1.0 || ^2.0"}, "type": "phpstan-extension", "extra": {"phpstan": {"includes": ["extension.neon"]}}, "autoload": {"psr-4": {"Nextras\\OrmPhpStan\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPStan extension for Nextras Orm", "keywords": ["dev", "static analysis"], "support": {"issues": "https://github.com/nextras/orm-phpstan/issues", "source": "https://github.com/nextras/orm-phpstan/tree/v2.0.0"}, "time": "2024-12-14T21:07:31+00:00"}, {"name": "ninjify/coding-standard", "version": "v0.12.1", "source": {"type": "git", "url": "https://github.com/ninjify/coding-standard.git", "reference": "c655eedbe1b4f9b307e9941ad347f9078fbdd58a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ninjify/coding-standard/zipball/c655eedbe1b4f9b307e9941ad347f9078fbdd58a", "reference": "c655eedbe1b4f9b307e9941ad347f9078fbdd58a", "shasum": ""}, "require": {"php": ">=7.2", "slevomat/coding-standard": "^7.0.18", "squizlabs/php_codesniffer": "^3.5.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.13.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Tuned & very strict coding standards for PHP projects. Trusted by Contributte, Apitte, Nettrine and many others.", "homepage": "https://github.com/ninjify/coding-standard", "keywords": ["Codestyle", "codesniffer", "ninji<PERSON>", "php"], "support": {"issues": "https://github.com/ninjify/coding-standard/issues", "source": "https://github.com/ninjify/coding-standard/tree/v0.12.1"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2022-02-11T14:34:15+00:00"}, {"name": "php-parallel-lint/php-console-color", "version": "v1.0.1", "source": {"type": "git", "url": "https://github.com/php-parallel-lint/PHP-Console-Color.git", "reference": "7adfefd530aa2d7570ba87100a99e2483a543b88"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-parallel-lint/PHP-Console-Color/zipball/7adfefd530aa2d7570ba87100a99e2483a543b88", "reference": "7adfefd530aa2d7570ba87100a99e2483a543b88", "shasum": ""}, "require": {"php": ">=5.3.2"}, "replace": {"jakub-onderka/php-console-color": "*"}, "require-dev": {"php-parallel-lint/php-code-style": "^2.0", "php-parallel-lint/php-parallel-lint": "^1.0", "php-parallel-lint/php-var-dump-check": "0.*", "phpunit/phpunit": "^4.8.36 || ^5.7.21 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "type": "library", "autoload": {"psr-4": {"PHP_Parallel_Lint\\PhpConsoleColor\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Simple library for creating colored console ouput.", "support": {"issues": "https://github.com/php-parallel-lint/PHP-Console-Color/issues", "source": "https://github.com/php-parallel-lint/PHP-Console-Color/tree/v1.0.1"}, "time": "2021-12-25T06:49:29+00:00"}, {"name": "php-parallel-lint/php-console-highlighter", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/php-parallel-lint/PHP-Console-Highlighter.git", "reference": "5b4803384d3303cf8e84141039ef56c8a123138d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-parallel-lint/PHP-Console-Highlighter/zipball/5b4803384d3303cf8e84141039ef56c8a123138d", "reference": "5b4803384d3303cf8e84141039ef56c8a123138d", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=5.3.2", "php-parallel-lint/php-console-color": "^1.0.1"}, "replace": {"jakub-onderka/php-console-highlighter": "*"}, "require-dev": {"php-parallel-lint/php-code-style": "^2.0", "php-parallel-lint/php-parallel-lint": "^1.0", "php-parallel-lint/php-var-dump-check": "0.*", "phpunit/phpunit": "^4.8.36 || ^5.7.21 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "type": "library", "autoload": {"psr-4": {"PHP_Parallel_Lint\\PhpConsoleHighlighter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.acci.cz/"}], "description": "Highlight PHP code in terminal", "support": {"issues": "https://github.com/php-parallel-lint/PHP-Console-Highlighter/issues", "source": "https://github.com/php-parallel-lint/PHP-Console-Highlighter/tree/v1.0.0"}, "time": "2022-02-18T08:23:19+00:00"}, {"name": "php-parallel-lint/php-parallel-lint", "version": "v1.4.0", "source": {"type": "git", "url": "https://github.com/php-parallel-lint/PHP-Parallel-Lint.git", "reference": "6db563514f27e19595a19f45a4bf757b6401194e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-parallel-lint/PHP-Parallel-Lint/zipball/6db563514f27e19595a19f45a4bf757b6401194e", "reference": "6db563514f27e19595a19f45a4bf757b6401194e", "shasum": ""}, "require": {"ext-json": "*", "php": ">=5.3.0"}, "replace": {"grogy/php-parallel-lint": "*", "jakub-onderka/php-parallel-lint": "*"}, "require-dev": {"nette/tester": "^1.3 || ^2.0", "php-parallel-lint/php-console-highlighter": "0.* || ^1.0", "squizlabs/php_codesniffer": "^3.6"}, "suggest": {"php-parallel-lint/php-console-highlighter": "Highlight syntax in code snippet"}, "bin": ["parallel-lint"], "type": "library", "autoload": {"classmap": ["./src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "This tool checks the syntax of PHP files about 20x faster than serial check.", "homepage": "https://github.com/php-parallel-lint/PHP-Parallel-Lint", "keywords": ["lint", "static analysis"], "support": {"issues": "https://github.com/php-parallel-lint/PHP-Parallel-Lint/issues", "source": "https://github.com/php-parallel-lint/PHP-Parallel-Lint/tree/v1.4.0"}, "time": "2024-03-27T12:14:49+00:00"}, {"name": "phpstan/extension-installer", "version": "1.4.3", "source": {"type": "git", "url": "https://github.com/phpstan/extension-installer.git", "reference": "85e90b3942d06b2326fba0403ec24fe912372936"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/extension-installer/zipball/85e90b3942d06b2326fba0403ec24fe912372936", "reference": "85e90b3942d06b2326fba0403ec24fe912372936", "shasum": ""}, "require": {"composer-plugin-api": "^2.0", "php": "^7.2 || ^8.0", "phpstan/phpstan": "^1.9.0 || ^2.0"}, "require-dev": {"composer/composer": "^2.0", "php-parallel-lint/php-parallel-lint": "^1.2.0", "phpstan/phpstan-strict-rules": "^0.11 || ^0.12 || ^1.0"}, "type": "composer-plugin", "extra": {"class": "PHPStan\\ExtensionInstaller\\Plugin"}, "autoload": {"psr-4": {"PHPStan\\ExtensionInstaller\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Composer plugin for automatic installation of PHPStan extensions", "keywords": ["dev", "static analysis"], "support": {"issues": "https://github.com/phpstan/extension-installer/issues", "source": "https://github.com/phpstan/extension-installer/tree/1.4.3"}, "time": "2024-09-04T20:21:43+00:00"}, {"name": "phpstan/phpstan", "version": "2.1.2", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan.git", "reference": "7d08f569e582ade182a375c366cbd896eccadd3a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan/zipball/7d08f569e582ade182a375c366cbd896eccadd3a", "reference": "7d08f569e582ade182a375c366cbd896eccadd3a", "shasum": ""}, "require": {"php": "^7.4|^8.0"}, "conflict": {"phpstan/phpstan-shim": "*"}, "bin": ["phpstan", "phpstan.phar"], "type": "library", "autoload": {"files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPStan - PHP Static Analysis Tool", "keywords": ["dev", "static analysis"], "support": {"docs": "https://phpstan.org/user-guide/getting-started", "forum": "https://github.com/phpstan/phpstan/discussions", "issues": "https://github.com/phpstan/phpstan/issues", "security": "https://github.com/phpstan/phpstan/security/policy", "source": "https://github.com/phpstan/phpstan-src"}, "funding": [{"url": "https://github.com/ondrejmirtes", "type": "github"}, {"url": "https://github.com/phpstan", "type": "github"}], "time": "2025-01-21T14:54:06+00:00"}, {"name": "phpstan/phpstan-deprecation-rules", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan-deprecation-rules.git", "reference": "1cc1259cb91ee4cfbb5c39bca9f635f067c910b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan-deprecation-rules/zipball/1cc1259cb91ee4cfbb5c39bca9f635f067c910b4", "reference": "1cc1259cb91ee4cfbb5c39bca9f635f067c910b4", "shasum": ""}, "require": {"php": "^7.4 || ^8.0", "phpstan/phpstan": "^2.0"}, "require-dev": {"php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/phpstan-phpunit": "^2.0", "phpunit/phpunit": "^9.6"}, "type": "phpstan-extension", "extra": {"phpstan": {"includes": ["rules.neon"]}}, "autoload": {"psr-4": {"PHPStan\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPStan rules for detecting usage of deprecated classes, methods, properties, constants and traits.", "support": {"issues": "https://github.com/phpstan/phpstan-deprecation-rules/issues", "source": "https://github.com/phpstan/phpstan-deprecation-rules/tree/2.0.1"}, "time": "2024-11-28T21:56:36+00:00"}, {"name": "phpstan/phpstan-nette", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan-nette.git", "reference": "7ff60d93371de7855216bb3b2d9f47f984322ede"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan-nette/zipball/7ff60d93371de7855216bb3b2d9f47f984322ede", "reference": "7ff60d93371de7855216bb3b2d9f47f984322ede", "shasum": ""}, "require": {"php": "^7.4 || ^8.0", "phpstan/phpstan": "^2.0"}, "conflict": {"nette/application": "<2.3.0", "nette/component-model": "<2.3.0", "nette/di": "<2.3.0", "nette/forms": "<2.3.0", "nette/http": "<2.3.0", "nette/utils": "<2.3.0"}, "require-dev": {"nette/application": "^3.0", "nette/di": "^3.1.10", "nette/forms": "^3.0", "nette/utils": "^2.3.0 || ^3.0.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/phpstan-phpunit": "^2.0", "phpstan/phpstan-strict-rules": "^2.0", "phpunit/phpunit": "^9.6"}, "type": "phpstan-extension", "extra": {"phpstan": {"includes": ["extension.neon", "rules.neon"]}}, "autoload": {"psr-4": {"PHPStan\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Nette Framework class reflection extension for PHPStan", "support": {"issues": "https://github.com/phpstan/phpstan-nette/issues", "source": "https://github.com/phpstan/phpstan-nette/tree/2.0.2"}, "time": "2025-01-15T09:43:39+00:00"}, {"name": "roave/security-advisories", "version": "dev-latest", "source": {"type": "git", "url": "https://github.com/Roave/SecurityAdvisories.git", "reference": "fa05b1cdeb1d38692aea5d34bed226b682403a6d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Roave/SecurityAdvisories/zipball/fa05b1cdeb1d38692aea5d34bed226b682403a6d", "reference": "fa05b1cdeb1d38692aea5d34bed226b682403a6d", "shasum": ""}, "conflict": {"3f/pygmentize": "<1.2", "admidio/admidio": "<4.3.12", "adodb/adodb-php": "<=5.20.20|>=5.21,<=5.21.3", "aheinze/cockpit": "<2.2", "aimeos/ai-admin-graphql": ">=2022.04.1,<2022.10.10|>=2023.04.1,<2023.10.6|>=2024.04.1,<2024.07.2", "aimeos/ai-admin-jsonadm": "<2020.10.13|>=2021.04.1,<2021.10.6|>=2022.04.1,<2022.10.3|>=2023.04.1,<2023.10.4|==2024.04.1", "aimeos/ai-client-html": ">=2020.04.1,<2020.10.27|>=2021.04.1,<2021.10.22|>=2022.04.1,<2022.10.13|>=2023.04.1,<2023.10.15|>=2024.04.1,<2024.04.7", "aimeos/ai-controller-frontend": "<2020.10.15|>=2021.04.1,<2021.10.8|>=2022.04.1,<2022.10.8|>=2023.04.1,<2023.10.9|==2024.04.1", "aimeos/aimeos-core": ">=2022.04.1,<2022.10.17|>=2023.04.1,<2023.10.17|>=2024.04.1,<2024.04.7", "aimeos/aimeos-typo3": "<19.10.12|>=20,<20.10.5", "airesvsg/acf-to-rest-api": "<=3.1", "akaunting/akaunting": "<2.1.13", "akeneo/pim-community-dev": "<5.0.119|>=6,<6.0.53", "alextselegidis/easyappointments": "<1.5", "alterphp/easyadmin-extension-bundle": ">=1.2,<1.2.11|>=1.3,<1.3.1", "amazing/media2click": ">=1,<1.3.3", "ameos/ameos_tarteaucitron": "<1.2.23", "amphp/artax": "<1.0.6|>=2,<2.0.6", "amphp/http": "<=1.7.2|>=2,<=2.1", "amphp/http-client": ">=4,<4.4", "anchorcms/anchor-cms": "<=0.12.7", "andreapollastri/cipi": "<=3.1.15", "andrewhaine/silverstripe-form-capture": ">=0.2,<=0.2.3|>=1,<1.0.2|>=2,<2.2.5", "apache-solr-for-typo3/solr": "<2.8.3", "apereo/phpcas": "<1.6", "api-platform/core": ">=2.2,<2.2.10|>=2.3,<2.3.6|>=2.6,<2.7.10|>=3,<3.0.12|>=3.1,<3.1.3", "appwrite/server-ce": "<=1.2.1", "arc/web": "<3", "area17/twill": "<1.2.5|>=2,<2.5.3", "artesaos/seotools": "<0.17.2", "asymmetricrypt/asymmetricrypt": "<9.9.99", "athlon1600/php-proxy": "<=5.1", "athlon1600/php-proxy-app": "<=3", "austintoddj/canvas": "<=3.4.2", "auth0/wordpress": "<=4.6", "automad/automad": "<*******-alpha5", "automattic/jetpack": "<9.8", "awesome-support/awesome-support": "<=6.0.7", "aws/aws-sdk-php": "<3.288.1", "azuracast/azuracast": "<0.18.3", "backdrop/backdrop": "<1.27.3|>=1.28,<1.28.2", "backpack/crud": "<3.4.9", "backpack/filemanager": "<2.0.2|>=3,<3.0.9", "bacula-web/bacula-web": "<*******-RC2-dev", "badaso/core": "<2.7", "bagisto/bagisto": "<2.1", "barrelstrength/sprout-base-email": "<1.2.7", "barrelstrength/sprout-forms": "<3.9", "barryvdh/laravel-translation-manager": "<0.6.2", "barzahlen/barzahlen-php": "<2.0.1", "baserproject/basercms": "<=5.1.1", "bassjobsen/bootstrap-3-typeahead": ">4.0.2", "bbpress/bbpress": "<2.6.5", "bcosca/fatfree": "<3.7.2", "bedita/bedita": "<4", "bigfork/silverstripe-form-capture": ">=3,<3.1.1", "billz/raspap-webgui": "<=3.1.4", "bk2k/bootstrap-package": ">=7.1,<7.1.2|>=8,<8.0.8|>=9,<9.0.4|>=9.1,<9.1.3|>=10,<10.0.10|>=11,<11.0.3", "blueimp/jquery-file-upload": "==6.4.4", "bmarshall511/wordpress_zero_spam": "<5.2.13", "bolt/bolt": "<3.7.2", "bolt/core": "<=4.2", "born05/craft-twofactorauthentication": "<3.3.4", "bottelet/flarepoint": "<2.2.1", "bref/bref": "<2.1.17", "brightlocal/phpwhois": "<=4.2.5", "brotkrueml/codehighlight": "<2.7", "brotkrueml/schema": "<1.13.1|>=2,<2.5.1", "brotkrueml/typo3-matomo-integration": "<1.3.2", "buddypress/buddypress": "<7.2.1", "bugsnag/bugsnag-laravel": ">=2,<2.0.2", "bytefury/crater": "<6.0.2", "cachethq/cachet": "<2.5.1", "cakephp/cakephp": "<3.10.3|>=4,<4.0.10|>=4.1,<4.1.4|>=4.2,<4.2.12|>=4.3,<4.3.11|>=4.4,<4.4.10", "cakephp/database": ">=4.2,<4.2.12|>=4.3,<4.3.11|>=4.4,<4.4.10", "cardgate/magento2": "<2.0.33", "cardgate/woocommerce": "<=3.1.15", "cart2quote/module-quotation": ">=4.1.6,<=4.4.5|>=5,<5.4.4", "cart2quote/module-quotation-encoded": ">=4.1.6,<=4.4.5|>=5,<5.4.4", "cartalyst/sentry": "<=2.1.6", "catfan/medoo": "<1.7.5", "causal/oidc": "<2.1", "cecil/cecil": "<7.47.1", "centreon/centreon": "<22.10.15", "cesnet/simplesamlphp-module-proxystatistics": "<3.1", "chriskacerguis/codeigniter-restserver": "<=2.7.1", "civicrm/civicrm-core": ">=4.2,<4.2.9|>=4.3,<4.3.3", "ckeditor/ckeditor": "<4.24", "cockpit-hq/cockpit": "<2.7|==2.7", "codeception/codeception": "<3.1.3|>=4,<4.1.22", "codeigniter/framework": "<3.1.9", "codeigniter4/framework": "<4.5.8", "codeigniter4/shield": "<1.0.0.0-beta8", "codiad/codiad": "<=2.8.4", "composer/composer": "<1.10.27|>=2,<2.2.24|>=2.3,<2.7.7", "concrete5/concrete5": "<9.3.4", "concrete5/core": "<8.5.8|>=9,<9.1", "contao-components/mediaelement": ">=2.14.2,<2.21.1", "contao/comments-bundle": ">=2,<4.13.40|>=*******-RC1-dev,<5.3.4", "contao/contao": "<=5.4.1", "contao/core": "<3.5.39", "contao/core-bundle": "<4.13.49|>=5,<5.3.15|>=5.4,<5.4.3", "contao/listing-bundle": ">=3,<=3.5.30|>=4,<4.4.8", "contao/managed-edition": "<=1.5", "corveda/phpsandbox": "<1.3.5", "cosenary/instagram": "<=2.3", "craftcms/cms": "<4.13.8|>=5,<5.5.5", "croogo/croogo": "<4", "cuyz/valinor": "<0.12", "czim/file-handling": "<1.5|>=2,<2.3", "czproject/git-php": "<4.0.3", "damienharper/auditor-bundle": "<5.2.6", "dapphp/securimage": "<3.6.6", "darylldoyle/safe-svg": "<1.9.10", "datadog/dd-trace": ">=0.30,<0.30.2", "datatables/datatables": "<1.10.10", "david-garcia/phpwhois": "<=4.3.1", "dbrisinajumi/d2files": "<1", "dcat/laravel-admin": "<=2.1.3|==2.2.0.0-beta|==2.2.2.0-beta", "derhansen/fe_change_pwd": "<2.0.5|>=3,<3.0.3", "derhansen/sf_event_mgt": "<4.3.1|>=5,<5.1.1|>=7,<7.4", "desperado/xml-bundle": "<=0.1.7", "dev-lancer/minecraft-motd-parser": "<=1.0.5", "devgroup/dotplant": "<2020.09.14-dev", "directmailteam/direct-mail": "<6.0.3|>=7,<7.0.3|>=8,<9.5.2", "doctrine/annotations": "<1.2.7", "doctrine/cache": ">=1,<1.3.2|>=1.4,<1.4.2", "doctrine/common": "<2.4.3|>=2.5,<2.5.1", "doctrine/dbal": ">=2,<2.0.8|>=2.1,<2.1.2|>=3,<3.1.4", "doctrine/doctrine-bundle": "<1.5.2", "doctrine/doctrine-module": "<0.7.2", "doctrine/mongodb-odm": "<1.0.2", "doctrine/mongodb-odm-bundle": "<3.0.1", "doctrine/orm": ">=1,<1.2.4|>=2,<2.4.8|>=2.5,<2.5.1|>=2.8.3,<2.8.4", "dolibarr/dolibarr": "<19.0.2", "dompdf/dompdf": "<2.0.4", "doublethreedigital/guest-entries": "<3.1.2", "drupal/core": ">=6,<6.38|>=7,<7.102|>=8,<10.2.11|>=10.3,<10.3.9|>=11,<11.0.8", "drupal/core-recommended": ">=7,<7.102|>=8,<10.2.11|>=10.3,<10.3.9|>=11,<11.0.8", "drupal/drupal": ">=5,<5.11|>=6,<6.38|>=7,<7.102|>=8,<10.2.11|>=10.3,<10.3.9|>=11,<11.0.8", "duncanmcclean/guest-entries": "<3.1.2", "dweeves/magmi": "<=0.7.24", "ec-cube/ec-cube": "<2.4.4|>=2.11,<=2.17.1|>=3,<=3.0.18.0-patch4|>=4,<=4.1.2", "ecodev/newsletter": "<=4", "ectouch/ectouch": "<=2.7.2", "egroupware/egroupware": "<23.1.20240624", "elefant/cms": "<2.0.7", "elgg/elgg": "<3.3.24|>=4,<4.0.5", "elijaa/phpmemcacheadmin": "<=1.3", "encore/laravel-admin": "<=1.8.19", "endroid/qr-code-bundle": "<3.4.2", "enhavo/enhavo-app": "<=0.13.1", "enshrined/svg-sanitize": "<0.15", "erusev/parsedown": "<1.7.2", "ether/logs": "<3.0.4", "evolutioncms/evolution": "<=3.2.3", "exceedone/exment": "<4.4.3|>=5,<5.0.3", "exceedone/laravel-admin": "<2.2.3|==3", "ezsystems/demobundle": ">=5.4,<5.4.6.1-dev", "ezsystems/ez-support-tools": ">=2.2,<2.2.3", "ezsystems/ezdemo-ls-extension": ">=5.4,<5.4.2.1-dev", "ezsystems/ezfind-ls": ">=5.3,<5.3.6.1-dev|>=5.4,<5.4.11.1-dev|>=2017.12,<2017.12.0.1-dev", "ezsystems/ezplatform": "<=1.13.6|>=2,<=2.5.24", "ezsystems/ezplatform-admin-ui": ">=1.3,<1.3.5|>=1.4,<1.4.6|>=1.5,<1.5.29|>=2.3,<2.3.26|>=3.3,<3.3.39", "ezsystems/ezplatform-admin-ui-assets": ">=4,<4.2.1|>=5,<5.0.1|>=5.1,<5.1.1", "ezsystems/ezplatform-graphql": ">=1.0.0.0-RC1-dev,<1.0.13|>=*******-beta1,<2.3.12", "ezsystems/ezplatform-http-cache": "<2.3.16", "ezsystems/ezplatform-kernel": "<1.2.5.1-dev|>=1.3,<1.3.35", "ezsystems/ezplatform-rest": ">=1.2,<=1.2.2|>=1.3,<1.3.8", "ezsystems/ezplatform-richtext": ">=2.3,<*******-dev|>=3.3,<3.3.40", "ezsystems/ezplatform-solr-search-engine": ">=1.7,<1.7.12|>=2,<2.0.2|>=3.3,<3.3.15", "ezsystems/ezplatform-user": ">=1,<1.0.1", "ezsystems/ezpublish-kernel": "<********-dev|>=7,<7.5.31", "ezsystems/ezpublish-legacy": "<=2017.12.7.3|>=2018.6,<=2019.03.5.1", "ezsystems/platform-ui-assets-bundle": ">=4.2,<4.2.3", "ezsystems/repository-forms": ">=2.3,<*******-dev|>=2.5,<2.5.15", "ezyang/htmlpurifier": "<=4.2", "facade/ignition": "<1.16.15|>=2,<2.4.2|>=2.5,<2.5.2", "facturascripts/facturascripts": "<=2022.08", "fastly/magento2": "<1.2.26", "feehi/cms": "<=2.1.1", "feehi/feehicms": "<=2.1.1", "fenom/fenom": "<=2.12.1", "filament/actions": ">=3.2,<3.2.123", "filament/infolists": ">=3,<3.2.115", "filament/tables": ">=3,<3.2.115", "filegator/filegator": "<7.8", "filp/whoops": "<2.1.13", "fineuploader/php-traditional-server": "<=1.2.2", "firebase/php-jwt": "<6", "fisharebest/webtrees": "<=2.1.18", "fixpunkt/fp-masterquiz": "<2.2.1|>=3,<3.5.2", "fixpunkt/fp-newsletter": "<1.1.1|>=2,<2.1.2|>=2.2,<3.2.6", "flarum/core": "<1.8.5", "flarum/flarum": "<*******-beta8", "flarum/framework": "<1.8.5", "flarum/mentions": "<1.6.3", "flarum/sticky": ">=*******-beta14,<=*******-beta15", "flarum/tags": "<=*******-beta13", "floriangaerber/magnesium": "<0.3.1", "fluidtypo3/vhs": "<5.1.1", "fof/byobu": ">=*******-beta2,<1.1.7", "fof/upload": "<1.2.3", "foodcoopshop/foodcoopshop": ">=3.2,<3.6.1", "fooman/tcpdf": "<6.2.22", "forkcms/forkcms": "<5.11.1", "fossar/tcpdf-parser": "<6.2.22", "francoisjacquet/rosariosis": "<=11.5.1", "frappant/frp-form-answers": "<3.1.2|>=4,<4.0.2", "friendsofsymfony/oauth2-php": "<1.3", "friendsofsymfony/rest-bundle": ">=1.2,<1.2.2", "friendsofsymfony/user-bundle": ">=1,<1.3.5", "friendsofsymfony1/swiftmailer": ">=4,<5.4.13|>=6,<6.2.5", "friendsofsymfony1/symfony1": ">=1.1,<1.5.19", "friendsoftypo3/mediace": ">=7.6.2,<7.6.5", "friendsoftypo3/openid": ">=4.5,<4.5.31|>=4.7,<4.7.16|>=6,<6.0.11|>=6.1,<6.1.6", "froala/wysiwyg-editor": "<3.2.7|>=4.0.1,<=4.1.3", "froxlor/froxlor": "<=2.2.0.0-RC3", "frozennode/administrator": "<=5.0.12", "fuel/core": "<1.8.1", "funadmin/funadmin": "<=5.0.2", "gaoming13/wechat-php-sdk": "<=1.10.2", "genix/cms": "<=1.1.11", "getformwork/formwork": "<1.13.1|==*******-beta1", "getgrav/grav": "<1.7.46", "getkirby/cms": "<=3.6.6.5|>=3.7,<=3.7.5.4|>=3.8,<=3.8.4.3|>=3.9,<=3.9.8.1|>=3.10,<=3.10.1|>=4,<=4.3", "getkirby/kirby": "<=2.5.12", "getkirby/panel": "<2.5.14", "getkirby/starterkit": "<=3.7.0.2", "gilacms/gila": "<=1.15.4", "gleez/cms": "<=1.3|==2", "globalpayments/php-sdk": "<2", "goalgorilla/open_social": "<12.3.8|>=12.4,<12.4.5|>=13.0.0.0-alpha1,<13.0.0.0-alpha11", "gogentooss/samlbase": "<1.2.7", "google/protobuf": "<3.15", "gos/web-socket-bundle": "<1.10.4|>=2,<2.6.1|>=3,<3.3", "gree/jose": "<2.2.1", "gregwar/rst": "<1.0.3", "grumpydictator/firefly-iii": "<6.1.17", "gugoan/economizzer": "<=0.9.0.0-beta1", "guzzlehttp/guzzle": "<6.5.8|>=7,<7.4.5", "guzzlehttp/oauth-subscriber": "<0.8.1", "guzzlehttp/psr7": "<1.9.1|>=2,<2.4.5", "haffner/jh_captcha": "<=2.1.3|>=3,<=3.0.2", "harvesthq/chosen": "<1.8.7", "helloxz/imgurl": "<=2.31", "hhxsv5/laravel-s": "<3.7.36", "hillelcoren/invoice-ninja": "<5.3.35", "himiklab/yii2-jqgrid-widget": "<1.0.8", "hjue/justwriting": "<=1", "hov/jobfair": "<1.0.13|>=2,<2.0.2", "httpsoft/http-message": "<1.0.12", "hyn/multi-tenant": ">=5.6,<5.7.2", "ibexa/admin-ui": ">=4.2,<4.2.3|>=4.6,<4.6.14", "ibexa/core": ">=4,<4.0.7|>=4.1,<4.1.4|>=4.2,<4.2.3|>=4.5,<4.5.6|>=4.6,<4.6.2", "ibexa/fieldtype-richtext": ">=4.6,<4.6.10", "ibexa/graphql": ">=2.5,<2.5.31|>=3.3,<3.3.28|>=4.2,<4.2.3", "ibexa/http-cache": ">=4.6,<4.6.14", "ibexa/post-install": "<1.0.16|>=4.6,<4.6.14", "ibexa/solr": ">=4.5,<4.5.4", "ibexa/user": ">=4,<4.4.3", "icecoder/icecoder": "<=8.1", "idno/known": "<=1.3.1", "ilicmiljan/secure-props": ">=1.2,<1.2.2", "illuminate/auth": "<5.5.10", "illuminate/cookie": ">=4,<=4.0.11|>=4.1,<6.18.31|>=7,<7.22.4", "illuminate/database": "<6.20.26|>=7,<7.30.5|>=8,<8.40", "illuminate/encryption": ">=4,<=4.0.11|>=4.1,<=4.1.31|>=4.2,<=4.2.22|>=5,<=5.0.35|>=5.1,<=5.1.46|>=5.2,<=5.2.45|>=5.3,<=5.3.31|>=5.4,<=5.4.36|>=5.5,<5.5.40|>=5.6,<5.6.15", "illuminate/view": "<6.20.42|>=7,<7.30.6|>=8,<8.75", "imdbphp/imdbphp": "<=5.1.1", "impresscms/impresscms": "<=1.4.5", "impresspages/impresspages": "<=1.0.12", "in2code/femanager": "<5.5.3|>=6,<6.3.4|>=7,<7.2.3", "in2code/ipandlanguageredirect": "<5.1.2", "in2code/lux": "<17.6.1|>=18,<24.0.2", "in2code/powermail": "<7.5.1|>=8,<8.5.1|>=9,<10.9.1|>=11,<12.4.1", "innologi/typo3-appointments": "<2.0.6", "intelliants/subrion": "<4.2.2", "inter-mediator/inter-mediator": "==5.5", "ipl/web": "<0.10.1", "islandora/crayfish": "<4.1", "islandora/islandora": ">=2,<2.4.1", "ivankristianto/phpwhois": "<=4.3", "jackalope/jackalope-doctrine-dbal": "<1.7.4", "james-heinrich/getid3": "<1.9.21", "james-heinrich/phpthumb": "<1.7.12", "jasig/phpcas": "<1.3.3", "jcbrand/converse.js": "<3.3.3", "joelbutcher/socialstream": "<5.6|>=6,<6.2", "johnbillion/wp-crontrol": "<1.16.2", "joomla/application": "<1.0.13", "joomla/archive": "<1.1.12|>=2,<2.0.1", "joomla/filesystem": "<1.6.2|>=2,<2.0.1", "joomla/filter": "<1.4.4|>=2,<2.0.1", "joomla/framework": "<1.5.7|>=2.5.4,<=3.8.12", "joomla/input": ">=2,<2.0.2", "joomla/joomla-cms": ">=2.5,<3.9.12", "joomla/session": "<1.3.1", "joyqi/hyper-down": "<=2.4.27", "jsdecena/laracom": "<2.0.9", "jsmitty12/phpwhois": "<5.1", "juzaweb/cms": "<=3.4", "jweiland/events2": "<8.3.8|>=9,<9.0.6", "kazist/phpwhois": "<=4.2.6", "kelvinmo/simplexrd": "<3.1.1", "kevinpapst/kimai2": "<1.16.7", "khodakhah/nodcms": "<=3", "kimai/kimai": "<=2.20.1", "kitodo/presentation": "<3.2.3|>=3.3,<3.3.4", "klaviyo/magento2-extension": ">=1,<3", "knplabs/knp-snappy": "<=1.4.2", "kohana/core": "<3.3.3", "krayin/laravel-crm": "<=1.3", "kreait/firebase-php": ">=3.2,<3.8.1", "kumbiaphp/kumbiapp": "<=1.1.1", "la-haute-societe/tcpdf": "<6.2.22", "laminas/laminas-diactoros": "<2.18.1|==2.19|==2.20|==2.21|==2.22|==2.23|>=2.24,<2.24.2|>=2.25,<2.25.2", "laminas/laminas-form": "<2.17.1|>=3,<3.0.2|>=3.1,<3.1.1", "laminas/laminas-http": "<2.14.2", "lara-zeus/artemis": ">=1,<=1.0.6", "lara-zeus/dynamic-dashboard": ">=3,<=3.0.1", "laravel/fortify": "<1.11.1", "laravel/framework": "<6.20.45|>=7,<7.30.7|>=8,<8.83.28|>=9,<9.52.17|>=10,<10.48.23|>=11,<11.31", "laravel/laravel": ">=5.4,<5.4.22", "laravel/pulse": "<1.3.1", "laravel/reverb": "<1.4", "laravel/socialite": ">=1,<2.0.10", "latte/latte": "<2.10.8", "lavalite/cms": "<=9|==10.1", "lcobucci/jwt": ">=3.4,<3.4.6|>=4,<4.0.4|>=4.1,<4.1.5", "league/commonmark": "<2.6", "league/flysystem": "<1.1.4|>=2,<2.1.1", "league/oauth2-server": ">=8.3.2,<8.4.2|>=8.5,<8.5.3", "lexik/jwt-authentication-bundle": "<2.10.7|>=2.11,<2.11.3", "libreform/libreform": ">=2,<=2.0.8", "librenms/librenms": "<2017.08.18", "liftkit/database": "<2.13.2", "lightsaml/lightsaml": "<1.3.5", "limesurvey/limesurvey": "<6.5.12", "livehelperchat/livehelperchat": "<=3.91", "livewire/livewire": "<2.12.7|>=3.0.0.0-beta1,<3.5.2", "lms/routes": "<2.1.1", "localizationteam/l10nmgr": "<7.4|>=8,<8.7|>=9,<9.2", "luyadev/yii-helpers": "<1.2.1", "maestroerror/php-heic-to-jpg": "<1.0.5", "magento/community-edition": "<2.4.5|==2.4.5|>=2.4.5.0-patch1,<2.4.5.0-patch10|==2.4.6|>=2.4.6.0-patch1,<2.4.6.0-patch8|>=2.4.7.0-beta1,<2.4.7.0-patch3", "magento/core": "<=1.9.4.5", "magento/magento1ce": "<1.9.4.3-dev", "magento/magento1ee": ">=1,<1.14.4.3-dev", "magento/product-community-edition": "<2.4.4.0-patch9|>=2.4.5,<2.4.5.0-patch8|>=2.4.6,<2.4.6.0-patch6|>=2.4.7,<2.4.7.0-patch1", "magneto/core": "<1.9.4.4-dev", "maikuolan/phpmussel": ">=1,<1.6", "mainwp/mainwp": "<=4.4.3.3", "mantisbt/mantisbt": "<=2.26.3", "marcwillmann/turn": "<0.3.3", "matyhtf/framework": "<3.0.6", "mautic/core": "<4.4.13|>=5,<5.1.1", "mautic/core-lib": ">=1.0.0.0-beta,<4.4.13|>=*******-alpha,<5.1.1", "maximebf/debugbar": "<1.19", "mdanter/ecc": "<2", "mediawiki/abuse-filter": "<1.39.9|>=1.40,<1.41.3|>=1.42,<1.42.2", "mediawiki/cargo": "<3.6.1", "mediawiki/core": "<1.39.5|==1.40", "mediawiki/data-transfer": ">=1.39,<1.39.11|>=1.41,<1.41.3|>=1.42,<1.42.2", "mediawiki/matomo": "<2.4.3", "mediawiki/semantic-media-wiki": "<4.0.2", "melisplatform/melis-asset-manager": "<5.0.1", "melisplatform/melis-cms": "<5.0.1", "melisplatform/melis-front": "<5.0.1", "mezzio/mezzio-swoole": "<3.7|>=4,<4.3", "mgallegos/laravel-jqgrid": "<=1.3", "microsoft/microsoft-graph": ">=1.16,<1.109.1|>=2,<2.0.1", "microsoft/microsoft-graph-beta": "<2.0.1", "microsoft/microsoft-graph-core": "<2.0.2", "microweber/microweber": "<=2.0.16", "mikehaertl/php-shellcommand": "<1.6.1", "miniorange/miniorange-saml": "<1.4.3", "mittwald/typo3_forum": "<1.2.1", "mobiledetect/mobiledetectlib": "<2.8.32", "modx/revolution": "<=2.8.3.0-patch", "mojo42/jirafeau": "<4.4", "mongodb/mongodb": ">=1,<1.9.2", "monolog/monolog": ">=1.8,<1.12", "moodle/moodle": "<4.3.8|>=4.4,<4.4.4", "mos/cimage": "<0.7.19", "movim/moxl": ">=0.8,<=0.10", "movingbytes/social-network": "<=1.2.1", "mpdf/mpdf": "<=7.1.7", "munkireport/comment": "<4.1", "munkireport/managedinstalls": "<2.6", "munkireport/munki_facts": "<1.5", "munkireport/munkireport": ">=2.5.3,<5.6.3", "munkireport/reportdata": "<3.5", "munkireport/softwareupdate": "<1.6", "mustache/mustache": ">=2,<2.14.1", "namshi/jose": "<2.2", "nategood/httpful": "<1", "neoan3-apps/template": "<1.1.1", "neorazorx/facturascripts": "<2022.04", "neos/flow": ">=1,<1.0.4|>=1.1,<1.1.1|>=2,<2.0.1|>=2.3,<2.3.16|>=3,<3.0.12|>=3.1,<3.1.10|>=3.2,<3.2.13|>=3.3,<3.3.13|>=4,<4.0.6", "neos/form": ">=1.2,<4.3.3|>=5,<5.0.9|>=5.1,<5.1.3", "neos/media-browser": "<7.3.19|>=8,<8.0.16|>=8.1,<8.1.11|>=8.2,<8.2.11|>=8.3,<8.3.9", "neos/neos": ">=1.1,<1.1.3|>=1.2,<1.2.13|>=2,<2.0.4|>=2.3,<3.0.20|>=3.1,<3.1.18|>=3.2,<3.2.14|>=3.3,<5.3.10|>=7,<7.0.9|>=7.1,<7.1.7|>=7.2,<7.2.6|>=7.3,<7.3.4|>=8,<8.0.2", "neos/swiftmailer": "<5.4.5", "nesbot/carbon": "<2.72.6|>=3,<3.8.4", "netcarver/textile": "<=4.1.2", "netgen/tagsbundle": ">=3.4,<3.4.11|>=4,<4.0.15", "nette/application": ">=2,<2.0.19|>=2.1,<2.1.13|>=2.2,<2.2.10|>=2.3,<2.3.14|>=2.4,<2.4.16|>=3,<3.0.6", "nette/nette": ">=2,<2.0.19|>=2.1,<2.1.13", "nilsteampassnet/teampass": "<*******-dev", "nonfiction/nterchange": "<4.1.1", "notrinos/notrinos-erp": "<=0.7", "noumo/easyii": "<=0.9", "novaksolutions/infusionsoft-php-sdk": "<1", "nukeviet/nukeviet": "<4.5.02", "nyholm/psr7": "<1.6.1", "nystudio107/craft-seomatic": "<3.4.12", "nzedb/nzedb": "<0.8", "nzo/url-encryptor-bundle": ">=4,<4.3.2|>=5,<5.0.1", "october/backend": "<1.1.2", "october/cms": "<1.0.469|==1.0.469|==1.0.471|==1.1.1", "october/october": "<=3.6.4", "october/rain": "<1.0.472|>=1.1,<1.1.2", "october/system": "<1.0.476|>=1.1,<1.1.12|>=2,<2.2.34|>=3,<3.5.15", "omeka/omeka-s": "<4.0.3", "onelogin/php-saml": "<2.10.4", "oneup/uploader-bundle": ">=1,<1.9.3|>=2,<2.1.5", "open-web-analytics/open-web-analytics": "<1.7.4", "opencart/opencart": ">=0", "openid/php-openid": "<2.3", "openmage/magento-lts": "<20.10.1", "opensolutions/vimbadmin": "<=3.0.15", "opensource-workshop/connect-cms": "<1.7.2|>=2,<2.3.2", "orchid/platform": ">=8,<14.43", "oro/calendar-bundle": ">=4.2,<=4.2.6|>=5,<=5.0.6|>=5.1,<5.1.1", "oro/commerce": ">=4.1,<5.0.11|>=5.1,<5.1.1", "oro/crm": ">=1.7,<1.7.4|>=3.1,<4.1.17|>=4.2,<4.2.7", "oro/crm-call-bundle": ">=4.2,<=4.2.5|>=5,<5.0.4|>=5.1,<5.1.1", "oro/customer-portal": ">=4.1,<=4.1.13|>=4.2,<=4.2.10|>=5,<=5.0.11|>=5.1,<=5.1.3", "oro/platform": ">=1.7,<1.7.4|>=3.1,<3.1.29|>=4.1,<4.1.17|>=4.2,<=4.2.10|>=5,<=5.0.12|>=5.1,<=5.1.3", "oveleon/contao-cookiebar": "<1.16.3|>=2,<2.1.3", "oxid-esales/oxideshop-ce": "<4.5", "oxid-esales/paymorrow-module": ">=1,<1.0.2|>=2,<2.0.1", "packbackbooks/lti-1-3-php-library": "<5", "padraic/humbug_get_contents": "<1.1.2", "pagarme/pagarme-php": "<3", "pagekit/pagekit": "<=1.0.18", "paragonie/ecc": "<2.0.1", "paragonie/random_compat": "<2", "passbolt/passbolt_api": "<4.6.2", "paypal/adaptivepayments-sdk-php": "<=3.9.2", "paypal/invoice-sdk-php": "<=3.9", "paypal/merchant-sdk-php": "<3.12", "paypal/permissions-sdk-php": "<=3.9.1", "pear/archive_tar": "<1.4.14", "pear/auth": "<1.2.4", "pear/crypt_gpg": "<1.6.7", "pear/pear": "<=1.10.1", "pegasus/google-for-jobs": "<1.5.1|>=2,<2.1.1", "personnummer/personnummer": "<3.0.2", "phanan/koel": "<5.1.4", "phenx/php-svg-lib": "<0.5.2", "php-censor/php-censor": "<2.0.13|>=2.1,<2.1.5", "php-mod/curl": "<2.3.2", "phpbb/phpbb": "<3.3.11", "phpems/phpems": ">=6,<=6.1.3", "phpfastcache/phpfastcache": "<6.1.5|>=7,<7.1.2|>=8,<8.0.7", "phpmailer/phpmailer": "<6.5", "phpmussel/phpmussel": ">=1,<1.6", "phpmyadmin/phpmyadmin": "<5.2.2", "phpmyfaq/phpmyfaq": "<3.2.5|==3.2.5|>=3.2.10,<=4.0.1", "phpoffice/common": "<0.2.9", "phpoffice/phpexcel": "<1.8.1", "phpoffice/phpspreadsheet": "<1.29.8|>=2,<2.1.7|>=2.2,<2.3.6|>=3,<3.8", "phpseclib/phpseclib": "<2.0.47|>=3,<3.0.36", "phpservermon/phpservermon": "<3.6", "phpsysinfo/phpsysinfo": "<3.4.3", "phpunit/phpunit": ">=4.8.19,<4.8.28|>=5.0.10,<5.6.3", "phpwhois/phpwhois": "<=4.2.5", "phpxmlrpc/extras": "<0.6.1", "phpxmlrpc/phpxmlrpc": "<4.9.2", "pi/pi": "<=2.5", "pimcore/admin-ui-classic-bundle": "<1.5.4", "pimcore/customer-management-framework-bundle": "<4.0.6", "pimcore/data-hub": "<1.2.4", "pimcore/data-importer": "<1.8.9|>=1.9,<1.9.3", "pimcore/demo": "<10.3", "pimcore/ecommerce-framework-bundle": "<1.0.10", "pimcore/perspective-editor": "<1.5.1", "pimcore/pimcore": "<11.2.4", "pixelfed/pixelfed": "<0.11.11", "plotly/plotly.js": "<2.25.2", "pocketmine/bedrock-protocol": "<8.0.2", "pocketmine/pocketmine-mp": "<5.11.2", "pocketmine/raklib": ">=0.14,<0.14.6|>=0.15,<0.15.1", "pressbooks/pressbooks": "<5.18", "prestashop/autoupgrade": ">=4,<4.10.1", "prestashop/blockreassurance": "<=5.1.3", "prestashop/blockwishlist": ">=2,<2.1.1", "prestashop/contactform": ">=1.0.1,<4.3", "prestashop/gamification": "<2.3.2", "prestashop/prestashop": "<8.1.6", "prestashop/productcomments": "<5.0.2", "prestashop/ps_contactinfo": "<=3.3.2", "prestashop/ps_emailsubscription": "<2.6.1", "prestashop/ps_facetedsearch": "<3.4.1", "prestashop/ps_linklist": "<3.1", "privatebin/privatebin": "<1.4|>=1.5,<1.7.4", "processwire/processwire": "<=3.0.229", "propel/propel": ">=*******-alpha1,<=*******-alpha7", "propel/propel1": ">=1,<=1.7.1", "pterodactyl/panel": "<1.11.8", "ptheofan/yii2-statemachine": ">=*******-RC1-dev,<=2", "ptrofimov/beanstalk_console": "<1.7.14", "pubnub/pubnub": "<6.1", "pusher/pusher-php-server": "<2.2.1", "pwweb/laravel-core": "<=*******-beta", "pxlrbt/filament-excel": "<1.1.14|>=*******-alpha,<2.3.3", "pyrocms/pyrocms": "<=3.9.1", "qcubed/qcubed": "<=3.1.1", "quickapps/cms": "<=*******-beta2", "rainlab/blog-plugin": "<1.4.1", "rainlab/debugbar-plugin": "<3.1", "rainlab/user-plugin": "<=1.4.5", "rankmath/seo-by-rank-math": "<=1.0.95", "rap2hpoutre/laravel-log-viewer": "<0.13", "react/http": ">=0.7,<1.9", "really-simple-plugins/complianz-gdpr": "<6.4.2", "redaxo/source": "<5.18", "remdex/livehelperchat": "<4.29", "reportico-web/reportico": "<=8.1", "rhukster/dom-sanitizer": "<1.0.7", "rmccue/requests": ">=1.6,<1.8", "robrichards/xmlseclibs": ">=1,<3.0.4", "roots/soil": "<4.1", "rudloff/alltube": "<3.0.3", "s-cart/core": "<6.9", "s-cart/s-cart": "<6.9", "sabberworm/php-css-parser": ">=1,<1.0.1|>=2,<2.0.1|>=3,<3.0.1|>=4,<4.0.1|>=5,<5.0.9|>=5.1,<5.1.3|>=5.2,<5.2.1|>=6,<6.0.2|>=7,<7.0.4|>=8,<8.0.1|>=8.1,<8.1.1|>=8.2,<8.2.1|>=8.3,<8.3.1", "sabre/dav": ">=1.6,<1.7.11|>=1.8,<1.8.9", "samwilson/unlinked-wikibase": "<1.39.6|>=1.40,<1.40.2|>=1.41,<1.41.1", "scheb/two-factor-bundle": "<3.26|>=4,<4.11", "sensiolabs/connect": "<4.2.3", "serluck/phpwhois": "<=4.2.6", "sfroemken/url_redirect": "<=1.2.1", "sheng/yiicms": "<1.2.1", "shopware/core": "<=********|>=6.6,<=6.6.5", "shopware/platform": "<=********|>=6.6,<=6.6.5", "shopware/production": "<=*******", "shopware/shopware": "<=5.7.17", "shopware/storefront": "<=*******|>=6.5.8,<*******-dev", "shopxo/shopxo": "<=6.1", "showdoc/showdoc": "<2.10.4", "shuchkin/simplexlsx": ">=1.0.12,<1.1.13", "silverstripe-australia/advancedreports": ">=1,<=2", "silverstripe/admin": "<1.13.19|>=2,<2.1.8", "silverstripe/assets": ">=1,<1.11.1", "silverstripe/cms": "<4.11.3", "silverstripe/comments": ">=1.3,<3.1.1", "silverstripe/forum": "<=0.6.1|>=0.7,<=0.7.3", "silverstripe/framework": "<5.3.8", "silverstripe/graphql": ">=2,<2.0.5|>=3,<3.8.2|>=4,<4.3.7|>=5,<5.1.3", "silverstripe/hybridsessions": ">=1,<2.4.1|>=2.5,<2.5.1", "silverstripe/recipe-cms": ">=4.5,<4.5.3", "silverstripe/registry": ">=2.1,<2.1.2|>=2.2,<2.2.1", "silverstripe/reports": "<5.2.3", "silverstripe/restfulserver": ">=1,<1.0.9|>=2,<2.0.4|>=2.1,<2.1.2", "silverstripe/silverstripe-omnipay": "<2.5.2|>=3,<3.0.2|>=3.1,<3.1.4|>=3.2,<3.2.1", "silverstripe/subsites": ">=2,<2.6.1", "silverstripe/taxonomy": ">=1.3,<1.3.1|>=2,<2.0.1", "silverstripe/userforms": "<3|>=5,<5.4.2", "silverstripe/versioned-admin": ">=1,<1.11.1", "simple-updates/phpwhois": "<=1", "simplesamlphp/saml2": "<4.6.14|==*******-alpha12", "simplesamlphp/saml2-legacy": "<4.6.14", "simplesamlphp/simplesamlphp": "<1.18.6", "simplesamlphp/simplesamlphp-module-infocard": "<1.0.1", "simplesamlphp/simplesamlphp-module-openid": "<1", "simplesamlphp/simplesamlphp-module-openidprovider": "<0.9", "simplesamlphp/xml-common": "<1.20", "simplesamlphp/xml-security": "==1.6.11", "simplito/elliptic-php": "<1.0.6", "sitegeist/fluid-components": "<3.5", "sjbr/sr-freecap": "<2.4.6|>=2.5,<2.5.3", "slim/psr7": "<1.4.1|>=1.5,<1.5.1|>=1.6,<1.6.1", "slim/slim": "<2.6", "slub/slub-events": "<3.0.3", "smarty/smarty": "<4.5.3|>=5,<5.1.1", "snipe/snipe-it": "<=7.0.13", "socalnick/scn-social-auth": "<1.15.2", "socialiteproviders/steam": "<1.1", "spatie/browsershot": "<5.0.3", "spatie/image-optimizer": "<1.7.3", "spencer14420/sp-php-email-handler": "<1", "spipu/html2pdf": "<5.2.8", "spoon/library": "<1.4.1", "spoonity/tcpdf": "<6.2.22", "squizlabs/php_codesniffer": ">=1,<2.8.1|>=3,<3.0.1", "ssddanbrown/bookstack": "<24.05.1", "starcitizentools/citizen-skin": ">=2.6.3,<2.31", "starcitizentools/tabber-neue": ">=1.9.1,<2.7.2", "statamic/cms": "<=5.16", "stormpath/sdk": "<9.9.99", "studio-42/elfinder": "<=2.1.64", "studiomitte/friendlycaptcha": "<0.1.4", "subhh/libconnect": "<7.0.8|>=8,<8.1", "sukohi/surpass": "<1", "sulu/form-bundle": ">=2,<2.5.3", "sulu/sulu": "<1.6.44|>=2,<2.5.21|>=2.6,<2.6.5", "sumocoders/framework-user-bundle": "<1.4", "superbig/craft-audit": "<3.0.2", "swag/paypal": "<5.4.4", "swiftmailer/swiftmailer": "<6.2.5", "swiftyedit/swiftyedit": "<1.2", "sylius/admin-bundle": ">=1,<1.0.17|>=1.1,<1.1.9|>=1.2,<1.2.2", "sylius/grid": ">=1,<1.1.19|>=1.2,<1.2.18|>=1.3,<1.3.13|>=1.4,<1.4.5|>=1.5,<1.5.1", "sylius/grid-bundle": "<1.10.1", "sylius/paypal-plugin": ">=1,<1.2.4|>=1.3,<1.3.1", "sylius/resource-bundle": ">=1,<1.3.14|>=1.4,<1.4.7|>=1.5,<1.5.2|>=1.6,<1.6.4", "sylius/sylius": "<1.12.19|>=********-alpha1,<1.13.4", "symbiote/silverstripe-multivaluefield": ">=3,<3.1", "symbiote/silverstripe-queuedjobs": ">=3,<3.0.2|>=3.1,<3.1.4|>=4,<4.0.7|>=4.1,<4.1.2|>=4.2,<4.2.4|>=4.3,<4.3.3|>=4.4,<4.4.3|>=4.5,<4.5.1|>=4.6,<4.6.4", "symbiote/silverstripe-seed": "<6.0.3", "symbiote/silverstripe-versionedfiles": "<=2.0.3", "symfont/process": ">=0", "symfony/cache": ">=3.1,<3.4.35|>=4,<4.2.12|>=4.3,<4.3.8", "symfony/dependency-injection": ">=2,<2.0.17|>=2.7,<2.7.51|>=2.8,<2.8.50|>=3,<3.4.26|>=4,<4.1.12|>=4.2,<4.2.7", "symfony/error-handler": ">=4.4,<4.4.4|>=5,<5.0.4", "symfony/form": ">=2.3,<2.3.35|>=2.4,<2.6.12|>=2.7,<2.7.50|>=2.8,<2.8.49|>=3,<3.4.20|>=4,<4.0.15|>=4.1,<4.1.9|>=4.2,<4.2.1", "symfony/framework-bundle": ">=2,<2.3.18|>=2.4,<2.4.8|>=2.5,<2.5.2|>=2.7,<2.7.51|>=2.8,<2.8.50|>=3,<3.4.26|>=4,<4.1.12|>=4.2,<4.2.7|>=5.3.14,<5.3.15|>=5.4.3,<5.4.4|>=6.0.3,<6.0.4", "symfony/http-client": ">=4.3,<5.4.47|>=6,<6.4.15|>=7,<7.1.8", "symfony/http-foundation": "<5.4.46|>=6,<6.4.14|>=7,<7.1.7", "symfony/http-kernel": ">=2,<4.4.50|>=5,<5.4.20|>=6,<6.0.20|>=6.1,<6.1.12|>=6.2,<6.2.6", "symfony/intl": ">=2.7,<2.7.38|>=2.8,<2.8.31|>=3,<3.2.14|>=3.3,<3.3.13", "symfony/maker-bundle": ">=1.27,<1.29.2|>=1.30,<1.31.1", "symfony/mime": ">=4.3,<4.3.8", "symfony/phpunit-bridge": ">=2.8,<2.8.50|>=3,<3.4.26|>=4,<4.1.12|>=4.2,<4.2.7", "symfony/polyfill": ">=1,<1.10", "symfony/polyfill-php55": ">=1,<1.10", "symfony/process": "<5.4.46|>=6,<6.4.14|>=7,<7.1.7", "symfony/proxy-manager-bridge": ">=2.7,<2.7.51|>=2.8,<2.8.50|>=3,<3.4.26|>=4,<4.1.12|>=4.2,<4.2.7", "symfony/routing": ">=2,<2.0.19", "symfony/runtime": ">=5.3,<5.4.46|>=6,<6.4.14|>=7,<7.1.7", "symfony/security": ">=2,<2.7.51|>=2.8,<3.4.49|>=4,<4.4.24|>=5,<5.2.8", "symfony/security-bundle": ">=2,<4.4.50|>=5,<5.4.20|>=6,<6.0.20|>=6.1,<6.1.12|>=6.2,<6.4.10|>=7,<7.0.10|>=7.1,<7.1.3", "symfony/security-core": ">=2.4,<2.6.13|>=2.7,<2.7.9|>=2.7.30,<2.7.32|>=2.8,<3.4.49|>=4,<4.4.24|>=5,<5.2.9", "symfony/security-csrf": ">=2.4,<2.7.48|>=2.8,<2.8.41|>=3,<3.3.17|>=3.4,<3.4.11|>=4,<4.0.11", "symfony/security-guard": ">=2.8,<3.4.48|>=4,<4.4.23|>=5,<5.2.8", "symfony/security-http": ">=2.3,<2.3.41|>=2.4,<2.7.51|>=2.8,<2.8.50|>=3,<3.4.26|>=4,<4.2.12|>=4.3,<4.3.8|>=4.4,<4.4.7|>=5,<5.0.7|>=5.1,<5.2.8|>=5.3,<5.4.47|>=6,<6.4.15|>=7,<7.1.8", "symfony/serializer": ">=2,<2.0.11|>=4.1,<4.4.35|>=5,<5.3.12", "symfony/symfony": "<5.4.47|>=6,<6.4.15|>=7,<7.1.8", "symfony/translation": ">=2,<2.0.17", "symfony/twig-bridge": ">=2,<4.4.51|>=5,<5.4.31|>=6,<6.3.8", "symfony/ux-autocomplete": "<2.11.2", "symfony/validator": "<5.4.43|>=6,<6.4.11|>=7,<7.1.4", "symfony/var-exporter": ">=4.2,<4.2.12|>=4.3,<4.3.8", "symfony/web-profiler-bundle": ">=2,<2.3.19|>=2.4,<2.4.9|>=2.5,<2.5.4", "symfony/webhook": ">=6.3,<6.3.8", "symfony/yaml": ">=2,<2.0.22|>=2.1,<2.1.7|>=2.2.0.0-beta1,<2.2.0.0-beta2", "symphonycms/symphony-2": "<2.6.4", "t3/dce": "<0.11.5|>=2.2,<2.6.2", "t3g/svg-sanitizer": "<1.0.3", "t3s/content-consent": "<1.0.3|>=2,<2.0.2", "tastyigniter/tastyigniter": "<3.3", "tcg/voyager": "<=1.4", "tecnickcom/tc-lib-pdf-font": "<2.6.4", "tecnickcom/tcpdf": "<6.8", "terminal42/contao-tablelookupwizard": "<3.3.5", "thelia/backoffice-default-template": ">=2.1,<2.1.2", "thelia/thelia": ">=2.1,<2.1.3", "theonedemon/phpwhois": "<=4.2.5", "thinkcmf/thinkcmf": "<6.0.8", "thorsten/phpmyfaq": "<=4.0.1", "tikiwiki/tiki-manager": "<=17.1", "timber/timber": ">=0.16.6,<1.23.1|>=1.24,<1.24.1|>=2,<2.1", "tinymce/tinymce": "<7.2", "tinymighty/wiki-seo": "<1.2.2", "titon/framework": "<9.9.99", "tltneon/lgsl": "<7", "tobiasbg/tablepress": "<=*******-RC1", "topthink/framework": "<6.0.17|>=6.1,<=8.0.4", "topthink/think": "<=6.1.1", "topthink/thinkphp": "<=3.2.3|>=6.1.3,<=8.0.4", "torrentpier/torrentpier": "<=2.4.3", "tpwd/ke_search": "<4.0.3|>=4.1,<4.6.6|>=5,<5.0.2", "tribalsystems/zenario": "<=9.7.61188", "truckersmp/phpwhois": "<=4.3.1", "ttskch/pagination-service-provider": "<1", "twbs/bootstrap": "<=3.4.1|>=4,<=4.6.2", "twig/twig": "<3.11.2|>=3.12,<3.14.1", "typo3/cms": "<9.5.29|>=10,<10.4.35|>=11,<11.5.23|>=12,<12.2", "typo3/cms-backend": "<4.1.14|>=4.2,<4.2.15|>=4.3,<4.3.7|>=4.4,<4.4.4|>=7,<=7.6.50|>=8,<=8.7.39|>=9,<=9.5.24|>=10,<10.4.46|>=11,<11.5.40|>=12,<12.4.21|>=13,<13.3.1", "typo3/cms-belog": ">=10,<=10.4.47|>=11,<=11.5.41|>=12,<=12.4.24|>=13,<=13.4.2", "typo3/cms-beuser": ">=10,<=10.4.47|>=11,<=11.5.41|>=12,<=12.4.24|>=13,<=13.4.2", "typo3/cms-core": "<=8.7.56|>=9,<=9.5.48|>=10,<=10.4.47|>=11,<=11.5.41|>=12,<=12.4.24|>=13,<=13.4.2", "typo3/cms-dashboard": ">=10,<=10.4.47|>=11,<=11.5.41|>=12,<=12.4.24|>=13,<=13.4.2", "typo3/cms-extbase": "<6.2.24|>=7,<7.6.8|==8.1.1", "typo3/cms-extensionmanager": ">=10,<=10.4.47|>=11,<=11.5.41|>=12,<=12.4.24|>=13,<=13.4.2", "typo3/cms-fluid": "<4.3.4|>=4.4,<4.4.1", "typo3/cms-form": ">=8,<=8.7.39|>=9,<=9.5.24|>=10,<=10.4.47|>=11,<=11.5.41|>=12,<=12.4.24|>=13,<=13.4.2", "typo3/cms-frontend": "<4.3.9|>=4.4,<4.4.5", "typo3/cms-indexed-search": ">=10,<=10.4.47|>=11,<=11.5.41|>=12,<=12.4.24|>=13,<=13.4.2", "typo3/cms-install": "<4.1.14|>=4.2,<4.2.16|>=4.3,<4.3.9|>=4.4,<4.4.5|>=12.2,<12.4.8|==13.4.2", "typo3/cms-lowlevel": ">=11,<=11.5.41", "typo3/cms-rte-ckeditor": ">=9.5,<9.5.42|>=10,<10.4.39|>=11,<11.5.30", "typo3/cms-scheduler": ">=11,<=11.5.41", "typo3/flow": ">=1,<1.0.4|>=1.1,<1.1.1|>=2,<2.0.1|>=2.3,<2.3.16|>=3,<3.0.12|>=3.1,<3.1.10|>=3.2,<3.2.13|>=3.3,<3.3.13|>=4,<4.0.6", "typo3/html-sanitizer": ">=1,<=1.5.2|>=2,<=2.1.3", "typo3/neos": ">=1.1,<1.1.3|>=1.2,<1.2.13|>=2,<2.0.4|>=2.3,<2.3.99|>=3,<3.0.20|>=3.1,<3.1.18|>=3.2,<3.2.14|>=3.3,<3.3.23|>=4,<4.0.17|>=4.1,<4.1.16|>=4.2,<4.2.12|>=4.3,<4.3.3", "typo3/phar-stream-wrapper": ">=1,<2.1.1|>=3,<3.1.1", "typo3/swiftmailer": ">=4.1,<4.1.99|>=5.4,<5.4.5", "typo3fluid/fluid": ">=2,<2.0.8|>=2.1,<2.1.7|>=2.2,<2.2.4|>=2.3,<2.3.7|>=2.4,<2.4.4|>=2.5,<2.5.11|>=2.6,<2.6.10", "ua-parser/uap-php": "<3.8", "uasoft-indonesia/badaso": "<=2.9.7", "unisharp/laravel-filemanager": "<2.9.1", "unopim/unopim": "<0.1.5", "userfrosting/userfrosting": ">=0.3.1,<4.6.3", "usmanhalalit/pixie": "<1.0.3|>=2,<2.0.2", "uvdesk/community-skeleton": "<=1.1.1", "uvdesk/core-framework": "<=1.1.1", "vanilla/safecurl": "<0.9.2", "verbb/comments": "<1.5.5", "verbb/formie": "<2.1.6", "verbb/image-resizer": "<2.0.9", "verbb/knock-knock": "<1.2.8", "verot/class.upload.php": "<=2.1.6", "villagedefrance/opencart-overclocked": "<=1.11.1", "vova07/yii2-fileapi-widget": "<0.1.9", "vrana/adminer": "<4.8.1", "vufind/vufind": ">=2,<9.1.1", "waldhacker/hcaptcha": "<2.1.2", "wallabag/tcpdf": "<6.2.22", "wallabag/wallabag": "<2.6.7", "wanglelecc/laracms": "<=1.0.3", "web-auth/webauthn-framework": ">=3.3,<3.3.4|>=4.5,<4.9", "web-auth/webauthn-lib": ">=4.5,<4.9", "web-feet/coastercms": "==5.5", "webbuilders-group/silverstripe-kapost-bridge": "<0.4", "webcoast/deferred-image-processing": "<1.0.2", "webklex/laravel-imap": "<5.3", "webklex/php-imap": "<5.3", "webpa/webpa": "<3.1.2", "wikibase/wikibase": "<=1.39.3", "wikimedia/parsoid": "<0.12.2", "willdurand/js-translation-bundle": "<2.1.1", "winter/wn-backend-module": "<1.2.4", "winter/wn-cms-module": "<1.0.476|>=1.1,<1.1.11|>=1.2,<1.2.7", "winter/wn-dusk-plugin": "<2.1", "winter/wn-system-module": "<1.2.4", "wintercms/winter": "<=1.2.3", "wireui/wireui": "<1.19.3|>=2,<2.1.3", "woocommerce/woocommerce": "<6.6|>=8.8,<8.8.5|>=8.9,<8.9.3", "wp-cli/wp-cli": ">=0.12,<2.5", "wp-graphql/wp-graphql": "<=1.14.5", "wp-premium/gravityforms": "<2.4.21", "wpanel/wpanel4-cms": "<=4.3.1", "wpcloud/wp-stateless": "<3.2", "wpglobus/wpglobus": "<=1.9.6", "wwbn/avideo": "<14.3", "xataface/xataface": "<3", "xpressengine/xpressengine": "<3.0.15", "yab/quarx": "<2.4.5", "yeswiki/yeswiki": "<=4.4.5", "yetiforce/yetiforce-crm": "<6.5", "yidashi/yii2cmf": "<=2", "yii2mod/yii2-cms": "<1.9.2", "yiisoft/yii": "<1.1.29", "yiisoft/yii2": "<2.0.49.4-dev", "yiisoft/yii2-authclient": "<2.2.15", "yiisoft/yii2-bootstrap": "<2.0.4", "yiisoft/yii2-dev": "<2.0.43", "yiisoft/yii2-elasticsearch": "<2.0.5", "yiisoft/yii2-gii": "<=2.2.4", "yiisoft/yii2-jui": "<2.0.4", "yiisoft/yii2-redis": "<2.0.8", "yikesinc/yikes-inc-easy-mailchimp-extender": "<6.8.6", "yoast-seo-for-typo3/yoast_seo": "<7.2.3", "yourls/yourls": "<=1.8.2", "yuan1994/tpadmin": "<=1.3.12", "zencart/zencart": "<=1.5.7.0-beta", "zendesk/zendesk_api_client_php": "<2.2.11", "zendframework/zend-cache": ">=2.4,<2.4.8|>=2.5,<2.5.3", "zendframework/zend-captcha": ">=2,<2.4.9|>=2.5,<2.5.2", "zendframework/zend-crypt": ">=2,<2.4.9|>=2.5,<2.5.2", "zendframework/zend-db": "<2.2.10|>=2.3,<2.3.5", "zendframework/zend-developer-tools": ">=1.2.2,<1.2.3", "zendframework/zend-diactoros": "<1.8.4", "zendframework/zend-feed": "<2.10.3", "zendframework/zend-form": ">=2,<2.2.7|>=2.3,<2.3.1", "zendframework/zend-http": "<2.8.1", "zendframework/zend-json": ">=2.1,<2.1.6|>=2.2,<2.2.6", "zendframework/zend-ldap": ">=2,<2.0.99|>=2.1,<2.1.99|>=2.2,<2.2.8|>=2.3,<2.3.3", "zendframework/zend-mail": "<2.4.11|>=2.5,<2.7.2", "zendframework/zend-navigation": ">=2,<2.2.7|>=2.3,<2.3.1", "zendframework/zend-session": ">=2,<2.2.9|>=2.3,<2.3.4", "zendframework/zend-validator": ">=2.3,<2.3.6", "zendframework/zend-view": ">=2,<2.2.7|>=2.3,<2.3.1", "zendframework/zend-xmlrpc": ">=2.1,<2.1.6|>=2.2,<2.2.6", "zendframework/zendframework": "<=3", "zendframework/zendframework1": "<1.12.20", "zendframework/zendopenid": "<2.0.2", "zendframework/zendrest": "<2.0.2", "zendframework/zendservice-amazon": "<2.0.3", "zendframework/zendservice-api": "<1", "zendframework/zendservice-audioscrobbler": "<2.0.2", "zendframework/zendservice-nirvanix": "<2.0.2", "zendframework/zendservice-slideshare": "<2.0.2", "zendframework/zendservice-technorati": "<2.0.2", "zendframework/zendservice-windowsazure": "<2.0.2", "zendframework/zendxml": ">=1,<1.0.1", "zenstruck/collection": "<0.2.1", "zetacomponents/mail": "<1.8.2", "zf-commons/zfc-user": "<1.2.2", "zfcampus/zf-apigility-doctrine": ">=1,<1.0.3", "zfr/zfr-oauth2-server-module": "<0.1.2", "zoujingli/thinkadmin": "<=6.1.53"}, "default-branch": true, "type": "metapackage", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "maintainer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "maintainer"}], "description": "Prevents installation of composer packages with known security vulnerabilities: no API, simply require it", "keywords": ["dev"], "support": {"issues": "https://github.com/Roave/SecurityAdvisories/issues", "source": "https://github.com/Roave/SecurityAdvisories/tree/latest"}, "funding": [{"url": "https://github.com/Ocramius", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/roave/security-advisories", "type": "tidelift"}], "time": "2025-01-23T18:06:21+00:00"}, {"name": "slevomat/coding-standard", "version": "7.2.1", "source": {"type": "git", "url": "https://github.com/slevomat/coding-standard.git", "reference": "aff06ae7a84e4534bf6f821dc982a93a5d477c90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/slevomat/coding-standard/zipball/aff06ae7a84e4534bf6f821dc982a93a5d477c90", "reference": "aff06ae7a84e4534bf6f821dc982a93a5d477c90", "shasum": ""}, "require": {"dealerdirect/phpcodesniffer-composer-installer": "^0.6.2 || ^0.7", "php": "^7.2 || ^8.0", "phpstan/phpdoc-parser": "^1.5.1", "squizlabs/php_codesniffer": "^3.6.2"}, "require-dev": {"phing/phing": "2.17.3", "php-parallel-lint/php-parallel-lint": "1.3.2", "phpstan/phpstan": "1.4.10|1.7.1", "phpstan/phpstan-deprecation-rules": "1.0.0", "phpstan/phpstan-phpunit": "1.0.0|1.1.1", "phpstan/phpstan-strict-rules": "1.2.3", "phpunit/phpunit": "7.5.20|8.5.21|9.5.20"}, "type": "phpcodesniffer-standard", "extra": {"branch-alias": {"dev-master": "7.x-dev"}}, "autoload": {"psr-4": {"SlevomatCodingStandard\\": "SlevomatCodingStandard"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Slevomat Coding Standard for PHP_CodeSniffer complements Consistence Coding Standard by providing sniffs with additional checks.", "support": {"issues": "https://github.com/slevomat/coding-standard/issues", "source": "https://github.com/slevomat/coding-standard/tree/7.2.1"}, "funding": [{"url": "https://github.com/kukulich", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/slevomat/coding-standard", "type": "tidelift"}], "time": "2022-05-25T10:58:12+00:00"}, {"name": "squizlabs/php_codesniffer", "version": "3.11.3", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "reference": "ba05f990e79cbe69b9f35c8c1ac8dca7eecc3a10"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/ba05f990e79cbe69b9f35c8c1ac8dca7eecc3a10", "reference": "ba05f990e79cbe69b9f35c8c1ac8dca7eecc3a10", "shasum": ""}, "require": {"ext-simplexml": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.3.4"}, "bin": ["bin/phpcbf", "bin/phpcs"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "role": "Former lead"}, {"name": "<PERSON>", "role": "Current lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/PHP_CodeSniffer/graphs/contributors"}], "description": "PHP_CodeSniffer tokenizes PHP, JavaScript and CSS files and detects violations of a defined set of coding standards.", "homepage": "https://github.com/PHPCSStandards/PHP_CodeSniffer", "keywords": ["phpcs", "standards", "static analysis"], "support": {"issues": "https://github.com/PHPCSStandards/PHP_CodeSniffer/issues", "security": "https://github.com/PHPCSStandards/PHP_CodeSniffer/security/policy", "source": "https://github.com/PHPCSStandards/PHP_CodeSniffer", "wiki": "https://github.com/PHPCSStandards/PHP_CodeSniffer/wiki"}, "funding": [{"url": "https://github.com/PHPCSStandards", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}, {"url": "https://thanks.dev/phpcsstandards", "type": "thanks_dev"}], "time": "2025-01-23T17:04:15+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {"deployer/deployer": 5, "roave/security-advisories": 20}, "prefer-stable": false, "prefer-lowest": false, "platform": {"php": "^8.4", "ext-fileinfo": "*", "ext-gd": "*", "ext-json": "*", "ext-simplexml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-amqp": "*"}, "platform-dev": {}, "platform-overrides": {"php": "8.4.1"}, "plugin-api-version": "2.6.0"}