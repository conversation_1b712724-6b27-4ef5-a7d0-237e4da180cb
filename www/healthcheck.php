<?php

use App\Bootstrap;
use App\Model\Orm\Orm;
use Elastica\Client;
use Nextras\Dbal\Connection;

require __DIR__ . '/../vendor/autoload.php';

$container = Bootstrap::boot()->createContainer();

// test database connection
$database = (function () use ($container) {
	$connection = $container->getByType(Connection::class);
	assert($connection instanceof Connection);

	try {
		$connection->query('SHOW STATUS');
		return true;
	} catch (Throwable) {
		return false;
	}
})();

// test elasticsearch connection
$elastic = (function () use ($container) {
	$elastica = $container->getByType(Client::class);
	assert($elastica instanceof Client);

	$esIndexRepository = $container->getByType(Orm::class)->esIndex;
	foreach ($esIndexRepository->findBy(['active' => 1]) as $esIndex) {
		try {
			$response = $elastica->request(sprintf('_cluster/health/%s', $esIndex->esName));
			if ($response->getData()['status'] === 'red') { // TODO !== 'green' when able
				return false;
			}

		} catch (Throwable) {
			return false;
		}
	}

	return true;
})();

$ok = $database && $elastic;
http_response_code($ok ? 200 : 500);
header('Content-Type: application/json');
echo json_encode([
	'database' => $database,
	'elastic' => $elastic,
], JSON_THROW_ON_ERROR);
