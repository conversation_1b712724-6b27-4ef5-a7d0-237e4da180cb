<?php declare(strict_types = 1);

namespace App\Infrastructure\Latte;

use App\Model\TranslateData;
use App\Model\TranslatorDB;
use Nette\SmartObject;
use Nette\Utils\Json;
use Nextras\Orm\Entity\IEntity;

/**
 * Useful template helpers
 */
final class Functions {

	use SmartObject;

	public function __construct(
		private readonly TranslatorDB $translator,
	)
	{
	}

	public function translate(string|\stdClass|TranslateData $data, ?array $params = null, bool $debug = false): string
	{
		return $this->translator->translateFunction($data, $params, $debug);
	}

	public static function cacheKey(mixed ... $parameters): string
	{
		$toJson = [];
		$prefix = '';

		if (is_string($parameters[0])) {
			$prefix = $parameters[0];
		}

		foreach ($parameters as $arg) {
			if ($arg instanceof IEntity) {
				$toJson[] = ['entity' => $arg::class, 'id' => $arg->getRawValue('id')];
			} else {
				$toJson[] = $arg;
			}
		}

		return $prefix.'_'.md5(Json::encode($toJson));
	}
}
