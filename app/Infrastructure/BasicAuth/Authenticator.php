<?php

declare(strict_types=1);

namespace App\Infrastructure\BasicAuth;

use Nette\Http\IRequest;

final class Authenticator
{
	/**
	 * @param array<string, string> $credentials
	 */
	public function __construct(
		private readonly array $credentials,
	) {}

	/**
	 * @param callable(): never $reject
	 */
	public function authenticate(
		IRequest $request,
		callable $reject,
	): void
	{
		if ($this->credentials === []) {
			return;
		}

		$header = $request->getHeader('Authorization');
		if ($header === null) {
			$reject();
		}

		if ( ! \preg_match('/Basic (?P<credentials>.+)/', $header, $match)) {
			$reject();
		}

		$decodedCredentials = \base64_decode($match['credentials'], true);
		if ($decodedCredentials === false) {
			$reject();
		}

		$credentialsParts = \explode(':', $decodedCredentials, 2);
		if (\count($credentialsParts) !== 2) {
			$reject();
		}

		[$username, $password] = $credentialsParts;
		if ( ! \array_key_exists($username, $this->credentials)) {
			$reject();
		}

		if ( ! \password_verify($password, $this->credentials[$username])) {
			$reject();
		}
	}
}
