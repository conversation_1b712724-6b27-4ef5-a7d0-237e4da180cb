<?php

declare(strict_types=1);

namespace App\Infrastructure\Debugger;

use Nette\StaticClass;
use App\Model\CustomField\LazyValue;
use <PERSON>\Dumper\Describer;
use <PERSON>\Dumper\Value;

final class LazyValueExposer
{

	use StaticClass;

	public static function exposeLazyValue(
		LazyValue $lazyValue,
		Value $value,
		Describer $describer,
	): void
	{
		$entity = $lazyValue->getEntity();
		$describer->addPropertyTo($value, 'entity', $entity);
	}

}
