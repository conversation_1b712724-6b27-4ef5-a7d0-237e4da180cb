<?php

declare(strict_types=1);

namespace App\Components\MessageForForm;

use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Localization\Translator;

/**
 * @property-read DefaultTemplate $template
 */
final class MessageForForm extends UI\Control
{

	private array $msgs = [];

	public function __construct(
		private readonly Translator $translator,
	) {}


	public function renderBase(iterable $flashes, ?UI\Form $form = NULL, bool $onlyFirst = TRUE, mixed $data = NULL, ?\stdClass $stdMessage = null): void
	{
		$this->template->setTranslator($this->translator);
		if ($form && $form->hasErrors()) {
			// tyto hlasky jiz byvaji prelozene
			foreach ($form->getErrors() as $error) {
				$this->addMsg($error, 'error');
			}
		}

		foreach ($flashes as $flash) {
			//tyto prelozene nejsou -> je nutne je prohnat translatorem

			// zjisteni zda hlaska neobsahuje odkaz k vlozeni
			$link = NULL;
			if (strpos($flash->message, "::") !== FALSE) {
				$tmp = explode("::", $flash->message);
				$flash->message = $tmp[0];
				$link = $tmp[1];
			}

			$strTranslated = (string) $this->translator->translate($flash->message);

			// nahrazeni pripadneho odkazu
			if ($link && str_contains($strTranslated, "%link%")) {
				$strTranslated = str_replace("%link%", $link, $strTranslated);
			}

			$this->addMsg($strTranslated, $flash->type);
		}


		if ($stdMessage) {
			$this->addMsg($stdMessage->text, $stdMessage->type);
		}

		$this->template->msgs = $this->msgs;
		$this->template->data = $data;
		$this->template->onlyFirst = $onlyFirst;
	}


	public function renderAdmin(iterable $flashes, ?UI\Form $form = NULL, bool $onlyFirst = TRUE, mixed $data = NULL, ?\stdClass $stdMessage = null): void
	{
		$this->renderBase($flashes, $form, $onlyFirst, $data, $stdMessage);
		$this->template->render(__DIR__ . "/messageForForm.latte");
	}

	public function render(iterable $flashes, ?UI\Form $form = NULL, bool $onlyFirst = TRUE, mixed $data = NULL, ?\stdClass $stdMessage = null): void
	{
		$this->renderBase($flashes, $form, $onlyFirst, $data, $stdMessage);
		$this->template->render(__DIR__ . "/messageForForm.latte");
	}

	private function addMsg(string $msg, string $type): void
	{
		$tmp = new \stdClass();
		$tmp->text = $msg;
		$tmp->type = $type;
		$this->msgs[] = $tmp;
	}
}
