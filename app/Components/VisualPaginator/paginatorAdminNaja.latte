{if isset($param['showPages']) && $paginator->itemCount}
	{($paginator->page * $paginator->itemsPerPage)-$paginator->itemsPerPage+1}
	-
	{if ($paginator->last)}
		{$paginator->itemCount}
	{else}
		{$paginator->page * $paginator->itemsPerPage}
	{/if}
	{_"from"}
	{$paginator->itemCount}

{else}

	{if $paginator->pageCount > 1}
		<p class="paging{if isset($param['class'])} {$param['class']}{/if}">
			<span class="paging__label">{_paging}:</span>
			{if !$paginator->isFirst()}
				<a {if $special}
					{*href="{$presenter->link("this", array_merge($get, array('page' => ($paginator->page - 1))))}"*}
					href="{$object->alias}{("?".http_build_query(array_merge($get, array('page' => $paginator->page - 1))))}"
					{if isset($param['ajax'])} data-naja="" {if $disableHistory} data-naja-history="off"{/if} {/if}
				{else}
					href="{link this, 'page' => $paginator->page - 1}"{/if}
						class="paging__prev link-icon link-icon--before" {if isset($param['ajax'])} data-naja="" {/if}>
					<span class="ico ico--chevron-left"></span>
					{_pagingPrev}
				</a>
			{/if}

			<span class="paging__pages">
				{foreach $steps as $step}
					{if $step == $paginator->page}
						<strong class="is-active">{$step}</strong>
					{else}
						<a {if $special}
						{*href="{$presenter->link("this", array_merge($get, array('page' => $step)))}"*}
						{*href="{link this, (expand) array_merge(array('page' => $step), $get)}"*}
						href="{$object->alias}{("?".http_build_query(array_merge($get, array('page' => $step))))}"
						{if isset($param['ajax'])} data-naja="" {if $disableHistory} data-naja-history="off"{/if} {/if}
					{else}
						href="{link this, 'page' => $step}"{/if}
							{if $iterator->first} class="first"
							{elseif $iterator->last} class="last"{else} class=""{/if} {if isset($param['ajax'])} data-naja="" {if $disableHistory} data-naja-history="off"{/if} {/if}>{$step}</a>
					{/if}
					{if $iterator->nextValue > $step + 1}<span class="paging__hellip">&hellip;</span>{/if}
				{/foreach}
			</span>

			{if !$paginator->isLast()}
				<a{if $special}
						{*href="{$presenter->link("this", array_merge($get, array('page' => ($paginator->page + 1))))}"*}
					href="{$object->alias}{("?".http_build_query(array_merge($get, array('page' => $paginator->page + 1))))}"
						{else}
					href="{link this, 'page' => $paginator->page + 1}"
						{/if}
						class="paging__next link-icon link-icon--after" {if isset($param['ajax'])} data-naja="" {if $disableHistory} data-naja-history="off"{/if} {/if}>
					{_pagingNext}
					<span class="ico ico--chevron-right"></span>
				</a>
			{/if}
		</p>
	{/if}
{/if}
