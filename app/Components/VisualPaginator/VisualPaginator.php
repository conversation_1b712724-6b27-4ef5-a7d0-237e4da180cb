<?php declare(strict_types=1);

namespace App\Components\VisualPaginator;

use Nette\Application\Attributes\Persistent;
use Nette\Application\UI\Control;
use Nette\Application\UI\Template;
use Nette\Localization\Translator;
use Nette\Utils\Paginator;

/**
 * @property-read Template $template
 */
final class VisualPaginator extends Control
{

	private ?Paginator $paginator = null;

	#[Persistent]
	public int $page = 1;

	public mixed $special = null;

	public mixed $object = null;

	private Translator $translator;

	private bool $noFollowEnabled = false;

	private ?int $startPage = null;
	private string $pageParameterName = 'page';


	public function __construct(
		private readonly bool $disableHistory = false,
	)
	{}

	public function getPaginator(): Paginator
	{
		return $this->paginator ??= new FakePaginator();
	}


	public function render(mixed $param = null): void
	{
		$this->template->noFollowEnabled = $this->noFollowEnabled;
		$this->template->pageParameterName = $this->pageParameterName;

		$this->baseRender($param, dirname(__FILE__) . '/paginator.latte');
	}


	public function renderAdmin(mixed $param = null): void
	{
		$this->baseRender($param, dirname(__FILE__) . '/paginatorAdmin.latte');
	}

	public function renderAdminNaja(mixed $param = null): void
	{
		$this->template->disableHistory = $this->disableHistory;
		$this->baseRender($param, dirname(__FILE__) . '/paginatorAdminNaja.latte');
	}


	public function baseRender(mixed $param, string $templatePath): void
	{
		$paginator = $this->getPaginator();
		$page = $paginator->page;
		if ($paginator->pageCount < 2) {
			$steps = [$page];

		} else {
			$arr = range(max($paginator->firstPage, $page - 2), min($paginator->lastPage, $page + 2));
			$count = 1;
			$quotient = ($paginator->pageCount - 1) / $count;
			for ($i = 0; $i <= $count; $i++) {
				$arr[] = round($quotient * $i) + $paginator->firstPage;
			}

			sort($arr);
			$steps = array_values(array_unique($arr));
		}

		if (isset($param['filter'])) {
			$this->template->filter = $param['filter'];
		}

		$get = $_GET;

		$this->template->param = $param;
		$this->template->get = $get;
		$this->template->special = $this->special;
		$this->template->object = $this->object;

		$this->template->steps = $steps;
		$this->template->paginator = $paginator;
		$this->template->setFile($templatePath);

		$this->template->startPage = $this->startPage;

		if ( ! isset($this->translator)) {
			$this->template->setTranslator($this->parent->translator);
		} else {
			$this->template->setTranslator($this->translator);
		}

		$this->template->render();
	}


	public function enableNofollow(bool $noFollowEnabled = true): void
	{
		$this->noFollowEnabled = $noFollowEnabled;
	}

	public function setTranslator(Translator $translator): void
	{
		$this->translator = $translator;
	}


	public function setStartPage(?int $page): void
	{
		$this->startPage = $page;
	}


	public function loadState(array $params): void
	{
		parent::loadState($params);
		$this->getPaginator()->page = $this->page;
	}

    public function pageParameterName(string $pageParameterName): void
    {
		$this->pageParameterName = $pageParameterName;
    }

}
