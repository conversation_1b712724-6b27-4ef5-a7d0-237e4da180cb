{if isset($param['showPages']) && $paginator->itemCount}
	{($paginator->page * $paginator->itemsPerPage)-$paginator->itemsPerPage+1}
	-
	{if ($paginator->last)}
		{$paginator->itemCount}
	{else}
		{$paginator->page * $paginator->itemsPerPage}
	{/if}
	{_"from"}
	{$paginator->itemCount}

{else}

	{if !isset($get['paramValue'])}
		{if isset($get['pager-paramValue'])}
			{php $get['paramValue'] = $get['pager-paramValue']}
		{else}
			{php $get['paramValue'] = ""}
		{/if}
	{/if}
	{if $paginator->pageCount > 1}
		<p class="paging{if isset($param['class'])} {$param['class']}{/if}">
			<span class="paging__label">{_paging}:</span>
			{if !$paginator->isFirst()}
				<a {if $special}
					{*href="{$presenter->link("this", array_merge($get, array('page' => ($paginator->page - 1))))}"*}
					href="{$object->alias}{("?".http_build_query(array_merge($get, array('page' => $paginator->page - 1))))}"
					{if isset($param['ajax'])} class="ajax"{/if}
				{else}
					href="{link this, 'page' => $paginator->page - 1, 'paramValue'=>$get['paramValue']}"{/if}
						class="paging__prev link-icon link-icon--before  {if isset($param['ajax'])} ajax{/if}">
					<span class="ico ico--chevron-left"></span>
					{_pagingPrev}
				</a>
			{/if}

			<span class="paging__pages">
				{foreach $steps as $step}
					{if $step == $paginator->page}
						<strong class="is-active">{$step}</strong>
					{else}
						<a {if $special}
						{*href="{$presenter->link("this", array_merge($get, array('page' => $step)))}"*}
						{*href="{link this, (expand) array_merge(array('page' => $step), $get)}"*}
						href="{$object->alias}{("?".http_build_query(array_merge($get, array('page' => $step))))}"
						{if isset($param['ajax'])} class="ajax"{/if}
					{else}
						href="{link this, 'page' => $step, 'paramValue'=>$get['paramValue']}"{/if}
							{if $iterator->first} class="first{if isset($param['ajax'])} ajax{/if}"
							{elseif $iterator->last} class="last {if isset($param['ajax'])} ajax{/if}"
							{else} class="{if isset($param['ajax'])} ajax{/if}"
							{/if}>{$step}</a>
					{/if}
					{if $iterator->nextValue > $step + 1}<span class="paging__hellip">&hellip;</span>{/if}
				{/foreach}
			</span>

			{if !$paginator->isLast()}
				<a{if $special}
						{*href="{$presenter->link("this", array_merge($get, array('page' => ($paginator->page + 1))))}"*}
					href="{$object->alias}{("?".http_build_query(array_merge($get, array('page' => $paginator->page + 1))))}"
						{else}
					href="{link this, 'page' => $paginator->page + 1, 'paramValue'=>$get['paramValue']}"
						{/if}
						class="paging__next link-icon link-icon--after{if isset($param['ajax'])} ajax{/if}">
					{_pagingNext}
					<span class="ico ico--chevron-right"></span>
				</a>
			{/if}
		</p>
	{/if}
{/if}
