<?php declare(strict_types = 1);

namespace App\Components\VisualPaginator;

use Nette\Utils\Paginator;

/**
 * Extends standard Nette paginator
 * It allow to have different items count on first page than on the rest of pages
 * In this case you must set "itemsOnFirstPage" property (if not, original functionality is used)
 */
final class FakePaginator extends Paginator
{

	/** @var positive-int|0|null */
	public ?int $itemsOnFirstPage = null;

	/**
	 * Sets the number of items to display on a first page.
	 * @param positive-int|0|null $itemsOnFirstPage
	 */
	public function setItemsOnFirstPage(?int $itemsOnFirstPage = null): FakePaginator
	{
		$this->itemsOnFirstPage = $itemsOnFirstPage;

		return $this;
	}


	/**
	 * Returns the total number of pages.
	 *
	 * @return int|NULL
	 */
	public function getPageCount(): ?int
	{
		if (!$this->itemsOnFirstPage) {
			return parent::getPageCount();
		}

		return $this->itemCount === null ? null : (int) \ceil(1 + (($this->itemCount - $this->itemsOnFirstPage) / parent::getItemsPerPage()));
	}


	public function getOffset(): int
	{
		if (!$this->itemsOnFirstPage) {
			return parent::getOffset();
		}

		if ($this->getPageIndex() > 0) {

			return $this->itemsOnFirstPage + ($this->getPageIndex() - 1) * parent::getItemsPerPage();
		}

		return 0;
	}


	public function getItemsPerPage(): int
	{
		if (!$this->itemsOnFirstPage) {
			return parent::getItemsPerPage();
		}

		if ($this->getPageIndex() > 0) {
			return parent::getItemsPerPage();
		} else {
			return $this->itemsOnFirstPage;
		}
	}


	public function getItemsPerPageBasic(): int
	{
		return parent::getItemsPerPage();
	}


	// kontrola prekroceni limitu strankovani
	public function isBadLink(array $parameters): bool
	{
		return isset($parameters['page']) && $parameters['page'] > $this->getLastPage();
	}

}
