{default $class = $param['class'] ?? false}
{default $showMoreBtn = $param['showMoreBtn'] ?? true}
{default $showPages = $param['showPages'] ?? false}
{default $pluralLang = 'btn_more_products'}

<div n:if="$paginator->pageCount > 1" n:class="paging, $class">
	<div class="paging__row">
		{* Str<PERSON>kovač *}
		<div n:if="$showPages" class="paging__pages">
			<ul class="paging__list">
				{if !$paginator->isFirst()}
					{if isset($filter)}
						{capture $link}{plink this, 'filter' => $filter, $pageParameterName => $paginator->page - 1}{/capture}
						{php $link = urldecode(htmlspecialchars_decode($link))}
					{else}
						{capture $link}{link this, 'page' => $paginator->page - 1}{/capture}
					{/if}
					<li class="paging__item paging__item--prev">
						<a href="{$link}" class="paging__link item-icon item-icon--arrow"{if $noFollowEnabled} rel="nofollow"{/if} rel="prev" data-naja data-naja-loader="body"{if isset($param['najaScroll'])} data-naja-scroll="{$param['najaScroll']}"{/if}>
							{('angle-left-bold')|icon, 'item-icon__icon'}
							<span class="item-icon__text">
								{_paging_prev}
							</span>
						</a>
					</li>
				{/if}

				{foreach $steps as $key => $step}
					{if isset($filter)}
						{capture $link}{plink this, 'filter' => $filter, $pageParameterName => $step}{/capture}
						{php $link = urldecode(htmlspecialchars_decode($link))}
					{else}
						{capture $link}{link this, $pageParameterName => $step}{/capture}
						{php $link = htmlspecialchars_decode($link)}
					{/if}
					{if $step == $paginator->page}
						<li class="paging__item">
							<strong class="paging__link is-active">{$paginator->page}</strong>
						</li>
					{else}
						<li class="paging__item">
							<a href="{$link}" class="paging__link"{if $noFollowEnabled} rel="nofollow"{/if} data-naja data-naja-loader="body"{if isset($param['najaScroll'])} data-naja-scroll="{$param['najaScroll']}"{/if}>
								{$step}
							</a>
						</li>
					{/if}
					{if $iterator->nextValue > $step + 1}
						<li class="paging__item">
							&hellip;
						</li>
					{/if}
				{/foreach}

				{if !$paginator->isLast()}
					{if isset($filter)}
						{capture $link}{plink this, 'filter' => $filter, $pageParameterName => $paginator->page + 1}{/capture}
						{php $link = urldecode(htmlspecialchars_decode($link))}
					{else}
						{capture $link}{link this, $pageParameterName => $paginator->page + 1}{/capture}
						{php $link = htmlspecialchars_decode($link)}
					{/if}
					<li class="paging__item paging__item--next">
						<a href="{$link}" class="paging__link item-icon item-icon--arrow"{if $noFollowEnabled} rel="nofollow"{/if} rel="next" data-naja data-naja-loader="body"{if isset($param['najaScroll'])} data-naja-scroll="{$param['najaScroll']}"{/if}>
							<span class="item-icon__text">
								{_paging_next}
							</span>
							{('angle-right-bold')|icon, 'item-icon__icon'}
						</a>
					</li>
				{/if}
			</ul>
		</div>

		{* Více *}
		<p n:if="$showMoreBtn && !$paginator->isLast()" class="paging__btn u-ta-c u-mb-0">
			{if isset($startPage)}
			{else}
				{var $startPage = $paginator->page}
			{/if}

			{if isset($filter)}
				{capture $link}{plink //this, 'filter' => $filter, $pageParameterName => $paginator->page + 1, 'more'=>$startPage}{/capture}
				{php $link = urldecode(htmlspecialchars_decode($link))}
			{else}
				{capture $link}{link //this, 'page' => $paginator->page + 1, 'more'=>$startPage}{/capture}
				{php $link = htmlspecialchars_decode($link)}
			{/if}

			<a href="{$link}" class="btn btn--lg btn--bd"{if $noFollowEnabled} rel="nofollow"{/if} data-naja data-naja-loader="body">
				<span class="btn__text">
					{if $paginator->getPage() + 1 == $paginator->getLastPage()}
						{var $futureItemNumber = $paginator->getItemCount() - $paginator->getPage() * $paginator->getItemsPerPage()}
					{else}
						{var $futureItemNumber = $paginator->getItemsPerPage()}
					{/if}

					{capture $pluralLang}{$futureItemNumber|plural: $pluralLang . "_1", $pluralLang . "_2", $pluralLang . "_3" }{/capture}
					{_$pluralLang|replace:'%s',(string)$futureItemNumber}
				</span>
			</a>
		</p>

		{* Nahoru *}
		<p class="paging__up u-mb-0">
			<a href="#productsList" class="btn btn--icon btn--bd">
				<span class="btn__text">
					{('arrow-top-thin')|icon}
					<span class="u-vhide">{_"top"}</span>
				</span>
			</a>
		</p>
	</div>

	{* Zbývající počet produktů *}
	<p n:if="!$paginator->isLast()" class="paging__count u-ta-c">
		{_paging_remaining} {$paginator->itemCount - ($paginator->itemsPerPage * $paginator->page)}
	</p>
</div>

{* {_showing}
{if isset($startPage)}
	{if $paginator->itemsOnFirstPage}
		{if $startPage == 1}
			{var $from = 1}
			{var $to= ($paginator->page-1)*$paginator->itemsPerPage + $paginator->itemsOnFirstPage}
		{else}
			{var $from = ($startPage-2)*$paginator->itemsPerPage + $paginator->itemsOnFirstPage + 1}
			{var $to= ($paginator->page-1)*$paginator->itemsPerPage + $paginator->itemsOnFirstPage}
		{/if}
	{else}
		{var $from = ($startPage-1)*$paginator->itemsPerPage+1}
		{var $to = $paginator->page*$paginator->itemsPerPage}
	{/if}

	{if $paginator->isLast()}
		{var $to = $paginator->itemCount}
	{/if}
	{$from}-{$to}
{else}
	{if $paginator->isLast()}
		{($paginator->offset)+1}–{$paginator->itemCount}
	{elseif $paginator->isFirst() && $paginator->itemsOnFirstPage}
		1–{$paginator->itemsOnFirstPage}
	{elseif $paginator->isFirst()}
		1–{$paginator->getItemsPerPageBasic()}
	{else}
		{$paginator->offset+1}–{$paginator->offset+$paginator->itemsPerPage}
	{/if}
{/if}
{_of}
{$paginator->itemCount} *}
