<?php

declare(strict_types=1);

function bd(mixed ...$args): void
{
	$trace = debug_backtrace();

	$namespace = $line = '';
	if (isset($trace[1])) {
		$namespace = $trace[1]['class'] ?? '';
		if (isset($trace[0]['line'])) {
			$line = $trace[0]['line'];
		} else {
			$line = null;
		}
	}

	foreach ($args as $arg) {
		\Tracy\Debugger::barDump($arg, $namespace !== '' ? $namespace . ':' . $line : null);
	}
}
