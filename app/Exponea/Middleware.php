<?php

declare(strict_types=1);

namespace App\Exponea;

use App\Exponea\Exception\NoPermissionException;
use App\Exponea\Exception\UnexpectedResponseSchemaException;
use App\Exponea\Exception\UnsuccessfulResponseException;
use Closure;
use Psr\Http\Message\ResponseInterface;
use GuzzleHttp\Psr7\Utils;

class Middleware
{

	/**
	 * Verification of "no permission" phrase inside body
	 */
	public static function verifyPermissions(): Closure // @phpstan-ignore-line
	{
		return function (callable $handler) {
			return function ($request, array $options) use ($handler) {
				return $handler($request, $options)->then(function (ResponseInterface $response) use ($request) {
					$body = $response->getBody()->getContents();

					// Detection of "no permission response"
					if (mb_stripos($body, 'no permission') !== false) {
						throw new NoPermissionException(
							'You don\'t have permission to use this method',
							$request,
							$response
						);
					}
					return $response->withBody(Utils::streamFor($body));
				});
			};
		};
	}

	/**
	 * Validation of received JSON message
	 */
	public static function validateJson(): Closure // @phpstan-ignore-line
	{
		return function (callable $handler) {
			return function ($request, array $options) use ($handler) {
				return $handler($request, $options)->then(function (ResponseInterface $response) use ($request) {
					$body = $response->getBody()->getContents();

					// Validation of JSON
					json_decode($body, true);
					if (json_last_error() !== JSON_ERROR_NONE) {
						throw new UnexpectedResponseSchemaException(
							'Response does not seem to be valid JSON: ' . json_last_error_msg(),
							$request,
							$response
						);
					}
					return $response->withBody(Utils::streamFor($body));
				});
			};
		};
	}

	/**
	 * Validation of received JSON message
	 */
	public static function checkSuccessFlag(): Closure // @phpstan-ignore-line
	{
		return function (callable $handler) {
			return function ($request, array $options) use ($handler) {
				return $handler($request, $options)->then(function (ResponseInterface $response) use ($request) {
					$body = $response->getBody()->getContents();

					// Verification of success flag
					$json = json_decode($body, true);
					if (isset($json['success']) && $json['success'] === false) {
						$message = 'Received unexpected success: false response';
						if (isset($json['errors'])) {
							$message .= ', errors: ' . json_encode($json['errors']);
						} elseif (isset($json['error'])) {
							$message .= ', error: ' . json_encode($json['error']);
						}
						throw new UnsuccessfulResponseException(
							$message,
							$request,
							$response
						);
					}
					return $response->withBody(Utils::streamFor($body));
				});
			};
		};
	}

}
