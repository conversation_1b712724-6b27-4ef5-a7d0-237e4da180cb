<?php

declare(strict_types=1);

namespace App\Exponea\Tracking\Event;

use App\Event\OrderSubmit;
use App\Exponea\Customer\CustomerIdInterface;
use App\Exponea\EventInterface;
use App\Model\Orm\Order\Product\ProductItem;

readonly class Purchase implements EventInterface
{

	public function __construct(
		private CustomerIdInterface $customerId,
		private OrderSubmit $cartEvent,
	)
	{
	}

	public function getCustomerIds(): CustomerIdInterface
	{
		return $this->customerId;
	}

	public function getEventType(): string
	{
		return 'purchase';
	}

	public function getTimestamp(): float
	{
		return microtime(true);
	}

	public function getProperties(): array
	{
		$discount = $this->cartEvent->order->getTotalDiscountVat()->getAmount()->toFloat();
		$totalPriceOriginal = round($this->cartEvent->order->getTotalPriceVat(true)->getAmount()->toFloat() + $this->cartEvent->order->getTotalDiscountVat()->getAmount()->toFloat(), 2);
		$discountPercentage = round((100 * $discount) / $totalPriceOriginal);

		$data = [
			'purchase_id' => $this->cartEvent->order->orderNumber,
			'purchase_status' => 'new',
			'payment_method' => $this->cartEvent->order->getPayment()->paymentMethod->getPaymentMethod()->getUniqueIdentifier(),
			'purchase_source_type' => 'online',
			'local_currency' => $this->cartEvent->order->getCurrency()->getCurrencyCode(),
			'product_list' => [],
			'total_quantity' => $this->cartEvent->order->getTotalCount(),
			'total_price' => $this->cartEvent->order->getTotalPriceVat(true)->getAmount()->toFloat(),
			'total_discount' => $discount,
			'discount_percentage' => $discountPercentage,
			'total_price_original' => $totalPriceOriginal,

			'language' => $this->cartEvent->order->mutation->langCode,
			'locale' => $this->cartEvent->order->mutation->isoCode,
			'timestamp' => microtime(true),
		];

		if (($voucher = $this->cartEvent->order->getAppliedVoucherItem()) !== null) {
			$data['coupon'] = $voucher->getCode();
			$data['coupon_value'] = $voucher->totalPriceVat->getAmount()->toFloat();
		}

		/** @var ProductItem $item */
		foreach ($this->cartEvent->order->getProducts() as $item) {
			$data['product_ids'][] = $item->variant->product->getPersistedId();
			$data['product_list'][] = [
				'product_id' => $item->variant->product->getPersistedId(),
				'quantity' => $item->amount,
			];
		}

		return $data;
	}

}
