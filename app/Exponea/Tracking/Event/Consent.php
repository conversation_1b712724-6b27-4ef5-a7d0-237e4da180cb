<?php

declare(strict_types=1);

namespace App\Exponea\Tracking\Event;

use App\Event\NewsletterSubscribe;
use App\Exceptions\LogicException;
use App\Exponea\Customer\CustomerIdInterface;
use App\Exponea\EventInterface;

readonly class Consent implements EventInterface
{

	public function __construct(
		private CustomerIdInterface $customerId,
		private NewsletterSubscribe $event
	)
	{
	}

	public function getCustomerIds(): CustomerIdInterface
	{
		return $this->customerId;
	}

	public function getEventType(): string
	{
		return 'consent';
	}

	public function getTimestamp(): float
	{
		return microtime(true);
	}

	public function getProperties(): array
	{
		return [
			'category' => $this->getConsentCategory(),
			'valid_until' => 'unlimited',
			'action' => $this->event->getAction(),
			'timestamp' => $this->getTimestamp(),
		];
	}

	private function getConsentCategory(): string
	{
		return match ($this->event::class) {
			NewsletterSubscribe::class => 'email', // @phpstan-ignore-line
			default => throw new LogicException('Invalid consent event type'),
		};
	}

}
