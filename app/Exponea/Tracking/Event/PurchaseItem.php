<?php

declare(strict_types=1);

namespace App\Exponea\Tracking\Event;

use App\Event\OrderSubmit;
use App\Exponea\Customer\CustomerIdInterface;
use App\Exponea\EventInterface;
use App\Model\Orm\Order\Product\ProductItem;
use App\Model\Security\User as UserSecurity;

readonly class PurchaseItem implements EventInterface
{

	public function __construct(
		private CustomerIdInterface $customerId,
		private OrderSubmit $cartEvent,
		private ProductItem $productItem,
		private UserSecurity $user,
	)
	{
	}

	public function getCustomerIds(): CustomerIdInterface
	{
		return $this->customerId;
	}

	public function getEventType(): string
	{
		return 'purchase_item';
	}

	public function getTimestamp(): float
	{
		return microtime(true);
	}

	public function getProperties(): array
	{
		$productLocalization = $this->productItem->variant->product->getLocalization($this->cartEvent->order->mutation);

		$data = $productLocalization->getBloomreachData(
			$this->productItem->variant,
			$this->cartEvent->order->mutation,
			$this->cartEvent->order->priceLevel,
			$this->cartEvent->order->country,
			$this->user
		) + [
				'purchase_id' => $this->cartEvent->order->orderNumber,
				'purchase_status' => 'new',
				'purchase_source_type' => 'online',

				'valid_until' => 'unlimited',
				'local_currency' => $this->cartEvent->order->getCurrency()->getCurrencyCode(),
				'quantity' => $this->productItem->amount,
				'price' => $this->productItem->unitPriceVat->getAmount()->toFloat(),
				'total_price' => $this->productItem->totalPriceVat->getAmount()->toFloat(),

				'discount' => 0,
				'total_discount' => 0,
				'total_price_original' => $this->productItem->totalPriceVat->getAmount()->toFloat(),

				'language' => $this->cartEvent->order->mutation->langCode,
				'locale' => $this->cartEvent->order->mutation->isoCode,
				'timestamp' => microtime(true),
			];

		if (($voucher = $this->cartEvent->order->getAppliedVoucherItem()) !== null) {
			$data['coupon'] = $voucher->getCode();
			$data['coupon_value'] = $voucher->totalPriceVat->getAmount()->toFloat();
		}

		return $data;
	}

}
