<?php

declare(strict_types=1);

namespace App\Exponea\Tracking\Event;

use App\Event\AddToCart;
use App\Event\RemoveFromCart;
use App\Exponea\Customer\CustomerIdInterface;
use App\Exponea\EventInterface;
use App\Model\Currency\CurrencyHelper;
use App\Model\Orm\Order\Product\ProductItem;

readonly class CartUpdate implements EventInterface
{

	public function __construct(
		private CustomerIdInterface $customerId,
		private AddToCart|RemoveFromCart $cartEvent,
	)
	{
	}

	public function getCustomerIds(): CustomerIdInterface
	{
		return $this->customerId;
	}

	public function getEventType(): string
	{
		return 'cart_update';
	}

	public function getTimestamp(): float
	{
		return microtime(true);
	}

	public function getProperties(): array
	{
		$productLocalization = $this->cartEvent->productLocalization;

		$data = $productLocalization->getBloomreachData(
			$this->cartEvent->variant,
			$this->cartEvent->mutation,
			$this->cartEvent->priceLevel,
			$this->cartEvent->state,
			$this->cartEvent->shoppingCart->getUserSecurity()
		) + [
				'cart_id' => $this->cartEvent->shoppingCart->getOrderId(),
				'action' => $this->cartEvent instanceof AddToCart ? 'add' : 'remove',

				'local_currency' => CurrencyHelper::getCurrencyCode(),
				'quantity' => $this->cartEvent->quantity,

				//'total_quantity' => $this->cartEvent->shoppingCart->getTotalCount(),
				'total_price' => $this->cartEvent->shoppingCart->getTotalPriceVat()->getAmount()->toFloat(),
				'total_discount' => $this->cartEvent->shoppingCart->getTotalDiscountVat()->getAmount()->toFloat(),
				'total_price_original' => round($this->cartEvent->shoppingCart->getTotalPriceVat()->getAmount()->toFloat() + $this->cartEvent->shoppingCart->getTotalDiscountVat()->getAmount()->toFloat(), 2),

				'language' => $this->cartEvent->mutation->langCode,
				'locale' => $this->cartEvent->mutation->isoCode,
				'timestamp' => microtime(true),
			];

		if (($voucher = $this->cartEvent->shoppingCart->getAppliedVoucherItem()) !== null) {
			$data['coupon'] = $voucher->getCode();
			$data['coupon_value'] = $voucher->totalPriceVat->getAmount()->toFloat();
		}
		/** @var ProductItem $item */
		foreach ($this->cartEvent->shoppingCart->getProducts() as $item) {
			$data['product_ids'][] = $item->variant->product->getPersistedId();
			$data['product_list'][] = [
				'product_id' => $item->variant->product->getPersistedId(),
				'quantity' => $item->amount,
			];
		}

		return $data;
	}

}
