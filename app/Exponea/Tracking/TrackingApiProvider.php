<?php
declare(strict_types=1);

namespace App\Exponea\Tracking;

use App\Exceptions\LogicException;
use App\Exponea\AbstractApiProvider;
use App\Exponea\Customer\CustomerIdInterface;
use App\Exponea\EventInterface;
use GuzzleHttp\Promise\PromiseInterface;
use GuzzleHttp\Psr7\Request;
use stdClass;

class TrackingApiProvider extends AbstractApiProvider
{

	private array $queue = [];

	/**
	 * Propagate event to Expponea
	 *
	 * Please note that sending event for customer id which doesn't exist in Exponea, will automatically create
	 * contact with sent identifier. It's transparent from your side (there will be no errors).
	 *
	 * Promise resolves to null
	 */
	public function addEvent(EventInterface $event): PromiseInterface
	{
		$customerIds = [];
		if ($event->getCustomerIDs()->getRegistered() !== null) {
			$customerIds[$event->getCustomerIDs()->getRegisteredKey()] = $event->getCustomerIDs()->getRegistered();
		}
		if ($event->getCustomerIDs()->getCookie() !== null) {
			$customerIds['cookie'] = $event->getCustomerIDs()->getCookie();
		}

		$body = [
			'customer_ids' => $customerIds,
			'event_type' => $event->getEventType(),
			'timestamp' => $event->getTimestamp(),
			'properties' => $event->getProperties(),
		];
		$request = new Request(
			'POST',
			'/track/v2/projects/{projectToken}/customers/events',
			[],
			json_encode($body) ?: '{}'
		);

		return $this->getClient()->call($request)->then(function ($e) {
			return $e->getStatusCode() === 200;
		});
	}

	public function queueEvent(EventInterface $event): TrackingApiProvider
	{
		$this->queue[] = $event;
		return $this;
	}

	public function processQueue(): PromiseInterface
	{
		$body = [];

		if ($this->queue === []) {
			throw new LogicException('Queue is empty');
		}

		/** @var EventInterface $event */
		foreach ($this->queue as $event) {
			$customerIds = [];
			if ($event->getCustomerIDs()->getRegistered() !== null) {
				$customerIds[$event->getCustomerIDs()->getRegisteredKey()] = $event->getCustomerIDs()->getRegistered();
			}
			if ($event->getCustomerIDs()->getCookie() !== null) {
				$customerIds['cookie'] = $event->getCustomerIDs()->getCookie();
			}

			$body['commands'][] = [
				'name' => 'customers/events',
				'data' => [
					'customer_ids' => $customerIds,
					'event_type' => $event->getEventType(),
					'timestamp' => $event->getTimestamp(),
					'properties' => $event->getProperties(),
				],
			];

		}

		$request = new Request(
			'POST',
			'/track/v2/projects/{projectToken}/batch',
			[],
			json_encode($body) ?: '{}'
		);

		return $this->getClient()->call($request)->then(function ($e) {
			return $e->getStatusCode() === 200;
		});
	}

	public function updateCustomerProperties(CustomerIdInterface $customerID, array $properties): PromiseInterface
	{
		$customerIds = [];
		if ($customerID->getRegistered() !== null) {
			$customerIds[$customerID->getRegisteredKey()] = $customerID->getRegistered();
		}
		if ($customerID->getCookie() !== null) {
			$customerIds['cookie'] = $customerID->getCookie();
		}
		$customerIds = $customerIds + ($customerID->getSoftIds() ?? []);

		// Lets override empty array with stdclass so json_encode will create {} instead of []
		// Exponea will reject requests with JSON arrays
		if ($properties === []) {
			$properties = new stdClass();
		}

		$body = [
			'customer_ids' => $customerIds,
			'properties' => $properties,
		];

		$request = new Request(
			'POST',
			'/track/v2/projects/{projectToken}/customers',
			[],
			json_encode($body) ?: '{}'
		);

		return $this->getClient()->call($request)->then(function ($e) {
			return $e->getStatusCode() === 200;
		});
	}

}
