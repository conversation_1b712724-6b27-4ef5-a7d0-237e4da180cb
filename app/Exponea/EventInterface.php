<?php
declare(strict_types = 1);

namespace App\Exponea;

use App\Exponea\Customer\CustomerIdInterface;
use JsonSerializable;

/**
 * Interface describing event which happened for contact
 */
interface EventInterface
{

	/**
	 * Person which event should be assigned to
	 */
	public function getCustomerIds(): CustomerIdInterface;
	/**
	 * Please check your API panel as event types propably might vary depending on your project requirements
	 */
	public function getEventType(): string;
	/**
	 * Time when event occured
	 *
	 * ATTENTION: Exponea uses FLOAT values for timestamps
	 */
	public function getTimestamp(): float;
	/**
	 * Get event properties
	 */
	public function getProperties(): array|JsonSerializable;

}
