<?php
declare(strict_types = 1);

namespace App\Exponea\Customer;

/**
 * Interface describing contact for which action should be made
 */
interface CustomerIdInterface
{

	/**
	 * Exponea API customer_ids.cookie field
	 */
	public function getCookie(): ?string;
	/**
	 * Exponea API customer_ids.registered field (should contain customer e-mail address which is base identifier)
	 */
	public function getRegistered(): ?string;
	/**
	 * Exponea customer Hard ID key name (eg. "email_id" otherwise "registered")
	 */
	public function getRegisteredKey(): string;
	/**
	 * Get Soft IDs which should be exported to Exponea (f.x. card number)
	 *
	 * @return array<string,string>|null
	 */
	public function getSoftIds(): array|null;

}
