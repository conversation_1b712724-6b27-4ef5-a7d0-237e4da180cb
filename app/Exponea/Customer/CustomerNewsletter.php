<?php
declare(strict_types=1);

namespace App\Exponea\Customer;

readonly class CustomerNewsletter implements CustomerIdInterface
{

	public function __construct(private string $email)
	{
	}

	public function getCookie(): ?string
	{
		return null;
	}

	public function getRegistered(): string
	{
		return $this->email;
	}

	public function getRegisteredKey(): string
	{
		return 'email_id';
	}

	public function getSoftIds(): array|null
	{
		return null;
	}

}
