<?php

declare(strict_types=1);

namespace App\Exponea\Customer;

use App\Model\Orm\User\User;
use App\Model\Orm\User\UserProvider;
use Nette\Http\Request;

final class Customer implements CustomerIdInterface
{

	private ?User $user = null;

	public function __construct(
		private readonly UserProvider $userProvider,
		private readonly Request $httpRequest
	)
	{
		$this->user = $this->userProvider->userEntity;
	}

	public function getCookie(): ?string
	{
		return $this->httpRequest->getCookie('__exponea_etc__');
	}

	public function getRegistered(): ?string
	{
		return $this->user?->email;
	}

	public function getRegisteredKey(): string
	{
		return 'email_id';
	}

	public function getSoftIds(): array|null
	{
		// TODO: Implement getSoftIds() method.
		return null;
	}

}
