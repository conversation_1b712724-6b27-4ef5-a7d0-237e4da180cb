<?php
declare(strict_types=1);

namespace App\Exponea;

use App\Exponea\Middleware as ExponeaMiddleware;
use App\Exponea\Tracking\TrackingApiProvider;
use Guzzle<PERSON>ttp\Client as HttpClient;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Middleware;
use GuzzleHttp\Promise\PromiseInterface;
use GuzzleHttp\Utils;
use Psr\Http\Message\RequestInterface;

class Client
{

	protected string $endpointUri = 'https://api.exponea.com';

	protected ?string $publicKey;

	private ?string $privateKey;

	protected ?string $projectToken;

	protected HttpClient $httpClient;

	protected TrackingApiProvider|null $tracking = null;

	public function __construct(
		?string $publicKey = null,
		?string $privateKey = null,
		?string $projectToken = null,
		?string $endpointUri = null,
	)
	{
		if ($endpointUri !== null) {
			$this->setEndpointUri($endpointUri);
		}
		if ($publicKey !== null) {
			$this->setPublicKey($publicKey);
		}
		if ($privateKey !== null) {
			$this->setPrivateKey($privateKey);
		}
		if ($projectToken !== null) {
			$this->setProjectToken($projectToken);
		}

		$this->httpClient = new HttpClient(
			[
				'base_uri' => $this->getEndpointUri(),
				'handler'  => $this->createHandler(),
				'headers'  => [
					'Accept'        => 'application/json',
					'Content-Type'  => 'application/json',
					'Authorization' => $this->getAuthHeader(),
				],
			]
		);
	}

	protected function createHandler(): HandlerStack
	{
		$handler = new HandlerStack(Utils::chooseHandler());

		// Request modification
		$handler->push(Middleware::mapRequest(function (RequestInterface $request) {
			return $request->withUri(
				$request->getUri()
					->withPath(str_replace(
						urlencode('{projectToken}'),
						urlencode($this->getProjectToken() ?? ''),
						$request->getUri()->getPath()
					))
			);
		}), 'inject_project_token');

		// Response validation
		$handler->push(ExponeaMiddleware::checkSuccessFlag(), 'success_flag');
		$handler->push(ExponeaMiddleware::validateJson(), 'validate_json');
		$handler->push(ExponeaMiddleware::verifyPermissions(), 'no_permission');

		// Basic response verifications
		$handler->push(Middleware::httpErrors(), 'http_errors');
		$handler->push(Middleware::redirect(), 'allow_redirects');
		$handler->push(Middleware::prepareBody(), 'prepare_body');

		return $handler;
	}

	public function tracking(): TrackingApiProvider
	{
		if ($this->tracking === null) {
			$this->tracking = new TrackingApiProvider($this);
		}
		return $this->tracking;
	}

	/**
	 * Call API and retrieve response with decoded and validated JSON response
	 */
	public function call(RequestInterface $request): PromiseInterface
	{
		return $this->httpClient->sendAsync($request);
	}

	public function setEndpointUri(string $endpointUri): Client
	{
		$this->endpointUri = $endpointUri;
		return $this;
	}

	public function setPublicKey(?string $publicKey): Client
	{
		$this->publicKey = $publicKey;
		return $this;
	}

	public function setPrivateKey(?string $privateKey): Client
	{
		$this->privateKey = $privateKey;
		return $this;
	}

	public function setProjectToken(?string $projectToken): Client
	{
		$this->projectToken = $projectToken;
		return $this;
	}

	protected function getAuthHeader(): string
	{
		return 'basic ' . base64_encode($this->getPublicKey() . ':' . $this->getPrivateKey());
	}

	public function getEndpointUri(): string
	{
		return $this->endpointUri;
	}

	public function getPublicKey(): ?string
	{
		return $this->publicKey;
	}

	public function getPrivateKey(): ?string
	{
		return $this->privateKey;
	}

	public function getProjectToken(): ?string
	{
		return $this->projectToken;
	}

}
