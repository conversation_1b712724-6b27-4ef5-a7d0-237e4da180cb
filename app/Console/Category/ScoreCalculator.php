<?php declare(strict_types = 1);

namespace App\Console\Category;

use App\Console\BaseCommand;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Orm;
use App\PostType\Page\Model\Orm\TreeModel;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name: 'category:score:calculate',
	description: 'Calculate categorie\'s score based on theirs sold products.',
)]
class ScoreCalculator extends BaseCommand
{

	public function __construct(protected readonly Orm $orm, private readonly TreeModel $treeModel, private readonly MutationHolder $mutationHolder)
	{
		parent::__construct();
	}

	protected function configure(): void
	{
		$this->addArgument('mutationCode', InputArgument::REQUIRED)
			->addArgument('batchSize', InputArgument::REQUIRED);
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->start($input);
		$mutationCode = $input->getArgument('mutationCode');
		$batchSize = (int) $input->getArgument('batchSize');

		$mutation = $this->orm->mutation->getByCode($mutationCode);
		$this->orm->setMutation($mutation);
		$this->mutationHolder->setMutation($mutation);

		$this->treeModel->calculateScore($mutationCode, $batchSize);

		$output->writeLn('DONE');

		return $this->end(self::SUCCESS);
	}

}
