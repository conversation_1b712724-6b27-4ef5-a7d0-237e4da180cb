<?php

declare(strict_types=1);

namespace App\Console;

use App\Model\Orm\Orm;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Page\Model\Orm\TreeModel;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name: 'copy:tree',
	description: 'Copy tree node by ID'
)]
final class CopyTreeCommand extends BaseCommand
{

	public function __construct(
		protected readonly Orm $orm,
		private readonly TreeModel $treeModel,
	)
	{
		parent::__construct();
	}

	protected function configure(): void
	{
		$this->addArgument('sourceId', InputArgument::REQUIRED, 'Id of tree to copy')
			->addArgument('destinationId', InputArgument::REQUIRED, 'Id of destination tree')
			->addOption('skipFirst', 's', InputOption::VALUE_NONE, 'Skip first level');
	}


	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->start($input);
		$this->orm->tree->setPublicOnly(false);

		$sourceId = $input->getArgument('sourceId');
		$destinationId = $input->getArgument('destinationId');
		$skipFirst = (bool) intval($input->getOption('skipFirst'));

		$treeToCopy = $this->orm->tree->getById($sourceId);
		$parentTreeWhereToInsert = $this->orm->tree->getById($destinationId);

		if ($treeToCopy && $parentTreeWhereToInsert) {
			$this->runCommand($treeToCopy, $parentTreeWhereToInsert, $skipFirst);
		}

		$output->writeLn('DONE');

		return $this->end(self::SUCCESS);
	}


	private function runCommand(Tree $treeToCopy, Tree $parentTree, bool $skipFirst): void
	{
		$this->treeModel->handleDuplicate($treeToCopy, $parentTree, $skipFirst);
	}

}
