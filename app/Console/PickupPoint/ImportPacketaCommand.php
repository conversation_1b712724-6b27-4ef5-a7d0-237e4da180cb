<?php declare(strict_types = 1);

namespace App\Console\PickupPoint;

use App\Console\BaseCommand;
use App\Exceptions\LogicException;
use App\Model\ConfigService;
use App\Model\Orm\DeliveryMethod\ZasilkovnaPickup;
use App\Model\Orm\Orm;
use GuzzleHttp\Client;
use Nette\Utils\Json;
use Nextras\Dbal\Connection;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use App\Model\Orm\State\State;

#[AsCommand(
	name: 'pickupPoint:import:packeta',
	description: 'Packeta (zasilkovna) branch import',
)]
// [AsCronTask(expression: '# 22 * * *', transports: 'cronCommands')]
final class ImportPacketaCommand extends BaseCommand
{

	private const DELIVERY_METHOD_IDENT = ZasilkovnaPickup::ID;

	private const WEEKDAYS = [
		'monday' => 'Pondělí',
		'tuesday' => 'Úterý',
		'wednesday' => 'Středa',
		'thursday' => 'Čtvrtek',
		'friday' => 'Pátek',
		'saturday' => 'Sobota',
		'sunday' => 'Neděle',
	];

	private ?string $apiKey = null;

	private const ENDPOINT = 'https://www.zasilkovna.cz/api/v5/__API_KEY__/branch.json?lang=cs_CZ';

	public function __construct(
		private readonly Connection $connection,
		protected readonly Orm $orm,
		protected readonly ConfigService $configService,
	)
	{
		parent::__construct();
		$this->apiKey = $this->configService->get('pickupPoints', 'packeta', 'api_key');
	}


	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->start($input);
		if (($deliveryMethod = $this->orm->deliveryMethod->getBy(['deliveryMethodUniqueIdentifier' => self::DELIVERY_METHOD_IDENT])) === null) {
			$output->writeln('Delivery method with unique identifier ' . self::DELIVERY_METHOD_IDENT . ' not exists. Skipping import.');
			return self::FAILURE;
		}

		$output->writeln('Delivery method: ' . $deliveryMethod->name);

		try {

			if ($this->apiKey === null) {
				throw new LogicException('Missing api key.');
			}

			$client = new Client();
			$endpoint = str_replace('__API_KEY__', $this->apiKey, self::ENDPOINT);
			$response = Json::decode($client->get($endpoint)->getBody()->getContents(), forceArrays: true);

			$stateIds = $this->orm->state->findBy(['code' => [State::CODE_CZ, State::CODE_SK]])->fetchPairs('code', 'id');

			$imported = [];
			$i = 0;
			foreach ($response['data'] as $item) {
				// unactive branch
				if ($item['status']['statusId'] !== '1') {
					continue;
				}

				// only CZ and SK branches
				if (!($item['country'] === 'cz' || $item['country'] === 'sk')) {
					continue;
				}

				$name = $item['name'];
				$extId = $item['id'];
				$address = $item['street'] . ', ' . $item['zip'] . ' ' . $item['city'];

				$data = [
					'deliveryMethodId' => $deliveryMethod->id,
					'extId' => $extId,
					'name' => $name,
					'address' => $address,
					'syncTime' => new DateTimeImmutable(),
					'lat' => $item['latitude'],
					'lng' => $item['longitude'],
					'openingHours' => Json::encode($this->getOpeningHours($item)),
					'image' => $item['photos'][0]['normal'] ?? null,
					'stateId' => $stateIds[mb_strtoupper($item['country'])] ?? State::DEFAULT_ID,
				];

				$imported[] = $extId;

				$this->connection->query('INSERT INTO [pickup_point] %values ON DUPLICATE KEY UPDATE %set', $data, $data);

				$i++;
			}

			if ($imported !== []) {
				$this->connection->query('DELETE FROM [pickup_point] WHERE deliveryMethodId = %?i AND extId NOT IN %s[]', $deliveryMethod->id, $imported);
			}

			$output->writeln('Total processed: ' . $i);

		} catch (\Throwable $e) {
			$output->writeln($e->getMessage());
		}

		return $this->end(self::SUCCESS);
	}

	private function getOpeningHours(array $item): array
	{
		$content = [];
		if (isset($item['openingHours']['regular'])) {
			foreach ($item['openingHours']['regular'] as $weekDay => $openingHoursString) {
				$openingHours = [];
				foreach (explode(',', $openingHoursString) as $o) {
					$e = explode('–', $o);
					if (strlen(trim($e[0])) > 0) {
						$openingHours[] = ['from' => trim($e[0]), 'to' => trim($e[1])];
					}
				}
				if ($openingHours !== []) {
					$content[self::WEEKDAYS[$weekDay]] = $openingHours;
				}
			}
		}
		return $content;
	}

}
