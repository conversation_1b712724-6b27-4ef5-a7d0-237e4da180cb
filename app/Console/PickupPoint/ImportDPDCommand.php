<?php declare(strict_types = 1);

namespace App\Console\PickupPoint;

use App\Console\BaseCommand;
use App\Model\Orm\DeliveryMethod\DPDPickup;
use App\Model\Orm\Orm;
use GuzzleHttp\Client;
use Nette\Utils\Json;
use Nextras\Dbal\Connection;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use App\Model\Orm\State\State;

#[AsCommand(
	name: 'pickupPoint:import:dpdpickup',
	description: 'DPD branch import',
)]
// [AsCronTask(expression: '# 22 * * *', transports: 'cronCommands')]
final class ImportDPDCommand extends BaseCommand
{

	private const string ENDPOINT = 'https://pickup.dpd.cz/api/getAll';

	private const string DELIVERY_METHOD_IDENT = DPDPickup::ID;
	private const array ENDPOINT_COUNTRY_CODES = ['203' => State::CODE_CZ, '703' => State::CODE_SK];

	public function __construct(
		private readonly Connection $connection,
		protected readonly Orm $orm,
	)
	{
		parent::__construct();
	}


	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->start($input);
		if (($deliveryMethod = $this->orm->deliveryMethod->getBy(['deliveryMethodUniqueIdentifier' => self::DELIVERY_METHOD_IDENT])) === null) {
			$output->writeln('Delivery method with unique identifier ' . self::DELIVERY_METHOD_IDENT . ' not exists. Skipping import.');
			return self::FAILURE;
		}

		$output->writeln('Delivery method: ' . $deliveryMethod->name);

		try {
			$client = new Client();

			$imported = [];

			foreach (self::ENDPOINT_COUNTRY_CODES as $dpdCountryCode => $countryCode) {
				$i = 0;
				$state = $this->orm->state->getBy(['code' => $countryCode]);
				$output->writeln('CountryCode: ' . $countryCode . ' / ' . $state->name);

				$endpoint = self::ENDPOINT . '?country=' . $dpdCountryCode;
				$response = $client->get($endpoint)->getBody()->getContents();
				$data = Json::decode($response, forceArrays: true);

				foreach ($data['data']['items'] as $item) {
					$name = $item['company'];
					$extId = $item['id'];
					$address = $item['street'] . ' ' . $item['house_number'] . ', ' . $item['postcode'] . ' ' . $item['city'];

					$data = [
						'deliveryMethodId' => $deliveryMethod->id,
						'extId' => $extId,
						'name' => $name,
						'address' => $address,
						'syncTime' => new DateTimeImmutable(),
						'lat' => $item['latitude'],
						'lng' => $item['longitude'],
						'openingHours' => Json::encode($this->getOpeningHours($item)),
						'image' => $item['photo'] ?? null,
						'stateId' => $state->id,
					];

					$imported[] = $extId;

					$this->connection->query('INSERT INTO [pickup_point] %values ON DUPLICATE KEY UPDATE %set', $data, $data);

					$i++;
				}
				$output->writeln('Total processed: ' . $i);
			}

			if ($imported !== []) {
				$this->connection->query('DELETE FROM [pickup_point] WHERE deliveryMethodId = %?i AND extId NOT IN %s[]', $deliveryMethod->id, $imported);
			}

		} catch (\Throwable $e) {
			$output->writeln($e->getMessage());
		}

		return $this->end(self::SUCCESS);
	}

	private function getOpeningHours(array $item): array
	{
		$content = [];
		if (isset($item['hours'])) {
			foreach ($item['hours'] as $weekDay) {
				$openingHours = [];
				if (!empty($weekDay['openMorning']) && !empty($weekDay['closeMorning'])) {
					$openingHours[] = ['from' => $weekDay['openMorning'], 'to' => $weekDay['closeMorning']];
				}
				if (!empty($weekDay['openAfternoon']) && !empty($weekDay['closeAfternoon'])) {
					$openingHours[] = ['from' => $weekDay['openAfternoon'], 'to' => $weekDay['closeAfternoon']];
				}
				if ($openingHours !== []) {
					$content[$weekDay['dayName']] = $openingHours;
				}
			}
		}
		return $content;
	}

}
