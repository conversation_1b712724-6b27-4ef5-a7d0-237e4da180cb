<?php declare(strict_types = 1);

namespace App\Console\PriceLog;

use App\Console\BaseCommand;
use App\Model\ConfigService;
use App\Model\Email\Common;
use App\Model\Email\CommonFactory;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use DateTimeImmutable;
use Nette\Utils\ArrayHash;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use function count;
use function intval;

#[AsCommand(
	name: 'price-log:check-sales-duration',
)]
final class CheckSalesDurationCommand extends BaseCommand
{

	protected ArrayHash $config;

	protected Common $commonEmail;

	public function __construct(
		protected readonly Orm $orm,
		private readonly CommonFactory $commonEmailFactory,
		private readonly MutationHolder $mutationHolder,
		protected readonly ConfigService $configService,
	)
	{
		parent::__construct();

		$this->config = ArrayHash::from($this->configService->get('priceLogger'));
		$this->commonEmail = $this->commonEmailFactory->create();
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->start($input);
		$output->writeln('Start checking for variants with sale longer then sale duration.');

		/** @var Mutation $mutation */
		foreach ($this->orm->mutation->findAll() as $mutation) {
			$notificationDays = $this->config->durationDays ?? 15;
			$notificationEmail = $this->config->notificationEmail ?? '';
			if (
				$notificationDays !== '' &&
				$notificationEmail !== ''
			) {
				$notificationDays = intval($notificationDays);
				$periodFrom = new DateTimeImmutable('-' . $notificationDays . ' days');
				$periodFrom = $periodFrom->setTime(23, 59, 59);
				$productVariantsAfterSaleDurationItems = $this->orm->productVariantPriceLog->findAfterSaleDuration($mutation->id, $periodFrom);

				if (count($productVariantsAfterSaleDurationItems) > 0) {
					$this->orm->setMutation($mutation);
					$this->mutationHolder->setMutation($mutation);

					$values = ['productVariantsAfterSaleDuration' => $productVariantsAfterSaleDurationItems, 'saleDurationNotificationDays' => $notificationDays];
					$output->writeln('Sending email...');

					// subject: 'Notifikace: produkty po době max. trvání akce',
					// TODO: add dbTemplate in superadmin
					$this->commonEmail->send(
						null,
						$notificationEmail,
						'productsAfterSaleDurationNotification',
						$values,
					);

					$output->writeln('Email sent...');
				}
			}
		}

		$output->writeln('Finished checking for variants with sale longer then sale duration.');

		return $this->end(self::SUCCESS);
	}

}
