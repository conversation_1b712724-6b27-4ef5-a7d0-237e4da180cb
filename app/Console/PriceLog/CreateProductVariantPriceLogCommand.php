<?php declare(strict_types = 1);

namespace App\Console\PriceLog;

use App\Console\BaseCommand;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Orm;
use App\Model\Orm\PriceLevel\PriceLevelModel;
use App\Model\PriceLogger\ProductVariantPriceLogger;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputDefinition;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Throwable;
use function floatval;
use function intval;
use function strval;

#[AsCommand(
	name:'price-log:create'
)]
class CreateProductVariantPriceLogCommand extends BaseCommand
{

	public function __construct(
		protected Orm $orm,
		protected ProductVariantPriceLogger $productVariantPriceLogger,
		protected PriceLevelModel $priceLevelModel,
		protected MutationHolder $mutationHolder,
	)
	{
		parent::__construct();
	}

	protected function configure(): void
	{
		$this->setDefinition(
			new InputDefinition([
				new InputArgument('productVariantId', InputArgument::REQUIRED, 'Product variant ID'), //264
				new InputArgument('originalPrice', InputArgument::REQUIRED, 'Original price'), //100
				new InputArgument('salePrice', InputArgument::REQUIRED, 'Sale price'), //90
				new InputArgument('priceLevelType', InputArgument::REQUIRED, 'PriceLevel type'), //1
			]),
		);
		parent::configure();
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->start($input);
		$output->writeln('Start creating product variant price log.');

		$productVariantId = intval($input->getArgument('productVariantId'));
		$originalPrice = floatval($input->getArgument('originalPrice'));
		$salePrice = floatval($input->getArgument('salePrice'));
		$priceLevelType = strval($input->getArgument('priceLevelType'));

		$mutation = $this->orm->mutation->getDefault();
		$this->orm->setMutation($mutation);
		$this->mutationHolder->setMutation($mutation);

		$productVariant = $this->orm->productVariant->getById($productVariantId);
		$priceLevels = $this->priceLevelModel->getAllPriceLevelByType();
		$priceLevel = $priceLevels[$priceLevelType] ?? null;

		if ($priceLevel === null) {
			$output->writeln('Log not created - priceLevelType not exists..');

			return self::FAILURE;
		}

		if ($productVariant !== null) {
			if ($productVariant->priceLogs->countStored() !== 0) {
				try {
					$priceLog = $this->productVariantPriceLogger->createLogWithChangeCheck($productVariant, $priceLevel, $originalPrice, $salePrice);

					if ($priceLog === null) {
						$output->writeln('Log not created - price wasn\'t changed.');

						return self::FAILURE;
					}

					$this->orm->flush();

					$output->writeln('Product variant price log created.');
				} catch (Throwable $exception) {
					$output->writeln($exception->getMessage());

					return self::FAILURE;
				}
			} else {
				$output->writeln('Product variant log not inited. Run eshop:logs:init first.');
			}
		} else {
			$output->writeln('Product variant with id ' . $productVariantId . ' not found.');
		}

		return $this->end(self::SUCCESS);
	}

}
