<?php declare(strict_types = 1);

namespace App\Console\PriceLog;

use App\Console\BaseCommand;
use App\Model\Currency\CurrencyHelper;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Orm;
use App\Model\Orm\ProductVariantPrice\ProductVariantPrice;
use App\Model\PriceLogger\ProductVariantPriceLogger;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Throwable;

#[AsCommand(
	name:'price-log:init'
)]
class InitProductVariantPriceLogCommand extends BaseCommand
{

	public function __construct(
		protected Orm $orm,
		protected ProductVariantPriceLogger $productVariantPriceLogger,
		protected MutationHolder $mutationHolder,
	)
	{
		parent::__construct();
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->start($input);
		$output->writeln('Init product variant price logs started.');

		$mutation = $this->orm->mutation->getDefault();
		$this->orm->setMutation($mutation);
		$this->mutationHolder->setMutation($mutation);

		$i = 0;
		/** @var ProductVariantPrice $productVariantPrice */
		foreach ($this->orm->productVariantPrice->findAllWithDiscountPriceLevel(CurrencyHelper::CURRENCY_CZK) as $productVariantPrice) {
			try {
				$hasLog = $this->orm->productVariantPriceLog->findBy([
						'mutation' => $productVariantPrice->mutation->id,
						'productVariant' => $productVariantPrice->productVariant,
						'priceLevel' => $productVariantPrice->priceLevel->id,
					])->countStored() > 0;
				if (!$hasLog) {
					/** @var ProductVariantPrice $discountPrice */
					$discountPrice = $this->orm->productVariantPrice->getBy([
						'mutation' => $productVariantPrice->mutation->id,
						'productVariant' => $productVariantPrice->productVariant,
						'priceLevel' => $productVariantPrice->priceLevel,
					]);

					$this->productVariantPriceLogger->initLog(
						$productVariantPrice->productVariant,
						$productVariantPrice->priceLevel,
						(float) $productVariantPrice->price->amount,
						((float) $discountPrice->price->amount) > 0 ? (float) $discountPrice->price->amount : (float) $productVariantPrice->price->amount,
					);
				}

				if (++$i === 500) {
					$i = 0;
					$this->orm->flush();
				}
			} catch (Throwable $exception) {
				$output->writeln('Cant init product variant with id ' . $productVariantPrice->productVariant->id . '. Error: ' . $exception->getMessage());
			}
		}

		$this->orm->flush();
		$output->writeln('Init product variant price logs finished.');

		return $this->end(self::SUCCESS);
	}

}
