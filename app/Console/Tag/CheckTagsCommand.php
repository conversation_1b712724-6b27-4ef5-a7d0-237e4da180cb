<?php declare(strict_types = 1);

namespace App\Console\Tag;

use App\Console\BaseCommand;
use App\Model\Messenger\Elasticsearch\ElasticBusWrapper;
use App\Model\Orm\Orm;
use App\PostType\Tag\Model\Checker\Provider;
use App\PostType\Tag\Model\Checker\Task\TagMessage;
use App\PostType\Tag\Model\TagType;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Scheduler\Attribute\AsCronTask;

#[AsCommand(
	name: 'tag:check',
	description: 'Assign tags to products by specific criterias',
)]
#[AsCronTask(expression: '0 23 * * *', arguments: 'new cs', transports: 'cronCommands')]
#[AsCronTask(expression: '45 23 * * *', arguments: 'giftFree cs', transports: 'cronCommands')]
#[AsCronTask(expression: '45 22 * * *', arguments: 'transitFree cs', transports: 'cronCommands')]
#[AsCronTask(expression: '45 22 * * *', arguments: 'paidByLo cs', transports: 'cronCommands')]
#[AsCronTask(expression: '45 22 * * *', arguments: 'paidByLoFull cs', transports: 'cronCommands')]
#[AsCronTask(expression: '45 22 * * *', arguments: 'promoPrice cs', transports: 'cronCommands')]
class CheckTagsCommand extends BaseCommand
{

	public function __construct(
		protected readonly Orm $orm,
		private readonly Provider $provider,
		private readonly ElasticBusWrapper $elasticBusWrapper,
	)
	{
		parent::__construct();
	}

	protected function configure(): void
	{
		$this->addArgument('tagType', InputArgument::REQUIRED)
			->addArgument('mutationCode', InputArgument::REQUIRED);
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->start($input);
		$tagType = TagType::from($input->getArgument('tagType'));
		$mutationCode = $input->getArgument('mutationCode');
		$mutation = $this->orm->mutation->getByChecked(['langCode' => $mutationCode]);

		foreach ($this->provider->getItems($mutation) as $productLocalizationId) {
			$this->elasticBusWrapper->send(
				new TagMessage(
					$productLocalizationId,
					$tagType
				)
			);
		}

		$output->writeLn('DONE');
		return $this->end(self::SUCCESS);
	}

}
