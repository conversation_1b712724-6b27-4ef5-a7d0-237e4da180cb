<?php /** @noinspection PhpUnused */
declare(strict_types = 1);

namespace App\Console\Erp;

use App\Model\Erp\Connector\PriceConnector;
use App\Model\Erp\Connector\Query;
use App\Model\Erp\Importer\PriceImporterFactory;
use App\Model\Erp\Importer\PriceImporter;
use App\Model\Erp\Processor\Batch\Result;
use App\Model\Orm\ImportCache\ImportCache;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Scheduler\Attribute\AsCronTask;

#[AsCommand(
	name: 'erp:import:price',
	description: 'Product price update',
)]
#[AsCronTask(expression: '40 18 * * *', schedule: 'import_price', transports: 'cronCommands')]
final class ImportPriceCommand extends BaseCommand
{

	private PriceImporter $importer;

	private array $erpIds = [];

	protected function configure(): void
	{
		$this->addOption('erpId', null, InputOption::VALUE_REQUIRED | InputOption::VALUE_IS_ARRAY, 'Import only specific IDs');
		parent::configure();
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->beginExecution($this->getDescription(), $output);

		$erpIds = $input->getOption('erpId');
		if ($erpIds !== []) {
			$this->erpIds = $erpIds;
		}

		$this->importer = $this->importers->get(PriceImporterFactory::class)->create($this->connectors->get(PriceConnector::class));
		assert($this->importer instanceof PriceImporter);

		$this->processBatches($output, $input);

		$this->endExecution($output);
		return $this->end(self::SUCCESS);
	}

	protected function processBatch(): Result
	{
		assert($this->importer instanceof PriceImporter);
		return $this->importer->import(
			ImportCache::TYPE_PRICE,
			new Query(
				erpIds: $this->erpIds,
			)
		);
	}

	protected function processResult(Result $result): void
	{
		assert($this->importer instanceof PriceImporter);
		$this->importer->afterImport($result);
	}

}
