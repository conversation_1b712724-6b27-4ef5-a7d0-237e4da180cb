<?php declare(strict_types = 1);

namespace App\Console\Erp;

use App\Exceptions\LogicException;
use App\Model\Erp\Connector\AlternativeCategoryGoogleConnector;
use App\Model\Erp\Connector\AlternativeCategoryHeurekaConnector;
use App\Model\Erp\Connector\AlternativeCategoryZboziConnector;
use App\Model\Erp\Importer\AlternativeCategoryGoogle;
use App\Model\Erp\Importer\AlternativeCategoryGoogleFactory;
use App\Model\Erp\Importer\AlternativeCategoryHeureka;
use App\Model\Erp\Importer\AlternativeCategoryHeurekaFactory;
use App\Model\Erp\Importer\AlternativeCategoryZbozi;
use App\Model\Erp\Importer\AlternativeCategoryZboziFactory;
use App\Model\Erp\Processor\Batch\Result;
use App\Model\Orm\ImportCache\ImportCache;
use App\PostType\Page\Model\Orm\TreeAlternative;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name: 'erp:import:alternativeCategories',
	description: 'Alternative categories import',
)]
// [AsCronTask(expression: '# #(0-3) * * *', arguments: 'heureka', transports: 'cronCommands')]
// [AsCronTask(expression: '# #(0-3) * * *', arguments: 'google', transports: 'cronCommands')]
// [AsCronTask(expression: '# #(0-3) * * *', arguments: 'zbozi', transports: 'cronCommands')]
final class ImportAlternativeCategoriesCommand extends BaseCommand
{

	private AlternativeCategoryZbozi|AlternativeCategoryGoogle|AlternativeCategoryHeureka $importer;

	protected function configure(): void
	{
		parent::configure();
		$this->addArgument('type', InputArgument::REQUIRED, 'Type of alternative [heureka, zbozi, google]');
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->beginExecution($this->getDescription(), $output);

		$type = $input->getArgument('type');

		$this->importer = match ($type) {
			TreeAlternative::TYPE_HEUREKA => $this->importers->get(AlternativeCategoryHeurekaFactory::class)->create($this->connectors->get(AlternativeCategoryHeurekaConnector::class)),
			TreeAlternative::TYPE_GOOGLE => $this->importers->get(AlternativeCategoryGoogleFactory::class)->create($this->connectors->get(AlternativeCategoryGoogleConnector::class)),
			TreeAlternative::TYPE_ZBOZI =>  $this->importers->get(AlternativeCategoryZboziFactory::class)->create($this->connectors->get(AlternativeCategoryZboziConnector::class)),
			default => throw new LogicException('Unknown alternative type'),
		};

		$this->processBatches($output, $input);

		$this->endExecution($output);
		return $this->end(self::SUCCESS);
	}

	protected function processBatch(): Result
	{
		return $this->importer->import(
			ImportCache::TYPE_ALTERNATIVE_CATEGORY
		);
	}
	protected function processResult(Result $result): void
	{
		$this->importer->afterImport($result);
	}

}
