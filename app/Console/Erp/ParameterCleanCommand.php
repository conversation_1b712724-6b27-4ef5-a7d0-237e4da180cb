<?php

declare(strict_types=1);

namespace App\Console\Erp;

use App\Model\Orm\Parameter\ParameterRepository;
use Nextras\Dbal\Connection;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name: 'erp:clean:parameter',
)]
// [AsCronTask(expression: '# #(0-2) * * *', transports: 'cronCommands')]
final class ParameterCleanCommand extends \App\Console\BaseCommand
{

	public function __construct(
		private readonly Connection $connection,
		private readonly ParameterRepository $parameterRepository,
	)
	{
		parent::__construct();
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->connection->query('DELETE FROM parameter_value WHERE id NOT IN (SELECT parameterValueId FROM product_parameter) ');
		$this->connection->query('DELETE FROM parameter WHERE id NOT IN (SELECT parameterId FROM product_parameter)');

		$this->parameterRepository->initSort(forceInit: true);

		return $this->end(self::SUCCESS);
	}

}
