<?php

declare(strict_types=1);

namespace App\Console\Erp;

use App\Model\Image\Storage\BasicStorage;
use Nette\Utils\FileSystem;
use Nette\Utils\Finder;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name: 'erp:product:image:path',
)]
final class ProductImagePathCommand extends \App\Console\BaseCommand
{

	public function __construct(
		private readonly BasicStorage $imageStorage,
	)
	{
		parent::__construct();
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		foreach (Finder::findFiles('*')->in(WWW_DIR . '/data/images')->limitDepth(10) as $file) {
			$source = (string) $file;
			$pathinfo = pathinfo($source);
			$destination = $this->imageStorage->getPathToOriginalImage($pathinfo['filename'], $pathinfo['extension'] ?? '');
			$destinationDir = dirname($destination);

			try {
				FileSystem::createDir($destinationDir, 0755);
				FileSystem::rename($source, $destination);
			} catch (\Throwable $e) {
				// if throw do nothing
			}

			$output->writeln($destinationDir . ' / ' . $pathinfo['filename']);
		}

		return $this->end(self::SUCCESS);
	}

}
