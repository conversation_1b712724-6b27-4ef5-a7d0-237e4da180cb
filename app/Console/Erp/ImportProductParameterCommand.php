<?php /** @noinspection PhpUnused */
declare(strict_types = 1);

namespace App\Console\Erp;

use App\Model\Erp\Connector\ParameterConnector;
use App\Model\Erp\Connector\Query;
use App\Model\Erp\Importer\ProductParameterImporter;
use Contributte\Monolog\LoggerManager;
use Nette\InvalidStateException;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Scheduler\Attribute\AsCronTask;
use Throwable;
use Tracy\Debugger;

#[AsCommand(
	name: 'erp:import:productParameter',
	description: 'Product parameter import',
)]
#[AsCronTask(expression: '10 3 * * *', schedule: 'import_productparameter', transports: 'cronCommands')]
final class ImportProductParameterCommand extends \App\Console\BaseCommand
{

	protected static $defaultName = 'erp:import:productParameter';

	public function __construct(
		protected ParameterConnector $connector,
		protected readonly LoggerManager $loggerManager,
		protected readonly ProductParameterImporter $productParameterImporter,
	)
	{
		parent::__construct();
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->logger = $this->loggerManager->get(CommandType::PRODUCT_PARAMETER_IMPORT);

		$output->writeLn('START');
		$this->start($input);

		$this->logger->info('Import start');

		try {

			$result = $this->connector->getRows(new Query());
			$rows = $result->count();

			$this->logger->info('Result loaded', ['rows' => $rows]);

			if ($result->count() === 0) {
				throw new InvalidStateException('Result is empty.');
			}

			$this->productParameterImporter->processResult($result, $this->logger);

		} catch (Throwable $e) {
			$this->logger->error($e->getMessage());
			$output->writeLn($e->getMessage());
			Debugger::log($e, Debugger::ERROR);
		}

		$output->writeLn('DONE');
		return $this->end(self::SUCCESS);
	}

}
