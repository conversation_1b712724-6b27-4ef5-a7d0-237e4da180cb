<?php declare(strict_types = 1);

namespace App\Console\Erp;

use App\Model\Erp\Processor\Batch\Result;
use App\Model\Erp\Processor\CommonProcessor;
use App\Model\Erp\Processor\Reader\AlternativeCategoryReader;
use App\Model\Erp\Processor\Reader\Reader;
use App\Model\Orm\ImportCache\ImportCache;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name:'erp:process:alternativeCategories',
	description: 'Alternative categories processing',
)]
// [AsCronTask(expression: '# 4 * * *', transports: 'cronCommands')]
final class ProcessAlternativeCategoriesCommand extends BaseCommand
{

	protected CommonProcessor $couponImportProcessor;

	protected Reader $reader;

	protected function init(): void
	{
		parent::init();
		$this->batchLimit = $this->config->alternative_category->process->batchLimit;
		$this->reader = $this->readers->get(AlternativeCategoryReader::class);
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->beginExecution($this->getDescription(), $output);

		$this->couponImportProcessor = $this->commonProcessorFactory->create(ImportCache::TYPE_ALTERNATIVE_CATEGORY, $output);
		$this->processBatches($output, $input);

		$this->endExecution($output);
		return $this->end(self::SUCCESS);
	}

	protected function processBatch(): Result
	{
		return $this->couponImportProcessor->process($this->processItem(...), $this->batchLimit);
	}

	public function continueNextBatch(): bool
	{
		return $this->couponImportProcessor->getNextBatch($this->batchLimit)->countStored() > 0;
	}


	private function processItem(ImportCache $importCache): void
	{
		assert($this->reader instanceof AlternativeCategoryReader);
		$this->reader->read($importCache, $this->couponImportProcessor->getLogger());
	}

	protected function initBatch(): void
	{
		assert($this->reader instanceof AlternativeCategoryReader);
		$this->reader->init();
	}

}
