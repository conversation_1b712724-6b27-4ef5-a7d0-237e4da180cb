<?php declare(strict_types = 1);

namespace App\Console\Erp;

use App\Model\Erp\Connector\HeurekaShopReviewConnector;
use App\Model\Erp\Importer\HeurekaShopReviewImporter;
use App\Model\Erp\Importer\HeurekaShopReviewImporterFactory;
use App\Model\Erp\Processor\Batch\Result;
use App\Model\Orm\ImportCache\ImportCache;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name: 'erp:import:heurekaShopReview',
	description: 'Shop review heureka import',
)]
// [AsCronTask(expression: '# #(9-15) * * *', transports: 'cronCommands')]
final class ImportHeurekaShopReviewCommand extends BaseCommand
{

	private HeurekaShopReviewImporter $importer;

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->beginExecution($this->getDescription(), $output);

		$this->importer = $this->importers->get(HeurekaShopReviewImporterFactory::class)->create($this->connectors->get(HeurekaShopReviewConnector::class));
		$this->processBatches($output, $input);

		$this->endExecution($output);
		return $this->end(self::SUCCESS);
	}

	protected function processBatch(): Result
	{
		assert($this->importer instanceof HeurekaShopReviewImporter);
		return $this->importer->import(
			ImportCache::TYPE_HEUREKA_SHOP_REVIEW
		);
	}

}
