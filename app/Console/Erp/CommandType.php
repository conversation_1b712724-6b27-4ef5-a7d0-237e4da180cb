<?php declare(strict_types = 1);

namespace App\Console\Erp;

readonly final class CommandType
{

	public const string PRODUCT_IMPORT = 'product_import';
	public const string PRODUCT_PROCESS = 'product_process';

	public const string PARAMETER_IMPORT = 'parameter_import';
	public const string PRODUCT_PARAMETER_IMPORT = 'product_parameter_import';

	public const string STOCK_IMPORT = 'stock_import';
	public const string STOCK_PROCESS = 'stock_process';

	public const string PRICE_IMPORT = 'price_import';
	public const string PRICE_PROCESS = 'price_process';

	public const string ORDER_IMPORT  = 'order_import';

	public const string HEUREKA_REVIEW_IMPORT = 'heureka_review_import';
	public const string HEUREKA_SHOP_REVIEW_IMPORT = 'heureka_shop_review_import';
	public const string HEUREKA_REVIEW_PROCESS = 'heureka_review_process';
	public const string HEUREKA_SHOP_REVIEW_PROCESS = 'heureka_shop_review_process';

	public const string ALTERNATIVE_CATEGORY_IMPORT = 'alternative_category_import';

}
