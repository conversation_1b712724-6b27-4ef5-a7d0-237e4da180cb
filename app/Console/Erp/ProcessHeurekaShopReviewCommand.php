<?php declare(strict_types = 1);

namespace App\Console\Erp;

use App\Model\Erp\Processor\Batch\Result;
use App\Model\Erp\Processor\CommonProcessor;
use App\Model\Erp\Processor\Reader\HeurekaShopReviewReader;
use App\Model\Erp\Processor\Reader\Reader;
use App\Model\Orm\ImportCache\ImportCache;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name: 'erp:process:heurekaShopReview',
	description: 'Shop review heureka processing',
)]
// [AsCronTask(expression: '16 16 * * *', transports: 'cronCommands')]
final class ProcessHeurekaShopReviewCommand extends BaseCommand
{

	private CommonProcessor $commonImportProcessor;

	private Reader $reader;

	protected function init(): void
	{
		parent::init();
		$this->batchLimit = $this->config->reviewHeureka->process->batchLimit ?? 100;
		$this->reader = $this->readers->get(HeurekaShopReviewReader::class);
	}


	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->beginExecution($this->getDescription(), $output);

		$this->commonImportProcessor = $this->commonProcessorFactory->create(ImportCache::TYPE_HEUREKA_SHOP_REVIEW, $output);
		$this->processBatches($output, $input);

		$this->endExecution($output);
		return $this->end(self::SUCCESS);
	}

	protected function processBatch(): Result
	{
		return $this->commonImportProcessor->process($this->processItem(...), $this->batchLimit);
	}

	public function continueNextBatch(): bool
	{
		return $this->commonImportProcessor->getNextBatch($this->batchLimit)->countStored() > 0;
	}


	private function processItem(ImportCache $importCache): void
	{
		assert($this->reader instanceof HeurekaShopReviewReader);
		$this->reader->readHeurekaReview($importCache, $this->commonImportProcessor->getLogger());
	}

}
