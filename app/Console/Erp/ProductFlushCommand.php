<?php

declare(strict_types=1);

namespace App\Console\Erp;

use Nette\Utils\FileInfo;
use Nette\Utils\FileSystem;
use Nette\Utils\Finder;
use Nextras\Dbal\Connection;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name: 'erp:product:flush',
)]
final class ProductFlushCommand extends \App\Console\BaseCommand
{

	public function __construct(
		private readonly Connection $connection,
	)
	{
		parent::__construct();
	}


	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->connection->query('SET foreign_key_checks = 0');
		$this->connection->query('TRUNCATE TABLE stock_supplies');

		$this->connection->query('TRUNCATE TABLE review_x_product');
		$this->connection->query('TRUNCATE TABLE review');

		$this->connection->query('TRUNCATE TABLE product_variant_price_log');
		$this->connection->query('TRUNCATE TABLE product_variant_price');
		$this->connection->query('TRUNCATE TABLE product_variant_localization');
		$this->connection->query('TRUNCATE TABLE product_variant');
		$this->connection->query('TRUNCATE TABLE product_tree');
		$this->connection->query('TRUNCATE TABLE product_review');
		$this->connection->query('TRUNCATE TABLE product_product');
		$this->connection->query('TRUNCATE TABLE product_parameter');
		$this->connection->query('TRUNCATE TABLE product_localization');
		$this->connection->query('TRUNCATE TABLE product_image');
		$this->connection->query('TRUNCATE TABLE product');

		$this->connection->query('TRUNCATE TABLE parameter_value');
		$this->connection->query('TRUNCATE TABLE parameter');

		$this->connection->query('TRUNCATE TABLE supplier');

		$this->connection->query('TRUNCATE TABLE import_cache');

		$this->connection->query('TRUNCATE TABLE order_voucher');
		$this->connection->query('TRUNCATE TABLE order_state_change');
		$this->connection->query('TRUNCATE TABLE order_product');
		$this->connection->query('TRUNCATE TABLE order_payment_information');
		$this->connection->query('TRUNCATE TABLE order_payment');
		$this->connection->query('TRUNCATE TABLE order_number_sequence');
		$this->connection->query('TRUNCATE TABLE order_delivery_information');
		$this->connection->query('TRUNCATE TABLE order_delivery');
		$this->connection->query('TRUNCATE TABLE `order`');

		$this->connection->query('TRUNCATE TABLE tag_x_product');
		$this->connection->query('TRUNCATE TABLE alias_history');

		$this->connection->query("DELETE FROM string WHERE `name` LIKE 'pvalue%'");
		$this->connection->query("DELETE FROM string WHERE `name` LIKE 'pname%'");

		//$this->connection->query('DELETE FROM image WHERE libraryId = %i', ProductImageConsumer::LIBRARY_TREE_CATEGORY);

		/** @var FileInfo $dir */
		foreach (Finder::findDirectories('images-*')->in(WWW_DIR . '/data') as $dir) {
			FileSystem::delete($dir->getPathname());
		}

		$this->connection->query("DELETE FROM alias WHERE module != 'tree'");

		$output->writeLn('DONE');

		return $this->end(self::SUCCESS);
	}

}
