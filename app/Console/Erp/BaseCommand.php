<?php declare(strict_types = 1);

namespace App\Console\Erp;

use App\Model\ConfigService;
use App\Model\Erp\Connector\ConnectorRegistry;
use App\Model\Erp\Importer\ImporterFactoryRegistry;
use App\Model\Erp\Processor\Batch\Result;
use App\Model\Erp\Processor\CommonProcessorFactory;
use App\Model\Erp\Processor\Reader\ReaderRegistry;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\OrmCleaner;
use Closure;
use Nette\Utils\ArrayHash;
use RuntimeException;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Throwable;

abstract class BaseCommand extends \App\Console\BaseCommand
{

	protected int $batchLimit = 0;

	protected int $processed = 0;

	protected bool $isLockable = true;

	protected ArrayHash $config;

	public function __construct(
		protected readonly ConnectorRegistry $connectors,
		protected readonly ImporterFactoryRegistry $importers,
		protected readonly ReaderRegistry $readers,
		protected readonly CommonProcessorFactory $commonProcessorFactory,
		protected readonly Orm $orm,
		private readonly OrmCleaner $ormCleaner,
		private readonly MutationHolder $mutationHolder,
		private readonly ConfigService $configService,
		protected readonly MessageBusInterface $messageBus,
	)
	{
		parent::__construct();
		$this->config = ArrayHash::from($this->configService->get('erp'));

		$mutation = $this->orm->mutation->getByCode(Mutation::DEFAULT_CODE);
		$this->orm->setMutation($mutation);
		$this->mutationHolder->setMutation($mutation);
	}

	protected function configure(): void
	{
		$this->addOption('with-progress', 'p', InputOption::VALUE_NONE, 'Show progress');
		parent::configure();
	}





	/**
	 * @throws RuntimeException
	 */
	protected function beginExecution(string $message, OutputInterface $output): void
	{
		$output->writeln($message);
		$this->init();
		$this->checkLock();
	}

	protected function endExecution(OutputInterface $output, bool $withFlush = true): void
	{
		if ($withFlush) {
			$this->orm->flush();
		}
		$this->releaseLock();

		$output->writeln('---');
		$output->writeln('DONE');
	}

	protected function processBatches(OutputInterface $output, InputInterface $input): void
	{
		$withProgress = (bool) $input->getOption('with-progress');
		do {
			try {
				$this->initBatch();
				$result = $this->processBatch();
				$this->orm->flush();
				$this->ormCleaner->safeClear();

				gc_collect_cycles();

				$processed = $result->count ?? 0;
				$this->processed += $processed;

				if ($this->config->batches->useSleep) {
					sleep($this->config->batches->sleep);
				}
				if ($withProgress) {
					$output->writeln(sprintf('Total processed items: %d', $this->processed));
				}

				$this->processResult($result);

			} catch (Throwable $e) {
				$output->writeln('Exception: ' . $e->getMessage());
				break;
			}

		} while ($this->continueNextBatch());
	}

	protected function processResult(Result $result): void
	{
	}

	protected function continueNextBatch(): bool
	{
		return false;
	}

	protected function processBatch(): Result
	{
		return new Result();
	}

	protected function initBatch(): void
	{
	}

	/**
	 * @phpstan-param Closure(mixed $value): mixed|null $decorator
	 */
	protected function passArgument(mixed $value, mixed $default = null, ?Closure $decorator = null): mixed
	{
		if (is_string($value) && strlen(trim($value)) === 0) {
			$value = null;
		}

		if ($default === null && $value === null) {
			return null;
		}

		if ($value === null) {
			return $default;
		}

		if (is_string($value) && $decorator !== null) {
			return $decorator($value);
		}

		return $value;
	}

}
