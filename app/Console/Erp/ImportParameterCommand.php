<?php /** @noinspection PhpUnused */
declare(strict_types = 1);

namespace App\Console\Erp;

use App\Model\Erp\Connector\ParameterConnector;
use App\Model\Erp\Connector\Query;
use App\Model\Erp\Importer\ParameterImporter;
use Contributte\Monolog\LoggerManager;
use Nette\InvalidStateException;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Scheduler\Attribute\AsCronTask;
use Throwable;
use Tracy\Debugger;

#[AsCommand(
	name: 'erp:import:parameter',
	description: 'Parameter import',
)]
#[AsCronTask(expression: '40 1 * * *', schedule: 'import_parameter', transports: 'cronCommands')]
final class ImportParameterCommand extends \App\Console\BaseCommand
{

	protected static $defaultName = 'erp:import:parameter';

	public function __construct(
		protected ParameterConnector $connector,
		protected readonly LoggerManager $loggerManager,
		protected readonly ParameterImporter $parameterImporter,
	)
	{
		parent::__construct();
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->logger = $this->loggerManager->get(CommandType::PARAMETER_IMPORT);

		$output->writeLn('START');
		$this->start($input);

		$this->logger->info('Import start');

		try {

			$result = $this->connector->getRows(new Query());
			$rows = $result->count();

			$this->logger->info('Result loaded', ['rows' => $rows]);

			if ($result->count() === 0) {
				throw new InvalidStateException('Result is empty.');
			}

			$this->parameterImporter->processResult($result, $this->logger);

		} catch (Throwable $e) {
			$this->logger->error($e->getMessage());
			$output->writeLn($e->getMessage());
			Debugger::log($e, Debugger::ERROR);
		}

		$output->writeLn('DONE');
		return $this->end(self::SUCCESS);
	}

}
