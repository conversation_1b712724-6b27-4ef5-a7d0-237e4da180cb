<?php declare(strict_types = 1);

namespace App\Console\Erp;

use App\Model\Orm\ImportCache\ImportCache;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name: 'erp:reprocess',
	description: 'Re-processing specified type and status by created time',
)]
// [AsCronTask(expression: '43 * * * *', schedule: 'import', transports: 'cronCommands')]
final class ReprocessCommand extends BaseCommand
{

	protected function configure(): void
	{
		parent::configure();
		$this->addArgument('type', InputArgument::REQUIRED, 'What type do you want to process?');
		$this->addOption('status', null, InputOption::VALUE_REQUIRED | InputOption::VALUE_IS_ARRAY, 'Status to find', [ImportCache::STATUS_QUEUED, ImportCache::STATUS_READY]);
		$this->addOption('createdTime', null, InputOption::VALUE_REQUIRED, 'Created time lower than', '-1 hour');
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->beginExecution($this->getDescription(), $output);

		try {
			$type = $input->getArgument('type');
			$status = $input->getOption('status');
			$createdTime = new DateTimeImmutable($input->getOption('createdTime'));

			$messageClass = ImportCache::toMessageClass($type);

			$iterations = 0;

			foreach (
				$this->orm->importCache->findBy([
					'type'          => $type,
					'status'        => $status,
					'createdTime<=' => $createdTime,
				]) as $importCache
			) {
				$importCache->status      = ImportCache::STATUS_QUEUED;
				$importCache->createdTime = new DateTimeImmutable();
				$this->orm->importCache->persistAndFlush($importCache);

				$this->messageBus->dispatch(new $messageClass($importCache->id));
				$iterations++;
			}

			$output->writeln('<info>' . $iterations . ' processed</info>');
		} catch (\Throwable $e) {
			$output->writeln('<error>' . $e->getMessage() . '</error>');
		}

		$this->endExecution($output, false);
		return $this->end(self::SUCCESS);
	}

}
