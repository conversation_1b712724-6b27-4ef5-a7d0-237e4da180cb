<?php

declare(strict_types=1);

namespace App\Console\Erp;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name: 'erp:product:image:flush',
)]
final class ProductImageFlushCommand extends \App\Console\BaseCommand
{

	public function __construct(
		//private readonly Connection $connection
	)
	{
		parent::__construct();
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		/*$ids = $this->connection->query('SELECT libraryImageId FROM product_image')->fetchPairs('libraryImageId', 'libraryImageId');
		$ids += $this->connection->query('SELECT id FROM image WHERE libraryId != %i', ProductImageConsumer::LIBRARY_TREE_CATEGORY)->fetchPairs('id', 'id');

		$output->writeln('Count IDs: ' . count($ids));

		$deleted = 0;
		foreach (Finder::findFiles('*')->from(WWW_DIR . IMAGES_DIR) as $file) {
			$source   = (string) $file;
			$pathinfo = pathinfo($source);

			$e = explode('-', $pathinfo['filename']);
			$id = reset($e);

			if (!isset($ids[$id])) {
				try {
					FileSystem::delete($source);
				} catch (\Throwable $e) {
					// if throw do nothing
				}
				$deleted++;
			}

		}

		$output->writeln('Deleted: ' . $deleted);

		$countDatabase = $this->connection->query('SELECT COUNT(id) AS toDelete FROM image WHERE id NOT IN %i[]', $ids)->fetch();
		$this->connection->query('DELETE FROM image WHERE id NOT IN %i[]', $ids);

		$output->writeln('Deleted from database: ' . $countDatabase->toDelete);*/

		return $this->end(self::SUCCESS);
	}

}
