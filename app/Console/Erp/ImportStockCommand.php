<?php /** @noinspection PhpUnused */
declare(strict_types = 1);

namespace App\Console\Erp;

use App\Model\Erp\Connector\StockConnector;
use App\Model\Erp\Connector\Query;
use App\Model\Erp\Importer\StockImporterFactory;
use App\Model\Erp\Importer\StockImporter;
use App\Model\Erp\Processor\Batch\Result;
use App\Model\Orm\ImportCache\ImportCache;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Scheduler\Attribute\AsCronTask;

#[AsCommand(
	name: 'erp:import:stock',
	description: 'Product stock update',
)]
#[AsCronTask(expression: '0 8,9,10,11,12,13,14,15,16,17,18,19 * * *', schedule: 'import_stock', transports: 'cronCommands')]
final class ImportStockCommand extends BaseCommand
{

	private StockImporter $importer;

	private array $erpIds = [];

	protected function configure(): void
	{
		$this->addOption('erpId', null, InputOption::VALUE_REQUIRED | InputOption::VALUE_IS_ARRAY, 'Import only specific IDs');
		parent::configure();
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->beginExecution($this->getDescription(), $output);

		$erpIds = $input->getOption('erpId');
		if ($erpIds !== []) {
			$this->erpIds = $erpIds;
		}

		$this->importer = $this->importers->get(StockImporterFactory::class)->create($this->connectors->get(StockConnector::class));
		assert($this->importer instanceof StockImporter);

		$this->processBatches($output, $input);

		$this->endExecution($output);
		return $this->end(self::SUCCESS);
	}

	protected function processBatch(): Result
	{
		assert($this->importer instanceof StockImporter);
		return $this->importer->import(
			ImportCache::TYPE_STOCK,
			new Query(
				erpIds: $this->erpIds,
			)
		);
	}

	protected function processResult(Result $result): void
	{
		assert($this->importer instanceof StockImporter);
		$this->importer->afterImport($result);
	}

}
