<?php /** @noinspection PhpUnused */
declare(strict_types = 1);

namespace App\Console\Erp;

use App\Model\Erp\Connector\ProductConnector;
use App\Model\Erp\Connector\Query;
use App\Model\Erp\Importer\ProductImporter;
use App\Model\Erp\Importer\ProductImporterFactory;
use App\Model\Erp\Processor\Batch\Result;
use App\Model\Orm\ImportCache\ImportCache;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Scheduler\Attribute\AsCronTask;

#[AsCommand(
	name: 'erp:import:product',
	description: 'Product import (new/update)',
)]
#[AsCronTask(expression: '10 2 * * *', schedule: 'import', transports: 'cronCommands')]
final class ImportProductCommand extends BaseCommand
{

	private ProductImporter $importer;

	private array $erpIds = [];

	protected function configure(): void
	{
		$this->addOption('erpId', null, InputOption::VALUE_REQUIRED | InputOption::VALUE_IS_ARRAY, 'Import only specific IDs');
		parent::configure();
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->beginExecution($this->getDescription(), $output);

		$erpIds = $input->getOption('erpId');
		if ($erpIds !== []) {
			$this->erpIds = $erpIds;
		}

		$this->importer = $this->importers->get(ProductImporterFactory::class)->create($this->connectors->get(ProductConnector::class));
		assert($this->importer instanceof ProductImporter);

		$this->processBatches($output, $input);

		$this->endExecution($output);
		return $this->end(self::SUCCESS);
	}

	protected function processBatch(): Result
	{
		assert($this->importer instanceof ProductImporter);
		return $this->importer->import(
			ImportCache::TYPE_PRODUCT,
			new Query(
				erpIds: $this->erpIds,
			)
		);
	}

	protected function processResult(Result $result): void
	{
		assert($this->importer instanceof ProductImporter);
		$this->importer->afterImport($result);
	}

}
