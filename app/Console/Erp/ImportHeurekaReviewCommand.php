<?php declare(strict_types = 1);

namespace App\Console\Erp;

use App\Model\Erp\Connector\HeurekaReviewConnector;
use App\Model\Erp\Importer\HeurekaReviewImporter;
use App\Model\Erp\Importer\HeurekaReviewImporterFactory;
use App\Model\Erp\Processor\Batch\Result;
use App\Model\Orm\ImportCache\ImportCache;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name: 'erp:import:heurekaReview',
	description: 'Review heureka import',
)]
// [AsCronTask(expression: '# #(9-15) * * *', transports: 'cronCommands')]
final class ImportHeurekaReviewCommand extends BaseCommand
{

	private HeurekaReviewImporter $importer;

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->beginExecution($this->getDescription(), $output);

		$this->importer = $this->importers->get(HeurekaReviewImporterFactory::class)->create($this->connectors->get(HeurekaReviewConnector::class));
		$this->processBatches($output, $input);

		$this->endExecution($output);
		return $this->end(self::SUCCESS);
	}

	protected function processBatch(): Result
	{
		assert($this->importer instanceof HeurekaReviewImporter);
		return $this->importer->import(
			ImportCache::TYPE_HEUREKA_REVIEW
		);
	}

}
