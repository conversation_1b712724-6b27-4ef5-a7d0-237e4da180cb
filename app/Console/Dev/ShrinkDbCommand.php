<?php declare(strict_types=1);

namespace App\Console\Dev;

use App\Console\BaseCommand;
use Exception;
use Nextras\Dbal\Connection;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name:'dev:shrinkDb',
	description: 'Delete large portion of data from DB'
)]
final class ShrinkDbCommand extends BaseCommand
{

	private string $description = 'To run this !!DANGEROUS!! command use parameter FORCE (-f)';

	public function __construct(
		private readonly string $stageName,
		private readonly Connection $connection,
	)
	{
		parent::__construct();
	}

	protected function configure(): void
	{
		$this->addOption('force', 'f', InputOption::VALUE_NONE, $this->description)
			->addArgument('maxProductId', InputArgument::REQUIRED);
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->start($input);
		if ($this->stageName === 'production') {
			throw new Exception('Cant run in production environment');
		}
		$forced = $input->getOption('force');
		if ($forced) {
			$output->writeLn('Clean tables for \'orders\'');
			$maxProductId = (int) $input->getArgument('maxProductId');
			$this->connection->query('SET foreign_key_checks = 0');

			$this->connection->query('DELETE from gift_x_product');
			$this->connection->query('DELETE from order_delivery_information');
			$this->connection->query('DELETE from order_payment_information');
			$this->connection->query('DELETE from order_voucher');
			$this->connection->query('DELETE from order_state_change');
			$this->connection->query('DELETE from order_product');
			$this->connection->query('DELETE from order_payment_information');
			$this->connection->query('DELETE from order_payment');
			$this->connection->query('DELETE from order_number_sequence');
			$this->connection->query('DELETE from order_gift');
			$this->connection->query('DELETE from order_discount');
			$this->connection->query('DELETE from order_delivery');
			$this->connection->query('DELETE from `order`');

			$this->connection->query('SET foreign_key_checks = 1');

			$this->shrink($maxProductId, $output);

			return $this->end(self::SUCCESS);

		} else {
			throw new Exception($this->description);
		}
	}

	private function shrink(int $maxProductId, OutputInterface $output): void
	{
		$realMaxProductId = $this->connection->query('select Max(id) from product')->fetchField();

		$step  = 1000;
		if ($realMaxProductId - $step > $maxProductId) {
			$iterationMaxProductId = $realMaxProductId - $step;
			$output->writeLn('Products ' . $iterationMaxProductId);
			$this->connection->query('DELETE from tag_x_product where productId > %i', $iterationMaxProductId);
			$this->connection->query('DELETE from review_x_product where product > %i', $iterationMaxProductId);
			$this->connection->query('DELETE from product_redirect where productId > %i', $iterationMaxProductId);
			$this->connection->query('DELETE from discount_product where productId > %i', $iterationMaxProductId);
			$this->connection->query('DELETE from gift_x_product where productId > %i', $iterationMaxProductId);
			$this->connection->query('DELETE from promotion_x_product where productId > %i', $iterationMaxProductId);
			$this->connection->query('DELETE from gift where productId > %i', $iterationMaxProductId);

			$this->connection->query('DELETE from product where damagedParentId > %i', $iterationMaxProductId);

			$this->connection->query('DELETE from product where id > %i', $iterationMaxProductId);

			$this->shrink($maxProductId, $output);
		}
	}

}
