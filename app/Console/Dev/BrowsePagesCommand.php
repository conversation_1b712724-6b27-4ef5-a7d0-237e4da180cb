<?php declare(strict_types=1);

namespace App\Console\Dev;

use App\Console\BaseCommand;
use App\Model\Link\LinkFactory;
use App\Model\Mutation\MutationHolder;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Orm;
use App\Model\Orm\Routable;
use <PERSON>uz<PERSON>Http\Client;
use GuzzleHttp\Pool;
use GuzzleHttp\Psr7\Request;
use Nette\Http\Url;
use Psr\Http\Message\ResponseInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name:'dev:browse:pages',
	description: 'Visit some pages in production to enforce cache load'
)]
final class BrowsePagesCommand extends BaseCommand
{

	public function __construct(
		private readonly string $stageName,
		private readonly MutationsHolder $mutationsHolder,
		private readonly MutationHolder $mutationHolder,
		private readonly Orm $orm,
		private readonly LinkFactory $linkFactory,
	)
	{
		parent::__construct();
	}

	protected function configure(): void
	{
		$this->addOption('force', 'f', InputOption::VALUE_NONE, 'Force overwrite');
		$this->addOption('maxRequests', 'r', InputOption::VALUE_OPTIONAL, 'Maximum number of requests to process');
	}

	public function getPageUrl(Routable $page): Url
	{
		$url = new Url($this->linkFactory->linkTranslateToNette($page));
		$url->user = 'superadmin';
		$url->password = 'skSuperadminStage6';

		$url->setQueryParameter('forcePersonalizationGranted', 1);

		return $url;
	}


	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$output->writeln($this->stageName);
		if ($this->stageName !== 'prod' && $input->getOption('force') !== true) {
			$output->writeln('<error>Only on prod environment allowed, or use -f as force option</error>');
			return 0; // skip for non-production
		}

		$urls = [];

		$mutation = $this->mutationsHolder->getDefault();
		$this->orm->setMutation($mutation);
		$this->mutationHolder->setMutation($mutation);
		foreach ($this->orm->tree->findBy([
			'mutation' => $mutation,
			'template' => ':Front:Catalog:default',
		])->orderBy('level') as $page) {
			$urls[] = $this->getPageUrl($page);
		}

		$pages = [
			'discount',
			'contact',
			'tag',
			'search',
		];
		foreach ($pages as $page) {
			if ($mutation->pages->$page !== null) {
				$urls[] = $this->getPageUrl($mutation->pages->$page);
			}
		}

		$output->writeln(count($urls) . ' page(s) queued');

		$client = new Client([
			'verify' => false,
			'timeout' => 15,
			'connect_timeout' => 15.0,
		]);

		//$urls = [$this->getPageUrl($mutation->pages->eshop)];

		$maxParallelRequests = (int) ($input->getOption('maxRequests') ?? 5);
		$retryQueue = [];
		$this->processRequests($output, $client, $urls, $maxParallelRequests, $retryQueue);

		return 0;
	}

	private function processRequests(OutputInterface $output, Client $client, array $urls, int $maxParallelRequests, array &$retryQueue, int $maxRetries = 5, int $currentRetry = 1): void
	{
		// Generovanie požiadaviek
		$requests = function ($urls) {
			foreach ($urls as $url) {
				yield new Request('GET', $url->getAbsoluteUrl());
			}
		};

		$output->writeln('Processing requests, attempt ' . $currentRetry . '...');

		// Spracovanie s Pool
		$pool = new Pool($client, $requests($urls), [
			'concurrency' => $maxParallelRequests,
			'fulfilled' => function (ResponseInterface $res, $index) use ($output, $urls) {
				$output->writeln('Request ' . $index . ': ' . $res->getStatusCode() . ' | ' . $urls[$index]?->getAbsoluteUrl());
			},
			'rejected' => function (\Throwable $e, $index) use (&$retryQueue, $output, $urls) {
				$retryQueue[] = $urls[$index];
				$output->writeln('Request ' . $index . ' failed: ' . $e::class . ' | ' . $urls[$index]?->getAbsoluteUrl() . ' | ' . $e->getMessage());
			},
		]);

		// Počkajte na dokončenie všetkých požiadaviek
		$pool->promise()->wait();

		// Ak je retry fronta nevyprázdnená a nedosiahol sa maximálny počet pokusov, spracujte ju znovu
		if ($retryQueue !== [] && $currentRetry < $maxRetries) {
			$retryUrls = $retryQueue;
			$retryQueue = []; // Vyprázdnite retry frontu pred ďalším spracovaním
			$this->processRequests($output, $client, $retryUrls, $maxParallelRequests, $retryQueue, $maxRetries, $currentRetry + 1);
		} elseif ($retryQueue !== []) {
			$output->writeln('Max retries reached. Unprocessed requests:');
			print_r(array_map(fn($item) => $item->getAbsoluteUrl(), $retryQueue));
		}
	}

}
