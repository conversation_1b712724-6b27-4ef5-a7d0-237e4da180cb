<?php declare(strict_types=1);

namespace App\Console\Elastic;

use App\Console\BaseCommand;
use App\Model\Orm\EsIndex\EsIndexFacade;
use App\Model\Orm\Orm;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name: 'elastic:index:clean',
	description: 'Remove all unused indexes',
)]
final class CleanCommand extends BaseCommand
{

	public function __construct(
		private readonly EsIndexFacade $esIndexFacade,
		protected readonly Orm $orm,
	)
	{
		parent::__construct();
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->start($input);
		$output->writeLn('REMOVE FROM ORM');

		foreach ($this->orm->esIndex->findBy([
			'active' => 0,
		]) as $esIndex) {
			$output->writeLn($esIndex->name);
			$this->esIndexFacade->delete($esIndex);
		}

		$output->writeLn('DONE');
		return $this->end(self::SUCCESS);
	}

}
