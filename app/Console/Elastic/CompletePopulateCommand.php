<?php declare(strict_types = 1);

namespace App\Console\Elastic;

use App\Console\BaseCommand;
use App\Model\Orm\EsIndex\EsIndexFacade;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Orm\Orm;
use App\Model\Mutation\MutationHolder;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name: 'elastic:index:completePopulate',
	description: 'Fill index by esIndex ID',
)]
final class CompletePopulateCommand extends BaseCommand
{

	public function __construct(
		private readonly EsIndexFacade $esIndexFacade,
		private readonly EsIndexRepository $esIndexRepository,
		protected readonly Orm $orm,
		private readonly MutationHolder $mutationHolder,
	)
	{
		parent::__construct();
	}


	protected function configure(): void
	{
		$this->addArgument('esIndexId', InputArgument::REQUIRED);
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->start($input);
		$esIndexId = (int) $input->getArgument('esIndexId');

		if (($esIndex = $this->esIndexRepository->getById($esIndexId)) !== null) {

			$this->orm->setMutation($esIndex->mutation);
			$this->mutationHolder->setMutation($esIndex->mutation);

			$this->esIndexFacade->fill($esIndex);
			$output->writeLn('DONE');
			return $this->end(self::SUCCESS);
		} else {
			$output->writeLn('EsIndex not found');
			return self::FAILURE;
		}
	}

}
