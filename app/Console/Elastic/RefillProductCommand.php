<?php declare(strict_types = 1);

namespace App\Console\Elastic;

use App\Console\BaseCommand;
use App\Model\ElasticSearch\Product\Convertor\CustomFeedAvailabilityData;
use App\Model\ElasticSearch\Product\Convertor\CustomFeedDataFlag;
use App\Model\ElasticSearch\Product\Convertor\FulltextData;
use App\Model\ElasticSearch\Product\Convertor\PriceData;
use App\Model\ElasticSearch\Product\Convertor\SalesData;
use App\Model\ElasticSearch\Product\Convertor\CustomFeedData;
use App\Model\ElasticSearch\Product\Convertor\SimilarBuyData;
use App\Model\ElasticSearch\Product\Facade;
use App\Model\Mutation\MutationHolder;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\TranslatorDB;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name: 'elastic:refill:product',
	description: 'Refill product index with data'
)]
// [AsCronTask('# #(0-1) * * *', arguments: '-a', transports: 'cronCommands')]
final class RefillProductCommand extends BaseCommand
{

	public function __construct(
		protected readonly Orm $orm,
		private readonly MutationsHolder $mutationsHolder,
		private readonly MutationHolder $mutationHolder,
		private readonly TranslatorDB $translatorDB,
		private readonly Facade $productElasticFacade,
		private readonly EsIndexRepository $esIndexRepository,
	)
	{
		parent::__construct();
	}

	protected function configure(): void
	{
		$this
			->addOption('addSimilarSales', SimilarBuyData::COMMAND_SHORTCUT, null, 'Add SimilarBuyData to selected index')
			->addOption('addOrderSales', SalesData::COMMAND_SHORTCUT, null, 'Add SalesData to selected index')
			->addOption('addCustomFeedData', CustomFeedData::COMMAND_SHORTCUT, null, 'Add customFeedData to selected index')
			->addOption('addCustomFeedAvailabilityData', CustomFeedAvailabilityData::COMMAND_SHORTCUT, null, 'Add customFeedAvailabilityData to selected index')
			->addOption('addFulltextData', FulltextData::COMMAND_SHORTCUT, null, 'Add fulltext to selected index')
			->addOption('addPriceData', PriceData::COMMAND_SHORTCUT, null, 'Add price to selected index')
			->addArgument(
				'indexId',
				InputArgument::OPTIONAL,
				'Index DB id (if missing, all active indices are used)',
				null
			);
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->start($input);
		$indexId = (int) $input->getArgument('indexId');
		$this->orm->setPublicOnly(false);

		$convertorStrings = $this->getConvertors($input, $output);

		if ($convertorStrings === []) {
			throw new \LogicException('Convertors not found');
		}

		if ($indexId !== 0) {
			$esIndex = $this->esIndexRepository->getByIdChecked($indexId);
			$this->setMutation($esIndex->mutation);

			$this->productElasticFacade->fill($esIndex, convertorStrings: $convertorStrings);
		} else {
			foreach ($this->mutationsHolder->findAll(false) as $mutation) {
				$this->setMutation($mutation);
				$esIndex = $this->esIndexRepository->getProductLastActive($mutation);
				if ($esIndex !== null) {
					$this->productElasticFacade->fill($esIndex, convertorStrings: $convertorStrings);
				}
			}
		}

		$output->writeLn('DONE');

		return $this->end(self::SUCCESS);
	}

	private function setMutation(Mutation $mutation): void
	{
		$this->orm->setMutation($mutation);
		$this->mutationHolder->setMutation($mutation);
		$this->translatorDB->setMutation($mutation);
	}

	private function getConvertors(InputInterface $input, OutputInterface $output): array
	{
		$convertorStrings = [];
		$addSimilarSales = (bool) $input->getOption('addSimilarSales');
		$addCustomFeedData = (bool) $input->getOption('addCustomFeedData');
		$addCustomFeedAvailabilityData = (bool) $input->getOption('addCustomFeedAvailabilityData');
		$addOrderSales = (bool) $input->getOption('addOrderSales');
		$addFulltextData = (bool) $input->getOption('addFulltextData');
		$addPriceData = (bool) $input->getOption('addPriceData');

		if ($addSimilarSales) {
			$convertorStrings[] = SimilarBuyData::class;
		}

		if ($addOrderSales) {
			$convertorStrings[] = SalesData::class;
		}
		if ($addPriceData) {
			$convertorStrings[] = PriceData::class;
		}
		if ($addFulltextData) {
			$convertorStrings[] = FulltextData::class;
		}

		if ($addCustomFeedData) {
			$convertorStrings[] = CustomFeedData::class;
			$convertorStrings[] = CustomFeedDataFlag::class;
		}

		if ($addCustomFeedAvailabilityData) {
			$convertorStrings[] = CustomFeedAvailabilityData::class;
		}

		foreach ($convertorStrings as $convertorString) {
			$output->writeln($convertorString);
		}

		return $convertorStrings;
	}

}
