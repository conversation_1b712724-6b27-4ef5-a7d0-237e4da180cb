<?php declare(strict_types = 1);

namespace App\Console\Elastic;

use App\Console\BaseCommand;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\EsIndex\EsIndex;
use App\Model\Orm\EsIndex\EsIndexModel;
use App\Model\Orm\Parameter\ParameterRepository;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name: 'elastic:mapping:upgrade',
	description: 'Try find obsolete indexes to upgrade'
)]
final class UpgradeIndexMappingCommand extends BaseCommand
{

	public function __construct(
		private readonly ParameterRepository $parameterRepository,
		private readonly EsIndexModel $esIndexModel,
		private readonly MutationsHolder $mutationsHolder,
	)
	{
		parent::__construct();
	}

	protected function configure(): void
	{
		$this->addOption('force', 'f', InputOption::VALUE_NONE, 'Force copy product index with new mapping');
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->start($input);
		$forced = $input->getOption('force');
		if ($forced || $this->isProductIndexObsolete()) {
			foreach ($this->mutationsHolder->findAll() as $mutation) {
				$this->esIndexModel->creteNewIndexWithOldDataCopy(EsIndex::TYPE_PRODUCT, $mutation);
			}
			$this->parameterRepository->unlockParameters();
		}

		return $this->end(self::SUCCESS);
	}

	private function isProductIndexObsolete(): bool
	{
		$lockedParameters = $this->parameterRepository->findBy(['isLockedForES' => 1]);
		// phpcs:ignore
		if ($lockedParameters->countStored() > 0) {
			return true;
		}
		// place to add new conditions

		return false;
	}

}
