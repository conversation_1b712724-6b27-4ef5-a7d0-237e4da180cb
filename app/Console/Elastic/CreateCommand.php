<?php declare(strict_types = 1);

namespace App\Console\Elastic;

use App\Console\BaseCommand;
use App\Model\Mutation\MutationHolder;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\EsIndex\EsIndex;
use App\Model\Orm\EsIndex\EsIndexFacade;
use App\Model\Orm\EsIndex\EsIndexModel;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use Nette\Utils\Strings;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use <PERSON>\Debugger;
use <PERSON>\ILogger;

#[AsCommand(
	name:'elastic:index:create',
	description: 'Create elastic indexes'
)]
final class CreateCommand extends BaseCommand
{

	public function __construct(
		protected readonly Orm $orm,
		private readonly MutationsHolder $mutationsHolder,
		private readonly MutationHolder $mutationHolder,
		private readonly EsIndexFacade $esIndexFacade,
		private readonly EsIndexRepository $esIndexRepository,
		private readonly EsIndexModel $esIndexModel,
	)
	{
		parent::__construct();
	}


	protected function configure(): void
	{
		$possibleTypes = EsIndex::getConstsByPrefix('TYPE_');
		$possibleMutationCodes = Mutation::getConstsByPrefix('CODE_');

		$this->addOption('populate', 'p', InputOption::VALUE_NONE, 'Populate index with data')
			->addOption('switch', 's', InputOption::VALUE_NONE, 'Mark index as active')
			->addOption('clean', 'c', InputOption::VALUE_NONE, 'Remove old index')
			->addArgument(
				'inputType',
				InputArgument::OPTIONAL,
				sprintf('ES index type [%s]', implode(',', $possibleTypes)),
				'*'
			)
			->addArgument(
				'inputMutationCode',
				InputArgument::OPTIONAL,
				sprintf('ES index mutation  [%s]', implode(',', $possibleMutationCodes)),
				'*'
			);
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->start($input);
		$populate = (bool) $input->getOption('populate');
		$switch = (bool) $input->getOption('switch');
		$clean = (bool) $input->getOption('clean');

		[$inputTypes, $inputMutationCodes] = $this->getInputs($input);
		$createdIds = [];
		try {
			foreach ($inputMutationCodes as $inputMutationCode) {
				try {
					$mutation = $this->mutationsHolder->getMutationByLangCode($inputMutationCode);
				} catch (\LogicException $e) {
					$output->writeln($e->getMessage());
					$output->writeln(sprintf('Skip \'%s\'', $inputMutationCode));
					continue;
				}

				$this->setMutation($mutation);

				foreach ($inputTypes as $inputType) {
					if ($inputType !== EsIndex::TYPE_ALL) {
						$esIndex = $this->esIndexModel->creteNewWithIndex($inputType, $mutation);

						$createdIds[] = $esIndex->id;
						if ($populate) {
							$this->esIndexFacade->fill(esIndex: $esIndex, autoSwitch: $switch);
						}
					}
				}
			}

			if (in_array(EsIndex::TYPE_ALL, $inputTypes)) {
				$defaultMutation = $this->orm->mutation->getRsDefault();

				$this->setMutation($defaultMutation);

				$esIndex = $this->esIndexModel->creteNewWithIndex(EsIndex::TYPE_ALL, $defaultMutation);

				$createdIds[] = $esIndex->id;
				if ($populate) {
					$this->esIndexFacade->fill(esIndex: $esIndex, autoSwitch: $switch);
				}
			}
		} catch (\Throwable $e) {
			Debugger::log($e, ILogger::EXCEPTION);
			throw $e;
		}

		if ($clean) {

			$conditionToClean = [
				'active' => 0,
			];
			if ($createdIds !== []) {
				$conditionToClean['id!='] = $createdIds;
			}

			foreach ($this->esIndexRepository->findBy($conditionToClean) as $esIndex) {
				$this->esIndexFacade->delete($esIndex);
			}
		}

		$output->writeLn('DONE');

		return $this->end(self::SUCCESS);
	}

	/**
	 * @param InputInterface $input
	 * @return array
	 */
	protected function getInputs(InputInterface $input): array
	{
		$rawInputType = is_array($input->getArgument('inputType')) ? $input->getArgument('inputType')[0] : $input->getArgument('inputType');
		$rawInputMutationCode = is_array($input->getArgument('inputMutationCode')) ? $input->getArgument('inputMutationCode')[0] : $input->getArgument('inputMutationCode');
		$inputType = Strings::lower($rawInputType);
		$inputMutationCode = Strings::lower($rawInputMutationCode);

		$possibleTypes = EsIndex::getConstsByPrefix('TYPE_');
		$possibleMutationCodes = Mutation::getConstsByPrefix('CODE_');

		if ($inputType === '*') {
			$inputType = $possibleTypes;
		} else {
			$inputType = array_filter(
				explode(',', $inputType),
				function (string $item) use ($possibleTypes) {
					return in_array($item, $possibleTypes);
				}
			);
		}

		if ($inputMutationCode === '*') {
			$inputMutationCode = $possibleMutationCodes;
		} else {
			$inputMutationCode = array_filter(
				explode(',', $inputMutationCode),
				function ($item) use ($possibleMutationCodes) {
					return in_array($item, $possibleMutationCodes);
				}
			);
		}

		return [$inputType, $inputMutationCode];
	}

	private function setMutation(Mutation $mutation): void
	{
		$this->orm->setMutation($mutation);
		$this->mutationHolder->setMutation($mutation);
	}

}
