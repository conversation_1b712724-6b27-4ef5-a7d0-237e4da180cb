<?php declare(strict_types = 1);

namespace App\Console;

use App\Utils\DateTime;
use Nette\Utils\FileSystem;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name:'test:second',
	description: 'test'
)]
// [AsPeriodicTask(frequency: '90 minute')]
// [AsCronTask('* * * * *', jitter: 20)]
// [AsPeriodicTask(frequency: '10 seconds', transports: 'cronCommands')]
final class TestSecondCommand extends BaseCommand
{

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->start($input);
		$datetime = new DateTime();
		FileSystem::createDir(LOG_DIR . '/SymfonyScheduler');
		$path = LOG_DIR . '/SymfonyScheduler/test-second_' . $datetime->format('Y-m-d') . '.log';
		file_put_contents($path, '@' . self::class . ' / ' . $datetime->format('H:i:s') . "\n", FILE_APPEND);
		sleep(2);
		dump($this::class . ' / ' . $datetime->format('H:i:s'));
		return 0;
	}

}
