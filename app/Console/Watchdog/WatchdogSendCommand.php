<?php declare(strict_types = 1);

namespace App\Console\Watchdog;

use App\Console\BaseCommand;
use App\Model\ElasticSearch\Product\Repository;
use App\Model\Mutation\MutationHolder;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Watchdog\WatchdogModel;
use App\Model\TranslatorDB;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name: 'watchdog:send',
	description: 'Send message from watchdog',
)]
// [AsCronTask(expression: '56 8-22/2 * * *', transports: 'cronCommands')]
class WatchdogSendCommand extends BaseCommand
{

	private Mutation $mutation;

	public function __construct(
		protected Orm $orm,
		private readonly MutationsHolder $mutationsHolder,
		private readonly MutationHolder $mutationHolder,
		private readonly Repository $esProductRepository,
		private readonly TranslatorDB $translator,
		private readonly WatchdogModel $watchdogModel,
	)
	{
		parent::__construct();
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->start($input);

		$this->mutation = $this->mutationsHolder->getDefault();
		$this->mutationHolder->setMutation($this->mutation);
		$this->orm->setMutation($this->mutation);
		$this->translator->setMutation($this->mutation);

		$productsToSend = $relatedProductsToSend = [];

		// Main products
		$productIdsToCheck = $this->orm->watchdog->findProductIds();
		$output->writeln('Checking ' . count($productIdsToCheck) . ' products');
		foreach ($this->esProductRepository->findOnStock($this->mutation, $productIdsToCheck, 1000) as $product) {
			if ($product->isStockedInLastTime()) {
				$productsToSend[] = $product->id;
			}
		}

		// Related products
		$relatedProductIdsToCheck = $this->orm->watchdog->findRelatedProductIds($productIdsToCheck);
		$output->writeln('Checking ' . count($relatedProductIdsToCheck) . ' related products');
		foreach ($this->esProductRepository->findOnStock($this->mutation, array_keys($relatedProductIdsToCheck), 1000) as $product) {
			$mainProductId = $relatedProductIdsToCheck[$product->id] ?? null;
			if ($product->isStockedInLastTime() && $mainProductId !== null && !in_array($mainProductId, $productsToSend)) {
				$relatedProductsToSend[$mainProductId] = $product->id;
			}
		}

		$sended = $this->sendWatchdog($productsToSend);
		$sendedRelated = $this->sendRelatedWatchdog($relatedProductsToSend);

		$output->writeln('Sended ' . $sended . ' products and ' . $sendedRelated . ' related products');
		$output->writeLn('DONE');
		return $this->end(self::SUCCESS);
	}

	private function sendWatchdog(array $productsToSend): int
	{
		$i = 0;
		foreach ($this->orm->watchdog->findBy(['product->id' => $productsToSend]) as $watchdog) {
			try {
				$this->watchdogModel->send($watchdog, $this->mutation);
				$this->orm->watchdog->remove($watchdog);
				$i++;
			} catch (\Throwable $e) {
				// do nothing
				dump($e->getMessage());
			}
		}

		$this->orm->watchdog->flush();
		return $i;
	}

	private function sendRelatedWatchdog(array $relatedProductsToSend): int
	{
		$i = 0;
		foreach ($this->orm->watchdog->findBy(['product->id' => array_keys($relatedProductsToSend)]) as $watchdog) {
			$relatedId = $relatedProductsToSend[$watchdog->product->id];
			if (!in_array($relatedId, $watchdog->relatedSended)) {
				try {
					$this->watchdogModel->sendRelated($watchdog, $this->mutation, $relatedId);

					$sended                  = $watchdog->relatedSended ?? [];
					$sended[]                = $relatedId;
					$watchdog->relatedSended = $sended;
					$this->orm->watchdog->persist($watchdog);
					$i++;
				} catch (\Throwable $e) {
					// do nothing
					dump($e->getMessage());
				}
			}

		}
		$this->orm->watchdog->flush();
		return $i;
	}

}
