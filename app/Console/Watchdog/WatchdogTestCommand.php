<?php declare(strict_types = 1);

namespace App\Console\Watchdog;

use App\Console\BaseCommand;
use App\Model\Mutation\MutationHolder;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Watchdog\WatchdogModel;
use App\Model\TranslatorDB;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name: 'watchdog:test',
	description: 'Send test message from watchdog by given e-mail address',
)]
class WatchdogTestCommand extends BaseCommand
{

	private Mutation $mutation;

	public function __construct(
		protected Orm $orm,
		private readonly MutationsHolder $mutationsHolder,
		private readonly MutationHolder $mutationHolder,
		private readonly TranslatorDB $translator,
		private readonly WatchdogModel $watchdogModel,
	)
	{
		parent::__construct();
	}

	protected function configure(): void
	{
		$this->addArgument('email', InputArgument::REQUIRED, 'E-mail address');
		//$this->addOption('type', '', InputOption::VALUE_OPTIONAL, 'related or default', 'default');
		parent::configure();
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->start($input);

		$this->mutation = $this->mutationsHolder->getDefault();
		$this->mutationHolder->setMutation($this->mutation);
		$this->orm->setMutation($this->mutation);
		$this->translator->setMutation($this->mutation);

		$watchdog = $this->orm->watchdog->getBy(['email' => $input->getArgument('email')]);

		try {
			$this->watchdogModel->send($watchdog, $this->mutation);
		} catch (\Throwable $e) {
			// do nothing
		}

		$output->writeLn('DONE');
		return $this->end(self::SUCCESS);
	}

}
