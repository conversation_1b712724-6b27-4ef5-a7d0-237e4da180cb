<?php declare(strict_types = 1);

namespace App\Console\Feeds;

use App\Console\BaseCommand;
use App\Model\ConfigService;
use App\Model\FeedGenerator\Individual\Factories\IndividualFactorySetup;
use App\Model\FeedGenerator\Individual\IndividualFeedGenerator;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name: 'feeds:generate:customFeed',
	description: 'Generate custom feed',
)]
// [AsCronTask('# #(3-4) * * *', arguments: 'cs', transports: 'cronCommands')]
class GenerateCustomFeedCommand extends BaseCommand
{

	public function __construct(
		private readonly IndividualFactorySetup $individualFactorySetup,
		protected readonly ConfigService $configService,
	)
	{
		parent::__construct();
	}

	protected function configure(): void
	{
		$this->addArgument('mutationCode', InputArgument::REQUIRED);
		$this->addArgument('outputFile', InputArgument::OPTIONAL);
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->setIsLockable(true, $this->configService->get('env'));
		$this->start($input);
		$output->writeLn('START');

		$mutationCode = $input->getArgument('mutationCode');
		$outputFile = $input->getArgument('outputFile');

		$individualFeedGenerator = new IndividualFeedGenerator($this->individualFactorySetup->create(), $mutationCode);

		$individualFeedGenerator->generateCustomFeed(WWW_DIR . '/exports/' . ($outputFile ?: 'customFeed') . '.xml');

		$output->writeLn('DONE');

		return $this->end(self::SUCCESS);
	}

}
