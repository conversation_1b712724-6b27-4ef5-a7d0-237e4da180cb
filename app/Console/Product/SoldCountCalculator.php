<?php declare(strict_types = 1);

namespace App\Console\Product;

use App\Console\BaseCommand;
use App\Model\ConfigService;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Orm;
use App\Model\Product\SoldCount\Dispatcher\DatabaseDispatcher;
use App\Model\Product\SoldCount\ProductLocalizationSoldCountCalculator;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name: 'product:soldCount:calculate',
	description: 'Calculate product localization\'s sold count based on orders',
)]
// [AsCronTask('# #(0-3) * * *', arguments: 'cs 100 0', transports: 'cronCommands')]
class SoldCountCalculator extends BaseCommand
{

	public function __construct(
		protected readonly Orm $orm,
		private readonly ProductLocalizationSoldCountCalculator $soldCountCalculator,
		private readonly MutationHolder $mutationHolder,
		private readonly DatabaseDispatcher $databaseDispatcher,
		protected readonly ConfigService $configService,
	)
	{
		parent::__construct();
	}

	protected function configure(): void
	{
		$this->addArgument('mutationCode', InputArgument::REQUIRED)
			->addArgument('batchSize', InputArgument::REQUIRED)
			->addArgument('limit', InputArgument::REQUIRED)
			->addArgument('onlyActiveProducts', InputArgument::OPTIONAL);
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->setIsLockable();
		$this->start($input);
		$mutationCode = $input->getArgument('mutationCode');
		$batchSize = (int) $input->getArgument('batchSize');
		$limit = (int) $input->getArgument('limit');
		$onlyActiveProducts = (bool) ($input->getArgument('onlyActiveProducts') ?? true);

		$this->databaseDispatcher->setMutationCode($mutationCode)->setBatchSize($batchSize)->setLimit($limit)->setOnlyActiveProducts($onlyActiveProducts);

		$mutation = $this->orm->mutation->getByCode($mutationCode);
		$this->orm->setMutation($mutation);
		$this->mutationHolder->setMutation($mutation);

		$daysToPast = (int) ($mutation->cf->mutationData->daysToPast ?? 31);
		$output->writeLn('Days to past: ' . $daysToPast);

		$this->soldCountCalculator->calculate($this->databaseDispatcher, $daysToPast);

		$output->writeLn('DONE');

		return $this->end(self::SUCCESS);
	}

}
