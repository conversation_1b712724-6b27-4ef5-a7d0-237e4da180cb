<?php declare(strict_types = 1);

namespace App\Console\Product;

use App\Console\BaseCommand;
use App\Model\ConfigService;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Orm;
use App\Model\Product\Score\Dispatcher\DatabaseDispatcher;
use App\Model\Product\Score\Score;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name: 'product:score:calculate',
	description: 'Calculate product\'s score based on directives',
)]
// [AsCronTask('# #(0-3) * * *', arguments: 'cs 100 0', transports: 'cronCommands')]
class ScoreCalculator extends BaseCommand
{

	public function __construct(
		protected readonly Orm $orm,
		private readonly Score $score,
		private readonly MutationHolder $mutationHolder,
		private readonly DatabaseDispatcher $databaseDispatcher,
		protected readonly ConfigService $configService,
	)
	{
		parent::__construct();
	}

	protected function configure(): void
	{
		$this->addArgument('mutationCode', InputArgument::REQUIRED)
			->addArgument('batchSize', InputArgument::REQUIRED)
			->addArgument('limit', InputArgument::REQUIRED)
			->addArgument('onlyActiveProducts', InputArgument::OPTIONAL);
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->setIsLockable();
		$this->start($input);
		$mutationCode = $input->getArgument('mutationCode');
		$batchSize = (int) $input->getArgument('batchSize');
		$limit = (int) $input->getArgument('limit');
		$onlyActiveProducts = (bool) ($input->getArgument('onlyActiveProducts') ?? true);

		$this->databaseDispatcher->setMutationCode($mutationCode)->setBatchSize($batchSize)->setLimit($limit)->setOnlyActiveProducts($onlyActiveProducts);

		$mutation = $this->orm->mutation->getByCode($mutationCode);
		$this->orm->setMutation($mutation);
		$this->mutationHolder->setMutation($mutation);

		$this->score->calculate($this->databaseDispatcher);

		$output->writeLn('DONE');

		return $this->end(self::SUCCESS);
	}

}
