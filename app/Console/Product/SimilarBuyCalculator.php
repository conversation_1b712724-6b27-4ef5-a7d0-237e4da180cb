<?php declare(strict_types = 1);

namespace App\Console\Product;

use App\Console\BaseCommand;
use App\Model\Product\SimilarBuy\Calculator;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name: 'product:similarBuy:calculate',
	description: 'Calculate product\'s score based on directives',
)]
class SimilarBuyCalculator extends BaseCommand
{

	public function __construct(
		private readonly Calculator $calculator,
	)
	{
		parent::__construct();
	}

	protected function configure(): void
	{
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->start($input);
		$this->calculator->calculate();
		$output->writeLn('DONE');
		return $this->end(self::SUCCESS);
	}

}
