<?php declare(strict_types = 1);

namespace App\Console\Product;

use App\Console\BaseCommand;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Mutation\MutationRepository;
use App\Model\Product\CategoryMainPositionCalculator;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name: 'product:category:position',
	description: 'Calculate product\'s position ic category',
)]
final class CategoryPositionCommand extends BaseCommand
{

	public function __construct(
		private readonly CategoryMainPositionCalculator $categoryMainPositionCalculator,
		private readonly MutationRepository $mutationRepository,
		private readonly MutationHolder $mutationHolder,
	)
	{
		parent::__construct();
	}


	protected function configure(): void
	{
		$this->addArgument('mutationCode', InputArgument::REQUIRED);
	}


	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->start($input);
		$mutationCode = $input->getArgument('mutationCode');
		$mutation = $this->mutationRepository->getByCode($mutationCode);

		$this->mutationHolder->setMutation($mutation);
		$output->writeLn('START');
		$this->categoryMainPositionCalculator->calculate();
		$output->writeLn('DONE');
		return $this->end(self::SUCCESS);
	}

}
