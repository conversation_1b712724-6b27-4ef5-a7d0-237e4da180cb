<?php declare(strict_types = 1);

namespace App\Console;

use App\Utils\DateTime;
use Nette\Utils\FileSystem;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Scheduler\Attribute\AsCronTask;

#[AsCommand(
	name:'test:test',
	description: 'test'
)]
#[AsCronTask('*/5 * * * *', transports: 'cronCommands')]
final class TestCommand extends BaseCommand
{

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->start($input);
		$datetime = new DateTime();
		FileSystem::createDir(LOG_DIR . '/SymfonyScheduler');
		$path = LOG_DIR . '/SymfonyScheduler/test-test_' . $datetime->format('Y-m-d') . '.log';
		file_put_contents($path, '@' . self::class . ' / ' . $datetime->format('H:i:s') . "\n", FILE_APPEND);
		sleep(5);
		dump($this::class . ' / ' . $datetime->format('H:i:s'));

		return $this->end(Command::SUCCESS);
	}

}
