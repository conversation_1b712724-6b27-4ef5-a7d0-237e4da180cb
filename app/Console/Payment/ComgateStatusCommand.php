<?php declare(strict_types = 1);

namespace App\Console\Payment;

use App\Console\BaseCommand;
use App\Model\Orm\CardPayment\CardPaymentProcessor;
use App\Model\Orm\CardPayment\CardPaymentStatus;
use App\Model\Orm\Orm;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name: 'payment:status:comgate',
	description: 'Check status of payments',
)]
final class ComgateStatusCommand extends BaseCommand
{

	public function __construct(
		protected readonly Orm $orm,
		private readonly CardPaymentProcessor $cardPaymentProcessor,
	)
	{
		parent::__construct();
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->start($input);
		foreach ($this->orm->cardPayment->findBy(['status!=' => [CardPaymentStatus::Authorized, CardPaymentStatus::Settled, CardPaymentStatus::Canceled]]) as $cardPayment) {
			$this->cardPaymentProcessor->processCheckPayment($cardPayment);
			$this->orm->persistAndFlush($cardPayment);
		}

		return $this->end(self::SUCCESS);
	}

}
