<?php declare(strict_types = 1);

namespace App\Console\Parameter;

use App\Console\BaseCommand;
use App\Model\Orm\Orm;
use App\Model\Orm\Parameter\ParameterModel;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name: 'parameter:sort',
	description: 'Calculate parameter sort by typeSort',
)]
class ParameterSortCommand extends BaseCommand
{

	public function __construct(
		protected readonly Orm $orm,
		private readonly ParameterModel $parameterModel,
	)
	{
		parent::__construct();
	}


	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->start($input);
		foreach ($this->orm->parameter->findBy(['typeSort!=' => null]) as $parameter) {
			$this->parameterModel->reindexSort($parameter);
		}

		$output->writeLn('DONE');

		return $this->end(self::SUCCESS);
	}

}
