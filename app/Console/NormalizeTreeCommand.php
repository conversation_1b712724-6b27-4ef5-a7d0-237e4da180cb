<?php declare(strict_types = 1);

namespace App\Console;

use Nextras\Dbal\Connection;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name:'site:normalize:tree',
	description: 'Normalize treeParents on tree'
)]
final class NormalizeTreeCommand extends BaseCommand
{

	public function __construct(
		private readonly Connection $connection,
	)
	{
		parent::__construct();
	}


	protected function configure(): void
	{
		$this->addOption('force', 'f', InputOption::VALUE_NONE, 'Run with FORCE to replace existing mutation');
	}


	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->start($input);
		$forced = $input->getOption('force');

		if ($forced === false) {
			$output->writeLn('FORCE option is not set. Use --force to run this command.');
			return $this->end(self::FAILURE);
		}

		$mutationsCount = $this->connection->query('SELECT count(id) as `count` FROM mutation')->fetchField();
		if ($mutationsCount > 1) {
			$output->writeLn('There are more than one mutation in the database.');
			return $this->end(self::FAILURE);
		}

		$this->connection->query('SET foreign_key_checks = 0');
		$this->connection->query('UPDATE tree SET treeParentId = NULL');
		$this->connection->query('TRUNCATE TABLE tree_parent');

		foreach ($this->connection->query('SELECT id FROM tree ORDER BY id') as $row) {
			$this->connection->query('INSERT INTO tree_parent () VALUES ()');
			$treeParentId = $this->connection->getLastInsertedId();
			$this->connection->query('UPDATE tree SET treeParentId = %i WHERE id = %i', $treeParentId, $row->id);
		}

		$this->connection->query('SET foreign_key_checks = 1');

		$output->writeLn('DONE');
		return $this->end(self::SUCCESS);
	}

}
