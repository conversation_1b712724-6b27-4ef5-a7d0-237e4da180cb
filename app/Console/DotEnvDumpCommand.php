<?php

declare(strict_types=1);

namespace App\Console;

use Nette\DI\Container;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name: 'env:dump',
)]
final class DotEnvDumpCommand extends BaseCommand
{

	public function __construct(
		private readonly Container $container,
	)
	{
		parent::__construct();
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->start($input);
		/** @var array $envParameters */
		$envParameters = $this->container->getParameter('env');

		$output->writeln('MESSENGER_CONSUMER_NAME');
		$output->writeln('getenv(): ' . getenv('MESSENGER_CONSUMER_NAME'));
		$output->writeln('DIC parameter: ' . $envParameters['MESSENGER_CONSUMER_NAME'] ?? '--unset--'); // @phpstan-ignore-line

		return $this->end(self::SUCCESS);
	}

}
