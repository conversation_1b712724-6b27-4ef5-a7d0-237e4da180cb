<?php declare(strict_types = 1);

namespace App\Console;

use App\Model\Cloner\SiteCloner;
use App\Model\Orm\Mutation\MutationRepository;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Exception\InvalidArgumentException;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name:'site:create',
	description: 'Create new mutation & copy pages & product'
)]
final class CreateNewMutationCommand extends BaseCommand
{

	public function __construct(
		private readonly SiteCloner $siteCloner,
		private readonly MutationRepository $mutationRepository,
	)
	{
		parent::__construct();
	}


	protected function configure(): void
	{
		$this->addOption('force', 'f', InputOption::VALUE_NONE, 'Run with FORCE to replace existing mutation')
			->addArgument('newMutationLangCode', InputArgument::REQUIRED)
			->addArgument('sourceMutationLangCode', InputArgument::REQUIRED);
	}


	protected function execute(InputInterface $input, OutputInterface $output): int
	{
		$this->start($input);
		$forced = $input->getOption('force');
		$rawNewMutationLangCode = is_array($input->getArgument('newMutationLangCode')) ? $input->getArgument('newMutationLangCode')[0] : $input->getArgument('newMutationLangCode');
		$rawSourceMutationLangCode = is_array($input->getArgument('sourceMutationLangCode')) ? $input->getArgument('sourceMutationLangCode')[0] : $input->getArgument('sourceMutationLangCode');
		$newMutationLangCode = mb_strtolower($rawNewMutationLangCode ?? '');
		$sourceMutationLangCode = mb_strtolower($rawSourceMutationLangCode ?? '');

		if (!preg_match('/[a-z]{2}/', $newMutationLangCode)) {
			throw new InvalidArgumentException('Parameter \'newMutationLangCode\' must be in [a-z]{2} format');
		}

		if (!preg_match('/[a-z]{2}/', $sourceMutationLangCode)) {
			throw new InvalidArgumentException('Parameter \'sourceMutationLangCode\' must be in [a-z]{2} format');
		}

		$sourceMutation = $this->mutationRepository->getByChecked(['langCode' => $sourceMutationLangCode]);

		if ($forced) {
			$this->siteCloner->replace($newMutationLangCode, $sourceMutation);
		} else {
			$this->siteCloner->create($newMutationLangCode, $sourceMutation);
		}

		$output->writeLn('DONE');
		return $this->end(self::SUCCESS);
	}

}
