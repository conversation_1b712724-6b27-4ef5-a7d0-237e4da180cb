<?php declare(strict_types = 1);

namespace App\Utils\DateTime;

use Nextras\Dbal\Utils\DateTimeImmutable;

final class DateTimeRounding
{

	private function __construct()
	{
	}

	public static function floorMilliseconds(DateTimeImmutable $dateTime): DateTimeImmutable
	{
		return $dateTime->setTime(
			(int) $dateTime->format('H'),
			(int) $dateTime->format('i'),
			(int) $dateTime->format('s'),
		);
	}

}
