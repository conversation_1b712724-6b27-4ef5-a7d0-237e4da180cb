<?php declare(strict_types=1);

namespace App\Utils\QrCodePayment;

use R<PERSON><PERSON><PERSON>\CzQrPayment\QrPayment as CzQrPayment;
use rikudou\SkQrPayment\QrPayment as SkQrPayment;
use Rikudou\Iban\Iban\IBAN;
use rikudou\SkQrPayment\Iban\IbanBicPair;

class QrCodePayment
{
	static function createCzPayment(string $iban): CzQrPayment
	{
		return new CzQrPayment(new IBAN($iban));
	}

	static function createSkPayment(string $iban, string $swift): SkQrPayment
	{
		return new SkQrPayment(new IbanBicPair($iban, $swift));
	}

}
