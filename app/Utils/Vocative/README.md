# Vocative Service for Czech and Slovak Names

This service provides functionality to convert Czech and Slovak names to vocative case (5th case).

> **Important:** When addressing users by their first name, it's recommended to also provide the surname for accurate gender detection and proper vocative form. This is especially important for names that could be used for both genders or have unusual forms.

## Installation

### Within This Project

The service is already registered in the application's DI container.

### In Other Projects

To use this service in another project, you need to:

1. Copy the following files to your project:
   - `VocationInterface.php`
   - `VocativeService.php`
   - `NameVocative.php`

2. Register the service in your DI container:

```php
// In your config file or DI container setup
services:
    vocativeService: App\Utils\Vocative\VocativeService(@stringRepository, @cacheStorage)
```

## Dependencies

The service has the following dependencies:

1. **StringRepository** - A repository for storing and retrieving custom vocative rules. It should implement a method `getByName(Mutation $mutation, string $name)` that returns an object with a `value` property containing the custom vocative form.

2. **Cache Storage** - A cache storage implementation compatible with Nette\Caching\Cache. This is used to cache vocative forms for better performance.

3. **Mutation** - An object representing the language mutation (e.g., Czech, Slovak). It should have a `langCode` property containing the language code (e.g., 'cs', 'sk').

If you don't have these dependencies in your project, you can modify the service to work without them:

- Remove the StringRepository dependency if you don't need custom rules
- Remove the Cache dependency if you don't need caching
- Modify the Mutation parameter to accept a simple string language code instead

## Code Structure

### VocationInterface

An interface that defines constants for gender determination:

```php
interface VocationInterface
{
    const string GENDER_WOMAN = 'woman';
    const string GENDER_MAN = 'man';
    const string GENDER_AUTO = 'auto';
}
```

### NameVocative

A core class that implements the vocative transformation logic. This class can be used independently without the other dependencies:

```php
// Using NameVocative directly
$nameVocative = new \App\Utils\Vocative\NameVocative();

// Convert a name to vocative case
$vocative = $nameVocative->vocative('Petr', VocationInterface::GENDER_MAN); // Returns "Petře"

// Determine gender of a name
$gender = $nameVocative->getGender('Nováková', true); // Returns VocationInterface::GENDER_WOMAN
```

### VocativeService

A service that wraps the `NameVocative` class and adds caching and custom rules from the StringRepository.

## Usage

### Basic Usage

```php
// In a presenter or another service
public function __construct(
    private VocativeService $vocativeService,
    private MutationHolder $mutationHolder
) {
}

public function someAction(): void
{
    $currentMutation = $this->mutationHolder->getCurrentMutation();

    // First name only
    $vocativeName = $this->vocativeService->vocative('Petr', $currentMutation); // Returns "Petře"

    // First name with last name for better gender detection
    $vocativeName = $this->vocativeService->vocative('Petr', $currentMutation, 'Novák'); // Returns "Petře"

    // Last name only
    $lastNameVocative = $this->vocativeService->vocativeLastName('Novák', $currentMutation); // Returns "Nováku"

    // Full name (first name + last name)
    $fullNameVocative = $this->vocativeService->vocativeFullName('Petr', 'Novák', $currentMutation); // Returns "Petře Nováku"

    // For ambiguous names, providing the last name helps with gender detection
    $vocativeName = $this->vocativeService->vocative('Alex', $currentMutation, 'Nováková'); // Correctly identifies as female
    $vocativeName = $this->vocativeService->vocative('Alex', $currentMutation, 'Novák'); // Correctly identifies as male
}
```

### Advanced Usage

When the automatic gender detection doesn't work correctly, or you want to ensure proper vocative forms, you can explicitly specify gender using constants:

```php
// For female first names (explicitly specify gender)
$vocativeName = $this->vocativeService->vocative('Marie', $mutation, null, VocationInterface::GENDER_WOMAN); // Returns "Marie"

// For male first names (explicitly specify gender)
$vocativeName = $this->vocativeService->vocative('Jan', $mutation, null, VocationInterface::GENDER_MAN); // Returns "Jane"

// For female last names (explicitly specify gender)
$lastNameVocative = $this->vocativeService->vocativeLastName('Nováková', $mutation, VocationInterface::GENDER_WOMAN); // Returns "Nováková"

// For male last names (explicitly specify gender)
$lastNameVocative = $this->vocativeService->vocativeLastName('Novák', $mutation, VocationInterface::GENDER_MAN); // Returns "Nováku"

// For full names (explicitly specify gender)
$fullNameVocative = $this->vocativeService->vocativeFullName('Marie', 'Nováková', $mutation, VocationInterface::GENDER_WOMAN); // Returns "Marie Nováková"
```

### Custom Rules

The service primarily looks for vocative forms in the StringRepository. For first names, it uses the prefix `vocative_` and for last names, it uses the prefix `vocative_lastname_`.

To add custom rules for specific names, you can add entries directly to the StringRepository:

```php
// For first names
$stringRepository->setTranslation($mutation, 'vocative_štefan', 'Štefane');

// For last names
$stringRepository->setTranslation($mutation, 'vocative_lastname_havel', 'Havle');
```

This allows you to handle special cases or foreign names that don't follow standard Czech or Slovak vocative rules.

## Supported Languages

- Czech (cs)
- Slovak (sk)

For other languages, the service returns the original name without any transformation. This is useful for multilingual applications where you want to use vocative forms only for Czech and Slovak names.

## Implementation Details

The service works in the following order:

1. **Check cache** - First, it checks if the vocative form is already in the cache.

2. **Check StringRepository** - If not in cache, it looks for a custom rule in the StringRepository:
   - For first names: using the key `vocative_firstname` (e.g., `vocative_petr`)
   - For last names: using the key `vocative_lastname_lastname` (e.g., `vocative_lastname_novak`)

3. **Apply rules** - If no custom rule is found, it applies Czech and Slovak vocative rules based on name endings and special cases.

4. **Cache result** - Finally, it caches the result for better performance.

The service uses a combination of rules and special cases to determine the correct vocative form when no custom rule is found in the StringRepository.

## Best Practices

1. **Always use full names when possible**: Providing both first name and surname helps with accurate gender detection.

2. **Handle ambiguous names carefully**: Some names can be used for both genders (e.g., Alex, Nikola). In these cases, explicitly specify the gender parameter using constants `VocationInterface::GENDER_MAN` or `VocationInterface::GENDER_WOMAN`.

3. **Consider cultural differences**: Some foreign names may not follow Czech or Slovak vocative rules. Use custom rules for these cases.

4. **Test with real data**: Before deploying to production, test the service with a variety of real names to ensure proper vocative forms.
