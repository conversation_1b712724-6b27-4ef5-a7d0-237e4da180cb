<?php declare(strict_types = 1);

namespace App\Utils\Vocative;

/**
 * Class for handling Czech and Slovak name vocative forms
 */
class NameVocative implements VocativeInterface
{

	/**
	 * Converts a name to vocative case
	 *
	 * @param string $name Name in nominative case
	 * @param string $gender Gender (GENDER_MAN, GENDER_WOMAN, GENDER_AUTO)
	 * @param bool|null $isLastName Whether the name is a surname (null = auto-detection)
	 * @return string Name in vocative case
	 */
    public function vocative(string $name, string $gender = self::GENDER_AUTO, ?bool $isLastName = null): string
    {
        // Keep original name for later use
        $originalName = $name;

        // Convert to lowercase for processing
        $key = mb_strtolower($name, 'UTF-8');

        // Auto-detect gender if not specified
        if ($gender === self::GENDER_AUTO) {
            $gender = $this->getGender($key);
        }

        if ($gender === self::GENDER_WOMAN) {
            // Auto-detect if it's a last name
            if ($isLastName === null) {
                $isLastName = $this->isWomanLastName($key);
            }

            if ($isLastName) {
                return $this->vocativeWomanLastName($originalName);
            }

            return $this->vocativeWoman($originalName);
        }

        return $this->vocativeMan($originalName, $key);
    }

    /**
     * Determines the gender of a name based on common patterns
     *
     * @param string $name Name in nominative case
     * @param bool $isLastName Whether the name is a surname
     * @return string Returns GENDER_MAN or GENDER_WOMAN
     */
    public function getGender(string $name, bool $isLastName = false): string
    {
        if (empty($name) || mb_strlen($name) < 2) {
            return self::GENDER_MAN;
        }

		if ($isLastName) {
            return $this->isWomanLastName($name) ? self::GENDER_WOMAN : self::GENDER_MAN;
        }

        $name = mb_strtolower($name, 'UTF-8');

        // Common female name endings
        $femaleEndings = ['a', 'á', 'e', 'ie', 'ová', 'ská', 'cká'];
        foreach ($femaleEndings as $ending) {
            if (mb_substr($name, -mb_strlen($ending)) === $ending) {
                return self::GENDER_WOMAN;
            }
        }

        // Default to male
        return self::GENDER_MAN;
    }

    /**
     * Determines if a female name is likely a surname
     *
     * @param string $name Name in lowercase
     * @return bool Whether the name is likely a surname
     */
    private function isWomanLastName(string $name): bool
    {
        $lastNameEndings = ['ová', 'ská', 'cká', 'ská', 'zká', 'ná', 'lá'];
        foreach ($lastNameEndings as $ending) {
            if (mb_substr($name, -mb_strlen($ending)) === $ending) {
                return true;
            }
        }

        return false;
    }

    /**
     * Converts a male name to vocative case (first or last name)
     *
     * @param string $name Original name
     * @param string $key Lowercase name
     * @return string Name in vocative case
     */
    private function vocativeMan(string $name, string $key): string
    {
        // Common male name vocative transformations
        $rules = [
            'el' => 'le',
            'ek' => 'ku',
            'ec' => 'če',
            'er' => 're',
            'a' => 'o',
            'o' => 'o',
            'ch' => 'chu',
            'k' => 'ku',
            'g' => 'gu',
            'h' => 'hu',
            'r' => 're',
            'x' => 'xi',
            'c' => 'ci',
            'd' => 'de',
            't' => 'te',
            'n' => 'ne',
            'b' => 'be',
            'f' => 'fe',
            'l' => 'le',
            'm' => 'me',
            'p' => 'pe',
            's' => 'se',
            'v' => 've',
            'z' => 'ze',
            'j' => 'ji',
            'š' => 'ši',
            'č' => 'či',
            'ř' => 'ři',
            'ž' => 'ži',
            'ě' => 'i',
            'í' => 'í',
            'i' => 'i',
            'y' => 'y',
            'ý' => 'ý',
            'ů' => 'ů',
            'u' => 'u',
            'ú' => 'ú',
            'á' => 'á',
            'é' => 'é',
            'ň' => 'ni',
            'ď' => 'di',
            'ť' => 'ti'
        ];

        // Special cases - includes both Czech and Slovak names
        $specialCases = [
            // Czech names
            'jan' => 'jane',
            'tomáš' => 'tomáši',
            'lukáš' => 'lukáši',
            'matěj' => 'matěji',
            'petr' => 'petře',
            'pavel' => 'pavle',
            'josef' => 'josefe',
            'jiří' => 'jiří',
            'martin' => 'martine',
            'michal' => 'michale',
            'jakub' => 'jakube',
            'david' => 'davide',
            'ondřej' => 'ondřeji',
            'adam' => 'adame',
            'marek' => 'marku',
            'milan' => 'milane',
            'daniel' => 'danieli',
            'karel' => 'karle',
            'jaroslav' => 'jaroslave',

            // Slovak names
            'jozef' => 'jozefe',
            'peter' => 'petre',
            'ján' => 'jáne',
            'juraj' => 'juraji',
            'dušan' => 'dušane',
            'miroslav' => 'miroslave',
            'vladimír' => 'vladimíre',
            'igor' => 'igore',
            'andrej' => 'andreji',
            'patrik' => 'patriku',
            'roman' => 'romane'
        ];

        // Check for special cases first
        if (isset($specialCases[$key])) {
            return mb_convert_case($specialCases[$key], MB_CASE_TITLE, 'UTF-8');
        }

        // Apply rules based on endings
        foreach ($rules as $suffix => $replacement) {
            if (mb_substr($key, -mb_strlen($suffix)) === $suffix) {
                $nameWithoutSuffix = mb_substr($name, 0, -mb_strlen($suffix));
                return $nameWithoutSuffix . $replacement;
            }
        }

        // Default case - add 'e'
        return $name . 'e';
    }

    /**
     * Converts a female first name to vocative case
     *
     * @param string $name Original name
     * @return string Name in vocative case
     */
    private function vocativeWoman(string $name): string
    {
        // For female first names ending with 'a', replace with 'o'
        if (mb_substr($name, -1) === 'a') {
            return mb_substr($name, 0, -1) . 'o';
        }

        // Other female names typically don't change in vocative
        return $name;
    }

    /**
     * Converts a female surname to vocative case
     *
     * @param string $name Original name
     * @return string Name in vocative case
     */
    private function vocativeWomanLastName(string $name): string
    {
        // Female surnames typically don't change in vocative
        return $name;
    }
}
