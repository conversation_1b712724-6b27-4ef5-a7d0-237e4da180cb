<?php
declare(strict_types=1);

namespace App;

use App\Infrastructure\Debugger\LazyValueExposer;
use App\Infrastructure\Debugger\OrmExposer;
use Nette\Bootstrap\Configurator;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\IEntity;
use App\Model\CustomField\LazyValue;
use Tracy;

class Bootstrap
{
	public static function boot(): Configurator
	{
		//FIX PRO CURL
		if (!defined('STDERR')) {
			define('STDERR', NULL);
		}

		if (PHP_VERSION_ID < 80400) { // @phpstan-ignore-line
			trigger_error('Old PHP version', E_USER_NOTICE);
			die;
		}


		define('ROOT_DIR', dirname(__DIR__));

		if (file_exists(ROOT_DIR . '/www/index.php')) {
			define('WWW_DIR', ROOT_DIR . '/www');
		} else {
			define('WWW_DIR', ROOT_DIR . '/web');
		}
		define('APP_DIR', __DIR__);
		define('FE_TEMPLATE_DIR', APP_DIR . '/FrontModule/templates');
		define('RS_TEMPLATE_DIR', APP_DIR . '/AdminModule/templates');
		define('IMAGES_DIR', '/data/images/');
		define('FILES_DIR', '/data/files/');
		define('TEMP_DIR', __DIR__ . '/../temp');
		define('CACHE_DIR', __DIR__ . '/../temp/cache');
		define('LOG_DIR', ROOT_DIR . '/nettelog');

		$configurator = new Configurator;

		$configurator->setTimeZone('Europe/Prague');
		$configurator->setTempDirectory(TEMP_DIR);

		$isCLI = (php_sapi_name() === 'cli');
		if (isset($_COOKIE['DEBUG_MODE'])) {
			define('IS_CLI', false);
			$debugMode = (bool)$_COOKIE['DEBUG_MODE'];
		} elseif ($isCLI) {
			define('IS_CLI', true);
			$debugMode = false;
			if (file_exists(ROOT_DIR . '/debuger-enforcer-in-cli')) {
				$debugMode = true;
			}
			echo 'Debug mode: ' . ($debugMode ? 'On' : 'Off') . "\n"; // show debug mode status on each cmd execution
		} else {
			define('IS_CLI', false);
			$debugMode = FALSE;
		}

		if ($debugMode) {
			ini_set('assert.exception', '1');
		}
		$configurator->setDebugMode($debugMode);

		// musi byt tu
		$configurator->enableTracy(LOG_DIR);

		error_reporting(~E_USER_DEPRECATED);

		$configurator->addStaticParameters([
			'config' => [
				'APP_DIR' => APP_DIR,
				'IS_CLI' => IS_CLI,
				'FE_TEMPLATE_DIR' => FE_TEMPLATE_DIR,
				'RS_TEMPLATE_DIR' => RS_TEMPLATE_DIR,
				'IMAGES_DIR' => IMAGES_DIR,
				'FILES_DIR' => FILES_DIR,
				'TEMP_DIR' => TEMP_DIR,
				'CACHE_DIR' => CACHE_DIR,
				'WWW_DIR' => WWW_DIR,
				'ROOT_DIR' => ROOT_DIR,
			],
		]);

		// set default messenger consumer name
		if (getenv('MESSENGER_CONSUMER_NAME') === false) {
			putenv('MESSENGER_CONSUMER_NAME=00');
		}
		// add dynamic parameters $_ENV
		$configurator->addDynamicParameters([
			'env' => getenv()
		]);

		$configurator->addConfig(APP_DIR . '/config/config.neon');
		if (file_exists(APP_DIR . '/config/config.local.neon')) {
			$configurator->addConfig(APP_DIR . '/config/config.local.neon'); // none section
		}

		$configurator->addServices(['configurator' => $configurator]);

		//Tracy\OutputDebugger::enable(); // odkomentovat pri ladeni - odhali kde je vypis
		Tracy\Debugger::$errorTemplate = APP_DIR.'/FrontModule/Presenters/Error/templates/500.html'; // vlastni staticka sablona misto Nette default /vendor/tracy/tracy/src/Tracy/Debugger/assets/error.500.phtml
//		Tracy\Dumper::$objectExporters[LazyValue::class] = LazyValueExposer::exposeLazyValue(...);
//		Tracy\Dumper::$objectExporters[IEntity::class] = OrmExposer::exposeEntity(...);
//		Tracy\Dumper::$objectExporters[ICollection::class] = OrmExposer::exposeCollection(...);
//		Tracy\Dumper::$objectExporters[IRelationshipCollection::class] = OrmExposer::exposeRelationshipCollection(...);

		return $configurator;
	}
}
