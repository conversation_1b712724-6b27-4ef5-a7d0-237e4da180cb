<?php declare(strict_types = 1);

namespace App\Api\Validator;

use Symfony\Component\Validator\Constraint;

/**
 * @Annotation
 */
class ConstraintMutation extends Constraint
{

	public string $message = 'Invalid mutation "{{ string }}"';

	public string $mode = 'strict'; // If the constraint has configuration options, define them as public properties

	public function validatedBy(): string
	{
		return static::class . 'Validator';
	}

}
