<?php declare(strict_types = 1);

namespace App\Api\Validator;

use App\Model\Orm\Orm;
use Nextras\Orm\Exception\NoResultException;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;
use Symfony\Component\Validator\Exception\UnexpectedValueException;

class ConstraintMutationValidator extends ConstraintValidator
{

	public function __construct(
		private Orm $orm,
	) {}

	public function validate(mixed $value, Constraint $constraint): void
	{
		if (!$constraint instanceof ConstraintMutation) {
			throw new UnexpectedTypeException($constraint, ConstraintMutation::class);
		}

		// custom constraints should ignore null and empty values to allow
		// other constraints (NotBlank, NotNull, etc.) to take care of that
		if ($value === null || $value === '') {
			return;
		}

		if (!is_string($value)) {
			// throw this exception if your validator cannot handle the passed type so that it can be marked as invalid
			throw new UnexpectedValueException($value, 'string');

			// separate multiple types using pipes
			// throw new UnexpectedValueException($value, 'string|int');
		}

		// access your configuration options like this:
		//if ($constraint->mode === 'strict') {
			// ...
		//}

		try {
			$this->orm->mutation->getByCode($value);
		} catch (NoResultException) {
			$this->context->buildViolation($constraint->message)
				->setParameter('{{ string }}', $value)
				->addViolation();
		}
	}

}
