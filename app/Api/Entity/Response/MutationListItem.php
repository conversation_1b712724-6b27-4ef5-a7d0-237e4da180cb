<?php declare(strict_types = 1);

namespace App\Api\Entity\Response;

use Apitte\Core\Mapping\Response\BasicEntity;
use App\Model\Orm\Mutation\Mutation;

class MutationListItem extends BasicEntity
{

	public readonly int $id;

	public readonly ?string $name;

	public readonly string $langCode;

	public function __construct(Mutation $mutation)
	{
		$this->id = $mutation->id;
		$this->name = $mutation->name;
		$this->langCode = $mutation->langCode;
	}

}
