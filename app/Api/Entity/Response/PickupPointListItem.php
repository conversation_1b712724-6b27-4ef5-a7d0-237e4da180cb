<?php declare(strict_types = 1);

namespace App\Api\Entity\Response;

use Apitte\Core\Mapping\Response\BasicEntity;
use Nette\Utils\Json;
use Nextras\Dbal\Result\Row;


class PickupPointListItem extends BasicEntity
{

	public readonly int $id;
	public readonly int $typeId;
	public readonly string $name;
	public readonly string $address;
	public readonly ?float $price;
	public readonly array $position;
	public readonly array $openingHours;
	public readonly ?string $image;

	public function __construct(Row $pickupPoint)
	{
		$this->id    = $pickupPoint->id;
		$this->typeId = $pickupPoint->deliveryMethodId;
		$this->name  = $pickupPoint->name;
		$this->address = $pickupPoint->address;
		$this->price = $pickupPoint->price;
		$this->position = ['lat' => (float) $pickupPoint->lat, 'lng' => (float) $pickupPoint->lng];
		$this->openingHours = Json::decode($pickupPoint->openingHours, forceArrays: true);
		$this->image = $pickupPoint->image;
	}

}
