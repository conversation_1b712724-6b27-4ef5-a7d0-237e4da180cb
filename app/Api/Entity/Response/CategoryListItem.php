<?php declare(strict_types = 1);

namespace App\Api\Entity\Response;

use Apitte\Core\Mapping\Response\BasicEntity;
use App\Model\CustomField\LazyValue;
use App\PostType\Page\Model\Orm\Tree;


class CategoryListItem extends BasicEntity
{
	public readonly ?string $erp_id;
	public readonly ?string $erp_parent_id;
	public readonly int $id;
	public readonly int $parent_id;
	public readonly string $name;
	public readonly bool $active;
	public readonly int $position;
	public readonly string $path;

	public readonly ?string $zbozi_id;
	public readonly ?string $heureka_id;
	public readonly ?string $google_id;

	public function __construct(Tree $category)
	{
		$this->erp_id = $category->extId;
		$this->erp_parent_id = $category->parent->extId;
		$this->id    = $category->id;
		$this->parent_id = $category->parentId;
		$this->name = $category->getName();
		$this->active = $category->isPublic();
		$this->position = $category->sort;

		$path = [];
		/** @var Tree $item */
		foreach ($category->pathItems as $item) {
			if ($item->type !== Tree::TYPE_CATALOG) {
				continue;
			}
			$path[] = $item->getName();
		}

		$this->path = implode(' > ', $path);
		$this->zbozi_id = $this->heureka_id = $this->google_id = null;
	}

}
