<?php declare(strict_types = 1);

namespace App\Api\Entity\Response;

use Apitte\Core\Mapping\Response\BasicEntity;
use Nette\Utils\Json;
use Nextras\Dbal\Result\Row;


class PickupPointListMarkerItem extends BasicEntity
{

	public readonly int $id;
	public readonly int $typeId;
	public readonly array $position;

	public function __construct(Row $pickupPoint)
	{
		$this->id    = $pickupPoint->id;
		$this->typeId = $pickupPoint->deliveryMethodId;
		$this->position = ['lat' => (float) $pickupPoint->lat, 'lng' => (float) $pickupPoint->lng];
	}

}
