<?php declare(strict_types = 1);

namespace App\Api\Decorator;

use Apitte\Core\Decorator\IRequestDecorator;
use Apitte\Core\Exception\Runtime\EarlyReturnResponseException;
use Apitte\Core\Http\ApiRequest;
use Apitte\Core\Http\ApiResponse;
use Apitte\Core\Http\RequestAttributes;
use Apitte\Core\Schema\Endpoint;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Mutation\MutationRepository;
use App\Model\Orm\Orm;
use GuzzleHttp\Psr7\Utils;

final class MutationFinder implements IRequestDecorator
{

	public const MUTATION_ID_PARAMETER_NAME = 'mutationId';
	public const MUTATION_PARAMETER_NAME = 'mutation';


	public function __construct(
		private readonly MutationRepository $mutationRepository,
		private readonly MutationHolder $mutationHolder,
	)
	{
	}

	/**
	 * @throws EarlyReturnResponseException If other request decorators and also deeper layers (endpoint) should be skipped
	 */
	public function decorateRequest(ApiRequest $request, ApiResponse $response): ApiRequest
	{
		/** @var Endpoint $endpoint Schema of matched endpoint */
		$endpoint = $request->getAttribute(RequestAttributes::ATTR_ENDPOINT);

		if ( ! $endpoint->hasParameter(self::MUTATION_ID_PARAMETER_NAME)) {
			return $request;
		}

		$mutationId = $request->getParameter(self::MUTATION_ID_PARAMETER_NAME);
		$mutation = $this->mutationRepository->getById($mutationId);

		if ($mutation === null) {
			$body = Utils::streamFor(json_encode([
				'status' => 'error',
				'code' => ApiResponse::S404_NOT_FOUND,
				'message' => 'Mutation not found',
			]));

			$response = $response
				->withStatus(ApiResponse::S404_NOT_FOUND)
				->withBody($body);

			throw new EarlyReturnResponseException($response);
		}

		$orm = $this->mutationRepository->getModel();
		assert($orm instanceof Orm);
		$orm->setMutation($mutation);
		$this->mutationHolder->setMutation($mutation);

		$requestAttributes = $request->getAttribute(RequestAttributes::ATTR_PARAMETERS);
		$requestAttributes[self::MUTATION_PARAMETER_NAME] = $mutation;

		return $request->withAttribute(RequestAttributes::ATTR_PARAMETERS, $requestAttributes);
	}

}
