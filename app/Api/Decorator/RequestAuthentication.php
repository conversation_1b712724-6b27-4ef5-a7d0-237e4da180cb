<?php declare(strict_types = 1);

namespace App\Api\Decorator;

use Apitte\Core\Decorator\IRequestDecorator;
use Apitte\Core\Exception\Runtime\EarlyReturnResponseException;
use Apitte\Core\Http\ApiRequest;
use Apitte\Core\Http\ApiResponse;
use Apitte\Core\Http\RequestAttributes;
use Apitte\Core\Schema\Endpoint;
use App\Model\Orm\ApiToken\ApiToken;
use App\Model\Orm\Orm;
use Nette\Utils\Strings;

final class RequestAuthentication implements IRequestDecorator
{

	public const API_TOKEN_ATTRIBUTE_NAME = 'apiToken';

	public function __construct(
		private readonly Orm $orm,
	)
	{
	}

	/**
	 * @throws EarlyReturnResponseException If other request decorators and also deeper layers (endpoint) should be skipped
	 */
	public function decorateRequest(ApiRequest $request, ApiResponse $response): ApiRequest
	{
		/** @var Endpoint $endpoint Schema of matched endpoint */
		$endpoint = $request->getAttribute(RequestAttributes::ATTR_ENDPOINT);

		if ($endpoint->hasTag('noAuthentication')) {
			return $request;
		}

		$authorizationHeader = $request->getHeader('Authorization')[0] ?? null;
		if ($authorizationHeader === null) {
			$this->reject($response, ApiResponse::S401_UNAUTHORIZED);
		}

		$matches = Strings::match($authorizationHeader, '/^bearer ([0-9a-z-]+)$/i');
		if ($matches === null) {
			$this->reject($response, ApiResponse::S401_UNAUTHORIZED);
		}

		/** @var ApiToken|null $apiToken */
		$apiToken = $this->orm->apiToken->getBy(['token' => $matches[1]]);
		if ($apiToken === null || ! $apiToken->isValid()) {
			$this->reject($response, ApiResponse::S403_FORBIDDEN);
		}

		return $request->withAttribute(self::API_TOKEN_ATTRIBUTE_NAME, $apiToken);
	}

	private function reject(ApiResponse $response, int $statusCode): never
	{
		$message = match ($statusCode) {
			ApiResponse::S401_UNAUTHORIZED => 'Unauthorized',
			ApiResponse::S403_FORBIDDEN => 'Invalid credentials, authentication failed.',
			default => 'Unknown error',
		};
		$response = $response->withStatus($statusCode)->writeJsonBody([
			'status' => 'error',
			'code' => $statusCode,
			'message' => $message,
		]);
		throw new EarlyReturnResponseException($response);
	}

}
