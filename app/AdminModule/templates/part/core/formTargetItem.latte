<div data-Templates-target="{$props['itemTargetName']}">
	{include $templates.'/part/box/list-item.latte',
	props: [
		data: [
			controller: 'RemoveItem SuggestInp',
			removeitem-target: 'item',
			suggestinp-target: 'wrapper',
			suggestinp-url-value: Nette\Utils\Json::encode($props['listSearchUrl'])
	],
		inps: [
			[
				placeholder: $props['listPlaceholder'],
				input: $props['formContainer']['name'],
				data: [
					suggestinp-target: 'input',

				]
			],
			[
				input: $props['formContainer']['id'],
				data: [
					suggestinp-target: 'idInput',
				],
				classes: 'u-hide',
				type: 'hidden'
			]
		],
		btnsAfter: [
			[
				icon: $templates.'/part/icons/times.svg',
				tooltip: 'Vymazat',
				variant: 'clear',
				data: [
					action: 'SuggestInp#clear'
				]
			],
			[
				icon: $templates.'/part/icons/trash.svg',
				tooltip: 'Odstranit',
				variant: 'remove',
				data: [
					action: 'RemoveItem#remove'
				]
			]
		],
		dragdrop: false
	]
	}
</div>
