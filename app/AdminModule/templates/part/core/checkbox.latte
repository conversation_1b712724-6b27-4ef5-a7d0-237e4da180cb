{var $props = [
	id: $props['id'] ?? '',
	name: $props['name'] ?? '',
	label: $props['label'] ?? '',
	input: $props['input'] ?? '',
	tooltip: $props['tooltip'] ?? '',
	icon: $props['icon'] ?? '',
	checked: $props['checked'] ?? false,
	variant: (isset($props['variant']) && in_array($props['variant'], ['checkbox', 'radio'])) ?  $props['variant'] : 'checkbox',
	classes: $props['classes'] ?? ['u-mb-sm'],
	data: $props['data'] ?? [],
	dataInp: $props['dataInp'] ?? [],
	classesInp: $props['classesInp'] ?? [],
	disabled: $props['disabled'] ?? false,
	formId: $props['formId'] ?? false,
]}

{var $baseClasses = ['inp-item']}
{if $props['variant']}
	{php $baseClasses[] = 'inp-item--' . $props['variant']}
{/if}
{if $props['icon']}
	{php $baseClasses[] = 'inp-item--icon'}
{/if}
{if $props['tooltip']}
	{php $baseClasses[] = 'tooltip'}
{/if}

{var $classes = implode(' ', array_merge($baseClasses, $props['classes']))}
{var $classesInp = implode(' ', array_merge(['inp-item__inp'], $props['classesInp']))}

<label
	class="{$classes}"
	{foreach $props['data'] as $key=>$value}
		data-{$key}="{$value|noescape}"
	{/foreach}
>

	{if $props['input']}
		<input
			n:name="$props['input']"
			class="{$classesInp}"
			{foreach $props['dataInp'] as $key=>$value}
				data-{$key}="{$value|noescape}"
			{/foreach}
			{if $props['formId']}
				form="{$props['formId']}"
			{/if}

			{if $props['input']->isOmitted()} disabled{/if}
		>
	{else}
		<input
			type="checkbox"
			name="{$props['name']}"
			class="{$classesInp}"
			value="1"
			{if $props['checked']} checked{/if}
			{if $props['disabled']} disabled{/if}

			{if $props['formId']}
				form="{$props['formId']}"
			{/if}
			{foreach $props['dataInp'] as $key=>$value}
				data-{$key}="{$value|noescape}"
			{/foreach}
		>
	{/if}

	<span class="inp-item__text">
		{if $props['icon']}
			<span class="icon">
				{include $props['icon']}
			</span>
			{if $props['tooltip']}
				<span class="tooltip__content">
					{$props['tooltip']}
				</span>
			{/if}
		{else}
			{if $props['label']}
				{$props['label']|noescape}
			{elseif $props['input']->name}
				{translate}{$props['input']->caption}{/translate}
			{/if}
		{/if}
	</span>
</label>

