{var $props = [
	items: $props['items'] ?? [],
	classes: $props['classes'] ?? [],
]}

{var $classes = implode(' ', array_merge(['m-icons', 'scroll', 'scroll--horizontal'], $props['classes']))}


<div class="{$classes}">
	<ul class="m-icons__list">
		{foreach $props['items'] as $item}
			{var $item = [
				icon: $item['icon'] ?? $templates.'/parts/icons/file.svg',
				href: $item['href'] ?? '',
				tooltip: $item['tooltip'] ?? '',
				linkType: (isset($item['linkType']) && in_array($item['linkType'], ['overlay', 'toggle'])) ? $item['linkType'] : null,
			]}

			<li class="m-icons__item">
				<a href="{$item['href']}" class="m-icons__link item-icon"
					{if $item['linkType'] == 'overlay'}
						data-controller="Toggle"
						data-action="Toggle#changeClass"
						data-toggle-target-class-value="is-visible"
					{/if}
					{if $item['linkType'] == 'toggle'}
						data-controller="Toggle"
						data-action="Toggle#addClass"
						data-toggle-target-class-value="is-open"
					{/if}
				>
					<span class="item-icon__icon icon">
						{include $item['icon']}
					</span>
					{if $item['tooltip']}
						<span class="item-icon__text">
							{$item['tooltip']}
						</span>
					{/if}
				</a>
			</li>
		{/foreach}
	</ul>
</div>
