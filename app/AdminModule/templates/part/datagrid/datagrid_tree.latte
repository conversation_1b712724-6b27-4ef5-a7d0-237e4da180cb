{**
 * @param array    $columns           Available columns
 * @param array    $actions           Available actions
 * @param array    $exports           Available exports
 * @param Row[]    $rows              List of rows (each contain a item from data source)
 * @param DataGrid $control           Parent (DataGrid)
 * @param string   $originalTemplate Original template file path
 * @param string   $iconPrefix       Icon prefix (fa fa-)
 *}

{extends $originalTemplate}

<div class="datagrid-tree-item-children datagrid-tree" n:snippet="table" n:block="data" {if $control->isSortable()}data-sortable-tree data-sortable-url="{plink $control->getSortableHandler()}" data-sortable-parent-path="{$control->getSortableParentPath()}"{/if}>
	{snippetArea items}
		<div class="datagrid-tree-item datagrid-tree-header" n:snippet="item-header">
			<div class="text-right" n:if="$control->canHideColumns() || $inlineAdd || $exports || $toolbarButtons">
				<span n:if="$toolbarButtons">
					{foreach $toolbarButtons as $toolbarButton}{$toolbarButton->renderButton()}{/foreach}
				</span>
			</div>
			<div class="datagrid-tree-item-content" data-has-children="">
				<div class="datagrid-tree-item-left">
					{foreach $columns as $key => $column}
						<strong>{$column->getName()|translate}</strong>
						{breakIf TRUE}
					{/foreach}
				</div>

				<div class="datagrid-tree-item-right">
					<div class="datagrid-tree-item-right-columns">
						{foreach $columns as $key => $column}
							{continueIf $iterator->isFirst()}
							<div class="datagrid-tree-item-right-columns-column col-{$column->getColumnName()} text-{$column->hasAlign() ? $column->getAlign() : 'left'}">
								<strong>{$column->getName()|translate}</strong>
							</div>
						{/foreach}
					</div>
					<div class="datagrid-tree-item-right-actions" n:if="($actions || $control->isSortable()) && $rows">
						<div class="datagrid-tree-item-right-actions-action">
							{var $tmpRow = reset($rows)}

							{foreach $actions as $key => $action}
								{if $tmpRow->hasAction($key)}
									{if $action->hasTemplate()}
										{include $action->getTemplate(), item => $tmpRow->getItem(), (expand) $action->getTemplateVariables(), row => $tmpRow}
									{else}
										{$action->render($tmpRow)|noescape}
									{/if}
								{/if}
							{/foreach}

							<span class="handle-sort btn btn-xs btn-default btn-secondary" n:if="$control->isSortable()">
								<i class="{$iconPrefix}arrows"></i>
							</span>
						</div>
					</div>
				</div>
			</div>
		</div>

		{foreach $rows as $row}
			{var $hasChildren = $control->hasTreeViewChildrenCallback() ? $control->treeViewChildrenCallback($row->getItem()) : $row->getValue($treeViewHasChildrenColumn)}
			{var $item = $row->getItem()}

			<div n:class="$hasChildren ? has-children, 'datagrid-tree-item'" data-id="{$row->getId()}" n:snippet="item-{$row->getId()}">
				<div n:class="datagrid-tree-item-content, $row->getControlClass()" data-id="{$row->getId()}" data-has-children="{$hasChildren ? true : false}">
					<div class="datagrid-tree-item-left">
						<a n:href="getChildren! parent => $row->getId()" data-toggle-tree="true" n:class="!$hasChildren ? hidden, 'chevron'" >
							<i n:block="icon-chevron" class="{$iconPrefix}chevron-right"></i>
						</a>
						{foreach $columns as $key => $column}
							{var $col = 'col-' . $key}
							{php $column = $row->applyColumnCallback($key, clone $column)}

							{if $column->hasTemplate()}
								{include $column->getTemplate(), item => $item, (expand) $column->getTemplateVariables()}
							{else}
								{ifset #$col}
									{include #$col, item => $item}
								{else}
									{if $column->isTemplateEscaped()}
										{$column->render($row)}
									{else}
										{$column->render($row)|noescape}
									{/if}
								{/ifset}
							{/if}

							{breakIf TRUE}
						{/foreach}
					</div>
					<div class="datagrid-tree-item-right">
						<div class="datagrid-tree-item-right-columns">
							{foreach $columns as $key => $column}
								{continueIf $iterator->isFirst()}

								<div class="datagrid-tree-item-right-columns-column text-{$column->hasAlign() ? $column->getAlign() : 'left'}">
									{var $col = 'col-' . $key}
									{php $column = $row->applyColumnCallback($key, clone $column)}

									{if $column->hasTemplate()}
										{include $column->getTemplate(), row => $row, item => $item, (expand) $column->getTemplateVariables()}
									{else}
										{ifset #$col}
											{include #$col, item => $item}
										{else}
											{if $column->isTemplateEscaped()}
												{$column->render($row)}
											{else}
												{$column->render($row)|noescape}
											{/if}
										{/ifset}
									{/if}
								</div>
							{/foreach}
						</div>
						<div class="datagrid-tree-item-right-actions">
							<div class="datagrid-tree-item-right-actions-action">
								{foreach $actions as $key => $action}
									{if $row->hasAction($key)}
										{if $action->hasTemplate()}
											{include $action->getTemplate(), item => $item, (expand) $action->getTemplateVariables(), row => $row}
										{else}
											{$action->render($row)|noescape}
										{/if}
									{/if}
								{/foreach}

								<span class="handle-sort btn btn-xs btn-default btn-secondary" n:if="$control->isSortable()">
									<i n:block = "icon-arrows" class="{$iconPrefix}arrows {$iconPrefix}arrows-alt"></i>
								</span>
							</div>
						</div>
					</div>
				</div>
				<div class="datagrid-tree-item-children" {if $control->isSortable()}data-sortable-tree data-sortable-url="{plink $control->getSortableHandler()}"{/if}></div>
			</div>
		{/foreach}
		{if !$rows}
			{='ublaboo_datagrid.no_item_found'|translate}
		{/if}
	{/snippetArea}
</div>
