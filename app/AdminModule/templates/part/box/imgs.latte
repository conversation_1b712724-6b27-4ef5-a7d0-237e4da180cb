{var $props = [
	items: $props['items'] ?? [],
	title: $props['title'] ?? '',
	dragdrop: $props['dragdrop'] ?? false,
	add: $props['add'] ?? false,
	file: $props['file'] ?? false,
	data: $props['data'] ?? [],
	dataList: $props['dataList'] ?? [],
	dataAdd: $props['dataAdd'] ?? [],
	classes: $props['classes'] ?? 'u-mb-sm',
]}

<div n:class="b-imgs, $props['classes']"
	{foreach $props['data'] as $key=>$value}
		data-{$key}="{$value|noescape}"
	{/foreach}
>
	<h2 n:if="$props['title']" class="b-std__title title">
		{$props['title']}
	</h2>
	<div n:if="$props['add'] && $props['file']" class="u-mt-xs">
		<button class="b-imgs__add b-imgs__add--file btn" type="button"
			{foreach $props['dataAdd'] as $key=>$value}
				data-{$key}="{$value|noescape}"
			{/foreach}
		>
			{include $templates.'/part/icons/plus.svg'}
			<input type="file" multiple data-action="change->ImageLibrary#addImage" accept="image/*">
		</button>
	</div>
	<div class="b-imgs__list grid grid--x-xs grid--x-xs"
		{foreach $props['dataList'] as $key=>$value}
			data-{$key}="{$value|noescape}"
		{/foreach}
	>
		{foreach $props['items'] as $item}
			{php $item['dragdrop'] = $props['dragdrop']}
			{include $templates.'/part/box/image-item.latte', props: $item}
		{/foreach}
	</div>
	<div n:if="$props['add'] && !$props['file']" class="u-mt-xs">
		<button class="b-imgs__add btn" type="button"
			{foreach $props['dataAdd'] as $key=>$value}
				data-{$key}="{$value|noescape}"
			{/foreach}
		>
			{include $templates.'/part/icons/plus.svg'}
		</button>
	</div>
</div>
