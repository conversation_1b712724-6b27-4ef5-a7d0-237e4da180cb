{default $paramUidList = false}


<p id="menu-params">
	{foreach $paramCats as $paramCat}
		<span class="inp-item inp-center">
			<input type="checkbox" id="cat{$paramCat->id}" {if $paramCat->isChecked || in_array($paramCat->id, $catsIdWithParamValue)}checked{/if} />
			<label for="cat{$paramCat->id}">{$paramCat->name}</label>
		</span>
	{/foreach}
</p>


<table class="reset crossroad-params-table">

	{foreach $paramCats as $paramCat}
		{foreach $paramCat->parameters as $param}
			{if $paramUidList}
				{continueIf !in_array($param->uid, $paramUidList)}
			{/if}
			<tr class="cat{$paramCat->id} {if !$paramCat->isChecked && !in_array($paramCat->id, $catsIdWithParamValue)}hide{/if}">
				<td>
{*					<span class="icon">*}
{*						{php $puid = $param->icon}*}
{*						{if $puid}*}
{*							{($puid)|icon}*}
{*						{/if}*}
{*						&nbsp;*}
{*					</span>*}
					<label for="inp-parameter-{$param->id}" class="inp-center">{$param->name}</label>
				</td>
				<td>
					{if !in_array($param->type, ['multiselect', 'select'])}
						{if $object && $object->getParameterById($param->id) && $object->getParameterValueById($param->id)}
							<input type="hidden" name="parameterValueId[{$param->id}]" value="{$object->getParameterValueById($param->id)->id}">
						{/if}
					{/if}

					{if $param->type === "text" || $param->type === "number"}
						<span class="inp-fix">
							<input type="text" class="inp-text w-full" name="parameterValue[{$param->id}]" id="inp-parameter-{$param->id}" value="{if $object && $object->getParameterById($param->id) && $object->getParameterValueById($param->id)}{$object->getParameterValueById($param->id)->value}{/if}" />
						</span>
					{elseif $param->type === "textarea"}
						<span class="inp-fix">
							<textarea name="parameterValue[{$param->id}]" class="inp-text" id="inp-parameter-{$param->id}">{if $object && $object->getParameterById($param->id) && $object->getParameterValueById($param->id)}{$object->getParameterValueById($param->id)->value}{/if}</textarea>
						</span>
					{elseif $param->type === "wysiwyg"}
						<span class="inp-fix">
							<textarea name="parameterValue[{$param->id}]" class="inp-text wysiwyg" id="inp-parameter-{$param->id}">{if $object && $object->getParameterById($param->id) && $object->getParameterValueById($param->id)}{$object->getParameterValueById($param->id)->value}{/if}</textarea>
						</span>
					{elseif $param->type === "bool"}
						<span class="inp-item inp-center">
							<input type="radio" name="parameterValue[{$param->id}]" value="" id="inp-parameter-{$param->id}" checked="checked") />
							<label for="inp-parameter-{$param->id}">{_"Undefined"}</label>
						</span>
						<span class="inp-item inp-center">
							<input type="radio" name="parameterValue[{$param->id}]" value="1" id="inp-parameter-{$param->id}-1" {if $object && $object->getParameterById($param->id) && $object->getParameterValueById($param->id) && $object->getParameterValueById($param->id)->value}checked="checked"{/if} />
							<label for="inp-parameter-{$param->id}-1">{_"Yes"}</label>
						</span>
						<span class="inp-item inp-center">
							<input type="radio" name="parameterValue[{$param->id}]" value="0" id="inp-parameter-{$param->id}-0" {if $object && $object->getParameterById($param->id) && $object->getParameterValueById($param->id) && $object->getParameterValueById($param->id)->value != '' && $object->getParameterValueById($param->id)->value != 1}checked="checked"{/if} />
							<label for="inp-parameter-{$param->id}-0">{_"No"}</label>
						</span>
					{elseif $param->type === "select"}
						<span class="inp-fix inp-fix-select">
							<select type="text" class="inp-text w-full" name="parameterValueId[{$param->id}]" id="inp-parameter-{$param->id}">
								<option value="">Nezvoleno</option>
								{foreach $param->options as $option}

									<option value="{$option->id}" {if isset($object) && $object->getParameterById($param->id) && $object->hasParameterValue($option)}selected="selected"{/if} >{$option->value}</option>
								{/foreach}
							</select>
						</span>
					{elseif $param->type === "multiselect"}
						<span class="inp-fix inp-fix-multiselect">
							<select type="text" class="inp-text w-full" name="parameterValueId[{$param->id}][]" id="inp-parameter-{$param->id}" multiple size="5">
								{foreach $param->options as $option}
									<option value="{$option->id}" {if isset($object) && $object->getParameterById($param->id) && $object->hasParameterValue($option)}selected="selected"{/if} >{$option->value}</option>
								{/foreach}
							</select>
						</span>
					{/if}
				</td>
				<td><span class="inp-center">{$param->unit}</span></td>
{*				{if $presenter->getName() != 'Admin:Page'}*}
{*					<td>*}
{*						<span class="inp-item inp-center">*}
{*							<input type="checkbox" id="detail{$param->id}" name="showParamDetail[{$param->id}]" {if isset($paramIconDetail[$param->id])}checked{/if} />*}
{*							<label for="detail{$param->id}">ikonka na detailu</label>*}
{*						</span>*}
{*						<span class="inp-item inp-center">*}
{*							<input type="checkbox" id="cat{$param->id}" name="showParamList[{$param->id}]" {if isset($paramIconList[$param->id])}checked{/if} />*}
{*							<label for="detail{$param->id}">ikonka na výpise</label>*}
{*						</span>*}
{*						*}{*<label for="cat{$paramCat->id}">{$paramCat->name}</label>*}
{*					</td>*}
{*				{/if}*}
			</tr>
		{/foreach}
	{/foreach}
</table>
