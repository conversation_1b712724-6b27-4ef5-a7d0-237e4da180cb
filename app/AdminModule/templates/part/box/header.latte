{var $props = [
	title: $props['title'] ?? null,
	titleTranslation: $props['titleTranslation'] ?? null,
	img: $props['img'] ?? '',
	hrefClose: $props['hrefClose'] ?? false,
	menu: $props['menu'] ?? false,
	publicLocalizations: $props['publicLocalizations'] ?? [],
	langCodes: $props['langCodes'] ?? [],
	classes: $props['classes'] ?? null,
	isPageTitle: $props['isPageTitle'] ?? false,
	hasGeneratedMenu: $props['hasGeneratedMenu'] ?? false,
	variant: (isset($props['variant']) && in_array($props['variant'], ['overlay'])) ? $props['variant'] : null
]}
<div n:class="b-header, $props['classes'], ($props['menu'] || $props['hasGeneratedMenu']) ? 'b-header--menu'">
	<a n:if="$props['hrefClose']" href="{$props['hrefClose']}" class="b-header__back icon"
		{if isset($props['variant']) && $props['variant'] == 'overlay'}
			data-controller="Toggle" data-action="Toggle#changeClass"
			data-toggle-target-class-value="is-visible"
		{/if}
	>
		{include $templates.'/part/icons/chevron-left.svg'}
	</a>
	<div n:if="isset($props['menu']) && $props['menu'] && isset($props['img']) && $props['img']" class="b-header__img">
		<img src="{$props['img']}" alt="">
	</div>
	<div class="b-header__content u-mb-last-0">
		<h1 n:if="$props['title'] || $props['titleTranslation']" class="b-header__title"
			{if isset($props['isPageTitle']) && $props['isPageTitle']}
				data-controller="PageTitle"
				data-action="ProductTitle:changeTitle@window->PageTitle#updateTitle"
				data-PageTitle-lang-value="cz"
			{/if}
		>
			{if $props['title']}{$props['title']|noescape}{else}{translate}{$props['titleTranslation']}{/translate}{/if}
		</h1>
		<p n:if="$props['publicLocalizations']" class="grid-inline">
			<span>
				Publikováno
			</span>
			<span class="tag" n:foreach="$props['publicLocalizations'] as $langCode">
				{$langCode|upper}
			</span>
		</p>
		<p n:if="$props['langCodes']" class="grid-inline">
			<span class="tag" n:foreach="$props['langCodes'] as $langCode">
				{$langCode|upper}
			</span>
		</p>
	</div>
	<div n:if="$props['menu']" class="b-header__menu">
		{include $templates.'/part/menu/icons.latte'
			props: $props['menu']
		}
	</div>
	<div n:if="$props['hasGeneratedMenu']" class="b-header__menu" data-pagemenu-target="menu"></div>
</div>
