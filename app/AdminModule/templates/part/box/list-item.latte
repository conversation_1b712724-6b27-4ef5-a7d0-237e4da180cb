{var $props = [
	texts: $props['texts'] ?? [],
	inps: $props['inps'] ?? [],
	img: $props['img'] ?? '',
	checkboxes: $props['checkboxes'] ?? [],
	btnsBefore: $props['btnsBefore'] ?? [],
	btnsAfter: $props['btnsAfter'] ?? [],
	tags: $props['tags'] ?? [],
	langs: $props['langs'] ?? [],
	data: $props['data'] ?? [],
	dragdrop: $props['dragdrop'] ?? false,
	progress: $props['progress'] ?? false,
	rowLabel: $props['rowLabel'] ?? '',
]}

<div class="b-list__item"
	{foreach $props['data'] as $key=>$value}
		data-{$key}='{$value|noescape}'
	{/foreach}
>

	{if $props['rowLabel'] !== ''}
		<span>{$props['rowLabel']}</span>
	{/if}

	{if $props['progress']}
		<div class="b-list__progress" data-file-target="progress"></div>
	{/if}
	{if $props['dragdrop']}
		<button class="b-list__dragdrop btn-icon btn-icon--grab" data-drag-handle type="button">
			{include $templates.'/part/icons/grip-vertical.svg'}
		</button>
	{/if}
	{foreach $props['btnsBefore'] as $btn}
		{var $btn = [
			icon: $btn['icon'] ?? $templates.'/part/icons/user.svg',
			data: $btn['data'] ?? [],
			tooltip: $btn['tooltip'] ?? '',
			type: (isset($btn['type']) && in_array($btn['type'], ['checkbox'])) ? $btn['type'] : null,
			variant: (isset($btn['variant']) && in_array($btn['variant'], ['remove'])) ? $btn['variant'] : null,
			classes: $btn['classes'] ?? [],
		]}

		{var $classesBtn = implode(' ', array_merge([
			'b-list__btn',
			'btn-icon',
			$btn['type'] ? 'btn-icon--' . $btn['type'],
			$btn['variant'] ? 'btn-icon--' . $btn['variant'],
			$btn['tooltip'] ? 'tooltip',
			], $btn['classes']))
		}

		<button
			type="button"
			class="{$classesBtn}"
			{foreach $btn['data'] as $key=>$value}
				data-{$key}="{$value|noescape}"
			{/foreach}
		>
			{include $btn['icon']}
			{if $btn['tooltip']}
				<span class="tooltip__content">
					{$btn['tooltip']}
				</span>
			{/if}
		</button>
	{/foreach}
	{if $props['img']}
		<span class="b-list__img">
			<img src="{$props['img']}" alt="">
		</span>
	{/if}
	{foreach $props['texts'] as $text}
		{var $text = [
			text: $text['text'] ?? '',
			classes: $text['classes'] ?? '',
		]}
		<span class="b-list__text {$text['classes']}">
			{$text['text']|noescape}
		</span>
	{/foreach}
	{foreach $props['inps'] as $inp}
		{var $inp = [
			input: $inp['input'],
			type: $inp['type'] ?? 'text',
			placeholder: $inp['placeholder'] ?? '',
			classes: $inp['classes'] ?? '',
			data: $inp['data'] ?? [],
			disabled: $inp['disabled'] ?? false,
		]}

		<span class="b-list__inp {$inp['classes']}">

			<input class="inp-text" n:name="$inp['input']"  placeholder="{$inp['placeholder']}"
				{if $inp['disabled']}
					disabled
				{/if}
				{foreach $inp['data'] as $key=>$value}
					data-{$key}="{$value|noescape}"
				{/foreach}
			>
			<div class="inp-text__holder"></div>
		</span>
	{/foreach}
	<span class="b-list__tags" data-File-target="tags" data-ProductVariant-target="langs" data-DeliveryPrice-target="priceLevel">
		{foreach $props['tags'] as $tag}
			<span class="tag">
				{$tag['text']}
			</span>
		{/foreach}
	</span>
	{foreach $props['checkboxes'] as $checkboxes}
		{var $checkboxes = [
			input: $checkboxes['input'],
			icon: $checkboxes['icon'] ?? 'text',
			tooltip: $checkboxes['tooltip'] ?? ''
		]}

		{include $templates.'/part/core/checkbox.latte',
			props: [
				input: $checkboxes['input'],
				icon: $checkboxes['icon'],
				tooltip: $checkboxes['tooltip'],
				variant: 'icon',
				classes: ['']
			]
		}
	{/foreach}
	{foreach $props['btnsAfter'] as $btn}
		{var $btn = [
			icon: $btn['icon'] ?? $templates.'/part/icons/user.svg',
			data: $btn['data'] ?? [],
			extend: $btn['extend'] ?? [],
			tooltip: $btn['tooltip'] ?? '',
			type: (isset($btn['type']) && in_array($btn['type'], ['checkbox'])) ? $btn['type'] : null,
			variant: (isset($btn['variant']) && in_array($btn['variant'], ['remove'])) ? $btn['variant'] : null,
			classes: $btn['classes'] ?? [],
		]}

		{var $classesBtn = implode(' ', array_merge([
			'b-list__btn',
			'btn-icon',
			$btn['type'] ? 'btn-icon--' . $btn['type'],
			$btn['variant'] ? 'btn-icon--' . $btn['variant'],
			$btn['tooltip'] ? 'tooltip',
			], $btn['classes']))
		}

		<button
			type="button"
			class="{$classesBtn}"
			{foreach $btn['data'] as $key=>$value}
				data-{$key}="{$value|noescape}"
			{/foreach}
		>
			{include $btn['icon']}
			{if $btn['tooltip']}
				<span class="tooltip__content">
					{$btn['tooltip']}
				</span>
			{/if}
		</button>

		{if $btn['extend']}
			<button
				type="button"
				class="{$classesBtn} b-list__mask"
				{foreach $btn['data'] as $key=>$value}
					data-{$key}="{$value|noescape}"
				{/foreach}
			></button>
		{/if}
	{/foreach}
</div>
