<!DOCTYPE html>
<!--[if IE 7 ]>    <html lang="cs" class="ie7 no-js"> <![endif]-->
<!--[if IE 8 ]>    <html lang="cs" class="ie8 no-js"> <![endif]-->
<!--[if IE 9 ]>    <html lang="cs" class="ie9 no-js"> <![endif]-->
<!--[if (gt IE 9)|!(IE)]><!--> <html lang="cs" class="no-js"> <!--<![endif]-->
	<head>
		<meta charset="utf-8" />
		<!--[if IE]><meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><![endif]-->
		<meta name="author" content="HTML by SuperKodéři (<EMAIL>)" />
		<meta name="keywords" content="" />
		<meta name="description" content="" />
		<!--meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0" /-->
		<title>{$title}</title>
		<link href="https://fonts.googleapis.com/css?family=Open+Sans:400,400italic,700&subset=latin,latin-ext" rel="stylesheet" type="text/css" />
		<link rel="stylesheet" href="/admin/new/dist/css/style-old.css?v={$webVersion}" />
		<link rel="stylesheet" href="/admin/css/style.css?v={$webVersion}" media="screen, projection" />
		<link rel="stylesheet" href="/admin/css/developers.css?v={$webVersion}" media="screen, projection" />
		<link rel="stylesheet" href="/admin/css/print.css?v={$webVersion}" media="print" />

		<link rel="shortcut icon" href="/admin/favicon.ico?v={$webVersion}" />
		<script>document.documentElement.className = document.documentElement.className.replace('no-js', 'js');</script>
	</head>
	<body>
		<div id="header">
			<p id="logo">
				<img src="/admin/new/dist/img/logo-superadmin-white.png" width="240" height="48" alt="SUPERADMIN" />
			</p>
			<p id="user">
				<span class="icon icon-user"></span><a n:href=":Admin:User:edit $userEntity->id">{$userEntity->name}</a>  ({$userEntity->role})
				<a n:href=":Admin:Sign:out" class="logout"><span class="icon icon-switch"></span><span class="vhide">logout</span></a>
			</p>
			<div id="search">
				<span class="inp-fix inp-fix-suggest">
					<input class="inp-text inp-suggest-global w-full" name="q" data-suggest="/{$config['adminAlias']}/search/suggest-global" autocomplete="off" />
				</span>
				<div class="search-trigger">
					<span class="icon icon-search"></span>
				</div>
			</div>
		</div>
		<div id="main">

			<div class="col-side">

				{foreach $menu as $section => $items}
					<div class="menu-main">
						<h2 n:if="$section && !is_numeric($section)">{_$section}</h2>
						<ul class="reset" n:inner-foreach="$items as $i">
							{continueIf isset($i['devOnly']) && $i['devOnly'] === true && $userEntity->role !== 'developer'}
							<li n:class="isset($i['devOnly']) && $i['devOnly'] === true ? dev">
								{if $i['action'] == "Import:default"}
									{if $userEntity->role == "developer" || ($userEntity->role == "admin" && $userEntity->email=="<EMAIL>")}
										<a n:href="$i['action']" n:class="($presenter->name == $i['resource']) ? active, isset($i['sub']) ? sub"><span class="icon icon-{$i['icon']}"></span> <span class="name">{_$i['title']}</span></a>
									{/if}
								{else}
									<a n:href="$i['action']" n:class="($presenter->name == $i['resource']) ? active, isset($i['sub']) ? sub">
										<span class="icon icon-{$i['icon']}"></span>
										<span class="name">{_$i['title']}
											{if $i['action'] == "Order:default" && $count = $newOrders->count()}
												[<span style="color:#ee9900;">{$count}</span>]
											{/if}
										</span>
									</a>
								{/if}
							</li>
						</ul>
					</div>
				{/foreach}
			</div>
			<div class="col-main">

				{snippet flash}
					<div n:foreach="$flashes as $flash" class="message message-{$flash->type}">{$flash->message}</div>
				{/snippet}

				{block #tree}{/block}

				{include #content}

			</div>

		</div>
		<script src="/admin/js/jquery.js"></script>
		<script src="/admin/js/jquery.plugin.js"></script>
		<script src="/admin/js/jquery-ui.js"></script>
		<script src="/admin/js/jquery.ui.datetimepicker.js"></script>
		<script src="/admin/js/jquery.nette.js"></script>
		<script src="/admin/js/jquery.cookie.js"></script>
		<script src="/admin/js/jquery.jstree.js"></script>
		<script src="/admin/js/uploadify5/jquery.uploadifive.min.js"></script>
		<script src="/admin/js/fancybox/jquery.mousewheel-3.0.4.pack.js"></script>
		 <!-- <script src="/admin/js/fancybox/jquery.fancybox-1.3.4.pack.js"></script> -->
		<script src="/admin/js/tinymce5/tinymce.min.js"></script>
		<script src="/admin/js/tinymce5/jquery.tinymce.min.js"></script>
		<script src="/admin/js/sk.js"></script>
		<script src="/admin/js/sk/sk.widgets.Suggest.js"></script>
		<script src="/admin/js/sk/sk.widgets.SuggestMenu.js"></script>
		<script src="/admin/js/app.js?v={$webVersion}"></script>
		<script src="/admin/new/dist/js/appCustomFields.js?v={$webVersion}"></script>

		<script>
			App.run({
				sessionID: {session_id()},
			});

			AppCf.run();
		</script>
	</body>
</html>
