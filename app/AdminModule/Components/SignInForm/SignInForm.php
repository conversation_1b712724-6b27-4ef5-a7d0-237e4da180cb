<?php declare(strict_types = 1);

namespace App\AdminModule\Components\SignInForm;

use App\AdminModule\Components\GoogleLogin\GoogleLogin;
use App\AdminModule\Components\GoogleLogin\GoogleLoginFactory;
use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\Model\AdminEmails;
use App\Model\Form\FormThrottler;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Security\AuthenticationException;
use Nette\Utils\ArrayHash;
use App\Model\Translator;

/**
 * @property-read DefaultTemplate $template
 */
final class SignInForm extends UI\Control
{

	public function __construct(
		private readonly Translator $translator,
		private readonly MessageForFormFactory $messageForFormFactory,
		private readonly GoogleLoginFactory $googleLoginFactory,
		private readonly AdminEmails $adminEmails,
		private readonly FormThrottler $formThrottler,
	) {}

	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->render(__DIR__ . '/signInForm.latte');
	}


	protected function createComponentForm(): UI\Form
	{
		$form = new UI\Form();
		$form->setTranslator($this->translator);
		$form->addText('username', 'username')
			->setRequired('msg_error_username');

		$form->addPassword('password', 'password');

		$form->addCheckbox('remember', 'remember');

		$form->addSubmit('send', 'login_button');

		$form->onValidate[] = function (UI\Form $form, ArrayHash $values) {
			if ( ! $this->adminEmails->isDeveloperEmail($values->username) && ! $values->password) {
				$form['password']->addError('msg_error_password');
			}
		};

		$form->onError[] = function ($form) {
			foreach ($form->errors as $error) $form->getPresenter()->flashMessage($error, 'error');
		};

		// call method signInFormSucceeded() on success
		$form->onSuccess[] = $this->signInFormSucceeded(...);

		$this->formThrottler->throttleFormSubmissions($form, formType: 'Admin:SignIn', discriminatorName: 'username');

		return $form;
	}


	private function signInFormSucceeded(UI\Form $form, ArrayHash $values): void
	{
		if ($this->adminEmails->isDeveloperEmail($values->username)) {
			$this['googleLogin']->handleLogin();
			return;
		}

		try {
			$user = $this->presenter->getUser();
			$user->login($values->username, $values->password);

			if ($values->remember) {
				$user->setExpiration('14 days', false);
			} else {
				$user->setExpiration('2 days', true);
			}

			$backlink = $this->presenter->getParameter('backlink');
			if ($backlink) {
				$this->presenter->restoreRequest($backlink);
			}

			$this->presenter->redirect(':Page:Admin:Page:default');

		} catch (AuthenticationException $e) {
			$form->addError($e->getMessage());
		}
	}


	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

	protected function createComponentGoogleLogin(): GoogleLogin
	{
		$googleLogin = $this->googleLoginFactory->create();

		$googleLogin->onLogin[] = fn() => $this->presenter->redirect(':Page:Admin:Page:default');
		$googleLogin->onError[] = function () {
			$this->flashMessage('msg_info_google_login_error', 'error');
			$this->redirect('this');
		};

		return $googleLogin;
	}

}
