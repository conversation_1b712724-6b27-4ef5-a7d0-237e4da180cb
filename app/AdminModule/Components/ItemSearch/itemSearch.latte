<div data-controller="SuggestInp" data-suggestinp-target="wrapper" data-suggestinp-url-value='{"searchParameterName":"q","link":"{link 'search!'}"}'>
	<div class="inp-fix">
		<input class="inp-text" name="q" id="search" autocomplete="off" data-suggestinp-target="input" placeholder="Hledat">
		<div class="inp-text__holder"></div>
	</div>
	<label for="search" class="btn">
		{include $templates.'/part/icons/search.svg'}
	</label>
</div>

{snippet 'itemSearch'}
	<ul class="reset" n:if="isset($foundItems)">
		{foreach $foundItems as $foundItem}
			<li class="item" data-id="{$foundItem->id}">
				<a n:href="clickItem!, foundItemId => $foundItem->id" data-naja="" data-naja-history="off">{$foundItem->name|prepareStrJs}</a>
			</li>
		{/foreach}
	</ul>
{/snippet}

