<?php declare(strict_types = 1);

namespace App\AdminModule\Components\ItemSearch;

use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use App\Model\Translator;

/**
 * @property-read DefaultTemplate $template
 */
final class ItemSearch extends UI\Control
{
	private ?array $foundItems = null;

	/**
	 * @phpstan-param \Closure(string $searchString): array<FoundItem> $searchItemFunction
	 * @phpstan-param \Closure(int $foundItemId): void $clickItemFunction
	 */
	public function __construct(
		private readonly \Closure $searchItemFunction,
		private readonly \Closure $clickItemFunction,
		private readonly Translator $translator,
	) {}

	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->add('templates', RS_TEMPLATE_DIR);

		if ($this->foundItems !== null) {
			$this->template->add('foundItems', $this->foundItems);
		}
		$this->template->render(__DIR__ . '/itemSearch.latte');

	}

	public function handleSearch(): void
	{
		$search = $this->presenter->request->getPost('q');
		$this->foundItems = [];
		if ($search !== null) {
			$this->foundItems = ($this->searchItemFunction)($search);
		}
		$this->redrawControl();
	}

	public function handleClickItem(int $foundItemId): void
	{
		($this->clickItemFunction)($foundItemId);
	}

}
