<form n:name="form">

	{foreach $flashes as $flash}
		<div class="message message-{$flash->type}">{$flash->message}</div>
	{/foreach}

	{embed $templates.'/part/box/std.latte', props=>[
		title=> 'Vytvoření',
	]}
		{block content}
			{include $templates.'/part/core/inp.latte' props: [
				input: $form['description'],
				classesLabel: ['title'],
			]}
			{include $templates.'/part/core/inp.latte' props: [
				input: $form['expiresAt'],
				classesLabel: ['title'],
				type: 'datetime-local'
			]}

			<p>
				<button n:name="send" class="btn btn--full btn--success">
					<span class="btn__text item-icon">
						<span class="item-icon__icon icon">
							{include $templates.'/part/icons/plus.svg'}
						</span>
						<span class="item-icon__text">
							Vygenerovat
						</span>
					</span>
				</button>
			</p>
		{/block}
	{/embed}
</form>
