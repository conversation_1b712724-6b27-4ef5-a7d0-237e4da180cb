<?php declare(strict_types = 1);

namespace App\AdminModule\Components\ApiTokenShellForm;

use App\AdminModule\Components\ApiTokenShellForm\FormData\BaseFormData;
use App\Model\Orm\ApiToken\ApiToken;
use App\Model\Orm\Orm;
use App\Model\Orm\User\User;
use App\Model\Translator;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Throwable;
use function sprintf;

final class ApiTokenShellForm extends Control
{

	public function __construct(
		private readonly User $userEntity,
		private readonly Translator $translator,
		private readonly Orm $orm,
	)
	{
	}

	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$template->add('templates', RS_TEMPLATE_DIR);

		$template->render(__DIR__ . '/ApiTokenShellForm.latte');
	}

	protected function createComponentForm(): Form
	{
		$form = new Form();
		$form->setMappedType(BaseFormData::class);
		$form->setTranslator($this->translator);

		$form->addText('description', 'description')->setRequired();
		$form->addText('expiresAt', 'expiresAt');
		$form->addSubmit('send', 'send');

		$form->onSuccess[] = $this->formSucceeded(...);
		$form->onError[] = $this->formError(...);
		return $form;
	}

	private function formError(Form $form): void
	{
		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	private function formSucceeded(Form $form, BaseFormData $data): void
	{
		$apiToken = new ApiToken(
			$this->userEntity,
			$data->description,
			$data->expiresAt ? new DateTimeImmutable($data->expiresAt) : null,
		);

		try {
			$this->orm->apiToken->persistAndFlush($apiToken);
		} catch (Throwable $exception) {
			$this->flashMessage('Akce se nezdařila', 'error');
		}

		$this->presenter->redirect('this');
	}

}
