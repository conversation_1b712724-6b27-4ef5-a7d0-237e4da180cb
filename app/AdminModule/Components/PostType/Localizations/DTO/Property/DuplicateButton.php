<?php declare(strict_types = 1);

namespace App\AdminModule\Components\PostType\Localizations\DTO\Property;

use App\Model\Orm\Mutation\Mutation;

class DuplicateButton
{

	private function __construct(
		public readonly string $text,
		public readonly int $targetMutationId,
		public readonly string $icon,
	)
	{
	}

	public static function create(Mutation $mutation, string $rsTemplatesPath): DuplicateButton
	{
		return new self(
			text: 'button_duplicate',
			targetMutationId: $mutation->id,
			icon: $rsTemplatesPath . '/part/icons/copy.svg'
		);
	}

}
