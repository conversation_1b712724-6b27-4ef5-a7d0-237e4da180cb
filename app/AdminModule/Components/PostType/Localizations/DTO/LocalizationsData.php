<?php declare(strict_types = 1);

namespace App\AdminModule\Components\PostType\Localizations\DTO;

use App\AdminModule\Components\PostType\Localizations\DTO\Property\DuplicateButton;
use App\AdminModule\Components\PostType\Localizations\DTO\Property\Language;
use App\PostType\Core\Model\LocalizationEntity;
use Nextras\Orm\Exception\NoResultException;

class LocalizationsData
{

	/**
	 * @param array<Language> $languages
	 */
	private function __construct(
		public readonly array $languages,
		public readonly string $editAction,
		public readonly ?DuplicateButton $duplicateButton = null,
	)
	{
	}


	public static function create(
		LocalizationEntity $sourceLocalizationEntity,
		array $mutations,
		string $rsTemplatesPath,
		string $editAction,
		bool $showDuplicateButton = true,
	): LocalizationsData
	{
		$parent = $sourceLocalizationEntity->getParent();
		$languages = [];

		foreach ($mutations as $mutation) {
			try {
				$localizationEntity = $parent->getLocalization($mutation);
				$languages[] = Language::create(
					mutation: $mutation,
					localizationEntity: $localizationEntity,
				);
			} catch (NoResultException) {
				$languages[] = Language::create(
					mutation: $mutation,
				);
			}
		}

		$duplicateButton = null;
		if ($showDuplicateButton) {
			$duplicateButton = DuplicateButton::create(mutation: $sourceLocalizationEntity->getMutation(), rsTemplatesPath: $rsTemplatesPath);
		}

		return new self(
			languages: $languages,
			editAction: $editAction,
			duplicateButton: $duplicateButton,
		);
	}

}
