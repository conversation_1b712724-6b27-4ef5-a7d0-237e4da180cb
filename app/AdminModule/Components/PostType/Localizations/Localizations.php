<?php declare(strict_types = 1);

namespace App\AdminModule\Components\PostType\Localizations;

use App\AdminModule\Components\PostType\Localizations\DTO\LocalizationsData;
use App\Model\Cloner\ClonerProvider;
use App\Model\Cloner\Exception\MissingCloner;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\BaseEntity;
use App\Model\Translator;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Page\Model\Orm\Tree;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nextras\Orm\Entity\IEntity;

/**
 * @property-read DefaultTemplate $template
 */
final class Localizations extends UI\Control
{

	public function __construct(
		private readonly string $rsTemplatesPath,
		private readonly LocalizationEntity $localizationEntity,
		private readonly Translator $translator,
		private readonly MutationsHolder $mutationsHolder,
		private readonly ClonerProvider $clonerProvider,
		private readonly bool $showDuplicateButton = true,
		private readonly string $editAction = 'edit',
	)
	{
	}

	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);

		try {
			assert($this->localizationEntity instanceof IEntity);
			$this->clonerProvider->getClonerByEntity($this->localizationEntity);

			$template->add('dto', LocalizationsData::create(
				sourceLocalizationEntity: $this->localizationEntity,
				mutations: $this->mutationsHolder->findAll(false),
				rsTemplatesPath: $this->rsTemplatesPath,
				editAction: $this->editAction,
				showDuplicateButton: $this->showDuplicateButton,
			));
		} catch (MissingCloner) {}

		$template->render(__DIR__ . '/localizations.latte');
	}


	public function handleDuplicate(int $mutationId): void
	{
		$mutation = $this->mutationsHolder->getByIdChecked($mutationId);

		assert($this->localizationEntity instanceof IEntity);
		$cloner = $this->clonerProvider->getClonerByEntity($this->localizationEntity);
		$newLocalization = $cloner->clone($this->localizationEntity, $mutation);

		$repository = $newLocalization->getRepository();

		assert($newLocalization instanceof BaseEntity);
		$repository->persistAndFlush($newLocalization);
		$this->presenter->redirect($this->editAction, ['id' => $newLocalization->getId()]);
	}

}
