<?php

declare(strict_types=1);

namespace App\AdminModule\Components\GoogleLogin;

use App\Model\AdminEmails;
use App\Model\Google\GoogleProviderFactory;
use App\Model\Orm\Orm;
use App\Model\TranslatorDB;
use League\OAuth2\Client\Provider\Google;
use League\OAuth2\Client\Provider\GoogleUser;
use League\OAuth2\Client\Token\AccessToken;
use Nette\Application\Attributes\CrossOrigin;
use Nette\Application\UI\Control;
use Nette\Http\Session;
use Nette\Http\SessionSection;
use Nette\Security\SimpleIdentity;
use Throwable;
use function assert;
use function hash_equals;

/**
 * @method void onLogin()
 * @method void onError()
 */
final class GoogleLogin extends Control
{

	public array $onLogin = [];

	public array $onError = [];

	private readonly SessionSection $session;

	public function __construct(
		private readonly bool $isEnabled,
		private readonly TranslatorDB $translator,
		private readonly GoogleProviderFactory $googleProviderFactory,
		private readonly Orm $orm,
		private readonly AdminEmails $adminEmails,
		Session $session,
	)
	{
		$this->session = $session->getSection(Google::class);
	}

	public function handleLogin(): void
	{
		if ( ! $this->isEnabled) {
			return;
		}

		$google = $this->googleProviderFactory->create($this->orm->mutation->getDefault());

		$authorizationUrl = $google->getAuthorizationUrl([
			'redirect_uri' => $this->link('//response!'),
		]);

		$this->session->state = $google->getState();
		$this->presenter->redirectUrl($authorizationUrl);
	}

	#[CrossOrigin]
	public function handleResponse(): void
	{
		if ($this->presenter->user->loggedIn) {
			return;
		}

		$error = $this->presenter->getParameter('error');
		if ($error !== null) {
			$this->onError();
			return;
		}

		$state = $this->presenter->getParameter('state');
		if ($state === null || $this->session->state === null || ! hash_equals($this->session->state, $state)) {
			$this->onError();
			return;
		}

		unset($this->session->state);

		$google = $this->googleProviderFactory->create($this->orm->mutation->getDefault());

		$accessToken = $google->getAccessToken('authorization_code', [
			'code' => $this->presenter->getParameter('code'),
			'redirect_uri' => $this->link('//response!'),
		]);

		assert($accessToken instanceof AccessToken);

		try {
			$googleUser = $google->getResourceOwner($accessToken);
			assert($googleUser instanceof GoogleUser);
		} catch (Throwable) {
			$this->onError();
			return;
		}

		$googleId = $googleUser->getId();
		$userById = $this->orm->user->getBy(['googleId' => $googleId]);

		if ($userById !== null) {
			$this->presenter->getUser()->login(new SimpleIdentity($userById->id, $userById->role));
			$this->onLogin();
			return;
		}

		$googleEmail = $googleUser->getEmail();
		$userByEmail = $this->orm->user->getByEmail($googleEmail, $this->orm->mutation->getDefault());
		if ($userByEmail === null) {
			$this->onError();
			return;
		}

		if ( ! $this->adminEmails->isDeveloperEmail($googleEmail)) {
			$this->onError();
			return;
		}

		$userByEmail->googleId = $googleId;
		$this->orm->user->persistAndFlush($userByEmail);

		$this->presenter->getUser()->login(new SimpleIdentity($userByEmail->id, $userByEmail->role));
		$this->onLogin();
	}

	public function render(): void
	{
		if ( ! $this->isEnabled) {
			return;
		}

		$this->template->setTranslator($this->translator);
		$this->template->render(__DIR__ . '/GoogleLogin.latte');
	}

}
