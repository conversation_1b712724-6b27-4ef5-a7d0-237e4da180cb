<?php declare(strict_types = 1);

namespace App\AdminModule\Components\Tree;

use App\Model\TreeStructure\TreeStructure;
use Closure;




interface TreeFactory
{


	/**
	 * @param Closure(): TreeStructure $getTreeStructureFunction
	 * @param Closure(int $movedNodeId, int $targetNodeId, string $action): void $moveNodeFunction
	 * @param Closure(int $parentId, string $name): void $createNodeFunction
	 * @param Closure(int $id): string $linkFunction
	 */
	public function create(
		Closure $getTreeStructureFunction,
		Closure $moveNodeFunction,
		Closure $createNodeFunction,
		Closure $linkFunction,
		bool $isThickbox
	): Tree;

}
