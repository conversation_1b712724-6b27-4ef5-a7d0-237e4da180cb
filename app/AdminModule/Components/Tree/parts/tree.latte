{foreach $siblings as $node}

	{varType App\Model\TreeStructure\Node $node}
	<li id="t{$node->getNodeId()}" href="{link 'link!', 'id' => $node->getNodeId()}" data-id="{$node->getNodeId()}">
		<a href="{plink 'default', 'id' => $node->getNodeId()}" class="{if $node->isSelected()}jstree-clicked{/if} {if !$node->isPublic()}nopublic{/if}" >{$node->getNodeName()}</a>
		{if $node->getNodeItems() !== []}
			<ul>
				{include 'tree.latte', 'siblings' => $node->getNodeItems()}
			</ul>
		{/if}
	</li>
{/foreach}
