<div class="col-tree">
	{if isset($treeStructure) && $treeStructure instanceof App\Model\TreeStructure\TreeStructure}
		<div class="m-tree"
			 data-controller="MenuTree"
			 data-menutree-create-value="{link create!}"
			 data-menutree-moveparametername-value="{$moveParameterName}"
			 data-menutree-targetparametername-value="{$targetParameterName}"
			 data-menutree-actionparametername-value="{$actionParameterName}"
			 data-menutree-nameparametername-value="{$nameParameterName}"
			 data-menutree-move-value="{link move!}"
			 {if $isThickbox}data-menutree-disablehistory-value="" {/if}
		>
			<ul>
				{include 'parts/tree.latte', 'siblings' => $treeStructure->getRootNodes()}
			</ul>
		</div>
	{/if}
</div>
