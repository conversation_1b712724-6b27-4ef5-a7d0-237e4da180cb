<?php declare(strict_types = 1);

namespace App\AdminModule\Components\LostPasswordForm;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\Model\AdminEmails;
use App\Model\ConfigService;
use App\Model\Email\CommonFactory;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\UserHash\UserHash;
use App\Model\Orm\UserHash\UserHashModel;
use App\Model\Translator;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Utils\ArrayHash;

/**
 * @property-read DefaultTemplate $template
 */
final class LostPasswordForm extends UI\Control
{

	public function __construct(
		private readonly Mutation $mutation,
		private readonly Orm $orm,
		private readonly Translator $translator,
		private readonly UserHashModel $userHashModel,
		private readonly CommonFactory $commonEmailFactory,
		private readonly ConfigService $configService,
		private readonly MessageForFormFactory $messageForFormFactory,
		private readonly AdminEmails $adminEmails,
	)
	{
	}

	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->render(__DIR__ . '/lostPasswordForm.latte');
	}


	protected function createComponentForm(): UI\Form
	{
		$form = new UI\Form();
		$form->setTranslator($this->translator);

		$form->addEmail('email', 'email')
			->setRequired('form_enter_email');

		$form->addSubmit('send', 'Send');

		// call method signInFormSucceeded() on success
		$form->onSuccess[] = [$this, 'lostPasswordFormSucceeded'];
		return $form;
	}


	public function lostPasswordFormSucceeded(UI\Form $form, ArrayHash $values): void
	{
		if ($this->adminEmails->isDeveloperEmail($values->email)) {
			$this->flashMessage('form_send_reset_password', 'ok');
			$this->redirect('this');
		}

		$user = $this->orm->user->getByEmail($values->email, $this->mutation);
		if ($user) {
			$existingRecentHash = $this->orm->userHash->getBy([
				'user' => $user,
				'type' => UserHash::TYPE_LOST_PASSWORD,
				'createdTime>=' => (new \DateTimeImmutable())->modify('-2 minutes'),
			]);

			if ($existingRecentHash === null) {
				$userHash = $this->userHashModel->generateHashForUser($user, UserHash::TYPE_LOST_PASSWORD, ['email' => $user->email], 1);
				$domainClear = $this->mutation->getBaseUrl();

				$mailData = [];

				$mailData['link'] = $domainClear . '/' . $this->configService->get('adminAlias') . '/reset-hesla?hashToken=' . $userHash->hash;

				$this->commonEmailFactory
					->create()
					->send('', $user->email, 'lostPassword', $mailData);
			}
		}

		$this->flashMessage('form_send_reset_password', 'ok');
		$this->redirect('this');
	}

	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

}
