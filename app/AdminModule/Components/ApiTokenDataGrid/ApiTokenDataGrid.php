<?php declare(strict_types = 1);

namespace App\AdminModule\Components\ApiTokenDataGrid;

use App\Model\Orm\ApiToken\ApiToken;
use App\Model\Orm\Orm;
use App\Model\Translator;
use Nette\Application\UI\Control;
use Ublaboo\DataGrid\DataGrid;

final class ApiTokenDataGrid extends Control
{

	public function __construct(
		private readonly Translator $translator,
		private readonly Orm $orm,
	)
	{
	}

	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->render(__DIR__ . '/ApiTokenDataGrid.latte');
	}

	protected function createComponentGrid(): DataGrid
	{
		$grid = new DataGrid();

		$apiTokens = $this->orm->apiToken->findBy([]);
		$grid->setDataSource($apiTokens);
		$grid->addColumnText('description', 'description')->setSortable();
		$grid->addColumnText('token', 'token');
		$grid->addColumnDateTime('expiresAt', 'expiresAt')->setSortable()->setFormat('Y-m-d H:i:s');
		$grid->addColumnDateTime('revokedAt', 'revokedAt')->setSortable()->setFormat('Y-m-d H:i:s');

		$grid->addAction('revoke', 'revoke', 'revoke!');

		$grid->setTranslator($this->translator);
		$grid->setDefaultSort('expiresAt');

		return $grid;
	}

	public function handleRevoke(int $id): void
	{
		/** @var ApiToken $apiToken */
		$apiToken = $this->orm->apiToken->getById($id);
		$apiToken->revoke();

		$this->orm->apiToken->persistAndFlush($apiToken);

		if ($this->presenter->isAjax()) {
			$this['grid']->redrawItem($id);
		} else {
			$this->redirect('this');
		}
	}

}
