{php $imgSrc = ''}
{if $object->firstImage}
	{varType App\Model\Image\ImageObjectFactory $imageObjectFactory}
	{php $img = $imageObjectFactory->getByName($object->firstImage->filename, 's')}
	{php $imgSrc = $img->src}
{/if}

{var $publicLocalizations = $object->productLocalizations->toCollection()->findBy(['public' => 1])->fetchPairs(null, 'mutation->langCode')}

{include $templates.'/part/box/header.latte',
	props: [
		hrefClose: '/superadmin/catalog',
		img: $imgSrc,
		title: $product->name,
		publicLocalizations: $publicLocalizations,
		hasGeneratedMenu: true,
		isPageTitle: true,
	]
}
