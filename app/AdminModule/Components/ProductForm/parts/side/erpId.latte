{varType App\Model\Orm\Product\Product $product}
{varType App\Model\Orm\User\User $userEntity}
{if $product->extId !== null && $userEntity->isDeveloper()}
	<h2 class="b-std__title title">
		ERP ID
	</h2>
	<p class="u-font-sm">
		<input class="inp-text inp-center" value="{$product->extId}" disabled {if isset($formId)} form="{$formId}" {/if}>
	</p>
{/if}
{if $product->firstVariant->code !== ''}
	<h2 class="b-std__title title">
		ERP Kód
	</h2>
	<p class="u-font-sm">
		<input class="inp-text inp-center" value="{$product->firstVariant->code}" disabled {if isset($formId)} form="{$formId}" {/if}>
	</p>
{/if}
{if $product->firstVariant->ean !== '0'}
	<h2 class="b-std__title title">
		EAN
	</h2>
	<p class="u-font-sm">
		<input class="inp-text inp-center" value="{$product->firstVariant->ean}" disabled {if isset($formId)} form="{$formId}" {/if}>
	</p>
{/if}
