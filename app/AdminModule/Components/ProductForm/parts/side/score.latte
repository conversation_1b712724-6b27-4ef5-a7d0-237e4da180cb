{varType App\Model\Orm\Product\Product $product}
{if $product->extId !== null}
	<h2 class="b-std__title title">
		{_"product_score"}
	</h2>
	<p class="u-font-sm">
		<input class="inp-text inp-center" value="{$product->score}" disabled {if isset($formId)} form="{$formId}" {/if}>
	</p>
{/if}
{if $product->isDamaged}
	<h2 class="b-std__title title" n:if="$product->damagedParent !== null">
		{_'damaged_parent'}
	</h2>
	<p class="u-font-sm">
		<a target="_blank" href="{plink Product:edit, id: $product->damagedParent->id}">{$product->damagedParent->internalName}</a>
	</p>
	<h2 class="b-std__title title">
		{_"damaged_type"}
	</h2>
	<p class="u-font-sm">
		{$product->damagedType}
	</p>

{/if}

