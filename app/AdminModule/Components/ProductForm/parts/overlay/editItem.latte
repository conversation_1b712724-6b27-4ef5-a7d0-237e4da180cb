<div data-Templates-target="overlays">
	{foreach $form['variants']->getComponents() as $variantId=>$variantContainer}
		{continueIf $variantId === 'newItemMarker'}

		{var $variant = $product->variants->toCollection()->getById($variantId)}
		{embed $templates.'/part/core/overlay.latte', props: [
		id: 'variant_' . $variantId,
		title: 'Editace / Přidání varianty',
		data: [
		controller: 'ProductVariantEdit',
		ProductVariantEdit-id-value: 'variant_' . $variantId,
		],
		], templates=>$templates}
			{block content}
				{include './variant.latte', variantId=>$variantId, variantContainer=>$variantContainer}
			{/block}
		{/embed}
	{/foreach}

	{foreach $form['productCommon']->getComponent('images')->components as $imageKey=>$imageContainer}
		{continueIf $imageKey === 'newItemMarker'}

		{var $imageId = $imageContainer['imageId']->getValue()}
		{var $productImage = $product->images->toCollection()->getBy(['libraryImage->id' => $imageKey])}
		{embed $templates.'/part/core/overlay.latte', props: [
			id: 'image_' . $productImage->libraryImage->id,
			title: 'Editace obrázku',
			], templates=>$templates}

			{block content}
				{include './image.latte', imageId=>$productImage->libraryImage->id, imageContainer=>$imageContainer}
			{/block}
		{/embed}
	{/foreach}

</div>
