{formContainer $variantContainer}
	{embed $templates.'/part/box/toggle.latte', props=>[
		title: '',
		id: 'variant-public-'.$variantId,
		open: true,
		tags: [
			[text: 'Lokalizované']
		]
	], templates=>$templates}
		{block content}
			{foreach $mutations as $mutation}
				{var $langCode = $mutation->langCode}
				<div class="js-lang js-lang--{$langCode}">
					{include $templates.'/part/core/inp.latte' props: [
						label: 'name_variant',
						input: $variantContainer['variantLocalizations'][$mutation->id]['name'],
						classesLabel: ['title'],
					]}
					{include $templates.'/part/core/inp.latte' props: [
						label: 'name_variant_short',
						input: $variantContainer['variantLocalizations'][$mutation->id]['nameShort'],
						classesLabel: ['title'],
					]}
					{include $templates.'/part/core/checkbox.latte',
						props: [
							input: $variantContainer['variantLocalizations'][$mutation->id]['active'],
							label: '<span class="grid-inline"><span class="tag">'.$langCode.'</span> <span>Aktivováno</span></span>',
							dataInp: [
								action: 'change->ProductVariantEdit#edit',
								ProductVariantEdit-target: 'lang',
								lang: $langCode,
							]
						]
					}
				</div>
			{/foreach}
		{/block}
	{/embed}


	{embed $templates.'/part/box/toggle.latte', props=>[
		title: 'Ceny - nastavení',
		open: true,
		id: 'variant-price-'.$variantId,
		tags: []
	], templates=>$templates}
		{block content}
			{var $variant = $product->variants->toCollection()->getById($variantId)}
			{varType App\Model\Orm\ProductVariant\ProductVariant $variant}

			<div class="grid">
				<div class="grid__cell size--3-12">
					{input $variantContainer['variantPrices']['usePrice']}
				</div>
				<div class="grid__cell size--3-12">
					<strong>DPH</strong>: {$variant->erpVat} %
				</div>
				<div class="grid__cell size--3-12">
					{include $templates.'/part/core/inp.latte' props: [
						label: null,
						input: $variantContainer['variantPrices']['margin'],
						classesLabel: ['title'],
					]}

				</div>
				<div class="grid__cell size--3-12">
					{include $templates.'/part/core/inp.latte' props: [
						label: null,
						input: $variantContainer['variantPrices']['marginMin'],
						classesLabel: ['title'],
					]}
				</div>
			</div>

		{if $variant !== null}
			{var $prices = $variant->pricesByLevelName}
			{foreach $mutations as $mutation}
				{varType App\Model\Orm\Mutation\Mutation $mutation}
				{var $currencyCode = $mutation->currency->getCurrencyCode()}
					<table class="table table-striped table-sm">
						<thead>
						<tr>
							<th>{_'price_level'}</th>
							<th>{_'price_level_label_discount'}</th>
							<th>{_'price_level_label_price'}</th>
							<th>{_'price_level_label_price_vat'}</th>
							<th>{_'product_stock_last_import'}</th>
						</tr>
						</thead>
						<tbody>
				{foreach $priceLevels as $priceLevel}
					{varType App\Model\Orm\PriceLevel\PriceLevel $priceLevel}

					{var $price = $prices[$mutation->id][$currencyCode][$priceLevel->type] ?? null}
					{varType App\Model\Orm\ProductVariantPrice\ProductVariantPrice $price}
						<tr>
							<td><strong>{$priceLevel->name}</strong></td>
							<td>{$priceLevel->discount}</td>
							<td>{if $price}{$price->price->amount|number:4}{/if}</td>
							<td>
								{if $price}
									{App\Model\VatCalculator::priceWithVat($price->price->asMoney(2), Brick\Math\BigDecimal::of($price->vat))->getAmount()}
								{/if}
							</td>
							<td>{if $price && $price->lastImport}{$price->lastImport|date:'j.n.Y H:i:s'}{else}--{/if}</td>
						</tr>
				{/foreach}

				{if $variant->isSubscription}
					{* @todo jk doplnit vypocet cen predplatneho z ceny pro registrovane - jako se bude volat na FE *}
					{var $priceLevel = $priceLevels[App\Model\Orm\PriceLevel\PriceLevel::TYPE_REGISTERED]}
					{var $price = $prices[$mutation->id][$currencyCode][$priceLevel->type] ?? null}
						<tr>
							<td><strong>{_'price_level_label_subscription_first'}</strong></td>
							<td>{$priceLevel->discountSubscriptionFirst}</td>
							<td></td>
							<td></td>
							<td>--</td>
						</tr>
					<tr>
						<td><strong>{_'price_level_label_subscription'}</strong></td>
						<td>{$priceLevel->discountSubscription}</td>
						<td></td>
						<td></td>
						<td>--</td>
					</tr>
				{/if}
						</tbody>
					</table>
			{/foreach}
		{/if}

		{/block}
	{/embed}

	{embed $templates.'/part/box/toggle.latte', props: [ title: 'Historie ceny', open: false, id: 'variant-price-history-'.$variantId], templates: $templates}
		{block content}
			{foreach $historyPriceLevels as $priceLevel}
				<h5>{$priceLevel->name}</h5>
				<table class="table table-striped table-sm">
					<thead>
					<tr>
						<th>{_Date}</th>
						<th>{_selling_price}</th>
						<th>{_product_price}</th>
						<th>{_original_price}</th>
						<th>{_label_discountPercent}</th>
					</tr>
					</thead>
					<tbody>
					<tr n:foreach="$pricesHistory[$priceLevel->id] as $price">
						<td>{$price->createdAt|date:'d. m. Y H:i'}</td>
						<td>{$price->salePrice}</td>
						<td>{$price->origPrice}</td>
						<td>{$price->realOrigPrice}</td>
						<td>{if $price->isInSale}{$price->discount} %{else}-{/if}</td>
					</tr>
					</tbody>
				</table>
			{/foreach}

		{/block}
	{/embed}

	{embed $templates.'/part/box/toggle.latte', props=>[
		title: '',
		open: true,
		id: 'variant-supply-'.$variantId,
	], templates=>$templates}
		{block content}
			{var $variant = $product->variants->toCollection()->getById($variantId)}
			<div n:if="$variant !== null" class="u-mb-sm" n:foreach="$stocks as $stock">
				<div class="grid" n:if="isset($variant->suppliesByStock[$stock->id])">
					<div class="grid__cell size--6-12">
						<strong>{_'product_stock_title'}</strong>: {$variant->suppliesByStock[$stock->id]->amount} {_'product_stock_pcs'}
					</div>
					<div class="grid__cell size--6-12">
						<strong>{_'product_stock_last_import'}</strong>: {if $variant->suppliesByStock[$stock->id]->lastImport}{$variant->suppliesByStock[$stock->id]->lastImport|date:'j.n.Y H:i:s'}{else}--{/if}
					</div>
				</div>
			</div>
		{/block}
	{/embed}

	{if $variantCommonContainer = $variantContainer->getComponent('variantCommon')}
		{formContainer $variantCommonContainer}
			{foreach $variantCommonContainer->getComponents() as $item}
				{if $item->options['type'] == 'text'}
					{include $templates.'/part/core/inp.latte' props: [
						input: $item,
						classesLabel: ['title']
					]}
				{elseif $item->options['type'] == 'checkbox'}
					{include $templates.'/part/core/checkbox.latte',
						props: [
							input: $item
						]
					}
				{elseif $item->options['type'] == 'select'}
					{include $templates.'/part/core/inp.latte' props: [
						input: $item,
						type: 'select',
						classesLabel: ['title']
					]}
				{else}
					<div>
						{label $item /}
						{input $item}
					</div>
				{/if}
			{/foreach}
		{/formContainer}
	{/if}
{/formContainer}
