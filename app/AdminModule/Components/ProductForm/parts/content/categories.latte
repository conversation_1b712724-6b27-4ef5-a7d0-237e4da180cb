{var $props = [
	title: 'Kategorie',
	id: 'categories',
	icon: $templates.'/part/icons/folder.svg',
	variant: 'main',
	classes: ['u-mb-xxs'],
	rowMain: false,
	tags: [
		[
			text: 'Lokalizované'
		]
	]
]}

{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
	{block content}
		<div class="grid grid--center grid--x-0 grid--y-0">
			{var $gridSize = 12}
			{if count($form['productLocalizations']->components) == 2}
				{var $gridSize = 6}
			{/if}

			{foreach $form['productLocalizations']->components as $mutationId=>$localizationContainer}

				{var $mutation = $mutations->getById($mutationId)}
				{var $langCode = $mutation->langCode}

				{var $items = []}

				{foreach $localizationContainer['categories']->components as $categoryId=>$categoryContainer}

					{continueIf $categoryId === 'newItemMarker'}


					{var $url = $urls['searchMutationPage']->toArray()}
					{php $url['params']['mutationId'] = $mutationId}
					{php $url['params']['templates'] = [':Front:Catalog:default']}

					{var $item = [
						data: [
							controller: 'RemoveItem SuggestInp',
							removeitem-target: 'item',
							suggestinp-target: 'wrapper',
							suggestinp-url-value: Nette\Utils\Json::encode($url)

						],
						inps: [
							[
								placeholder: 'Zadejte název kategorie',
								input: $categoryContainer['name'],
								data: [
									suggestinp-target: 'input',
								]
							],
							[
								input: $categoryContainer['id'],
								data: [
									suggestinp-target: 'idInput',
								],
								classes: 'u-hide',
								type: 'hidden'
							]
						],
						btnsAfter: [
							[
								icon: $templates.'/part/icons/trash.svg',
								tooltip: 'Odstranit',
								variant: 'remove',
								data: [
									action: 'RemoveItem#remove'
								]
							]
						]
					]}

					{php $items[] = $item}
				{/foreach}


				{capture $localizedContent}
					{include $templates.'/part/box/list.latte',
						props: [
							data: [
								controller: 'List',
								List-mutationId-value: $mutation->id,
								List-name-value: 'category',
							],
							listData: [
								List-target: 'list',
							],
							addData: [
								action: 'List#add',
							],
							add: true,
							dragdrop: true,
							items: $items
						]
					}
				{/capture}

				{if $langCode == $defaultMutation->langCode}
					<div class="grid__cell size--{$gridSize}-12 size--lang js-lang js-lang--{$langCode|lower}">
						<div class="row-main">
							<div class="tag u-mb-xxs">
								{$langCode}
							</div>
							{$localizedContent}
						</div>
					</div>
				{else}
					<div class="grid__cell size--{$gridSize}-12 size--lang js-lang js-lang--{$langCode|lower}">
						<div class="row-main">
							{include $templates.'/part/core/checkbox.latte',
								props: [
									input: $form['productLocalizations'][$mutation->id]['setup']['inheritCategories'],
									label: '<span class="grid-inline"><span class="tag">'.strtoupper($langCode).'</span> <span>přebírá nastavení z výchozí verze</span></span>',
									dataInp: [
										controller: 'ToggleCheckbox',
										action: 'ToggleCheckbox#changeClass',
										togglecheckbox-target-value: '#checkbox-categories-'.strtolower($langCode),
										togglecheckbox-target-class-value: 'is-open'
									],
								]
							}
							{var $isOpen = !($form['productLocalizations'][$mutation->id]['setup']['inheritCategories']->getValue())}

							<div id="checkbox-categories-{$langCode|lower}" class="js-toggle-checkbox__content u-mt--xs {if $isOpen}is-open{/if}">
								{$localizedContent}
							</div>
						</div>
					</div>
				{/if}
			{/foreach}
		</div>
	{/block}
{/embed}
