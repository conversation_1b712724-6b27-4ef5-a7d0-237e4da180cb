{var $props = [
	title: 'Platnost',
	id: 'validity',
	icon: $templates.'/part/icons/calendar-alt.svg',
	variant: 'main',
	classes: ['u-mb-xxs']
]}

{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
	{block content}
		<div class="grid grid--y-0">
			<div class="grid__cell size--6-12">
				{include $templates.'/part/core/inp.latte' props: [
					input: $form['productCommon']['publicFrom'],
					classesLabel: ['title'],
					type: 'datetime-local'
				]}
			</div>
			<div class="grid__cell size--6-12">
				{include $templates.'/part/core/inp.latte' props: [
					input: $form['productCommon']['publicTo'],
					classesLabel: ['title'],
					type: 'datetime-local'
				]}
			</div>
		</div>
	{/block}
{/embed}




