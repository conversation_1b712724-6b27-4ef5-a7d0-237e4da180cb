{var $props = [
	title: 'Obsah',
	id: 'content',
	icon: $templates.'/part/icons/align-left.svg',
	variant: 'main',
	open: true,
	classes: ['u-mb-xxs'],
	rowMain: false,
	tags: [
		[text: 'Povinné'],
		[text: 'Lokalizované'],
	]
]}

{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
	{block content}
		<div class="grid grid--center grid--x-0 grid--y-0">
			{var $gridSize = 12}
			{if count($form['productLocalizations']->components) == 2}
				{var $gridSize = 6}
			{/if}

			{foreach $form['productLocalizations']->components as $mutationId=>$localizationContainer}
				{var $mutation = $mutations->getById($mutationId)}
				{var $langCode = $mutation->langCode}

				{var $langCode = $mutation->langCode}

				<div class="grid__cell size--{$gridSize}-12 size--lang js-lang js-lang--{$langCode|lower}">
					<div class="row-main">
						<div class="tag u-mb-xs">
							{$langCode}
						</div>

						{include $templates.'/part/core/inp.latte' props: [
							label: 'name',
							input: $localizationContainer['name'],
							classesLabel: ['title'],
							dataInp: [
								controller: 'ProductTitle',
								action: 'input->ProductTitle#changeTitle blur->ProductTitle#generateAlias',
								producttitle-lang-value: $langCode
							]
						]}

						{include $templates.'/part/core/inp.latte' props: [
							label: 'annotation',
							input: $localizationContainer['annotation'],
							classesLabel: ['title'],
							type: 'textarea',
							rows: 5,
							dataInp: [
								controller: 'Tiny',
								tiny-type: 'lite',
								tiny-target: 'item',
							]
						]}

						{include $templates.'/part/core/inp.latte' props: [
							label: 'content',
							input: $localizationContainer['content'],
							classesLabel: ['title'],
							type: 'textarea',
							rows: 20,
							dataInp: [
								controller: 'Tiny',
								tiny-target: 'item',
							]
						]}
					</div>
				</div>
			{/foreach}
		</div>

	{/block}
{/embed}




