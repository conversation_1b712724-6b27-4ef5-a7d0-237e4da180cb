{var $props = [
	title: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
	id: 'images',
	variant: 'main',
	icon: $templates.'/part/icons/images.svg',
	classes: ['u-mb-xxs'],
	tags: [
		[text: 'Lokalizované'],
	]
]}



{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
	{block content}

		{var $items = []}
		{var $ids = []}


		{foreach $form['productCommon']->getComponent('images')->components as $imageKey=>$imageContainer}
			{continueIf $imageKey === 'newItemMarker'}

			{var $imageId = $imageContainer['imageId']->getValue()}
			{if is_int($imageKey)}
				{var $productImage = $product->images->toCollection()->getBy(['libraryImage->id' => $imageKey])}
				{php $img = $imageObjectFactory->getByName($productImage->filename, 's')}
				{php $imgSrc = $img->src}
			{else}
				{var $imgSrc = ''}
			{/if}



			{var $item = [
				dragdrop: true,
				data: [
					controller: 'RemoveItem',
					removeitem-target: 'item',
					removeitem-animation-value: 'fade',
					id: $productImage->libraryImage->id
				],
				img: $imgSrc,
				inps: [
					[
						input: $imageContainer['imageId'],
						type: 'hidden'
					]
				],
				btns: [
					[
						icon: $templates.'/part/icons/pencil-alt.svg',
						tooltip: 'Editovat',
						data: [
							controller: 'Toggle',
							action: 'Toggle#changeClass',
							toggle-target-value: '#overlay-image_'.$productImage->libraryImage->id,
							toggle-target-class-value: 'is-visible',
						],
					],
					[
						icon: $templates.'/part/icons/trash.svg',
						tooltip: 'Odstranit',
						variant: 'remove',
						data: [
							action: 'RemoveItem#remove ImageList#removeImg',
						],
					]
				]
			]}

			{php $items[] = $item}
			{php $ids[] = $productImage->libraryImage->id}
		{/foreach}

		{include $templates.'/part/box/imgs.latte',
			props: [
				add: true,
				data: [
					controller: 'ImageList',
					imagelist-ids-value: json_encode($ids),
				],
				dataList: [
					imagelist-target: 'list',
				],
				dataAdd: [
					controller: 'Toggle',
					action: 'Toggle#changeClass ImageList#newImg',
					toggle-target-value: '#overlay-library',
					toggle-target-class-value: 'is-visible'
				],
				dragdrop: true,
				items: $items
			]
		}


	{/block}
{/embed}


