{embed $templates.'/part/box/toggle.latte', props=>[
	title: 'Parametry',
	id: 'params',
	icon: $templates.'/part/icons/sliders-h.svg',
	variant: 'main',
	classes: ['u-mb-xxs']
], templates=>$templates}
	{block content}

		{default $paramUidList = false}

		{embed $templates.'/part/box/toggle.latte', props=>[
			title: Parametry,
			id: 'param-product',
			open: true
		], templates=>$templates}
			{block content}
				{foreach $parameters as $param}
					{if $paramUidList}
						{continueIf !in_array($param->uid, $paramUidList)}
					{/if}

					{if !in_array($param->type, ['multiselect', 'select'])}
						{if $object && $object->getParameterById($param->id) && $object->getParameterValueById($param->id)}
							<input type="hidden" name="parameterValueId[{$param->id}]" value="{$object->getParameterValueById($param->id)->id}">
						{/if}
					{/if}

					{if $param->type === "text" || $param->type === "number" || $param->type === "textarea"}
						{include $templates.'/part/core/inp.latte' props: [
							id: 'inp-parameter-'.$param->id,
							label: $param->name,
							type: $param->type,
							name: 'parameterValue['.$param->id.']',
							value: ($object && $object->getParameterById($param->id) && $object->getParameterValueById($param->id)) ? $object->getParameterValueById($param->id)->internalValue : '',
							classesLabel: ['title'],
							sufix: $param->type === "number" ? $translatorDB->translate('pname_unit_' . $param->id) : null
						]}
					{elseif $param->type === "wysiwyg"}
						{include $templates.'/part/core/inp.latte' props: [
							id: 'inp-parameter-'.$param->id,
							label: $param->name,
							type: 'textarea',
							name: 'parameterValue['.$param->id.']',
							value: ($object && $object->getParameterById($param->id) && $object->getParameterValueById($param->id)) ? $object->getParameterValueById($param->id)->internalValue : '',
							classesLabel: ['title'],
							rows: 20,
							dataInp: [
								controller: 'Tiny',
								tiny-target: 'item',
							]
						]}
					{elseif $param->type === "bool"}
						{include $templates.'/part/core/inp.latte' props: [
							id: 'inp-parameter-'.$param->id,
							label: $param->name,
							type: 'select',
							name: 'parameterValue['.$param->id.']',
							classesLabel: ['title'],
							optionsCustom: [
								[
									text: 'Undefined',
									value: '',
									selected: false
								],
								[
									text: 'Yes',
									value: '1',
									selected: ($object && $object->getParameterById($param->id) && $object->getParameterValueById($param->id) && $object->getParameterValueById($param->id)->internalValue) ? true : false
								],
								[
									text: 'No',
									value: '0',
									selected: ($object && $object->getParameterById($param->id) && $object->getParameterValueById($param->id) && $object->getParameterValueById($param->id)->internalValue != '' && $object->getParameterValueById($param->id)->internalValue != 1) ? true : false
								]
							]
						]}
					{elseif $param->type === "select"}
						{include $templates.'/part/core/inp.latte' props: [
							idPrefix: 'inp-parameter-',
							id: $param->id,
							label: $param->name,
							type: 'select',
							name: 'parameterValue['.$param->id.']',
							classesLabel: ['title'],
							sufix: $translatorDB->translate('pname_unit_' . $param->id),
							options: $param->options,
							multiple: false
						], object=>$object}


					{elseif $param->type === "multiselect"}


						{include $templates.'/part/core/inp.latte' props: [
							idPrefix: 'inp-parameter-',
							id: $param->id,
							label: $param->name,
							type: 'select',
							name: 'parameterValue['.$param->id.'][]',
							classesLabel: ['title'],
							options: $param->options,
							multiple: true
						], object=>$object}
					{/if}

{*						{if $presenter->getName() != 'Admin:Page'}*}
{*							<div class="inp-items u-mb-sm u-mt--xs">*}
{*								<div class="inp-items__list grid">*}
{*									<div class="inp-items__item grid__cell size--auto">*}
{*										{include $templates.'/part/core/checkbox.latte',*}
{*											props: [*}
{*												label: 'Ikonka na detailu',*}
{*												checked: isset($paramIconDetail[$param->id]) ? true : false,*}
{*												name: 'showParamDetail['.$param->id.']'*}
{*											]*}
{*										}*}
{*									</div>*}
{*									<div class="inp-items__item grid__cell size--auto">*}
{*										{include $templates.'/part/core/checkbox.latte',*}
{*											props: [*}
{*												label: 'Ikonka na detailu',*}
{*												checked: isset($paramIconList[$param->id]) ? true : false,*}
{*												name: 'showParamList['.$param->id.']'*}
{*											]*}
{*										}*}
{*									</div>*}
{*								</div>*}
{*							</div>*}
{*						{/if}*}
				{/foreach}
			{/block}
		{/embed}
	{/block}
{/embed}




