{var $props = [
	title: 'Alternativní produkty',
	id: 'similar',
	variant: 'main',
	icon: $templates.'/part/icons/barcode.svg',
	classes: ['u-mb-xxs'],
	tags: []
]}

{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
	{block content}

		{var $items = []}
		{foreach $form['productCommon']['similar']->components as $productKey=>$productContainer}
				{continueIf $productKey === 'newItemMarker'}

				{var $url = $urls['searchProduct']->toArray()}
{*				{php $url['params']['mutationId'] = $mutationId}*}

				{var $item = [
					inps: [
						[
							input: $productContainer['name'],
							placeholder: 'Zadejte název produktu',
							data: [
								suggestinp-target: 'input',
							]
						],
						[
							input: $productContainer['id'],
							data: [
								suggestinp-target: 'idInput',
							],
							classes: 'u-hide',
							type: 'hidden'

						]
					],
					btnsAfter: [
						[
							icon: $templates.'/part/icons/trash.svg',
							tooltip: 'Odstranit',
							variant: 'remove',
							data: [
								action: 'RemoveItem#remove'
							],
						]
					],
					data: [
						controller: 'RemoveItem SuggestInp',
						removeitem-target: 'item',
						suggestinp-target: 'wrapper',
						suggestinp-url-value: Nette\Utils\Json::encode($url),
					]

				]}
				{php $items[] = $item}
			{/foreach}

			{include $templates.'/part/box/list.latte',
				props: [
					data: [
						controller: 'List',
						List-name-value: 'similar',
					],
					listData: [
						List-target: 'list',
					],
					addData: [
						action: 'List#add',
					],
					add: true,
					dragdrop: true,
					type: 'input',
					items: $items
				]
			}

	{/block}
{/embed}
