<?php declare(strict_types = 1);

namespace App\AdminModule\Components\ProductForm\DTO;

use App\AdminModule\Components\ProductForm\DTO\Property\DuplicateButton;

class DTO
{

	private function __construct(
		public readonly DuplicateButton $duplicateButton,
	)
	{
	}

	public static function create(string $rsTemplatesPath): DTO
	{
		return new self(
			DuplicateButton::create($rsTemplatesPath)
		);
	}

}
