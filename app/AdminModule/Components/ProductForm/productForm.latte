{varType App\Model\Orm\Product\Product $object}

<form n:name="form" class="main__main" data-controller="pagemenu">
	{control messageForForm, $flashes, $form}

	<div class="main__header">
		{include './parts/header.latte', object=>$object}
	</div>
	<div class="main__content scroll" data-controller="ToggleAll">
		<p><button type="button" class="btn" data-action="ToggleAll#collapse"><span class="btn__text">Sbalit vše</span></button></p>
		{include './parts/content/content.latte', form=>$form}
		{include './parts/content/categories.latte', form=>$form}
		{include './parts/content/variants.latte', form=>$form}
		{include './parts/content/tags.latte', form=>$form}
		{include './parts/content/params.latte', form=>$form}
{*		{include './parts/content/paramsStatic.latte', form=>$form}*}
		{include './parts/content/imgs.latte', form=>$form}
		{include './parts/content/files.latte', form=>$form}
		{include './parts/content/pages.latte', form=>$form}
		{include './parts/content/products.latte', form=>$form}
		{include './parts/content/similarProducts.latte', form=>$form}
		{include './parts/content/seo.latte', form=>$form}
		{include './parts/content/validity.latte', form=>$form}

		{var $hasEnabledCf = false}
		{foreach $object->productLocalizations as $productLocalization}
			{if $productLocalization->getCfScheme()}
				{php $hasEnabledCf = true}
			{/if}
		{/foreach}

		{if $hasEnabledCf}
			{include './parts/content/custom-fields.latte', form=>$form}
		{/if}

		{var $hasEnabledCc = false}
		{foreach $object->productLocalizations as $productLocalization}
			{if $productLocalization->getCCModules()}
				{php $hasEnabledCc = true}
			{/if}
		{/foreach}

		{if $hasEnabledCc}
			{include './parts/content/custom-content.latte', form=>$form}
		{/if}


	</div>
	<div class="main__content-side scroll">
		{include './parts/side/btns.latte'}
{*		{include './parts/side/erpId.latte'}*}
		{include './parts/side/score.latte'}
		{include './parts/side/state.latte', form=>$form}
		{include './parts/side/lang.latte', form=>$form}
		{include './parts/side/duplicate.latte'}
		{include './parts/side/template.latte', form=>$form}
		{include './parts/side/edits.latte'}
	</div>

	{include './parts/overlay/editItem.latte', form=>$form}


	{capture $templateTargets}
		{include './parts/newItemTemplate.latte', form=>$form, product=>$product}
		{include $templates . '/part/core/libraryOverlay.latte'}
	{/capture}
</form>

{$templateTargets|noescape}
