<?php declare(strict_types = 1);

namespace App\AdminModule\Components\ProductForm;

use App\AdminModule\Components\ProductForm\Exception\FreeTransportException;
use App\Model\Orm\DeliveryMethod\DeliveryMethodConfigurationRepository;
use App\Model\Orm\Product\Product;
use Nette\Utils\ArrayHash;

final class ProductFormValidate
{

	public function __construct(
		private readonly DeliveryMethodConfigurationRepository $deliveryMethodConfigurationRepository,
	)
	{
	}

	final public function verifyFreeTransport(ArrayHash $values, Product $product): bool
	{
		if (!$product->firstVariant) {
			return false;
		}

		if ($product->firstVariant->getWeight()->isZero()) {
			return false;
		}

		if (!isset($values->productCommon->isFreeTransport)) {
			return false;
		}

		if (!$values->productCommon->isFreeTransport) {
			return false;
		}

		$limits = $this->deliveryMethodConfigurationRepository->getFreeDeliveriesLimits();

		if (!count($limits)) {
			throw new FreeTransportException(FreeTransportException::NO_FREE_TRANSPORTS);
		}

		foreach ($limits as $limit) {
			if (!isset($limit['maxWeight']) && $limit['maxWeight'] !== null) {
				continue;
			}

			if ($limit['maxWeight'] === null) {
				return true;
			}

			if ($product->firstVariant->getWeight()->isLessThan($limit['maxWeight'])) {
				return true;
			}
		}

		throw new FreeTransportException(FreeTransportException::NO_SUITABLE_TRANSPORT);
	}

}
