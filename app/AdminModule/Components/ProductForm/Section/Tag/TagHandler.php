<?php declare(strict_types=1);

namespace App\AdminModule\Components\ProductForm\Section\Tag;

use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\PostType\Tag\Model\Orm\TagLocalization\TagLocalizationRepository;
use Nette\Utils\ArrayHash;

class TagHandler
{
	public function __construct(
		private readonly TagLocalizationRepository $tagLocalizationRepository,
	)
	{
	}


	public function saveTags(ProductLocalization $productLocalization, ArrayHash $tagsFormData): void
	{
		$processedTagRowsIds = [];
		foreach ($tagsFormData as $key => $tagLocalizationFormData) {
			if ($key === 'newItemMarker') {
				continue;
			}

			$tagLocalization = $this->tagLocalizationRepository->getById($tagLocalizationFormData->id);
			if (!$tagLocalization) {
				continue;
			}
			$this->tagLocalizationRepository->replaceRelationRow($productLocalization->product, $tagLocalization, $tagLocalizationFormData->from, $tagLocalizationFormData->to);
			$processedTagRowsIds[] = (int) $tagLocalizationFormData->id;
		}

		foreach ($productLocalization->product->tagLocalizations->toCollection()->findBy(['mutation' => $productLocalization->mutation]) as $tagLocalization) {
			if (in_array($tagLocalization->getPersistedId(), $processedTagRowsIds)) {
				continue;
			}
			if ($tagLocalization->tag->type->isSystemTag()) {
				continue;
			}

			$this->tagLocalizationRepository->removeRelationRow($productLocalization->product, $tagLocalization);
		}
	}
}
