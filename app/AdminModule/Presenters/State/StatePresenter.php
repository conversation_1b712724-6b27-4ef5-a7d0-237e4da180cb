<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\State;

use App\AdminModule\Presenters\BasePresenter;
use App\AdminModule\Presenters\State\Components\DataGrid\DataGrid;
use App\AdminModule\Presenters\State\Components\DataGrid\DataGridFactory;

final class StatePresenter extends BasePresenter
{

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
	)
	{
		parent::__construct();
	}

	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create();
	}

}
