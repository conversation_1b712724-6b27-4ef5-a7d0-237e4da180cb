<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Newsletter;

use App\AdminModule\Presenters\BasePresenter;
use App\AdminModule\Presenters\Newsletter\Components\DataGrid\DataGrid;
use App\AdminModule\Presenters\Newsletter\Components\DataGrid\DataGridFactory;
use App\AdminModule\Presenters\Newsletter\Components\ShellForm\ShellForm;
use App\AdminModule\Presenters\Newsletter\Components\ShellForm\ShellFormFactory;
use App\Exceptions\UserException;
use App\Model\Orm\NewsletterEmail\NewsletterEmail;
use Exception;
use Nette\Application\AbortException;
use Nette\Application\UI\Form;
use Nextras\Orm\Entity\ToArrayConverter;
use Throwable;

final class NewsletterPresenter extends BasePresenter
{

	protected ?NewsletterEmail $object = null;

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
		private readonly ShellFormFactory $shellFormFactory,
	)
	{
		parent::__construct();
	}


	public function actionEdit(int $id): void
	{
		$this->object = $this->orm->newsletterEmail->getById($id);
	}

	/**
	 * @throws AbortException
	 */
	public function handleDelete(int $id): void
	{
		$email = $this->orm->newsletterEmail->getById($id);
		if (isset($email)) {
			$this->orm->newsletterEmail->remove($email);
			$this->orm->flush();
		}

		$this->redirect(':default');
	}


	protected function createComponentEditEmailForm(): Form
	{
		$form = new Form();
		$form->setTranslator($this->translator);

		$objectId = $this->object->id ?? null;

		$form->addHidden('id', $objectId);
		$form->addText('email', 'email')
			->addRule(Form::Email)
			->setHtmlType('email')
			->setRequired();
		$form->addSelect('mutation', 'mutation', $this->orm->mutation->findAll()->orderBy('langCode')->fetchPairs('id', 'langCode'))
			->setRequired();

		if ($objectId !== null) {
			$this->object = $this->orm->newsletterEmail->getById($objectId);
			$form->setDefaults($this->object->toArray(ToArrayConverter::RELATIONSHIP_AS_ID));
		}

		$form->addSubmit('save', 'Save');

		$form->onError[] = [$this, 'editEmailFormError'];
		$form->onSuccess[] = [$this, 'editEmailFormSucceeded'];

		return $form;
	}


	public function editEmailFormError(Form $form): void
	{
		if ($this->isAjax()) {
			$this->redrawControl();
		}
	}

	public function editEmailFormSucceeded(Form $form): void
	{
		$values = $form->getValues();

		try {
			if (($email = $this->orm->newsletterEmail->getById($this->object->id)) === null) {
				throw new Exception('Invalid email ID');
			}

			$this->orm->newsletterEmail->save($email, ['email' => $values->email, 'mutation' => $values->mutation]);

			$this->flashMessage('OK', 'ok');
		} catch (UserException $e) {
			$this->flashMessage('E-mail already exists', 'error');
			return;
		} catch (Throwable $e) {
			$this->flashMessage($e->getMessage(), 'error');
			return;
		}

		$this->orm->flush();
		$this->redirect('this');
	}


	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create();
	}

	protected function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create();
	}

}
