<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Newsletter\Components\ShellForm;

use App\AdminModule\Presenters\Newsletter\Components\ShellForm\FormData\BaseFormData;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Mutation\MutationRepository;
use App\Model\Orm\NewsletterEmail\NewsletterEmailModel;
use App\Model\Orm\NewsletterEmail\NewsletterEmailRepository;
use App\Model\Translator;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nette\Utils\Random;
use Nextras\Orm\Collection\ICollection;

class ShellForm extends Control
{
	/** @var ICollection<Mutation>  */
	private ICollection $mutations;

	public function __construct(
		private readonly Translator $translator,
		private readonly NewsletterEmailModel $newsletterEmailModel,
		private readonly MutationRepository $mutationRepository,
		private readonly NewsletterEmailRepository $newsletterEmailRepository,
	)
	{
		$this->onAnchor[] = [$this, 'init'];
	}


	public function init(): void
	{
		$this->mutations = $this->mutationRepository->findBy([]);
	}

	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$template->add('templates', RS_TEMPLATE_DIR);
		$template->add('mutations', $this->mutations);

		$template->render(__DIR__ . '/shellForm.latte');
	}


	protected function createComponentForm(): Form
	{
		$form = new Form();
		$form->setMappedType(BaseFormData::class);
		$form->setTranslator($this->translator);

		$form->addEmail('email', 'email')->setRequired();
		$form->addSelect('mutation', 'select_mutation', $this->mutations->fetchPairs('id', 'name'))->setRequired();
		$form->addSubmit('send', 'send');

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];
		$form->onValidate[] = [$this, 'formValidate'];
		return $form;
	}



	public function formValidate(Form $form, BaseFormData $values): void
	{
		$mutation = $this->mutationRepository->getById($values->mutation);

		if ($mutation === null) {
			$form->addError('invalid_mutation');
			$this->flashMessage('invalid_mutation', 'error');
		}

		$newsletterEmail = $this->newsletterEmailRepository->getBy([
			'email' => $values->email,
			'mutation' => $values->mutation,
		]);

		if ($newsletterEmail !== null) {
			$form->addError('email_already_subscribed');
			$this->flashMessage('email_already_subscribed', 'error');
		}
	}

	public function formError(Form $form): void
	{
		if ($form->getErrors() === []) {
			$this->flashMessage('Error', 'error');
		}

		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSucceeded(Form $form, BaseFormData $data): void
	{
		$mutation = $this->mutationRepository->getById($data->mutation);
		$this->newsletterEmailModel->create($data->email, $mutation);
		$this->presenter->redirect('this');
	}

}
