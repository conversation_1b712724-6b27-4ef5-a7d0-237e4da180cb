{layout $templates.'/@layout-new.latte'}

{var $hrefClose = '/superadmin/styleguide/box-header'}
{var $localizations = ['cs', 'en', 'de']}
{var $items = [
	[href: '#content', icon: $templates.'/part/icons/align-left.svg', tooltip: 'Obsah', linkType: 'toggle'],
	[href: '#categories', icon: $templates.'/part/icons/folder.svg', tooltip: 'Kate<PERSON>ie', linkType: 'toggle'],
	[href: '#variants', icon: $templates.'/part/icons/coins.svg', tooltip: 'Varianty', linkType: 'toggle'],
	[href: '#params', icon: $templates.'/part/icons/sliders-h.svg', tooltip: 'Parametry', linkType: 'toggle'],
	[href: '#images', icon: $templates.'/part/icons/images.svg', tooltip: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', linkType: 'toggle'],
	[href: '#videos', icon: $templates.'/part/icons/youtube.svg', tooltip: 'Videa', linkType: 'toggle'],
	[href: '#files', icon: $templates.'/part/icons/file-archive.svg', tooltip: 'Soubory', linkType: 'toggle'],
	[href: '#pages', icon: $templates.'/part/icons/file.svg', tooltip: 'Stránky', linkType: 'toggle'],
	[href: '#products', icon: $templates.'/part/icons/barcode.svg', tooltip: 'Produkty', linkType: 'toggle'],
	[href: '#seo', icon: $templates.'/part/icons/google.svg', tooltip: 'SEO', linkType: 'toggle'],
	[href: '#validity', icon: $templates.'/part/icons/calendar-alt.svg', tooltip: 'Platnost', linkType: 'toggle'],
	[href: '#vats', icon: $templates.'/part/icons/coins.svg', tooltip: 'Sazby DPH', linkType: 'toggle'],
]}
{var $toggle = [
	title: 'Content in toggle',
	id: 'contentInToggle',
	variant: 'main',
	classes: ['u-mb-0'],
	rowMain: false
]}

{block #content}
	<div class="main__main main__main--nospacing" data-controller="pagemenu">
		<div class="main__header scroll">

			{include $templates.'/part/box/header.latte',
				props: [
					title: 'Simple header',
				]
			}

			{include $templates.'/part/box/header.latte',
				props: [
					title: 'Stimulus header',
					isPageTitle: true,
				]
			}

			{include $templates.'/part/box/header.latte',
				props: [
					title: 'Backlink header, custom classes',
					hrefClose: $hrefClose,
					classes: 'u-color-primary u-font-italic',
				]
			}

			{include $templates.'/part/box/header.latte',
				props: [
					title: 'Header in overlay',
					variant: 'overlay',
					hrefClose: '#overlay-IDHERE',
				]
			}

			{include $templates.'/part/box/header.latte',
				props: [
					title: 'Simple label header',
					langCodes: $localizations,
				]
			}

			{include $templates.'/part/box/header.latte',
				props: [
					title: 'Header with menu',
					menu: [
						items: $items
					],
				]
			}

			{include $templates.'/part/box/header.latte',
				props: [
					title: 'Header with menu and image',
					menu: [items: $items],
					img: $baseUrl.'/admin/new/dist/img/illust/sample.jpg',
				]
			}

			{include $templates.'/part/box/header.latte',
				props: [
					title: 'Header with public localization',
					publicLocalizations: $localizations,
				]
			}

			{embed $templates.'/part/box/header.latte',
				templates: $templates,
				props: [
					title: 'Embed header / backlink / public localization / menu and image',
					hrefClose: $hrefClose,
					publicLocalizations: $localizations,
					menu: [items: $items],
					img: $baseUrl.'/admin/new/dist/img/illust/sample.jpg',
				]
			}{/embed}

			{include $templates.'/part/box/header.latte',
				props: [
					title: 'If exist title and titleTranslation then show title, try remove title',
					titleTranslation: 'hide_first_image',
				]
			}

			{include $templates.'/part/box/header.latte',
				props: [
					title: 'Automatic generated menu from toggle in content',
					hasGeneratedMenu: true,
				]
			}

		</div>

		<div class="main__content scroll">
			{include $templates.'/part/box/toggle.latte', props: $toggle}
		</div>

		<div class="main__content-side scroll u-mb-last-0">
			<p>
				Space for side components (is not required)
			</p>
		</div>
	</div>
{/block}
