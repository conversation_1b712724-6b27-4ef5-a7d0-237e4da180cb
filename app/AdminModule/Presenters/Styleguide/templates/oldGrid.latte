{block content}
	<h1><PERSON><PERSON>y</h1>
	<ol>
		<li>1/2</li>
		<li>1/3, 2/3</li>
		<li>1/4, 2/4, 3/4</li>
		<li>1/5, 2/5, 3/5, 4/5</li>
		<li>Grid definuje class "grid-row"</li>
		<li>Následující elementy jsou sloupce v gridu</li>
		<li>Gridy lze do sebe zanořovat. Stačí vytvořit vnitřní "grid-row"</li>
		<li>
			Šířka gridu definuje class "grid-(<em>počet dílů</em>)-(<em>z čeho</em>)"<br />
			např. dvě pětiny se zapíší "grid-2-5"
		</li>
	</ol>
	<div class="grid-row margin-h2">
		<div class="grid-1-2">
			<h2>1/2</h2>
			<p>
				Lorem ipsum dolor sit amet, consectetur adipisicing elit. Obcaecati, quasi, non aperiam soluta dolor ullam sunt voluptates minima dicta fuga excepturi deleniti rerum dolorum incidunt accusamus! Temporibus sint saepe nisi!
			</p>
		</div>
		<div class="grid-1-2">
			<h2>1/2</h2>
			<p>
				Lorem ipsum dolor sit amet, consectetur adipisicing elit. Obcaecati, quasi, non aperiam soluta dolor ullam sunt voluptates minima dicta fuga excepturi deleniti rerum dolorum incidunt accusamus! Temporibus sint saepe nisi!
			</p>
		</div>
	</div>
	<div class="grid-row margin-h2">
		<div class="grid-1-3">
			<h2>1/3</h2>
			<p>
				Lorem ipsum dolor sit amet, consectetur adipisicing elit. Obcaecati, quasi, non aperiam soluta dolor ullam sunt voluptates minima dicta fuga excepturi deleniti rerum dolorum incidunt accusamus! Temporibus sint saepe nisi!
			</p>
		</div>
		<div class="grid-2-3">
			<h2>2/3</h2>
			<p>
				Lorem ipsum dolor sit amet, consectetur adipisicing elit. Obcaecati, quasi, non aperiam soluta dolor ullam sunt voluptates minima dicta fuga excepturi deleniti rerum dolorum incidunt accusamus! Temporibus sint saepe nisi!
			</p>
		</div>
	</div>
	<div class="grid-row margin-h2">
		<div class="grid-1-4">
			<h2>1/4</h2>
			<p>
				Lorem ipsum dolor sit amet, consectetur adipisicing elit. Obcaecati, quasi, non aperiam soluta dolor ullam sunt voluptates minima dicta fuga excepturi deleniti rerum dolorum incidunt accusamus! Temporibus sint saepe nisi!
			</p>
		</div>
		<div class="grid-1-4">
			<h2>1/4</h2>
			<p>
				Lorem ipsum dolor sit amet, consectetur adipisicing elit. Obcaecati, quasi, non aperiam soluta dolor ullam sunt voluptates minima dicta fuga excepturi deleniti rerum dolorum incidunt accusamus! Temporibus sint saepe nisi!
			</p>
		</div>
		<div class="grid-1-4">
			<h2>1/4</h2>
			<p>
				Lorem ipsum dolor sit amet, consectetur adipisicing elit. Obcaecati, quasi, non aperiam soluta dolor ullam sunt voluptates minima dicta fuga excepturi deleniti rerum dolorum incidunt accusamus! Temporibus sint saepe nisi!
			</p>
		</div>
		<div class="grid-1-4">
			<h2>1/4</h2>
			<p>
				Lorem ipsum dolor sit amet, consectetur adipisicing elit. Obcaecati, quasi, non aperiam soluta dolor ullam sunt voluptates minima dicta fuga excepturi deleniti rerum dolorum incidunt accusamus! Temporibus sint saepe nisi!
			</p>
		</div>
	</div>
	<div class="grid-row margin-h2">
		<div class="grid-1-4">
			<h2>1/4</h2>
			<p>
				Lorem ipsum dolor sit amet, consectetur adipisicing elit. Obcaecati, quasi, non aperiam soluta dolor ullam sunt voluptates minima dicta fuga excepturi deleniti rerum dolorum incidunt accusamus! Temporibus sint saepe nisi!
			</p>
		</div>
		<div class="grid-3-4">
			<h2>3/4</h2>
			<p>
				Lorem ipsum dolor sit amet, consectetur adipisicing elit. Obcaecati, quasi, non aperiam soluta dolor ullam sunt voluptates minima dicta fuga excepturi deleniti rerum dolorum incidunt accusamus! Temporibus sint saepe nisi!
			</p>
		</div>
	</div>
	<div class="grid-row margin-h2">
		<div class="grid-1-5">
			<h2>1/5</h2>
			<p>
				Lorem ipsum dolor sit amet, consectetur adipisicing elit. Obcaecati, quasi, non aperiam soluta dolor ullam sunt voluptates minima dicta fuga excepturi deleniti rerum dolorum incidunt accusamus! Temporibus sint saepe nisi!
			</p>
		</div>
		<div class="grid-1-5">
			<h2>1/5</h2>
			<p>
				Lorem ipsum dolor sit amet, consectetur adipisicing elit. Obcaecati, quasi, non aperiam soluta dolor ullam sunt voluptates minima dicta fuga excepturi deleniti rerum dolorum incidunt accusamus! Temporibus sint saepe nisi!
			</p>
		</div>
		<div class="grid-1-5">
			<h2>1/5</h2>
			<p>
				Lorem ipsum dolor sit amet, consectetur adipisicing elit. Obcaecati, quasi, non aperiam soluta dolor ullam sunt voluptates minima dicta fuga excepturi deleniti rerum dolorum incidunt accusamus! Temporibus sint saepe nisi!
			</p>
		</div>
		<div class="grid-1-5">
			<h2>1/5</h2>
			<p>
				Lorem ipsum dolor sit amet, consectetur adipisicing elit. Obcaecati, quasi, non aperiam soluta dolor ullam sunt voluptates minima dicta fuga excepturi deleniti rerum dolorum incidunt accusamus! Temporibus sint saepe nisi!
			</p>
		</div>
		<div class="grid-1-5">
			<h2>1/5</h2>
			<p>
				Lorem ipsum dolor sit amet, consectetur adipisicing elit. Obcaecati, quasi, non aperiam soluta dolor ullam sunt voluptates minima dicta fuga excepturi deleniti rerum dolorum incidunt accusamus! Temporibus sint saepe nisi!
			</p>
		</div>
	</div>
	<div class="grid-row margin-h2">
		<div class="grid-2-5">
			<h2>2/5</h2>
			<p>
				Lorem ipsum dolor sit amet, consectetur adipisicing elit. Obcaecati, quasi, non aperiam soluta dolor ullam sunt voluptates minima dicta fuga excepturi deleniti rerum dolorum incidunt accusamus! Temporibus sint saepe nisi!
			</p>
		</div>
		<div class="grid-3-5">
			<h2>3/5</h2>
			<p>
				Lorem ipsum dolor sit amet, consectetur adipisicing elit. Obcaecati, quasi, non aperiam soluta dolor ullam sunt voluptates minima dicta fuga excepturi deleniti rerum dolorum incidunt accusamus! Temporibus sint saepe nisi!
			</p>
		</div>
	</div>
{/block}
