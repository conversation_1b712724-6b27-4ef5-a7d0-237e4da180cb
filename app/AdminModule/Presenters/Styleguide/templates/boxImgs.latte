{layout $templates.'/@layout-new.latte'}

{var $image1 = [
	img: $baseUrl.'/admin/new/dist/img/illust/sample.jpg',
	name: 'Simple image'
]}
{var $image2 = [
	img: $baseUrl.'/admin/new/dist/img/illust/sample.jpg',
	name: 'Image with buttons',
	dragdrop: true,
	btns: [
		[
			icon: $templates.'/part/icons/pencil-alt.svg',
			tooltip: 'Editovat',
			data: [
				controller: 'Toggle',
				action: 'Toggle#changeClass',
				toggle-target-value: '#overlay-newItemMarker',
				toggle-target-class-value: 'is-visible',
			],
		],
		[
			icon: $templates.'/part/icons/trash.svg',
			tooltip: 'Odstranit',
			variant: 'remove',
			data: [
				action: 'RemoveItem#remove ImageList#removeImg'
			],
		]
	]
]}
{var $image3 = [
	img: $baseUrl.'/admin/new/dist/img/illust/sample.jpg',
	name: 'Selected image with buttons and stimulus',
	isSelected: true,
	data: [
		controller: 'RemoveItem',
		removeitem-target: 'item',
		removeitem-animation-value: 'fade',
		id: 'imageId'
	],
	dragdrop: true,
	btns: [
		[
			icon: $templates.'/part/icons/pencil-alt.svg',
			tooltip: 'Editovat',
			data: [
				controller: 'Toggle',
				action: 'Toggle#changeClass',
				toggle-target-value: '#overlay-newItemMarker',
				toggle-target-class-value: 'is-visible',
			],
		],
		[
			icon: $templates.'/part/icons/trash.svg',
			tooltip: 'Odstranit',
			variant: 'remove',
			data: [
				action: 'RemoveItem#remove ImageList#removeImg'
			],
		]
	]
]}




{block #content}
	<div class="main__main main__main--nospacing">
		<div class="main__header">
			<div class="b-header">
				<h1 class="b-header__title">
					Images
				</h1>
			</div>
		</div>

		<div class="main__content scroll">

			{include $templates.'/part/box/imgs.latte'}
{*  
				{include $templates.'/part/box/imgs.latte',
					props: [
						add: true,
						file: true,
						dataAdd: [
							url: '/superadmin/library/',
							folder-id: $object->id,
						],
						dataList: [
							imagelibrary-target: 'list',
						],
						items: $items,
						classes: ['b-imgs--selectable', 'u-mb-sm'],
					]
				}

				{include $templates.'/part/box/imgs.latte',
					props: [
						add: true,
						data: [
							controller: 'ImageList',
							imagelist-ids-value: json_encode($ids),
						],
						dataList: [
							imagelist-target: 'list',
						],
						dataAdd: [
							controller: 'Toggle',
							action: 'Toggle#changeClass ImageList#newImg',
							toggle-target-value: '#overlay-library',
							toggle-target-class-value: 'is-visible'
						],
						dragdrop: true,
						items: $items
					]
				}
*}




		{include $templates.'/part/box/image-item.latte', props: $image1}
		{include $templates.'/part/box/image-item.latte', props: $image2}
		{include $templates.'/part/box/image-item.latte', props: $image3}


		</div>

		<div class="main__content-side scroll u-mb-last-0">
			<p>
				Space for side components (is not required)
			</p>
		</div>
	</div>
{/block}
