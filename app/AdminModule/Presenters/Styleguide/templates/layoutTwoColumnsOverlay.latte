{layout $templates.'/@layout-new.latte'}

{block #content}
	<div class="main__main">
		<div class="main__header">
			<div class="b-header">
				<h1 class="b-header__title">
					Header (is not required)
				</h1>
			</div>
		</div>

		<div class="main__content scroll">
			<div class="row-main">
				<p>
					<a href="#overlay-id" class="btn" data-controller="Toggle" data-action="Toggle#changeClass" data-toggle-target-class-value="is-visible">
						<span class="btn__text">
							Show overlay
						</span>
					</a>
				</p>
			</div>
		</div>

		<div class="main__content-side">
			<p>
				Space for side components
			</p>
		</div>

		{embed $templates.'/part/core/overlay.latte', props: [
			id: 'id',
			title: 'Overlay title',
		], templates=>$templates}
			{block content}
				<p>
					Overlay content
				</p>
			{/block}
		{/embed}
	</div>
{/block}
