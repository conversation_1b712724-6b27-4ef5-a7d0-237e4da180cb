{default $nameSuffix = ''}

<h2 class="h3 u-mb-sm">
	{_'title_personal_info'}
</h2>

<div class="u-mb-xs">
	<div class="grid grid--x-xs grid--y-xxs">
		<div class="grid__cell size--6-12@sm">
			{include 'inp.latte', form: $form, name: 'inv_firstname'.$nameSuffix, required: true, class: 'u-mb-0', error: true}
		</div>
		<div class="grid__cell size--6-12@sm">
			{include 'inp.latte', form: $form, name: 'inv_lastname'.$nameSuffix, required: true, class: 'u-mb-0', error: true}
		</div>
		<div class="grid__cell size--6-12@sm">
			{include 'inp.latte', form: $form, name: 'inv_phone'.$nameSuffix, type: 'tel'}
		</div>
		<div class="grid__cell size--6-12@sm">
			{include 'inp.latte', form: $form, name: 'inv_street'.$nameSuffix, required: true, class: 'u-mb-0', error: true}
		</div>
		<div class="grid__cell size--6-12@sm">
			{include 'inp.latte', form: $form, name: 'inv_city'.$nameSuffix, required: true, class: 'u-mb-0', error: true}
		</div>
		<div class="grid__cell size--6-12@sm">
			{include 'inp.latte', form: $form, name: 'inv_zip'.$nameSuffix, required: true, class: 'u-mb-0', error: true}
		</div>
		<div class="grid__cell size--6-12@sm">
			{include 'inp.latte', form: $form, name: 'inv_state'.$nameSuffix, class: 'u-mb-0', error: true}
		</div>
	</div>
</div>

<div n:class="f-open, u-mb-xs">
	<div n:class="f-open__box">
		<div class="grid grid--x-xs grid--y-xxs">
			<div class="grid__cell">
				{include 'inp.latte', form: $form, name: 'inv_company' . $nameSuffix, class:'u-mb-0', required: true, error: true}
			</div>
			<div class="grid__cell size--6-12@sm">
				{include 'inp.latte', form: $form, name: 'inv_ic' . $nameSuffix, class:'u-mb-0', required: true, error: true}
			</div>
			<div class="grid__cell size--6-12@sm">
				{include 'inp.latte', form: $form, name: 'inv_dic' . $nameSuffix, class:'u-mb-0', error: true}
			</div>
		</div>
	</div>
</div>

<div n:class="f-open">
	<div class="f-open__box u-pt-sm">
		<h2 class="h3 u-mb-sm">
			{_'title_delivery_address'}
		</h2>
		<div class="grid grid--x-xs grid--y-xxs">
			<div class="grid__cell size--6-12@sm">
				{include 'inp.latte', form: $form, name: 'del_firstname' . $nameSuffix, class:'u-mb-0', required: true, error: true}
			</div>
			<div class="grid__cell size--6-12@sm">
				{include 'inp.latte', form: $form, name: 'del_lastname' . $nameSuffix, class:'u-mb-0', required: true, error: true}
			</div>
			<div class="grid__cell size--6-12@sm">
				{include 'inp.latte', form: $form, name: 'del_company' . $nameSuffix, class:'u-mb-0', required: true, error: true}
			</div>
			<div class="grid__cell size--6-12@sm">
				{include 'inp.latte', form: $form, name: 'del_phone' . $nameSuffix, type: 'tel', class: 'u-mb-0', error: true}
			</div>
			<div class="grid__cell size--6-12@sm">
				{include 'inp.latte', form: $form, name: 'del_street' . $nameSuffix, class:'u-mb-0', required: true, error: true}
			</div>
			<div class="grid__cell size--6-12@sm">
				{include 'inp.latte', form: $form, name: 'del_city' . $nameSuffix, class:'u-mb-0', required: true}
			</div>
			<div class="grid__cell size--6-12@sm">
				{include 'inp.latte', form: $form, name: 'del_zip' . $nameSuffix, class:'u-mb-0', required: true}
			</div>
			<div class="grid__cell size--6-12@sm">
				{include 'inp.latte', form: $form, name: 'del_state' . $nameSuffix, class: 'u-mb-0'}
			</div>
		</div>
	</div>
</div>
