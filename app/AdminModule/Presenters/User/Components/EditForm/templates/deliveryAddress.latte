{*		{control formMessage, $flashes, $form}*}
<div n:if="isset($customAddress)" class="b-user-address">
	{foreach $customAddress as $k=>$address}
		<div class="b-address u-mb-md">
			<p class="grid grid--x-xs grid--y-xxs">
				<span class="grid__cell size--auto">
					{capture $confirmMsg}
						{if empty($address->delStreet)}
							{$address->invFirstname} {$address->invLastname}, {$address->invStreet}, {$address->invZip} {$address->invCity}
						{else}
							{$address->delFirstname} {$address->delLastname}, {$address->delStreet}, {$address->delZip} {$address->delCity}
						{/if}
					{/capture}
				</span>
			</p>

			<div n:class="f-open, 'f-open--'.$iterator->getCounter()">
				<div n:class="f-open__box, u-pt-sm"> {*\SuperKoderi\Components\OrderStep2Form::hasBoxError($form, $k)*}
					{include 'addressBox.latte', form: $form, nameSuffix: '_'.$k}
				</div>
			</div>
			<p class="u-mb-md">
				<a n:href="deleteCustomAddress!, key:  $k" class="btn btn-red btn-icon-before" data-controller="Confirm" data-Confirm-msg-value="{_confirm_delete_address|replace:'%address',preg_replace('/\t/', '', $confirmMsg)}">
					<span>
						<span class="icon icon-cancel-circle"></span>
						{_'btn_delete'}
					</span>
				</a>
			</p>
			<hr class="u-mb-0">
		</div>
	{/foreach}
</div>

