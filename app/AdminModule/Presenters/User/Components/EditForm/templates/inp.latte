{default $class = false}
{default $cols = false}
{default $rows = false}
{if is_string($name)}
	{default $type = isset($form[$name]) ? $form[$name]->getOption('type') : false}
	{default $label = isset($form[$name]) ? ($type == 'checkbox' ? $form[$name]->caption : $form[$name]->label->getText()) : ''}
	{default $disabled = isset($form[$name]) ? $form[$name]->isDisabled() : false}
	{default $required = isset($form[$name]) ? $form[$name]->isRequired() : false}
	{default $hasError = $form[$name]->errors ?? false}
{elseif $name instanceof Nette\Forms\Control}
	{default $type = $name->getOption('type')}
	{default $label = $type === 'checkbox' ? $name->caption : $name->label->getText()}
	{default $disabled = $name->isDisabled()}
	{default $required = $name->isRequired()}
	{default $hasError = $name->errors ?? false}
{/if}
{default $labelLang = false}
{default $inpClass = false}
{default $labelClass = false}
{default $labelReplace = false}
{default $error = false}
{default $placeholderLang = false}
{default $tabindex = false}
{default $dataAction = false}
{default $placeholder = ''}

{if is_string($placeholderLang)}
	{capture $placeholder}{translate}{$placeholderLang}{/translate}{/capture}
{/if}
{capture $label}{if $labelLang}{translate}{$labelLang}{/translate}{else}{$label}{/if}{if $label && $type != 'checkbox'}:{/if}{if $required && $label} <span class="inp-required">*</span>{/if}{/capture}

<p n:class="$hasError ? has-error, $class">
	{if $type == 'checkbox'}
		<label n:class="inp-item, inp-item--checkbox, $labelClass">
			<input n:if="!($name instanceof Nette\Forms\Control)" type="checkbox" name="{$name}" value="1"{if $form[$name]->value == 1} checked="checked"{/if} n:class="inp-item__inp, $inpClass"{if $tabindex} tabindex="{$tabindex}"{/if}{if $dataAction} data-action="{$dataAction}"{/if}>
			<input n:if="($name instanceof Nette\Forms\Control)" n:name="$name" n:class="inp-item__inp, $inpClass"{if $dataAction} data-action="{$dataAction}"{/if}>
			<span class="inp-item__text">
				{if $labelReplace}
					{$label|replace:"%link%",$labelReplace|noescape}
				{else}
					{$label|noescape}
				{/if}
			</span>
		</label>
	{else}
		<label n:name="{$name}" n:class="inp-label, $labelClass">{$label|noescape}</label>

		{if $type == 'tel'}
			<span class="inp-fix" data-controller="inp-phone">
				{input $name, type=>$type, class=>implode(' ', ['inp-text', $inpClass]), data-inp-phone-target=>'inp', placeholder=>$placeholder, tabindex=>$tabindex}
			</span>
		{else}
				<span class="inp-fix">
					{input $name class=>implode(' ', ['inp-text', $inpClass]), cols=>$cols, rows=>$rows, disabled=>$disabled, placeholder=>$placeholder, tabindex=>$tabindex}

					<span n:if="$error == true" class="inp-error"></span>
				</span>
		{/if}
	{/if}
</p>
