<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\User\Components\DataGrid;

use App\Model\Orm\Orm;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserRepository;
use App\Model\Translator;
use App\PostType\Core\AdminModule\Components\DataGrid\HasMutationColumn;
use Nette\Application\UI\Control;

class DataGrid extends Control
{

	use HasMutationColumn;

	public function __construct(
		private readonly User $userEntity,
		private readonly Translator $translator,
		private readonly Orm $orm,
		private readonly UserRepository $userRepository,
	)
	{
	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->render(__DIR__ . '/dataGrid.latte');
	}


	public function createComponentGrid(): \Ublaboo\DataGrid\DataGrid
	{
		$roles = [
			null => $this->translator->translate('filter_user_all'),
		];
		$roles = array_merge($roles, User::getConstsByPrefix('ROLE_'));

		$grid = new \Ublaboo\DataGrid\DataGrid();
		if ($this->userEntity->role !== User::ROLE_DEVELOPER) {
			$users = $this->userRepository->findBy([
				'role!=' => User::ROLE_DEVELOPER,
			]);

			unset($roles[User::ROLE_DEVELOPER]);
		} else {
			$users = $this->userRepository->findBy([]);
		}

		$grid->setDataSource($users);
		$grid->addColumnText('email', 'email')->setSortable()->setFilterText();
		$grid->addColumnText('lastname', 'lastname')->setSortable()->setFilterText();
		$grid->addColumnText('firstname', 'firstname')->setSortable()->setFilterText();
		$grid->addColumnText('role', 'role')->setFilterSelect($roles);
		$grid->addColumnDateTime('createdTime', 'createdTime')->setFormat('Y-m-d H:i:s');

		$this->addColumnMutation($grid);

		$grid->addAction('edit', 'Edit', ':edit')->setClass('btn btn-xs btn-primary');

		$grid->setTranslator($this->translator);

		$grid->setDefaultSort('lastname');

		return $grid;
	}

}
