<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\User;

use App\AdminModule\Presenters\User\Components\DataGrid\DataGrid;
use App\AdminModule\Presenters\User\Components\DataGrid\DataGridFactory;
use App\AdminModule\Presenters\User\Components\EditForm\EditForm;
use App\AdminModule\Presenters\User\Components\EditForm\EditFormFactory;
use App\AdminModule\Presenters\User\Components\ShellForm\ShellForm;
use App\AdminModule\Presenters\User\Components\ShellForm\ShellFormFactory;
use App\AdminModule\Presenters\BasePresenter;
use App\Model\Orm\User\User;

/**
 * @property User $object
 */
final class UserPresenter extends BasePresenter
{

	private User $object;

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
		private readonly EditFormFactory $editFormFactory,
		private readonly ShellFormFactory $shellFormFactory,
	)
	{
		parent::__construct();
	}


	public function actionEdit(int $id): void
	{
		if ($this->user->isDeveloper()) {
			$users = $this->orm->user->findBy([]);
		} else {
			$users = $this->orm->user->findBy([
				'role!=' => User::ROLE_DEVELOPER,
			]);
		}

		$object = $users->getById($id);
		if ($object === null) {
			$this->redirect('default');
		}

		$this->object = $object;
	}


	protected function createComponentEditForm(): EditForm
	{
		$mutations = $this->orm->mutation->findAll()->fetchPairs('id', 'name');
		return $this->editFormFactory->create($this->object, $mutations, $this->userEntity);
	}

	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create($this->userEntity);
	}


	protected function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create();
	}

}
