<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\DeliveryMethod\Components\DataGrid;

use App\Model\Orm\DeliveryMethod\DeliveryMethodConfiguration;
use App\Model\Orm\DeliveryMethod\DeliveryMethodConfigurationRepository;
use App\Model\Orm\DeliveryMethod\DeliveryMethodPrice;
use App\Model\Translator;
use Nette\Application\UI\Control;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Ublaboo\DataGrid\Exception\DataGridException;

/**
 * @property-read DefaultTemplate $template
 */
class DataGrid extends Control
{

	public function __construct(
		private readonly Translator $translator,
		private readonly DeliveryMethodConfigurationRepository $deliveryMethodConfigurationRepository,
	)
	{
	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);

		$template->render(__DIR__ . '/dataGrid.latte');
	}


	/**
	 * @throws DataGridException
	 */
	public function createComponentGrid(): \Ublaboo\DataGrid\DataGrid
	{
		$grid = new \Ublaboo\DataGrid\DataGrid();

		$grid->setDataSource($this->deliveryMethodConfigurationRepository->findAll()->orderBy('mutation'));
		$grid->addColumnText('mutation', 'label_mutation', 'mutation.name');
		$grid->addColumnText('deliveryMethodUniqueIdentifier', 'delivery_method_unique_identifier');
		$grid->addColumnText('name', 'name');
		$grid->addColumnText('public', 'public')->setReplacement([
			1 => $this->translator->translate('public'),
			0 => $this->translator->translate('non-public'),
		]);
		$grid->addColumnText('price', 'price')->setRenderer(function (DeliveryMethodConfiguration $methodConfiguration) {
			if (($methodConfiguration->prices->countStored()) === 0) {
				return '<span style="color:red;">without prices</span>';
			}
			/** @var DeliveryMethodPrice $price */
			$price = $methodConfiguration->prices->toCollection()->getBy([]);

			return $price->price->asMoney();
		})->setTemplateEscaping(false);

		$grid->addColumnText('allowedPaymentMethods', 'allowedPaymentMethods')->setRenderer(function (DeliveryMethodConfiguration $methodConfiguration) {
			return implode(', ', $methodConfiguration->allowedPaymentMethods->toCollection()->fetchPairs(null, 'paymentMethodUniqueIdentifier'));
		});

		$grid->addColumnText('allowedCurrencies', 'allowedCurrencies')->setRenderer(function (DeliveryMethodConfiguration $methodConfiguration) {
			return implode(', ', $methodConfiguration->currencies->toCollection()->fetchPairs(null, 'currency'));
		});

		$grid->addAction('edit', 'Edit', ':edit')->setClass('btn btn-xs btn-primary');

		$grid->setTranslator($this->translator);

		return $grid;
	}

}
