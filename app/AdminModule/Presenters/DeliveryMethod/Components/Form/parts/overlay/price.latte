
{formContainer $pricesContainer}
	{embed $templates.'/part/box/toggle.latte', props=>[
		title: 'Nastavení',
		id: 'price-options-'.$priceId,
		open: true,

	], templates=>$templates}
		{block content}
			{include $templates.'/part/core/inp.latte' props: [
				input: $pricesContainer['externalId'],
				classesLabel: ['title'],
			]}
		{/block}
	{/embed}
	{embed $templates.'/part/box/toggle.latte', props=>[
	title: 'Platná pro',
	id: 'price-setting-'.$priceId,
	open: true,

	], templates=>$templates}
		{block content}
			<div style="display: none">
			{include $templates.'/part/core/inp.latte' props: [
				input: $pricesContainer['priceLevel'],
				type: 'select',
				classesLabel: ['title'],
				dataInp: [
					DeliveryPriceEdit-target: 'tag',
					action: 'change->DeliveryPriceEdit#edit',
				],
			]}
			</div>
			{include $templates.'/part/core/inp.latte' props: [
				input: $pricesContainer['state'],
				type: 'select',
				classesLabel: ['title'],
				dataInp: [
					DeliveryPriceEdit-target: 'name',
					action: 'change->DeliveryPriceEdit#edit',
				],
			]}
			{include $templates.'/part/core/inp.latte' props: [
				input: $pricesContainer['currency'],
				type: 'select',
				classesLabel: ['title'],
				dataInp: [
					action: 'change->DeliveryPriceEdit#changeCurrency'
				]
			]}
		{/block}
	{/embed}
	{embed $templates.'/part/box/toggle.latte', props=>[
		title: 'Cena za dopravu',
		id: 'price-'.$priceId,
		open: true,

	], templates=>$templates}
		{block content}
			{include $templates.'/part/core/inp.latte' props: [
				input: $pricesContainer['price'],
				type: 'number',
				classesLabel: ['title'],
				prefix: $pricesContainer['currency']->getValue(),
				prefixDataInp: [
					DeliveryPriceEdit-target: 'currency',
				],
				dataInp: [
					DeliveryPriceEdit-target: 'price',
					action: 'input->DeliveryPriceEdit#edit',
					currency: $pricesContainer['currency']->getValue(),
				],
			]}
		{/block}
	{/embed}
	{embed $templates.'/part/box/toggle.latte', props=>[
		title: 'Ostatní podmínky',
		id: 'price-conditions-'.$priceId,
		open: true,

	], templates=>$templates}
		{block content}
			{include $templates.'/part/core/inp.latte' props: [
				input: $pricesContainer['freeFrom'],
				type: 'number',
				classesLabel: ['title'],
				prefix: $pricesContainer['currency']->getValue(),
				prefixDataInp: [
					DeliveryPriceEdit-target: 'currency',
				],
				dataInp: [
					DeliveryPriceEdit-target: 'freeFrom',
					action: 'input->DeliveryPriceEdit#edit',
				],
			]}
			{include $templates.'/part/core/inp.latte' props: [
				input: $pricesContainer['maxCodPrice'],
				type: 'number',
				classesLabel: ['title'],
				prefixDataInp: [
					DeliveryPriceEdit-target: 'currency',
				],
				prefix: $pricesContainer['currency']->getValue()
			]}
			{include $templates.'/part/core/inp.latte' props: [
				input: $pricesContainer['maxWeight'],
				type: 'number',
				classesLabel: ['title'],
				prefix: 'g'
			]}
		{/block}
	{/embed}
{/formContainer}
