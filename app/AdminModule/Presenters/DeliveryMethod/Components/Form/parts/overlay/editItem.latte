<div data-Templates-target="overlays">
	{foreach $form['prices']->getComponents() as $priceId=>$pricesContainer}
		{continueIf $priceId === 'newItemMarker'}


		{embed $templates.'/part/core/overlay.latte', props: [
			id: 'price_'.$priceId ,
			title: 'Editace / Přidání ceny za dopravu',
			data: [
			controller: 'DeliveryPriceEdit',
			DeliveryPriceEdit-id-value: 'price_'.$priceId ,
		],
		], templates: $templates}
			{block content}
				{var $price = $deliveryMethodConfiguration->prices->toCollection()->getById($priceId)}
				{include './price.latte', price: $price, priceId:$priceId, pricesContainer:$pricesContainer}
			{/block}
		{/embed}
	{/foreach}


</div>
