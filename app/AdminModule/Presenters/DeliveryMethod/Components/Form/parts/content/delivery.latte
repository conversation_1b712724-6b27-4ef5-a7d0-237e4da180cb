{var $clasess = ['u-mb-xxs']}
{if $deliveryMethodConfiguration->deliveryMethodUniqueIdentifier === App\Model\Orm\DeliveryMethod\Store::ID}
	{php $clasess[] = 'u-hide'}
{/if}
{embed $templates.'/part/box/toggle.latte', props=>[
	title: 'Doba dodáni',
	id: 'delivery',
	icon: $templates.'/part/icons/sliders-h.svg',
	variant: 'main',
	classes: $clasess
], templates=>$templates}
	{block content}



			{include $templates.'/part/core/inp.latte' props: [
				input: $form['deliveryDayFrom'],
				type: 'number',
				classesLabel: ['title']
			]}
			{include $templates.'/part/core/inp.latte' props: [
				input: $form['deliveryDayTo'],
				type: 'number',
				classesLabel: ['title']
			]}

			{foreach $form['deliveryHourByStock']->components as $stockAlias => $stockInput}
				{include $templates.'/part/core/inp.latte' props: [
					input: $stockInput,
					classesLabel: ['title']
				]}
			{/foreach}


	{/block}
{/embed}
