{var $props = [
title: 'Sazby DPH',
id: 'vats',
icon: $templates . '/part/icons/coins.svg',
variant: 'main',
classes: ['u-mb-xxs']
]}

{embed $templates . '/part/box/toggle.latte', props=>$props, templates=>$templates}
	{block content}
		<div class="grid grid--y-0">
			{foreach $form['vats']->components as $stateId => $stateContainer}
				<div class="grid__cell size--6-12">
					{include $templates.'/part/core/inp.latte' props: [
						input: $stateContainer['vatRate'],
						type: 'select',
						classesLabel: ['title'],
					]}
				</div>
			{/foreach}
		</div>
	{/block}
{/embed}




