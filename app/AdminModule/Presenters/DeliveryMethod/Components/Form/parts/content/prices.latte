{var $props = [
	title: 'Ce<PERSON>, váhy',
	id: 'prices',
	icon: $templates.'/part/icons/coins.svg',
	variant: 'main',
	classes: ['u-mb-xxs'],
]}

{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
	{block content}

		{var $items = []}

		{foreach $form['prices']->getComponents() as $pricesId => $pricesContainer}
			{continueIf $pricesId === 'newItemMarker'}

			{var App\Model\Orm\DeliveryMethod\DeliveryMethodPrice $price = $deliveryMethodConfiguration->prices->toCollection()->getById($pricesId)}
			{var $tags = [ [text: $price?->priceLevel->name]]}

			{var $itemName = $price?->state->name}
			{var $itemPrice = $price?->price->asMoney()}
			{var $itemFreeFrom = ''}
			{if $price?->freeFrom !== null}
				{php $itemFreeFrom = ', zdarma od '.Brick\Money\Money::of($price->freeFrom, $price->price->currency)}
			{/if}
			{var $item = [
				data: [
					removeitem-target: 'item',
					controller: 'DeliveryPrice RemoveItem',
					action: 'DeliveryPriceEdit:updateValues@window->DeliveryPrice#updateValues',
					DeliveryPrice-id-value: 'price_' . $pricesId,
				],
				tags: $tags,
				texts: [
					[
						text: '[<span data-DeliveryPrice-target="name">'.$itemName.'</span>] <span data-DeliveryPrice-target="price">'.$itemPrice.'</span><span data-DeliveryPrice-target="freeFrom">'.$itemFreeFrom.'</span>',
					]
				],
				btnsAfter: [
					[
						icon: $templates.'/part/icons/pencil-alt.svg',
						tooltip: 'Editovat',
						extend: true,
						data: [
							controller: 'Toggle',
							action: 'Toggle#changeClass DeliveryPrice#edit',
							toggle-target-value: '#overlay-price_'.$pricesId,
							toggle-target-class-value: 'is-visible'
						]
					],
					[
						icon: $templates.'/part/icons/trash.svg',
						tooltip: 'Odstranit',
						variant: 'remove',
						data: [
							action: 'RemoveItem#remove DeliveryPrice#remove'
						]
					]
				]
			]}

			{php $items[] = $item}
		{/foreach}

		{include $templates.'/part/box/list.latte',
			props: [
				data: [
					controller: 'List',
					List-name-value: 'price',
				],
				listData: [
					List-target: 'list',
				],
				addData: [
					action: 'List#add',
				],
				add: true,
				dragdrop: false,
				items: $items
			]
		}
	{/block}
{/embed}
