<?php

declare(strict_types=1);

namespace App\AdminModule\Presenters\DeliveryMethod\Components\Form;

use App\Model\CustomField\CustomFields;
use App\Model\Orm\DeliveryMethod\DeliveryMethodConfiguration;
use App\AdminModule\Presenters\DeliveryMethod\Components\Form\FormData\BaseFormData;
use App\Model\Orm\DeliveryMethod\DeliveryMethodConfigurationRepository;
use App\Model\Orm\DeliveryMethod\DeliveryMethodCurrency;
use App\Model\Orm\DeliveryMethod\DeliveryMethodPrice;
use App\Model\Orm\Orm;
use App\Model\Orm\Price;
use App\Model\Orm\User\User;
use App\PostType\Core\AdminModule\Components\Form\Definition\RelationInfo;
use Brick\Math\BigDecimal;
use Brick\Money\Money;
use Nette\Utils\ArrayHash;

final readonly class Handler
{

	public function __construct(
		private Orm $orm,
		private DeliveryMethodConfigurationRepository $deliveryMethodConfigurationRepository,
		private \App\PostType\Core\AdminModule\Components\Form\Handler $coreHandler,
		private CustomFields $customFields,
	)
	{
	}

	public function handle(DeliveryMethodConfiguration $deliveryMethodConfiguration, BaseFormData $data, User $user, RelationInfo $stateRelationsInfo, RelationInfo $paymentRelationsInfo): void
	{
		$deliveryMethodConfiguration->public = $data->publish->public;
		$deliveryMethodConfiguration->name = $data->name;
		$deliveryMethodConfiguration->desc = $data->desc;
		$deliveryMethodConfiguration->pageText = $data->pageText;
		$deliveryMethodConfiguration->tooltip = $data->tooltip;
		$deliveryMethodConfiguration->sort = $data->sort;
		$deliveryMethodConfiguration->isRecommended = $data->isRecommended;
		$deliveryMethodConfiguration->isGift = $data->isGift;
		$deliveryMethodConfiguration->pageShow = $data->pageShow;
		$deliveryMethodConfiguration->calculateFree = $data->calculateFree;
		$deliveryMethodConfiguration->deliveryDayFrom = $data->deliveryDayFrom;
		$deliveryMethodConfiguration->deliveryDayTo = $data->deliveryDayTo;
		$deliveryMethodConfiguration->deliveryHourByStock = $data->deliveryHourByStock;
		$deliveryMethodConfiguration->setCf($this->customFields->prepareDataToSave($data->cf));

		$this->handleCurrencies($deliveryMethodConfiguration, $data);
		$this->handleVats($deliveryMethodConfiguration, $data);
		$this->handleCountries($deliveryMethodConfiguration, $data, $stateRelationsInfo);
		$this->handlePayments($deliveryMethodConfiguration, $data, $paymentRelationsInfo);
		$this->handlePrices($deliveryMethodConfiguration, $data);

		$this->deliveryMethodConfigurationRepository->persistAndFlush($deliveryMethodConfiguration);
	}

	private function handleCurrencies(DeliveryMethodConfiguration $deliveryMethodConfiguration, BaseFormData $data): void
	{
		$toDelete = $deliveryMethodConfiguration->currencies->toCollection()->fetchPairs('currency');
		$currencies = $data->currencies;

		foreach ($currencies as $currency) {
			if (!isset($toDelete[$currency])) {
				$c = new DeliveryMethodCurrency();
				$c->currency = $currency;
				$deliveryMethodConfiguration->currencies->add($c);
			}
			unset($toDelete[$currency]);
		}

		$this->deliveryMethodConfigurationRepository->persistAndFlush($deliveryMethodConfiguration);

		foreach ($deliveryMethodConfiguration->prices->toCollection()->findBy(['price->currency' => array_keys($toDelete)]) as $price) {
			$this->orm->deliveryMethodPrice->remove($price);
		}

		/** @var DeliveryMethodCurrency $delete */
		foreach ($toDelete as $delete) {
			$this->orm->deliveryMethodCurrency->remove($delete);
		}
		$this->orm->flush();
	}

	private function handleVats(DeliveryMethodConfiguration $deliveryMethodConfiguration, BaseFormData $data): void
	{
		if (isset($data->vats)) {
			$vats = [];
			foreach ($data->vats as $stateId => $vatData) {
				$vats[$stateId] = $vatData->vatRate;
			}

			$deliveryMethodConfiguration->vats = $vats;
		}
	}

	private function handleCountries(DeliveryMethodConfiguration $deliveryMethodConfiguration, BaseFormData $data, RelationInfo $stateRelationsInfo): void
	{
		$this->coreHandler->handleHasManyRelation((array) $data->{$stateRelationsInfo->propertyName}, $stateRelationsInfo);
	}

	private function handlePayments(DeliveryMethodConfiguration $deliveryMethodConfiguration, BaseFormData $data, RelationInfo $paymentRelationsInfo): void
	{
		$this->coreHandler->handleHasManyRelation((array) $data->{$paymentRelationsInfo->propertyName}, $paymentRelationsInfo);
	}

	private function handlePrices(DeliveryMethodConfiguration $deliveryMethodConfiguration, BaseFormData $data): void
	{
		$tmpIds = [];
		if (isset($data->prices)) {
			//update items

				foreach ($data->prices as $priceId => $priceData) {
					if ($priceId === 'newItemMarker') {
						continue;
					}

					if (is_int($priceId)) {
						$price = $this->orm->deliveryMethodPrice->getById($priceId);
					} else {
						$price = new DeliveryMethodPrice();
						$price->deliveryMethod = $deliveryMethodConfiguration;
					}

					if ($price === null) {
						continue;
					}

					$this->handleOnePrice($price, $priceData);

					$this->orm->deliveryMethodPrice->persistAndFlush($price);
					$tmpIds[] = $price->id;
				}

		}

		/** @var DeliveryMethodPrice $price */
		foreach ($deliveryMethodConfiguration->prices as $price) {
			if (!in_array($price->id, $tmpIds)) {
				$this->orm->deliveryMethodPrice->remove($price);
			}
		}
	}

	private function handleOnePrice(DeliveryMethodPrice $price, ArrayHash $data): void
	{
		$price->priceLevel = $data->priceLevel;
		$price->state = $data->state;
		$price->price = Price::from(Money::of($data->price, $data->currency));
		$price->freeFrom = $data->freeFrom !== null ? BigDecimal::of($data->freeFrom) : null;
		$price->maxWeight = $data->maxWeight;
		$price->maxCodPrice = $data->maxCodPrice;
		$price->externalId = $data->externalId;
	}

}
