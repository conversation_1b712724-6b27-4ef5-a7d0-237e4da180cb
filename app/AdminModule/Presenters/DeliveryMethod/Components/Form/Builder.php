<?php declare(strict_types=1);

namespace App\AdminModule\Presenters\DeliveryMethod\Components\Form;

use App\Model\Currency\CurrencyHelper;
use App\Model\Orm\DeliveryMethod\DeliveryMethodConfiguration;
use App\Model\Orm\DeliveryMethod\DeliveryMethodPrice;
use App\Model\Orm\Orm;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Stock\Stock;
use App\Model\Orm\User\User;
use App\Model\Translator;
use App\PostType\Core\AdminModule\Components\Form\Definition\RelationInfo;
use Nette\Application\UI;
use App\PostType\Core\AdminModule\Components\Form\Builder as CoreBuilder;
use Nette\Forms\Container;
use Nette\Http\Request;
use Nextras\Orm\Collection\ICollection;

final readonly class Builder
{

	/** @var ICollection<PriceLevel> */
	private ICollection $priceLevels;

	/** @var ICollection<Stock> */
	private ICollection $stocks;

	public function __construct(
		private Orm $orm,
		private CoreBuilder $coreBuilder,
		private Translator $translator,
		private Request $request,
	)
	{
		$this->priceLevels = $this->orm->priceLevel->findAll();
		$this->stocks = $this->orm->stock->findAll();
	}

	public function build(UI\Form $form, DeliveryMethodConfiguration $deliveryMethodConfiguration, User $user, RelationInfo $countriesRelationsInfo, RelationInfo $paymentRelationsInfo): void
	{
		$this->addCommonToForm($form, $deliveryMethodConfiguration);
		$this->addDeliveryToForm($form, $deliveryMethodConfiguration);
		$this->addVats($form, $deliveryMethodConfiguration);
		$this->addPrices($form, $deliveryMethodConfiguration, $this->request->getPost());

		// add countries relation
		$this->coreBuilder->addHasManyRelation($form, $countriesRelationsInfo, $this->request->getPost());
		// add payments relation
		$this->coreBuilder->addHasManyRelation($form, $paymentRelationsInfo, $this->request->getPost());

		$this->coreBuilder->addButtons($form);
		$this->coreBuilder->addPublish($form, $deliveryMethodConfiguration);
	}

	private function addCommonToForm(UI\Form $form, DeliveryMethodConfiguration $deliveryMethodConfiguration): void
	{
		$form->addHidden('cf');
		$form->addText('name', 'name')
			->setRequired()
			->setDefaultValue($deliveryMethodConfiguration->name);
		$form->addTextArea('desc', 'desc')
			->setDefaultValue($deliveryMethodConfiguration->desc);
		$form->addTextArea('tooltip', 'tooltip')
			->setNullable()
			->setDefaultValue($deliveryMethodConfiguration->tooltip);
		$form->addText('sort', 'sort')
			->addRule($form::Integer, 'Please enter valid number.')
			->setDefaultValue($deliveryMethodConfiguration->sort);
		$form->addCheckbox('isRecommended', 'is_recommended')
			->setDefaultValue($deliveryMethodConfiguration->isRecommended);
		$form->addCheckbox('isGift', 'is_gift')
			->setDefaultValue($deliveryMethodConfiguration->isGift);
		$form->addCheckbox('calculateFree', 'calculate_free')
			->setDefaultValue($deliveryMethodConfiguration->calculateFree);

		$form->addMultiSelect('currencies', 'select_currencies', CurrencyHelper::CURRENCIES_SELECT)
			->setDefaultValue($deliveryMethodConfiguration->currencies->toCollection()->fetchPairs(null, 'currency'));
			 //->setRequired();
		$form->addCheckbox('pageShow', 'page_show_delivery_and_payment')
			->setDefaultValue($deliveryMethodConfiguration->pageShow);
		$form->addTextArea('pageText', 'page_text_delivery_and_payment')
			->setNullable()
			->setDefaultValue($deliveryMethodConfiguration->pageText);
	}

	private function addDeliveryToForm(UI\Form $form, DeliveryMethodConfiguration $deliveryMethodConfiguration): void
	{
		$form->addText('deliveryDayFrom', 'delivery_day_from')
			->setRequired()
			->addRule($form::Integer, 'Please enter valid number.')
			->setDefaultValue($deliveryMethodConfiguration->deliveryDayFrom);
		$form->addText('deliveryDayTo', 'delivery_day_to')
			->setNullable()
			->addRule($form::Integer, 'Please enter valid number.')
			->setDefaultValue($deliveryMethodConfiguration->deliveryDayTo);

		$hourContainer = $form->addContainer('deliveryHourByStock');
		foreach ($this->stocks as $stock) {
			$hourContainer->addText($stock->alias, 'delivery_hour_stock_' . $stock->alias)
				->setNullable()
				->setDefaultValue($deliveryMethodConfiguration->deliveryHourByStock->{$stock->alias} ?? null);
		}
	}

	private function addVats(UI\Form $form, DeliveryMethodConfiguration $deliveryMethodConfiguration): void
	{
		$vatsContainer = $form->addContainer('vats');
		$states = $deliveryMethodConfiguration->countries->toCollection();

		foreach ($states as $state) {
			$stateContainer = $vatsContainer->addContainer($state->id);

			$vatRates = [];
			foreach ($state->vatRates as $level => $rate) {
				if ($rate !== null) {
					$vatRates[$level->value] = sprintf('%s%% (%s)', $rate, $this->translator->translate('vat_rate_' . $level->value));
				}
			}

			$stateId = $state->id;

			$stateContainer->addSelect('vatRate', $state->code, $vatRates)->setTranslator(null);
			if (isset($deliveryMethodConfiguration->vats->$stateId)) {
				$stateContainer['vatRate']->setDefaultValue($deliveryMethodConfiguration->vats->$stateId);
			}
		}
	}

	private function addPrices(UI\Form $form, DeliveryMethodConfiguration $deliveryMethodConfiguration, array $postData): void
	{
		$pricesContainer = $form->addContainer('prices');

		if ($postData === []) {
			/** @var DeliveryMethodPrice $price */
			foreach ($deliveryMethodConfiguration->prices as $price) {
				$this->createPriceContainer($deliveryMethodConfiguration, $pricesContainer, $price->id, $price);
			}
		} elseif (isset($postData['prices'])) {
			foreach ($postData['prices'] as $priceId => $priceData) {
				if (is_int($priceId)) {
					$this->createPriceContainer($deliveryMethodConfiguration, $pricesContainer, $priceId, $price ?? null);
				} elseif (preg_match('/^newItemMarker_/', $priceId)) {
					$price = $deliveryMethodConfiguration->prices->toCollection()->getById($priceId);
					$this->createPriceContainer($deliveryMethodConfiguration, $pricesContainer, $priceId, $price);
				}
			}
		}

		$this->createPriceContainer($deliveryMethodConfiguration, $pricesContainer, 'newItemMarker');
	}

	private function createPriceContainer(DeliveryMethodConfiguration $deliveryMethodConfiguration, Container $pricesContainer, string|int $name, ?DeliveryMethodPrice $price = null): void
	{
		$priceContainer = $pricesContainer->addContainer($name);
		$priceContainer->addSelect('priceLevel', 'price_level', $this->priceLevels->fetchPairs('id', 'name'))
			->setDefaultValue($price?->priceLevel->id ?? PriceLevel::DEFAULT_ID);

		$states = $deliveryMethodConfiguration->countries->toCollection()->fetchPairs('id', 'name');
		if (($price !== null && !isset($states[$price->state->id])) || $price === null) {
			$states = $deliveryMethodConfiguration->mutation->states->toCollection()->fetchPairs('id', 'name');
		}

		$priceContainer->addSelect('state', 'state', $states)
			->setDefaultValue($price?->state->id);
		$priceContainer->addText('price', 'price')
			->setHtmlType('number')
			->setHtmlAttribute('step', 0.0001)
			->setDefaultValue($price?->price->asMoney()->getAmount()->toFloat() ?? 0);

		//$priceContainer->addText('currency', 'currency')->setNullable()->setDefaultValue($price?->price->asMoney()->getCurrency()->getCurrencyCode() ?? null);
		$priceContainer->addSelect('currency', 'currency', CurrencyHelper::CURRENCIES_SELECT)->setPrompt('-- vyberte --')->setDefaultValue($price?->price->asMoney()->getCurrency()->getCurrencyCode() ?? null);

		$priceContainer->addText('freeFrom', 'free_from_price')
			->setNullable()
			->setHtmlType('number')
			->setHtmlAttribute('step', 0.0001)
			->setDefaultValue($price?->freeFrom?->toFloat());

		$priceContainer->addText('maxWeight', 'max_weight')
			->setNullable()
			->setHtmlType('number')
			->setDefaultValue($price?->maxWeight);
		$priceContainer->addText('maxCodPrice', 'max_cod_price')
			->setNullable()
			->setHtmlType('number')
			->setDefaultValue($price?->maxCodPrice);

		$priceContainer->addText('externalId', 'externalId')
			->setNullable()
			->setDefaultValue($price?->externalId);

		if ($name === 'newItemMarker') {
			$priceContainer['priceLevel']->setPrompt('choose');
			$priceContainer['state']->setPrompt('choose');
			$priceContainer['currency']->setValue('CZK');
		}
	}

}
