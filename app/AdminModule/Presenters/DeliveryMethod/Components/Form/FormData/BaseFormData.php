<?php declare(strict_types=1);

namespace App\AdminModule\Presenters\DeliveryMethod\Components\Form\FormData;

use App\PostType\Core\AdminModule\Components\Form\FormData\PublishFormData;
use Nette\Utils\ArrayHash;

final class BaseFormData
{

	public PublishFormData $publish;

	public string $name;

	public string $desc;

	public ?string $tooltip;

	public int $sort;

	public bool $isRecommended;

	public bool $isGift;

	public bool $calculateFree;

	public int $deliveryDayFrom;

	public ?int $deliveryDayTo;

	public ArrayHash $deliveryHourByStock;

	public ArrayHash $vats;

	public ArrayHash $prices;

	public ArrayHash $countries;

	public ArrayHash $allowedPaymentMethods;

	public string $cf;

	public array $currencies;

	public ?string $pageText;

	public bool $pageShow;

}
