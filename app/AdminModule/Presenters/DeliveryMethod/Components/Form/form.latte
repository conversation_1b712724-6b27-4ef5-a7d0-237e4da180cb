{varType App\Model\Orm\Mutation\Mutation $mutation}
<form n:name="form" class="main__main" data-controller="pagemenu">
	<div class="main__header">
		{include $templates.'/part/box/header.latte',
			props: [
				hrefClose: 'default',
				img: $imgSrc,
				title: '['.$deliveryMethodConfiguration->deliveryMethodUniqueIdentifier.'] '.$deliveryMethodConfiguration->name,
				hasGeneratedMenu: true,
				isPageTitle: true,
			]
		}
	</div>
	<div class="main__content scroll">

		{snippet flash}
			<div n:foreach="$flashes as $flash" class="message message-{$flash->type}">{$flash->message}</div>
		{/snippet}

		{*php dump($form->components['countries'], $relationsInfo)*}
		{include './parts/content/content.latte', form => $form}
		{include './parts/content/delivery.latte', form => $form}
		{include './parts/content/prices.latte', form => $form}

		{foreach $relationsInfo as $relationInfo}
			{include $corePartsDirectory . '/relation.latte', form: $form, relationInfo: $relationInfo}
		{/foreach}

		{capture $newItemTemplate}
			{foreach $relationsInfo as $relationInfo}
				<div class="u-hide">
				{if ! $relationInfo->singleValue}
					{if isset($form[$relationInfo->propertyName])}
						{include $templates.'/part/core/formTargetItem.latte', props: [
						itemTargetName: $relationInfo->propertyName . 'ListItem',
						formContainer: $form[$relationInfo->propertyName]['newItemMarker'],
						listPlaceholder: $relationInfo->inputPlaceHolder,
						listSearchUrl: $relationInfo->suggestUrl,
						dragdrop: $relationInfo->dragAndDrop,
						]}
					{/if}
				{/if}
				</div>
			{/foreach}
		{/capture}

		{include './parts/content/vats.latte', form => $form}
		{include './parts/content/pageText.latte', form => $form}

		{if $deliveryMethodConfiguration->getCfScheme() !== []}
			{include './parts/content/custom-fields.latte', form=>$form}
		{/if}

	</div>

	<div class="main__content-side scroll">
		{include $corePartsDirectory . '/side/state.latte', form: $form}
		{include $corePartsDirectory . '/side/btns.latte'}
	</div>

	{include './parts/overlay/editItem.latte', form=>$form}

</form>
{$newItemTemplate}
{include './parts/newItemTemplate.latte', form: $form}
{include $templates . '/part/core/libraryOverlay.latte'}

