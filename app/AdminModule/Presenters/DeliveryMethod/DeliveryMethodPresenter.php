<?php

namespace App\AdminModule\Presenters\DeliveryMethod;

use App\AdminModule\Presenters\BasePresenter;
use App\AdminModule\Presenters\DeliveryMethod\Components\DataGrid\DataGrid;
use App\AdminModule\Presenters\DeliveryMethod\Components\DataGrid\DataGridFactory;
use App\AdminModule\Presenters\DeliveryMethod\Components\Form\Form;
use App\AdminModule\Presenters\DeliveryMethod\Components\Form\FormFactory;
use App\AdminModule\Presenters\DeliveryMethod\Components\ShellForm\ShellForm;
use App\AdminModule\Presenters\DeliveryMethod\Components\ShellForm\ShellFormFactory;
use App\Model\Orm\DeliveryMethod\DeliveryMethodConfiguration;

class DeliveryMethodPresenter extends BasePresenter
{

	private ?DeliveryMethodConfiguration $object;

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
		private readonly ShellFormFactory $shellFormFactory,
		private readonly FormFactory $formFactory,
	)
	{
	}

	public function startup(): void
	{
		parent::startup();
	}



	public function actionEdit(int $id): void
	{
		$this->object = $this->orm->deliveryMethod->getById($id);

		if (!$this->object) {
			$this->redirect('default');
		}
	}

	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create();
	}

	protected function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create();
	}

	protected function createComponentForm(): Form
	{
		return $this->formFactory->create($this->object, $this->userEntity);
	}

}
