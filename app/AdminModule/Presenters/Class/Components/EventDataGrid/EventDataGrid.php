<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Class\Components\EventDataGrid;

use App\AdminModule\Presenters\Class\Components\DataGridHelper\DataGripCreationHelper;
use App\Model\DateTime\DateTimeHelper;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\ClassEvent\ClassEvent;
use App\Model\Orm\ClassEvent\ClassEventModel;
use App\Model\Orm\ClassEvent\ClassEventRepository;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Product\Product;
use App\Model\Orm\State\StateRepository;
use App\Model\Translator;
use App\PostType\Core\AdminModule\Components\DataGrid\HasMutationColumn;
use Nette\Application\UI\Control;
use Nette\Forms\Container;
use Nette\Utils\ArrayHash;
use Ublaboo\DataGrid\Column\Action\Confirmation\StringConfirmation;
use Ublaboo\DataGrid\DataGrid;
use Ublaboo\DataGrid\Exception\DataGridException;

class EventDataGrid extends Control
{

	use HasMutationColumn;

	public function __construct(
		private readonly Product $product,
		private readonly ClassEventModel $classEventModel,
		private readonly Orm $orm,
		private readonly Translator $translator,
		private readonly MutationsHolder $mutationsHolder,
		private readonly StateRepository $stateRepository,
		private readonly ClassEventRepository $classEventRepository,
		private readonly DataGripCreationHelper $dataGripCreationHelper,
	)
	{
	}

	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->render(__DIR__ . '/eventDataGrid.latte');
	}

	/**
	 * @throws DataGridException
	 */
	public function createComponentGrid(): DataGrid
	{
		$grid = new DataGrid();
		$grid->setTranslator($this->translator);
		$grid->setItemsPerPageList([20, 30, 40, 50, 100], false);
		$grid->setDefaultSort(['from' => 'ASC']);

		$grid->setDataSource(
			$this->product->classEvents->toCollection()
		);

		$grid->addColumnText('city', 'city');
		$grid->addColumnText('place', 'place');
		$grid->addColumnDateTime('from', 'class_event_from')->setFormat('d.m.Y H:i');
		$grid->addColumnDateTime('to', 'class_event_to')->setFormat('d.m.Y H:i');
		$grid->addColumnText('capacity', 'class_event_capacity')->setAlign('right');
		$grid->addColumnText('capacityUsed', 'class_event_capacity_used')->setAlign('right');
		$grid->addColumnText('state', 'state')->setRenderer(
			fn(ClassEvent $classEvent) => $classEvent->state->name
		)->setFilterSelect($this->orm->state->findAll()->orderBy('name')->fetchPairs('id', 'name'))->setPrompt($this->translator->translate('all'));

		$this->addColumnMutation($grid);

		$grid->addColumnStatus('public', 'public')
			->addOption(1, 'ano')
			->setClass('btn btn-xs btn-success')
			->endOption()
			->addOption(0, 'ne')
			->setClass('btn btn-xs btn-danger')
			->endOption()->onChange[] = function (string $classEventId, string $value): void {
			$classEvent = $this->classEventRepository->getById((int) $classEventId);
			$classEvent->public = $value;
			$this->orm->classEvent->persistAndFlush($classEvent);
			if ($this->presenter->isAjax()) {
				$this['grid']->redrawItem($classEventId);
			}
			};

		$add = $grid->addInlineAdd();
		$add->setPositionTop();
		$add->onControlAdd[] = $this->addInlineAdd(...);
		$add->onSubmit[] = $this->addSubmit(...);

		$inlineEdit = $grid->addInlineEdit();
		$inlineEdit->onControlAdd[] = $this->addInlineEdit(...);
		$inlineEdit->onSubmit[] = $this->editSubmit(...);
		$inlineEdit->onSetDefaults[]  = $this->setDefaultForInlineEdit(...);

		$grid->addAction('editEvent', 'Edit', ':Admin:ClassEvent:edit', ['productId' => 'product.id', 'id'])->setClass('btn btn-xs btn-primary');

		$grid->addAction('delete', '', 'delete!')
			->setIcon('trash')
			->setTitle('Smazat')
			->setClass('btn btn-xs btn-danger ajax')
			->setConfirmation(
				new StringConfirmation('Opravdu smazat?')
			);

		return $grid;
	}

	private function addInlineAdd(Container $container): void
	{
		$container->addText('city', 'city')->setRequired();
		$container->addText('place', 'place');

		$mutations = array_map(fn(Mutation $mutation) => $mutation->name, $this->mutationsHolder->findAllById());
		$states = $this->stateRepository->findAll()->fetchPairs('id', 'name');
		$container->addSelect('mutation', 'mutation', $mutations)->setRequired();
		$container->addSelect('state', 'state', $states)->setRequired();
		$container->addDateTime('from', 'class_event_from')->setRequired();
		$container->addDateTime('to', 'class_event_to')->setRequired();
		$container->addInteger('capacity', '');
	}

	private function addSubmit(ArrayHash $values): void
	{
		$capacity = $values['capacity'] ?? 0;
		$mutation = $this->dataGripCreationHelper->getMutationFromValues($values);
		$state = $this->dataGripCreationHelper->getStateFromValues($values);
		$city = $values['city'] ?? '';
		$place = $values['place'] ?? '';
		$from = DateTimeHelper::createNextrasDateTime($values['from']);
		$to = DateTimeHelper::createNextrasDateTime($values['to']);
		$this->classEventModel->create($mutation, $state, $this->product, $city, $place, $from, $to, $capacity);

		$this['grid']->setDataSource($this->product->classEvents->toCollection());
		$this['grid']->reload();
		$this->redrawControl();
	}


	private function addInlineEdit(Container $container): void
	{
		$container->addText('city', '')->setRequired();
		$container->addText('place', '');
		$container->addDateTime('from', '');
		$container->addDateTime('to', '');
		$container->addInteger('capacity', '');
	}

	private function setDefaultForInlineEdit(Container $container, ClassEvent $item): void
	{
		$container->setDefaults([
			'city' => $item->city,
			'place' => $item->place,
			'from' => $item->from->format('Y-m-d H:i:s'),
			'to' => $item->to->format('Y-m-d H:i:s'),
			'capacity' => $item->capacity,
		]);
	}

	private function editSubmit(string $id, ArrayHash $values): void
	{
		$classEvent = $this->classEventRepository->getByIdChecked((int) $id);
		$city = $values['city'] ?? '';
		$place = $values['place'] ?? '';
		$capacity = $values['capacity'] ?? 0;
		$from = DateTimeHelper::createNextrasDateTime($values['from']);
		$to = DateTimeHelper::createNextrasDateTime($values['to']);

		$classEvent->city = $city;
		$classEvent->place = $place;
		$classEvent->from = $from;
		$classEvent->to = $to;
		$classEvent->capacity = $capacity;
		$this->orm->classEvent->persistAndFlush($classEvent);
	}



	public function handleDelete(string $id): void
	{
		$classEvent = $this->classEventRepository->getById((int) $id);
		if ($classEvent !== null) {
			$this->classEventRepository->removeAndFlush($classEvent);

			$this['grid']->setDataSource($this->product->classEvents->toCollection());
			$this['grid']->reload();
			$this->redrawControl();
		}
	}

}
