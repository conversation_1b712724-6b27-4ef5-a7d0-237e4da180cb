<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Class\Components\SectionDataGrid;

use App\AdminModule\Presenters\Class\Components\DataGridHelper\DataGripCreationHelper;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\ClassSection\ClassSection;
use App\Model\Orm\ClassSection\ClassSectionModel;
use App\Model\Orm\ClassSection\ClassSectionRepository;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Product\Product;
use App\Model\Translator;
use App\PostType\Core\AdminModule\Components\DataGrid\HasMutationColumn;
use Nette\Application\UI\Control;
use Nette\Forms\Container;
use Nette\Utils\ArrayHash;
use Ublaboo\DataGrid\Column\Action\Confirmation\StringConfirmation;
use Ublaboo\DataGrid\DataGrid;
use Ublaboo\DataGrid\Exception\DataGridException;

class SectionDataGrid extends Control
{

	use HasMutationColumn;

	public function __construct(
		private readonly Product $product,
		private readonly Orm $orm,
		private readonly Translator $translator,
		private readonly MutationsHolder $mutationsHolder,
		private readonly ClassSectionModel $classSectionModel,
		private readonly DataGripCreationHelper $dataGripCreationHelper,
		private readonly ClassSectionRepository $classSectionRepository,
	)
	{
	}

	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->render(__DIR__ . '/sectionDataGrid.latte');
	}

	/**
	 * @throws DataGridException
	 */
	public function createComponentGrid(): DataGrid
	{
		$grid = new DataGrid();
		$grid->setTranslator($this->translator);
		$grid->setItemsPerPageList([20, 30, 40, 50, 100], false);

		$grid->setDataSource(
			$this->product->classSections->toCollection()
		);
		$grid->addColumnText('name', 'name');
		$grid->addColumnText('type', 'type')->setRenderer(
			fn(ClassSection $classSection): string => $this->translator->translate('section_type_' . $classSection->type)
		);
		$grid->addColumnText('completionTime', 'completionTime');
		$this->addColumnMutation($grid);

//		$this->addType($grid);
//		$this->addCompletionTime($grid);

		$grid->addAction('editSection', 'Edit', ':Admin:ClassSection:edit', ['productId' => 'product.id', 'id'])->setClass('btn btn-xs btn-primary');
		$grid->setDefaultSort(['mutationId' => 'ASC', 'sort' => 'ASC']);

		$add = $grid->addInlineAdd();
		$add->setPositionTop();
		$add->onControlAdd[] = $this->addInlineAdd(...);
		$add->onSubmit[] = $this->addSubmit(...);

		$inlineEdit = $grid->addInlineEdit();
		$inlineEdit->onControlAdd[] = $this->addInlineEdit(...);
		$inlineEdit->onSubmit[] = $this->editSubmit(...);
		$inlineEdit->onSetDefaults[]  = $this->setDefaultForInlineEdit(...);

		$grid->addAction('delete', '', 'delete!')
			->setIcon('trash')
			->setTitle('Smazat')
			->setClass('btn btn-xs btn-danger ajax')
			->setConfirmation(
				new StringConfirmation('Opravdu smazat?')
			);

		return $grid;
	}

	private function addInlineAdd(Container $container): void
	{
		$mutations = array_map(fn(Mutation $mutation) => $mutation->name, $this->mutationsHolder->findAllById());
		$container->addSelect('mutation', 'mutation', $mutations)->setRequired();
		$container->addText('name', '');
		$container->addText('completionTime', '');
		$container->addSelect('type', '', $this->getTypeSelector())->setRequired();
	}
	private function addSubmit(ArrayHash $values): void
	{
		$name = $values['name'] ?? '';
		$type = $values['type'] ?? ClassSection::TYPE_CATEGORY;
		$completionTime = $values['completionTime'] ?? 0;
		$mutation = $this->dataGripCreationHelper->getMutationFromValues($values);
		$this->classSectionModel->create($mutation, $this->product, $name, $type, (int) $completionTime);

		$this['grid']->setDataSource($this->product->classSections->toCollection());
		$this['grid']->reload();

		$this->redrawControl();
	}

	private function addInlineEdit(Container $container): void
	{
		$mutations = array_map(fn(Mutation $mutation) => $mutation->name, $this->mutationsHolder->findAllById());
		$container->addText('name', '');
		$container->addSelect('mutation', 'mutation', $mutations)->setRequired();
		$container->addSelect('type', 'type', $this->getTypeSelector())->setRequired();
		$container->addInteger('completionTime', '');
	}

	private function setDefaultForInlineEdit(Container $container, ClassSection $item): void
	{
		$container->setDefaults([
			'name' => $item->name,
			'mutation' => $item->mutation->id,
			'type' => $item->type,
			'completionTime' => $item->completionTime,
		]);
	}

	private function editSubmit(string $id, ArrayHash $values): void
	{
		$classSection = $this->classSectionRepository->getByIdChecked((int) $id);
		$name = $values['name'] ?? '';
		$completionTime = $values['completionTime'] ?? 0;
		$type = $values['type'] ?? ClassSection::TYPE_CATEGORY;

		$classSection->name = $name;
		$classSection->completionTime = $completionTime;
		$classSection->type = $type;
		$classSection->mutation = $this->dataGripCreationHelper->getMutationFromValues($values);
		$this->classSectionRepository->persistAndFlush($classSection);
	}

	public function handleDelete(string $id): void
	{
		$classSection = $this->classSectionRepository->getById((int) $id);
		if ($classSection !== null) {
			$this->classSectionRepository->removeAndFlush($classSection);

			$this['grid']->setDataSource($this->product->classSections->toCollection());
			$this['grid']->reload();
			$this->redrawControl();
		}
	}

	private function getTypeSelector(): array
	{
		return ClassSection::getConstsByPrefix('TYPE_', 'section_type_');
	}

}
