<?php

declare(strict_types=1);

namespace App\AdminModule\Presenters\Class\Components\ClassForm;

use App\Model\Orm\Orm;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductLocalization\ProductLocalizationModel;
use App\Model\Orm\ProductVariantLocalization\ProductVariantLocalizationModel;

final class ClassFormModel
{

	public function __construct(
		private readonly ProductLocalizationModel $productLocalizationModel,
		private readonly ProductVariantLocalizationModel $productVariantLocalizationModel,
		private readonly Orm $orm,
	)
	{
	}

	public function handleLocalizationButtonInSubmit(string $submitAction, Product $product): void
	{
		if (preg_match('/^(' . ClassForm::SUBMIT_MUTATION_CREATE . '|' . ClassForm::SUBMIT_MUTATION_REMOVE . ')([a-zA-Z]+)$/', $submitAction, $matches)) {
			$mutation = $this->orm->mutation->getByCode(strtolower($matches[2]));
			if ($matches[1] === ClassForm::SUBMIT_MUTATION_CREATE) {
				$this->productLocalizationModel->create($mutation, $product);
				$this->productVariantLocalizationModel->createForAllVariant($mutation, $product);
			} elseif ($matches[1] === ClassForm::SUBMIT_MUTATION_REMOVE) {
				$this->productLocalizationModel->delete($mutation, $product);
				$this->productVariantLocalizationModel->deleteForAllVariant($mutation, $product);
			}
		}
	}

}
