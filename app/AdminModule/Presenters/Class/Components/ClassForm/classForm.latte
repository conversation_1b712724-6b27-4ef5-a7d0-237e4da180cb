{varType App\Model\Orm\Product\Product $product}

<form n:name="form" id="classForm">
	{include '../../../../Components/ProductForm/parts/content/content.latte', form=>$form}
	{include '../../../../Components/ProductForm/parts/content/categories.latte', form=>$form}
	{include '../../../../Components/ProductForm/parts/content/variant.latte', form=>$form}
	{include '../../../../Components/ProductForm/parts/content/params.latte', form=>$form}
	{include '../../../../Components/ProductForm/parts/content/tags.latte', form=>$form}
	{include '../../../../Components/ProductForm/parts/content/imgs.latte', form=>$form}
	{include '../../../../Components/ProductForm/parts/content/seo.latte', form=>$form}
	{include '../../../../Components/ProductForm/parts/content/validity.latte', form=>$form}


{*		{include '../../../../Components/ProductForm/parts/content/files.latte', form=>$form}*}
	{include '../../../../Components/ProductForm/parts/content/products.latte', form=>$form}




	{var $hasEnabledCf = false}
	{foreach $object->productLocalizations as $productLocalization}
		{if $productLocalization->getCfScheme()}
			{php $hasEnabledCf = true}
		{/if}
	{/foreach}

	{if $hasEnabledCf}
		{include '../../../../Components/ProductForm/parts/content/custom-fields.latte', form=>$form}
	{/if}

	{var $hasEnabledCc = false}
	{foreach $object->productLocalizations as $productLocalization}
		{if $productLocalization->getCCModules()}
			{php $hasEnabledCc = true}
		{/if}
	{/foreach}

	{if $hasEnabledCc}
		{include '../../../../Components/ProductForm/parts/content/custom-content.latte', form=>$form}
	{/if}

	{*include '../../../../Components/ProductForm/parts/overlay/editItem.latte', form=>$form*}
	{capture $templateTargets}
		{include '../../../../Components/ProductForm/parts/newItemTemplate.latte', form=>$form, product=>$product}
		{include $templates . '/part/core/libraryOverlay.latte'}
	{/capture}
</form>

{$templateTargets|noescape}


