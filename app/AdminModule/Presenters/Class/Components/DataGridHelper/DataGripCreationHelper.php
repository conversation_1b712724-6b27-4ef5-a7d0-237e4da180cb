<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Class\Components\DataGridHelper;

use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\State\State;
use App\Model\Orm\State\StateRepository;
use Nette\Utils\ArrayHash;

class DataGripCreationHelper
{

	public function __construct(
		private readonly MutationsHolder $mutationsHolder,
		private readonly StateRepository $stateRepository,
	)
	{
	}

	public function getMutationFromValues(ArrayHash $values): Mutation
	{
		$mutationId = $values['mutation'] ?? null;
		$mutation = null;
		if ($mutationId !== null) {
			$mutation = $this->mutationsHolder->findAllById()[(int) $mutationId] ?? null;
		}
		if ($mutation === null) {
			$mutation = $this->mutationsHolder->getDefaultRs();
		}

		return $mutation;
	}

	public function getStateFromValues(ArrayHash $values): State
	{
		$stateId = $values['state'] ?? null;
		$state = null;
		if ($stateId !== null) {
			$state = $this->stateRepository->getById((int) $stateId);
		}
		if ($state === null) {
			$state = $this->stateRepository->getByChecked(['code' => State::CODE_CZ]);
		}

		return $state;
	}

}
