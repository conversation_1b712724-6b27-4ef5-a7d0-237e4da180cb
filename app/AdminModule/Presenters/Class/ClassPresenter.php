<?php declare(strict_types=1);

namespace App\AdminModule\Presenters\Class;

use App\AdminModule\Presenters\Class\Components\ClassForm\ClassForm;
use App\AdminModule\Presenters\Class\Components\ClassForm\ClassFormFactory;
use App\AdminModule\Presenters\BasePresenter;
use App\AdminModule\Presenters\Class\Components\EventDataGrid\EventDataGrid;
use App\AdminModule\Presenters\Class\Components\EventDataGrid\EventDataGridFactory;
use App\AdminModule\Presenters\Class\Components\SectionDataGrid\SectionDataGrid;
use App\AdminModule\Presenters\Class\Components\SectionDataGrid\SectionDataGridFactory;
use App\Model\Orm\Alias\AliasModel;
use App\Model\Orm\File\FileModel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductModel;
use App\Model\Orm\ProductVariant\ProductVariantModel;
use Nette\Application\Attributes\Persistent;
use Nette\DI\Attributes\Inject;
use Nette\Http\FileUpload;
use Nette\Utils\Json;

final class ClassPresenter extends BasePresenter
{

	#[Persistent]
	public int $id;

	#[Persistent]
	public string $sort;

	#[Inject]
	public ProductVariantModel $productVariantModel;

	#[Inject]
	public ProductModel $productModel;

	#[Inject]
	public FileModel $fileModel;

	#[Inject]
	public ClassFormFactory $productFormFactory;

	#[Inject]
	public SectionDataGridFactory $sectionDataGridFactory;

	#[Inject]
	public EventDataGridFactory $eventDataGridFactory;

	#[Inject]
	public AliasModel $aliasModel;

	private ?Product $object = null;

	public function actionEdit(?int $id = null): void
	{
		if ($id === null) {
			$this->redirect('Catalog:');
		}

		$this->object = $this->orm->product->getById($id);
		if (!$this->object) {
			$this->redirect('Catalog:');
		}

		$this->productModel->normalizeProduct($this->object);
	}

	public function renderEdit(): void
	{
		$this->template->object = $this->object;
		$this->template->product = $this->object;
	}

	public function handleUpload(?FileUpload $file = null): never
	{
		$file ??= new FileUpload($_FILES['file']);
		$fileEntity = $this->fileModel->add($file);
		echo Json::encode([
			'id' => $fileEntity->id,
		]);
		$this->terminate();
	}


	public function actionRegenerateAliasMutation(int $id, string $name, string $lang): void
	{
		$mutation = $this->orm->mutation->getByCode($lang);

		$product = $this->orm->product->getById($id);
		$this->orm->setMutation($mutation);

		$productLocalization = $product->getLocalization($mutation);
		echo $this->aliasModel->handleAliasChange($productLocalization, $mutation, $name, false);
		$this->terminate();
	}


	protected function createComponentClassForm(): ClassForm
	{
		return $this->productFormFactory->create($this->object, $this->userEntity);
	}

	protected function createComponentSectionDataGrid(): SectionDataGrid
	{
		return $this->sectionDataGridFactory->create($this->object);
	}

	protected function createComponentEventDataGrid(): EventDataGrid
	{
		return $this->eventDataGridFactory->create($this->object);
	}

}
