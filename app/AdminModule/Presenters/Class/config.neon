services:
	-
		implement: App\AdminModule\Presenters\Class\Components\ClassForm\ClassFormFactory
		arguments: [rsTemplatesPath: %rsTemplatesPath%]

	- App\AdminModule\Presenters\Class\Components\SectionDataGrid\SectionDataGridFactory
	- App\AdminModule\Presenters\Class\Components\EventDataGrid\EventDataGridFactory

	- App\AdminModule\Presenters\Class\Components\ClassForm\ClassFormBuilder
	- App\AdminModule\Presenters\Class\Components\ClassForm\ClassFormSuccess

	- App\Model\Orm\ClassEvent\ClassEventModel
	- App\Model\Orm\ClassSection\ClassSectionModel
	- App\AdminModule\Presenters\Class\Components\DataGridHelper\DataGripCreationHelper
