{layout $templates.'/@layout-new.latte'}
{var $dataGridShown = true}

{block #content}
<div class="main__main main__main--one-column">
	<div class="main__header">
		{include $templates.'/part/box/header.latte',
			props: [
				title: 'Produkty',
				isPageTitle: true,
			]
		}
	</div>
	<div class="main__content scroll scroll--horizontal">

		{control dataGrid}

	</div>

{*	<div class="main__content-side scroll">*}
{*		{control shellForm}*}
{*	</div>*}
</div>



{*<div class="box-title">*}
{*	<p class="r">*}
{*		<a n:href="Product:services" class="btn btn-icon-before">*}
{*			<span><span class="icon icon-settings"></span> Správa služeb</span>*}
{*		</a>*}

{*		<a n:href="Product:add" class="btn btn-icon-before">*}
{*			<span><span class="icon icon-plus"></span> {_'new_product_button'}</span>*}
{*		</a>*}
{*	</p>*}
{*	<h1>{_"Products"}</h1>*}
{*</div>*}
{*<br/>*}
