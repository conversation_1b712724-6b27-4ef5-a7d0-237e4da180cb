<?php declare(strict_types=1);

namespace App\AdminModule\Presenters\Catalog;

use App\AdminModule\Presenters\BasePresenter;
use App\AdminModule\Presenters\Catalog\Components\DataGrid\DataGrid;
use App\AdminModule\Presenters\Catalog\Components\DataGrid\DataGridFactory;
use App\AdminModule\Presenters\Catalog\Components\ShellForm\ShellForm;
use App\AdminModule\Presenters\Catalog\Components\ShellForm\ShellFormFactory;

final class CatalogPresenter extends BasePresenter
{
	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
		private readonly ShellFormFactory $shellFormFactory,
	)
	{
		parent::__construct();
	}

	protected function createComponentDataGrid(): DataGrid
	{
		return $this->dataGridFactory->create($this->userEntity);
	}

	protected function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create();
	}

}
