<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Catalog\Components\DataGrid\DataSource;

use Symfony\Component\PropertyAccess\PropertyAccess;

class DataMapper
{

	public function __construct(
		private readonly array $maps,
		private readonly array $dataTemplate = []
	)
	{
	}


	public function tryReMap(array $data): ?array
	{
		$changed = false;
		$newData = $this->dataTemplate;
		$propertyAccessor = PropertyAccess::createPropertyAccessorBuilder()
			->enableExceptionOnInvalidIndex()
			->getPropertyAccessor();
		foreach ($this->maps as $path => $newPath) {
			if ($propertyAccessor->isReadable($data, $path)) {
				$value = $propertyAccessor->getValue($data, $path);
				$propertyAccessor->setValue($newData, $newPath, $value);
				$changed = true;
			}
		}

		bd($newData);

		if ($changed) {
			return $newData; // @phpstan-ignore-line
		} else {
			return null;
		}
	}

}
