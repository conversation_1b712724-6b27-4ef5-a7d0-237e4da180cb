<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Catalog\Components\ShellForm;

use App\AdminModule\Presenters\Catalog\Components\ShellForm\FormData\BaseFormData;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Mutation\MutationRepository;
use App\Model\Orm\Product\ProductModel;
use App\Model\Translator;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nextras\Orm\Collection\ICollection;

class ShellForm extends Control
{

	/** @var ICollection<Mutation>  */
	private ICollection $mutations;

	public function __construct(
		private readonly Translator $translator,
		private readonly MutationRepository $mutationRepository,
		private readonly ProductModel $productModel,
	)
	{
		$this->onAnchor[] = [$this, 'init'];
	}


	public function init(): void
	{
		$this->mutations = $this->mutationRepository->findBy([]);
	}

	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$template->add('templates', RS_TEMPLATE_DIR);
		$template->add('mutations', $this->mutations);

		$template->render(__DIR__ . '/shellForm.latte');
	}


	protected function createComponentForm(): Form
	{
		$form = new Form();
		$form->setMappedType(BaseFormData::class);
		$form->setTranslator($this->translator);


//		$templates = [':Front:Product:detail'=>'product'];
//		$form->addSelect('template', 'select_template', $templates);
		$form->addSubmit('send', 'send');

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];
		return $form;
	}


	public function formError(Form $form): void
	{
		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSucceeded(Form $form, BaseFormData $data): void
	{
		$product = $this->productModel->create(':Front:Product:detail');

		$this->presenter->redirect('Product:edit', ['id' => $product->id]);

	}

}
