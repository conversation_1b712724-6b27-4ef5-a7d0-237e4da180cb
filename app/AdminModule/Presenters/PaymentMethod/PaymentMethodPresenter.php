<?php

namespace App\AdminModule\Presenters\PaymentMethod;

use App\AdminModule\Presenters\BasePresenter;
use App\AdminModule\Presenters\PaymentMethod\Components\DataGrid\DataGrid;
use App\AdminModule\Presenters\PaymentMethod\Components\DataGrid\DataGridFactory;
use App\AdminModule\Presenters\PaymentMethod\Components\Form\Form;
use App\AdminModule\Presenters\PaymentMethod\Components\Form\FormFactory;
use App\AdminModule\Presenters\PaymentMethod\Components\ShellForm\ShellForm;
use App\AdminModule\Presenters\PaymentMethod\Components\ShellForm\ShellFormFactory;
use App\Model\Orm\PaymentMethod\PaymentMethodConfiguration;

class PaymentMethodPresenter extends BasePresenter
{

	private ?PaymentMethodConfiguration $object;

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
		private readonly ShellFormFactory $shellFormFactory,
		private readonly FormFactory $formFactory,
	)
	{
	}

	public function startup(): void
	{
		parent::startup();
	}



	public function actionEdit(int $id): void
	{
		$this->object = $this->orm->paymentMethod->getById($id);

		if (!$this->object) {
			$this->redirect('default');
		}
	}

	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create();
	}

	protected function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create();
	}

	protected function createComponentForm(): Form
	{
		return $this->formFactory->create($this->object, $this->userEntity);
	}

}
