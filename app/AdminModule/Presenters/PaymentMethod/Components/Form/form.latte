{varType App\Model\Orm\Mutation\Mutation $mutation}
<form n:name="form" class="main__main" data-controller="pagemenu">
	<div class="main__header">
		{include $templates.'/part/box/header.latte',
			props: [
				hrefClose: 'default',
				img: $imgSrc,
				title: '['.$paymentMethodConfiguration->paymentMethodUniqueIdentifier.'] '.$paymentMethodConfiguration->name,
				hasGeneratedMenu: true,
				isPageTitle: true,
			]
		}
	</div>
	<div class="main__content scroll">

		{snippet flash}
			<div n:foreach="$flashes as $flash" class="message message-{$flash->type}">{$flash->message}</div>
		{/snippet}

		{include './parts/content/content.latte', form => $form}
		{include './parts/content/prices.latte', form => $form}
		{include './parts/content/vats.latte', form => $form}
		{include './parts/content/pageText.latte', form => $form}

		{if $paymentMethodConfiguration->getCfScheme() !== []}
			{include './parts/content/custom-fields.latte', form=>$form}
		{/if}

	</div>

	<div class="main__content-side scroll">
		{include $corePartsDirectory . '/side/state.latte', form: $form}
		{include $corePartsDirectory . '/side/btns.latte'}
	</div>

	{include './parts/overlay/editItem.latte', form=>$form}
</form>

{include './parts/newItemTemplate.latte', form: $form}
{include $templates . '/part/core/libraryOverlay.latte'}
