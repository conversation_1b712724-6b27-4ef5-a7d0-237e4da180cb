{var $anchorName = 'settings-main'}
{var $icon = $templates . '/part/icons/cog.svg'}
{var $title = 'Hlavní nastavení'}

{var $props = [
title: $title,
id: $anchorName,
icon: $icon,
variant: 'main',
open: true,
classes: ['u-mb-xxs'],
]}

{embed $templates . '/part/box/toggle.latte', props => $props, templates => $templates}
	{block content}
		{varType App\Model\Orm\User\User $userEntity}
		{include $templates.'/part/core/inp.latte' props: [
			input: $form['name'],
			classesLabel: ['title']
		]}
		{include $templates.'/part/core/inp.latte' props: [
			input: $form['desc'],
			type: 'textarea',
			classesLabel: ['title']
		]}
		{include $templates.'/part/core/inp.latte' props: [
			input: $form['tooltip'],
			type: 'textarea',
			classesLabel: ['title']
		]}
		{include $templates.'/part/core/inp.latte' props: [
			input: $form['sort'],
			type: 'number',
			classesLabel: ['title']
		]}
		{include $templates.'/part/core/checkbox.latte' props: [
			input: $form['isRecommended'],
			type: 'checkbox',
			classesLabel: ['title']
		]}

		{include $templates.'/part/core/inp.latte' props: [
			input: $form['currencies'],
			type: 'select',
			classesLabel: ['title']
		]}

		{*include $templates . '/part/core/inp.latte', props: [
			input: $form['name'],
			classesLabel: ['title'],
		]*}
	{/block}
{/embed}
