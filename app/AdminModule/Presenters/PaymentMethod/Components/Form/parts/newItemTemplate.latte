<div class="u-hide">
	<div data-Templates-target="priceItem">
		{include $templates.'/part/box/list-item.latte',
		props: [
			dragdrop: false,
			data: [
				controller: 'DeliveryPrice RemoveItem',
				action: 'DeliveryPriceEdit:updateValues@window->DeliveryPrice#updateValues',
				removeitem-target: 'item',
				DeliveryPrice-id-value: 'newItemMarker',
			],
			texts: [
				[
				text: '[<span data-DeliveryPrice-target="name">Nová cena</span>] <span data-DeliveryPrice-target="price"></span><span data-DeliveryPrice-target="freeFrom"></span>',
				]
			],
			btnsAfter: [
				[
				icon: $templates.'/part/icons/pencil-alt.svg',
				tooltip: 'Editovat',
				data: [
					controller: 'Toggle',
					action: 'Toggle#changeClass DeliveryPrice#edit',
					toggle-target-value: '#overlay-newItemMarker',
					toggle-target-class-value: 'is-visible'
					]
				],
				[
				icon: $templates.'/part/icons/trash.svg',
				tooltip: 'Odstranit',
				variant: 'remove',
				data: [
					action: 'RemoveItem#remove DeliveryPrice#remove',
					]
				]
			]
		]
		}
	</div>

	<div data-Templates-target="priceOverlay">
		{var $pricesContainer = $form['prices']['newItemMarker']}

		{embed $templates.'/part/core/overlay.latte', props: [
			id: 'newItemMarker',
			title: 'Editace / Přidání ceny za platbu',
			data: [
				controller: 'DeliveryPriceEdit',
				DeliveryPriceEdit-id-value: 'newItemMarker',
			],
			classes: ['is-visible'],
		], templates=>$templates}
			{block content}
				{include './overlay/price.latte', priceId: 'newItemMarker', pricesContainer: $pricesContainer}
			{/block}
		{/embed}
	</div>
</div>
