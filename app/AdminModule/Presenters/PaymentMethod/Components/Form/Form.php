<?php

declare(strict_types=1);

namespace App\AdminModule\Presenters\PaymentMethod\Components\Form;

use App\AdminModule\Presenters\PaymentMethod\Components\Form\FormData\BaseFormData;
use App\Model\ConfigService;
use App\Model\CustomField\SuggestUrls;
use App\Model\Orm\Order\OrderState;
use App\Model\Orm\Orm;
use App\Model\Orm\PaymentMethod\PaymentMethodConfiguration;
use App\Model\Orm\User\User;
use App\Model\Translator;
use App\Model\TranslatorDB;
use Nette\Application\UI\Control;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use const RS_TEMPLATE_DIR;

/**
 * @property-read DefaultTemplate $template
 */
final class Form extends Control
{

	public function __construct(
		private readonly PaymentMethodConfiguration $paymentMethodConfiguration,
		private readonly User $userEntity,
		private readonly Builder $formBuilder,
		private readonly Handler $formHandler,
		private readonly SuggestUrls $urls,
		private readonly Translator $translator,
		private readonly TranslatorDB $translatorDB,
		private readonly Orm $orm,
		private readonly ConfigService $configService,
	)
	{
	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$template->add('templates', RS_TEMPLATE_DIR);
		$template->add('paymentMethodConfiguration', $this->paymentMethodConfiguration);
		$template->add('imgSrc', '');
		$template->add('corePartsDirectory', \App\PostType\Core\AdminModule\Components\Form\Form::TEMPLATE_PARTS_DIR);

		$template->orm = $this->orm;
		$template->translatorDB = $this->translatorDB;
		$template->fileUploadLink = $this->presenter->link(':Admin:File:upload');

		$template->config = $this->configService->getParams();
		$template->userEntity = $this->userEntity;

		$template->showDeleteButton = $this->paymentMethodConfiguration->orderItems->toCollection()->findBy(['order->state!=' => OrderState::Draft])->countStored() === 0;

		$template->urls = $this->urls;
		$template->render(__DIR__ . '/form.latte');
	}

	protected function createComponentForm(): \Nette\Application\UI\Form
	{
		$form = new \Nette\Application\UI\Form();
		$form->setMappedType(BaseFormData::class);

		$this->formBuilder->build($form, $this->paymentMethodConfiguration, $this->userEntity);

		$form->setTranslator($this->translator);
		$form->onSuccess[] = $this->formSucceeded(...);
		$form->onError[] = $this->formError(...);
		$form->onValidate[] = $this->formValidate(...);
		return $form;
	}
	private function formValidate(\Nette\Application\UI\Form $form, BaseFormData $data): void
	{
		foreach ($data->prices as $priceId => $priceData) {
			if ($priceId === 'newMarkerItem') {
				continue;
			}

			if (!is_int($priceId)) {
				$exists = $this->orm->paymentMethodPrice->getBy(['paymentMethod' => $this->paymentMethodConfiguration, 'priceLevel' => $priceData->priceLevel, 'state' => $priceData->state, 'price->currency' => $priceData->currency]);
				if ($exists !== null) {
					$form->addError('price_already_exists');
				}
			}

		}
	}
	private function formSucceeded(\Nette\Application\UI\Form $form, BaseFormData $data): void
	{
		$this->formHandler->handle($this->paymentMethodConfiguration, $data, $this->userEntity);
		$this->presenter->redirect('edit', ['id' => $this->paymentMethodConfiguration->id]);
	}

	private function formError(\Nette\Application\UI\Form $form): void
	{
		$this->flashMessage('Error: ' . implode(', ', $form->getErrors()), 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	public function handleDelete(): void
	{
		$this->orm->removeAndFlush($this->paymentMethodConfiguration);
		$this->presenter->flashMessage('msg_ok_deleted', 'ok');
		$this->presenter->redirect('default');
	}

}
