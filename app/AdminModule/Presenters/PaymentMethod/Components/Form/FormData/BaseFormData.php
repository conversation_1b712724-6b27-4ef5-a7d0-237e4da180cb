<?php declare(strict_types=1);

namespace App\AdminModule\Presenters\PaymentMethod\Components\Form\FormData;

use App\PostType\Core\AdminModule\Components\Form\FormData\PublishFormData;
use Nette\Utils\ArrayHash;

final class BaseFormData
{

	public PublishFormData $publish;

	public string $name;

	public string $desc;

	public ?string $tooltip;

	public int $sort;

	public bool $isRecommended;

	public ArrayHash $vats;

	public ArrayHash $prices;

	public string $cf;

	public array $currencies;

	public ?string $pageText;

	public bool $pageShow;

}
