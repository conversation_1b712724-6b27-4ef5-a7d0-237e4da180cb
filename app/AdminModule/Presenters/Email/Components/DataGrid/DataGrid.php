<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Email\Components\DataGrid;

use App\Model\Orm\EmailTemplate\EmailTemplate;
use App\Model\Orm\EmailTemplate\EmailTemplateRepository;
use App\Model\Orm\Orm;
use App\Model\Translator;
use App\PostType\Core\AdminModule\Components\DataGrid\HasMutationColumn;
use LogicException;
use Nette\Application\UI\Control;
use Ublaboo\DataGrid\Exception\DataGridException;

class DataGrid extends Control
{

	use HasMutationColumn;

	public function __construct(
		private readonly bool $isDeveloper,
		private readonly Translator $translator,
		private readonly Orm $orm,
		private readonly EmailTemplateRepository $emailTemplateRepository,
	)
	{
	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);

		$template->render(__DIR__ . '/dataGrid.latte');
	}


	/**
	 * @throws DataGridException
	 */
	public function createComponentGrid(): \Ublaboo\DataGrid\DataGrid
	{
		$grid = new \Ublaboo\DataGrid\DataGrid();

		$collection = $this->emailTemplateRepository->findBy(['isHidden' => 0]);

		$grid->setItemsPerPageList([30, 50]);

		if (!$this->isDeveloper) {
			$collection = $collection->findBy(['isDeveloper' => 0]);
		}

		$grid->setDataSource($collection);
		$grid->addColumnText('name', 'name')->setSortable()->setFilterText();
		$grid->addColumnText('subject', 'subject')->setSortable()->setFilterText();

		if ($this->isDeveloper) {
			$grid->addColumnText('key', 'label_key')->setFilterText();
			$grid->addColumnText('isDeveloper', 'onlyForDevelopers')
				->setTemplateEscaping(false)
				->setRenderer(
					function (EmailTemplate $emailTemplate) {
						return ($emailTemplate->isDeveloper === 1 ) ? '<span class="btn-success btn btn-xs">Pro developery</span>' : '';
					}
				);
		}

		$this->addColumnMutation($grid);

		$grid->addAction('edit', 'Edit', ':edit')->setClass('btn btn-xs btn-primary');

		$grid->setTranslator($this->translator);

		return $grid;
	}

}
