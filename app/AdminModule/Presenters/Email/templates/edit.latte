{templateType AdminModule\EmailPresenterTemplate}

{block #content}

{snippet flash}
	<div n:foreach="$flashes as $flash" class="message message-{$flash->type}">{$flash->message}</div>
{/snippet}

{snippet editForm}
	<p class="r">
		<a class="btn btn-icon-before" n:href="default">
			<span><span class="icon icon-arrow-left "></span> {_Back}</span>
		</a>
	</p>
	<h1>{$object->name}</h1>
	<p>
		<span class="icon icon-globe"></span>
		<span class=""><strong>{$object->mutation->name}</strong></span>
	</p>

	<form n:name="editForm">
		{*input id*}
		<div class="grid-row">
			<p class="grid-1-3">
				{label name /}<br>
				<span class="inp-fix">
					{input name class => 'inp-text w-full'}
				</span>
			</p>
			<p class="grid-1-3">
				{label subject /}<br>
				<span class="inp-fix">
					{input subject class => 'inp-text w-full'}
				</span>
			</p>
			<p class="grid-1-5" n:if="!$object">
				{label mutation /}
				<span class="inp-fix  inp-fix-select">
					{input mutation class => 'inp-text'}
				</span>
			</p>
		</div>
		<div class="grid-row" n:if="$isDeveloper">
			<p class="grid-1-3" >
				{label key /}
				<span class="inp-fix">
					{input key class => 'inp-text'}
				</span>
			</p>
			<p class="grid-1-3 no-label" >
				<span class="inp-item inp-center">
					{input isDeveloper:}
					{label isDeveloper: /}
				</span>
			</p>
		</div>

		{php  $pTabs = $config['tabs']['emailTemplates']}
		<div class="menu-tabs">
			<ul class="reset">
				<li><a href="#tab-default">{_tab_content}</a></li>
				<li n:if="in_array('files', $pTabs)"><a href="#tab-files">{_tab_files}</a></li>
			</ul>
		</div>

		<div id="tab-default" class="tab-fragment">
			<div class="grid-row">
				<div class="grid-1">
					<p>
						{label body /}<br>
						<span class="inp-fix">
						{input body class => 'inp-text w-full emailTemplateWysiwag'}
					</span>
					</p>
				</div>
				<div class="grid-1-3">
					{*include "part/emailHelp.latte"*}
				</div>
			</div>
		</div>

		<div id="tab-files" class="tab-fragment" n:if="in_array('files', $pTabs) && isset($object)">
			<div class="crossroad-attached">
				<div class="holder {if !isset($object) || !$object->files}hide{/if}">
					<div class="hd">
						<div class="grid-row">
							<p class="grid-1">
								{_name}
							</p>
						</div>
					</div>
					<div class="bd">
						<ul class="sortable reset" id="files">
							{foreach $object->files as $file}
								{include 'part/form/files.latte', 'data' => $file}
							{/foreach}
						</ul>
					</div>
				</div>
				<div class="ft">
					<p>
						<span href="#" class="btn btn-icon-before">
							<span><span class="icon icon-plus"></span> {_add_button}</span>

							<input id="file_upload{$object->id}" class="file_upload" name="file_upload"
								   type="file"
								   multiple="true" data-method="prepend"
								   data-script="{plink uploadFiles!}"
								   data-place="files" data-idref="{$object->id}"
								   data-pattern='<li id="${l}fileID{r}" class="uploadifive-queue-item"><div class="thumb js-handle"><span class="img"></span><div class="uploadify-progress"><div class="uploadify-progress-bar"></div></div></div></li>'/>

						</span>
					</p>
				</div>
			</div>
		</div>

		<div class="fixed-bar">
			<button n:name="save" class="btn btn-green btn-icon-before">
				<span><span class="icon icon-checkmark"></span> {_save_button}</span>
			</button>
			{include $templates . '/part/box/btnSaveBack.latte'}

			<a n:if="$isDeveloper" n:href="delete! $object->id"
					class="btn btn-red btn-icon-before btn-delete ajax">
				<span><span class="icon icon-close"></span> {_delete_button}</span>
			</a>
		</div>
	</form>
{/snippet}
