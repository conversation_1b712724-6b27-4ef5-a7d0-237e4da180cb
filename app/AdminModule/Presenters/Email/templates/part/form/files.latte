<li>
	{php $idFile = isset($data->file) ?$data->file : $data->id }
	<div class="inner">
		<span class="drag-area js-handle"></span>
		<div class="grid-row">
			<p class="grid-1">
				<span class="inp-fix inp-icon-before">
					<a href="{$data->url}" class="icon icon-file-4 icon-file-{$data->ext}"></a>
					<input type="text" class="inp-text w-full" name="fileName[{$idFile}]" value="{$data->name}" id="inp-video" />
				</span>
			</p>
		</div>
		<input type="hidden" name="fileSort[{$idFile}]" value="{$data->sort}" class="inp-sort" />
		<input type="hidden" name="fileSize[{$idFile}]" value="{$data->size}" class="inp-sort" />
		<a href="#" class="icon icon-close remove"></a>
	</div>
</li>