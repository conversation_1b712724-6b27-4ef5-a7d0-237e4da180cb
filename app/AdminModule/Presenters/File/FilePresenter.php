<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\File;

use App\AdminModule\Presenters\BasePresenter;
use App\Model\Orm\File\FileModel;
use Nette\Http\FileUpload;
use Nette\Utils\Json;

final class FilePresenter extends BasePresenter
{

	public function __construct(
		private readonly FileModel $fileModel,
	)
	{
		parent::__construct();
	}


	public function actionUpload(): void
	{
		if (isset($_FILES['file'])) {
			$file = new FileUpload($_FILES['file']);
			$fileEntity = $this->fileModel->add($file);
			echo Json::encode([
				'id' => $fileEntity->id,
			]);
		}

		$this->terminate();
	}

}
