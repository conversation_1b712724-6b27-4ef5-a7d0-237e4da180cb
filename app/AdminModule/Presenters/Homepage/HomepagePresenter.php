<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Homepage;

use App\AdminModule\Presenters\BasePresenter;
use App\Model\Mutation\MutationsHolder;

final class HomepagePresenter extends BasePresenter
{

	public function __construct(
	)
	{
		parent::__construct();
	}

	public function renderDefault(): void
	{
		$mainMutation = $this->mutationsHolder->getDefault();
		$this->redirect(':Page:Admin:Page:default', ['id' => $mainMutation->rootId]);
	}

}
