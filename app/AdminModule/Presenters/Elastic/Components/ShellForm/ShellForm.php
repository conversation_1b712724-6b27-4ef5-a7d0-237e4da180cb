<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Elastic\Components\ShellForm;

use App\AdminModule\Presenters\Elastic\Components\ShellForm\FormData\BaseFormData;
use App\Model\ElasticSearch\IndexModel;
use App\Model\Orm\EsIndex\EsIndex;
use App\Model\Orm\EsIndex\EsIndexModel;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Mutation\MutationRepository;
use App\Model\Translator;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nextras\Orm\Collection\ICollection;

class ShellForm extends Control
{

	/** @var ICollection<Mutation>  */
	private ICollection $mutations;

	private array $esIndexType;

	public function __construct(
		private readonly Translator $translator,
		private readonly EsIndexModel $esIndexModel,
		private readonly MutationRepository $mutationRepository,
	)
	{
		$this->onAnchor[] = [$this, 'init'];
	}


	public function init(): void
	{
		$this->mutations = $this->mutationRepository->findBy([]);
		$esIndexType = [];
		foreach (EsIndex::getConstsByPrefix('TYPE_') as $key => $value) {
			$esIndexType[$key] = sprintf('index_type_%s', $value);
		}

		$this->esIndexType = $esIndexType;
	}

	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$template->add('templates', RS_TEMPLATE_DIR);
		$template->add('mutations', $this->mutations);

		$template->render(__DIR__ . '/shellForm.latte');
	}


	protected function createComponentForm(): Form
	{
		$form = new Form();
		$form->setMappedType(BaseFormData::class);
		$form->setTranslator($this->translator);


		$form->addSelect('type', 'select_type', $this->esIndexType)->setRequired();
		$form->addSelect('mutation', 'select_mutation', $this->mutations->fetchPairs('id', 'name'))->setRequired();
		$form->addSubmit('send', 'send');

		$form->onValidate[] = [$this, 'formValidate'];
		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];
		return $form;
	}


	public function formValidate(Form $form, BaseFormData $data): void
	{
		$selectedMutation = $this->mutationRepository->getById($data->mutation);
		if ($selectedMutation === null) {
			$form['mutation']->addError('invalid_mutation');
		}

		if ($data->type === EsIndex::TYPE_ALL) {
			$defaultRsMutation = $this->mutationRepository->getRsDefault();
			if ($defaultRsMutation !== $selectedMutation) {
				$form->addError('combination_of_type_and_mutation_is_invalid');
			}
		}

	}


	public function formError(Form $form): void
	{
		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSucceeded(Form $form, BaseFormData $data): void
	{
		$mutation = $this->mutationRepository->getByIdChecked($data->mutation);
		$this->esIndexModel->creteNewWithIndex($data->type, $mutation);

		$this->presenter->redirect('this');
	}

}
