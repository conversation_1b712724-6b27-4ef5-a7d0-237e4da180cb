<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Elastic;

use App\AdminModule\Presenters\BasePresenter;
use App\AdminModule\Presenters\Elastic\Components\DataGrid\DataGrid;
use App\AdminModule\Presenters\Elastic\Components\DataGrid\DataGridFactory;
use App\AdminModule\Presenters\Elastic\Components\DisconnectedIndexDataGrid\DisconnectedIndexDataGrid;
use App\AdminModule\Presenters\Elastic\Components\DisconnectedIndexDataGrid\DisconnectedIndexDataGridFactory;
use App\AdminModule\Presenters\Elastic\Components\ShellForm\ShellForm;
use App\AdminModule\Presenters\Elastic\Components\ShellForm\ShellFormFactory;
use App\Model\Orm\EsIndex\EsIndexRepository;

final class ElasticPresenter extends BasePresenter
{

	public function __construct(
		private readonly EsIndexRepository $esIndexRepository,
		private readonly DataGridFactory $dataGridFactory,
		private readonly DisconnectedIndexDataGridFactory $disconnectedIndexDataGridFactory,
		private readonly ShellFormFactory $shellFormFactory,
	)
	{
		parent::__construct();
	}


	protected function createComponentGrid(): DataGrid
	{
		$activeIndexes = $this->esIndexRepository->findBy(['active' => 1]);
		return $this->dataGridFactory->create($activeIndexes, true);
	}


	protected function createComponentDisconnectedIndexDataGrid(): DisconnectedIndexDataGrid
	{
		return $this->disconnectedIndexDataGridFactory->create();
	}


	protected function createComponentGridInActive(): DataGrid
	{
		$activeIndexes = $this->esIndexRepository->findBy(['active' => 0]);
		return $this->dataGridFactory->create($activeIndexes);
	}

	protected function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create();
	}

}
