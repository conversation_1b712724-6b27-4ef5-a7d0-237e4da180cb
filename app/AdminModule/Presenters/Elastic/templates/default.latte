{layout $templates.'/@layout-new.latte'}
{var $dataGridShown = true}

{block #content}
<div class="main__main">
	<div class="main__header">
		{include $templates.'/part/box/header.latte',
			props: [
			title: 'Elastic search',
			isPageTitle: true,
		]
		}
	</div>
	<div class="main__content scroll">

		<h3>Aktuální indexy</h3>
		{control grid}

		<h3>Neaktivní indexy</h3>
		{control gridInActive}

		<h3>Odpojené indexy</h3>
		{control disconnectedIndexDataGrid}
	</div>

	<div class="main__content-side scroll">
		{control shellForm}
	</div>
</div>
