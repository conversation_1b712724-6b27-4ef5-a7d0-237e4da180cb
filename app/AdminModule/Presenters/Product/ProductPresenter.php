<?php

declare(strict_types=1);

namespace App\AdminModule\Presenters\Product;

use App\AdminModule\Components\ProductForm\ProductForm;
use App\AdminModule\Components\ProductForm\ProductFormFactory;
use App\AdminModule\Presenters\BasePresenter;
use App\Model\Orm\Alias\AliasModel;
use App\Model\Orm\File\FileModel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariantModel;
use Nette\Application\Attributes\Persistent;
use Nette\Application\UI;
use Nette\DI\Attributes\Inject;
use Nette\Http\FileUpload;
use Nette\Utils\Json;

final class ProductPresenter extends BasePresenter
{

	#[Persistent]
	public string $sort;

	#[Inject]
	public ProductVariantModel $productVariantModel;

	#[Inject]
	public FileModel $fileModel;

	#[Inject]
	public ProductFormFactory $productFormFactory;

	#[Inject]
	public AliasModel $aliasModel;

	private ?Product $object = null;


	public function actionEdit(?int $id = null): void
	{
		if ($id === null) {
			$this->redirect('Catalog:');
		}

		$this->object = $this->orm->product->getById($id);
		if (!$this->object) {
			$this->redirect('Catalog:');
		}
	}


//	DEBUG UPLOADIFY
	public function handleUpload(?FileUpload $file = null): never
	{
		//\Nette\Diagnostics\Debugger::$consoleMode = true;

		$file ??= new FileUpload($_FILES['file']);

		$fileEntity = $this->fileModel->add($file);
//		$template = $this->createTemplate()->setFile(APP_DIR . "/AdminModule/templates/Page/part/form/files.latte");
//		$template->data = $file;
		echo Json::encode([
			'id' => $fileEntity->id,
		]);
		$this->terminate();
	}


	public function actionRegenerateAliasMutation(int $id, string $name, string $lang): void
	{
		$mutation = $this->orm->mutation->getByCode($lang);

		$product = $this->orm->product->getById($id);
		$this->orm->setMutation($mutation);

		$productLocalization = $product->getLocalization($mutation);
		echo $this->aliasModel->handleAliasChange($productLocalization, $mutation, $name, false);
		$this->terminate();
	}


	protected function createComponentProductForm(): ProductForm
	{
		return $this->productFormFactory->create($this->object, $this->userEntity);
	}
//
//
//	protected function createComponentDataGrid(): ProductForm
//	{
//		return $this->productFormFactory->create($this->object, $this->userEntity);
//	}

}
