<?php declare(strict_types=1);

namespace App\AdminModule\Presenters\ClassEvent;

use App\AdminModule\Presenters\BasePresenter;
use App\AdminModule\Presenters\ClassEvent\Components\Form\Form;
use App\AdminModule\Presenters\ClassEvent\Components\Form\FormFactory;
use App\AdminModule\Presenters\ClassSection\Components\MetaDataGrid\MetaDataGrid;
use App\AdminModule\Presenters\ClassSection\Components\MetaDataGrid\MetaDataGridFactory;
use App\Model\Orm\ClassEvent\ClassEvent;
use App\Model\Orm\Product\Product;
use Nette\Application\Attributes\Persistent;
use Nette\DI\Attributes\Inject;

class ClassEventPresenter extends BasePresenter
{

	#[Persistent]
	public int $id;

	#[Persistent]
	public int $productId;

	#[Inject]
	public MetaDataGridFactory $metaDataGridFactory;

	#[Inject]
	public FormFactory $formFactory;

	private Product $product;

	private ClassEvent $classEvent;

	public function actionEdit(int $id, int $productId): void
	{
		$this->product = $this->orm->product->getByIdChecked($productId);
		$this->classEvent = $this->product->classEvents->toCollection()->getByIdChecked($id);
	}
	public function renderEdit(): void
	{
		$this->template->product = $this->product;
		$this->template->classEvent = $this->classEvent;
	}

	protected function createComponentForm(): Form
	{
		return $this->formFactory->create($this->classEvent);
	}

	protected function createComponentMetaDataGrid(): MetaDataGrid
	{
		return $this->metaDataGridFactory->create(classEvent: $this->classEvent);
	}

}
