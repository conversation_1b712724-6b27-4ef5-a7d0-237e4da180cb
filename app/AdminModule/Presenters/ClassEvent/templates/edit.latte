{layout $templates.'/@layout-new.latte'}
{var $dataGridShown = true}

{block #content}
{varType App\Model\Orm\Product\Product $product}
{varType App\Model\Orm\ClassEvent\ClassEvent $classEvent}

<div class="main__main" data-controller="pagemenu">
	<div class="main__header">
		{include $templates.'/part/box/header.latte',
			props: [
				hrefClose: $presenter->link('Class:edit', ['id' => $product->id]),
				title: $product->name . ' - ' . $classEvent->getNiceName(),
				hasGeneratedMenu: true,
				isPageTitle: true,
			]
		}
	</div>
	<div class="main__content scroll">
		<div class="message message--{$flash->type} u-mb-sm" n:foreach="$flashes as $flash">
			{$flash->message}
		</div>
		{control form}
		{control metaDataGrid}
	</div>


	<div class="main__content-side scroll">
		{control form:side}
	</div>
</div>



