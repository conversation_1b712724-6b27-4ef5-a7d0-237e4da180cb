<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\ClassEvent\Components\Form;

use App\Model\Orm\ClassEvent\ClassEvent;
use App\Model\Orm\ClassEvent\ClassEventRepository;
use App\Model\Orm\User\UserRepository;
use Nextras\Dbal\Utils\DateTimeImmutable;

final class FormSuccess
{

	public function __construct(
		//		private readonly CustomFields $customFields,
		//		private readonly CustomContent $customContent,
		private readonly ClassEventRepository $classEventRepository,
		private readonly UserRepository $userRepository,
	)
	{
	}

	public function execute(ClassEvent $classEvent, FormBaseData $submittedValues): void
	{
		$classEvent->city = $submittedValues->city;
		$classEvent->place = $submittedValues->place;
		$classEvent->capacity = $submittedValues->limit;

		$from = DateTimeImmutable::createFromFormat('Y-m-d H:i:s', $submittedValues->from->format('Y-m-d H:i:s'));
		if ($from instanceof DateTimeImmutable) {
			$classEvent->from = $from;
		}

		$to = DateTimeImmutable::createFromFormat('Y-m-d H:i:s', $submittedValues->from->format('Y-m-d H:i:s'));
		if ($to instanceof DateTimeImmutable) {
			$classEvent->from = $to;
		}

		$lectors = $classEvent->lectors->toCollection()->fetchPairs('id');

		foreach ($lectors as $lector) {
			if (!in_array($lector->id, $submittedValues->lectors)) {
				$classEvent->lectors->remove($lector);
			}
		}

		foreach ($submittedValues->lectors as $lectorId) {
			if (!isset($lectors[$lectorId])) {
				$classEvent->lectors->add($this->userRepository->getById($lectorId));
			}
		}

		$classEvent->public = (int) $submittedValues->public;

		$this->classEventRepository->persistAndFlush($classEvent);
	}

}
