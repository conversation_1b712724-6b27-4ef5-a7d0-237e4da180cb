<?php declare(strict_types=1);

namespace App\AdminModule\Presenters\ClassEvent\Components\Form;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\Model\Orm\ClassEvent\ClassEvent;
use App\Model\Translator;
use App\PostType\Core\AdminModule\Components\Form\Form as CoreForm;
use Nette\Application\UI\Control;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;

/**
 * @property-read DefaultTemplate $template
 */
final class Form extends Control
{

	public function __construct(
		private readonly ClassEvent $classEvent,
		private readonly Translator $translator,
		private readonly FormBuilder $formBuilder,
		private readonly FormSuccess $formSuccess,
		private readonly MessageForFormFactory $messageForFormFactory,
	)
	{
		$this->onAnchor[] = $this->init(...);
	}


	private function init(): void
	{
	}

	public function initTemplate(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->add('corePartsDirectory', CoreForm::TEMPLATE_PARTS_DIR);
		$this->template->add('templates', RS_TEMPLATE_DIR);
		$this->template->add('classEvent', $this->classEvent);
	}


	public function render(): void
	{
		$this->initTemplate();
		$this->template->classEvent = $this->classEvent;
		$this->template->render(__DIR__ . '/form.latte');
	}

	public function renderSide(): void
	{
		$this->template->form = $this->getComponent('form');
		$this->template->render(__DIR__ . '/side.latte');
	}


	protected function createComponentForm(): \Nette\Application\UI\Form
	{
		$form = new \Nette\Application\UI\Form();
		$form->setMappedType(FormBaseData::class);
		$form->setTranslator($this->translator);

		$this->formBuilder->build($form, $this->classEvent);

		$form->onSuccess[] = $this->formSuccess(...);
		$form->onError[] = $this->formError(...);
		return $form;
	}

	public function formError(\Nette\Application\UI\Form $form): void
	{
		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSuccess(\Nette\Application\UI\Form $form, FormBaseData $values): void
	{
		$this->formSuccess->execute($this->classEvent, $values);
		$this->presenter->redirect('edit', ['id' => $this->classEvent->id, 'productId' => $this->classEvent->product->id]);
	}

	public function handleDelete(): void
	{
	}

	public function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

}
