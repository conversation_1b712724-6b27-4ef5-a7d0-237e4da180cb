<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\ClassEvent\Components\Form;

use App\Model\Orm\ClassEvent\ClassEvent;
use App\Model\Orm\Orm;
use App\Model\Orm\User\User;
use Nette\Application\UI\Form;

final class FormBuilder
{

	public function __construct(private readonly Orm $orm)
	{
	}


	public function build(Form $form, ClassEvent $classEvent): void
	{
		$form->addText('city', 'city')->setDefaultValue($classEvent->city ?? '')->setRequired();
		$form->addText('place', 'place')->setDefaultValue($classEvent->place ?? '');
		$form->addDateTime('from', 'start')->setDefaultValue($classEvent->from);
		$form->addDateTime('to', 'finish')->setDefaultValue($classEvent->to);
		$form->addInteger('limit', 'limit')->setDefaultValue($classEvent->capacity);
		$form->addMultiSelect('lectors', 'lectors', $this->orm->user->findBy(['role' => User::ROLE_LECTOR])->fetchPairs('id', 'name'))->setDefaultValue($classEvent->lectors->toCollection()->fetchPairs(null, 'id'));
		$form->addSubmit('send');

		$form->addCheckbox('public', 'public')->setDefaultValue($classEvent->public);
	}

}
