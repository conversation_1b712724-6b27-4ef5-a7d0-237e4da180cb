{varType App\Model\Orm\ClassEvent\ClassEvent $classEvent}

<form n:name="form" id="form">
	{include './parts/content/content.latte', form=>$form}
	{include './parts/content/eventDetail.latte', form=>$form}
{*	{include $corePartsDirectory . '/content/coreCustomItems.latte', form => $form, entityLocalization=>$classEvent}*}

{*	{capture $templateTargets}*}
{*		{include './parts/newItemTemplate.latte', form=>$form, product=>$product}*}
{*		{include $templates . '/part/core/libraryOverlay.latte'}*}
{*	{/capture}*}
</form>

{*{$templateTargets|noescape}*}


