<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Redirect\Components\ShellForm;

use App\AdminModule\Presenters\Redirect\Components\ShellForm\FormData\BaseFormData;
use App\Model\Orm\Redirect\RedirectModel;
use App\Model\Translator;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nette\Utils\Strings;

class ShellForm extends Control
{

	public function __construct(
		private readonly Translator $translator,
		private readonly RedirectModel $redirectModel,
	)
	{
	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$template->add('templates', RS_TEMPLATE_DIR);

		$template->render(__DIR__ . '/shellForm.latte');
	}


	protected function createComponentForm(): Form
	{
		$form = new Form();
		$form->setMappedType(BaseFormData::class);
		$form->setTranslator($this->translator);

		$form->addText('newUrl', 'newUrl')->setRequired();
		$form->addText('oldUrl', 'oldUrl')->setRequired();

		$codes = [
			301 => 301,
			302 => 302,
			303 => 303,
			307 => 307,
			308 => 308,
		];
		$form->addSelect('code', 'code', $codes)->setRequired();

		$form->addSubmit('send', 'send');

		$form->onValidate[] = [$this, 'formValidate'];
		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];
		return $form;
	}


	public function formValidate(Form $form, BaseFormData $data): void
	{
		foreach (['oldUrl', 'newUrl'] as $name) {
			if (str_contains($data->$name, '#')) {
				$form[$name]->addError('error');
			}
		}

		if ($form->hasErrors()) {
			$this->flashMessage($this->translator->translate('msg_redirect_invalid_char'), 'error');
		}
	}


	public function formError(Form $form): void
	{
		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSucceeded(Form $form, BaseFormData $data): never
	{
		$this->redirectModel->save($data->oldUrl, $data->newUrl, $data->code);

		$this->presenter->redirect('this');
	}

}
