<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Redirect\Components\DataGrid;

use App\Model\Orm\Redirect\RedirectModel;
use App\Model\Orm\Redirect\RedirectRepository;
use App\Model\Translator;
use Nette\Application\UI\Control;
use Ublaboo\DataGrid\Column\Action\Confirmation\CallbackConfirmation;
use Ublaboo\DataGrid\Exception\DataGridException;

class DataGrid extends Control
{

	public function __construct(
		private readonly RedirectRepository $redirectRepository,
		private readonly Translator $translator,
		private readonly RedirectModel $redirectModel,
	)
	{
	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);

		$template->render(__DIR__ . '/dataGrid.latte');
	}


	/**
	 * @throws DataGridException
	 */
	public function createComponentGrid(): \Ublaboo\DataGrid\DataGrid
	{
		$grid = new \Ublaboo\DataGrid\DataGrid();

		$grid->setDataSource($this->redirectRepository->findAll());
		$grid->addColumnText('oldUrl', 'oldUrl')->setSortable()->setFilterText();
		$grid->addColumnText('newUrl', 'newUrl')->setSortable()->setFilterText();
		$grid->addColumnText('code', 'code')->setSortable()->setFilterText();

//		$grid->addAction('edit', 'Edit', ':edit')->setClass('btn btn-xs btn-primary');

		$grid->setTranslator($this->translator);

		$grid->addAction('delete', '', 'delete!')
			->setTitle('Odstranit')
			->setIcon('trash')
			->setConfirmation(
				new CallbackConfirmation(
					function ($item) {
						return 'Opravdu chcete smazat tento záznam?';
					}
				)
			);

		return $grid;
	}


	public function handleDelete(int $id): void
	{
		$redirectToDelete = $this->redirectRepository->getById([
			'id' => $id,
		]);

		if ($redirectToDelete !== null) {
			$this->redirectModel->delete($redirectToDelete);
		}

		$this->presenter->redirect('this');
	}

}
