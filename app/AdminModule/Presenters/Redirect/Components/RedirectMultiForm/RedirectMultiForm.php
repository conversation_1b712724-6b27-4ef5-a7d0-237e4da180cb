<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Redirect\Components\RedirectMultiForm;

use App\Model\ConfigService;
use App\Model\Orm\Orm;
use App\Model\Orm\Redirect\RedirectModel;
use App\Model\Translator;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Drivers\Exception\UniqueConstraintViolationException;
use function assert;

/**
 * @property-read DefaultTemplate $template
 */
final class RedirectMultiForm extends UI\Control
{

	public function __construct(
		private readonly Translator $translator,
		private readonly RedirectModel $redirectModel,
		private readonly Orm $orm,
	)
	{
	}


	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$this->template->add('templates', RS_TEMPLATE_DIR);


		$this->template->render(__DIR__ . '/redirecMultiForm.latte');
	}



	protected function createComponentForm(): UI\Form
	{
		$form = new UI\Form();
		$form->setTranslator($this->translator);

		$form->addTextArea('oldUrl', 'oldUrl', 10)
			->setRequired();
		$form->addTextArea('newUrl', 'newUrl', 10)
			->setRequired();

		$form->addSubmit('send');
		$form->onSuccess[] = [$this, 'formSucceeded'];
		return $form;
	}


	public function explode(string $value): array
	{
		$result = preg_split('/\r\n|[\r\n]/', $value);
		assert($result !== false);
		return $result;
	}


	public function formSucceeded(UI\Form $form, ArrayHash $values): void
	{
		$oldArray = $this->explode($values->oldUrl);
		$newArray = $this->explode($values->newUrl);

		if (count($oldArray) !== count($newArray)) {
			$this->flashMessage('Počet starých a nových adres nesouhlasí.', 'error');
		}

		foreach ($oldArray as $key => $old) {
			try {

				// try find old redirect
				$oldRedirect = $this->orm->redirect->getBy(['oldUrl' => $old]);

				if ($oldRedirect) {
					$redirect = $this->redirectModel->save($old, $newArray[$key], $oldRedirect->code, $oldRedirect);
				} else {
					$redirect = $this->redirectModel->save($old, $newArray[$key]);
				}
			} catch (UniqueConstraintViolationException $e) {
				$this->flashMessage('Záznam \'' . $old . '\' již existuje', 'error');
			}
		}

		$this->presenter->redirect('default');
	}

}
