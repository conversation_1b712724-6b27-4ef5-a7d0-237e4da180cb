<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Redirect;

use App\AdminModule\Components\RedirectForm\RedirectForm;
use App\AdminModule\Components\RedirectForm\RedirectFormFactory;
use App\AdminModule\Presenters\BasePresenter;
use App\AdminModule\Presenters\Redirect\Components\DataGrid\DataGrid;
use App\AdminModule\Presenters\Redirect\Components\DataGrid\DataGridFactory;
use App\AdminModule\Presenters\Redirect\Components\RedirectMultiForm\RedirectMultiForm;
use App\AdminModule\Presenters\Redirect\Components\RedirectMultiForm\RedirectMultiFormFactory;
use App\AdminModule\Presenters\Redirect\Components\ShellForm\ShellForm;
use App\AdminModule\Presenters\Redirect\Components\ShellForm\ShellFormFactory;
use App\Model\Orm\Redirect\Redirect;

final class RedirectPresenter extends BasePresenter
{

	private ?Redirect $object = null;

	public function __construct(

		private readonly DataGridFactory $dataGridFactory,
		private readonly RedirectMultiFormFactory $redirectMultiFormFactory,
		private readonly ShellFormFactory $shellFormFactory,
	)
	{
		parent::__construct();
	}

	public function actionEdit(int $id): void
	{
		$this->object = $this->orm->redirect->getById($id);

		if (!$this->object) {
			$this->redirect('default');
		}
	}


	protected function createComponentRedirectMultiForm(): RedirectMultiForm
	{
		return $this->redirectMultiFormFactory->create();
	}


	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create();
	}


	protected function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create();
	}

}
