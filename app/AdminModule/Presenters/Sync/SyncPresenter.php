<?php

declare(strict_types=1);

namespace App\AdminModule\Presenters\Sync;

use App\AdminModule\Presenters\BasePresenter;
use App\AdminModule\Presenters\Sync\Components\DataGrid\DataGrid;
use App\AdminModule\Presenters\Sync\Components\DataGrid\DataGridFactory;

final class SyncPresenter extends BasePresenter
{
	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
	)
	{
	}

	public function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create();
	}
}
