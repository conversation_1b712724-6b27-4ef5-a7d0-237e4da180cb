<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Sync\Components\DataGrid;

use App\Model\CacheFactory;
use App\Model\Orm\ImportCache\ImportCache;
use App\Model\Orm\Orm;
use App\Model\Translator;
use Nette\Application\UI\Control;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Caching\Cache;
use Nextras\Dbal\Result\Row;

/**
 * @property-read DefaultTemplate $template
 */
class DataGrid extends Control
{

	public function __construct(
		private readonly Translator $translator,
		private readonly CacheFactory $cacheFactory,
		private readonly Orm $orm,
	) {
	}
	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);

		$template->render(__DIR__ . '/dataGrid.latte');
	}


	public function createComponentGrid(): \Ublaboo\DataGrid\DataGrid
	{
		$grid = new \Ublaboo\DataGrid\DataGrid();

		$grid->setPrimaryKey('type');
		$grid->setDataSource($this->prepareData());

		$grid->addColumnText('type', 'Import type');
		$grid->addColumnText('num', 'Rows in queue');
		$grid->addColumnText('errors', 'Failed rows');
		$grid->addColumnDateTime('createdTime', 'Created time')->setFormat('d.m.Y H:i:s');

		$grid->setTranslator($this->translator);


		return $grid;
	}

	private function prepareData(): array
	{
		return $this->cacheFactory->create('import_cache')->load('data', function (&$dependencies) {
			$dependencies[Cache::Expire] = '20 second';
			$dataForDataGrid = [];
			$errors = $this->orm->importCache->getCountByTypes(ImportCache::STATUS_ERROR);
			$ready = $this->orm->importCache->getCountByTypes(ImportCache::STATUS_READY);
			$ready += $this->orm->importCache->getCountByTypes(ImportCache::STATUS_QUEUED);
			/**
			 * @var string $type
			 * @var Row $data
			 */
			foreach ($errors as $type => $data) {
				if (!isset($dataForDataGrid[$type])) {
					$item = $this->orm->importCache->getById($data->id);
					$dataForDataGrid[$type] = $data->toArray() + [
							'createdTime' => $item->createdTime,
							'errors'      => $data->num,
							'num'         => $ready[$type]->num ?? 0
						];
				} else {
					$dataForDataGrid[$type]['errors'] = $data->num;
				}
			}
			foreach ($ready as $type => $data) {
				if (!isset($dataForDataGrid[$type])) {
					$item = $this->orm->importCache->getById($data->id);
					$dataForDataGrid[$type] = $data->toArray() + [
							'createdTime' => $item->createdTime,
							'errors'      => $errors[$type]->num ?? 0,
							'num'         => $data->num,
						];
				} else {
					$dataForDataGrid[$type]['num'] = $data->num;
				}
			}

			return array_values($dataForDataGrid);
		});
	}

}
