<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Sign;

use App\AdminModule\Components\LostPasswordForm\LostPasswordForm;
use App\AdminModule\Components\LostPasswordForm\LostPasswordFormFactory;
use App\AdminModule\Components\ResetPasswordForm\ResetPasswordForm;
use App\AdminModule\Components\ResetPasswordForm\ResetPasswordFormFactory;
use App\AdminModule\Components\SignInForm\SignInForm;
use App\AdminModule\Components\SignInForm\SignInFormFactory;
use App\AdminModule\Presenters\BasePresenter;
use App\Model\Security\Acl;
use Nette\Application\Attributes\Persistent;

final class SignPresenter extends BasePresenter
{

	#[Persistent]
	public string $backlink = '';

	public function __construct(
		private readonly LostPasswordFormFactory $lostPasswordFormFactory,
		private readonly SignInFormFactory $signInFormFactory,
		private readonly ResetPasswordFormFactory $restPasswordFormFactory,
	)
	{
		parent::__construct();
	}

	public function actionOut(): void
	{
		$this->getUser()->logout(true);
		$this->flashMessage('msg_info_logout');
		$this->redirect('default');
	}


	public function actionLostPassword(): void
	{
		if ($this->user->isLoggedIn() && $this->user->isAllowed(Acl::RES_ADMIN)) {
			$this->redirect('Page:default');
		}
	}


	public function actionResetPassword(?string $hashToken): void
	{
		if ($hashToken) {
			$userHash = $this->orm->userHash->getBy(['hash' => $hashToken]);
			if ($userHash && $userHash->isValid()) {

			} else {
				$this->getComponent('lostPasswordForm')->flashMessage('reset_password_expired_link', 'error');
				$this->redirect('Sign:lostPassword');
			}
		} else {
//			$this->flashMessage("reset_password_no_valid_link", "error");
			$this->getComponent('lostPasswordForm')->flashMessage('reset_password_no_valid_link', 'error');
			$this->redirect('Sign:lostPassword');
		}
	}


	public function createComponentLostPasswordForm(): LostPasswordForm
	{
		return $this->lostPasswordFormFactory->create($this->orm->mutation->getByCode('cs'));
	}

	public function createComponentResetPasswordForm(): ResetPasswordForm
	{
		$hash = $this->getParameter('hashToken');
		return $this->restPasswordFormFactory->create($hash);
	}


	protected function createComponentSignInForm(): SignInForm
	{
		return $this->signInFormFactory->create();
	}

}
