<?php

namespace App\AdminModule\Presenters\Holiday;


use App\AdminModule\Presenters\BasePresenter;
use App\AdminModule\Presenters\Holiday\Components\DataGrid\DataGrid;
use App\AdminModule\Presenters\Holiday\Components\DataGrid\DataGridFactory;
use App\AdminModule\Presenters\Holiday\Components\Form\Form;
use App\AdminModule\Presenters\Holiday\Components\Form\FormFactory;
use App\AdminModule\Presenters\Holiday\Components\ShellForm\ShellForm;
use App\AdminModule\Presenters\Holiday\Components\ShellForm\ShellFormFactory;
use App\Model\Orm\Holiday\Holiday;
use App\Model\Orm\Holiday\HolidayModel;
use Nette\Application\AbortException;
use Nette\Application\UI;

use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Tracy\Debugger;

class HolidayPresenter extends BasePresenter
{
	/** @var ICollection<Holiday>  */
	private ICollection $holidays;

	private ?Holiday $object;

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
		private readonly ShellFormFactory $shellFormFactory,
		private readonly FormFactory $formFactory,
	)
	{
	}

	public function startup(): void
	{
		parent::startup();
		$this->orm->holiday->setPublicOnly(FALSE);
	}

	public function actionDefault(): void
	{
		$this->holidays = $this->orm->holiday->findAll()->orderBy('publicFrom');
	}

	public function actionEdit(int $id): void
	{
		$this->object = $this->orm->holiday->getById($id);

		if (!$this->object) {
			$this->redirect('default');
		}
	}

	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create($this->holidays);
	}

	protected function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create();
	}

	protected function createComponentForm(): Form
	{
		return $this->formFactory->create($this->object, $this->userEntity);
	}
}
