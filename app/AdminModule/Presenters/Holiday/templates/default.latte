{layout $templates.'/@layout-new.latte'}
{var $dataGridShown = true}

{block #content}
<div class="main__main">
	<div class="main__header">
		{include $templates.'/part/box/header.latte',
		props: [
		title: $translator->translate('Holidays'),
		isPageTitle: true,
		]
		}
	</div>
	<div class="main__content scroll">
		{snippet flash}
			<div n:foreach="$flashes as $flash" class="message message-{$flash->type}">{$flash->message}</div>
		{/snippet}
		{control grid}
	</div>
	<div class="main__content-side scroll">
		{control shellForm}
	</div>
</div>
{/block}
