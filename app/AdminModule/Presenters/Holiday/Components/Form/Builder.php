<?php declare(strict_types=1);

namespace App\AdminModule\Presenters\Holiday\Components\Form;

use App\Model\Orm\Holiday\Holiday;
use App\Model\Orm\User\User;
use App\PostType\Core\AdminModule\Components\Form\Builder as CoreBuilder;

final class Builder
{
	public function __construct(
		private readonly CoreBuilder $coreBuilder,
	)
	{
	}

	public function build(\Nette\Application\UI\Form $form, Holiday $holiday, User $user): void
	{
		$form->addText("name", "label_internalName")->setRequired()->setDefaultValue($holiday->name);
		$form->addText('publicFrom', 'holiday_public_from')->setRequired()->setDefaultValue($holiday->publicFrom->format('Y-m-d'));
		$form->addText('publicTo', 'holiday_public_to')->setRequired()->setDefaultValue($holiday->publicTo->format('Y-m-d'));

		$this->coreBuilder->addButtons($form);
		$this->coreBuilder->addPublish($form, $holiday);
	}

}
