<?php

declare(strict_types=1);

namespace App\AdminModule\Presenters\Holiday\Components\Form;

use App\Model\Orm\Holiday\Holiday;
use App\Model\Orm\Holiday\HolidayRepository;
use App\AdminModule\Presenters\Holiday\Components\Form\FormData\BaseFormData;
use App\Model\Orm\User\User;

final readonly class Handler
{

	public function __construct(
		private HolidayRepository $holidayRepository,
	)
	{
	}

	public function handle(Holiday $holiday, BaseFormData $data, User $user): void
	{
		$holiday->name = $data->name;
		$holiday->publicTo = $data->publicTo;
		$holiday->publicFrom = $data->publicFrom;
		$holiday->public = (int) $data->publish->public;

		$this->holidayRepository->persistAndFlush($holiday);
	}

}
