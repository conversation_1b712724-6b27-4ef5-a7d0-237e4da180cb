<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Holiday\Components\ShellForm;

use App\AdminModule\Presenters\Holiday\Components\ShellForm\FormData\BaseFormData;
use App\Model\Orm\Holiday\HolidayModel;

use App\Model\Translator;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;

/**
 * @property-read DefaultTemplate $template
 */
class ShellForm extends Control
{
	public function __construct(
		private readonly Translator $translator,
		private readonly HolidayModel $holidayModel,
	)
	{
		$this->onAnchor[] = [$this, 'init'];
	}


	public function init(): void
	{

	}

	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$template->add('templates', RS_TEMPLATE_DIR);

		$template->render(__DIR__ . '/shellForm.latte');
	}


	protected function createComponentForm(): Form
	{
		$form = new Form();
		$form->setMappedType(BaseFormData::class);
		$form->setTranslator($this->translator);

		$form->addSubmit('send', 'send');

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];
		return $form;
	}



	public function formError(Form $form): void
	{
		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSucceeded(Form $form, BaseFormData $data): void
	{
		$holiday = $this->holidayModel->create();
		$this->presenter->redirect('edit', ['id' => $holiday->id]);
	}

}
