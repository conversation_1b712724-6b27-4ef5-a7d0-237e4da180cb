<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Holiday\Components\DataGrid;

use App\Model\Orm\Holiday\Holiday;
use App\Model\Orm\Holiday\HolidayRepository;
use App\Model\Translator;
use Nette\Application\UI\Control;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nextras\Orm\Collection\ICollection;
use Ublaboo\DataGrid\Exception\DataGridException;

/**
 * @property-read DefaultTemplate $template
 */
class DataGrid extends Control
{
	/**
	 * @param ICollection<Holiday> $collection
	 */
	public function __construct(
		private readonly ICollection $collection,
		private readonly Translator $translator,
		private readonly HolidayRepository $holidayRepository,
	)
	{
	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);

		$template->render(__DIR__ . '/dataGrid.latte');
	}


	/**
	 * @throws DataGridException
	 */
	public function createComponentGrid(): \Ublaboo\DataGrid\DataGrid
	{
		$grid = new \Ublaboo\DataGrid\DataGrid();

		$grid->setDataSource($this->collection);
		$grid->addColumnText('name', 'name')->setSortable()->setFilterText();
		$grid->addColumnDateTime("publicFrom","holiday_public_from")->setFormat('d.m.Y');
		$grid->addColumnDateTime("publicTo","holiday_public_to")->setFormat('d.m.Y');

		/*$grid->addColumnStatus('public', 'public')->setAlign('right')
		     ->addOption(1, 'public')->setIcon('check')->endOption()
		     ->addOption(0, 'non-public')->setClass('btn-danger')->endOption()
			->onChange[] = [$this, 'publicChange']
		;*/
		$grid->addColumnText('public', 'public')
		     ->setAlign('right')
		     ->setRenderer(fn(Holiday $holiday) => $this->translator->translate($holiday->public ? 'public': 'non-public'));

		$grid->addAction('edit', 'Edit', ':edit')->setClass('btn btn-xs btn-primary');

		$grid->setTranslator($this->translator);

		return $grid;
	}

	public function publicChange(string $id, string $public): void
	{
		/** @var Holiday $holiday */
		$holiday = $this->holidayRepository->getByIdChecked((int) $id);
		$holiday->public = (int)$public;
		$this->holidayRepository->persistAndFlush($holiday);

		if ($this->presenter->isAjax()) {
			$this['grid']->redrawItem($id);
		}
	}

}
