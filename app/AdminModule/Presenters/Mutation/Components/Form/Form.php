<?php

declare(strict_types=1);

namespace App\AdminModule\Presenters\Mutation\Components\Form;

use App\Model\ConfigService;
use App\Model\CustomField\SuggestUrls;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\User\User;
use App\Model\Translator;
use App\Model\TranslatorDB;
use App\AdminModule\Presenters\Mutation\Components\Form\FormData\BaseFormData;
use Nette\Application\UI\Control;
use const RS_TEMPLATE_DIR;

final class Form extends Control
{
	public function __construct(
		private readonly Mutation $mutation,
		private readonly User $userEntity,
		private readonly Builder $formBuilder,
		private readonly Handler $formHandler,
		private readonly SuggestUrls $urls,
		private readonly Translator $translator,
		private readonly TranslatorDB $translatorDB,
		private readonly Orm $orm,
		private readonly ConfigService $configService,
	) {}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$template->add('templates', RS_TEMPLATE_DIR);
		$template->add('mutation', $this->mutation);
		$template->add('imgSrc', '');
		$template->add('corePartsDirectory', \App\PostType\Core\AdminModule\Components\Form\Form::TEMPLATE_PARTS_DIR);

		$template->orm = $this->orm;
		$template->translatorDB = $this->translatorDB;
		$template->fileUploadLink = $this->presenter->link(':Admin:File:upload');

		$template->config = $this->configService->getParams();
		$template->userEntity = $this->userEntity;

		$template->urls = $this->urls;
		$template->render(__DIR__ . '/form.latte');
	}

	protected function createComponentForm(): \Nette\Application\UI\Form
	{
		$form = new \Nette\Application\UI\Form();
		$form->setMappedType(BaseFormData::class);

		$this->formBuilder->build($form, $this->mutation, $this->userEntity);

		$form->setTranslator($this->translator);
		$form->onSuccess[] = $this->formSucceeded(...);
		$form->onError[] = $this->formError(...);

		return $form;
	}

	private function formSucceeded(\Nette\Application\UI\Form $form, BaseFormData $data): void
	{
		$this->formHandler->handle($this->mutation, $data, $this->userEntity);
		$this->presenter->redirect('edit', ['id' => $this->mutation->id]);
	}

	private function formError(): void
	{
		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

}
