{var $anchorName = 'content'}
{var $icon = $templates . '/part/icons/cog.svg'}
{var $title = 'Nastavení'}

{var $props = [
	title: $title,
	id: $anchorName,
	icon: $icon,
	variant: 'main',
	open: true,
	classes: ['u-mb-xxs'],
]}

{embed $templates . '/part/box/toggle.latte', props => $props, templates => $templates}
	{block content}
		{varType App\Model\Orm\User\User $userEntity}
		{if $userEntity->isDeveloper()}
			{include $templates . '/part/core/inp.latte', props: [
				input: $form['name'],
				classesLabel: ['title'],
			]}
			{include $templates . '/part/core/inp.latte', props: [
				input: $form['langCode'],
				classesLabel: ['title'],
			]}
			{include $templates . '/part/core/inp.latte', props: [
				input: $form['domain'],
				classesLabel: ['title'],
			]}
			{include $templates . '/part/core/inp.latte', props: [
				input: $form['urlPrefix'],
				classesLabel: ['title'],
			]}
			{include $templates . '/part/core/inp.latte', props: [
				input: $form['urlReviewPrefix'],
				classesLabel: ['title'],
			]}
			{include $templates . '/part/core/inp.latte', props: [
				input: $form['rootId'],
				classesLabel: ['title'],
			]}
			{include $templates . '/part/core/inp.latte', props: [
				input: $form['hidePageId'],
				classesLabel: ['title'],
			]}
		{/if}

		{include $templates . '/part/core/inp.latte', props: [
			input: $form['adminEmail'],
			classesLabel: ['title'],
		]}
		{include $templates . '/part/core/inp.latte', props: [
			input: $form['contactEmail'],
			classesLabel: ['title'],
		]}
		{include $templates . '/part/core/inp.latte', props: [
			input: $form['orderEmail'],
			classesLabel: ['title'],
		]}
		{include $templates . '/part/core/inp.latte', props: [
			input: $form['fromEmail'],
			classesLabel: ['title'],
		]}
		{include $templates . '/part/core/inp.latte', props: [
			input: $form['fromEmailName'],
			classesLabel: ['title'],
		]}
		{include $templates . '/part/core/inp.latte', props: [
			input: $form['heurekaOverenoKey'],
			classesLabel: ['title'],
		]}

	{/block}
{/embed}
