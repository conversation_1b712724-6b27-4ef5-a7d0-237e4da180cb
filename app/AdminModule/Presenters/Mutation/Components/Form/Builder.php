<?php declare(strict_types=1);

namespace App\AdminModule\Presenters\Mutation\Components\Form;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\User\User;
use App\PostType\Core\AdminModule\Components\Form\Builder as CoreBuilder;

final class Builder
{
	public function __construct(
		private readonly CoreBuilder $coreBuilder,
	)
	{
	}

	public function build(\Nette\Application\UI\Form $form, Mutation $mutation, User $user): void
	{
		if ($user->isDeveloper()) {
			$form->addText('name', 'label_name')
				->setDefaultValue($mutation->name);
			$form->addText('langCode', 'label_langCode')
				->setDefaultValue($mutation->langCode);
			$form->addText('urlReviewPrefix', 'label_urlReviewPrefix')
				->setDefaultValue($mutation->urlReviewPrefix);
			$form->addText('domain', 'label_domain')
				->setDisabled()->setDefaultValue($mutation->domain);
			$form->addText('urlPrefix', 'label_urlPrefix')
				->setDisabled()->setDefaultValue($mutation->urlPrefix);
			$form->addInteger('rootId', 'label_rootId')
				->setDisabled()->setDefaultValue($mutation->rootId);
			$form->addInteger('hidePageId', 'label_hidePageId')
				->setDisabled()->setDefaultValue($mutation->hidePageId);
		}

		$form->addText('adminEmail', 'label_adminEmail')
			->setDefaultValue($mutation->adminEmail);
		$form->addText('contactEmail', 'label_contactEmail')
			->setDefaultValue($mutation->contactEmail);
		$form->addText('orderEmail', 'label_orderEmail')
			->setDefaultValue($mutation->orderEmail);
		$form->addText('fromEmail', 'label_fromEmail')
			->setDefaultValue($mutation->fromEmail);
		$form->addText('fromEmailName', 'label_fromEmailName')
			->setDefaultValue($mutation->fromEmailName);
		$form->addText('heurekaOverenoKey', 'label_heurekaOverenoKey')
			->setDefaultValue($mutation->heurekaOverenoKey);


		$form->addContainer('setup')->addHidden('cf');

		$this->coreBuilder->addButtons($form);
	}

}
