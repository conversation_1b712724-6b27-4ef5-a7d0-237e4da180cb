<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Mutation\Components\SynonymsDataGrid;

use App\Model\Orm\EsIndex\EsIndexModel;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Translator;
use Nette\Application\UI\Control;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Forms\Container;
use Nette\Utils\ArrayHash;
use Nette\Utils\Json;
use Ublaboo\DataGrid\Column\Action\Confirmation\CallbackConfirmation;
use Ublaboo\DataGrid\DataGrid;
use Ublaboo\DataGrid\Exception\DataGridException;

/**
 * @property-read  DefaultTemplate $template
 */
class SynonymsDataGrid extends Control
{

	public function __construct(
		private readonly Mutation $mutation,
		private readonly Translator $translator,
		private readonly Orm $orm,
		private readonly EsIndexModel $esIndexModel,
	)
	{
	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->render(__DIR__ . '/synonymsDataGrid.latte');
	}

	public function createDataSource(): array
	{
		$synonyms = $this->mutation->synonyms;
		$dataSource = [];
		foreach ($synonyms as $word => $synonym) {
			$synonym = (array) $synonym;

			$dataSource[] = ['id' => $word, 'word' => $word, 'synonym' => implode(Mutation::SYNONYMS_DELIMITER, $synonym)];
		}

		return $dataSource;
	}

	/**
	 * @throws DataGridException
	 */
	public function createComponentDataGrid(): DataGrid
	{
		$grid = new DataGrid();
		$grid->setDataSource($this->createDataSource());
		$grid->setTranslator($this->translator);
		$grid->addColumnText('word', 'word')
			->setFilterText();
		$grid->addColumnText('synonym', 'synonym')
			->setEditableCallback($this->onEdit(...));

		$add = $grid->addInlineAdd();
		$add->setPositionTop();
		$add->onControlAdd[] = $this->onControlAdd(...);
		$add->onSubmit[] = $this->onSubmit(...);

		$grid->addAction('delete', 'delete_button', 'delete!', ['word' => 'word'])
			->setClass('btn btn-xs btn-danger')
			->setConfirmation(
				new CallbackConfirmation(
					function ($item) {
						return $this->translator->translate('delete_confirm') . ' [' . $item['word'] . ']';
					}
				)
			);

		return $grid;
	}

	private function onEdit(string $id, string $value): string
	{
		$originalSynonyms = $this->getOriginSynonymsArray();

		$value = $this->getSynonyms($value);
		$originalSynonyms[$id] = $value;

		$this->mutation->synonyms = ArrayHash::from($originalSynonyms);
		$this->orm->persistAndFlush($this->mutation);

		$this->markToRecreateIndex();

		return implode(Mutation::SYNONYMS_DELIMITER, $value);
	}

	private function onControlAdd(Container $container): void
	{
		$container->addText('word', '');
		$container->addText('synonym', '');
	}

	private function onSubmit(ArrayHash $values): void
	{
		$originSynonyms = $this->getOriginSynonymsArray();

		$synonyms = [];
		$words = [];
		$errors = [];

		$word = trim($values['word']);

		if (array_key_exists($word, $originSynonyms)) { // duplicate word
			$errors[] = $word;
		}

		$wordSynonyms = $this->getSynonyms($values['synonym']);

		if (empty($wordSynonyms)) { // synonyms not filled
			$errors[] = $word;
		}

		foreach ($wordSynonyms as $n => $syn) {
			if (array_key_exists($syn, $synonyms)) { // duplicate synonym
				unset($wordSynonyms[$n]);
				$errors[] = $syn;
				continue;
			}

			$synonyms[$syn] = $syn;
		}

		if (empty($wordSynonyms)) { // synonyms are empty
			$errors[] = $word;
		}

		$words[$word] = $wordSynonyms;

		foreach ($words as $word => $syns) {
			foreach ($syns as $m => $syn) {
				if (array_key_exists($syn, $words)) { // synonym equal to other word
					unset($words[$word][$m]);
					$errors[] = $syn;

					if (count($words[$word]) < 1) { // synonyms are empty
						unset($words[$word]);
					}
				}
			}
		}

		// fix array keys
		foreach ($words as $word => $synonyms) {
			$words[$word] = array_values($synonyms);
		}

		$this->mutation->synonyms = ArrayHash::from(array_merge($originSynonyms, $words));

		$this->orm->persistAndFlush($this->mutation);

		$this->markToRecreateIndex();

		if ($errors) {
			$this->presenter->flashMessage($this->translator->translate('msg_info_synonyms_duplicity') . ': ' . implode(', ', $errors), 'error');
		} else {
			$this->presenter->flashMessage($this->translator->translate('msg_ok_synonyms'), 'ok');
		}

		$this['dataGrid']->setDataSource($this->createDataSource());
		$this['dataGrid']->reload();
		$this->presenter->redrawControl();
	}

	private function getOriginSynonymsArray(): array
	{
		return Json::decode(Json::encode($this->mutation->synonyms), forceArrays: true);
	}

	private function getSynonyms(string $synonyms): array
	{
		$synonyms = explode(Mutation::SYNONYMS_DELIMITER, $synonyms);
		foreach ($synonyms as $k => $s) {
			$s = trim($s);
			if (empty($s)) {
				unset($synonyms[$k]);
				continue;
			}

			$synonyms[$k] = $s;
		}

		return $synonyms;
	}

	private function markToRecreateIndex(): void
	{
		$esIndexesToRecreate = $this->orm->esIndex->findBy([
			'active' => 1,
			'mutation' => $this->mutation,
		]);

		foreach ($esIndexesToRecreate as $esIndexToRecreate) {
			$this->esIndexModel->markToRecreate($esIndexToRecreate);
		}
	}

	public function handleDelete(string $word): void
	{
		$originalSynonyms = $this->getOriginSynonymsArray();

		unset($originalSynonyms[$word]);

		$this->mutation->synonyms = ArrayHash::from($originalSynonyms);
		$this->orm->persistAndFlush($this->mutation);
		$this->markToRecreateIndex();

		$this->presenter->flashMessage('msg_deleted_synonyms', 'warning');

		$this->presenter->redirect('this');
	}

}
