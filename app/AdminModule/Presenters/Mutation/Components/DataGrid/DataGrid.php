<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Mutation\Components\DataGrid;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Translator;
use Nette\Application\UI\Control;
use Nextras\Orm\Collection\ICollection;
use Ublaboo\DataGrid\Exception\DataGridException;

class DataGrid extends Control
{
	/**
	 * @param ICollection<Mutation> $collection
	 */
	public function __construct(
		private readonly ICollection $collection,
		private readonly Translator $translator,
	)
	{
	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);

		$template->render(__DIR__ . '/dataGrid.latte');
	}


	/**
	 * @throws DataGridException
	 */
	public function createComponentGrid(): \Ublaboo\DataGrid\DataGrid
	{
		$grid = new \Ublaboo\DataGrid\DataGrid();

		$grid->setDataSource($this->collection);
		$grid->addColumnText('name', 'name')->setSortable()->setFilterText();

		$grid->addAction('edit', 'Edit', ':edit')->setClass('btn btn-xs btn-primary');

		$grid->setTranslator($this->translator);

		return $grid;
	}

}
