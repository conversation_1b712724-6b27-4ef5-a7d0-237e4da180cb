<?php declare(strict_types=1);

namespace App\AdminModule\Presenters\Mutation;

use App\AdminModule\Presenters\BasePresenter;
use App\AdminModule\Presenters\Mutation\Components\DataGrid\DataGrid;
use App\AdminModule\Presenters\Mutation\Components\DataGrid\DataGridFactory;
use App\AdminModule\Presenters\Mutation\Components\Form\Form;
use App\AdminModule\Presenters\Mutation\Components\Form\FormFactory;
use App\AdminModule\Presenters\Mutation\Components\SynonymsDataGrid\SynonymsDataGrid;
use App\AdminModule\Presenters\Mutation\Components\SynonymsDataGrid\SynonymsDataGridFactory;
use App\Model\Orm\Mutation\Mutation;
use Nette\Application\Attributes\Persistent;
use Nextras\Orm\Collection\ICollection;

final class MutationPresenter extends BasePresenter
{

	protected ?Mutation $object = null;
	/** @var ICollection<Mutation>  */
	private ICollection $mutations;

	#[Persistent]
	public int $id;

	public function __construct(
		private readonly SynonymsDataGridFactory $synonymsDataGridFactory,
		private readonly DataGridFactory $dataGridFactory,
		private readonly FormFactory $formFactory,
	)
	{
		parent::__construct();
	}


	public function actionDefault(): void
	{
		$this->mutations = $this->orm->mutation->findAll();
	}


	public function actionEdit(int $id): void
	{
		$this->object = $this->orm->mutation->getById($id);

		if (!$this->object) {
			$this->redirect('default');
		}
	}

	public function actionSynonyms(int $id): void
	{
		$this->object = $this->orm->mutation->getById($id);
		if (!$this->object) {
			$this->redirect('default');
		}
	}

	public function renderSynonyms(): void
	{
		$this->template->object = $this->object;
	}

	protected function createComponentSynonymsDataGrid(): SynonymsDataGrid
	{
		return $this->synonymsDataGridFactory->create($this->object);
	}

	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create($this->mutations);
	}

	protected function createComponentForm(): Form
	{
		return $this->formFactory->create($this->object, $this->userEntity);
	}

}
