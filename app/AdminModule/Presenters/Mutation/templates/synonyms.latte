{layout $templates.'/@layout-new.latte'}
{varType App\Model\Orm\Mutation\Mutation $object}
{var $dataGridShown = true}

{block #content}
<div class="main__main">
	<div class="main__header" n:snippet="header">
		{capture $sectionTitle}{_'mutation_synonyms'|replace:'%mutation', $object->name}{/capture}
		{include $templates.'/part/box/header.latte',
			props: [
				title: $sectionTitle,
				hrefClose: $presenter->link('edit', [id=>$object->id]),
				isPageTitle: true,
			]
		}
	</div>

	<div class="main__content scroll"  n:snippet="mainContent">
		{snippet flash}
			<div n:foreach="$flashes as $flash" class="message message--{$flash->type}">{$flash->message}</div>
		{/snippet}

		{control synonymsDataGrid}
	</div>
	<div class="main__content-side scroll">
	</div>
</div>

{*}

{block content}

<div class="box-title">
	<p class="r">
		<a n:href="Mutation:default" class=" btn-icon-before">
			{_mutation_back_button}
		</a>
	</p>
	<h1 n:block=title><a n:href="Mutation:edit , id=>$object->id">{$object->name}</a> - {_title_synonyms}</h1>

</div>
<br />

{snippet flash}
	<div n:foreach="$flashes as $flash" class="message message-{$flash->type}">{$flash->message}</div>
{/snippet}

{control synonymsDataGrid}
{control synonymsForm}

*}
