<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\ApiToken;

use App\AdminModule\Components\ApiTokenDataGrid\ApiTokenDataGrid;
use App\AdminModule\Components\ApiTokenDataGrid\ApiTokenDataGridFactory;
use App\AdminModule\Components\ApiTokenShellForm\ApiTokenShellForm;
use App\AdminModule\Components\ApiTokenShellForm\ApiTokenShellFormFactory;
use App\AdminModule\Presenters\BasePresenter;

final class ApiTokenPresenter extends BasePresenter
{

	public function __construct(
		private readonly ApiTokenDataGridFactory $dataGridFactory,
		private readonly ApiTokenShellFormFactory $shellFormFactory,
	)
	{
		parent::__construct();
	}

	protected function createComponentGrid(): ApiTokenDataGrid
	{
		return $this->dataGridFactory->create();
	}

	protected function createComponentShellForm(): ApiTokenShellForm
	{
		return $this->shellFormFactory->create($this->userEntity);
	}

}
