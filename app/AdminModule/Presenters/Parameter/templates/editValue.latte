{layout $templates.'/@layout-new.latte'}
{varType App\Model\Orm\ParameterValue\ParameterValue $parameterValue}

{block #content}
<div class="main__main">
	<div class="main__header" n:snippet="header">
		{include $templates.'/part/box/header.latte',
			props: [
				hrefClose: $presenter->link('edit', [id=>$parameterValue->parameter->id]),
				title: $parameterValue->parameter->name . ' - ' . $parameterValue->internalValue,
				isPageTitle: true,
			]
		}
	</div>
	<div class="main__content scroll">
		{control parameterValueForm}
	</div>
	<div class="main__content-side scroll">
		{control parameterValueForm:side}
	</div>
</div>

{include $templates . '/part/core/libraryOverlay.latte'}
