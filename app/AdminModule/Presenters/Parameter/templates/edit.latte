{layout $templates.'/@layout-new.latte'}
{var $dataGridShown = true}

{block #content}
{varType App\Model\Orm\Parameter\Parameter $parameter}
<div class="main__main" data-controller="pagemenu">
	<div class="main__header">
		{include $templates.'/part/box/header.latte',
		props: [
			hrefClose: 'default',
			title: $parameter->name,
			hasGeneratedMenu: true,
			isPageTitle: true,
		]
		}
	</div>
	<div class="main__content scroll">
		<div class="message message--{$flash->type} u-mb-sm" n:foreach="$flashes as $flash">
			{$flash->message}
		</div>

		{control form}

		{var $props = [
			title: 'Hodnoty',
			id: 'parameter-values',
			icon: $templates.'/part/icons/ruler.svg',
			open: true,
			variant: 'main',
			classes: ['u-mb-xxs'],
			rowMainClass: 'row-main-max',
		]}
		{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
			{block content}
				{control valuesDataGrid}
			{/block}
		{/embed}
	</div>

	<div class="main__content-side scroll">

		{control form:side}
	</div>
</div>



