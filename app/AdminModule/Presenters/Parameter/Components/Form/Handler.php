<?php

declare(strict_types=1);

namespace App\AdminModule\Presenters\Parameter\Components\Form;

use App\AdminModule\Presenters\Parameter\Components\Form\FormData\BaseFormData;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Parameter\ParameterModel;
use App\Model\Orm\String\StringModel;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\Parameter\ParameterRepository;

final readonly class Handler
{

	public function __construct(
		private ParameterRepository $parameterRepository,
		private MutationsHolder $mutationsHolder,
		private StringModel $stringModel,
		private ParameterModel $parameterModel,
	)
	{
	}

	public function handle(\Nette\Application\UI\Form $form, Parameter $parameter, BaseFormData $data): void
	{
		$typeSort = $parameter->typeSort;

		$this->handleCommon($parameter, $data);
		$this->handleTranslations($parameter, $data->mutations);
		$this->parameterRepository->persistAndFlush($parameter);

		if ($typeSort !== $data->typeSort) {
			$this->parameterModel->reindexSort($parameter);
		}
	}

	private function handleCommon(Parameter $parameter, BaseFormData $commonFormData): void
	{
		$parameter->name = $commonFormData->name;
		if ($parameter->uid === null) {
			$parameter->uid = $this->parameterModel->getUid($parameter, trim((string) $commonFormData->uid));
		}
		$parameter->extId = $commonFormData->extId;
		$parameter->typeSort = $commonFormData->typeSort;
		$oldValueIsInFilter = $parameter->isInFilter;
		$parameter->isInFilter = (int) $commonFormData->isInFilter;

		if ($oldValueIsInFilter !== $parameter->isInFilter) {
			$parameter->isLockedForES = 1;
		}

		$parameter->isInDetail = (int) $commonFormData->isInDetail;
		$parameter->isProtected = $commonFormData->isProtected;

		if ($parameter->type === Parameter::TYPE_TEXT) {
			$parameter->type = $commonFormData->type;
		}

		$parameter->productType = $commonFormData->productType;
		if (empty($parameter->productType)) {
			$parameter->productType = null;
		}
	}

	private function handleTranslations(Parameter $parameter, array $mutations): void
	{
		$keyMap = [
			'name' => 'pname',
			'tooltip' => 'pname_tooltip',
			'unit' => 'pname_unit',
			'filterPrefix' => 'pname_filter_prefix',
			'filterPostfix' => 'pname_filter_postfix',
		];

		foreach ($mutations as $mutationId => $mutationData) {
			$mutation = $this->mutationsHolder->getMutationById($mutationId);

			foreach ($keyMap as $dataName => $translationName) {
				if (isset($mutationData[$dataName])) {
					$key = $translationName . '_' . $parameter->id;
					$newValue = trim($mutationData[$dataName]);

					$this->stringModel->saveTranslation(
						mutation: $mutation,
						key: $key,
						newValue: $newValue,
						deleteOnEmptyString: true,
					);
				}
			}
		}
	}

}
