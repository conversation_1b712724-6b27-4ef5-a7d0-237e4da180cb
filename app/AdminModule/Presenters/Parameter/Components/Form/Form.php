<?php

declare(strict_types=1);

namespace App\AdminModule\Presenters\Parameter\Components\Form;

use App\AdminModule\Presenters\Parameter\Components\Form\FormData\BaseFormData;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Parameter\ParameterModel;
use App\Model\Orm\User\User;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Translator;
use Nette\Application\UI\Control;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nextras\Dbal\Drivers\Exception\ForeignKeyConstraintViolationException;
use Throwable;
use <PERSON>\Debugger;
use <PERSON>\ILogger;
use const RS_TEMPLATE_DIR;

/**
 * @property-read DefaultTemplate $template
 */
final class Form extends Control
{

	public function __construct(
		private readonly Parameter $parameter,
		private readonly User $userEntity,
		private readonly Builder $formBuilder,
		private readonly Handler $formHandler,
		private readonly Translator $translator,
		private readonly MutationsHolder $mutationsHolder,
		private readonly ParameterModel $parameterModel,
	)
	{
	}

	public function render(): void
	{
		if ($this->parameter->options->toCollection()->limitBy(1)->orderBy('sort')->fetch()?->sort === 0 && $this->parameter->type !== Parameter::TYPE_TEXT) {
			$this->parameterModel->reindexSort($this->parameter);
		}

		$template = $this->initTemplate();
		$template->render(__DIR__ . '/form.latte');
	}
	public function renderSide(): void
	{
		$this->template->render(__DIR__ . '/side.latte');
	}

	/**
	 * @return DefaultTemplate
	 */
	public function initTemplate(): DefaultTemplate
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$template->add('templates', RS_TEMPLATE_DIR);
		$template->add('parameter', $this->parameter);
		$template->add('mutations', $this->mutationsHolder->findAllById());
		$template->showDeleteButton = !$this->parameter->isProtected;
		return $template;
	}

	protected function createComponentForm(): \Nette\Application\UI\Form
	{
		$form = new \Nette\Application\UI\Form();
		$form->setMappedType(BaseFormData::class);

		$this->formBuilder->build($form, $this->parameter, $this->userEntity);

		$form->setTranslator($this->translator);
		$form->onSuccess[] = $this->formSucceeded(...);
		$form->onError[] = $this->formError(...);

		return $form;
	}

	private function formSucceeded(\Nette\Application\UI\Form $form, BaseFormData $data): void
	{
		$this->formHandler->handle($form, $this->parameter, $data);
		$this->presenter->redirect('edit', ['id' => $this->parameter->id]);
	}

	private function formError(): void
	{
		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	public function handleDelete(): void
	{
		$parameterToDelete = $this->parameter;

		if ($parameterToDelete->isProtected) {
			$this->presenter->flashMessage('parameter_msg_error_delete_uid', 'error');
			$this->presenter->redirect('this');
		}

		try {
			$this->parameterModel->remove($parameterToDelete);
			$this->presenter->flashMessage('OK', 'ok');
		} catch (ForeignKeyConstraintViolationException $e) {
			$this->presenter->flashMessage('parameters_msg_error_remove_fk_constraint', 'error');
			$this->presenter->redirect('default');
		} catch (Throwable $e) {
			Debugger::log($e, ILogger::ERROR);
			$this->presenter->flashMessage('msg_operation_failed', 'error');
			$this->presenter->redirect('this');
		}

		$this->presenter->redirect('default');
	}

	public function handleReindexSort(): void
	{
		$this->parameterModel->reindexSort($this->parameter);
		$this->presenter->redirect('this');
	}

}
