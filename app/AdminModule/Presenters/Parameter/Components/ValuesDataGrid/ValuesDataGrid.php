<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Parameter\Components\ValuesDataGrid;

use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\ParameterValue\ParameterValueModel;
use App\Model\Orm\ParameterValue\ParameterValueRepository;
use App\Model\Orm\String\StringModel;
use App\Model\Translator;
use App\Model\TranslatorDB;
use Nette\Application\UI\Control;
use Nette\Forms\Container;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Ublaboo\DataGrid\Column\Action\Confirmation\StringConfirmation;
use Ublaboo\DataGrid\DataGrid;
use Ublaboo\DataGrid\Exception\DataGridException;

class ValuesDataGrid extends Control
{

	public const PARAMETER_VALUES_MAP = [
		'value' => 'pvalue',
		'alias' => 'pvalue_alias',
		'filterValue' => 'pvalue_filter',
		'filterValueTitle' => 'pvalue_filter_title',
	];

	public function __construct(
		private readonly Parameter $parameter,
		private readonly Translator $translator,
		private readonly ParameterValueRepository $parameterValueRepository,
		private readonly MutationsHolder $mutationsHolder,
		private readonly TranslatorDB $translatorDB,
		private readonly StringModel $stringModel,
		private readonly ParameterValueModel $parameterValueModel,
	)
	{
	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);

		$template->render(__DIR__ . '/valuesDataGrid.latte');
	}


	/**
	 * @throws DataGridException
	 */
	public function createComponentGrid(): DataGrid
	{
		$grid = new DataGrid();
		$grid->setDataSource($this->parameterValueRepository->findBy([
			'parameter' => $this->parameter,
		])->orderBy(['prioritySort' => ICollection::DESC, 'sort' => ICollection::ASC]));

		$grid->addInlineAdd()
			->onControlAdd[] = function (Container $container) {
			$container->addText('internalValue', '')->setRequired();
			$this->redrawControl();
			};

		$grid->getInlineAdd()->onSubmit[] = function (ArrayHash $values): void {
			$this->parameterValueModel->createNewValue($this->parameter, $values->internalValue);
		};

		$firstParameterValue = $this->parameter->options->toCollection()->limitBy(1)->orderBy('sort', ICollection::ASC)->fetch();

		$hasCf = $firstParameterValue !== null && $firstParameterValue->getCfScheme();

		if ($this->parameter->typeSort === null) {
			$grid->setSortable();
			$grid->setSortableHandler('parameterValueSort!');
		}

		if ($this->parameter->type !== Parameter::TYPE_TEXT) {
			$grid->addColumnText('sort', '#');
		}

		$grid->addColumnText('id', 'id')->setFilterText();
		$grid->addColumnText('internalValue', 'name')
			->setEditableCallback(
				function (string $parameterValueId, string $newValue) {
					$parameterValue = $this->parameterValueRepository->getByIdChecked((int) $parameterValueId);
					$parameterValue->internalValue = $newValue;
					$this['grid']->redrawItem($parameterValueId);
				}
			)->setFilterText();
		$grid->addColumnText('internalAlias', 'alias')->setEditableCallback(
			function (string $parameterValueId, string $newValue) {
				$parameterValue = $this->parameterValueRepository->getByIdChecked((int) $parameterValueId);
				$parameterValue->internalAlias = $newValue;
				$this['grid']->redrawItem($parameterValueId);
			}
		)->setFilterText();

		if ($this->parameter->hasTranslatedValues) {
			foreach ($this->mutationsHolder->findAll(false) as $mutation) {

				$this->translatorDB->setMutation($mutation);
				foreach (self::PARAMETER_VALUES_MAP as $propertyName => $translationPrefix) {

					$grid->addColumnText('mutation-.' . $mutation->id . '-' . $propertyName, $mutation->langCode . '_' . $propertyName)
						->setEditableCallback(
							function (string $parameterValueId, string $newValue) use ($mutation, $propertyName) {
								$key = $this->getTranslationKey($propertyName, $parameterValueId);
								$this->stringModel->saveTranslation($mutation, $key, $newValue, true);
								$this['grid']->redrawItem($parameterValueId);
							}
						)
						->setRenderer(function (ParameterValue $parameterValue) use ($propertyName, $mutation) {
							$this->translatorDB->setMutation($mutation);
							return $parameterValue->$propertyName;
						});
				}
			}
		}

		$grid->addColumnStatus('prioritySort', 'parameters_label_value_priority_sort')
			->addOption(true, 'Ano')->endOption()
			->addOption(false, 'Ne')->endOption()
			->onChange[] = $this->prioritySortChange(...);

		$grid->addColumnStatus('isHidden', 'parameters_label_value_is_hidden')
			->addOption(true, 'Schovat')->setClass('btn-danger')->endOption()
			->addOption(false, 'Zobrazit')->endOption()
			->onChange[] = $this->isHiddenChangeChange(...);

		$grid->addColumnText('product_count', 'product_count')->setRenderer(function (ParameterValue $parameterValue) {
			return $parameterValue->products->countStored();
		});/*->setSortable()->setSortableCallback(function (DbalCollection $collection, array $order) {
			if (key($order) === 'product_count') {
				$qb = $collection->getQueryBuilder();
				$orderValue = reset($order);
				$direction = match ($orderValue) {
					'DESC' => ICollection::DESC,
					default => ICollection::ASC,
				};

				$qb->addSelect('(SELECT COUNT(id) FROM product_parameter WHERE parameterValueId = parameter_value.id) AS product_count')->orderBy('product_count ' . $direction);
			}
		});*/

		$grid->addAction('delete', '', 'delete!')
			->setIcon('trash')
			->setTitle('Smazat')
			->setClass('btn btn-xs btn-danger <strong class="text-danger">ajax</strong>')
			->setConfirmation(
				new StringConfirmation('Smazat hodnotu parametru %s?', 'internalValue') // Second parameter is optional
			);

		$grid->setTranslator($this->translator);

		if ($hasCf) {
			$grid->addAction('editValue', 'Detail nastavení', 'Parameter:editValue')->addParameters(['parameterId' => (string) $this->parameter->id]);
		}
		$grid->setItemsPerPageList([20, 30, 40, 50, 100], false);

		return $grid;
	}


	public function prioritySortChange(string $parameterValueId, string $newValue): void
	{
		$parameterValue = $this->parameterValueRepository->getByIdChecked((int) $parameterValueId);
		$parameterValue->prioritySort = (bool) $newValue;
		$this->parameterValueRepository->persistAndFlush($parameterValue);

		$this['grid']->reload();
	}
	public function isHiddenChangeChange(string $parameterValueId, string $newValue): void
	{
		$parameterValue = $this->parameterValueRepository->getByIdChecked($parameterValueId);
		$parameterValue->isHidden = (bool) $newValue;
		$this->parameterValueRepository->persistAndFlush($parameterValue);
		$this['grid']->redrawItem($parameterValueId);
	}

	private function getTranslationKey(string $propertyName, string $parameterValueId): string
	{
		if ( ! isset(self::PARAMETER_VALUES_MAP[$propertyName])) {
			throw new \LogicException('Missing ParameterValue property in map');
		}

		return self::PARAMETER_VALUES_MAP[$propertyName] . '_' . $parameterValueId;
	}

	public function handleDelete(string $id): void
	{
		$parameterValue = $this->parameterValueRepository->getByIdChecked((int) $id);
		$this->parameterValueRepository->removeAndFlush($parameterValue);
		$this->presenter->redirect('this');
	}

}
