<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Parameter\Components\DataGrid;

use App\Model\Orm\Parameter\Parameter;
use App\PostType\Core\AdminModule\Components\DataGrid\Definition\DataGridDefinition;
use App\PostType\Core\AdminModule\Components\DataGrid\Definition\Extenders\CustomDataGridExtender;
use Nextras\Dbal\Connection;
use Ublaboo\DataGrid\DataGrid;

readonly class ParameterDataGridPrescription
{

	public function __construct(
		private readonly Connection $connection
	)
	{
	}

	public function get(): DataGridDefinition
	{
		$datagrid = new DataGrid();
		$datagrid->setPagination(false);
		$datagrid->setSortable();
		$datagrid->setSortableHandler('parameterSort!');

		$parametersCount = $this->connection->query('SELECT parameterId, COUNT(id) AS product_count FROM `product_parameter` GROUP BY parameterId')->fetchPairs('parameterId', 'product_count');
		$valuesCount = $this->connection->query('SELECT parameterId, COUNT(id) AS value_count FROM `parameter_value` GROUP BY parameterId')->fetchPairs('parameterId', 'value_count');

		$extender = new CustomDataGridExtender(function (DataGrid $dataGrid) use ($parametersCount, $valuesCount) {
			$dataGrid->getColumn('name')->setSortable(false);
			$dataGrid->removeFilter('name');

			$dataGrid->addColumnText('value_count', 'value_count')->setRenderer(fn(Parameter $parameter) => $valuesCount[$parameter->id] ?? 0);
			$dataGrid->addColumnText('product_count', 'product_count')->setRenderer(fn(Parameter $parameter) => $parametersCount[$parameter->id] ?? 0);
		});

		return new DataGridDefinition(
			dataGrid: $datagrid,
			beforeRenderExtenders: [$extender]
		);
	}

}
