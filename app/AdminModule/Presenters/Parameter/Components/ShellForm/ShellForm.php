<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Parameter\Components\ShellForm;

use App\AdminModule\Presenters\Parameter\Components\ShellForm\FormData\BaseFormData;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\Parameter\ParameterModel;
use App\Model\Translator;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;

class ShellForm extends Control
{

	public function __construct(
		private readonly Translator $translator,
		private readonly ParameterModel $parameterModel,
	)
	{
	}



	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$template->add('templates', RS_TEMPLATE_DIR);

		$template->render(__DIR__ . '/shellForm.latte');
	}


	protected function createComponentForm(): Form
	{
		$form = new Form();
		$form->setMappedType(BaseFormData::class);
		$form->setTranslator($this->translator);

		$form->addText('name', 'name')->setRequired();
		$form->addSelect('parameterType', 'select_parameter_type', Parameter::getConstsByPrefix('TYPE_'));
		$form->addSubmit('send', 'send');

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];
		return $form;
	}


	public function formError(Form $form): void
	{
		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSucceeded(Form $form, BaseFormData $data): never
	{
		$newParameter = $this->parameterModel->createNew($data);
		$this->presenter->redirect('edit', ['id' => $newParameter->id]);
	}

}
