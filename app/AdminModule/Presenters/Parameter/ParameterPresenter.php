<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Parameter;

use App\AdminModule\Presenters\BasePresenter;
use App\AdminModule\Presenters\Parameter\Components\DataGrid\ParameterDataGridPrescription;
use App\AdminModule\Presenters\Parameter\Components\Form\Form;
use App\AdminModule\Presenters\Parameter\Components\Form\FormFactory;
use App\AdminModule\Presenters\Parameter\Components\ShellForm\ShellForm;
use App\AdminModule\Presenters\Parameter\Components\ShellForm\ShellFormFactory;
use App\AdminModule\Presenters\Parameter\Components\ValueEditForm\ValueEditForm;
use App\AdminModule\Presenters\Parameter\Components\ValueEditForm\ValueEditFormFactory;
use App\AdminModule\Presenters\Parameter\Components\ValuesDataGrid\ValuesDataGrid;
use App\AdminModule\Presenters\Parameter\Components\ValuesDataGrid\ValuesDataGridFactory;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGrid;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGridFactory;
use Nette\Application\Attributes\Persistent;
use Nextras\Orm\Collection\ICollection;

final class ParameterPresenter extends BasePresenter
{

	public const ORM_REPOSITORY_NAME = 'parameter';

	public ?Parameter $parameter;

	#[Persistent]
	public int $id;

	private ?ParameterValue $parameterValue;

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
		private readonly ParameterDataGridPrescription $parameterDataGridPrescription,
		private readonly ValuesDataGridFactory $valuesDataGridFactory,
		private readonly ShellFormFactory $shellFormFactory,
		private readonly FormFactory $formFactory,
		private readonly ValueEditFormFactory $valueEditFormFactory,
	)
	{
		parent::__construct();
	}

	protected function startup(): void
	{
		parent::startup();
	}

	public function actionEdit(int $id): void
	{
		$this->parameter = $this->orm->parameter->getById($id);

		if ($this->parameter === null) {
			$this->redirect('default');
		}

		if ($this->isAjax()) {
			$this->redrawControl();
		}
	}

	public function renderEdit(int $id): void
	{
		$this->template->parameter = $this->parameter;
	}

	public function actionEditValue(int|string $parameterId, int $id): void
	{
		$parameterId = (int) $parameterId;
		$this->parameter = $this->orm->parameter->getById($parameterId);
		$this->parameterValue = $this->orm->parameterValue->getById($id);

		if ($this->parameter === null) {
			$this->redirect('default');
		}

		if ($this->parameterValue === null) {
			$this->redirect('edit', ['id' => $this->parameter->id]);
		}
	}

	public function renderEditValue(): void
	{
		$this->template->parameter = $this->parameter;
		$this->template->parameterValue = $this->parameterValue;
	}

	protected function createComponentForm(): Form
	{
		return $this->formFactory->create($this->parameter, $this->userEntity);
	}

	protected function createComponentParameterValueForm(): ValueEditForm
	{
		return $this->valueEditFormFactory->create($this->parameterValue);
	}

	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create(self::ORM_REPOSITORY_NAME, $this->orm->parameter->findAll()->orderBy('sort', ICollection::ASC), $this->parameterDataGridPrescription->get());
	}

	public function handleParameterSort(): void
	{
		$query = $this->getHttpRequest()->getQuery();

		$item_id = (int) $query['grid-item_id'];
		$prev_id = (int) ($query['grid-prev_id'] ?? null);
		$next_id = (int) ($query['grid-next_id'] ?? null);

		if (($item_id + $prev_id + $next_id) >= 3) {
			$this->orm->parameter->initSort();

			$item = $this->orm->parameter->getById($item_id);
			$prev = $this->orm->parameter->getById($prev_id);
			$next = $this->orm->parameter->getById($next_id);

			$this->orm->parameter->moveUp($prev?->sort ?? 0, $item->sort);
			$this->orm->parameter->moveDown($next?->sort ?? 0, $item->sort);

			//$prev->sort--;
			$this->orm->refreshAll();

			if ($prev !== null) {
				$item->sort = $prev->sort + 1;
			} elseif ($next !== null) {
				$item->sort = (($nordr = ($next->sort - 1)) > 0) ? $nordr : 1;
			} else {
				$item->sort = 1;
			}

			$this->orm->parameter->persistAndFlush($item);

		}
		if ($this->isAjax()) {
			$this->getPayload()->newUrl = $this->getHttpRequest()->getUrl()->withQuery([]);
			$this->sendPayload();
		}
	}

	public function handleParameterValueSort(): void
	{
		$query = $this->getHttpRequest()->getQuery();

		$item_id = (int) $query['valuesDataGrid-item_id'];
		$prev_id = (int) ($query['valuesDataGrid-prev_id'] ?? null);
		$next_id = (int) ($query['valuesDataGrid-next_id'] ?? null);

		if (($item_id + $prev_id + $next_id) >= 3) {
			$item = $this->orm->parameterValue->getById($item_id);
			$prev = $this->orm->parameterValue->getById($prev_id);
			$next = $this->orm->parameterValue->getById($next_id);

			$this->orm->parameterValue->moveUp($this->parameter, $prev?->sort ?? 0, $item->sort);
			$this->orm->parameterValue->moveDown($this->parameter, $next?->sort ?? 0, $item->sort);

			//$prev->sort--;
			$this->orm->refreshAll();

			if ($prev !== null) {
				$item->sort = $prev->sort + 1;
			} elseif ($next !== null) {
				$item->sort = (($nordr = ($next->sort - 1)) > 0) ? $nordr : 1;
			} else {
				$item->sort = 1;
			}

			$this->orm->parameterValue->persistAndFlush($item);

		}
		if ($this->isAjax()) {
			$this->getPayload()->newUrl = $this->getHttpRequest()->getUrl()->withQuery(['id' => $this->parameter->id]);
			$this->sendPayload();
		}
	}

	protected function createComponentValuesDataGrid(): ValuesDataGrid
	{
		return $this->valuesDataGridFactory->create($this->parameter);
	}

	protected function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create();
	}

}
