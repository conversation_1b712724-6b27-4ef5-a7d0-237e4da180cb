cf:
	fields:
		flagIcon:
			type: group
			label: "SVG ikona vlajky"
			items:
				flagIcon:
					type: image # has size
	templates:
		parameterValue-jazyk: [@cf.flagIcon]

services:
	- App\AdminModule\Presenters\Parameter\Components\Form\FormFactory
	- App\AdminModule\Presenters\Parameter\Components\Form\Builder
	- App\AdminModule\Presenters\Parameter\Components\Form\Handler

	- App\AdminModule\Presenters\Parameter\Components\ValuesDataGrid\ValuesDataGridFactory

	- App\AdminModule\Presenters\Parameter\Components\ValueEditForm\ValueEditFormFactory
	- App\AdminModule\Presenters\Parameter\Components\ShellForm\ShellFormFactory
	- App\AdminModule\Presenters\Parameter\Components\DataGrid\ParameterDataGridPrescription
