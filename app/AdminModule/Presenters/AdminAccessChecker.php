<?php

declare(strict_types=1);

namespace App\AdminModule\Presenters;

use Nette\Http\Helpers;

final readonly class AdminAccessChecker
{
	/**
	 * @param string[] $allowedIpRanges
	 */
	public function __construct(
		private array $allowedIpRanges,
	) {}

	public function isAllowed(string $ip): bool
	{
		if (count($this->allowedIpRanges) === 0) {
			return true;
		}

		foreach ($this->allowedIpRanges as $allowedIpRange) {
			if (Helpers::ipMatch($ip, $allowedIpRange)) {
				return true;
			}
		}

		return false;
	}
}
