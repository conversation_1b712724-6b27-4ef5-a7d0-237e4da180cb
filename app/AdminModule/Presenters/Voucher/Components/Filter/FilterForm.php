<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Voucher\Components\Filter;

use App\AdminModule\Components\ItemSearch\FoundItem;
use App\AdminModule\Components\ItemSearch\ItemSearch;
use App\AdminModule\Components\ItemSearch\ItemSearchFactory;
use App\Model\ElasticSearch\Product\Repository;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Mutation\MutationRepository;
use App\Model\Orm\Orm;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\Parameter\ParameterRepository;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\ParameterValue\ParameterValueRepository;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductRepository;
use App\Model\Orm\Voucher\Voucher;
use App\Model\Orm\VoucherLimit\VoucherLimit;
use App\Model\Translator;
use App\PostType\Page\Model\Orm\TreeRepository;
use App\PostType\Tag\Model\Orm\Tag\TagRepository;
use Elastica\Result;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Exception\NoResultException;

class FilterForm extends Control
{

	private Mutation $mutation;

	public function __construct(
		private array $filterSetup,
		private readonly Voucher $voucher,
		private readonly ?VoucherLimit $voucherLimit,
		private readonly Translator $translator,
		private readonly MutationRepository $mutationRepository,
		private readonly Repository $esProductRepository,
		private readonly EsIndexRepository $esIndexRepository,
		private readonly ItemSearchFactory $itemSearchFactory,
		private readonly ProductRepository $productRepository,
		private readonly TreeRepository $treeRepository,
		private readonly TagRepository $tagRepository,
		private readonly ParameterValueRepository $parameterValueRepository,
		private readonly ParameterRepository $parameterRepository,
		private readonly Orm $orm,
	)
	{
		$this->onAnchor[] = [$this, 'init'];
	}


	public function init(): void
	{
		$this->mutation = $this->mutationRepository->getRsDefault();
	}

	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->add('templates', RS_TEMPLATE_DIR);
		$template->add('products', $this->getProducts());
		$template->add('voucherLimit', $this->voucherLimit);
		$template->add('voucher', $this->voucher);

		$template->render(__DIR__ . '/filterForm.latte');
	}


	protected function createComponentForm(): Form
	{
		$form = new Form();
		$form->setTranslator($this->translator);
		$form->addHidden('voucherLimitId', $this->voucherLimit?->id ?? '');

		$form->addCheckbox('negate', 'negate')->setDefaultValue($this->voucherLimit?->isNegated ?? false);

		$categories = $this->treeRepository
			->findPairsInPath($this->mutation->pages->eshop->id, 'id', 'name');
		$form->addMultiSelect('categories', 'categories', $categories->fetchPairs('id', 'name'))->setDefaultValue($this->voucherLimit?->limit->categories ?? []);

		$tags = $this->tagRepository
			->findAll()
			->fetchPairs('id', 'internalName');
		$form->addMultiSelect('tags', 'tags', $tags)->setDefaultValue($this->voucherLimit?->limit->tags);

		$form->addMultiSelect('productTypes', 'productTypes', $this->getValuesCollectionByUid(Parameter::UID_PRODUCT_TYPE)->fetchPairs('id', 'internalValue'))->setDefaultValue($this->voucherLimit?->limit->productTypes);

		$form->addSubmit('filter', 'filter');

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];
		return $form;
	}

	/**
	 * @return ICollection<ParameterValue>|\Nextras\Dbal\Result\Result
	 */
	private function getValuesCollectionByUid(string $uid): ICollection|\Nextras\Dbal\Result\Result
	{
		/** @var EmptyCollection<ParameterValue> $collection */
		$collection = new EmptyCollection();
		try {
			$parameter = $this->parameterRepository->getByChecked([
				'uid' => $uid,
			]);
			$collection         = $this->parameterValueRepository->findPairsForParameter($parameter, 'id', 'internalValue');
		} catch (NoResultException) {
			// do nothing
		}
		return $collection;
	}


	public function formError(Form $form): void
	{
		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSucceeded(Form $form, ArrayHash $values): void
	{
		$httpData = $form->getHttpData();
		assert(is_array($httpData));
		$voucherLimitId = (int) $values->voucherLimitId;

		if ($voucherLimitId === 0) {
			$limit = new VoucherLimit();
			$this->voucher->limits->add($limit);
		} else {
			$limit = $this->orm->voucherLimit->getById($voucherLimitId);
		}

		$limit->isNegated = $values->negate;
		unset($values->voucherLimitId, $values->negate);
		$values->products = $httpData['products'] ?? [];
		$limit->limit = $values;
		$this->orm->voucher->persistAndFlush($this->voucher);
		$this->presenter->redirect('this', ['filterSetup' => null, 'voucherLimitId' => $limit->id]);
	}

	public function createComponentProductSearch(): ItemSearch
	{
		$clickItemFunction = function ($foundItemId): void {
			$product = $this->productRepository->getById($foundItemId);
			if ($product !== null) {
				if (!isset($this->filterSetup['products'])) {
					$this->filterSetup['products'] = [];
				}
				$this->filterSetup['products'][$foundItemId] = $foundItemId;

				$this->presenter->payload->newUrl = $this->presenter->link('this', ['filterSetup' => $this->filterSetup]);
				// prepare for redraw
				$this->filterSetup['products'] = [];
				$this->filterSetup['products'][$foundItemId] = $foundItemId;

				$this->redrawControl('productsArea');
				$this->redrawControl('products');
			}
		};

		$searchItemFunction = function (string $searchString): array {
			$esIndex = $this->esIndexRepository->getProductLastActive($this->mutation);
			if ($esIndex !== null) {
				$products = $this->esProductRepository->findByPrefix($esIndex, $searchString)->getResults();
				return array_map(fn(Result $product) => new FoundItem((int) $product->getSource()['id'], $product->getSource()['name']), $products);
			}
			return [];
		};

		return $this->itemSearchFactory->create($searchItemFunction, $clickItemFunction);
	}

	/**
	 * @return ICollection<Product>
	 */
	private function getProducts(): ICollection
	{
		$products = [];
		if (isset($this->filterSetup['products']) && is_array($this->filterSetup['products'])) {
			$products = array_merge($products, $this->filterSetup['products']);
		}

		if ( $this->voucherLimit !== null && isset($this->voucherLimit->limit->products) && !$this->presenter->isAjax()) {
			$products = array_merge($products, $this->voucherLimit->limit->products);
		}

		if ($products !== []) {
			return $this->productRepository->findByIds($products);
		}

		/** @var EmptyCollection<Product> $emptyCollection */
		$emptyCollection = new EmptyCollection();
		return $emptyCollection;
	}

	public function handleDeleteVoucherLimit(int $voucherLimitId): void
	{
		/** @var VoucherLimit|null $voucherLimit */
		$voucherLimit = $this->orm->voucherLimit->getById($voucherLimitId);
		if ($voucherLimit !== null) {
			$this->orm->voucherLimit->removeAndFlush($voucherLimit);
		}

		$this->presenter->redirect('this', ['voucherLimitId' => null]);
	}

}
