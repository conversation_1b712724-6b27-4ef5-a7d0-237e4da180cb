<label class="inp-label">
	{$name}
</label>
<div style="position: relative; z-index: 1">
	{control $searchComponentName}
</div>

{var $variableName = 'products'}
<div class="b-list__list" data-naja-snippet-append="" n:snippet="$variableName">
	{foreach $items as $item}
		<div class="b-list__item" data-controller="RemoveItem" data-removeitem-target="item" style="width: 200px">
			<span class="b-list__inp ">
				{$nameFunction($item)}
			</span>
			<span class="b-list__inp u-hide">
				<input type="hidden" name="{$variableName}[]" value="{$idFunction($item)}">
				<div class="inp-text__holder"></div>
			</span>
			<button
				type="button"
				class="b-list__btn btn-icon btn-icon--remove"
				data-action="RemoveItem#remove"
			>
				{include $templates.'/part/icons/trash.svg'}
			</button>
		</div>
	{/foreach}
</div>
