

<form n:name="form">
	{foreach $flashes as $flash}
		<div class="message message-{$flash->type}">{$flash->message}</div>
	{/foreach}


	{snippetArea productsArea}
		{include 'part/list.latte',
			name:'Produkty',
			searchComponentName: 'productSearch',
			items: $products,
			nameFunction: fn(App\Model\Orm\Product\Product $product) => $product->name,
			idFunction: fn(App\Model\Orm\Product\Product $product) => $product->id,
		}
	{/snippetArea}

	{include $templates.'/part/core/inp.latte' props: [
		input: $form['categories'],
		type: 'select',
		multiple: true,
	]}
	{include $templates.'/part/core/inp.latte' props: [
		input: $form['tags'],
		type: 'select',
		multiple: true,
	]}
	{include $templates.'/part/core/inp.latte' props: [
		input: $form['productTypes'],
		type: 'select',
		multiple: true,
	]}

	{*var $dataAttrs = ['search-url' => $presenter->link('Search2:state')]}
	{include $templates.'/part/core/inp.latte' props: [
		input: $form['writers'],
		type: 'select',
		multiple: true,
		dataInp: $dataAttrs
	]*}

	{include $templates.'/part/core/checkbox.latte',
		props: [
			input: $form['negate'],
			label: '<span class="grid-inline"><strong>Negovat</strong></span>',
			classes: ['u-mb-sm']
		]
	}

	<p>
		<button n:name="filter" class="btn btn--full btn--success">
			<span class="btn__text item-icon">
				<span class="item-icon__icon icon">
					{include $templates.'/part/icons/save.svg'}
				</span>
				<span class="item-icon__text">
					{_save_voucher_condition}
				</span>
			</span>
		</button>
	</p>
	<p n:if="$voucherLimit !== null">
		<a n:href="deleteVoucherLimit!, voucherLimitId: $voucherLimit->id" class="btn btn--full btn--grey btn--remove"  data-controller="Confirm" data-confirm-message-value="Opravdu smazat podmínku?" data-action="Confirm#confirm">
			<span class="btn__text item-icon">
				<span class="item-icon__icon icon">
					{include $templates.'/part/icons/trash.svg'}
				</span>
				<span class="item-icon__text">
					{_delete_voucher_condition}
				</span>
			</span>
		</a>
	</p>
</form>
