<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Voucher\Components\LimitDataGrid;

use App\Model\Translator;
use Elastica\Result;
use Nette\Application\UI\Control;

class DataGrid extends Control
{

	public function __construct(
		private readonly DataSource $dataSource,
		private readonly Translator $translator,
	)
	{
	}

	public function render(): void
	{
//		bd($this->onFilter());
		$this->template->setTranslator($this->translator);
		$this->template->render(__DIR__ . '/dataGrid.latte');
	}

	public function createComponentGrid(): \Ublaboo\DataGrid\DataGrid
	{
		$grid = new \Ublaboo\DataGrid\DataGrid();
		$grid->setDataSource($this->dataSource);
		$grid->setItemsPerPageList([50], false);

		$grid->addColumnText('id', 'id')->setSortable();
		$grid->addColumnText('name', 'name')->setSortable();
		$grid->addColumnText('eans', 'eans')->setRenderer(
			fn (Result $row) => implode(', ', $row->getSource()['eans'])
		);

		$grid->setDefaultSort(['name' => 'asc']);

		$grid->setTranslator($this->translator);
		$grid->setRefreshUrl(false);

		return $grid;
	}

}
