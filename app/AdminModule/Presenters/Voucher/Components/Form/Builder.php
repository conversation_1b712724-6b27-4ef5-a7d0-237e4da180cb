<?php declare(strict_types=1);

namespace App\AdminModule\Presenters\Voucher\Components\Form;

use App\Model\ConfigService;
use Nette\Application\UI;
use App\Model\Orm\User\User;
use App\Model\Orm\Voucher\Voucher;
use App\PostType\Core\AdminModule\Components\Form\Builder as CoreBuilder;

final readonly class Builder
{

	public function __construct(
		private CoreBuilder $coreBuilder,
		private ConfigService $configService,
	)
	{
	}

	public function build(\Nette\Application\UI\Form $form, Voucher $voucher, User $user): void
	{
		$this->addCommonToForm($form, $voucher);
		$this->addCodeSectionToForm($form);

		$this->coreBuilder->addButtons($form);
		$this->coreBuilder->addPublish($form, $voucher);

		if ($voucher->synced) {
			$form['internalName']->setDisabled()->setValue($voucher->internalName);
			$form['publicFrom']->setDisabled()->setValue($voucher->publicFrom->format('Y-m-d\TH:i'));
			$form['publicTo']->setDisabled()->setValue($voucher->publicTo->format('Y-m-d\TH:i'));
			$form['discountAmount']->setDisabled()->setValue($voucher->discount->getValue('amount'));
			$form['discountPercent']->setDisabled()->setValue($voucher->discountPercent);
			$form['minPriceOrder']->setDisabled()->setValue($voucher->minPriceOrder);
			$form['reuse']->setDisabled()->setValue($voucher->reuse);
			$form['combination']->setDisabled()->setValue($voucher->combination);
			$form['combinationType']->setDisabled()->setValue($voucher->combinationType);
		}
	}

	private function addCommonToForm(UI\Form $form, Voucher $voucher): void
	{
		$form->addHidden('type', $voucher->type);
		$form->addText('internalName', 'internalName')
			->setRequired()
			->setDefaultValue($voucher->internalName);

		$form->addText('name', 'name')
			->setRequired()
			->setDefaultValue($voucher->name);

		$form->addText('publicFrom', 'publicFrom');
		if ($voucher->publicFrom !== null) {
			$form['publicFrom']->setDefaultValue($voucher->publicFrom->format('Y-m-d\TH:i'));
		}

		$form->addText('publicTo', 'publicTo');
		if ($voucher->publicTo !== null) {
			$form['publicTo']->setDefaultValue($voucher->publicTo->format('Y-m-d\TH:i'));
		}

//		$kinds = $this->configService->get('voucherKinds');
//		$form->addSelect('kind', 'label_voucherKind', $kinds)
//			->setDefaultValue($voucher->kind)
//			->setRequired('Type is required');


		$form->addText('discountAmount', 'label_discountAmount')
			->addRule($form::Float, 'Please enter valid float.')
			->setDefaultValue((float) $voucher->discount->getValue('amount'));

		$form->addText('discountPercent', 'label_discountPercent')
			->addRule($form::Range, 'nejméně %d a nejvíce %d', [0, 100])
			->addRule($form::Integer, 'Please enter valid float.')
			->setNullable()
			->setDefaultValue($voucher->discountPercent);

		if (isset($form['type'])) {
			$form['discountAmount']->addConditionOn($form['type'], $form::Equal, 'amount')
				->addRule($form::Filled, 'Zadejte slevu v ' . $voucher->mutation->currency->getCurrencyCode());
			$form['discountPercent']->addConditionOn($form['type'], $form::Equal, 'percent')
				->addRule($form::Filled, 'Zadejte slevu v %%');
		}

		$form->addText('minPriceOrder', 'label_minPriceOrder')
			->setDefaultValue($voucher->minPriceOrder?->toFloat())->addRule($form::Float, 'Please enter float.')->setNullable();
		$form->addCheckbox('reuse', 'label_reuse')->setDefaultValue($voucher->reuse);
		$form->addCheckbox('combination', 'label_combination')->setDefaultValue($voucher->combination);
		$form->addSelect('combinationType', 'label_combinationType', $this->configService->get('voucherTypes'))->setPrompt('all')->setDefaultValue($voucher->combinationType);
	}

	private function addCodeSectionToForm(UI\Form $form): void
	{
		$form->addSubmit('generate');

		$codesContainer = $form->addContainer('codes');

		$codesContainer->addText('newVoucherString', 'newVoucherString');
		$codesContainer->addInteger('newVoucherCount', 'newVoucherCount')
			->addRule($form::Range, 'nejméně %d a nejvíce %d', [0, 1000]);
		$codesContainer->addText('newVoucherPrefix', 'newVoucherPrefix');
	}

}
