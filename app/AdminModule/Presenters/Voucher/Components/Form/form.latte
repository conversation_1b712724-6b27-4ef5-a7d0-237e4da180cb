{varType App\Model\Orm\Mutation\Mutation $mutation}
{varType App\Model\Orm\Voucher\Voucher $voucher}
<form n:name="form" class="main__main" data-controller="pagemenu">
	<div class="main__header">
		{include $templates.'/part/box/header.latte',
			props: [
				hrefClose: 'default',
				img: $imgSrc,
				title: $voucher->internalName,
				hasGeneratedMenu: true,
				isPageTitle: true,
			]
		}
	</div>
	<div class="main__content scroll">
		<div class="message message--{$flash->type} u-mb-sm" n:foreach="$flashes as $flash">
			{$flash->message}
		</div>

		{include './parts/content/content.latte', form: $form}
		{if !$voucher->synced}
			{include './parts/content/generator.latte', form: $form}
		{/if}
		{include './parts/content/codes.latte', form: $form}
	</div>

	<div class="main__content-side scroll">
		{include $corePartsDirectory . '/side/state.latte', form: $form}
		{include $corePartsDirectory . '/side/btns.latte', showDeleteButton: !$voucher->synced}
		<p class="u-mb-xs" n:if="$voucher->type !== App\Model\Orm\Voucher\Voucher::TYPE_AMOUNT_COMBINATION">
			<a href="{plink "limits", id => $voucher->id}" class="btn btn--full btn--grey" target="_blank">
				<span class="btn__text item-icon">
					<span class="item-icon__icon icon">
						{include $templates.'/part/icons/external-link-alt.svg'}
					</span>
					<span class="item-icon__text">
						<span class="grid-inline"> <span>Limity uplatnění</span></span>
					</span>
				</span>
			</a>
		</p>

		{include './parts/side/edits.latte'}
	</div>
</form>

{include $templates . '/part/core/libraryOverlay.latte'}

