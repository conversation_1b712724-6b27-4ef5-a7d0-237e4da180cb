{var $props = [
title: 'Hlavn<PERSON> nastavení',
id: 'settings-main',
icon: $templates.'/part/icons/cog.svg',
open: true,
variant: 'main',
classes: ['u-mb-xxs']
]}

{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
	{block content}
		{include $templates.'/part/core/inp.latte' props: [
		input: $form['internalName'],
		classesLabel: ['title']
		]}
		{include $templates.'/part/core/inp.latte' props: [
		input: $form['name'],
		classesLabel: ['title']
		]}



		{include $templates.'/part/core/inp.latte' props: [
			input: $form['discountAmount'],
			type: 'number',
			classesLabel: ['title'],
			sufix: $currency,
			classes: in_array($voucher->type, [App\Model\Orm\Voucher\Voucher::TYPE_AMOUNT, App\Model\Orm\Voucher\Voucher::TYPE_AMOUNT_COMBINATION]) ? ['u-mb-sm'] : ['u-hide'],
		]}


		{include $templates.'/part/core/inp.latte' props: [
			input: $form['discountPercent'],
			type: 'number',
			classesLabel: ['title'],
			sufix: '%',
			classes: $voucher->type === App\Model\Orm\Voucher\Voucher::TYPE_PERCENT ? ['u-mb-sm'] : ['u-hide'],
		]}


		{include $templates.'/part/core/inp.latte' props: [
		input: $form['minPriceOrder'],
		type: 'number',
		classesLabel: ['title'],
			sufix: $currency
		]}

		{*		{include $templates.'/part/core/inp.latte' props: [*}
		{*			input: $form['mutation'],*}
		{*			type: 'select',*}
		{*			classesLabel: ['title']*}
		{*		]}*}

		{if isset($form['kind'])}
			{include $templates.'/part/core/inp.latte' props: [
			input: $form['kind'],
			type: 'select',
			classesLabel: ['title']
			]}
		{/if}

		{include $templates.'/part/core/checkbox.latte' props: [
			input: $form['reuse'],
			type: 'checkbox',
			classesLabel: ['title']
		]}

		{include $templates.'/part/core/checkbox.latte' props: [
			input: $form['combination'],
			type: 'checkbox',
			classesLabel: ['title']
		]}

		{include $templates.'/part/core/inp.latte' props: [
			input: $form['combinationType'],
			type: 'select',
			classesLabel: ['title']
		]}


		<div class="grid grid--y-0">
			<div class="grid__cell size--6-12">
				{include $templates.'/part/core/inp.latte' props: [
				input: $form['publicFrom'],
				classesLabel: ['title'],
				type: 'datetime-local'
				]}
			</div>
			<div class="grid__cell size--6-12">
				{include $templates.'/part/core/inp.latte' props: [
				input: $form['publicTo'],
				classesLabel: ['title'],
				type: 'datetime-local'
				]}
			</div>
		</div>

	{/block}
{/embed}
