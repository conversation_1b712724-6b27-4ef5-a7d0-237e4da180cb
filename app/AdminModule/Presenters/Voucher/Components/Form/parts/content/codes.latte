{var $props = [
title: 'Slevové kódy',
id: 'codes',
icon: $templates.'/part/icons/qrcode.svg',
open: true,
variant: 'main',
classes: ['u-mb-xxs']
]}

{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
	{block content}
		{*		{control codeGrid}*}

		{if $voucher->codes->countStored()}
			<p>
				<span class="icon icon-download-3"></span>
				<a n:href="export">{_exportVouchers}</a>
			</p>
		{/if}

		{if $voucher->codes->countStored()}
			{if $voucher->codes->countStored() <= 20} {* show max. 20 codes *}
				<p>
					{foreach $voucher->codes as $code}

						{if $code->isUsed}
							<span class="red">
							{$code->code}
						</span> ({_'voucher_code_used'})
						{else}
							<strong>
								{$code->code}
							</strong>
							{if $code->createdTime}
								({$code->createdTime|date:"d. m. Y"})
							{/if}
							{*						{if $code->specialPriceDPH}*}
							{*							sleva: {$code->specialPriceDPH} Kč *}{* |priceFormat neni mutace *}
							{*						{/if}*}
						{/if}
						{if !$iterator->last}, {/if}

					{/foreach}
				</p>
			{else}
				<p>{_'vouchersMoreThan20'}</p>
				<p>
					<span class="icon icon-download-3"></span>
					<a n:href="exportAll">{_exportVouchersAll}</a>
				</p>
			{/if}
		{/if}

	{/block}
{/embed}
