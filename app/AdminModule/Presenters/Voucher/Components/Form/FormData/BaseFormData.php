<?php declare(strict_types=1);

namespace App\AdminModule\Presenters\Voucher\Components\Form\FormData;

use App\Model\Orm\Price;
use App\PostType\Core\AdminModule\Components\Form\FormData\FormDataHelper;
use App\PostType\Core\AdminModule\Components\Form\FormData\PublishFormData;
use Brick\Money\Money;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;

final class BaseFormData
{

	public Price $discount;

	public ?DateTimeImmutable $publicFrom;

	public ?DateTimeImmutable $publicTo;

	public function __construct(
		public string $type,
		public string $name,
		public PublishFormData $publish,
		public ArrayHash $codes,
		public ?string $internalName = null,
		public ?float $minPriceOrder = null,
		public ?bool $reuse = null,
		?string $publicFrom = null,
		?string $publicTo = null,
		public ?int $discountPercent = null,
		public ?float $discountAmount = null,
		public ?bool $combination = null,
		public ?string $combinationType = null,
	)
	{
		$this->publicFrom =	FormDataHelper::convertInvalidDateTimeValue($publicFrom);
		$this->publicTo = FormDataHelper::convertInvalidDateTimeValue($publicTo);
		$this->discount = Price::from(Money::of($this->discountAmount ?? 0, 'CZK'));
	}

}
