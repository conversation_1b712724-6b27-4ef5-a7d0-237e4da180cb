{layout $templates.'/@layout-new.latte'}
{varType App\Model\Orm\Voucher\Voucher $voucher}
{varType App\Model\Orm\VoucherLimit\VoucherLimit $voucherLimit}
{varType App\Model\Orm\VoucherLimit\VoucherLimitModel $voucherLimitModel}
{var $dataGridShown = true}

{block #content}
<div class="main__main">
	<div class="main__header" n:snippet="header">
		{include $templates.'/part/box/header.latte',
			props: [
				title: $sectionTitle,
				hrefClose: $presenter->link('edit', [id=>$voucher->id]),
				isPageTitle: true,
			]
		}
	</div>

	<div class="main__content scroll"  n:snippet="mainContent">
		{embed $templates . '/part/box/toggle.latte', templates => $templates, props=> [
			title: $voucherLimit !== null ? $translator->translate('voucher_edit_condition') : $translator->translate('voucher_add_condition'),
			id: 'filter',
			icon: $templates . '/part/icons/search.svg',
			variant: 'main',
			open: false,
			classes: ['u-mb-xxs'],
			rowMainClass: 'row-main-max',
		]}
			{block content}
				{control filterForm}
			{/block}
		{/embed}

		{embed $templates . '/part/box/toggle.latte', templates => $templates, props=> [
			title: $translator->translate('Products') . ' [ '.$voucherLimitModel->getTextCondition($voucher->limits->toCollection()).' ]',
			id: 'products',
			icon: $templates . '/part/icons/book.svg',
			variant: 'main',
			open: false,
			classes: ['u-mb-xxs'],
			rowMainClass: 'row-main-max',
		]}
			{block content}
				{control limitDataGrid}
			{/block}
		{/embed}





	</div>

	<div class="main__content-side scroll">
		{var $alpha = 'A'}
		<p class="u-mb-xs" n:foreach="$voucher->limits as $limit">
			<a n:href="this, voucherLimitId: $limit->id" class="btn btn--full btn--success">
				<span class="btn__text item-icon" {if $limit === $voucherLimit}style="background: #000"{/if}>
					<span class="item-icon__icon icon">
						{include $templates.'/part/icons/edit.svg'}
					</span>
					<span class="item-icon__text">
						<span class="grid-inline">
							<span>
								{$alpha} {_voucher_condition}
							</span>
						</span>
					</span>
				</span>
			</a>
			{php $alpha++}
		</p>
		<p class="u-mb-xs">
			<a n:href="this, voucherLimitId:null" class="btn btn--full btn--grey">
			<span class="btn__text item-icon">
				<span class="item-icon__icon icon">
					{include $templates.'/part/icons/plus.svg'}
				</span>
				<span class="item-icon__text">
					<span class="grid-inline"> <span>{_voucher_add_condition}</span></span>
				</span>
			</span>
			</a>
		</p>
	</div>
</div>
