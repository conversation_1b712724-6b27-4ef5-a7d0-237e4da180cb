<?php

namespace App\AdminModule\Presenters\Voucher;

use App\AdminModule\Presenters\BasePresenter;
use App\AdminModule\Presenters\Voucher\Components\DataGrid\DataGrid;
use App\AdminModule\Presenters\Voucher\Components\DataGrid\DataGridFactory;
use App\AdminModule\Presenters\Voucher\Components\Filter\FilerFormFactory;
use App\AdminModule\Presenters\Voucher\Components\Filter\FilterForm;
use App\AdminModule\Presenters\Voucher\Components\Form\Form;
use App\AdminModule\Presenters\Voucher\Components\Form\FormFactory;
use App\AdminModule\Presenters\Voucher\Components\LimitDataGrid;
use App\AdminModule\Presenters\Voucher\Components\ShellForm\ShellForm;
use App\AdminModule\Presenters\Voucher\Components\ShellForm\ShellFormFactory;
use App\Model\Orm\Voucher\Voucher;
use App\Model\Orm\VoucherLimit\VoucherLimit;
use App\Model\Orm\VoucherLimit\VoucherLimitModel;
use Elastica\Query\BoolQuery;
use Nette\Application\Attributes\Persistent;
use Nette\Application\Responses\JsonResponse;
use Nextras\Orm\Entity\IEntity;
use Nextras\Orm\Repository\IRepository;

class VoucherPresenter extends BasePresenter
{

	private ?Voucher $object;

	private ?VoucherLimit $voucherLimit = null;

	#[Persistent]
	public array $filterSetup = [];

	private BoolQuery $boolQuery;

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
		private readonly ShellFormFactory $shellFormFactory,
		private readonly FormFactory $formFactory,
		private readonly FilerFormFactory $filerFormFactory,
		private readonly VoucherLimitModel $voucherLimitModel,
		private readonly LimitDataGrid\DataSourceFactory $dataSourceFactory,
		private readonly LimitDataGrid\DataGridFactory $limitDataGridFactory,
	)
	{
	}

	public function startup(): void
	{
		parent::startup();
	}

	public function actionEdit(int $id): void
	{
		$this->object = $this->orm->voucher->getById($id);

		if (!$this->object) {
			$this->redirect('default');
		}
	}

	public function actionLimits(int $id, ?int $voucherLimitId = null): void
	{
		$this->object = $this->orm->voucher->getById($id);
		if ($this->object === null) {
			$this->redirect('default');
		}

		if ($voucherLimitId !== null) {
			$this->voucherLimit = $this->orm->voucherLimit->getById($voucherLimitId);
		}

		$this->boolQuery = $this->voucherLimitModel->getElasticCondition($this->object->limits->toCollection());
	}

	public function searchRepoistory(IRepository $repository, string $searchTerm, array $selectedValues, string $valueColumn = 'name', string $repositoryMethod = 'searchByName'): void // @phpstan-ignore-line
	{
		$items = $repository->{$repositoryMethod}($searchTerm, $selectedValues)->limitBy(20)->fetchAll();

		$responseData = array_map(function (IEntity $item) use ($valueColumn): array {
			return ['value' => $item->getPersistedId(), 'label' => $item->{$valueColumn}];
		}, $items);
		$this->sendResponse(
			new JsonResponse(
				$responseData
			)
		);
	}

	public function getSelectedValues(string $selectedValues): array
	{
		if ($selectedValues === '') {
			$selectedValues = [];
		} else {
			$selectedValues = explode(',', $selectedValues);
		}
		return $selectedValues;
	}

	public function renderLimits(): void
	{
		$this->template->add('voucher', $this->object);
		$this->template->add('voucherLimit', $this->voucherLimit);
		$this->template->add('voucherLimitModel', $this->voucherLimitModel);
		$this->template->add('sectionTitle', str_replace('%voucherName', $this->object->internalName, $this->translator->translate('voucher_limits')));
	}

	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create();
	}

	protected function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create($this->userEntity);
	}

	protected function createComponentForm(): Form
	{
		return $this->formFactory->create($this->object, $this->userEntity);
	}

	protected function createComponentFilterForm(): FilterForm
	{
		return $this->filerFormFactory->create($this->filterSetup, $this->object, $this->voucherLimit);
	}

	protected function createComponentLimitDataGrid(): LimitDataGrid\DataGrid
	{
		$dataSource = $this->dataSourceFactory->create($this->boolQuery, $this->mutationHolder->getMutation());
		return $this->limitDataGridFactory->create($dataSource);
	}

}
