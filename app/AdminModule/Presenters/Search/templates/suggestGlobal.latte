{if isset($esResult)}
	<ul class="reset">
		{dump $esResult->getResults()}
	{foreach $esResult->getResults() as $k => $i}
		{varType Elastica\Result $i}
		{dump $i->kind}
		{if $i->kind == 'blog'}
			<li class="item" data-id="{$i->id}">
				<a n:href=":Blog:Admin:Blog:edit $i->id">{$i->name|prepareStrJs}{if $isMoreThanOneLanguage} ({$i->langCode|prepareStrJs}){/if}</a>
			</li>
		{elseif $i->kind == 'blogTag'}
			<li class="item" data-id="{$i->id}">
				<a n:href=":BlogTag:Admin:BlogTag:edit $i->id">{$i->name|prepareStrJs}{if $isMoreThanOneLanguage} ({$i->langCode|prepareStrJs}){/if}</a>
			</li>
		{elseif $i->kind == 'author'}
			<li class="item" data-id="{$i->id}">
				<a n:href=":Author:Admin:Author:edit $i->id">{$i->name|prepareStrJs}{if $isMoreThanOneLanguage} ({$i->langCode|prepareStrJs}){/if}</a>
			</li>
		{elseif $i->kind == 'tree'}
			<li class="item" data-id="{$i->id}">
				<a n:href=":Page:Admin:Page:default $i->id">{$i->name|prepareStrJs}{if $isMoreThanOneLanguage} ({$i->langCode|prepareStrJs}){/if}</a>
			</li>
		{elseif $i->kind == 'seoLink'}
			<li class="item" data-id="{$i->id}">
				<a n:href=":Admin:SeoLink:edit $i->id">{$i->name|prepareStrJs}{if $isMoreThanOneLanguage} ({$i->langCode|prepareStrJs}){/if}</a>
			</li>
		{elseif $i->kind == 'product'}
			<li class="item" data-id="{$i->id}">
				[product]
				<a n:href=":Admin:Product:edit $i->id">{$i->name|prepareStrJs}</a>
			</li>
		{/if}
	{/foreach}
	</ul>
{/if}
