<?php declare(strict_types=1);

namespace App\AdminModule\Presenters\ClassSection;

use App\AdminModule\Presenters\BasePresenter;
use App\AdminModule\Presenters\ClassSection\Components\Form\Form;
use App\AdminModule\Presenters\ClassSection\Components\Form\FormFactory;
use App\AdminModule\Presenters\ClassSection\Components\MetaDataGrid\MetaDataGrid;
use App\AdminModule\Presenters\ClassSection\Components\MetaDataGrid\MetaDataGridFactory;
use App\Model\Orm\ClassSection\ClassSection;
use App\Model\Orm\Product\Product;
use Nette\Application\Attributes\Persistent;
use Nette\DI\Attributes\Inject;

class ClassSectionPresenter extends BasePresenter
{

	#[Persistent]
	public int $id;

	#[Persistent]
	public int $productId;

	#[Inject]
	public FormFactory $formFactory;

	#[Inject]
	public MetaDataGridFactory $metaDataGridFactory;

	private Product $product;

	private ClassSection $classSection;

	public function actionEdit(int $id, int $productId): void
	{
		$this->product = $this->orm->product->getByIdChecked($productId);
		$this->classSection = $this->product->classSections->toCollection()->getByIdChecked($id);
	}
	public function renderEdit(): void
	{
		$this->template->product = $this->product;
		$this->template->classSection = $this->classSection;
	}

	protected function createComponentForm(): Form
	{
		return $this->formFactory->create($this->classSection);
	}

	protected function createComponentMetaDataGrid(): MetaDataGrid
	{
		return $this->metaDataGridFactory->create($this->classSection);
	}

}
