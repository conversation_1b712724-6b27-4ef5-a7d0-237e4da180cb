<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\ClassSection\Components\MetaDataGrid;

use App\Model\Orm\ClassEvent\ClassEvent;
use App\Model\Orm\ClassEventSectionMetadata\ClassEventSectionMetadata;
use App\Model\Orm\ClassEventSectionMetadata\ClassEventSectionMetadataModel;
use App\Model\Orm\ClassEventSectionMetadata\ClassEventSectionMetadataRepository;
use App\Model\Orm\ClassSection\ClassSection;
use App\Model\Translator;
use Nette\Application\UI\Control;
use Nette\Forms\Container;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Ublaboo\DataGrid\Column\Action\Confirmation\StringConfirmation;
use Ublaboo\DataGrid\DataGrid;

class MetaDataGrid extends Control
{

	public function __construct(
		private readonly Translator $translator,
		private readonly ClassEventSectionMetadataModel $classEventSectionMetadataModel,
		private readonly ClassEventSectionMetadataRepository $classEventSectionMetadataRepository,
		private readonly ?ClassEvent $classEvent = null,
		private readonly ?ClassSection $classSection = null,
	)
	{
	}

	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->render(__DIR__ . '/metaDataGrid.latte');
	}

	public function createComponentGrid(): DataGrid
	{
		$grid = new DataGrid();
		$grid->setTranslator($this->translator);
		$grid->setItemsPerPageList([20, 30, 40, 50, 100], false);

		$grid->setDataSource(
			$this->getCollection()
		);

		if ($this->classSection !== null) {
			$this->addEvent($grid);
		}
		if ($this->classEvent !== null) {
			$this->addSection($grid);
		}

		$grid->addColumnText('content', 'content_for_section_type');
		$grid->addColumnText('description', 'description');

		$add = $grid->addInlineAdd();
		$add->setPositionTop();
		$add->onControlAdd[] = $this->addInlineAdd(...);
		$add->onSubmit[] = $this->addSubmit(...);

		$inlineEdit = $grid->addInlineEdit();
		$inlineEdit->onControlAdd[] = $this->addInlineEdit(...);
		$inlineEdit->onSetDefaults[]  = $this->setDefaultForInlineEdit(...);
		$inlineEdit->onSubmit[] = $this->editSubmit(...);

		$grid->addAction('delete', '', 'delete!')
			->setIcon('trash')
			->setTitle('Smazat')
			->setClass('btn btn-xs btn-danger ajax')
			->setConfirmation(
				new StringConfirmation('Opravdu smazat?')
			);

		return $grid;
	}

	/**
	 * @return ICollection<ClassEventSectionMetadata>
	 */
	public function getCollection(): ICollection
	{
		if ($this->classSection !== null) {
			return $this->classSection->classEventSectionMetadata->toCollection();
		} else {
			assert($this->classEvent !== null);
			return $this->classEvent->classEventSectionMetadata->toCollection();
		}
	}

	private function addEvent(DataGrid $grid): void
	{
		$grid->addColumnText('event', 'class_event')->setRenderer(
			fn(ClassEventSectionMetadata $classEventSectionMetadata) => $classEventSectionMetadata->classEvent->getNiceName()
		);
	}

	private function addSection(DataGrid $grid): void
	{
		$grid->addColumnText('section', 'class_section')->setRenderer(
			fn(ClassEventSectionMetadata $classEventSectionMetadata) => $classEventSectionMetadata->classSection->name
		);
	}

	private function addInlineEdit(Container $container): void
	{
		if ($this->classSection !== null) {
			$container->addSelect('event', 'event', $this->getEventSelector());
		}
		if ($this->classEvent !== null) {
			$container->addSelect('section', 'section', $this->getSectionSelector());
		}
		$container->addText('content', 'content');
		$container->addTextArea('description', 'description');
	}

	/**
	 * @return array|string[]
	 */
	public function getEventSelector(): array
	{
		$events = $this->classSection->product->classEvents->toCollection()->findBy(['mutation' => $this->classSection->mutation])->fetchPairs('id');
		$events = array_map(fn(ClassEvent $event) => $event->getNiceName(), $events);
		return $events;
	}

	/**
	 * @return array|string[]
	 */
	public function getSectionSelector(): array
	{
		return $this->classEvent->product->classSections->toCollection()->findBy(['mutation' => $this->classEvent->mutation])->fetchPairs('id', 'name');
	}

	private function setDefaultForInlineEdit(Container $container, ClassEventSectionMetadata $item): void
	{
		if ($this->classSection !== null) {
			$container->setDefaults([
				'event' => $item->classEvent->id,
				'content' => $item->content,
				'description' => $item->description,
			]);
		}

		if ($this->classEvent !== null) {
			$container->setDefaults([
				'section' => $item->classSection->id,
				'content' => $item->content,
				'description' => $item->description,
			]);
		}
	}

	private function addInlineAdd(Container $container): void
	{
		if ($this->classSection !== null) {
			$events = $this->getEventSelector();
			$container->addSelect('event', 'event', $events)->setRequired();
		}
		if ($this->classEvent !== null) {
			$sections = $this->getSectionSelector();
			$container->addSelect('section', 'section', $sections)->setRequired();
		}
		$container->addText('content', 'content');
		$container->addTextArea('description', 'description');
		//->addRule(Form::URL, 'Please enter a valid URL.')
	}

	private function addSubmit(ArrayHash $values): void
	{
		$content = $values['content'] ?? '';
		$description = $values['description'] ?? '';
		if ($this->classSection !== null) {
			$eventId = $values['event'] ?? null;
			if ($eventId !== null && ($event = $this->classSection->product->classEvents->toCollection()->getById($eventId)) !== null) {
				$this->classEventSectionMetadataModel->create($event, $this->classSection, $content, $description);
			}
		}

		if ($this->classEvent !== null) {
			$sectionId = $values['section'] ?? null;
			if ($sectionId !== null && ($section = $this->classEvent->product->classSections->toCollection()->getById($sectionId)) !== null) {
				$this->classEventSectionMetadataModel->create($this->classEvent, $section, $content, $description);
			}
		}

		$this['grid']->setDataSource($this->getCollection());
		$this['grid']->reload();
		$this->redrawControl();
	}

	private function editSubmit(string $id, ArrayHash $values): void
	{
		$classEventSectionMetadata = $this->classEventSectionMetadataRepository->getById((int) $id);
		if ($classEventSectionMetadata !== null) {
			$classEventSectionMetadata->content = $values['content'] ?? '';
			$classEventSectionMetadata->description = $values['description'] ?? '';

			if ($this->classSection !== null) {
				$eventId = $values['event'] ?? null;
				if ($eventId !== null && ($event = $this->classSection->product->classEvents->toCollection()->getById($eventId)) !== null) {
					$classEventSectionMetadata->classEvent = $event;
				}
			}

			if ($this->classEvent !== null) {
				$sectionId = $values['section'] ?? null;
				if ($sectionId !== null && ($section = $this->classEvent->product->classSections->toCollection()->getById($sectionId)) !== null) {
					$classEventSectionMetadata->classSection = $section;
				}
			}

			$this->classEventSectionMetadataRepository->persistAndFlush($classEventSectionMetadata);
		}
	}

	public function handleDelete(string $id): void
	{
		$classEventSectionMetadata = $this->classEventSectionMetadataRepository->getById((int) $id);
		if ($classEventSectionMetadata !== null) {
			$this->classEventSectionMetadataRepository->removeAndFlush($classEventSectionMetadata);
			$this['grid']->setDataSource($this->getCollection());
			$this['grid']->reload();
			$this->redrawControl();
		}
	}

}
