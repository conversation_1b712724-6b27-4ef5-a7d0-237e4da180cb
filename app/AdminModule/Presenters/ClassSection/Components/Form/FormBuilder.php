<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\ClassSection\Components\Form;

use App\Model\Orm\ClassSection\ClassSection;
use Nette\Application\UI\Form;

final class FormBuilder
{

	public function __construct()
	{
	}


	public function build(Form $form, ClassSection $classSection): void
	{
		$form->addText('name', 'name')->setDefaultValue($classSection->name)->setRequired();
		$form->addText('content', 'content_for_section_type')->setDefaultValue($classSection->content);
		$form->addTextArea('description', 'description')->setDefaultValue($classSection->description);

		$types = ClassSection::getConstsByPrefix('TYPE_', 'section_type_');
		$form->addSelect('type', 'type', $types)->setDefaultValue($classSection->type)->setRequired();

		$form->addInteger('completionTime', 'completionTime')->setDefaultValue($classSection->completionTime)->setRequired();
		$form->addSubmit('send');
	}

}
