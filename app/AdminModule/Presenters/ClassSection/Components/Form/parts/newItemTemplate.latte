<div class="u-hide">

		{var $productLocalization = $product->productLocalizations->toCollection()->getBy(['mutation' => $mutation])}
		{var $categoryContainer = $form['productLocalizations'][$mutation->id]['categories']['newItemMarker']}
		<div data-Templates-target="categoryItem" data-mutationid="{$mutation->id}">
			{var $url = $urls['searchMutationPage']->toArray()}
			{php $url['params']['mutationId'] = $mutation->id}
			{php $url['params']['templates'] = [':Front:Catalog:default']}


			{include $templates.'/part/box/list-item.latte',
			props: [
				data: [
					controller: 'RemoveItem SuggestInp',
					removeitem-target: 'item',
					suggestinp-target: 'wrapper',
					suggestinp-url-value: Nette\Utils\Json::encode($url)
			],
				inps: [
					[
						placeholder: '<PERSON>adejte název kategorie',
						input: $categoryContainer['name'],
						data: [
							suggestinp-target: 'input',

						]
					],
					[
						input: $categoryContainer['id'],
						data: [
							suggestinp-target: 'idInput',
						],
						classes: 'u-hide',
						type: 'hidden'
					]

				],
				btnsAfter: [
					[
						icon: $templates.'/part/icons/trash.svg',
						tooltip: 'Odstranit',
						variant: 'remove',
						data: [
							action: 'RemoveItem#remove'
						]
					]
				],
				dragdrop: true
			]
			}
		</div>
</div>
