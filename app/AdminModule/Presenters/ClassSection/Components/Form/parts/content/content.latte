{var $anchorName = 'content'}
{var $icon = $templates . '/part/icons/align-left.svg'}
{var $title = 'Obsah'}

{var $props = [
	title: $title,
	id: $anchorName,
	icon: $icon,
	variant: 'main',
	open: true,
	classes: ['u-mb-xxs'],
]}

{*{embed $templates . '/part/box/toggle.latte', props => $props, templates => $templates}*}
{*	{block content}*}
		{include $templates . '/part/core/inp.latte', props: [
			input: $form['name'],
			classesLabel: ['title'],
		]}
		{include $templates . '/part/core/inp.latte', props: [
			input: $form['type'],
			classesLabel: ['title'],
			removeItemButton: false,
			type: select,
		]}

{*		{include $templates . '/part/core/inp.latte', props: [*}
{*			input: $form['mutation'],*}
{*			classesLabel: ['title'],*}
{*			removeItemButton: false,*}
{*			type: select,*}
{*		]}*}

		{include $templates . '/part/core/inp.latte', props: [
			input: $form['completionTime'],
			classesLabel: ['title'],
			type: number,
		]}

		{include $templates.'/part/core/inp.latte' props: [
			input: $form['description'],
			classesLabel: ['title'],
			type: 'textarea',
			rows: 3,
		]}
		{include $templates . '/part/core/inp.latte', props: [
			input: $form['content'],
			classesLabel: ['title'],
		]}


{*	{/block}*}
{*{/embed}*}



