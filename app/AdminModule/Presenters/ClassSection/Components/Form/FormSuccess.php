<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\ClassSection\Components\Form;

use App\Model\Orm\ClassSection\ClassSection;
use App\Model\Orm\ClassSection\ClassSectionRepository;

final class FormSuccess
{

	public function __construct(
		//		private readonly CustomFields $customFields,
		//		private readonly CustomContent $customContent,
		private readonly ClassSectionRepository $classSectionRepository,
	)
	{
	}

	public function execute(ClassSection $classSection, FormBaseData $submittedValues): void
	{
		$classSection->name = $submittedValues->name;
		$classSection->completionTime = $submittedValues->completionTime;
		$classSection->type = $submittedValues->type;
//		$classSection->mutation = $submittedValues->mutation;
		$classSection->content = $submittedValues->content;
		$classSection->description = $submittedValues->description;
		$this->classSectionRepository->persistAndFlush($classSection);
	}

}
