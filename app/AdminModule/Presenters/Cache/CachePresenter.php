<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Cache;

use App\AdminModule\Presenters\BasePresenter;
use App\AdminModule\Presenters\Cache\Components\DataGrid\DataGrid;
use App\AdminModule\Presenters\Cache\Components\DataGrid\DataGridFactory;

final class CachePresenter extends BasePresenter
{

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,

	)
	{
		parent::__construct();
	}


	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create();
	}

}
