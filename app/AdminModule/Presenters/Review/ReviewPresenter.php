<?php

declare(strict_types=1);

namespace App\AdminModule\Presenters\Review;

use App\AdminModule\Presenters\BasePresenter;

final class ReviewPresenter extends BasePresenter
{

	public function actionDefault(?int $id): void
	{
		$this->addComponent($this->visualPaginatorFactory->create(), 'pager');
		$paginator = $this['pager']->getPaginator();
		$paginator->itemsPerPage = $this->configService->getParam('adminPaging');

		$reviews = $this->orm->productReview->findAll()->orderBy(["date" => "DESC"])->limitBy($paginator->itemsPerPage, $paginator->offset);
		$paginator->itemCount = $this->orm->productReview->findAll()->count();
		$this->template->reviews = $reviews;
	}

}
