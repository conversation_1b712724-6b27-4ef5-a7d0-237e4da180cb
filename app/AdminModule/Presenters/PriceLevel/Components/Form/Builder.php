<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\PriceLevel\Components\Form;

use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\User\User;
use App\PostType\Core\AdminModule\Components\Form\Builder as CoreBuilder;

final class Builder
{
	public function __construct(
		private readonly CoreBuilder $coreBuilder,
	)
	{
	}

	public function build(\Nette\Application\UI\Form $form, PriceLevel $priceLevel, User $user): void
	{
		$form->addText('name', 'label_name')
			->setDefaultValue($priceLevel->name);

		if ($priceLevel->isEditDiscount) {
			$form->addText('discount', 'price_level_label_discount')
				->setDefaultValue($priceLevel->discount);
		}
		if ($priceLevel->isEditDiscountSubscriptions) {
			$form->addText('discountSubscription', 'price_level_label_discount_subscription')
				->setDefaultValue($priceLevel->discountSubscription);
			$form->addText('discountSubscriptionFirst', 'price_level_label_discount_subscription_first')
				->setDefaultValue($priceLevel->discountSubscriptionFirst);
		}

		$this->coreBuilder->addButtons($form);
	}

}
