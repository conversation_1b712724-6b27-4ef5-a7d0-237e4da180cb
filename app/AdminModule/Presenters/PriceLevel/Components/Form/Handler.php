<?php

declare(strict_types = 1);

namespace App\AdminModule\Presenters\PriceLevel\Components\Form;

use App\AdminModule\Presenters\PriceLevel\Components\Form\FormData\BaseFormData;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\PriceLevel\PriceLevelRepository;
use App\Model\Orm\User\User;
use App\Model\StringHelper;

final class Handler
{

	public function __construct(
		private readonly PriceLevelRepository $priceLevelRepository,
//		private readonly CacheFactory $cacheFactory,
	)
	{
	}

	public function handle(PriceLevel $priceLevel, BaseFormData $data, User $user): void
	{
		$priceLevel->name = $data->name;

		if ($priceLevel->isEditDiscount) {
			$priceLevel->discount = StringHelper::floatToNull(str_replace(',', '.', $data->discount));
		}

		if ($priceLevel->isEditDiscountSubscriptions) {
			$priceLevel->discountSubscription = StringHelper::floatToNull(str_replace(',', '.', $data->discountSubscription));
			$priceLevel->discountSubscriptionFirst = StringHelper::floatToNull(str_replace(',', '.', $data->discountSubscriptionFirst));
		}

		$this->priceLevelRepository->persistAndFlush($priceLevel);

//		$this->cacheFactory->create('Nette.Templating.Cache')->clean([
//			Cache::Tags => [
//				'serviceMenu',
//				'footer',
//			]
//		]);
	}

}
