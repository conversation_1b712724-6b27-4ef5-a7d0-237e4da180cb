<?php declare(strict_types=1);

namespace App\AdminModule\Presenters\PriceLevel;

use App\AdminModule\Presenters\BasePresenter;
use App\AdminModule\Presenters\PriceLevel\Components\DataGrid\DataGrid;
use App\AdminModule\Presenters\PriceLevel\Components\DataGrid\DataGridFactory;
use App\AdminModule\Presenters\PriceLevel\Components\Form\Form;
use App\AdminModule\Presenters\PriceLevel\Components\Form\FormFactory;
use App\Model\Orm\PriceLevel\PriceLevel;
use Nette\Application\Attributes\Persistent;
use Nextras\Orm\Collection\ICollection;

final class PriceLevelPresenter extends BasePresenter
{

	protected ?PriceLevel $object = null;
	/** @var ICollection<PriceLevel>  */
	private ICollection $priceLevels;

	#[Persistent]
	public int $id;

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
		private readonly FormFactory $formFactory,
	)
	{
		parent::__construct();
	}


	public function actionDefault(): void
	{
		$this->priceLevels = $this->orm->priceLevel->findAll()->orderBy('sort');
	}


	public function actionEdit(int $id): void
	{
		$this->object = $this->orm->priceLevel->getById($id);

		if (!$this->object) {
			$this->redirect('default');
		}
	}

	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create($this->priceLevels);
	}

	protected function createComponentForm(): Form
	{
		return $this->formFactory->create($this->object, $this->userEntity);
	}

}
