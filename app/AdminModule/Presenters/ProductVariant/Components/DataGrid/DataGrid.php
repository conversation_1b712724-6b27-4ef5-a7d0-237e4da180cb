<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\ProductVariant\Components\DataGrid;

use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Translator;
use Nette\Application\UI\Control;
use Nextras\Orm\Collection\ICollection;
use Ublaboo\DataGrid\Exception\DataGridException;

class DataGrid extends Control
{

	/**
	 * @param ICollection<ProductVariant> $collection
	 */
	public function __construct(
		private readonly ICollection $collection,
		private readonly Translator $translator,
	)
	{
	}

	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);

		$template->render(__DIR__ . '/dataGrid.latte');
	}

	/**
	 * @throws DataGridException
	 */
	public function createComponentGrid(): \Ublaboo\DataGrid\DataGrid
	{
		$grid = new \Ublaboo\DataGrid\DataGrid();
		$grid->setDataSource($this->collection);
		$grid->setTranslator($this->translator);
		$grid->setItemsPerPageList([10], false);

		$grid->addColumnText('extId', 'erpCode');
		$grid->addColumnText('ean', 'ean');
		$grid->addColumnText('erpName', 'name', 'erpName');
		$grid->addColumnText('erpCategoryPath', 'erpCategory', 'erpCategoryPath')
			->setRenderer(function ($value) {
				return str_replace('%|%', ' / ', $value->erpCategoryPath ?? '');
			});

		$grid->setDefaultSort(['erpName' => ICollection::ASC]);

		return $grid;
	}
}
