<?php declare(strict_types=1);

namespace App\AdminModule\Presenters\ProductVariant\Components\Filter;

use App\Model\Orm\ProductVariant\ProductVariantRepository;
use App\Model\Translator;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;

class FilterForm extends Control
{

	public function __construct(
		private readonly array $filterSetup,
		private readonly Translator $translator,
		private readonly ProductVariantRepository $variantRepository,
	)
	{
	}

	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->add('templates', RS_TEMPLATE_DIR);

		$template->render(__DIR__ . '/filterForm.latte');
	}


	protected function createComponentForm(): Form
	{
		$form = new Form();
		$form->setTranslator($this->translator);

		$form->addTextArea('erpIds', 'erpCode');
		$form->addTextArea('eans', 'ean');
		$form->addSelect('erpCategory', 'erpCategory', $this->getErpCategoryList())->setPrompt('Nezvoleno');

		if (isset($this->filterSetup['eans'])) {
			$form['eans']->setDefaultValue($this->filterSetup['eans']);
		}

		if (isset($this->filterSetup['erpIds'])) {
			$form['erpIds']->setDefaultValue($this->filterSetup['erpIds']);
		}

		if (isset($this->filterSetup['erpCategory'])) {
			$form['erpCategory']->setDefaultValue($this->filterSetup['erpCategory']);
		}

		if (isset($this->filterSetup['products']) && is_array($this->filterSetup['products'])) {
			$products = $this->variantRepository->findByIds($this->filterSetup['products'])->fetchPairs('id', 'erpName');
			$form->addMultiSelect('products', 'products', $products);
			$form['products']->setDefaultValue(array_keys($products));
		} else {
			$form->addMultiSelect('products', 'products', []);
		}

		$form->addSubmit('filter', 'filter');

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];
		return $form;
	}


	public function formError(Form $form): void
	{
		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSucceeded(Form $form, array $data): void
	{
		$httpData = $form->getHttpData();
		assert(is_array($httpData));
		$fakedItems = ['products'];
		foreach ($fakedItems as $fakedItem) {
			if (isset($httpData[$fakedItem])) {
				$data[$fakedItem] = $httpData[$fakedItem];
			}
		}
		$this->presenter->redirect('this', ['filterSetup' => $data]);
	}

	private function getErpCategoryList(): array
	{
		$list = $this->variantRepository->getErpCategoryList();

		return array_map(function ($value) {
			return str_replace('%|%', ' / ', $value);
		}, $list);
	}
}
