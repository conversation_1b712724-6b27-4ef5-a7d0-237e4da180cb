

<form n:name="form">
	{foreach $flashes as $flash}
		<div class="message message--{$flash->type}">{$flash->message}</div>
	{/foreach}
	<br>

	{if isset($form['products'])}
		{include $templates.'/part/core/inp.latte' props: [
			input: $form['products'],
			type: 'select',
			multiple: true,
			dataInp: [
				searchUrl: $presenter->link(':products', ['filterSetup' => []]),
				asyncurl: true
			]
		]}
	{/if}

	<div class="grid grid--left grid--x-xs">
		<div class="grid__cell size--12-12">
			{include $templates.'/part/core/inp.latte' props: [
				input: $form['erpCategory'],
				type: 'select',
				classesLabel: ['title']
			]}
		</div>
	</div>
	<div class="grid grid--left grid--x-xs">
		<div class="grid__cell size--6-12">
			{include $templates.'/part/core/inp.latte' props: [
			input: $form['erpIds'],
			type: 'textarea',
			]}
		</div>
		<div class="grid__cell size--6-12">
			{include $templates.'/part/core/inp.latte' props: [
				input: $form['eans'],
				type: 'textarea',
			]}
		</div>
	</div>
	<p>
		<button n:name="filter" class="btn btn--full btn--success">
			<span class="btn__text item-icon">
				<span class="item-icon__icon icon">
					{include $templates.'/part/icons/plus.svg'}
				</span>
				<span class="item-icon__text">
					Filtrovat
				</span>
			</span>
		</button>
	</p>
</form>
