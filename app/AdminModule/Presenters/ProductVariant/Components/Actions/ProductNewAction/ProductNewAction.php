<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\ProductVariant\Components\Actions\ProductNewAction;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Product\ProductModel;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\User\User;
use App\Model\Translator;
use Nette\Application\AbortException;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nextras\Orm\Collection\ICollection;
use Throwable;
use Tracy\Debugger;

class ProductNewAction extends Control
{

	/**
	 * @param ICollection<ProductVariant> $variants
	 */
	public function __construct(
		private readonly Mutation $mutation,
		private readonly ICollection $variants,
		private readonly User $user,
		private readonly Translator $translator,
		private readonly ProductModel $productModel,
	)
	{
		$this->onAnchor[] = [$this, 'init'];
	}


	public function init(): void
	{
	}

	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->add('templates', RS_TEMPLATE_DIR);
		$template->render(__DIR__ . '/productNewAction.latte');
	}

	protected function createComponentForm(): Form
	{
		$form = new Form();
		$form->setTranslator($this->translator);

		$form->addSubmit('add');

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];
		return $form;
	}

	public function formError(Form $form): void
	{
		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	public function formSucceeded(Form $form, array $data): void
	{
		try {
			$product = $this->productModel->createFromFreeErpVariants($this->mutation, $this->variants, $this->user);
			$this->presenter->redirect(':Admin:Product:edit', ['id' => $product->id]);

		} catch (AbortException $e) {
			throw $e;

		} catch (Throwable $e) {
			bd($e->getMessage());
			Debugger::log($e);
		}

		$this->flashMessage(sprintf('%s: %s', $this->translator->translate('error'), $this->translator->translate($e->getMessage())), 'error');
		$this->presenter->redirect('this');
	}

}
