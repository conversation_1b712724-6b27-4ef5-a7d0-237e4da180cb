<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\ProductVariant\Components\Actions\ProductAddAction;

use App\Exceptions\LogicException;
use App\Model\CustomField\SuggestUrls;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Product\ProductModel;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\User\User;
use App\Model\Translator;
use Nette\Application\AbortException;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nextras\Orm\Collection\ICollection;
use Throwable;
use Tracy\Debugger;

class ProductAddAction extends Control
{
	/**
	 * @param ICollection<ProductVariant> $variants
	 */
	public function __construct(
		private readonly Mutation $mutation,
		private readonly ICollection $variants,
		private readonly User $user,
		private readonly Translator $translator,
		private readonly SuggestUrls $urls,
		private readonly ProductModel $productModel,
		private readonly Orm $orm,
	)
	{
		$this->onAnchor[] = [$this, 'init'];
	}

	public function init(): void
	{
	}

	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->add('templates', RS_TEMPLATE_DIR);
		$template->urls = $this->urls;
		$template->render(__DIR__ . '/productAddAction.latte');
	}

	protected function createComponentForm(): Form
	{
		$form = new Form();
		$form->setTranslator($this->translator);

		// Přidání inputu pro produkt s našeptávačem
		$productContainer = $form->addContainer('product');
		$productContainer->addText('name', 'Produkt')
			->setHtmlAttribute('placeholder', 'Zadejte název produktu')
			->setHtmlAttribute('data-suggestinp-target', 'input');

		$productContainer->addHidden('id')
			->setHtmlAttribute('data-suggestinp-target', 'idInput');

		$form->addSubmit('add');

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];
		return $form;
	}

	public function formError(Form $form): void
	{
		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	public function formSucceeded(Form $form, array $data): void
	{
		try {
			$this->orm->product->setPublicOnly(false);
			$product = $this->orm->product->getById($data['product']['id']);

			if ($product === null) {
				throw new LogicException('add_product_msg_error_unknown_product');
			}

			$product = $this->productModel->addFreeErpVariants($this->mutation, $this->variants, $this->user, $product);
			$this->presenter->redirect(':Admin:Product:edit', ['id' => $product->id]);

		} catch (AbortException $e) {
			throw $e;

		} catch (Throwable $e) {
			bd($e->getMessage());
			Debugger::log($e);
		}

		$this->flashMessage(sprintf('%s: %s', $this->translator->translate('error'), $this->translator->translate($e->getMessage())), 'error');
		$this->presenter->redirect('this');
	}
}
