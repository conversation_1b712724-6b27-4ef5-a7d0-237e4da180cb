<form n:name="form">
    {foreach $flashes as $flash}
        <div class="message message--{$flash->type}">{$flash->message}</div>
    {/foreach}
    <br>

    <div class="grid grid--left grid--x-xs">
		<div class="grid__cell size--2-12">
			<button n:name="add" class="btn btn--success">
                <span class="btn__text item-icon">
                    <span class="item-icon__icon icon">
                        {include $templates.'/part/icons/plus.svg'}
                    </span>
                    <span class="item-icon__text">
                        {_'btn_product_action_add'}
                    </span>
                </span>
			</button>
		</div>
        <div class="grid__cell size--10-12">
			{var $url = $urls['searchProduct']->toArray()}
            {include $templates.'/part/box/list-item.latte',
                props: [
                    inps: [
                        [
                            input: $form['product']['name'],
                            placeholder: 'Zadejte název produktu',
                            data: [
                                suggestinp-target: 'input',
                            ]
                        ],
                        [
                            input: $form['product']['id'],
                            data: [
                                suggestinp-target: 'idInput',
                            ],
                            classes: 'u-hide',
                            type: 'hidden'
                        ]
                    ],
                    data: [
                        controller: 'SuggestInp',
                        suggestinp-target: 'wrapper',
						suggestinp-url-value: Nette\Utils\Json::encode($url)
                    ]
                ]
            }
        </div>
    </div>
</form>
