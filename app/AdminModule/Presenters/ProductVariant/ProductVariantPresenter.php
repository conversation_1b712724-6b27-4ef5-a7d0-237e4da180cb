<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\ProductVariant;

use App\AdminModule\Presenters\BasePresenter;
use App\AdminModule\Presenters\ProductVariant\Components\Actions\ProductNewAction\ProductNewAction;
use App\AdminModule\Presenters\ProductVariant\Components\Actions\ProductNewAction\ProductNewActionFactory;
use App\AdminModule\Presenters\ProductVariant\Components\Actions\ProductAddAction\ProductAddAction;
use App\AdminModule\Presenters\ProductVariant\Components\Actions\ProductAddAction\ProductAddActionFactory;
use App\AdminModule\Presenters\ProductVariant\Components\Filter\FilterFormFactory;
use App\AdminModule\Presenters\ProductVariant\Components\Filter\FilterForm;
use App\AdminModule\Presenters\ProductVariant\Components\DataGrid\DataGrid;
use App\AdminModule\Presenters\ProductVariant\Components\DataGrid\DataGridFactory;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\User\User;
use Nette\Application\Attributes\Persistent;
use Nette\Application\Responses\JsonResponse;
use Nextras\Orm\Collection\ICollection;

final class ProductVariantPresenter extends BasePresenter
{
	/** @var ICollection<ProductVariant> */
	private ICollection $variants;

	#[Persistent]
	public array $filterSetup = [];

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
		private readonly FilterFormFactory $filterFormFactory,
		private readonly ProductNewActionFactory $productNewActionFactory,
		private readonly ProductAddActionFactory $productAddActionFactory,
	)
	{
		parent::__construct();
	}

	public function actionDefault(): void
	{
		$this->variants = $this->orm->productVariant->findBy([
			'product' => null,
		]);

		$data = $this->filterSetup;
		$conds = [];

		if (isset($data['erpIds']) && trim($data['erpIds']) !== '') {
			$erpIds = explode("\n", $data['erpIds']);
			if ($erpIds !== []) {
				$conds['erpIds'] = $erpIds;
			}
		}

		if (isset($data['eans']) && trim($data['eans']) !== '') {
			$eans = explode("\n", $data['eans']);
			if ($eans !== []) {
				$conds['eans'] = $eans;
			}
		}

		if (isset($data['products']) && is_array($data['products']) && count($data['products']) > 0) {
			$conds['ids'] = $data['products'];
		}

		if (isset($data['erpCategory']) && trim($data['erpCategory']) !== '') {
			$conds['erpCategory'] = $data['erpCategory'];
		}

		if ($conds !== []) {
			$this->variants = $this->variants->findBy(
				[
					ICollection::OR,
					isset($conds['erpCategory']) ? ['erpCategoryPath' => $conds['erpCategory']] : [],
					isset($conds['erpIds']) ? ['extId' => $conds['erpIds']] : [],
					isset($conds['eans']) ? ['ean' => $conds['eans']] : [],
					isset($conds['ids']) ? ['id' => $conds['ids']] : [],
				]);
		}
	}

	public function actionProducts(string $searchTerm = '', string $selectedValues = ''): void
	{
		$responseData = [];
		$variants = $this->orm->productVariant->searchByName($searchTerm)
			->findBy([
				'product' => null
			])
			->limitBy(10);

		foreach ($variants as $variant) {
			$responseData[] = ['value' => $variant->id, 'label' => $variant->erpName];
		}

		$this->sendResponse(
			new JsonResponse(
				$responseData
			)
		);
	}

	public function renderDefault(): void
	{
		$this->template->hasFilter = $this->filterSetup !== [];
	}

	protected function createComponentFilterForm(): FilterForm
	{
		return $this->filterFormFactory->create($this->filterSetup);
	}

	protected function createComponentDataGrid(): DataGrid
	{
		return $this->dataGridFactory->create($this->variants);
	}

	protected function createComponentProductNewAction(): ProductNewAction
	{
		assert($this->userEntity instanceof User);
		return $this->productNewActionFactory->create($this->mutationsHolder->getDefault(), $this->variants, $this->userEntity);
	}

	protected function createComponentProductAddAction(): ProductAddAction
	{
		assert($this->userEntity instanceof User);
		return $this->productAddActionFactory->create($this->mutationsHolder->getDefault(), $this->variants, $this->userEntity);
	}

}
