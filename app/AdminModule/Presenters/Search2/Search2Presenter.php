<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Search2;

use App\AdminModule\Presenters\BasePresenter;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Searchable;
use App\PostType\Author\Model\Orm\AuthorRepository;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagRepository;
use App\PostType\Tag\Model\TagType;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\Expression\LikeExpression;

/**
 * @property DefaultTemplate $template
 */
final class Search2Presenter extends BasePresenter
{

	public function actionPage(?int $parentId = null, ?int $pathId = null): void
	{
		$search = $this->getSearch();
		$selectedSiblingsIds = $this->getSelectedSiblingsIds();

		if ($search) {

			$pages = $this->orm->tree->searchByName($search, $parentId, $pathId, $selectedSiblingsIds);
			$this->template->result = $pages;
		}
	}

	public function actionAltCategory(string $type): void
	{
		$search = $this->getSearch();

		if ($search) {
			$this->template->alts = $this->orm->treeAlternative->searchByName($search, $type);
		}
	}
	public function actionPageInMutation(?int $parentId = null, ?int $pathId = null, ?int $mutationId = null, string $templates = ''): void
	{
		$search = $this->getSearch();
		$selectedSiblingsIds = $this->getSelectedSiblingsIds();

		if ($search) {

			$pages = $this->orm->tree->searchByName($search, $parentId, $pathId, $selectedSiblingsIds);
			if ($mutationId) {
				$mutation = $this->orm->mutation->getById($mutationId);
				if ($mutation) {

					$pages = $pages->findBy([
						'rootId' => $mutation->rootId,
					]);
				}

				if ($templates !== '') {
					$pages = $pages->findBy(['template' => explode(',', $templates)]);
				}
			}

			$this->template->result = $pages;
		}
	}



	public function actionPageSibling(?int $ignoreMutationId, ?int $parentId, ?int $pathId): void
	{
		$search = $this->getSearch();
		$selectedSiblingsIds = $this->getSelectedSiblingsIds();

		if ($search !== '') {
			$pages = $this->orm->tree->searchByName($search, $parentId, $pathId, $selectedSiblingsIds);
			if ($ignoreMutationId !== null) {
				$pages = $pages->findBy(['mutation!=' => $ignoreMutationId]);
			}

			$pages = $pages->findBy(['treeParent!=' => null]);
			$this->template->result = $pages;
		}
	}


	public function actionTagInMutation(?int $parentId = null, ?int $mutationId = null): void
	{
		$search = $this->getSearch();
		$selectedSiblingsIds = $this->getSelectedSiblingsIds();

		if ($search) {

			$tags = $this->orm->blogTag->searchByName($search, $selectedSiblingsIds);
			if ($mutationId) {
				$mutation = $this->orm->mutation->getById($mutationId);
				if ($mutation) {
					$tags = $tags->findBy([
						'mutation' => $mutation,
					]);
				}
			}

			$this->template->result = $tags;
		}
	}

	public function actionTagLocalizations(): void
	{
		$this->commonSearch($this->orm->tagLocalization, $this->getMutation());
	}

	public function actionBlogTag(): void
	{
		$this->commonSearch($this->orm->blogTag);
	}

	public function actionAuthor(): void
	{
		$this->commonSearch($this->orm->author);
	}

	public function actionAlias(?int $mutationId = null): void
	{
		$search = $this->getSearch();
		$selectedSiblingsIds = $this->getSelectedSiblingsIds();

		if ($search !== '') {

			$aliases = $this->orm->alias->findByName($search);

			if ($selectedSiblingsIds !== []) {
				$aliases = $aliases->findBy(['id!=' => $selectedSiblingsIds]);
			}

			if ($mutationId !== null) {
				$mutation = $this->orm->mutation->getById($mutationId);
				if ($mutation !== null) {
					$aliases = $aliases->findBy([
						'mutation' => $mutation,
					]);
				}
			}

			$this->template->aliases = $aliases->limitBy(20);
		}
	}

	public function actionProduct(): void
	{
		$search = $this->getSearch();
		$selectedSiblingsIds = $this->getSelectedSiblingsIds();

		if ($search !== '') {
			$products = $this->orm->product->searchBy($search, $selectedSiblingsIds, groupById: true);
			$this->template->result = $products;
		}

		if ($this->isAjax()) {
			$this->setLayout(false);
		}
	}

	public function actionProductVariant(?int $mutationId = null): void
	{
		$search = $this->getSearch();
		$selectedSiblingsIds = $this->getSelectedSiblingsIds();

		if ($search !== '') {
			$result = $this->orm->productVariantLocalization->findBy(
				[
					'mutation->id' => $mutationId,
					'name~' => LikeExpression::contains($search)
				]);
			if ($selectedSiblingsIds !== []) {
				$result = $result->findBy(['variant->id!=' => $selectedSiblingsIds]);
			}

			$this->template->result = $result;
		}

		if ($this->isAjax()) {
			$this->setLayout(false);
		}
	}
	public function actionState(?int $mutationId): void
	{
		$search = $this->getSearch();
		$selectedSiblingsIds = $this->getSelectedSiblingsIds();


		if ($search !== '') {
			$result = $this->orm->state->searchByName($search, $selectedSiblingsIds);
			if ($mutationId !== null) {
				$mutation = $this->orm->mutation->getById($mutationId);
				$result = $result->findBy(['id' => $mutation->states->toCollection()->fetchPairs(null, 'id')]);
			}

			$this->template->results = $result;
		}
		if ($this->isAjax()) {
			$this->setLayout(false);
		}
	}

	public function actionPaymentMethod(?int $mutationId = null): void
	{
		$search = $this->getSearch();
		$selectedSiblingsIds = $this->getSelectedSiblingsIds();

		if ($search !== '') {
			$this->template->results = $this->orm->paymentMethod->findBy(['mutation->id' => $mutationId, 'name~' => LikeExpression::contains($search)]);
		}

		if ($this->isAjax()) {
			$this->setLayout(false);
		}
	}

	public function actionTags(?int $mutationId = null): void
	{
		$search = $this->getSearch();
		$selectedSiblingsIds = $this->getSelectedSiblingsIds();

		if ($search === '') {
			return;
		}

		$where = [
			'mutation->id' => $mutationId,
			'name~' => LikeExpression::contains($search),
		];
		if ($selectedSiblingsIds !== []) {
			$where['tag->id!='] = $selectedSiblingsIds;
		}
		$this->template->results = $this->orm->tagLocalization->findBy($where);
	}

	public function actionCalendarTag(): void
	{
		$this->commonSearch($this->orm->calendarTag);
	}

	public function actionParameter(string $types = '', bool $onlyForFilter = false, bool $onlyForDetail = false): void
	{
		$search = $this->getSearch();
		$selectedSiblingsIds = $this->getSelectedSiblingsIds();

		if ($search !== '') {
			$parameters = $this->orm->parameter->searchByName($search, $selectedSiblingsIds);
			if ($types) {
				$types = explode(',', $types);
				$parameters = $parameters->findBy([
					'type' => $types,
				]);
			}

			if ($onlyForFilter) {
				$parameters = $parameters->findBy([
					'isInFilter' => 1,
					'isLockedForES' => 0,
				]);
			}

			if($onlyForDetail){
				$parameters = $parameters->findBy([
					'isInDetail' => 1,
				]);
			}

			$this->template->parameters = $parameters;
		}

		if ($this->isAjax()) {
			$this->setLayout(false);
		}
	}


	public function actionSeoLinkParameterValues(): void
	{
		$searchQuery = $this->getSearch();
		$selectedSiblingsIds = $this->getSelectedSiblingsIds();

		if ($searchQuery !== '') {
			$allowedParamUids = $this->configService->get('seoLink', 'paramsList');
			$conditions = [
				'internalValue~' => LikeExpression::contains($searchQuery),
				'parameter->uid' => $allowedParamUids,
			];

			if ($selectedSiblingsIds !== []) {
				$conditions['id!='] = $selectedSiblingsIds;
			}

			$parameterValues = $this->orm->parameterValue->findBy($conditions);
			$this->template->parameterValues = $parameterValues;
		}

		if ($this->isAjax()) {
			$this->setLayout(false);
		}
	}

	public function actionParameterValues(int $parameterId, ?int $productId = null): void
	{
		$parameter = $this->orm->parameter->getById($parameterId);
		$product = $this->orm->product->getById($productId);
		$values = [];

		foreach ($parameter->options->toCollection() as $parameterValue) {
			$row = ['value' => $parameterValue->id, 'label' => $parameterValue->internalValue];
			if ($productId !== null && ($product->getParameterById($parameterId) !== null) && $product->hasParameterValue($parameterValue)) {
				$row['selected'] = true;
			}
			$values[] = $row;
		}

		$this->sendJson($values);
	}

	public function actionBrand(int $parameterId, ?int $productId = null): void
	{
		$brand = $this->orm->brand->getById($parameterId);
		$product = $this->orm->product->getById($productId);
		$values = [];

		foreach ($brand->localizations->toCollection() as $brandLocalization) {
			$row = ['value' => $brandLocalization->id, 'label' => $brandLocalization->name];
			if ($productId !== null && ($product->getParameterById($parameterId) !== null) && $product->hasParameterValue($brandLocalization->brandParameterValue)) {
				$row['selected'] = true;
			}
			$values[] = $row;
		}
	}

	private function parseSiblingsIds(string $selectedSiblingsIds): array
	{
		if ($selectedSiblingsIds) {
			return explode('|', $selectedSiblingsIds);
		} else {
			return [];
		}
	}


	private function getSearch(): string
	{
		$search = trim($this->request->getPost('search') ?? '');
		if ($search === '') {
			$search = trim($this->request->getParameter('search') ?? '');
		}

		return $search;
	}


	private function getSelectedSiblingsIds(): array
	{
		$selectedSiblingsIds = trim($this->request->getPost('selectedSiblingsIds') ?? '');
		$selectedSiblingsIds = $this->parseSiblingsIds($selectedSiblingsIds);
		return $selectedSiblingsIds;
	}


	private function commonSearch(Searchable $repository, ?Mutation $mutation = null): void
	{
		$search = $this->getSearch();
		$selectedSiblingsIds = $this->getSelectedSiblingsIds();

		$results = new EmptyCollection();
		if ($search !== '') {

			$results = $repository->searchByName($search, $selectedSiblingsIds);

			if ($mutation !== null) {
				$results = $results->findBy(['mutation' => $mutation]);
			}
		}

		$this->template->results = $results;
		$this->template->setFile(__DIR__ . '/templates/common-result.latte');
	}


	protected function beforeRender(): void
	{
		if ($this->isAjax()) {
			$this->setLayout(false);
		}

		parent::beforeRender();
	}

	private function getMutation(): ?Mutation
	{
		$mutationId = $this->request->getParameter('mutationId');
		if ($mutationId !== null) {
			return $this->orm->mutation->getByIdChecked($mutationId);
		}

		return null;
	}

	public function actionUsps(?int $mutationId = null): void
	{
		$search = $this->getSearch();
		$selectedSiblingsIds = $this->getSelectedSiblingsIds();

		if ($search === '') {
			return;
		}

		$where = [
			'mutation->id' => $mutationId,
			'name~' => LikeExpression::contains($search),
		];

		$this->template->results = $this->orm->usp->findBy($where);
	}
}
