<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Order\Components\OrderForm;

use App\Model\Erp\ErpOrderService;
use App\Model\Orm\Order\Delivery\LegacyDeliveryInformation;
use App\Model\Orm\Order\Delivery\OnlineDeliveryInformation;
use App\Model\Orm\Order\Delivery\PhysicalDeliveryInformation;
use App\Model\Orm\Order\Delivery\PickupDeliveryInformation;
use App\Model\Orm\Order\Delivery\StoreDeliveryInformation;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Orm;
use App\Model\Orm\User\User;
use Nette\Application\UI\Form;
use Nette\Utils\ArrayHash;

final readonly class OrderFormSuccess
{

	public function __construct(
		private Orm $orm,
		private ErpOrderService $erpOrderService,
	)
	{
	}


	public function execute(Form $form, Order $order, User $user, ArrayHash $values): void
	{
		$this->handleOrderCustomer($order,$values->orderCustomer);
		$this->handleOrderDeliveryInformation($order, $values->orderDeliveryInformation);
		$this->orm->order->persistAndFlush($order);

		$this->erpOrderService->updateOrder($order);
	}

	private function handleOrderCustomer(Order $order, ArrayHash $orderCustomerFormData): void
	{
		$order->name = $orderCustomerFormData->name;
		$order->phone = $orderCustomerFormData->phone;
		$order->email = $orderCustomerFormData->email;
		$order->street = $orderCustomerFormData->street;
		$order->city = $orderCustomerFormData->city;
		$order->zip = $orderCustomerFormData->zip;
		$order->country = $this->orm->state->getById((int)$orderCustomerFormData->country);
		$order->companyName = $orderCustomerFormData->companyName;
		$order->companyIdentifier = $orderCustomerFormData->companyIdentifier;
	}

	private function handleOrderDeliveryInformation(Order $order, ArrayHash $orderDeliveryInformationFormData): void
	{
		$deliveryType = $order->delivery->information->getType();
		$deliveryInformation = $order->delivery->information;
		if ($deliveryInformation instanceof PhysicalDeliveryInformation || $deliveryInformation instanceof LegacyDeliveryInformation) {
			$deliveryInformation->company = $orderDeliveryInformationFormData->company;
			$deliveryInformation->name = $orderDeliveryInformationFormData->name;
			$deliveryInformation->street = $orderDeliveryInformationFormData->street;
			$deliveryInformation->city = $orderDeliveryInformationFormData->city;
			$deliveryInformation->zip = $orderDeliveryInformationFormData->zip;
			$deliveryInformation->country = $this->orm->state->getById((int)$orderDeliveryInformationFormData->country);
			$deliveryInformation->phoneNumber = $orderDeliveryInformationFormData->phoneNumber;
			$deliveryInformation->trackingCode = $orderDeliveryInformationFormData->trackingCode;
		} elseif ($deliveryInformation instanceof PickupDeliveryInformation) {
			$deliveryInformation->phoneNumber = $orderDeliveryInformationFormData->phoneNumber;
			$deliveryInformation->trackingCode = $orderDeliveryInformationFormData->trackingCode;
			$deliveryInformation->pickupPointId = $orderDeliveryInformationFormData->pickupPointId;
		} elseif ($deliveryInformation instanceof StoreDeliveryInformation || $deliveryInformation instanceof OnlineDeliveryInformation) {
			$deliveryInformation->phoneNumber = $orderDeliveryInformationFormData->phoneNumber;
		}
		if ($deliveryInformation instanceof LegacyDeliveryInformation) {
			$deliveryInformation->pickupPointId = $orderDeliveryInformationFormData->pickupPointId;
		}
	}
}
