{var $props = [
	title: 'Doručova<PERSON>í údaje',
	id: 'deliveryInformation',
	icon: $templates.'/part/icons/align-left.svg',
	variant: 'main',
	open: true,
	classes: ['u-mb-xxs'],
	rowMain: false
]}

{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
	{block content}
			{ifset $form['orderDeliveryInformation']['name']}
				{include $templates.'/part/core/inp.latte' props: [
					input: $form['orderDeliveryInformation']['name'],
					classesLabel: ['title']
				]}
			{/ifset}
			{ifset $form['orderDeliveryInformation']['company']}
				{include $templates.'/part/core/inp.latte' props: [
					input: $form['orderDeliveryInformation']['company'],
					classesLabel: ['title']
				]}
			{/ifset}
			{ifset $form['orderDeliveryInformation']['phoneNumber']}
				{include $templates.'/part/core/inp.latte' props: [
					input: $form['orderDeliveryInformation']['phoneNumber'],
					classesLabel: ['title']
				]}
			{/ifset}
			{ifset $form['orderDeliveryInformation']['street']}
				{include $templates.'/part/core/inp.latte' props: [
					input: $form['orderDeliveryInformation']['street'],
					classesLabel: ['title']
				]}
			{/ifset}
			{ifset $form['orderDeliveryInformation']['city']}
				{include $templates.'/part/core/inp.latte' props: [
					input: $form['orderDeliveryInformation']['city'],
					classesLabel: ['title']
				]}
			{/ifset}
			{ifset $form['orderDeliveryInformation']['zip']}
				{include $templates.'/part/core/inp.latte' props: [
					input: $form['orderDeliveryInformation']['zip'],
					classesLabel: ['title']
				]}
			{/ifset}
			{ifset $form['orderDeliveryInformation']['country']}
		 		{include $templates.'/part/core/inp.latte' props: [
					input: $form['orderDeliveryInformation']['country'],
					type: 'select',
					classesLabel: ['title']
				]}
			{/ifset}
			{ifset $form['orderDeliveryInformation']['trackingCode']}
		 		{include $templates.'/part/core/inp.latte' props: [
					input: $form['orderDeliveryInformation']['trackingCode'],
					classesLabel: ['title']
				]}
			{/ifset}

			{ifset $form['orderDeliveryInformation']['pickupPointId']}
		 		{include $templates.'/part/core/inp.latte' props: [
					input: $form['orderDeliveryInformation']['pickupPointId'],
					classesLabel: ['title']
				]}
			{/ifset}

			{ifset $form['send']}
				<div class="grid grid--left grid--x-xs">
					<div class="grid__cell size--auto">
						<button n:name="send" class="btn btn--success">
							<span class="btn__text item-icon">
								<span class="item-icon__icon icon">
									{include $templates.'/part/icons/save.svg'}
								</span>
								<span class="item-icon__text">
									{_'save_button'}
								</span>
							</span>
						</button>
					</div>
				</div>
				{/ifset}
	{/block}
{/embed}



