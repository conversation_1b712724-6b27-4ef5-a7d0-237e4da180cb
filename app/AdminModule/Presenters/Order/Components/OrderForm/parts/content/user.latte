{var $props = [
	title: '<PERSON><PERSON><PERSON>n<PERSON>',
	id: 'user',
	icon: $templates.'/part/icons/align-left.svg',
	variant: 'main',
	open: true,
	classes: ['u-mb-xxs'],
	rowMain: false
]}

{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
	{block content}

				{include $templates.'/part/core/inp.latte' props: [
					input: $form['orderCustomer']['name'],
					classesLabel: ['title']
				]}

				{include $templates.'/part/core/inp.latte' props: [
					input: $form['orderCustomer']['email'],
					classesLabel: ['title']
				]}

				{include $templates.'/part/core/inp.latte' props: [
					input: $form['orderCustomer']['phone'],
					classesLabel: ['title']
				]}

				{include $templates.'/part/core/inp.latte' props: [
					input: $form['orderCustomer']['street'],
					classesLabel: ['title']
				]}

				{include $templates.'/part/core/inp.latte' props: [
					input: $form['orderCustomer']['city'],
					classesLabel: ['title']
				]}

				{include $templates.'/part/core/inp.latte' props: [
					input: $form['orderCustomer']['zip'],
					classesLabel: ['title']
				]}

		 		{include $templates.'/part/core/inp.latte' props: [
					input: $form['orderCustomer']['country'],
					type: 'select',
					classesLabel: ['title']
				]}

				{include $templates.'/part/core/inp.latte' props: [
					input: $form['orderCustomer']['companyName'],
					classesLabel: ['title']
				]}

				{include $templates.'/part/core/inp.latte' props: [
					input: $form['orderCustomer']['companyIdentifier'],
					classesLabel: ['title']
				]}

				{include $templates.'/part/core/inp.latte' props: [
					input: $form['orderCustomer']['vatNumber'],
					classesLabel: ['title']
				]}

				{include $templates.'/part/core/inp.latte' props: [
					input: $form['orderCustomer']['note'],
					type: 'textarea',
					classesLabel: ['title']
				]}
				{ifset $form['send']}
				<div class="grid grid--left grid--x-xs">
					<div class="grid__cell size--auto">
						<button n:name="send" class="btn btn--success">
							<span class="btn__text item-icon">
								<span class="item-icon__icon icon">
									{include $templates.'/part/icons/save.svg'}
								</span>
								<span class="item-icon__text">
									{_'save_button'}
								</span>
							</span>
						</button>
					</div>
				</div>
				{/ifset}
	{/block}
{/embed}



