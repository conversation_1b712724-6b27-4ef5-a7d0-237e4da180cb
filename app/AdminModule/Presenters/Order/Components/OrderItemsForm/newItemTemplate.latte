{ifset $form['items']['newItemMarker']}
<div class="u-hide">
{var $productContainer = $form['items']['newItemMarker']}
	<div data-Templates-target="orderItemItem" data-mutationid="{$order->mutation->id}">
		{var $url = $urls['searchProductVariant']->toArray()}
		{php $url['params']['mutationId'] = $order->mutation->id}

		{include $templates.'/part/box/list-item.latte',
			props: [
				inps: [
					[
						input: $productContainer['name'],
						placeholder: 'Zadejte název produktu',
						data: [
							suggestinp-target: 'input',
						]
					],
					[
						input: $productContainer['amount'],
						placeholder: 'Zadejte počet kusů',
						data: [
							suggestinp-target: 'input',
						]
					],
					[
						input: $productContainer['id'],
						data: [
							suggestinp-target: 'idInput',
						],
						classes: 'u-hide',
						type: 'hidden'
					]
				],
				btnsAfter: [
					[
						icon: $templates.'/part/icons/trash.svg',
						tooltip: 'Odstranit',
						variant: 'remove',
						data: [
							action: 'RemoveItem#remove'
						],
					]
				],
				data: [
					controller: 'RemoveItem SuggestInp',
					removeitem-target: 'item',
					suggestinp-target: 'wrapper',
					suggestinp-url-value: Nette\Utils\Json::encode($url)
				],
				dragdrop: false
			]
		}
	</div>
</div>
{/ifset}

