<?php

declare(strict_types=1);

namespace App\AdminModule\Presenters\Order\Components\OrderItemsForm;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\Model\ConfigService;
use App\Model\Currency\CurrencyHelper;
use App\Model\CustomField\SuggestUrls;
use App\Model\Erp\ErpOrderService;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderModel;
use App\Model\Orm\Orm;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Stock\Stock;
use App\Model\Orm\User\User;
use App\Model\Translator;
use App\Model\TranslatorDB;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Http\IRequest;
use Nette\Utils\ArrayHash;

/**
 * @property-read DefaultTemplate $template
 */
final class OrderItemsForm extends Control
{
	private mixed $method;

	private array $postData = [];


	public function __construct(
		private Order $order,
		private readonly User $userEntity,
		private readonly SuggestUrls $urls,
		private readonly Orm $orm,
		private readonly Translator $translator,
		private readonly TranslatorDB $translatorDB,
		private readonly OrderItemsFormBuilder $orderItemsFormBuilder,
		private readonly OrderItemsFormSuccess $orderItemsFormSuccess,
		private readonly ConfigService $configService,
		private readonly MessageForFormFactory $messageForFormFactory,
		private readonly ErpOrderService $erpOrderService,
	)
	{
		$this->onAnchor[] = $this->init(...);
	}

	private function init(): void
	{
		$this->method = $this->getPresenter()->request->getMethod();
		if ($this->method === IRequest::Post) {
			$this->postData = $this->getPresenter()->request->getPost();
		}
	}


	public function render(): void
	{
		$this->template->object = $this->order;
		$this->template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$this->template->add('templates', RS_TEMPLATE_DIR);

		$this->template->order = $this->order;
		$this->template->priceLevels = $this->orm->priceLevel->findBy(['type' => [PriceLevel::TYPE_PURCHASE, PriceLevel::TYPE_RECOMMENDED]]);
		$this->template->currencies = CurrencyHelper::CURRENCIES;
		$this->template->stocks = $this->orm->stock->findBy(['alias' => Stock::ALIAS_SHOP]);
		$this->template->orm = $this->orm;
		$this->template->translatorDB = $this->translatorDB;
		$this->template->config = $this->configService->getParams();
		$this->template->userEntity = $this->userEntity;
		$this->template->defaultMutation = $this->orm->mutation->getDefault();
		$this->template->setTranslator($this->translator);
		$this->template->urls = $this->urls;
		$this->template->disableEdit = !$this->order->isActionAllowed(Order::ORDER_ACTION_ITEMS_EDIT);
		$this->template->render(__DIR__ . '/orderItemsForm.latte');

	}

	protected function createComponentForm(): Form
	{
		$form = new Form();

		$form->setTranslator($this->translator);

		$this->orderItemsFormBuilder->build($form, $this->order, $this->postData);

		$form->onValidate[] = $this->formValidate(...);
		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];

		return $form;
	}

	private function formValidate(Form $form, ArrayHash $values): void
	{
		$ret = $this->erpOrderService->syncStatus($this->order);
		if (!$ret) {
			$form->addError($this->erpOrderService->getLastMessage()->text);
		} else {
			//load updated order
			$this->order = $this->orm->order->getById($this->order->id);
		}

		if (!$this->order->isActionAllowed(Order::ORDER_ACTION_ITEMS_EDIT)) {
			$form->addError('order_edit_not_allowed');
		}

	}

	public function formError(Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSucceeded(Form $form, ArrayHash $values): void
	{

		try {
			$this->orderItemsFormSuccess->execute($form, $this->order, $this->userEntity, $values);
		} catch (\LogicException $e) {
			$this->flashMessage($e->getMessage(), 'error');
		}

		$this->presenter->redirect('detail', ['id' => $this->order->id]);
	}

	public function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

}
