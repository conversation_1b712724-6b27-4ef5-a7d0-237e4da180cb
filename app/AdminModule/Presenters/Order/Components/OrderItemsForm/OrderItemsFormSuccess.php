<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Order\Components\OrderItemsForm;

use App\Model\Orm\Order\Product\ProductItem;
use LogicException;
use App\Model\Erp\ErpOrderService;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderItem;
use App\Model\Orm\Orm;
use App\Model\Orm\Price;
use App\Model\Orm\User\User;
use App\Model\VatCalculator;
use Brick\Math\BigDecimal;
use Brick\Money\Money;
use Nette\Application\UI\Form;
use Nette\Utils\ArrayHash;

final readonly class OrderItemsFormSuccess
{

	public function __construct(
		private Orm $orm,
		private ErpOrderService $erpOrderService,
	)
	{
	}

	public function execute(Form $form, Order $order, User $user, ArrayHash $values): void
	{
		$attachedItems = $order->getBuyableItems();
		$attachedItemsIds = [];
		foreach ($attachedItems as $row) {
			assert($row instanceof ProductItem);
			$attachedItemsIds[$row->id] = $row;
		}

		foreach ($values->items as $id => $itemFormData) {
			assert($itemFormData instanceof ArrayHash);
			if ($id === 'newItemMarker') {
				continue;
			}
			if (is_int($id) && !empty($itemFormData->id)) {
				$attachedOrderProduct = $this->orm->orderProduct->getById($itemFormData->id);
				if ($attachedOrderProduct) {
					unset($attachedItemsIds[$attachedOrderProduct->id]);
					$attachedOrderProduct->amount = (int)$itemFormData->amount;

					$currency = $order->currency->getCurrencyCode();
					$unitPrice = (float)$itemFormData->unitPrice;
					$unitPriceVat = (float)$itemFormData->unitPriceVat;
					if (!empty($unitPriceVat)) {
						$vat = $attachedOrderProduct->getVatRateValue()->toFloat();
						$unitPrice = VatCalculator::priceWithoutVat(Money::of($unitPriceVat, $currency), BigDecimal::of($vat))->getAmount()->toFloat();
					}
					if (empty($unitPrice)) {
						throw new LogicException('no_price_provided');
					}
					$attachedOrderProduct->unitPrice = Price::from(Money::of($unitPrice, $currency));;

					$this->orm->persistAndFlush($attachedOrderProduct);
				}
			} else {
				if ((int)$itemFormData->amount === 0) {
					throw new LogicException('amount_not_provided');
				}
				$productVariant = $this->orm->productVariant->getById($itemFormData->id);
				//todo add product with custom price
				if ($productVariant === null) {
					throw new LogicException('product_variant_not_found');
				}
				$addedAmount = $order->addProduct($productVariant, (int)$itemFormData->amount);
				if ($addedAmount !== (int)$itemFormData->amount) {
					//throw new LogicException('amount_not_fully_filled');
				}
				$this->orm->persistAndFlush($order);
			}
		}

		foreach ($attachedItemsIds as $attachedItemsId => $attachedOrderProduct) {
			$attachedOrderProduct = $this->orm->orderProduct->getById($attachedItemsId);
			$this->orm->orderProduct->removeAndFlush($attachedOrderProduct);
		}

		// TODO do this? $order->refresh(onlyProducts: true);
		$this->orm->persistAndFlush($order);

		$this->erpOrderService->updateOrder($order);
	}
}
