<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Order\Components\OrderItemsForm;

use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\Product\ProductItem;
use Nette\Application\UI\Form;
use Nextras\Orm\Collection\ICollection;

final class OrderItemsFormBuilder
{


	private bool $disableEdit = true;

	public function build(Form $form, Order $order, array $postData): void
	{
		$this->disableEdit = !$order->isActionAllowed(Order::ORDER_ACTION_ITEMS_EDIT);

		$this->addOrderItems($form, $order->products->toCollection(), $postData);
		$this->addButtonsToForm($form);
	}

	private function addButtonsToForm(Form $form): void {
		if ($this->disableEdit) {
			return;
		}
		$form->addSubmit('send');
	}

	/**
	 * @param ICollection<ProductItem> $collection
	 */
	private function addOrderItems(Form $form, ICollection $collection, array $postData): void
	{
		$itemsContainer = $form->addContainer('items');

		if ($postData) {
			if (isset($postData['items'])) {
				foreach ($postData['items'] as $itemKey => $item) {
					$itemContainer = $itemsContainer->addContainer($itemKey);
					$itemContainer->addHidden('id');
					$itemContainer->addText('name');
					$itemContainer->addText('amount');
					$itemContainer->addText('unitPrice');
					$itemContainer->addText('unitPriceVat');
				}
			}
		} else {
			foreach ($collection as $item) {
				assert($item instanceof ProductItem);
				$itemContainer = $itemsContainer->addContainer($item->id);
				$itemContainer->addHidden('id')
					->setDisabled($this->disableEdit)
					->setDefaultValue($item->id);
				$itemContainer->addText('name')
					->setDisabled($this->disableEdit)
					->setDefaultValue($item->variantName);
				$itemContainer->addText('amount')
					->setDisabled($this->disableEdit)
					->setDefaultValue($item->amount);
				$itemContainer->addText('unitPrice')
					->setDisabled($this->disableEdit)
					->setDefaultValue($item->unitPrice->asMoney()->getAmount()->toFloat());
				$itemContainer->addText('unitPriceVat')
					->setDisabled($this->disableEdit);
			}
		}

		if (!$this->disableEdit) {
			//fake
			$itemContainer = $itemsContainer->addContainer('newItemMarker');
			$itemContainer->addHidden('id');
			$itemContainer->addText('name');
			$itemContainer->addText('amount');
			//$itemContainer->addText('unitPrice');
			//$itemContainer->addText('unitPriceVat');
		}
	}

}
