{varType App\Model\Orm\Product\Product $object}

<form n:name="form">
	{control messageForForm, $flashes, $form}
	{var $props = [
		title: 'Storno',
		id: 'orderCancel',
		icon: $templates.'/part/icons/align-left.svg',
		variant: 'main',
		open: true,
		classes: ['u-mb-xxs'],
		rowMain: false
	]}

	{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
		{block content}

					{include $templates.'/part/core/inp.latte' props: [
						input: $form['cancelReason'],
						type: 'select',
						classesLabel: ['title']
					]}

					<div class="grid grid--left grid--x-xs">
						<div class="grid__cell size--auto">
							<button n:name="cancel" class="btn btn--success">
								<span class="btn__text item-icon">
									<span class="item-icon__icon icon">
										{include $templates.'/part/icons/trash.svg'}
									</span>
									<span class="item-icon__text">
										{_'cancel_button'}
									</span>
								</span>
							</button>
						</div>
					</div>
		{/block}
	{/embed}
</form>
