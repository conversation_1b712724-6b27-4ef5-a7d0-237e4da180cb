<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Library\Components\LibraryForm;

use App\AdminModule\Presenters\Library\Components\LibraryForm\FormData\BaseFormData;
use App\Model\Orm\LibraryTree\LibraryTree;
use App\Model\Orm\LibraryTree\LibraryTreeRepository;
use App\Model\Translator;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;

final class LibraryForm extends Control
{

	public function __construct(
		private readonly LibraryTree $libraryTree,
		private readonly LibraryTreeRepository $libraryTreeRepository,
		private readonly Translator $translator,
		private readonly string $rsTemplatesPath,
	)
	{
	}


	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->templates = $this->rsTemplatesPath;
		$this->template->canDelete = $this->libraryTree->parent !== null || $this->libraryTreeRepository->findBy(['parent' => null])->countStored() > 1;
		$this->template->render(__DIR__ . '/libraryForm.latte');
	}


	protected function createComponentForm(): Form
	{
		$form = new Form();

		$form->setTranslator($this->translator);

		$form->addText('name', 'name')
			->setRequired(true)
			->addFilter(function ($value) {
				return trim($value);
			})->setDefaultValue($this->libraryTree->name);


		$form->addSubmit('save', 'save_button');

		$form->onSuccess[] = $this->formSucceeded(...);
		return $form;
	}


	public function formSucceeded(Form $form, BaseFormData $baseFormData): void
	{
		$this->libraryTree->name = $baseFormData->name;
		$this->libraryTreeRepository->persistAndFlush($this->libraryTree);

		$this->presenter->redirect('default', ['id' => $this->libraryTree->id]);
	}


	public function handleDelete(): void
	{
		if ($this->libraryTree->parent == null) {
			$futureTarget = $this->libraryTreeRepository->getBy([
				'parent' => null,
				'id!=' => $this->libraryTree->id
			]);
		} else {
			$futureTarget = $this->libraryTree->parent;
		}

		if ($futureTarget !== null) {
			$this->libraryTreeRepository->removeAndFlush($this->libraryTree);
		} else {
			$this->flashMessage('cant_delete_last_directory');
			$futureTarget = $this->libraryTree;
		}

		$this->presenter->redirect('default', ['id' => $futureTarget->id]);

	}

}
