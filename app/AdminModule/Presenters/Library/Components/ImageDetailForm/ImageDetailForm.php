<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Library\Components\ImageDetailForm;

use App\AdminModule\Presenters\Library\Components\ImageDetailForm\FormData\BaseFormData;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\LibraryImage\LibraryImage;
use App\Model\Orm\LibraryImage\LibraryImageRepository;
use App\Model\Translator;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;

final class ImageDetailForm extends Control
{

	public function __construct(
		private readonly LibraryImage $libraryImage,
		private readonly string $rsTemplatesPath,
		private readonly LibraryImageRepository $libraryImageRepository,
		private readonly Translator $translator,
		private readonly MutationsHolder $mutationsHolder,
	)
	{
	}


	public function render(): void
	{
		$this->template->setTranslator($this->translator);

		$this->template->add('libraryImage', $this->libraryImage);
		$this->template->add('templates', $this->rsTemplatesPath);
		$this->template->add('mutations', $this->findMutations());
		$this->template->render(__DIR__ . '/imageDetailForm.latte');
	}


	protected function createComponentForm(): Form
	{
		$form = new Form();

		$form->setTranslator($this->translator);

		$form->addText('name', 'name')
			->setRequired()
			->setDefaultValue($this->libraryImage->name);

		$altsContainer = $form->addContainer('alts');
		foreach ($this->findMutations() as $mutation) {
			$altsContainer->addText((string)$mutation->id, $mutation->langCode)->setDefaultValue($this->libraryImage->getAlt($mutation));
		}

		$form->addSubmit('save', 'save_button');

		$form->onError[] = $this->onError(...);
		$form->onSuccess[] = $this->formSucceeded(...);
		return $form;
	}

	public function onError(Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->presenter->redrawControl('imageMainForm');
		}
	}


	public function formSucceeded(Form $form, BaseFormData $baseFormData): void
	{


		$this->libraryImage->name = $baseFormData->name;
		$this->libraryImageRepository->persistAndFlush($this->libraryImage);
		foreach ($baseFormData->alts as $mutationId=>$alt) {

			try {
				$mutation = $this->mutationsHolder->getMutationById($mutationId);
			} catch (\LogicException){
				$mutation = null;
			}
			if ($mutation !== null) {
				if ($this->libraryImage->getAlt($mutation) !== $alt->alt) {
					$this->libraryImage->setAlt($mutation, $alt->alt);
				}
			}
		}
		$this->flashMessage($this->translator->translate('imageSaved'), 'success');
		$this->libraryImageRepository->persistAndFlush($this->libraryImage);
		if ($this->presenter->isAjax()) {
			$this->presenter->redrawControl('imageMainForm');
		} else {
			$this->presenter->redirect('this');
		}
	}

	/**
	 * @return \App\Model\Orm\Mutation\Mutation[]
	 */
	public function findMutations(): array
	{
		return $this->mutationsHolder->findAll(false);
	}

}
