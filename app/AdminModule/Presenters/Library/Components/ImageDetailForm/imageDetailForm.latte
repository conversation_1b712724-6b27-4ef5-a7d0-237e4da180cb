<form n:name="form" class="form" data-naja="" data-naja-history="off">
	{foreach $flashes as $flash}
		<div class="message message-{$flash->type}">{$flash->message}</div><br>
	{/foreach}





		<div class="row-main">
			<div class="grid grid--y-0">
				<div class="grid__cell size--4-12">
					<img src="{$libraryImage->getSize('library')->src}" alt="">
				</div>
				<div class="grid__cell size--8-12">
					{include $templates.'/part/core/inp.latte' props: [
					input: $form['name'],
					classesLabel: ['title']
					]}

					{if count($mutations) > 0}
						<h3>{translate}image_alts{/translate}</h3>
						{foreach $mutations as $mutation}
							{include $templates.'/part/core/inp.latte' props: [
							input: $form['alts'][$mutation->id],
							classesLabel: ['title']
							]}
						{/foreach}
					{/if}
				</div>
			</div>
		 </div>





	<p>
		<button n:name="save" class="btn btn--full btn--success">
		<span class="btn__text item-icon">
			<span class="item-icon__icon icon">
				{include $templates.'/part/icons/save.svg'}
			</span>
			<span class="item-icon__text">
				Uložit
			</span>
		</span>
		</button>
	</p>
</form>
