<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Library\Components\ImageDetailForm\FormData;

final class BaseFormData
{

	/**
	 * @var ImageAlt[]
	 */
	public readonly array $alts;

	public function __construct(
		public readonly string $name,
		array $alts,
	)
	{
		$altsObjects = [];
		foreach ($alts as $mutationId => $alt) {
			$altsObjects[$mutationId] = new ImageAlt($alt);
		}

		$this->alts = $altsObjects;
	}
}
