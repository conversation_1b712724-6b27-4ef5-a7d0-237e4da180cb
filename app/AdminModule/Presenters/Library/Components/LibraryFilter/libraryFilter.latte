<form n:name="form" class="form-filter"
	data-naja=""
	data-naja-history="off"
>
	<div class="b-library__filter grid grid--middle u-mb-sm">
		<div class="grid__cell size--autogrow">
			<span class="inp-fix">
				{input fulltext class => 'inp-text', placeholder => 'Zadejte výraz pro filtrování obrázků'}
				<div class="inp-text__holder"></div>
			</span>
		</div>

		<div class="b-library__filter-global grid__cell size--auto">
			<span class="inp-item inp-item--checkbox">
				<input n:name="directorySearch" class="inp-item__inp"/>
				<label n:name="directorySearch" class="inp-item__text"/>
			</span>
		</div>

		<div class="grid__cell size--auto">
			<button class="btn btn--success" type="submit">
				<span class="btn__text item-icon">
					<span class="item-icon__icon icon">
						{include $templates.'/part/icons/search.svg'}
					</span>
					<span class="item-icon__text">
						{_filter_button}
					</span>
				</span>
			</button>

			{if $showReset}
				<a n:href="clearFilter!" class="btn btn--grey"
					data-naja
					data-naja-history="off"
				>
					<span class="btn__text item-icon">
						<span class="item-icon__icon icon">
							{include $templates.'/part/icons/trash.svg'}
						</span>
						<span class="item-icon__text">
							{_filter_cancel_button}
						</span>
					</span>
				</a>
			{/if}
		</div>
	</div>
</form>
