<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Library\Components\LibraryFilter;

use App\AdminModule\Presenters\Library\LibraryPresenter;
use App\Model\Translator;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nette\Http\Session;
use Nette\Http\SessionSection;
use Nette\Utils\ArrayHash;

/** @property-read LibraryPresenter $presenter */
final class LibraryFilter extends Control
{

	public const LIBRARY_FILTER_SESSION_NAME = 'libraryFilter';

	private SessionSection $filterSession;

	public function __construct(
		private Translator $translator,
		Session $session,
	)
	{
		$this->filterSession = $session->getSection(self::LIBRARY_FILTER_SESSION_NAME);
	}


	public function render(): void
	{
		$this->template->data = $this->filterSession->get('data');
		$this->template->setTranslator($this->translator);

		$this->template->templates = RS_TEMPLATE_DIR;
		$this->template->filterData = $this->filterSession->data;
		$this->template->showReset = (isset($this->filterSession->data['fulltext']));
		$this->template->render(__DIR__ . '/libraryFilter.latte');
	}


	protected function createComponentForm(): Form
	{
		$form = new Form();

		$form->setTranslator($this->translator);

		$form->addText('fulltext', 'search_libs')
			->setRequired(true)
			->addFilter(function ($value) {
				return trim($value);
			});

		$form->addCheckbox('directorySearch', 'directorySearch')
			->setDefaultValue(isset($this->filterSession->data['directorySearch']) && $this->filterSession->data['directorySearch']);

		if (($data = $this->filterSession->get('data')) !== null) {
			$form->setDefaults($data);
		}

		$form->addSubmit('save', 'save_button');

		$form->onSuccess[] = [$this, 'filterFormSucceeded'];
		return $form;
	}


	public function filterFormSucceeded(Form $form, ArrayHash $values): void
	{
		if (isset($values->fulltext) && trim($values->fulltext))
		$this->filterSession->data = (array) $values;


		if ($this->presenter->isAjax()) {
			$this->presenter->forcedPage = 1;
			$this->presenter->redrawControl();
		} else {
			$this->presenter->redirect('this', ['forcedPage' => 1]);
		}
	}


	public function handleClearFilter(): void
	{
		$this->filterSession->data = [];


		if ($this->presenter->isAjax()) {
			$this->presenter->forcedPage = 1;
			$this->presenter->redrawControl();
		} else {
			$this->presenter->redirect('this', ['forcedPage' => 1]);
		}
	}

}
