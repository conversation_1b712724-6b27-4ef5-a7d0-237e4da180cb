
{if $thickbox} <div
		data-controller="Templates"
		data-action="ImageLibrary:newItem@window->Templates#newItem"
>
	{/if}

	<div class="u-hide">
		<div data-Templates-target="libraryImageItem">
			{include $templates.'/part/box/image-item.latte',
				props: [
					data: [
						controller: 'File',
						imagelibrary-target: 'img',
						action: 'click->ImageLibrary#selectImage Templates:uploadFile@window->File#upload',
						file-url-value: '{uploadUrl}',
						file-id-value: 'newItemMarker',
					],
					img: '{imageSrc}',
					name: '{imageName}'
				]
			}
		</div>
	</div>


	<div class="b-library block-loader" data-block-loader
		{if !$thickbox}
			data-controller="ImageLibrary"
			data-action="Tree:goToStart->ImageLibrary#startLoading Tree:goToEnd->ImageLibrary#goTo Tree:treeAdjustStart->ImageLibrary#startLoading Tree:treeAdjustEnd->ImageLibrary#stopLoading"
		{/if}
	>
		<div n:snippet="libraryTree">
			{control tree}
		</div>

		<div n:snippet="library">
			{if $thickbox === 1}{control libraryFilter}{/if}
			{if $object && $object->id}
				{if $images}
					{var $items = []}

					{foreach $images as $image}
						{varType App\Model\Image\ImageObjectFactory $imageObjectFactory}
						{php $img = $imageObjectFactory->getByName($image->filename, 'library', $image->getTimestamp())}

						{*										'naja-modal' =>'snippet--mainContant',*}

						{var $btns = []}
						{if !$thickbox && $hasItemActions}
							{var $btns[] =
								[
									classes: "",
									tooltip: "Popisky obrázku",
									icon: $templates.'/part/icons/list-alt.svg',
									data: [
										'naja' => '',
										'naja-overlay-selector' =>'imageDetail',
										'naja-history' =>'off',
										'naja-loader' => '[data-block-loader]'
									],
									link: [
										url: $presenter->link('imageOverlay', ['imageId' => $image->id]),
									]
								]
							}

							{var $btns[] =
								[
									classes: "",
									tooltip: "Stáhnout originál",
									icon: $templates.'/part/icons/download.svg',
									link: [
										url: $presenter->link('downloadOriginal!', ['imageId' => $image->id]),
									]
								]
							}

							{var $btns[] = [
									classes: "",
									variant: "remove",
									tooltip: "Odstranit",
									icon: $templates.'/part/icons/trash.svg',
									data: [
										naja: "",
										naja-history: "off",
										naja-loader: '[data-block-loader]'
									],
									link: [
										url: $presenter->link('deleteImage!', ['imageId' => $image->id]),
									],
									confirmMsg: "Opravdu chtete smazat tento obrázek?",
								]
							}




							{if $img->hasRotations()}
								{var $btns[] =
									[
										classes: "",
										tooltip: "Otočit do leva",
										icon: $templates.'/part/icons/reply.svg',
										link: [
											url: $presenter->link('rotation!', ['imageId' => $image->id, 'direction' => 'left']),
										]
									]
								}
								{var $btns[] =
									[
										classes: "",
										tooltip: "Otočit do prava",
										icon: $templates.'/part/icons/share.svg',
										link: [
											url: $presenter->link('rotation!', ['imageId' => $image->id, 'direction' => 'right']),
										]
									]
								}
							{/if}
						{/if}

						{var $item = [
							img: $img->src,
							data: [
								id: $image->id,
								src: $img->src,
								sort: $image->sort,
								imagelibrary-target: 'img',
								action: 'click->ImageLibrary#selectImage',
							],
							name: $image->name,
							btns: $btns
						]}
						{php $items[] = $item}
					{/foreach}

					{include $templates.'/part/box/imgs.latte',
						props: [
							add: $hasAdd,
							file: true,
							dragdrop: true,
							dataAdd: [
								url: '/superadmin/library/',
								folder-id: $object->id,
							],
							dataList: [
								imagelibrary-target: 'list',
								controller: 'libraryimagelist',
								libraryimagelist-url-value: $presenter->link('//this')
							],
							items: $items,
							classes: 'b-imgs--selectable u-mb-sm',
						]
					}
				{/if}

				<div class="paging">
					<p class="l">
						{control pager:adminNaja, showPages => "true"}
					</p>
					<p class="r">
						{control pager:adminNaja, class => "r", ajax => true}
					</p>
				</div>
			{/if}
		</div>
		<div class="block-loader__loader"></div>

	</div>


{if $thickbox} </div> {/if}
