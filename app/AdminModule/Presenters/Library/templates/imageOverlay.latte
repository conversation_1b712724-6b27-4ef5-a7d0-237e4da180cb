{layout $templates.'/@layout-new.latte'}
{varType App\Model\Orm\LibraryImage\LibraryImage $libraryImage}

{block #content}
<div class="main__main">
	<div class="main__header" n:snippet="header">
		{include $templates.'/part/box/header.latte',
			props: [
				title: $translator->translate('image') . ': ' . $libraryImage->name,
				isPageTitle: true,
			]
		}
	</div>
	<div class="main__content scroll"  n:snippet="imageMainForm">
		{control imageDetailForm}
	</div>
	<div class="main__content-side scroll">
	</div>
</div>
