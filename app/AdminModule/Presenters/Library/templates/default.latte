{layout $templates.'/@layout-new.latte'}
{var $dataGridShown = false}

{block #content}
	<div class="main__main">
		<div class="main__header" n:snippet="header">

			{if $thickbox === 1}

				{include $templates.'/part/box/header.latte',
					props: [
						title: $object->name,
						isPageTitle: true,
					]
				}
			{else}
				<div class="b-header"> {* b-header--menu*}
					<div class="b-header__content">
						<div class="grid grid--y-0" >
							<div class="grid__cell size--2-12">
								<h1 class="b-header__title">
									{$object->name}
								</h1>

							</div>
							<div class="grid__cell size--10-12">
								{control libraryFilter}
							</div>
						</div>
					</div>
				</div>
			{/if}


		</div>
		<div class="main__content scroll">
			{snippet content}
				{include 'part/content.latte'}
			{/snippet}



		</div>
		<div class="main__content-side scroll">
			{snippet side}
				{control libraryForm}
			{/snippet}

		</div>
	</div>

	{embed $templates . '/part/core/overlay.latte', props: [
		id: 'imageDetail',
		title: 'Nastavení obrázku',
		class: ['b-overlay--full'],
		], templates => $templates}

			{block content}
				{snippet imageMainForm}
				{/snippet}
			{/block}
	{/embed}

