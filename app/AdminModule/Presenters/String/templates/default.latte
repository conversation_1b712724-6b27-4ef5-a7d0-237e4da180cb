{layout $templates.'/@layout-new.latte'}
{var $dataGridShown = true}

{block #content}
	<div class="main__main main__main--noside main__main--nospacing main__main--nopadding main__main--valign-top">
		<div class="main__content scroll scroll--horizontal">
			<div class="main__header">
				{include $templates.'/part/box/header.latte',
					props: [
						titleTranslation: 'System strings',
					]
				}
			</div>
			{snippet flashes}
				<div n:foreach="$flashes as $flash" class="message message--{$flash->type}">{$flash->message}</div>
			{/snippet}

			<div data-controller="DataGrid" class="oversize-datagrid">
				{control stringGrid}
			</div>
		</div>
	</div>

	{embed $templates.'/part/core/overlay.latte', props: [
		id: 'import',
		title: 'Import překladů',
		classes: ['b-overlay--full']
	], templates=>$templates}
		{block content}
			{form importForm}
				<div class="inp u-mb-sm">
					{label file, class: "inp-label title" /}
					<div class="inp-fix">
						{input file, class: "inp-text"}
						<div class="inp-text__holder"></div>
					</div>
				</div>
				<label class="inp-item inp-item--checkbox u-mb-sm" n:name="updateOnly">
					<input class="inp-item__inp" type="checkbox" n:name="updateOnly">
					<span class="inp-item__text">{translate}update_only{/translate}</span>
				</label>
				<button class="btn btn--success" n:name="import">
					<span class="btn__text item-icon">{translate}import_button{/translate}</span>
				</button>
			{/form}
		{/block}
	{/embed}
{/block}
