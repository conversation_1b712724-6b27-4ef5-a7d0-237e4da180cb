<?php declare(strict_types = 1);

namespace App\FrontModule\Presenters;

use Nette\Application\Attributes\Persistent;

trait HasFilterTrait
{
	protected array $cleanFilterParam;

	#[Persistent]
	public array $filter = [];

	protected function setupCleanFilterParam(): void
	{
		$filterParams = $this->filter;

		$filterParams = array_filter($filterParams);
		foreach (['dials',] as $filterArrKey) {
			if (isset($filterParams[$filterArrKey])) {
				$filterParams[$filterArrKey] = (array)$filterParams[$filterArrKey];
				foreach ($filterParams[$filterArrKey] as &$dialValue) {
					$dialValue = array_filter($dialValue);
				}
			}
		}

		array_walk_recursive($filterParams, function (&$val): void {
			if (is_numeric($val)) {
				$val += 0;
			}
			if (is_string($val)) {
				$val = trim($val);
			}
		});

		$this->cleanFilterParam = $filterParams;
	}

	protected function getFilterParam(string $key, mixed $default = null): mixed
	{
		return $this->cleanFilterParam[$key] ?? $default;
	}

	protected function getFilterUrl(): string
	{
		return urldecode(htmlspecialchars_decode($this->link('//this', ['filter' => $this->cleanFilterParam])));
	}
}
