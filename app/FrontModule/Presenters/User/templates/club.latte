{block content}
	{varType App\Model\Pages $pages}
	<div class="b-club u-mb-last-0">
		{if $userEntity->isClubMember ?? false}
			{include $templates.'/part/box/club-info.latte', class: 'u-mb-sm u-mb-md@md', isUserInClub: $isUserInClub}
		{else}
			<div class="u-mb-last-0 u-mb-sm u-mb-md@md">
				{* <p class="b-club__msg message message--warning">
					{_"you_are_not_in_club_yet"}
				</p> *}

				<p n:if="$pages->userAddress !== null && !$userEntity->hasPhone" class="b-club__msg message message--warning">
					{_"user_set_your_phone"}
					<a href="{plink $pages->userAddress}" class="btn btn--bd btn--xs">
						<span class="btn__text">{_"user_set_your_phone_button"}</span>
					</a>
				</p>
				<p n:if="$pages->userAddress !== null && !Nette\Utils\Validators::isEmail($userEntity->email)" class="b-club__msg message message--warning">
					{_"user_set_your_email"}
					<a href="{plink $pages->userAddress}" class="btn btn--bd btn--xs">
						<span class="btn__text">{_"user_set_your_email_button"}</span>
					</a>
				</p>
				<p n:if="!$hasNewsletterSubscription" class="b-club__msg message message--warning">
					{_"user_set_subscribe_mailing"}
					<a n:href="subscribe!" class="btn btn--bd btn--xs">
						<span class="btn__text">{_"user_set_subscribe_mailing_button"}</span>
					</a>
				</p>
			</div>
		{/if}

		{if $object->cf->loaytyClubBonuses ?? false}
			<h2 n:if="$object->cf->loaytyClubBonuses??->title ?? false" class="u-mt-0 u-mb-xs">
				{$object->cf->loaytyClubBonuses->title}
			</h2>
			<blockquote n:ifcontent>
				{include $templates.'/part/box/content.latte', content: $object->cf->loaytyClubBonuses??->bonuses ?? false}
			</blockquote>
		{/if}
	</div>
{/block}
