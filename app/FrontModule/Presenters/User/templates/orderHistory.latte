{block content}

{if $user->loggedIn}
	{snippetArea userOrderHistoryArea}
		{include 'default.latte'}
	{/snippetArea}
{elseif $object->uid == 'userOrderHistory'}
	<div class="u-pt-sm">
		<div class="row-main row-main--md">
			{foreach $flashes as $flash}
				<div class="message message-{$flash->type}">{_$flash->message}</div>
			{/foreach}
			{* <div class="u-mb-xl">
				<div class="grid grid--lg">
					<div class="grid__cell size--side u-mb-last-0 u-no-print">
						{include '../part/box/ask.latte', class=>'u-hide u-show@lg u-mb-lg'}
					</div>
					<div class="grid__cell size--main u-mb-last-0"> *}
							{snippet orderHistoryDetail}
								{if $order}
									{control orderHistory:detail, $presenter->getParameter('orderHash')}
								{else}
									<div class="message message-error">{_'no_results_title'}</div>
								{/if}
							{/snippet}
					{* </div>
				</div>
			</div> *}
		</div>
	</div>
{/if}



