{block content}
<div class="b-layout b-layout--user">
	<div class="row-main">
		<div class="b-layout__inner">
			<div class="b-layout__bc">
				{control breadcrumb}
			</div>
			<div class="b-layout__nav" data-controller="toggle-class">
				{embed $templates.'/part/core/toggle.latte', class=>'u-fw-b'}
					{block text}
						{_"toggle_user_account_mobile"} {$object->nameAnchor}
					{/block}
				{/embed}
				<div class="b-layout__menu">
					{embed $templates.'/part/box/user.latte', class=>false, userEntity=>$userEntity, pages=>$pages}
						{block content}
							{control userSideMenu}
						{/block}
					{/embed}
				</div>
			</div>
			<div class="b-layout__main u-mb-last-0">
				{include $templates . '/part/box/annot.latte', class: 'u-mb-xxs u-mb-xs@md', name: $object->name}
				{include $templates . '/part/box/content.latte'}

				{default $class = false}

				<section n:class="b-library, $class, u-mb-last-0">
					{control myLibrary}
					{control boughtProducts}
				</section>
			</div>
		</div>
	</div>
</div>

