{block content}
	<h1 class="h3 u-mb-sm">
		{$object->name}
	</h1>
	<p n:ifcontent n:if="$object??->annotation" class="u-mb-sm">
		{rtrim($object??->annotation, '<br>')|noescape}
	</p>

	{foreach $flashes as $flash}
		<p class="message message--{$flash->type} u-mb-xs u-mb-sm@md">{_$flash->message}</p>
	{/foreach}

	{if $object->uid == 'userLogin'} {* login *}
		{var $userPromoCardTitle = 'user_login_promo_title'}
		{snippet signInForm}
			{control signInForm}
		{/snippet}
	{elseif $object->uid == 'registration'} {* registration *}
		{var $userPromoCardTitle = 'user_registration_promo_title'}
		{snippet registrationForm}
			{control registrationForm}
		{/snippet}
	{elseif $object->uid == 'lostPassword'} {* lost password *}
		{snippet lostPasswordForm}
			{control lostPasswordForm}
		{/snippet}
	{elseif $object->uid == 'resetPassword'} {* reset password *}
		{control lostPasswordForm:reset}
	{/if}

	{include $templates . '/part/box/content.latte'}
