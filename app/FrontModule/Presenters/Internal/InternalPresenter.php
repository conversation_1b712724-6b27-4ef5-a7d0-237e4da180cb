<?php

declare(strict_types=1);

namespace App\FrontModule\Presenters\Internal;

use App\FrontModule\Presenters\BasePresenter;
use App\Model\Orm\Routable;
use App\PostType\Page\Model\Orm\Tree;

final class InternalPresenter extends BasePresenter
{

	public function actionDefault(): void
	{
		if (isset($this->params['object'])) {
			$this->setObject($this->params['object']);
		}


		if ($this->getObject() instanceOf Tree && $uid = $this->getObject()->uid) {

			switch ($uid) {
				case 'basketRemove':
					break;

				case 'popupGallery':
					$activeTab = 'photo';
					if (isset($this->params['tab']) && in_array($this->params['tab'], ['photo', 'video'])) {
						$activeTab = $this->params['tab'];
					}

					$activeItem = 1;
					if (isset($this->params['item'])) {
						$activeItem = (int)$this->params['item'];
					}

					if (isset($this->params['pageId'])) {
						$this->template->localObject = $this->orm->tree->getById($this->params['pageId']);
					}
					if (isset($this->params['productId'])) {
						$this->template->localObject = $this->orm->productVariant->getById($this->params['productId']);
					}
					$this->template->activeItem = $activeItem;
					$this->template->activeTab = $activeTab;


					if ($this->isAjax()) {
						$this->redrawControl('gallery');
					}
					break;

				case 'popupContact':

					if ($this->isAjax()) {
						$this->redrawControl('hotline');
					}
					break;
			}

			$this->setView($uid);
		}
	}

}
