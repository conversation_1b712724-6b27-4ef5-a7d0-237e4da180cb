<?php

namespace App\FrontModule\Presenters;

use App\Components\VisualPaginator\PagerLimits;
use Nette\Application\UI\Presenter;
use Nette\Utils\Paginator;

trait HasPagerLimits
{


	public function getPagerLimits(): ?PagerLimits
	{
		assert($this instanceof Presenter);
		if ($this->action !== 'default') {
			return null;
		}

		$paginator = $this['catalogProducts']['pager']->getPaginator();
		assert($paginator instanceof Paginator);

		return new PagerLimits($paginator->page, $paginator->pageCount);
	}

}
