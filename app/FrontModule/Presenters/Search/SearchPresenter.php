<?php declare(strict_types=1);

namespace App\FrontModule\Presenters\Search;

use App\Event\Search;
use App\Event\SearchResuls;
use App\FrontModule\Components\CatalogProducts\CatalogProducts;
use App\FrontModule\Components\CatalogProducts\CatalogProductsCallbackBuilder;
use App\FrontModule\Components\CatalogProducts\CatalogProductsFactory;
use App\FrontModule\Presenters\BasePresenter;
use App\FrontModule\Presenters\HasPagerLimits;
use App\FrontModule\Presenters\Pageable;
use App\Model\BucketFilter\BucketFilterBuilder;
use App\Model\BucketFilter\BucketFilterBuilderFactory;
use App\Model\BucketFilter\Result;
use App\Model\BucketFilter\SortCreator;
use App\Model\FulltextSearch\FulltextSearchBucketFilterProvider;
use App\Model\FulltextSearch\SearchFactory;
use App\Model\FulltextSearch\UserHistory;
use App\Model\Orm\Product\ProductRepository;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\PostType\Page\Model\Orm\CommonTree;
use Nette\Application\Attributes\Persistent;
use Nette\DI\Attributes\Inject;
use Nextras\Orm\Collection\EmptyCollection;
use stdClass;

/**
 * @property-read CommonTree $object
 */
final class SearchPresenter extends BasePresenter implements Pageable
{
	use HasPagerLimits;

	#[Persistent]
	public string $search = '';
	#[Inject]
	public CatalogProductsFactory $catalogProductsFactory;
	#[Inject]
	public CatalogProductsCallbackBuilder $catalogProductsCallbackBuilder;
	#[Persistent]
	public int $page = 1;
	private array $filterParams;
	private BucketFilterBuilder $bucketFilterBuilder;
	private stdClass $filter;
	private array $cleanFilterParam;

	public function __construct(
		private readonly SearchFactory $fulltextSearchFactory,
		private readonly UserHistory $searchUserHistory,
		private readonly FulltextSearchBucketFilterProvider $fulltextSearchBucketFilterProvider,
	)
	{
		parent::__construct();
	}

	protected function startup(): void
	{
		parent::startup();
	}


	public function actionDefault(CommonTree $object, ?string $search = null, array $filter = [], string $order = SortCreator::FULLTEXT): void
	{
		$this->setObject($object);
		$this->filterParams = $filter;
		$this->cleanFilterParam = $this->filterParams;


		if ($search === null) {
			$search = $this->getHttpRequest()->getPost('search');
			$search = trim($search);
		}
		$this->search = $search;
		$this->bucketFilterBuilder = $this->fulltextSearchBucketFilterProvider->get($this->search, $this, $this->mutation, $this->currentState, $this->priceLevel, $this->filterParams);
	}

	public function renderDefault(): void
	{
		$itemsObject = Result::from(
			new EmptyCollection(), /** @phpstan-ignore-line */
			0,
			0,
		);

		$fulltextResults = [];

		if ($this->search !== '') {
			$fulltextResults = $this->fulltextSearchFactory->create($this->mutation, $this->search)->search();
			$this->filter = $this->bucketFilterBuilder->getFilter();
			$this->template->filter = $this->filter;
		}

		$hasSomeResults = $itemsObject->totalCount
			|| count(
				array_filter($fulltextResults, fn(\App\Model\FulltextSearch\Result $result) => $result->hasItems())
			) > 0;

		if ($hasSomeResults) {
			$this->searchUserHistory->add($this->mutation, $this->search, $this->userEntity);
		} else {
			$this->searchUserHistory->addNoResult($this->mutation, $this->search);
		}

		$this->template->hasResults = $hasSomeResults;
		$this->template->productResults = $itemsObject;
		$this->template->search = $this->search;
		$this->template->fulltextResults = $fulltextResults;
		$this->template->cleanFilterParam = $this->filterParams;
		$this->template->sortOptions = ['fulltextScore', 'top', 'newest', 'oldest', 'cheapest', 'expensive', 'name'];
		$this->template->hasResults = $this->search !== '' && $this['catalogProducts']->getTotalCount() > 0;

		$this->eventDispatcher->dispatch(
			new SearchResuls(
				fulltextResults: $fulltextResults + [ProductLocalization::class => $itemsObject],
				searchTerm: $this->search,
				hasResults: $hasSomeResults,
			)
		);

		if (isset($this->bucketFilterBuilder)) {
			$this->bucketFilterBuilder->redrawSnippets($this->cleanFilterParam);
		}
	}

	protected function createComponentCatalogProducts(): CatalogProducts
	{
		$paramsToTemplate = [
			'productTitle' => 'h2',
			'ajaxPage' => true,
			'cleanFilterParam' => $this->cleanFilterParam,
			'class' => 'u-mb-0',
		];

		$findCatalogProductsDataCallback = $this->catalogProductsCallbackBuilder->build($this->bucketFilterBuilder);

		return $this->catalogProductsFactory->create(
			$this->object,
			$this->setup,
			$findCatalogProductsDataCallback,
			$this->getPageParameter(),
			$paramsToTemplate,
			'catalog',
			'search',
		);

	}

}
