{default $class = false}

<form n:if="count($filter->boxes ?? [])" action="{link $object, filter=>[], 'selectedTab' => $selectedTab ?? null, 'page' => null}" n:class="f-filter, $class" data-controller="filter" data-naja data-naja-history="off" data-naja-loader="body" method="get">
	<input n:if="isset($cleanFilterParam['search'])" type="hidden" name="search" value="{$cleanFilterParam['search']}">
	<input n:if="isset($catalogOrder) && $catalogOrder != 'top'" name="order" type="hidden" value="{$catalogOrder}">
	<input type="hidden" name="filter_set" value="1">
	<h2 class="f-filter__title h4">
		{_"filter_title"}
	</h2>

	<button type="button" class="f-filter__close as-link" data-action="toggle-class#toggle">
		{('close')|icon}
		<span class="u-vhide">
			{_"btn_close"}
		</span>
	</button>

	<div class="f-filter__wrap">
		{if isset($filter->boxesByNamespace)}
			{foreach $filter->boxesByNamespace as $namespace => $boxes}
				{if !App\Model\BucketFilter\SetupCreator\FilterFlagsConfig::NAMESPACE_FLAGS_SEPARATE && $namespace === App\Model\BucketFilter\ElasticItem\DiscreteValues::NAMESPACE_FLAGS}
					{include './boxes/flags.latte', boxes: $boxes}
				{else}
					{foreach $boxes as $box}
						{if $box instanceOf App\Model\BucketFilter\Box\Slider}
							{include './boxes/slider.latte', box: $box}
						{else}
							{include './boxes/checkBoxes.latte', box: $box}
						{/if}
					{/foreach}
				{/if}
			{/foreach}
		{/if}
	</div>

	{* Zrušit vše *}
	<p n:if="$filter->nonRoot ?? false" class="f-filter__remove-all u-pt-sm u-mb-sm u-ta-c">
		{capture $link}{link 'this', 'filter' => $filter->followingCleanFilterParameters, 'pager-page' => null, 'filter_clear' => 1}{/capture}
		{php $link = urldecode(htmlspecialchars_decode($link))}
		<a href="{$link}" class="f-filter__remove">
			{_btn_filter_remove}
		</a>
	</p>

	{* noJS submit btn *}
	<p class="u-js-hide u-mb-0 u-pt-xs u-d-n@lg">
		<button type="submit" class="btn" name="filterSubmit">
			<span class="btn__text">
				{_btn_filter}
			</span>
		</button>
	</p>

	{* Sticky mobile btns *}
	<p class="f-filter__btns u-mb-0">
		<button type="button" class="f-filter__btn btn btn--bd" data-action="toggle-class#toggle">
			<span class="btn__text">
				{* <span class="btn__info">{_"btn_no_filter"}</span> *}
				<span class="btn__inner">{_"btn_close"}</span>
			</span>
		</button>
		{* <button type="submit" class="f-filter__btn btn" data-action="filter#submitForm"> *}
		<button type="button" class="f-filter__btn btn" data-action="toggle-class#toggle">
			<span class="btn__text">
				{ifset $presenter['catalogProducts']['pager']}
				<span class="btn__info">
					{php $total = $presenter['catalogProducts']['pager']->getPaginator()->getItemCount()}
					{_"filter_selection"} {$total} {_($total|plural: "item_1", "item_2", "item_3")}
				</span>
				{/ifset}
				<span class="btn__inner">{_"btn_show"}</span>
			</span>
		</button>
	</p>
</form>
{*<script n:if="$changedSearchValue ?? false">*}
{*	var input = document.getElementById({$changedSearchValue});*}
{*	var end = input.value.length;*}
{*	input.setSelectionRange(end, end);*}
{*	input.focus();*}
{*</script>*}
