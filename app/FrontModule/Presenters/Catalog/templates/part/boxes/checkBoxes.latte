{default $forceOpen = false}
{varType App\Model\BucketFilter\Box\CheckBoxes $box}

{embed '../filter-group.latte', open: $box->isOpen() || $forceOpen, title: $box->title, tooltip: $box->description ?? '', templates: $templates, itemCount: $box->getOptionsCount()}
	{block content}
		<p n:if="$box->hasSearch()" class="u-mb-xs">
			<label for="f-filter-input-{$box->getName()}" class="u-vhide">
				{_'placeholder_filter_search'} {* {_'search_filter_value_' . $box->getName()} *}
			</label>
			<span class="inp-fix">
				<input class="inp-text" type="text"
					name="filter[searchValue][{$box->getName()}]"
					value="{$box->searchValue()}" id="f-filter-input-{$box->getName()}"
					placeholder="{_'placeholder_filter_search'}"
					{* data-naja data-naja-history="off" data-naja-unique="off" *}
					data-action="input->filter#submitDebounced paste->filter#submitDebounced"
				    data-filter-fulltext=""
				>

			</span>
		</p>

		<ul id="f-filter-values-list-{$box->getName()}" class="f-filter__list">
			{foreach $box->getOrderedItems() as $value}
				{capture $link}{link 'this', filter => $value->followingFilterParameters, 'page' => null}{/capture}
				{php $link = urldecode(htmlspecialchars_decode($link))}
				<li n:class="f-filter__item, ($box->visibleValuesCount ?? false) && $iterator->getCounter() > $box->visibleValuesCount ? u-js-hide">
					<label class="f-filter__inp-item inp-item inp-item--checkbox">
						<input type="checkbox" name="{$value->inputName}" value="{$value->inputValue}" data-action="change->filter#submit" class="inp-item__inp inp-filter" {if !$value->isChecked && $value->count == 0} disabled{/if} {if isset($value->isChecked) && $value->isChecked} checked{/if}  data-list-id="{_$box->title}" data-list-name="{$value->name}" data-list-action="{if isset($value->isChecked) && $value->isChecked}remove{else}add{/if}">
						<span class="inp-item__text">
							{if !$value->isChecked && $value->count == 0}
								{$value->name}
								{if $box->unit}
									{$box->unit}
								{/if}
							{else}
								<a href="{$link}" class="f-filter__link"{if isset($linkSeo) && $linkSeo->hasNofollow( $linkSeoPage ?? $object, ['filter' => $value->followingFilterParameters])} rel="nofollow"{/if}>
									{$value->name}
									{if $box->unit}
										{$box->unit}
									{/if}
								</a>
							{/if}

							<span n:if="!$value->isChecked && $value->count" class="f-filter__count">
								({if isset($box->isPlus) && $box->isPlus}+{/if}{$value->count|number:0, ',', '&nbsp;'|noescape})
							</span>
						</span>
					</label>
				</li>
			{/foreach}
			{if $box->searchValueNotFound}
				{_btn_search_value_not_found}
			{/if}
		</ul>
		{if $box->showMoreLink !== null}
			<p class="f-filter__more">
				<a href="{link $box->showMoreLink}" class="item-icon">
					<span class="item-icon__text">
						{_btn_show_all}
					</span>
				</a>
			</p>
		{elseif $box->showMoreButton}
			<p class="f-filter__more">
				<button type="button" class="as-link item-icon" data-controller="toggle-class" data-action="toggle-class#toggle" data-toggle-class="is-expanded" data-toggle-closest=".f-filter__group">
					<span class="item-icon__text">
						{_btn_more_values|replace:'%count', (string) $box->showMoreButtonCount}
					</span>
					<span class="item-icon__text">
						{_btn_show_less}
					</span>
				</button>
			</p>
		{/if}
	{/block}
{/embed}
