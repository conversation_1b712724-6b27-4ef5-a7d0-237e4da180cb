{default $forceOpen = false}
{default $isOpen = false}
{default $visible = false}

{capture $items}
	<ul class="f-filter__list">
		{foreach $boxes as $box}
			{if ($box->isOpen() || $forceOpen) && $isOpen === false}
				{var $isOpen = true}
			{/if}

			{foreach $box->getOrderedItems() as $value}
				{capture $link}{link 'this', filter => $value->followingFilterParameters, 'page' => null}{/capture}
				{php $link = urldecode(htmlspecialchars_decode($link))}
				{php $visible = true}
				<li class="f-filter__item{if isset($box->visibleValuesCount) && $box->visibleValuesCount && $iterator->getCounter() > $box->visibleValuesCount} u-js-hide{/if}"{if isset($box->visibleValuesCount) && $box->visibleValuesCount && $iterator->getCounter() > $box->visibleValuesCount} data-filtergroup-target="hidden"{/if}>
					<label class="f-filter__inp-item inp-item inp-item--checkbox">
						<input type="checkbox" name="{$value->inputName}" value="{$value->inputValue}"  data-action="change->filter#submit" class="inp-item__inp inp-filter" {if !$value->isChecked && $value->count == 0} disabled{/if} {if isset($value->isChecked) && $value->isChecked} checked{/if} data-list-id="{_filter_title_flags}" data-list-name="{$value->name}" data-list-action="{if isset($value->isChecked) && $value->isChecked}remove{else}add{/if}">
						<span class="inp-item__text">
							{if !$value->isChecked && $value->count == 0}
								{$value->name}
								{if $box->unit}
									{$box->unit}
								{/if}
							{else}
								<a href="{$link}" class="f-filter__link" {if isset($linkSeo) && $linkSeo->hasNofollow( $linkSeoPage ?? $object, ['filter' => $value->followingFilterParameters])} rel="nofollow"{/if}>
									{$value->name}
									{if $box->unit}
										{$box->unit}
									{/if}
								</a>
							{/if}

							<span n:if="!$value->isChecked && $value->count" class="f-filter__count">
								({if isset($box->isPlus) && $box->isPlus}+{/if}{$value->count|number:0, ',', '&nbsp;'|noescape})
							</span>
						</span>
					</label>
				</li>
			{/foreach}
		{/foreach}
	</ul>
{/capture}

{if $visible}
	{embed '../filter-group.latte', open: $isOpen || $forceOpen, title: ($object->hasCourses) ? 'filter_title_flags_courses' : 'filter_title_flags'}
		{block content}
			{$items}
		{/block}
	{/embed}
{/if}
