{default $forceOpen = false}

{if $box->inputValueMin < $box->inputValueMax}
	{embed '../filter-group.latte', open: $box->isOpen() || $forceOpen, title: $box->title}
		{block content}
			<div data-controller="slider" data-slider-filter-outlet=".f-filter">
				<div class="nouislider-holder">
					<div class="nouislider" data-slider-target="slider"
							data-start="{$box->selectedMin}"
							data-end="{$box->selectedMax}"
							data-min="{$box->inputValueMin}"
							data-max="{$box->inputValueMax}"
							data-step="{$box->step}"></div>
				</div>

				<p class="tw-flex tw-items-center tw-gap-[0.8rem] tw-mb-0 tw-text-[1.3rem]">
					<span class="inp-fix inp-fix--sm tw-flex-1">
						<input type="number" class="inp-text inp-text--sm" min="{$box->inputValueMin}" max="{$box->inputValueMax}" data-range="min" data-action="change->filter#submit"
							{* {if $box->selectedMin == $box->inputValueMin} disabled {/if} *}
							data-list-id="{$box->name}"
							data-list-name="{_$box->title}"
							name="{$box->inputNameMin}"
							value="{$box->selectedMin}"
							data-slider-target="inpFrom">
					</span>
					<span>-</span>
					<span class="inp-fix inp-fix--sm tw-flex-1">
						<input type="number" class="inp-text inp-range inp-text--sm" min="{$box->inputValueMin}" max="{$box->inputValueMax}" data-range="max" data-action="change->filter#submit"
							{* {if $box->selectedMax == $box->inputValueMax} disabled {/if} *}
							data-list-id="{$box->name}"
							data-list-name="{_$box->title}"
							name="{$box->inputNameMax}"
							value="{$box->selectedMax}"
							data-slider-target="inpTo">
					</span>
					<span>{$box->unit}</span>
				</p>
			</div>
		{/block}
	{/embed}
{/if}
