{varType App\Model\BucketFilter\Box\Slider $box}

{capture $link}{link 'this', filter => $box->filterToDeSelect, page => null}{/capture}
{php $link = urldecode(htmlspecialchars_decode($link))}

<div class="b-filters__group">
	<p class="b-filters__title">
		{$box->title}:
		<strong>
			{if $box->inputValueMin != $box->selectedMin && $box->inputValueMax != $box->selectedMax}
				{$box->selectedMin}{if $box->unit} {$box->unit}{/if} - {$box->selectedMax}{if $box->unit} {$box->unit}{/if}
			{elseif $box->inputValueMin != $box->selectedMin}
				{_from} {$box->selectedMin}{if $box->unit} {$box->unit}{/if}
			{elseif $box->inputValueMax != $box->selectedMax}
				{_to} {$box->selectedMax}{if $box->unit} {$box->unit}{/if}
			{/if}
		</strong>
		<a href="{$link}" class="b-filters__remove" data-naja data-naja-loader="body"{if $linkSeo->hasNofollow( $linkSeoPage ?? $object, ['filter' => $box->filterToDeSelect])} rel="nofollow"{/if}>{_btn_filter_cancel}</a>
	</p>
</div>
