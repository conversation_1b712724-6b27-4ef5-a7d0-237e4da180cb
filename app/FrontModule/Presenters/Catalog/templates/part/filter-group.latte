{default $open = false}
{default $title = false}
{default $tooltip = false}

{if isset($itemCount) && $itemCount < 2}
	{* hide section *}
{else}
	<div n:class="f-filter__group, $open ? is-open" data-controller="toggle-class">
		<p n:if="$title" class="f-filter__name">
			<button type="button" n:class="f-filter__toggle, as-link, $open ? is-open" data-action="toggle-class#toggle" aria-expanded="{$open ? 'true' : 'false'}">
				{_$title}
			{if $tooltip}
				{embed $templates.'/part/core/tooltip.latte', class: 'f-filter__tooltip', btnClass=>'as-link', placement: 'right'}
					{block btn}
						{('info')|icon, 'tooltip__icon'}
					{/block}
					{block content}
						{$tooltip|noescape}
					{/block}
				{/embed}
			{/if}
			{('angle-down')|icon, 'f-filter__arrow'}
			</button>
		</p>
		<div class="f-filter__inner">
			{block content}{/block}
		</div>
	</div>
{/if}


