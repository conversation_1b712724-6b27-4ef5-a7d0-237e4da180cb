{block content}
	<div class="row-main">


		{snippet catalogHeader}
			{include $templates.'/part/box/header.latte', class: 'b-header--catalog u-mb-xs u-mb-md@md', showMore: true, cf: $object->cf->catalog_header ?? false, disableDescriprion: $filter->nonRoot}
		{/snippet}


		{* Crossroad categories *}
		{cache cacheKey('categoriesCrossroad', $object), expire: '5 minutes', tags: [App\PostType\Page\Model\Orm\Tree::CACHED_LIST_TAG]}
			{control categories:crossroad}
		{/cache}

		<div class="b-catalog u-mb-md u-mb-xl@md">
			{* Filter, sort, selected *}
			{snippetArea filterAreaMainWrapper}
				{include $templates.'/part/box/filter.latte', class: false}
			{/snippetArea}
			<div class="b-catalog__main">
				{snippet products}
					 {control catalogProducts}
				{/snippet}
			</div>
		</div>

		{control customContentRenderer}

		{if $object->uid == 'courses'}
			<hr class="u-mb-md u-mb-xl@md">
			{include $templates.'/part/box/faq.latte'}
			<hr class="u-mb-md u-mb-xl@md">
		{else}
			<hr class="u-mb-sm u-mb-xl@md">
			{include $templates.'/part/box/services.latte', showTitle: false}
		{/if}

		{include $templates.'/part/box/benefits.latte'}
	</div>
{/block}
