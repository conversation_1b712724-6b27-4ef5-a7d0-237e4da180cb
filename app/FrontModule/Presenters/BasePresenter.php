<?php declare(strict_types = 1);

namespace App\FrontModule\Presenters;

use App\Event\AfterLogin;
use App\Event\AfterLogout;
use App\Event\BannerClicked;
use App\Event\FilterClicked;
use App\Event\MenuClicked;
use App\Event\PageView;
use App\Event\Provider\BannerClickedProvider;
use App\Event\Provider\FilterClickedProvider;
use App\Event\Provider\FilterRangeClickedProvider;
use App\Event\Provider\MenuClickedProvider;
use App\Event\Provider\SuggestClickedProvider;
use App\Event\SearchClick;
use App\Event\ViewPromotion;
use App\Exceptions\LogicException;
use App\FrontModule\Components\BookReviews\BookReviews;
use App\FrontModule\Components\BookReviews\BookReviewsFactory;
use App\FrontModule\Components\Bestsellers\CatalogProducts;
use App\FrontModule\Components\Breadcrumb\Breadcrumb;
use App\FrontModule\Components\Breadcrumb\BreadcrumbFactory;
use App\FrontModule\Components\CanonicalUrl\CanonicalUrl;
use App\FrontModule\Components\CanonicalUrl\CanonicalUrlFactory;
use App\FrontModule\Components\ContactForm\ContactFormControl;
use App\FrontModule\Components\ContactForm\ContactFormControlFactory;
use App\FrontModule\Components\EditButton\EditButton;
use App\FrontModule\Components\EditButton\EditButtonFactory;
use App\FrontModule\Components\InfoBox\InfoBox;
use App\FrontModule\Components\InfoBox\InfoBoxFactory;
use App\FrontModule\Components\NewsletterForm\NewsletterFormControl;
use App\FrontModule\Components\NewsletterForm\NewsletterFormControlFactory;
use App\FrontModule\Components\OpenGraph\DTO\Util\ProductData;
use App\FrontModule\Components\OpenGraph\Enums\OpenGraphType;
use App\FrontModule\Components\OpenGraph\OpenGraph;
use App\FrontModule\Components\OpenGraph\OpenGraphFactory;
use App\FrontModule\Components\ProductList\ProductList;
use App\FrontModule\Components\ProductList\ProductListFactory;
use App\FrontModule\Components\ProductList\ProductListInterestedFactory;
use App\FrontModule\Components\Robots\Robots;
use App\FrontModule\Components\Robots\RobotsFactory;
use App\FrontModule\Components\SeoTools\SeoTools;
use App\FrontModule\Components\SeoTools\SeoToolsFactory;
use App\FrontModule\Components\SiteHeader\SiteHeader;
use App\FrontModule\Components\SiteHeader\SiteHeaderFactory;
use App\FrontModule\Components\ProductBox\ProductBoxFactory;
use App\FrontModule\Presenters\Homepage\HomepagePresenter;
use App\FrontModule\Presenters\Order\OrderPresenter;
use App\FrontModule\Presenters\Product\ProductPresenter;
use App\Infrastructure\BasicAuth\Authenticator;
use App\Infrastructure\Latte\Filters;
use App\Model\BucketFilter\Sort;
use App\Model\CacheFactory;
use App\Model\CacheStorageService;
use App\Model\Comparators\ComparatorModeProvider;
use App\Model\Consent\MarketingConsent;
use App\Model\Currency\CookieStorage;
use App\Model\Currency\CurrencyHelper;
use App\Model\CustomField\LazyValue;
use App\Model\DTO\Product\ProductDtoProvider;
use App\Model\ImageResizerWrapper;
use App\Model\LastVisitedProduct;
use App\Model\Link\LinkFactory;
use App\Model\Mutation\MutationDetector;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\IpAddress\IpAddressModel;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Parameter\ParameterRepository;
use App\Model\Orm\ParameterValue\ParameterValueFilterHelper;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\PriceLevel\PriceLevelModel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\Routable;
use App\Model\Orm\State\State;
use App\Model\Orm\User\UserModel;
use App\Model\Setup;
use App\Model\ShoppingCart\SavedForLaterProvider;
use App\Model\ShoppingCart\ShoppingCartInterface;
use App\Model\StaticPage\StaticPage;
use App\Model\TagManager\TagManager;
use App\Model\Template\PartCondition;
use App\Model\TranslatorDB;
use App\PostType\Discount\FrontModule\Presenters\DiscountPresenter;
use App\PostType\Tag\FrontModule\Presenters\TagPresenter;
use App\Utils\DateTime;
use Brick\Money\Currency;
use Nette;
use Nette\Application\Attributes\Persistent;
use Nette\Application\Request;
use Nette\Application\UI\Presenter;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Caching\Cache;
use Nette\DI\Attributes\Inject;
use stdClass;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Tracy\Debugger;
use function assert;

/**
 * @property Routable|StaticPage $object
 * @property-read DefaultTemplate $template
 */
abstract class BasePresenter extends \App\BasePresenter
{

	#[Persistent]
	public string $alias;

	#[Inject]
	public Nette\Http\RequestFactory $requestFactory;

	#[Inject]
	public Nette\Http\Session $session;

	#[Inject]
	public CacheFactory $cacheFactory;

	#[Inject]
	public TranslatorDB $translator;

	#[Inject]
	public MutationHolder $mutationHolder;

	#[Inject]
	public MutationDetector $mutationDetector;

	#[Inject]
	public LinkFactory $linkFactory;

	#[Inject]
	public ImageResizerWrapper $imageResizerWrapper;

	#[Inject]
	public ContactFormControlFactory $contactFormFactory;

	#[Inject]
	public NewsletterFormControlFactory $newsletterFormFactory;


	#[Inject]
	public BreadcrumbFactory $breadcrumbFactory;

	#[Inject]
	public RobotsFactory $robotsFactory;

	#[Inject]
	public SeoToolsFactory $seoToolsFactory;

	#[Inject]
	public CanonicalUrlFactory $canonicalUrlFactory;

	#[Inject]
	public UserModel $userModel;

	#[Inject]
	public EditButtonFactory $editButtonFactory;

	#[Inject]
	public LastVisitedProduct $lastVisitedProduct;

	#[Inject]
	public ProductListFactory $productListFactory;

	#[Inject]
	public ProductListInterestedFactory $productListInterestedFactory;

	#[Inject]
	public ParameterRepository $parameterRepository;

	#[Inject]
	public ParameterValueFilterHelper $parameterValueFilterHelper;

	#[Inject]
	public BookReviewsFactory $bookReviewsFactory;

	#[Inject]
	public OpenGraphFactory $openGraphFactory;

	#[Inject]
	public InfoBoxFactory $infoBoxFactory;
	#[Inject]
	public ProductBoxFactory $productBoxFactory;

	protected Routable|StaticPage $object;

	protected ?Nette\Utils\Paginator $paginator = null;

	protected Mutation $mutation;

	protected Cache $cache;

	/**
	 * state = kolize s persistant v NewsletterPresenter
	 */
	protected State $currentState;

	protected PriceLevel $priceLevel;

	#[Inject]
	public Authenticator $basicAuth;


	protected bool $bypassBasicAuth = false;

	#[Inject]
	public ShoppingCartInterface $shoppingCart;

	#[Inject]
	public SavedForLaterProvider $savedForLaterProvider;

	#[Inject]
	public MarketingConsent $marketingConsent;

	#[Inject]
	public EventDispatcherInterface $eventDispatcher;

	#[Inject]
	public TagManager $tagManager;
	#[Inject]
	public BannerClickedProvider $bannerClickedProvider;
	#[Inject]
	public MenuClickedProvider $menuClickedProvider;
	#[Inject]
	public FilterClickedProvider $filterClickedProvider;
	#[Inject]
	public FilterRangeClickedProvider $filterRangeClickedProvider;
	#[Inject]
	public SuggestClickedProvider $suggestClickedProvider;
	#[Inject]
	public CookieStorage $currencyCookieStorage;
	#[Inject]
	public PartCondition $partCondition;
	#[Inject]
	public Nette\Application\Application $application;
	#[Inject]
	public SiteHeaderFactory $siteHeaderFactory;
	#[Inject]
	public IpAddressModel $ipAddressModel;
	#[Inject]
	public PriceLevelModel $priceLevelModel;
	#[Inject]
	public CacheStorageService $cacheStorageService;
	#[Inject]
	public ProductDtoProvider $productDtoProvider;
	#[Inject]
	public ComparatorModeProvider $comparatorModeProvider;

	protected Setup $setup;
	protected ?Sort $seoToolsSort = null;
	protected ?stdClass $seoToolsFilter = null;

	protected function startup(): void
	{
		// $this->session->start();

		// ******* redirects ************************
		if (isset($_GET['terminate'])) {
			$this->terminate();
		}

		if ($this->request->hasFlag(Request::RESTORED)) { // reseni pro backlink po ajaxu
			$this->redirect('this');
		}

		// ******* basic ************************
		parent::startup();

		$this->dbaLog->register();
		DateTime::setCacheStorageService($this->cacheStorageService);
		$this->setMutation();
		$this->setState();
		$this->setPriceLevel();
		$this->cache = $this->cacheFactory->create(static::class);

//		$this->autoCanonicalize = FALSE;

		// ******* helpers & templates ************************
		Filters::$mutation = $this->mutationHolder->getMutation();
		Filters::$translator = $this->translator;
		Filters::$version = $this->configService->get('webVersion');

		CurrencyHelper::setCookieStorage($this->currencyCookieStorage);
		CurrencyHelper::setDefaultCurrencyCode($this->mutation->currency->getCurrencyCode());

		if ($this->getUserEntity()?->preferredCurrency) {
			//CurrencyHelper::setCurrencyCode($this->getUserEntity()->preferredCurrency);
		}

		$this->comparatorModeProvider->init();

		$this->ipAddressModel->setIpAddress($_SERVER['REMOTE_ADDR'] ?? '');
		$this->ipAddressModel->init();
		// prepsani defaultnich hlasek
		Nette\Forms\Validator::$messages[Nette\Forms\Form::Email] = 'form_valid_email';
		Nette\Forms\Validator::$messages[Nette\Forms\Form::Filled] = 'form_error';

		// ******* other ************************
		$this->savedForLaterProvider->setMutation($this->mutation);;
		$this->shoppingCart->setMutation($this->mutation);
		$this->shoppingCart->setCurrency(CurrencyHelper::getCurrencyCode());
		$this->mutation->setSelectedCurrency(Currency::of(CurrencyHelper::getCurrencyCode()));

		$this->application->onShutdown[] = function () {
			$this->translator->persistCacheMap();
		};

		if ($this->configService->get('identityMapDebug') ?? false) {

			$this->application->onShutdown[] = function () {
				$container = $this->getContext();
				bdump($container->getByType(Orm::class)->getIdentityMap($container),	'onShutdown IdentityMap');
			};
		}

		$this->setup = new Setup(
			$this->mutation,
			$this->currentState,
			$this->priceLevel,
			$this->userEntity,
		);
		$this->productDtoProvider->setSetup($this->setup);

		if ((int) $this->getParameter('forcePersonalizationGranted') === 1) {
			$this->marketingConsent->forcePersonalizationGranted();
		}
	}

	public function checkRequirements(mixed $element): void
	{
		parent::checkRequirements($element);

		if ( ! $element instanceof Nette\Application\UI\ComponentReflection) {
			return;
		}

		if ($this->bypassBasicAuth) {
			return;
		}

		$this->basicAuth->authenticate(
			$this->getHttpRequest(),
			function (): never {
				$this->getHttpResponse()->setHeader('WWW-Authenticate', 'Basic realm="app"');
				if (Debugger::isEnabled()) {
					$this->terminate();
				} else {
					$this->error(httpCode: Nette\Http\IResponse::S401_Unauthorized);
				}
			},
		);
	}

	protected function setMutation(): void
	{
		if ($this->getParameter('mutation')) {
			$this->mutation = $this->getParameter('mutation');
		} else {
			$this->mutation = $this->mutationDetector->detect();
		}

		$this->mutationHolder->setMutation($this->mutation);
		$this->orm->setMutation($this->mutation);
		$this->translator->setMutation($this->mutation);
	}


	protected function setState(): void
	{
		$this->currentState = $this->orm->state->getDefault($this->mutation);
	}

	protected function setPriceLevel(): void
	{
		$priceLevelId = $this->userEntity?->priceLevel->id ?? PriceLevel::DEFAULT_ID;
		$priceLevel = $this->priceLevelModel->getById($priceLevelId);
		if ($priceLevel === null) {
			throw new \LogicException('Missing price level');
		}
		$this->priceLevel = $priceLevel;
	}

	protected function beforeRender(): void
	{
		parent::beforeRender();

		// ******* basic ************************
		$this->template->setTranslator($this->translator);
		$this->template->translator = $this->translator;
		$this->template->mutation = $this->mutation;
		$this->template->imageObjectFactory = $this->imageObjectFactory;
		$this->layout = FE_TEMPLATE_DIR . '/@layout.latte';
		$this->template->templates = FE_TEMPLATE_DIR;
		$this->template->isHomepage = $this instanceof HomepagePresenter;
		$this->template->isEnvProduction = $this->configService->isEnvProduction();
		//  robots per mutation
		if ($this->mutation->langCode) {
			$this->template->currencyCode = $this->mutation->currency; //"CZK";
		}
		$this->template->partCondition = $this->partCondition;

		$this->template->object = $this->getObject();
		$this->template->pages = $this->mutation->pages;
		$this->template->state = $this->currentState;
		$this->template->priceLevel = $this->priceLevel;

		// ******* callbacks ********************
		$this->template->getImage = function ($entity, $size) {
			return $this->imageResizerWrapper->getResizedImage($entity, $size);
		};

		$this->template->cfg = function () {
			return call_user_func_array([$this->configService, 'get'], func_get_args());
		};

		$this->template->productDtoProviderCallback =  function (Product $product, ProductVariant $variant) {
			return$this->productDtoProvider->get($product, $variant);
		};

		// ******* other ********************


		$this->template->isOrder = isset($this->getObject()->uid) && in_array($this->getObject()->uid, ['basket', 'step1', 'step2', 'step3']);
		$this->template->googleAnalyticsCode = $this->mutation->getGACode();
		$this->template->googleApiKey = $this->configService->getParam('google', 'apiKey');
		$this->template->systemMessages = []; //$this->orm->systemMessage->findAllPublic();
		$this->template->marketingConsent = $this->marketingConsent->isPersonalizationGranted();



		// Dispatch PageView event
		$this->eventDispatcher->dispatch(new PageView($this, $this->getObject(), $this->getHttpRequest(), $this->userEntity));

		// Dispatch BannerClicked event
		if (($listId = $this->bannerClickedProvider->listId) !== null) {
			$listName = $this->bannerClickedProvider->listName;
			$this->eventDispatcher->dispatch(
				new BannerClicked(
					creativeName: $listId,
					creativeSlot: $listName,
				)
			);
		}

		// Dispatch MenuClicked event
		if (($menuClick = $this->menuClickedProvider->listId) !== null) {
			$submenuClick = $this->menuClickedProvider->listName;
			$this->eventDispatcher->dispatch(
				new MenuClicked(
					mainMenu: $menuClick,
					subMenu: $submenuClick,
				)
			);
		}

		// Dispatch FilterClicked event
		if (($filterName = $this->filterClickedProvider->listId) !== null) {
			$filterValue = $this->filterClickedProvider->listName;
			$filterAction = $this->filterClickedProvider->listAction;
			$this->eventDispatcher->dispatch(
				new FilterClicked(
					filterName: $filterName,
					filterValue: $filterValue,
					filterAction: $filterAction
				)
			);
		}

		if (($suggestName = $this->suggestClickedProvider->listId) !== null) {
			$this->eventDispatcher->dispatch(new SearchClick($suggestName));
		}

		// Dispatch FilterClicked event
		/*if (($filterName = $this->filterRangeClickedProvider->listId) !== null) {
			$filterValue = $this->getRequest()->getParameter('filter')['ranges'][$filterName] ?? null;

			if ($filterValue !== null) {
				foreach ($filterValue as $key => $value) {
					if ($key === 'max') {
						$filterValue['top'] = sprintf('%0.2f', $filterValue['max']);
						unset($filterValue['max']);
					} elseif ($key === 'min') {
						$filterValue['low'] = sprintf('%0.2f', $filterValue['min']);
						unset($filterValue['min']);
					}
				}
				$this->eventDispatcher->dispatch(
					new FilterClicked(
						filterName: $filterName,
						filterValue: $filterValue,
					)
				);
			}
		}*/

		if (($value = $this->getHttpRequest()->getCookie('loginComplete')) !== null) {
			$this->eventDispatcher->dispatch(new AfterLogin($this->user, $this->mutation, $value));
		}

		if (($value = $this->getHttpRequest()->getCookie('logoutComplete')) !== null) {
			$this->eventDispatcher->dispatch(new AfterLogout($this->user, $this->mutation, $value));
		}

	}

	public function handleMeasureIp(): void
	{
		$this->template->measureIp = base64_encode($this->configService->get('REMOTE_ADDR'));

		if ($this->userEntity && isset($this->userEntity->id)) {
			$this->template->measureUserId = $this->userEntity->id;
			if ($this->userEntity->createdTime) {
				$this->template->measureUserCreated = $this->userEntity->createdTime->format('Y-d-m');
			} else {
				$this->template->measureUserCreated = null;
			}
		} else {
			$this->template->measureUserId = null;
			$this->template->measureUserCreated = null;
		}
	}

	public function setObject(Routable|StaticPage $object): void
	{
		$this->object = $object;
	}


	public function getObject(): Routable|StaticPage
	{
		return $this->object;
	}

	public function getPaginator(): ?Nette\Utils\Paginator
	{
		return $this->paginator;
	}

	public function setPaginator(?Nette\Utils\Paginator $paginator): void
	{
		$this->paginator = $paginator;
	}

	public function handleLogout(): void
	{
		$this->getHttpResponse()->setCookie('logoutComplete', 'siteHeader', '+1 minute');
		$this->getUser()->logout(true);
		$this->flashMessage('msg_info_logout');
		$this->redirect('this');
	}


	// ************************** COMPONENTS ****************************** /



	protected function createComponentBreadcrumb(): Breadcrumb
	{
		return $this->breadcrumbFactory->create($this->getObject());
	}


	protected function createComponentContactForm(): ContactFormControl
	{
		assert($this->object instanceof Routable);
		return $this->contactFormFactory->create($this->object);
	}


	protected function createComponentNewsletterForm(): NewsletterFormControl
	{
		return $this->newsletterFormFactory->create();
	}

	protected function createComponentCanonicalUrl(): CanonicalUrl
	{
		return $this->canonicalUrlFactory->create($this->getObject(), $this['robots']);
	}


	protected function createComponentRobots(): Robots
	{
		return $this->robotsFactory->create($this->getObject(), $this->mutationHolder->getMutation());
	}

	protected function createComponentSeoTools(): SeoTools
	{
		$presenterFilterParameter = $this->getRequest()->getParameter('filter') ?? [];

		$seoTools = $this->seoToolsFactory->create($this->getObject(), $this['robots'], $this['canonicalUrl'], $presenterFilterParameter);

		if ($this instanceof Pageable) {
			$seoTools->setPagerLimits($this->getPagerLimits());
		}

		$seoTools->setSort($this->seoToolsSort);
		$seoTools->setFilter($this->seoToolsFilter);


		return $seoTools;
	}
	protected function createComponentEditButton(): EditButton
	{
		$routable = $this->getObject();
		if ($routable instanceof Routable) {
			return $this->editButtonFactory->create($routable, $this->userEntity);
		} else {
			return $this->editButtonFactory->create(null, $this->userEntity);
		}
	}

	final public function createComponentOpenGraph(): OpenGraph
	{
		$openGraphData = $this->getObject();
		if (
			$openGraphData instanceof ProductLocalization
		 	&& isset($this->variant)
		 	&& $this->variant instanceof ProductVariant
		) {
			$openGraphData = new ProductData($openGraphData, $this->variant);
		}

		return $this->openGraphFactory->create(OpenGraphType::default, $this->setup, $openGraphData);
	}

	protected function createComponentLastVisitedProducts(): ProductList
	{
		return $this->productListFactory->create($this->currentState, $this->priceLevel, ProductList::TYPE_LAST_VISITED, $this->userEntity)
		                                ->setIncludeProductIds($this->lastVisitedProduct->getProductIds())
										->setTemplateFile('lastVisited.latte')
			                            ->setLimit(21);
	}


	public function createComponentBookReviews(): BookReviews
	{
		return $this->bookReviewsFactory->create();
	}



	// ****************************** INTERNALS ****************************** /

	/**
	 * @param array $args
	 * @throws Nette\Application\UI\InvalidLinkException
	 */
	public function link(Product|LazyValue|Routable|string $destination, $args = []): string
	{
		$generator = function () use ($destination, $args) {
			[$destination, $args] = $this->translateDestination($destination, $args);

			if ($destination instanceof Routable) {
				[$destination, $args] = $this->linkFactory->linkInPresenter($destination, $args);
			}
			$options = [
				Cache::Expire => '10 minutes'
			];
			return [parent::link($destination, $args), $options];
		};

		if (is_string($destination)) {
			return $generator()[0];
		}
		return $generator()[0];
//		return $this->presenterCache->getLink($destination, $args, $generator);
	}
	/**
	 * @param array $args
	 * @throws Nette\Application\UI\InvalidLinkException
	 */
	public function netteLink(string $destination, array $args = []): string
	{
		return parent::link($destination, $args);
	}


	/**
	 * @throws Nette\Application\AbortException
	 */
	public function redirect(Product|LazyValue|Routable|string $destination, $args = []): never
	{
		[$destination, $args] = $this->translateDestination($destination, $args);

		if ($destination instanceof Routable) {
			[$destination, $args] = $this->linkFactory->linkInPresenter($destination, $args);
		}

		parent::redirect($destination, $args);
	}


	private function translateDestination(Product|LazyValue|Routable|string $destination, array $args): array
	{
		if ($destination instanceof LazyValue) {
			$destination = $destination->getEntity();
			if ($destination === null) {
				trigger_error('Bad CF LazyValue entity', E_USER_NOTICE);
				// value for common user without debug mode
				$destination = 'this';
			}
		}

		if (is_string($destination)) { // input: this, //this, logout!
			$mutation = $args['mutation'] ?? $this->mutation;
			if ($mutation->urlPrefix) {
				$args['urlPrefix'] = $mutation->urlPrefix;
			}
		}

		if ($destination instanceof Product) {
			$mutation = $args['mutation'] ?? $this->mutation;
			if ($mutation->urlPrefix) {
				$args['urlPrefix'] = $mutation->urlPrefix;
			}
			$destination = $destination->getLocalization($mutation);
		}

		return [$destination, $args];
	}



	public function formatTemplateFiles(): array
	{
		$fileName = static::getReflection()->getFileName();
		assert($fileName !== false);

		$dir = dirname($fileName);
		$dir = is_dir("$dir/templates") ? $dir : dirname($dir);
		return ["$dir/templates/$this->view.latte"];
	}


	public function handleEmptyCart(): void
	{
		$this->shoppingCart->doEmpty();

		if (!$this->isAjax()) {
			$this->redirect('this');
		}
		$this->redrawControl();
	}

	public function handleToCart(): void
	{
		$this->shoppingCart->storeVisit();

		$this->redirect($this->mutation->pages->cart);
	}

	public function fireEventViewPromotion(string $name, string $slot, ?string $promotion_id = null, ?string $promotion_name = null): string
	{
		$prefix = match(static::class) {
			HomepagePresenter::class => 'homepage',
			ProductPresenter::class => 'product',
			OrderPresenter::class => 'order',
			default => 'unknown'
		};

		$slot = $prefix . '_' . $slot;

		$this->eventDispatcher->dispatch(new ViewPromotion($name, $slot, $promotion_id, $promotion_name));

		return $slot;
	}


	protected function createComponentInfoBox(): InfoBox
	{
		return $this->infoBoxFactory->create();
	}

	protected function createComponentSiteHeader(): SiteHeader
	{
		return $this->siteHeaderFactory->create(
			$this->getObject(),
			$this->setup,
		);
	}

	public function getMutation(): Mutation
	{
		return $this->mutation;
	}

	public function getPageParameter(): int
	{
		$page = (int)$this->getHttpRequest()->getQuery('page');
		return $page > 0 ? $page : 1;
	}

	public function isSignalRequest(): bool
	{
		return $this->getParameter(Presenter::SignalKey) !== null;
	}

	public function getReferer(): string
	{
		$referer = $this->getHttpRequest()->getReferer();

		if ($referer !== null && $referer->getHost() === $this->configService->get('mutations', $this->mutation->langCode, 'domain')) {
			return (string) $referer;
		}

		return $this->link($this->mutation->pages->title);
	}
}
