<?php declare(strict_types = 1);

namespace App\FrontModule\Presenters\Redirect;

use App\FrontModule\Presenters\BasePresenter;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Redirect\Redirect;
use Nette\Utils\Validators;

final class RedirectPresenter extends BasePresenter
{

	public function __construct(
		private readonly MutationsHolder $mutationsHolder
	)
	{
	}

	public function actionDefault(Redirect $redirect): never
	{
		if (Validators::isUrl($redirect->newUrl)) {
			$newUrl = $redirect->newUrl;
		} else {
			$defaultMutation = $this->mutationsHolder->getDefault();
			$newUrl = $defaultMutation->getBaseUrlWithPrefix() . '/' . $redirect->newUrl;
		}

		$this->redirectUrl($newUrl, $redirect->code);
	}

}
