<?php declare(strict_types = 1);

namespace App\FrontModule\Presenters\Newsletter;

use App\FrontModule\Presenters\BasePresenter;
use App\Model\Orm\NewsletterEmail\NewsletterEmailModel;
use Nette\Application\AbortException;
use Nette\Application\Attributes\Persistent;

final class NewsletterPresenter extends BasePresenter
{

	#[Persistent]
	public ?string $state;

	/** @var string[][]  */
	private array $stateMessageList = [
		'success' => ['Byli jste úspěšně odhlášeni z newsletteru.', 'ok'],
		'already' => ['Z newsletteru jste již byli odhlášeni dříve.', 'info'],
		'error' => ['Tento odkaz je neplatný.', 'error'],
	];

	public function __construct(
		private readonly NewsletterEmailModel $newsletterEmailModel,
	)
	{
		parent::__construct();
	}

	protected function startup(): void
	{
		parent::startup();

		if (isset($this->params['object'])) {
			$this->setObject($this->params['object']);
		}
	}

	/**
	 * @throws AbortException
	 */
	public function actionUnsubscribe(string $email, string $hash): void
	{
		/**
		 * @todo: hash !?
		 */
		if (isset($this->state) && isset($this->stateMessageList[$this->state])) {
			$this->template->stateMessage = $this->stateMessageList[$this->state];
			return;
		}

		$state = 'already';

		if ($hash === 'testHash') {
			$state = 'success';
		} else {
			if ($this->newsletterEmailModel->unsubscribeEmail($email, $this->mutation)) {
				$state = 'success';
			}

			if (($user = $this->orm->user->getBy(['email' => $email])) !== null) {
				if ($this->newsletterEmailModel->unsubscribeUser($user, $this->mutation)) {
					$state = 'success';
				}
			}
		}

		$this->redirect('unsubscribe', ['state' => $state]); // 'unsubscribe' intentionally; 'this' doesn't null params
	}

}
