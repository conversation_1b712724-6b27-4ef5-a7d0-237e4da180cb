<?php declare(strict_types = 1);

namespace App\FrontModule\Presenters\Precart;

use App\Event\AddToCart as AddToCartEvent;
use App\FrontModule\Components\AddToCart\AddToCart;
use App\FrontModule\Components\CartFreeDelivery\CartFreeDelivery;
use App\FrontModule\Components\CartFreeDelivery\CartFreeDeliveryFactory;
use App\FrontModule\Components\ProductList\ProductList;
use App\FrontModule\Presenters\BasePresenter;
use App\Model\BucketFilter\ElasticItem\IsInPath;
use App\Model\BucketFilter\Sort;
use App\Model\Currency\CurrencyHelper;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\PostType\Page\Model\Orm\CommonTree;
use Nette\Http\UrlScript;
use Nette\Utils\ArrayHash;


class PrecartPresenter extends BasePresenter
{
	private ?string $scId = null;

	private ?UrlScript $lastUrl = null;

	private ArrayHash $data;

	private ProductVariant $productVariant;

	public function __construct(
		private readonly CartFreeDeliveryFactory $cartFreeDeliveryFactory,
	)
	{
	}

	protected function startup(): void
	{
		parent::startup();

		$this->scId = $this->getParameter('scId');
		$this->lastUrl = $this->session->getSection(self::class)->get('lastUrl');

		if ($this->scId === null) {
			if ($this->lastUrl !== null) {
				$this->redirectUrl((string) $this->lastUrl);
			}
			$this->redirect($this->mutation->pages->eshop);
		}

		if(($sessionData = $this->session->getSection(AddToCart::class)->get($this->scId)) === null) {
			if ($this->lastUrl !== null) {
				$this->redirectUrl((string) $this->lastUrl);
			}
			$this->redirect($this->mutation->pages->eshop);
		}

		$this->data = ArrayHash::from($sessionData);
		$this->productVariant = $this->orm->productVariant->getById($this->data->variantId);

		if (!$this->isAjax()) {
			$this->session->getSection(self::class)->set('lastUrl', $sessionData['previousUrl'] ?? null);

			if (isset($sessionData['previousUrl'])) {
				$this->lastUrl = $this->template->lastUrl = $sessionData['previousUrl'];
			}
		}
	}

	public function actionDefault(CommonTree $object): void
	{
		$this->setObject($object);
	}

	public function renderDefault(): void
	{
		$this->template->variant = $this->productVariant;
		$this->template->quantity = $this->data->quantity;
		$this->template->quantityAdded = $this->data->quantityAdded;
		$this->template->lastUrl = $this->lastUrl;
		$this->template->isAjax = $this->isAjax();
		$this->template->marketingConsent = $this->marketingConsent->isPersonalizationGranted();

		if ($this->data->quantityAdded === 0) {
			$this->setView('error');
		}

		if (!$this->isAjax()) {
			$this->eventDispatcher->dispatch(
				new AddToCartEvent(
					shoppingCart: $this->shoppingCart,
					productLocalization: $this->productVariant->product->getLocalization($this->mutation),
					variant: $this->productVariant,
					mutation: $this->mutation,
					state: $this->currentState,
					priceLevel: $this->priceLevel,
					currency: CurrencyHelper::getCurrency(),
					quantity: $this->template->quantityAdded,
				)
			);
		}

		if ($this->isAjax()) {
			$this->getPayload()->modalSnippet = $this->getSnippetId('precart');
			$this->redrawControl('precart');
		}

	}

	public function afterRender(): void
	{
		if ($this->scId !== null){
			//$this->session->getSection(AddToCart::class)->remove($this->scId);
		}
	}

	public function createComponentFreeDelivery(): CartFreeDelivery
	{
		return $this->cartFreeDeliveryFactory->create($this->mutationHolder->getMutation(), $this->currentState, $this->priceLevel, $this->userProvider);
	}

	public function createComponentProductListCustomerAlsoBuy(): ProductList
	{
		$product = $this->productVariant->product;
		$productIds = $orderedIds = $cartIds =[];
		if ($this->marketingConsent->isPersonalizationGranted()) {
			$orderedIds = $this->userModel->findOrderedProductIds($this->user);
			$cartIds = $this->shoppingCart->getProductsIds();
			$productIds = $this->userModel->findRelatedOrderedProducts(
				productIds: [$product->id],
				orderFrom: (new \DateTimeImmutable())->modify('-1 year')
			);
		}
		return $this->productListFactory->create($this->currentState, $this->priceLevel, ProductList::TYPE_INTERESTED, $this->userEntity)
		                                ->setIncludeProductIds(array_keys($productIds))
		                                ->setExcludeProductIds([$product->id])
		                                ->setOrderedProductIds($orderedIds)
		                                ->setCartProductIds($cartIds)
		                                ->setLimit(21)
		                                ->setAppendBestselling()
		                                ->setAppendBestsellingWithExclude()
		                                ->setTitle('productList_customer_also_buy')
		                                ->addOnBestsellingElasticItemList(function (array &$items) use($product) : void {
			                                if(($category = $product->mainCategory) !== null){
				                                $items[] = new IsInPath($category);
			                                }
		                                })->addOnSortItem(function (Sort &$sort): void {
											$sort->addByComputedScore();
										});
	}

}
