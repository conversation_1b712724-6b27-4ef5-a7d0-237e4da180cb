{layout none}
<!DOCTYPE html>
<html lang="cs" class="no-js">
<head>
	<meta charset="utf-8">
	<!--[if IE]><meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><![endif]-->
	<meta name="author" content="Autor">

	<meta name="robots" content="noindex, nofollow">
	<meta name="viewport" content="width=device-width, initial-scale=1">

	<title>SuperAdmin</title>

	<link rel="apple-touch-icon" sizes="180x180" href="/static/img/favicon/apple-touch-icon.png">
	<link rel="icon" type="image/png" sizes="32x32" href="/static/img/favicon/favicon-32x32.png">
	<link rel="icon" type="image/png" sizes="16x16" href="/static/img/favicon/favicon-16x16.png">
	<link rel="shortcut icon" href="/static/img/favicon.ico">

	<style>
		html {
			height: 100vh;
			display: flex;
			justify-content: center;
			align-items: center;
			font-family: 'Roboto', sans-serif;
			line-height: 1.7;
			background: #f9f7ed;
			color: #1d1b1a;
			font-size: 62.5%;
		}
		:focus-visible {
			outline: .1rem solid #42a755;
			outline-offset: 0
		}
		body {
			font-size: 1.7rem;
			line-height: calc(26 / 17);
		}
		.b-500 {
			color: #1D1B1A;
			text-align: center;
			max-width: 500px;
			margin: 6rem auto;
		}
		.b-500 h1 {
			font-size: 3.6rem;
			color: #1D1B1A;
			margin: 1.4em 0 .5em;
			display: block;
			font-weight: bold;
			line-height: 1.36;
			letter-spacing: -0.033em;
		}
		.b-500 .btn {
			display: inline-flex;
			justify-content: center;
			align-items: center;
			min-height: var(--btn-height, 5.2rem);
			padding: var(--btn-padding, 1rem 2rem);
			border: 0.1rem solid var(--btn-bd, transparent);
			border-radius: 0.5rem;
			background-color: var(--btn-bg, #42a755);
			color: var(--btn-color, #fff);
			cursor: pointer;
			font-weight: bold;
			font-size: var(--btn-fs, 1.7rem);
			line-height: 1rem;
			text-align: center;
			box-sizing: border-box;
			text-decoration: none;
			transition: background-color .3s,border-color .3s,color .3s,box-shadow .3s;
		}
		.b-500 .btn:focus,
		.hoverevents .b-500 .btn:hover {
			--btn-bg: #3A964A;
			--btn-color: #fff
		}
	</style>

</head>
<body>
<p class="b-500">
	<h1>Něco se u nás rozbilo</h1>
	<p>{_'server_error_500_msg'|noescape}</p>
	<p>
		<button onclick="location.reload(true)" class="btn">Zkusit to znovu</button>
	</p>
</p>

</body>
</html>
