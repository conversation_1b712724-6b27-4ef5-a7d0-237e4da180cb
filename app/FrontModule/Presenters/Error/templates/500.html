
<!DOCTYPE html>
<html lang="cs" class="no-js">
	<head>
		<meta charset="utf-8">
		<!--[if IE]><meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><![endif]-->
		<meta name="author" content="Autor">

		<meta name="robots" content="noindex, nofollow">
		<meta name="viewport" content="width=device-width, initial-scale=1">

		<title>Superadmin</title>

		<link rel="apple-touch-icon" sizes="180x180" href="/static/img/favicon/apple-touch-icon.png">
		<link rel="icon" type="image/png" sizes="32x32" href="/static/img/favicon/favicon-32x32.png">
		<link rel="icon" type="image/png" sizes="16x16" href="/static/img/favicon/favicon-16x16.png">
		<link rel="shortcut icon" href="/static/img/favicon.ico">

		<style>
			html {
				height: 100vh;
				display: flex;
				justify-content: center;
				align-items: center;
				font-family: 'Roboto', sans-serif;
				line-height: 1.6;
				background: #f5f4fa;
				color: #2e3a4d;
				font-size: 62.5%;
			}
			:focus-visible {
				outline: .1rem solid #42a755;
				outline-offset: 0
			}
			body {
				font-size: 1.6rem;
			}
			.b-500 {
				color: #2E3A4D;
				text-align: center;
				max-width: 500px;
				margin: 6rem auto;
			}
			.b-500 h1 {
				font-size: 4rem;
				color: #010e47;
				margin: 0 0 1.2rem;
				display: block;
				font-weight: bold;
			}
			.b-500 p {
				margin: 0 0 2.4rem;
			}
			.b-500 .btn {
				display: inline-flex;
				justify-content: center;
				align-items: center;
				min-height: var(--btn-height, 4.4rem);
				padding: var(--btn-padding, 0.9rem 2.2rem 0.7rem);
				border: 0.1rem solid var(--btn-bd, transparent);
				border-radius: 0.5rem;
				background-color: var(--btn-bg, #3e5efc);
				color: var(--btn-color, #fff);
				cursor: pointer;
				font-weight: bold;
				font-size: var(--btn-fs, 1.4rem);
				line-height: 1.6;
				text-align: center;
				box-sizing: border-box;
				text-decoration: none;
				transition: background-color .3s,border-color .3s,color .3s,box-shadow .3s;
			}
			.b-500 .btn:focus,
			.b-500 .btn:hover {
				--btn-bg: #093ed8;
				--btn-color: #fff
			}
		</style>

	</head>
	<body>
		<div class="b-500">
			<h1>Něco se u nás rozbilo</h1>
			<p>Omlouváme se. Chyba 500 znamená, že náš server přestal fungovat, na opravě pracujeme. Nezbýva než chvilku počkat.</p>
			<p>
				<button onclick="location.reload(true)" class="btn">Zkusit to znovu</button>
			</p>
		</div>

	</body>
</html>
