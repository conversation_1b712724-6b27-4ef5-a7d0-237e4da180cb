<?php declare(strict_types = 1);

namespace App\FrontModule\Presenters\Error;

use App\FrontModule\Components\ProductList\ProductList;
use App\FrontModule\Presenters\BasePresenter;
use App\Model\Orm\Mutation\MutationModel;
use App\Model\StaticPage\Page404Factory;
use App\Model\StaticPage\Page500Factory;
use Nette;
use Nette\Application\Request;
use Throwable;
use Tracy\Debugger;

/**
 * @property Nette\Bridges\ApplicationLatte\DefaultTemplate $template
 */
final class ErrorPresenter extends BasePresenter
{

	protected bool $bypassBasicAuth = true;

	public function __construct(
		private readonly MutationModel $mutationModel,
		private readonly Page404Factory $page404Factory,
		private readonly Page500Factory $page500Factory,
	)
	{
		parent::__construct();
	}

	public function actionDefault(Throwable $exception, ?Request $request = null): void
	{
		// adhoc detect mutation
		$host = $this->getHttpRequest()->getUrl()->getHost();
		$urlPrefix = '';
		foreach ($this->configService->get('mutations') as $m) {
			if (isset($m['urlPrefix']) && $m['urlPrefix'] && str_contains($_SERVER['REQUEST_URI'],	$m['urlPrefix'] . '/')) {
				$urlPrefix = $m['urlPrefix'];
				break;
			}
		}

		$hotsWithoutWWW = str_replace('www.', '', $host);
		$this->mutation = $this->mutationModel->findForRouter($hotsWithoutWWW, $urlPrefix);
		$this->orm->setMutation($this->mutation);
		$this->translator->setMutation($this->mutation);
		$this->mutationHolder->setMutation($this->mutation);

		//Debugger::enable(null, ROOT_DIR . '/nettelog');

		if ($this->isAjax()) { // AJAX request? Just note this error in payload.
			$this->payload->error = true;
			$this->terminate();

		} elseif ($exception instanceof Nette\Application\BadRequestException) {

			$name = $this->translator->translate('page.404.name');
			$annotation = $this->translator->translate('page.404.annotation');
			$description = $this->translator->translate('page.404.description');

			$object = $this->page404Factory->create($this->mutation, $name, $annotation, $description);
			// load template 403.latte or 404.latte or ... 4xx.latte
			$view = in_array($exception->getCode(), [403, 404, 405, 410, 500], true) ? (string) $exception->getCode() : '4xx';

			Debugger::log("HTTP code {$exception->getCode()}: {$exception->getMessage()} in {$exception->getFile()}:{$exception->getLine()}", 'access');
		} else {

			$name = $this->translator->translate('page.500.name');
			$annotation = $this->translator->translate('page.500.annotation');
			$description = $this->translator->translate('page.500.description');

			$object = $this->page500Factory->create($this->mutation, $name, $annotation, $description);
			$view = '500'; // load template 500.latte

			Debugger::log($exception, Debugger::ERROR); // and log exception
		}

		$this->setObject($object);
		$this->template->object = $this->getObject();
		$this->template->alias = $_SERVER['REQUEST_URI'];
		$this->template->message = $exception->getMessage();
		$this->setTemplateData($exception->getCode());

		$this->setView($view);
	}


	private function setTemplateData(int $errorCode): void
	{
		if ($errorCode === 404){
			$this->getTemplate()->add('errorContent', $this->mutationHolder->getMutation()->cf->error404 ?? null);  // @phpstan-ignore-line
		} elseif ($errorCode === 500){
			$this->getTemplate()->add('errorContent', $this->mutationHolder->getMutation()->cf->error500 ?? null);  // @phpstan-ignore-line
		}
	}

	protected function createComponentProductListOthersReading(): ProductList
	{
		$orderedProductIds = $this->userModel->findOrderedProductIds($this->user);
		$cartProductIds = $relatedProductIds = [];
		if ($this->marketingConsent->isPersonalizationGranted()) {
			$cartProductIds = $this->shoppingCart->getProductsIds();

			$relatedProductIds = $this->userModel->findRelatedOrderedProducts(
				productIds: array_merge($orderedProductIds, $this->lastVisitedProduct->getProductIds()),
				orderFrom: (new \DateTimeImmutable())->modify('-1 year')
			);
		}

		return $this->productListFactory->create($this->currentState, $this->priceLevel, ProductList::TYPE_INTERESTED, $this->userEntity)
		                                ->setIncludeProductIds(array_keys($relatedProductIds))
		                                ->setOrderedProductIds($orderedProductIds)
		                                ->setCartProductIds($cartProductIds)
		                                ->setLimit(21)
										->setTitle('productList_others_reading')
		                                ->setAppendBestselling();
	}
}
