{block content}
	{varType App\Model\Orm\Product\Product $product}
	{varType App\Model\Orm\User\User $userEntity}
	{varType App\Model\Orm\ProductVariant\ProductVariant $variant}
	{varType App\Model\Orm\ProductLocalization\ProductLocalization $productLocalization}

	{var $priceVat = $variant->priceVat($mutation, $priceLevel, $state)}

	{control structuredData}

	<div class="row-main">
		{snippetArea productDetail}
			{include $templates.'/part/box/product-detail.latte'}
		{/snippetArea}

		{include $templates.'/part/box/services.latte', showHighlighted: true}
		{include $templates.'/part/box/benefits.latte'}
	</div>

	<div class="row-main">
	{include $templates . '/part/box/content.latte'}
	</div>

		{* Hodnocení *}
		<div class="row-main">
		{embed $templates.'/part/box/product-section.latte', id=>'rating', titleLang=>'product_section_title_rating'}
			{block contentSide}
				{if $allowReview}
				<p class="u-mb-0">
					<a href="{plink $pages->productReviewAdd, productId => $product->id}"
						data-modal='{"medium": "fetch", "modalClass": "b-modal--8-12"}'
						data-snippetid="snippet--productReviewAdd"
						>

						{if $userEntity?->reviews->toCollection()->getBy(['product' => $product])}
							{_"btn_edit_review"}
						{else}
							{_"btn_add_review"}
						{/if}
					</a>
				</p>
				{/if}
			{/block}
			{block content}
				{include $templates.'/part/box/reviews-total.latte', class=>'u-maw-5-12 u-mb-xs u-mb-sm@lg'}
				{include $templates.'/part/crossroad/reviews.latte', class=>'u-maw-8-12'}
			{/block}
		{/embed}
		</div>

{/block}

{* Parametry *}
{* {embed $templates.'/part/box/product-section.latte', id=>'parameters', titleLang=>'product_section_title_parameteres', showParameters:$showParameters}
	{block content}
		<div class="grid grid--y-xs">
			<div n:ifcontent class="grid__cell size--7-12@xl">
				{cache cacheKey('productParameters', $product), expire: '24 hour', tags: $product->getTemplateCacheTags()}
					{control productParameters}
				{/cache}
			</div>
			<div class="grid__cell size--5-12@xl">
				{include $templates.'/part/menu/product-categories.latte'}
			</div>
		</div>
	{/block}
{/embed} *}

{* Hodnocení *}
{* {if !$product->isCertificate}
	{embed $templates.'/part/box/product-section.latte', id=>'rating', titleLang=>'product_section_title_rating'}
		{block contentSide}
			<p class="u-mb-0">
				<a href="{plink $pages->productReviewAdd, productId => $product->id}" data-modal='{"medium": "fetch", "modalClass": "b-modal--8-12"}' data-snippetid="snippet--productReviewAdd">
					{if $userEntity?->reviews->toCollection()->getBy(['product' => $product])}
						{_"btn_edit_review"}
					{else}
						{_"btn_add_review"}
					{/if}
				</a>
			</p>
		{/block}
		{block content}
			{include $templates.'/part/box/reviews-total.latte', class=>'u-maw-5-12 u-mb-xs u-mb-sm@lg'}
			{include $templates.'/part/crossroad/reviews.latte', class=>'u-maw-8-12'}
		{/block}
	{/embed}
{/if} *}

{* Mohlo by vás zajímat *}
{* {if $marketingConsent}
	{snippet productDetailListInterested}
		{if !count($presenter->lastVisitedProduct->getProductIds()) == 0}
			{php $control['productListInterested']->setTemplateParameters(['class' => 'section--products section--w-line'])}
		{/if}
		{control productListInterested}
	{/snippet}
{/if} *}
