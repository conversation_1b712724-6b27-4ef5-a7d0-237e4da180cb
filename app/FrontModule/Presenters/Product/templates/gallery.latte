{block content}
	{snippet productPreview}
		{capture $content}
			{dump $images}
			<p class="b-book u-mb-0" data-controller="book">
				{foreach $images as $image}
					<img n:class="$iterator->counter > 2 ? u-vhide" src="{$image->getSize('xl')->src}" alt="">
				{/foreach}
			</p>
		{/capture}

		{*THICKBOX*}
		{if $presenter->isAjax()}
			{$content}
		{else}
			<div class="row-main">
				{control breadcrumb}
				<div class="u-maw-8-12 u-mx-auto u-mb-last-0 u-pt-sm@md u-mb-md u-mb-2xl@md">
					{include $templates.'/part/box/annot.latte'}
					{include $templates.'/part/crossroad/std.latte'}
					{include $templates.'/part/box/content.latte'}

					{$content}
				</div>
			</div>
		{/if}
	{/snippet}
{/block}