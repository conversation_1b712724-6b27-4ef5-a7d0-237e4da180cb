{varType App\Model\Orm\Order\Order $order}
{var $isOnlinePayment = $order->payment->information->isOnline()}
{var $paymentType = $order->payment->paymentMethod->getPaymentMethod()->getPaymentType()}
{var $deliveryType = $order->delivery->deliveryMethod->getDeliveryMethod()->getDeliveryType()}
{var $cardPaymentTypes = [
	App\Model\Orm\Order\Payment\PaymentType::Card,
]}
{default $cfContact = $pages->contact->cf->contacts ?? false}

{var $isFailedPayment = $isOnlinePayment && in_array($paymentType, $cardPaymentTypes) && $order->payment->information->state === App\Model\Orm\Order\Payment\PaymentState::Failed}
{var $isSuccessPayment = $isOnlinePayment && in_array($paymentType, $cardPaymentTypes) && $order->payment->information->state === App\Model\Orm\Order\Payment\PaymentState::Completed}

{block content}
	<div class="row-main u-pt-xs u-pt-lg@md">
		{* Poděkovámí a informace *}
		<div class="b-thankyou tw-mb-[3.2rem] md:tw-mb-[8rem]">
			{* Nadpis *}
			<div class="u-mb-last-0 tw-text-center tw-mb-[1.2rem] md:tw-mb-[2.8rem]">
				{if $isSuccessPayment}
					<h1 class="tw-text-green tw-mb-[0.8rem] md:tw-mb-[1.2rem] tw-text-[2.2rem] md:tw-text-[3.3rem] tw-leading-[1.4] u-maw-5-12 u-mx-auto">
						{_'order_status_payment_success'}
					</h1>
					<p class="md:tw-text-[1.7rem] u-maw-8-12 u-mx-auto">
						{_"order_text_payment_success"}
					</p>
				{elseif $isFailedPayment}
					<h1 class="tw-text-status-invalid tw-mb-[0.8rem] md:tw-mb-[1.2rem] tw-text-[2.2rem] md:tw-text-[3.3rem] tw-leading-[1.4] u-maw-5-12 u-mx-auto">
						{_'order_status_payment_failed'}
					</h1>
					<p class="md:tw-text-[1.7rem] u-maw-8-12 u-mx-auto">
						{_"order_text_payment_error"}
					</p>
				{else}
					<h1 class="tw-text-green tw-mb-[0.8rem] md:tw-mb-[1.2rem] tw-text-[2.2rem] md:tw-text-[3.3rem] tw-leading-[1.4] u-maw-5-12 u-mx-auto">
						{_'title_order_success'}
					</h1>
					<p class="md:tw-text-[1.7rem] u-maw-8-12 u-mx-auto">
						{_"order_text_completed"}
					</p>
				{/if}
			</div>

			{* Box s informacemi *}
			<div class="u-maw-8-12 u-mx-auto {if $isFailedPayment}tw-bg-status-invalid-light{else}tw-bg-status-valid-light{/if} tw-rounded-md md:tw-rounded-xl tw-p-[2.4rem_1.6rem] md:tw-p-[4.8rem_6rem]">
				<div class="tw-flex tw-items-center md:tw-items-start tw-gap-[1.2rem_2.8rem] max-md:tw-flex-col">
					<p class="u-mb-0 tw-bg-white tw-rounded-md tw-p-[1.2rem] tw-text-center md:tw-flex-[2] tw-relative tw-w-full tw-max-w-[37rem]
						before:tw-content-[''] before:tw-absolute before:tw-size-[2rem] before:tw-rounded-full before:tw-top-[50%] before:tw-right-[0] before:tw--translate-y-1/2 before:tw-translate-x-1/2 {if $isFailedPayment}before:tw-bg-status-invalid-light{else}before:tw-bg-status-valid-light{/if}
						after:tw-content-[''] after:tw-absolute after:tw-size-[2rem] after:tw-rounded-full after:tw-top-[50%] after:tw-left-[0] after:tw--translate-y-1/2 after:tw--translate-x-1/2 {if $isFailedPayment}after:tw-bg-status-invalid-light{else}after:tw-bg-status-valid-light{/if}"
					>
						<span class="tw-text-help tw-text-[1.4rem]">{_order_no}:</span>
						<b class="tw-block">{$order->orderNumber}</b>
					</p>
					<p class="u-mb-0 md:tw-flex-[3] max-md:tw-text-center">
						{default $cfPhone = $cfContact->phone ?? ''}
						{capture $phone}<a href="tel:+420{$cfPhone|replace:' ',''}" class="tw-no-underline {if $isFailedPayment}tw-text-status-invalid{else}tw-text-status-valid{/if}"><b>+420 {$cfPhone}</b></a>{/capture}
						{_"order_email_send"|noescape|replace:'%phone', $phone->__toString()}
					</p>
				</div>

				{* Platba / Osobní vyzvednutí *}
				{if $isSuccessPayment}
				{elseif $isFailedPayment}
					<p class="tw-text-center tw-mb-0 tw-mt-[2.8rem] md:tw-mt-[4rem]">
						<a n:href="pay!, orderHash: $order->hash, orderId: $order->id" class="btn btn--secondary btn--lg">
							<span class="btn__text">
								{_"btn_repeat_payment"}
							</span>
						</a>
					</p>
				{elseif !$order->isPaid() && !in_array($presenter->getParameter('paymentStatus'), ['PAID'])}
					<hr class="tw-my-[2rem] md:tw-my-[4rem] tw-border-gray-900 tw-opacity-15">

					{* Platba *}
					{if $paymentType === App\Model\Orm\Order\Payment\PaymentType::BankTransfer}
						{* Platba převodem *}
						{include $templates.'/part/box/payment-instructions.latte'}
					{elseif in_array($paymentType, $cardPaymentTypes)}
						{* Platba kartou *}
						{if $order->payment->information->state === App\Model\Orm\Order\Payment\PaymentState::Pending}
							{var $deliveryMessage = 'Expedovat budeme jakmile dorazí platba.'}
							{if $paymentType === App\Model\Orm\Order\Payment\PaymentType::BenefitCard}
								<p class="u-mb-xxs u-mb-xs@md">
									{_"payment_instructions_card_benefit_text"|noescape|replace:'%amount', App\Infrastructure\Latte\Filters::formatMoney($order->getTotalPriceWithDeliveryVat())}
								</p>
								<p>
									{control orderBenefitCardPay}
								</p>
							{else}
								<p class="u-mb-xxs u-mb-xs@md">
									{_"payment_instructions_card_text"|noescape|replace:'%amount', App\Infrastructure\Latte\Filters::formatMoney($order->getTotalPriceWithDeliveryVat())}
								</p>
								<p>
									<a n:href="pay!, orderHash: $order->hash, orderId: $order->id" class="btn btn--md" target="_blank" rel="noopener noreferrer">
										<span class="btn__text">
											{_"btn_pay_card"}
										</span>
									</a>
								</p>
							{/if}
						{elseif $order->payment->information->state === App\Model\Orm\Order\Payment\PaymentState::Failed}
							{var $deliveryMessage = 'Expedovat budeme jakmile dorazí platba.'}
							<p class="u-mb-xxs u-mb-xs@md">
								<a n:href="pay!, orderHash: $order->hash, orderId: $order->id" class="btn">
									<span class="btn__text">{_"btn_pay_card_repeat"}</span>
								</a>
							</p>
						{elseif $order->payment->information->state === App\Model\Orm\Order\Payment\PaymentState::Unknown}
							<h2 class="h4 u-mb-xxs u-mb-xs@md">
								{_"payment_instructions_card_unknown_title"}
							</h2>
							<p class="u-mb-xxs u-mb-xs@md">
								{_"payment_instructions_card_unknown_text"|noescape}
							</p>
						{/if}
					{/if}

					{* Osobní vyzvednutí *}
					{if $deliveryType === App\Model\Orm\Order\Delivery\DeliveryType::Store}
						{include store-info.latte cfContact=>$cfContact}
					{/if}

				{/if}
			</div>
		</div>

		{* <p>
			TODO: na poslední chvíli
		</p> *}

		<hr class="u-mb-sm u-mb-lg@md">
		{* {include $templates.'/part/box/services.latte', showHighlighted: true} *}
		{include $templates.'/part/box/benefits.latte'}
	</div>
{/block}
