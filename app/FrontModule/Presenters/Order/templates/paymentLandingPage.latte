{block content}
{varType App\Model\Orm\Order\Order $order}

<div class="row-main">
	<div class="u-mb-last-0 u-pt-xs u-pt-lg@md u-mb-lg u-mb-xl@md">
		<div class="b-thankyou u-maw-9-12 u-mx-auto u-mb-last-0 u-mb-sm u-mb-xl@md">
			{if !$order->isPaid()}
				<h1 class="b-thankyou__title u-ta-c item-icon u-c-orange u-mb-xxs u-mb-xs@md">
					{('alert-circle')|icon, 'item-icon__icon'}
					<span class="item-icon__text">
						{translate('title_order_not_paid', ['%order%' => $order->orderNumber])}
					</span>
				</h1>
			{else}
				<h1 class="b-thankyou__title u-ta-c item-icon u-c-green u-mb-xxs u-mb-xs@md">
					{('check-circle')|icon, 'item-icon__icon'}
					<span class="item-icon__text">
						{translate('title_order_paid', ['%order%' => $order->orderNumber])}
					</span>
				</h1>
			{/if}

			{if !$order->isPaid()}
				{* Platba *}
				{if $order->payment->paymentMethod->getPaymentMethod()->getPaymentType() === App\Model\Orm\Order\Payment\PaymentType::BankTransfer}
					{* Platba převodem *}
					{embed $templates.'/part/box/pay.latte', class: 'u-mb-xxs u-mb-xs@md'}
						{block content}
							<h2 class="h4 u-mb-xxs">
								{_"payment_instructions_title"}
							</h2>
							<div class="grid">
								<div class="grid__cell size--6-12@md">
									{var $paymentAccount = $order->getPaymentAccount() ?? new \stdClass()}
									<dl class="b-pay__list">
										{if isset($paymentAccount->bban) && $paymentAccount->bban !== ""}
											{*$paymentAccount->bban*}
											<dt>{_"account_number"}:</dt>
											<dd>{$paymentAccount->bban}</dd>
										{else}
											<dt>{_"account_iban"}:</dt>
											<dd>
												{$paymentAccount?->iban ?? '-'}
											</dd>
											<dt>{_"account_swift"}:</dt>
											<dd>
												{$paymentAccount?->swift ?? '-'}
											</dd>
										{/if}

										<dt>{_"variable_symbol"}:</dt>
										<dd>{$order->payment->information->variableSymbol}</dd>
										<dt>{_"price"}:</dt>
										<dd>{$order->getTotalPriceWithDeliveryVat()|money}</dd>
									</dl>
								</div>
								<div class="grid__cell size--6-12@md">
									<dl class="b-pay__list">
										<dt>{_"qr_pay"}:</dt>
										<dd><img n:if="($qrCode = $order->payment->paymentMethod->getPaymentMethod()->getQrCode($order)) !== null" src="{$qrCode|nocheck}" alt="" loading="lazy" width="130" height="130"></dd>
									</dl>
								</div>
							</div>
						{/block}
					{/embed}
				{elseif $order->payment->paymentMethod->getPaymentMethod()->getPaymentType() === App\Model\Orm\Order\Payment\PaymentType::Card}
					{* Platba kartou *}
					{if $order->payment->information->state === App\Model\Orm\Order\Payment\PaymentState::Pending}
						{embed $templates.'/part/box/pay.latte', class: 'u-mb-xxs u-mb-xs@md'}
							{block content}
								<h2 class="h4">
									{_"payment_instructions_card_title"}
								</h2>
								<p class="u-mb-xxs u-mb-xs@md">
									{_"payment_instructions_card_text"|noescape|replace:'%amount', App\Infrastructure\Latte\Filters::formatMoney($order->getTotalPriceWithDeliveryVat())}
								</p>
								<p>
									<a n:href="pay!, orderHash: $order->hash, orderId: $order->id" class="btn btn--md" target="_blank" rel="noopener noreferrer">
										<span class="btn__text">
											{_"btn_pay_card_repeat"}
										</span>
									</a>
								</p>
							{/block}
						{/embed}
					{elseif $order->payment->information->state === App\Model\Orm\Order\Payment\PaymentState::Failed}
						{embed $templates.'/part/box/pay.latte', class: 'u-mb-xxs u-mb-xs@md'}
							{block content}
								<h2 class="h4 u-mb-xxs u-mb-xs@md">
									{_"order_status_payment_failed"}
								</h2>
								<div class="grid grid--x-xs grid--y-xxs grid--middle">
									<div class="grid__cell size--auto">
										<p class="u-mb-0">
											<a n:href="pay!, orderHash: $order->hash, orderId: $order->id" class="btn">
												<span class="btn__text">
													{_"btn_pay_card"}
												</span>
											</a>
										</p>
									</div>
									<div class="grid__cell size--autogrow">
										<p class="u-mb-0">
											<a n:href="changeToBankPayment!, orderHash: $order->hash">{_"btn_change_to_bank_transfer"|noescape}</a><br>
										</p>
									</div>
								</div>
							{/block}
						{/embed}
					{elseif $order->payment->information->state === App\Model\Orm\Order\Payment\PaymentState::Unknown}
						{embed $templates.'/part/box/pay.latte', class: 'u-mb-xxs u-mb-xs@md'}
							{block content}
								<h2 class="h4 u-mb-xxs u-mb-xs@md">
									{_"payment_instructions_card_unknown_title"}
								</h2>
								<p class="u-mb-xxs u-mb-xs@md">
									{_"payment_instructions_card_unknown_text"|noescape}
								</p>
							{/block}
						{/embed}
					{/if}
				{/if}
			{else}
				{embed $templates.'/part/box/pay.latte', class: 'u-mb-xxs u-mb-xs@md', type: 'success'}
					{block content}
						<p class="h4">
							{_"payment_instructions_card_ok_title"}
						</p>
					{/block}
				{/embed}
			{/if}
		</div>
	</div>
</div>
