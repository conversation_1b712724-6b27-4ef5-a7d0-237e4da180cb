{default $cfContact = $pages->contact->cf->contacts ?? false}
{if $cfContact}
	<div class="b-thankyou__pickup tw-mb-0 tw-rounded-xl tw-relative tw-overflow-hidden tw-p-[2.4rem] md:tw-p-[6rem] md:tw-text-[1.5rem]">
		{php $bg = isset($cfContact->store_image) ? $cfContact->store_image->getEntity() ?? false : false}
		<img n:if="$bg" class="tw-absolute tw-inset-0 img img--fit" src="{$bg->getSize('xl-2-1')->src}" alt="{$bg->getAlt($mutation)}" loading="lazy">
		<h2 class="tw-text-white tw-text-center tw-mt-0 tw-mb-[1.6rem] md:tw-mb-[2rem] tw-relative tw-z-[1]">{_store_pickup_title}</h2>
		<p class="u-maw-3-12 u-mx-auto tw-mb-0 tw-bg-bg tw-bg-opacity-90 tw-rounded-lg tw-p-[2rem_2.4rem] md:tw-p-[2.4rem_2.8rem] tw-relative tw-z-[1] tw-flex tw-gap-[1rem] tw-items-start">
			<span class="tw-flex-1 tw-border-r tw-border-t-0 tw-border-b-0 tw-border-l-0 tw-border-solid tw-border-tile tw-pr-[1rem]">
				{if $cfContact->store_address_full ?? false}
					{$cfContact->store_address_full|texy|noescape}
				{/if}
				{if $cfContact->phone_hours ?? false}
					{$cfContact->phone_hours}
				{/if}
				<a href="#" class="tw-block">{_"more_about_store"}</a>
			</span>
			{('pin-outline')|icon, 'tw-text-primary tw-w-[4rem]'}
		</p>
	</div>
{/if}
