<?php declare(strict_types = 1);

namespace App\FrontModule\Presenters\Order;

use App\Event\BeginCheckout;
use App\Event\OrderSubmit;
use App\Event\ViewCart;
use App\FrontModule\Components\Cart\Cart;
use App\FrontModule\Components\Cart\CartFactory;
use App\FrontModule\Components\OrderBenefitCardPay\OrderBenefitCardPay;
use App\FrontModule\Components\OrderBenefitCardPay\OrderBenefitCardPayFactory;
use App\FrontModule\Components\OrderCreateUser\OrderCreateUser;
use App\FrontModule\Components\OrderCreateUser\OrderCreateUserFactory;
use App\FrontModule\Presenters\BasePresenter;
use App\Model\Orm\CardPayment\CardPayment;
use App\Model\Orm\CardPayment\CardPaymentProcessor;
use App\Model\Orm\Order\OrderModel;
use App\Model\Orm\Order\Payment\BankTransferPaymentInformation;
use App\Model\Orm\Order\Payment\CardPaymentInformation;
use App\Model\Orm\PaymentMethod\BankTransfer;
use App\Model\Orm\PaymentMethod\Card;
use App\PostType\Page\Model\Orm\CommonTree;
use Nette\Application\BadRequestException;

class OrderPresenter extends BasePresenter
{

	public function __construct(
		private readonly CardPaymentProcessor $cardPaymentProcessor,
		private readonly OrderCreateUserFactory $orderCreateUserFactory,
		private readonly OrderBenefitCardPayFactory $orderBenefitCardPayFactory,
		private readonly OrderModel $orderModel,
		private readonly CartFactory $cartFactory,
	)
	{
		parent::__construct();
	}

	protected function startup(): void
	{
		parent::startup();

		if (!$this->isAjax()) {
			$this->shoppingCart->storeVisit();
		}
	}

	public function actionDefault(CommonTree $object): void
	{
		$this->setObject($object);
		$changed = $this->shoppingCart->getChangedItems(onlyProducts: true, onlyToRemove: true);

		if ($changed !== [] && $this->getParameter('refreshCart') === null && !$this->isAjax()) {
			$this->redirect($this->mutation->pages->cart, ['refreshCart' => 0]);
		}
	}

	public function renderDefault(): void
	{
		$this->eventDispatcher->dispatch(new ViewCart($this->shoppingCart));
	}

	public function actionStep1(): void
	{
		$this->getHttpResponse()->setHeader('Permissions-Policy', 'geolocation=(self)');
		$this->setObject($this->mutation->pages->step1);

		if ($this->shoppingCart->isEmpty()) {
			$this->redirect($this->mutation->pages->cart);
		}

		if ($this->shoppingCart->getChangedItems(onlyProducts: true) !== []) {
			$this->redirect($this->mutation->pages->cart, ['refreshCart' => 0]);
		}
	}

	public function renderStep1(): void
	{
		$this->eventDispatcher->dispatch(new BeginCheckout($this->shoppingCart));
	}

	public function actionStep2(): void
	{
		$this->setObject($this->mutation->pages->step2);

		if ($this->shoppingCart->isEmpty()) {
			$this->redirect($this->mutation->pages->cart);
		}

		if (!$this->shoppingCart->hasPayment() || !$this->shoppingCart->hasDelivery()) {
			$this->redirect($this->mutation->pages->step1);
		}

		if ($this->shoppingCart->getChangedItems(onlyProducts: true) !== []) {
			$this->redirect($this->mutation->pages->cart, ['refreshCart' => 0]);
		}
	}

	public function actionStep3(?int $orderId, ?string $orderHash): void
	{
		$this->setObject($this->mutation->pages->step3);

		$order = $this->orm->order->getBy(['id' => $orderId, 'hash' => $orderHash]);

		if ($order === null) {
			throw new BadRequestException('Order not found.');
		}

		$this->template->order = $order;
	}
	public function renderStep3(?int $orderId, ?string $orderHash): void
	{
		$this->eventDispatcher->dispatch(new OrderSubmit($this->template->order));
	}

	public function actionPaymentLandingPage(?string $orderHash): void
	{
		if (empty($orderHash)) {
			throw new BadRequestException('Order not found.');
		}

		$this->setObject($this->mutation->pages->step3);

		$order = $this->orm->order->getBy(['hash' => $orderHash]);

		if ($order === null) {
			throw new BadRequestException('Order not found.');
		}

		if (!$order->isDraft()) {
			if ($order->payment->information instanceof CardPaymentInformation) {
				/** @var CardPayment $cardPayment */
				foreach ($order->payment->information->payments as $cardPayment) {
					$this->cardPaymentProcessor->processCheckPayment($cardPayment);
				}
			}
		}

		$this->template->order = $order;
	}

	public function handlePay(?int $orderId, ?string $orderHash): void
	{
		$order = $this->orm->order->getBy(['id' => $orderId, 'hash' => $orderHash]);
		if ($order === null) {
			throw new BadRequestException('Order not found.');
		}

		$cardPayment = $this->orderModel->createCardPayment($order);
		$this->redirectUrl($cardPayment->externalUrl ?? $this->link('this'));
	}

	public function handleChangeToBankPayment(string $orderHash): void
	{
		$order = $this->orm->order->getBy(['hash' => $orderHash]);
		if ($order !== null && $order->payment->information instanceof CardPaymentInformation) {
			$this->orderModel->changePayment($order, BankTransfer::ID);
		}

		$this->redirect('this', ['orderHash' => $order->hash, 'orderId' => $order->id]);
	}

	public function handleChangeToCardPayment(string $orderHash, bool $redirectToGate = false): void
	{
		$order = $this->orm->order->getBy(['hash' => $orderHash]);
		if ($order !== null && $order->payment->information instanceof BankTransferPaymentInformation) {
			$this->orderModel->changePayment($order, Card::ID);
		}
		if ($redirectToGate) {
			$this->redirect('pay', ['orderId' => $order->id, 'orderHash' => $order->hash]);
		}
		//$this->flashMessage('order_change_to_card_payment_success', 'ok');
		$this->redirect('this', ['orderHash' => $order->hash, 'orderId' => $order->id]);
	}

	public function createComponentOrderCreateUser(): OrderCreateUser
	{
		return $this->orderCreateUserFactory->create($this->orm->order->getBy(['hash' => $this->getParameter('orderHash')]));
	}

	public function createComponentOrderBenefitCardPay(): OrderBenefitCardPay
	{
		return $this->orderBenefitCardPayFactory->create($this->orm->order->getBy(['hash' => $this->getParameter('orderHash')]));
	}

	public function createComponentCart(): Cart
	{
		return $this->cartFactory->create($this->userProvider, $this->setup->state, $this->setup->priceLevel); //->setShowFlashes();
	}

}
