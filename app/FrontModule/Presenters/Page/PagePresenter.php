<?php

declare(strict_types=1);

namespace App\FrontModule\Presenters\Page;


use App\FrontModule\Components\CustomContentRenderer\HasCustomContentRenderer;
use App\FrontModule\Components\PhotoBank\PhotoBank;
use App\FrontModule\Components\PhotoBank\PhotoBankFactory;
use App\FrontModule\Presenters\BasePresenter;
use App\Model\Currency\CurrencyHelper;
use App\PostType\Page\Model\Orm\Tree;
use Nextras\Orm\Collection\ICollection;

/**
 *  @method Tree getObject()
 */
final class PagePresenter extends BasePresenter
{
	use HasCustomContentRenderer;

	public function __construct(
		private readonly PhotoBankFactory $photoBankFactory,
	)
	{
	}

	protected function startup(): void
	{
		parent::startup();

		if ($this->params['object'] instanceof Tree) {
			$this->setObject($this->params['object']);
		}

		$object = $this->getObject();
		\assert($object instanceof Tree);

		$this->template->isDetail = (bool) $object->last;

		//bd($this->getGlobalState());
	}


	public function actionContact(): void
	{
	}

	public function renderDeliveryAndPayment(): void
	{
		$this->template->deliveries = $this->orm->deliveryMethod->findBy(['public' => 1, 'pageShow' => 1, 'mutation' => $this->mutation, 'currencies->currency' => CurrencyHelper::getCurrencyCode()])->orderBy('sort', ICollection::ASC);
		$this->template->payments = $this->orm->paymentMethod->findBy(['public' => 1, 'pageShow' => 1, 'mutation' => $this->mutation, 'currencies->currency' => CurrencyHelper::getCurrencyCode()])->orderBy('sort', ICollection::ASC);
	}

	public function actionDefault(): void
	{
		if ($this->isAjax()) {
			$this->presenter->setLayout(FALSE);
		}

		// ??? neni ID
//		if ($this->getObject()->uid === "newsletter2") {
//			$session = $this->session->getSection('newsletter');
//			if (!$session->newsletterId) {
////				$this->redirectUrl("/");
//				$this->template->hideForm = TRUE;
//			}
//		}
	}



	public function renderDefault(): void
	{
//		$this->addComponent($this->visualPaginatorFactory->create(), 'pager');
//		$paginator = $this['pager']->getPaginator();
//		$paginator->itemsPerPage = $this->configService->getParam('pagesPaging');
//		$cfg = array('offset' => $paginator->offset, 'limit' => $paginator->itemsPerPage);
//		$items = $this->treeService->fetchCrossroad($this->getObject()->id, $cfg);
//		$paginator->itemCount = $items->count;
//		$this->template->crossroad = $items;

		if ($this->isAjax()) {
			if ($this->getParameter('fancybox')) {
				$this->redrawControl('content');
			} else {
			//	$this->redrawControl();
			}
		}

	}

	public function createComponentPhotoBank(): PhotoBank
	{
		return $this->photoBankFactory->create($this->getObject(), $this->setup);
	}
}
