{block content}
	{snippet content}
		<div class="row-main">
			{embed $templates.'/part/box/header.latte', class: 'u-mb-sm u-mb-md@md', object: $object, templates: $templates}
				{block side}
					<form action="?" class="b-header__search f-search">
						<p class="f-search__wrap u-mb-0">
							<span class="f-search__inp-fix inp-fix">
								<label for="search" class="u-vhide">{_"form_label_search_faq"}</label>
								<input type="search" id="search" name="search" class="f-search__inp inp-text" placeholder="{_'form_label_search_faq'}">
							</span>
							<button type="submit" class="f-search__btn btn btn--icon">
								<span class="btn__text">
									{('search')|icon, 'btn__icon'}
									<span class="u-vhide">{_"btn_search"}</span>
								</span>
							</button>
						</p>
					</form>
				{/block}
			{/embed}
			<div class="u-mb-md u-mb-xl@lg">
				<div class="grid grid--x-sm grid--y-sm">
					<div class="grid__cell size--8-12@lg size--9-12@xl u-mb-last-0">
						<h2 class="h3 tw-mb-[0.8rem]">
							{_"faq_title_alphabet"}
						</h2>
						<p class="tw-mb-[1.2rem] md:tw-mb-[2.4rem]">
							{_"faq_annot_alphabet"}
						</p>
						{include $templates.'/part/crossroad/alphabet.latte', class: 'tw-mb-[2.8rem] md:tw-mb-[5.2rem]'} {* TODO BE *}
						<h2 class="h3 u-mt-0 tw-mb-[0.8rem]">
							{_"faq_title_categories"}
						</h2>
						<p class="tw-mb-[1.2rem] md:tw-mb-[2.4rem]">
							{_"faq_annot_categories"}
						</p>
						{include $templates.'/part/crossroad/std.latte', crossroad: $pages->eshop->crossroad} {* TODO BE *}
					</div>
					<div class="grid__cell size--4-12@lg size--3-12@xl">
						{include $templates.'/part/box/help.latte', class: 'b-help--big tw-h-full'}
					</div>
				</div>
			</div>
			{control customContentRenderer}
			<hr class="u-mb-sm u-mb-lg@md">
			{include $templates.'/part/box/services.latte'}
			{include $templates.'/part/box/benefits.latte'}
		</div>
	{/snippet}
{/block}
