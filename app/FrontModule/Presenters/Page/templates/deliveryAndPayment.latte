{block content}
	{snippet content}
		<div class="row-main">
			<div class="u-maw-8-12 u-mx-auto u-mb-last-0 u-pt-xs u-mb-md u-mb-2xl@md">
				{include $templates.'/part/box/annot.latte', class=>'b-annot--spec u-mb-xxs u-mb-md@md'}

				<h2 class="u-mb-xxs u-mb-sm@md">
						{_"title_delivery_options"}
				</h2>

				<div class="f-method f-method--static f-method--deliveryMethod u-mb-xs u-mb-sm@md">
					<ul class="f-method__list">
						{foreach $deliveries as $item}
							{varType App\Model\Orm\DeliveryMethod\DeliveryMethodConfiguration $item}
							{var $price = $item->getPrice($priceLevel, $state, App\Model\Currency\CurrencyHelper::getCurrency())}
							{continueIf $price === null}
							{include $templates.'/part/form/part/method-item.latte',
							class: false,
							name:'deliveryMethod',
							title: $item->name,
							imgs: $item->cf->deliveryPayment??->icon ?? [],
							tooltipContent:$item->tooltip,
							price: $price->getPrice(),
							templates: $templates,
							isStatic: true,
							freeFrom: $price->getFreeFrom() ?? false,
							}
						{/foreach}
					</ul>
				</div>
				<div n:foreach="$deliveries as $item" class="u-mb-last-0 u-mb-xs u-mb-sm@md">
						<h3>
							{$item->name}
						</h3>
						{$item->pageText ?? ''|noescape}
				</div>

				{include $templates.'/part/box/content.latte', content: $object->cf->delivery_content?->content ?? false}

				<h2 class="u-mb-xxs u-mb-sm@md">
					{_"title_payment_options"}
				</h2>
				<div class="f-method f-method--static f-method--paymentMethod u-mb-xs u-mb-sm@md">
					<ul class="f-method__list">
						{foreach $payments as $item}
							{varType App\Model\Orm\PaymentMethod\PaymentMethodConfiguration $item}
							{var $price = $item->getPrice($priceLevel, $state, App\Model\Currency\CurrencyHelper::getCurrency())}
							{continueIf $price === null}
							{include $templates.'/part/form/part/method-item.latte',
							class: false,
							name:'paymentMethodMethod',
							title: $item->name,
							imgs: $item->cf->deliveryPayment??->icon ?? [],
							tooltipContent:$item->tooltip,
							price: $price->getPrice(),
							templates: $templates,
							isStatic: true,
							}
						{/foreach}
					</ul>
				</div>
				<div n:foreach="$payments as $payment" class="u-mb-last-0 u-mb-xs u-mb-sm@md">
					<h3>
						{$payment->name}
					</h3>
					{$payment->pageText ?? ''|noescape}
				</div>
			</div>
		</div>
	{/snippet}
{/block}
