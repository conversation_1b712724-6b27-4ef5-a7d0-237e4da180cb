{block content}
	{snippet content}
		<div class="row-main">
			{include $templates.'/part/box/header-ebook.latte'}
			{control customContentRenderer, [ccClass: 'u-maw-8-12 u-mx-auto']}

			{php $personCtaCf = $object->cf->person_cta_ebook ?? false}
			{default $headerCf = $object->cf->header_ebook ?? false}
			{php $file = isset($headerCf->file) ? $headerCf->file->getEntity() ?? false : false}

			{embed $templates.'/part/box/person-cta.latte', person: isset($personCtaCf->person) ? $personCtaCf->person->getEntitY() ?? false : false, content: $personCtaCf->content ?? false}
				{block content}
					<p n:if="$file" class="u-ta-c">
						<a href="#ebook" class="btn btn--secondary" data-modal='{"modalClass": "b-modal--4-12"}'>
							<span class="btn__text">
								<span class="btn__inner">
									{('download')|icon, 'btn__icon'}
									{_"btn_download_ebook"}
								</span>
							</span>
						</a>
					</p>
				{/block}
			{/embed}

			<div class="u-js-hide">
				<div id="ebook">
					<div class="row-main">
						{if $user->isLoggedIn()}
							{* TODO: podmínka pokud odebírá newsletter *}
							{* Má registraci a odebírá newsletter *}
							{* <div class="b-ebook-download">
								<h2 class="h3">
									{_'ebook_download_subscribed_title'}
								</h2>
								<p>
									{_'ebook_download_subscribed_annot'}
								</p>
								<p class="b-file item-icon link-mask">
									{('file')|icon, 'item-icon__icon'}
									<span class="item-icon__text">
										<a href="#" class="u-d-b link-mask__link">Nová pravidla pro drony.pdf</a>
										<span class="u-c-help">(15.2 MB)</span>
									</span>
								</p>
								<hr>
								<p class="b-ebook-download__info item-icon">
									{('info-outline')|icon, 'item-icon__icon'}
									<span class="item-icon__text">
										{_'ebook_download_subscribed_info'|noescape|replace:'%link', '#'} TODO link
									</span>
								</p>
							</div> *}

							{* Má registraci, ale neodebírá newsletter *}
							<div class="b-ebook-download">
								<h2 class="h3">
									{_'ebook_download_not_subscribed_title'}
								</h2>
								<p n:if="isset($pages->personalData)">
									{capture $link}{plink $pages->personalData}{/capture}
									{* TODO: email *}
									{_'ebook_download_not_subscribed_annot'|noescape|replace:'%mail','<EMAIL>'|replace:'%link',$link->__toString()}
								</p>
								<label class="inp-item inp-item--checkbox">
									<input type="checkbox" name="subscribe" value="1" class="inp-item__inp">
									<span class="inp-item__text">
										{_"form_label_subscribe"}
									</span>
								</label>
								<p class="u-mb-0">
									<a href="#" class="btn btn--block btn--secondary btn--lg">
										<span class="btn__text">
											{_"btn_ebook_want"}
										</span>
									</a>
								</p>
							</div>
						{else}
							{* Nemá registrace *}
							<div class="b-ebook-download">
								<h2 class="h3>
									{_'ebook_download_unregistered_title'}
								</h2>
								<p n:if="isset($pages->personalData)">
									{capture $link}{plink $pages->personalData}{/capture}
									{_'ebook_download_unregistered_annot'|noesccape|replace:'%link', $link->__toString()}
								</p>
								<form action="?">
									<p>
										<label for="mail" class="inp-label u-fw-n">
											{_"form_label_email_address"}
										</label>
										<span class="inp-fix">
											<input type="text" name="mail" id="mail" class="inp-text">
										</span>
									</p>
									<p class="u-mb-0">
										<a href="#" class="btn btn--block btn--secondary btn--lg">
											<span class="btn__text">
												{_"btn_ebook_want"}
											</span>
										</a>
									</p>
								</form>
							</div>
						{/if}

					</div>
				</div>
			</div>

			<hr class="u-mb-sm u-mb-md@md">
			{include $templates.'/part/box/benefits.latte'}
		</div>
	{/snippet}
{/block}
