{block content}

	<div class="u-pt-md">
		<div class="row-main">
			<div class="u-maw-10-12 u-mx-auto">
				<h1>
					Typografie
				</h1>
				<h2>
					Heading h2
				</h2>
				<p>
					Suspendisse <a href="#">link</a> vel nisi accumsan pretium. Etiam id massa ut neque iaculis auctor. Fermentum eu dictum risus consequat. Cras magna justo, iaculis non scelerisque eget, condimentum in tellus. Maecenas a sem quam. Suspendisse facilisis neque tempor enim imperdiet sodales. Vitae lobortis sapien vestibulum. Suspendisse vehicula libero vel nisi accumsan. Suspendisse vehicula libero vel nisi accumsan pretium. Etiam id massa ut neque iaculis auctor. Fermentum eu dictum risus consequat. Cras magna justo, iaculis non scelerisque eget, condimentum in tellus. Maecenas a sem quam. Suspendisse.
				</p>
				<p>
					Nam liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assuiber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assum.
				</p>
				<figure>
					<img src="/static/img/illust/sample.jpg" width="400" height="250" alt="">
					<figcaption>
						Image description
					</figcaption>
				</figure>
				<h3>
					Heading h3
				</h3>
				<ul>
					<li>
						<strong>Etiam ante sem,</strong> porta a porttitor ut, varius varius metus.
					</li>
					<li>
						<strong>Nunc eu felis</strong> quis metus volutpat pellentesque.
					</li>
					<li>
						<strong>Duis gravida</strong> tincidunt enim sed cursus.
					</li>
					<li>
						<strong>Nunc eu felis</strong> quis metus volutpat pellentesque.
					</li>
					<li>
						<strong>Duis gravida</strong> tincidunt enim sed cursus.
					</li>
				</ul>
				<ol>
					<li>
						<strong>Etiam ante sem,</strong> porta a porttitor ut, varius varius metus.
					</li>
					<li>
						<strong>Nunc eu felis</strong> quis metus volutpat pellentesque.
					</li>
					<li>
						<strong>Duis gravida</strong> tincidunt enim sed cursus.
					</li>
					<li>
						<strong>Nunc eu felis</strong> quis metus volutpat pellentesque.
					</li>
					<li>
						<strong>Duis gravida</strong> tincidunt enim sed cursus.
					</li>
				</ol>
				<p>
					Suspendisse <a href="#">link</a> vel nisi accumsan pretium. Etiam id massa ut neque iaculis auctor. Fermentum eu dictum risus consequat. Cras magna justo, iaculis non scelerisque eget, condimentum in tellus. Maecenas a sem quam. Suspendisse facilisis neque tempor enim imperdiet sodales. Vitae lobortis sapien vestibulum. Suspendisse vehicula libero vel nisi accumsan. Suspendisse vehicula libero vel nisi accumsan pretium. Etiam id massa ut neque iaculis auctor. Fermentum eu dictum risus consequat. Cras magna justo, iaculis non scelerisque eget, condimentum in tellus. Maecenas a sem quam. Suspendisse.
				</p>
				<p>
					Nam liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assuiber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assum.
				</p>
				<blockquote>
					<p>
						<strong>Blockquote</strong> – Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolorum quia ipsa corrupti temporibus ratione voluptatibus, voluptatem eos culpa a, numquam suscipit deleniti veniam libero. Dicta soluta sint, officiis enim voluptate.
					</p>
				</blockquote>
				<blockquote class="quote">
					<p>
						<strong>Quote</strong> – Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolorum quia ipsa corrupti temporibus ratione voluptatibus, voluptatem eos culpa a, numquam suscipit deleniti veniam libero. Dicta soluta sint, officiis enim voluptate.
					</p>
				</blockquote>
				<blockquote class="b-quote u-mb-last-0">
					<p class="b-quote__person">
						<img class="b-quote__img img img--circle" src="/static/img/illust/dummy-course.png" alt="" loading="lazy">
						<span>
							<span class="b-quote__name">Alex Novotný</span>
							<span class="b-quote__position u-d-b u-c-help">CEO a zakladatel DronPro</span>
						</span>
						<img class="b-quote__quotes" src="/static/img/illust/quotes.svg" alt="" loading="lazy" width="40" height="40">
					</p>
					<p>
						<i class="b-quote__text">Změny v bezpilotní legislativě jsou pro spoustu dronařů ožehavým tématem. V e-booku jsme se snažili vše shrnout tak, aby to bylo každému na první dobrou jasné a informace byly rychle využitelné v letecké praxi.</i>
					</p>
				</blockquote>

				<div class="b-tip tw-relative tw-rounded-xl tw-overflow-hidden tw-my-[1.6rem] md:tw-my-[2.8rem]">
					<div class="b-popular__decorations b-popular__decorations--tip"></div>
					<div class="b-popular__content-inner u-mb-last-0 tw-p-[2rem_2.4rem] md:tw-p-[3.2rem_12rem_3.2rem_4rem] before:tw-content-[''] before:tw-absolute before:tw-w-[0.4rem] before:tw-top-0 before:tw-bottom-0 before:tw-left-0 before:tw-bg-primary">
						{('lightbulb'|icon, 'tw-absolute tw-top-[2rem] md:tw-top-[3.2rem] tw-right-[2rem] md:tw-right-[4rem] tw-w-[4rem] md:tw-w-[6rem] u-c-primary')}
						<p class="flag flag--yellow-gradient flag--sm tw-mb-[0.4rem]">TIP</p>
						<p class="h4 u-mt-0 tw-mb-[0.8rem] tw-pr-[5rem] md:tw-pr-0">Kvalitní noční záběry z dronu a RAW</p>
						<p>
							Vylepšený algoritmus redukce šumu nočních snímků u modelu Mini 4 Pro vám pomůže porizovat jasnější a kvalitnější záběry, i za šera nebo v noci. Každý drobný detail navíc můžete zaznamenat díky 48MP RAW a nové generaci SmartPhoto, která kombinuje HDR zobrazení, rozpoznavání scén a mnoho dalších funkcí.
						</p>
					</div>
				</div>



				<p>
					Mirum est notare quam littera gothica, quam nunc putamus parum claram, anteposuerit litterarum formas humanitatis per seacula quarta decima et quinta decima. Eodem modo typi, qui nunc nobis videntur parum clari, fiant sollemnes in futurum.<br>Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore te feugait nulla facilisi. Nam <strong>liber tempor cum soluta nobis</strong> eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assum.
				</p>
				<h4>
					Heading 4 úrovně
				</h4>
				<ol>
					<li>
						Lorem ipsum dolor sit amet.
						<ol>
							<li>
								Lorem ipsum dolor sit amet, consectetur adipisicing elit. Facere consequuntur id quidem, expedita, dicta, eos temporibus incidunt, mollitia aspernatur vitae excepturi vel nam dolorem voluptates fuga assumenda reprehenderit? Aut, nesciunt.
							</li>
							<li>
								Lorem ipsum dolor sit amet, consectetur adipisicing elit. Tenetur deleniti aliquid, voluptatibus ratione! Id consequatur aperiam iusto nam in beatae atque a, voluptate iste fugit ea ex officia architecto impedit?
							</li>
						</ol>
					</li>
					<li>
						Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod
					</li>
					<li>
						tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, uis nostrud exercitation ullamco laboris nisi ut iquip x ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse
					</li>
					<li>
						cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
					</li>
				</ol>
				<ul>
					<li>
						Lorem ipsum dolor sit amet.
						<ul>
							<li>
								Lorem ipsum dolor sit amet, consectetur adipisicing elit. Facere consequuntur id quidem, expedita, dicta, eos temporibus incidunt, mollitia aspernatur vitae excepturi vel nam dolorem voluptates fuga assumenda reprehenderit? Aut, nesciunt.
							</li>
							<li>
								Lorem ipsum dolor sit amet, consectetur adipisicing elit. Tenetur deleniti aliquid, voluptatibus ratione! Id consequatur aperiam iusto nam in beatae atque a, voluptate iste fugit ea ex officia architecto impedit?
							</li>
						</ul>
					</li>
					<li>
						Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod
					</li>
					<li>
						tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, uis nostrud exercitation ullamco laboris nisi ut iquip x ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse
					</li>
					<li>
						cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
					</li>
				</ul>
				<hr>
				<p>
					Nam liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assum.
				</p>
				<dl class="u-maw-5-12">
					<dt>
						Definition List Title
					</dt>
					<dd>
						This is a definition list division.
					</dd>
					<dt>
						Definition List Title
					</dt>
					<dd>
						This is a definition list division.
					</dd>
					<dt>
						Definition List Title
					</dt>
					<dd>
						This is a definition list division.
					</dd>
				</dl>
				<h5>
					Heading 5 úrovně
				</h5>
				<p>
					Lorem ipsum dolor sit amet, consectetur adipisicing elit. Vitae odio minima nostrum, blanditiis maxime ex reiciendis laborum voluptatum molestiae sequi optio, repudiandae sit explicabo. Obcaecati neque eligendi, totam debitis aperiam!
				</p>
				<h6>
					Heading 6 úrovně
				</h6>
				<p>
					<cite>Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore te feugait nulla facilisi. Nam liber tempor cum soluta nobis eleifend option congue nihil imperdiet doming id quod mazim placerat facer possim assum.</cite>
				</p>
				<h2>
					Placeholdery v textu
				</h2>
				<p>
					Lorem ipsum dolor sit amet, consectetur adipisicing elit. Vitae odio minima nostrum, blanditiis maxime ex reiciendis laborum voluptatum molestiae sequi optio, repudiandae sit explicabo. Obcaecati neque eligendi, totam debitis aperiam!
				</p>
				{* {include '../part/box/placeholder-article.latte'} *}
				<p>
					Lorem ipsum dolor sit amet, consectetur adipisicing elit. Vitae odio minima nostrum, blanditiis maxime ex reiciendis laborum voluptatum molestiae sequi optio, repudiandae sit explicabo. Obcaecati neque eligendi, totam debitis aperiam!
				</p>
				{* {include '../part/box/placeholder-product.latte'} *}
				<p>
					Lorem ipsum dolor sit amet, consectetur adipisicing elit. Vitae odio minima nostrum, blanditiis maxime ex reiciendis laborum voluptatum molestiae sequi optio, repudiandae sit explicabo. Obcaecati neque eligendi, totam debitis aperiam!
					Lorem ipsum dolor sit amet, consectetur adipisicing elit. Vitae odio minima nostrum, blanditiis maxime ex reiciendis laborum voluptatum molestiae sequi optio, repudiandae sit explicabo. Obcaecati neque eligendi, totam debitis aperiam!
					Lorem ipsum dolor sit amet, consectetur adipisicing elit. Vitae odio minima nostrum, blanditiis maxime ex reiciendis laborum voluptatum molestiae sequi optio, repudiandae sit explicabo. Obcaecati neque eligendi, totam debitis aperiam!
					Lorem ipsum dolor sit amet, consectetur adipisicing elit. Vitae odio minima nostrum, blanditiis maxime ex reiciendis laborum voluptatum molestiae sequi optio, repudiandae sit explicabo. Obcaecati neque eligendi, totam debitis aperiam!
				</p>
				<h2>
					Tabular data
				</h2>
				<div class="u-table-responsive">
					<table>
						<caption>
							Table Caption
						</caption>
						<thead>
							<tr>
								<th>
									Table Heading 1
								</th>
								<th>
									Table Heading 2
								</th>
								<th>
									Table Heading 3
								</th>
								<th>
									Table Heading 4
								</th>
								<th>
									Table Heading 5
								</th>
							</tr>
						</thead>
						<tbody>
							<tr>
								<td>
									Table Cell 1
								</td>
								<td>
									Table Cell 2
								</td>
								<td>
									Table Cell 3
								</td>
								<td>
									Table Cell 4
								</td>
								<td>
									Table Cell 5
								</td>
							</tr>
							<tr>
								<td>
									Table Cell 1
								</td>
								<td>
									Table Cell 2
								</td>
								<td>
									Table Cell 3
								</td>
								<td>
									Table Cell 4
								</td>
								<td>
									Table Cell 5
								</td>
							</tr>
							<tr>
								<td>
									Table Cell 1
								</td>
								<td>
									Table Cell 2
								</td>
								<td>
									Table Cell 3
								</td>
								<td>
									Table Cell 4
								</td>
								<td>
									Table Cell 5
								</td>
							</tr>
							<tr>
								<td>
									Table Cell 1
								</td>
								<td>
									Table Cell 2
								</td>
								<td>
									Table Cell 3
								</td>
								<td>
									Table Cell 4
								</td>
								<td>
									Table Cell 5
								</td>
							</tr>
						</tbody>
						<tfoot>
							<tr>
								<th>
									Table Footer 1
								</th>
								<th>
									Table Footer 2
								</th>
								<th>
									Table Footer 3
								</th>
								<th>
									Table Footer 4
								</th>
								<th>
									Table Footer 5
								</th>
							</tr>
						</tfoot>
					</table>
					<p class="info">
						I tabulka může mít popisek
					</p>
				</div>
				<h2>
					Headings
				</h2>
				<h1>
					Heading 1
				</h1>
				<h2>
					Heading 2
				</h2>
				<h3>
					Heading 3
				</h3>
				<h4>
					Heading 4
				</h4>
				<h5>
					Heading 5
				</h5>
				<h6>
					Heading 6
				</h6>


				<h2>
					Paragraphs
				</h2>
				<p>
					A paragraph (from the Greek paragraphos, “to write beside” or “written beside”) is a self-contained unit of a discourse in writing dealing with a particular point or idea. A paragraph consists of one or more sentences. Though not required by the syntax of any language, paragraphs are usually an expected part of formal writing, used to organize longer prose.
				</p>

				<h2>
					Blockquotes
				</h2>
				<blockquote>
					<p>
						A block quotation (also known as a long quotation or extract) is a quotation in a written document, that is set off from the main text as a paragraph, or block of text.
					</p>
					<p>
						It is typically distinguished visually using indentation and a different typeface or smaller size quotation. It may or may not include a citation, usually placed at the bottom.
					</p>
					<cite>
						<a href="#">
							Said no one, ever.
						</a>
					</cite>
				</blockquote>

				<h2>
					Lists
				</h2>

				<h3>
					Definition list
				</h3>
				<dl class="u-maw-5-12">
					<dt>
						Definition List Title
					</dt>
					<dd>
						This is a definition list division.
					</dd>
				</dl>

				<h3>
					Ordered List
				</h3>
				<ol>
					<li>
						List Item 1
					</li>
					<li>
						List Item 2
					</li>
					<li>
						List Item 3
					</li>
				</ol>

				<h3>
					Unordered List
				</h3>
				<ul>
					<li>
						List Item 1
					</li>
					<li>
						List Item 2
					</li>
					<li>
						List Item 3
					</li>
				</ul>

				<h2>
					Horizontal rules
				</h2>
				<hr>

				<h2>
					Tabular data
				</h2>
				<div class="u-table-responsive">
					<table>
						<caption>
							Table Caption
						</caption>
						<thead>
							<tr>
								<th>
									Table Heading 1
								</th>
								<th>
									Table Heading 2
								</th>
								<th>
									Table Heading 3
								</th>
								<th>
									Table Heading 4
								</th>
								<th>
									Table Heading 5
								</th>
							</tr>
						</thead>
						<tbody>
							<tr>
								<td>
									Table Cell 1
								</td>
								<td>
									Table Cell 2
								</td>
								<td>
									Table Cell 3
								</td>
								<td>
									Table Cell 4
								</td>
								<td>
									Table Cell 5
								</td>
							</tr>
							<tr>
								<td>
									Table Cell 1
								</td>
								<td>
									Table Cell 2
								</td>
								<td>
									Table Cell 3
								</td>
								<td>
									Table Cell 4
								</td>
								<td>
									Table Cell 5
								</td>
							</tr>
							<tr>
								<td>
									Table Cell 1
								</td>
								<td>
									Table Cell 2
								</td>
								<td>
									Table Cell 3
								</td>
								<td>
									Table Cell 4
								</td>
								<td>
									Table Cell 5
								</td>
							</tr>
							<tr>
								<td>
									Table Cell 1
								</td>
								<td>
									Table Cell 2
								</td>
								<td>
									Table Cell 3
								</td>
								<td>
									Table Cell 4
								</td>
								<td>
									Table Cell 5
								</td>
							</tr>
						</tbody>
						<tfoot>
							<tr>
								<th>
									Table Footer 1
								</th>
								<th>
									Table Footer 2
								</th>
								<th>
									Table Footer 3
								</th>
								<th>
									Table Footer 4
								</th>
								<th>
									Table Footer 5
								</th>
							</tr>
						</tfoot>
					</table>
				</div>

				<h2>
					Code
				</h2>
				<p>
					<strong>Keyboard input:</strong> <kbd>Cmd</kbd>
				</p>
				<p>
					<strong>Inline code:</strong> <code>&lt;div&gt;code&lt;/div&gt;</code>
				</p>
				<p>
					<strong>Sample output:</strong> <samp>This is sample output from a computer program.</samp>
				</p>

				<h2>
					Pre-formatted text
				</h2>
				<pre>P R E F O R M A T T E D T E X T
					! " # $ % &amp; ' ( ) * + , - . /
					0 1 2 3 4 5 6 7 8 9 : ; &lt; = &gt; ?
					@ A B C D E F G H I J K L M N O
					P Q R S T U V W X Y Z [ \ ] ^ _
					` a b c d e f g h i j k l m n o
					p q r s t u v w x y z { | } ~ </pre>

				<h2>
					Inline elements
				</h2>
				<p>
					<a href="#">This is a text link</a>.
				</p>
				<p>
					<strong>Strong is used to indicate strong importance.</strong>
				</p>
				<p>
					<em>This text has added emphasis.</em>
				</p>
				<p>
					<del>This text is deleted</del> and <ins>This text is inserted</ins>.
				</p>
				<p>
					<s>This text has a strikethrough</s>.
				</p>
				<p>
					Superscript<sup>®</sup>.
				</p>
				<p>
					Subscript for things like H<sub>2</sub>O.
				</p>
				<p>
					Abbreviation: <abbr title="HyperText Markup Language">HTML</abbr>
				</p>
				<p>
					<q cite="https://developer.mozilla.org/en-US/docs/HTML/Element/q">This text is a short inline quotation.</q>
				</p>
				<p>
					<cite>This is a citation.</cite>
				</p>
				<p>
					The <dfn>dfn element</dfn> indicates a definition.
				</p>
				<p>
					The <mark>mark element</mark> indicates a highlight.
				</p>
				<p>
					The <var>variable element</var>, such as <var>x</var> = <var>y</var>.
				</p>
				<p>
					The time element: <time datetime="2013-04-06T12:32+00:00">2 weeks ago</time>
				</p>

				<h2>
					Formulářové prvky
				</h2>
				<h3>
					Message boxy
				</h3>
				<h4>Sm / system</h4>
				<p class="message message--sm">
					<span class="message__emoji">👌</span>
					<span class="message__content">
						<b>TIP!</b> Při zakoupení libovolných dvou kurzů získáte 20 % slevu na oba! Mrkněte na <a href="#">navazující kurzy a školení</a>.
					</span>
					<button type="button" class="message__close as-link">
						{('cross')|icon}
						<span class="u-vhide">
							{_"btn_close"}
						</span>
					</button>
				</p>
				<p class="message message--sm message--ok">
					<span class="message__emoji">👌</span>
					<span class="message__content">
						<b>TIP!</b> Při zakoupení libovolných dvou kurzů získáte 20 % slevu na oba! Mrkněte na <a href="#">navazující kurzy a školení</a>.
					</span>
					<button type="button" class="message__close as-link">
						{('cross')|icon}
						<span class="u-vhide">
							{_"btn_close"}
						</span>
					</button>
				</p>
				<p class="message message--sm message--error">
					<span class="message__emoji">👌</span>
					<span class="message__content">
							<b>TIP!</b> Při zakoupení libovolných dvou kurzů získáte 20 % slevu na oba! Mrkněte na <a href="#">navazující kurzy a školení</a>.
					</span>
					<button type="button" class="message__close as-link">
						{('cross')|icon}
						<span class="u-vhide">
							{_"btn_close"}
						</span>
					</button>
				</p>
				<p class="message message--sm message--warning">
					<span class="message__emoji">👌</span>
					<span class="message__content">
						<b>TIP!</b> Při zakoupení libovolných dvou kurzů získáte 20 % slevu na oba! Mrkněte na <a href="#">navazující kurzy a školení</a>.
					</span>
					<button type="button" class="message__close as-link">
						{('cross')|icon}
						<span class="u-vhide">
							{_"btn_close"}
						</span>
					</button>
				</p>
				<p class="message message--sm message--warning2">
					<span class="message__emoji">👌</span>
					<span class="message__content">
						<b>TIP!</b> Při zakoupení libovolných dvou kurzů získáte 20 % slevu na oba! Mrkněte na <a href="#">navazující kurzy a školení</a>.
					</span>
					<button type="button" class="message__close as-link">
						{('cross')|icon}
						<span class="u-vhide">
							{_"btn_close"}
						</span>
					</button>
				</p>
				<p class="message message--sm message--purple">
					<span class="message__emoji">👌</span>
					<span class="message__content">
						<b>TIP!</b> Při zakoupení libovolných dvou kurzů získáte 20 % slevu na oba! Mrkněte na <a href="#">navazující kurzy a školení</a>.
					</span>
					<button type="button" class="message__close as-link">
						{('cross')|icon}
						<span class="u-vhide">
							{_"btn_close"}
						</span>
					</button>
				</p>
				<h4>Default</h4>
				<p class="message">
					<span class="message__emoji">👌</span>
					<span class="message__content">
						<b>TIP!</b> Při zakoupení libovolných dvou kurzů získáte 20 % slevu na oba! Mrkněte na <a href="#">navazující kurzy a školení</a>.
					</span>
				</p>
				<p class="message message--ok">
					<span class="message__emoji">👌</span>
					<span class="message__content">
						<b>TIP!</b> Při zakoupení libovolných dvou kurzů získáte 20 % slevu na oba! Mrkněte na <a href="#">navazující kurzy a školení</a>.
					</span>
				</p>
				<p class="message message--error">
					<span class="message__emoji">👌</span>
					<span class="message__content">
						<b>TIP!</b> Při zakoupení libovolných dvou kurzů získáte 20 % slevu na oba! Mrkněte na <a href="#">navazující kurzy a školení</a>.
					</span>
				</p>
				<p class="message message--warning">
					<span class="message__emoji">👌</span>
					<span class="message__content">
						<b>TIP!</b> Při zakoupení libovolných dvou kurzů získáte 20 % slevu na oba! Mrkněte na <a href="#">navazující kurzy a školení</a>.
					</span>
				</p>
				<p class="message message--warning2">
					<span class="message__emoji">👌</span>
					<span class="message__content">
						<b>TIP!</b> Při zakoupení libovolných dvou kurzů získáte 20 % slevu na oba! Mrkněte na <a href="#">navazující kurzy a školení</a>.
					</span>
				</p>
				<p class="message message--purple">
					<span class="message__emoji">👌</span>
					<span class="message__content">
						<b>TIP!</b> Při zakoupení libovolných dvou kurzů získáte 20 % slevu na oba! Mrkněte na <a href="#">navazující kurzy a školení</a>.
					</span>
				</p>
				<h4>Lg</h4>
				<p class="message message--lg">
					<span class="message__emoji">👌</span>
					<span class="message__content">
						<b>TIP!</b> Při zakoupení libovolných dvou kurzů získáte 20 % slevu na oba! Mrkněte na <a href="#">navazující kurzy a školení</a>.
					</span>
				</p>
				<p class="message message--lg message--ok">
					<span class="message__emoji">👌</span>
					<span class="message__content">
						<b>TIP!</b> Při zakoupení libovolných dvou kurzů získáte 20 % slevu na oba! Mrkněte na <a href="#">navazující kurzy a školení</a>.
					</span>
				</p>
				<p class="message message--lg message--error">
					<span class="message__emoji">👌</span>
					<span class="message__content">
						<b>TIP!</b> Při zakoupení libovolných dvou kurzů získáte 20 % slevu na oba! Mrkněte na <a href="#">navazující kurzy a školení</a>.
					</span>
				</p>
				<p class="message message--lg message--warning">
					<span class="message__emoji">👌</span>
					<span class="message__content">
						<b>TIP!</b> Při zakoupení libovolných dvou kurzů získáte 20 % slevu na oba! Mrkněte na <a href="#">navazující kurzy a školení</a>.
					</span>
				</p>
				<p class="message message--lg message--warning2">
					<span class="message__emoji">👌</span>
					<span class="message__content">
						<b>TIP!</b> Při zakoupení libovolných dvou kurzů získáte 20 % slevu na oba! Mrkněte na <a href="#">navazující kurzy a školení</a>.
					</span>
				</p>
				<p class="message message--lg message--purple">
					<span class="message__emoji">👌</span>
					<span class="message__content">
						<b>TIP!</b> Při zakoupení libovolných dvou kurzů získáte 20 % slevu na oba! Mrkněte na <a href="#">navazující kurzy a školení</a>.
					</span>
				</p>
				<h3>
					Flagy
				</h3>
				<p>
					<a href="#" class="flag flag--gray-light u-fw-n">
						Text štítku
						{('angle-right')|icon, 'flag__icon'}
					</a>
					<a href="#" class="flag flag--gray-light u-fw-n flag--bd">
						Text štítku
						{('angle-right')|icon, 'flag__icon'}
					</a>
					<a href="#" class="flag flag--rainbow">
						Text štítku
						{('angle-right')|icon, 'flag__icon'}
					</a>
					<a href="#" class="flag flag--rainbow flag--bd">
						Text štítku TODO
						{('angle-right')|icon, 'flag__icon'}
					</a>
					<a href="#" class="flag flag--yellow-gradient">
						Text štítku
						{('angle-right')|icon, 'flag__icon'}
					</a>
					<a href="#" class="flag flag--purple-light">
						Text štítku
						{('angle-right')|icon, 'flag__icon'}
					</a>
					<a href="#" class="flag flag--green-light">
						Text štítku
						{('angle-right')|icon, 'flag__icon'}
					</a>
					<a href="#" class="flag flag--blue-light">
						Text štítku
						{('angle-right')|icon, 'flag__icon'}
					</a>
					<a href="#" class="flag flag--orange-light">
						Text štítku
						{('angle-right')|icon, 'flag__icon'}
					</a>
					<a href="#" class="flag flag--red-light">
						Text štítku
						{('angle-right')|icon, 'flag__icon'}
					</a>
					<a href="#" class="flag flag--gray">
						Text štítku
						{('angle-right')|icon, 'flag__icon'}
					</a>
					<a href="#" class="flag flag--green">
						Text štítku
						{('angle-right')|icon, 'flag__icon'}
					</a>
					<a href="#" class="flag flag--green flag--bd">
						Text štítku
						{('angle-right')|icon, 'flag__icon'}
					</a>
					<a href="#" class="flag">
						Text štítku
						{('angle-right')|icon, 'flag__icon'}
					</a>
					<a href="#" class="flag flag--bd">
						Text štítku
						{('angle-right')|icon, 'flag__icon'}
					</a>
					<a href="#" class="flag flag--blue">
						Text štítku
						{('angle-right')|icon, 'flag__icon'}
					</a>
					<a href="#" class="flag flag--blue flag--bd">
						Text štítku
						{('angle-right')|icon, 'flag__icon'}
					</a>
					<a href="#" class="flag flag--red">
						Text štítku
						{('angle-right')|icon, 'flag__icon'}
					</a>
					<a href="#" class="flag flag--red flag--bd">
						Text štítku
						{('angle-right')|icon, 'flag__icon'}
					</a>
					<a href="#" class="flag flag--remove">
						Text štítku
						{('cross')|icon, 'flag__icon'}
					</a>
				</p>
				<h4>
					Úřad práce
				</h4>
				<p>
					<a href="#" class="flag flag--drak">
						Drak Friday
					</a>
					<a href="#" class="flag flag--drak flag--md">
						Drak Friday
					</a>
				</p>
				<p>
					<a href="#" class="flag flag--yellow-gradient">
						{('cz')|icon, 'flag__icon'}
						až 82% příspěvek státu
					</a>
					<a href="#" class="flag flag--yellow-gradient flag--md">
						{('cz')|icon, 'flag__icon'}
						až 82% příspěvek státu
					</a>
				</p>
				<h3>
					Inputy
				</h3>
				<p>
					<label for="default" class="inp-label">
						Defaultní input s labelem <span class="u-c-gray">(volitelné)</span>
					</label>
					<span class="inp-fix">
						<input type="text" name="default" id="default" class="inp-text">
					</span>
				</p>
				<p>
					<label for="default" class="inp-label">
						Defaultní input s labelem <span class="u-c-gray">(volitelné)</span>
					</label>
					<span class="inp-fix">
						{('envelope')|icon, 'inp-fix__icon'}
						<input type="text" name="default" id="default" class="inp-text">
					</span>
				</p>
				<p>
					<label for="placeholder" class="u-vhide">
						Input s placeholderem a skrytým labelem
					</label>
					<span class="inp-fix">
						<input type="text" name="placeholder" id="placeholder" class="inp-text" placeholder="Input s placeholderem a skrytým labelem">
					</span>
				</p>
				<p>
					<span class="inp-fix">
						<input type="text" name="placeholder" id="placeholder" class="inp-text" value="Input s hodnotou">
					</span>
				</p>
				<p>
					<label for="email" class="inp-label">
						E-mail input <span class="inp-required">*</span>
					</label>
					<span class="inp-fix">
						<input type="email" name="email" id="email" class="inp-text">
					</span>
				</p>
				<p>
					<label for="password" class="inp-label">
						Password input
					</label>
					<span class="inp-fix" data-controller="password">
						<input type="password" name="password" id="password" value="heslo" class="inp-text" data-password-target="inp" placeholder=""> {* kvuli barve ikony pri vyplnenem inputu musi byt vzdy uveden placeholder attribut *}
						<button type="button" class="inp-fix__link as-link" data-action="click->password#toggle">
							{('eye')|icon, 'inp-fix__icon'}
							<span class="u-vhide">
								{_"show_password"}
							</span>
						</button>
					</span>
				</p>
				<p>
					<label for="number" class="inp-label">
						Number input
					</label>
					<span class="inp-fix">
						<input type="number" name="number" id="number" class="inp-text">
					</span>
				</p>
				<p>
					<label for="tel" class="inp-label">
						Phone input
					</label>
					<span class="inp-fix">
						<input type="tel" name="tel" id="tel" class="inp-text">
					</span>
				</p>
				<p>
					<label for="number" class="inp-label">
						Number input
					</label>
					<span class="inp-fix">
						<input type="number" name="number" id="number" class="inp-text">
					</span>
				</p>
				<p>
					<label for="select" class="inp-label">
						Select
					</label>
					<span class="inp-fix inp-fix--sm">
						<select name="select" id="select" class="inp-select">
							<option value="">
								Vyberte hodnotu
							</option>
							<option value="1">
								Hodnota 1
							</option>
							<option value="2">
								Hodnota 2
							</option>
							<option value="3">
								Hodnota 3
							</option>
							<option value="4">
								Hodnota 4
							</option>
						</select>
					</span>
				</p>
				<p>
					<label for="select" class="inp-label">
						Fake select
					</label>
					<span class="inp-fix inp-fix--sm">
						<button type="button" name="select" id="select" class="inp-select as-link">
							Vyberte hodnotu
						</button>
					</span>
				</p>
				<p>
					<label for="select" class="inp-label">
						Select
					</label>
					<span class="inp-fix">
						<select name="select" id="select" class="inp-select">
							<option value="">
								Vyberte hodnotu
							</option>
							<option value="1">
								Hodnota 1
							</option>
							<option value="2">
								Hodnota 2
							</option>
							<option value="3">
								Hodnota 3
							</option>
							<option value="4">
								Hodnota 4
							</option>
						</select>
					</span>
				</p>
				<p>
					<label for="select" class="inp-label">
						Select lg
					</label>
					<span class="inp-fix inp-fix--lg">
						<select name="select" id="select" class="inp-select">
							<option value="">
								Vyberte hodnotu
							</option>
							<option value="1">
								Hodnota 1
							</option>
							<option value="2">
								Hodnota 2
							</option>
							<option value="3">
								Hodnota 3
							</option>
							<option value="4">
								Hodnota 4
							</option>
						</select>
					</span>
				</p>
				<p class="has-error">
					<label for="select" class="inp-label">
						Error select
					</label>
					<span class="inp-fix inp-fix--sm">
						<select name="select" id="select" class="inp-select">
							<option value="">
								Vyberte hodnotu
							</option>
							<option value="1">
								Hodnota 1
							</option>
							<option value="2">
								Hodnota 2
							</option>
							<option value="3">
								Hodnota 3
							</option>
							<option value="4">
								Hodnota 4
							</option>
						</select>
					</span>
					<span class="inp-error">
						Zadejte e-mail ve správném formátu.
					</span>
				</p>
				<p class="has-error">
					<label for="select" class="inp-label">
						Error select
					</label>
					<span class="inp-fix">
						<select name="select" id="select" class="inp-select">
							<option value="">
								Vyberte hodnotu
							</option>
							<option value="1">
								Hodnota 1
							</option>
							<option value="2">
								Hodnota 2
							</option>
							<option value="3">
								Hodnota 3
							</option>
							<option value="4">
								Hodnota 4
							</option>
						</select>
					</span>
					<span class="inp-error">
						Zadejte e-mail ve správném formátu.
					</span>
				</p>
				<p class="has-error">
					<label for="select" class="inp-label">
						Error select lg
					</label>
					<span class="inp-fix inp-fix--lg">
						<select name="select" id="select" class="inp-select">
							<option value="">
								Vyberte hodnotu
							</option>
							<option value="1">
								Hodnota 1
							</option>
							<option value="2">
								Hodnota 2
							</option>
							<option value="3">
								Hodnota 3
							</option>
							<option value="4">
								Hodnota 4
							</option>
						</select>
					</span>
					<span class="inp-error">
						Zadejte e-mail ve správném formátu.
					</span>
				</p>
				<p>
					<label for="select" class="inp-label">
						Disabled select lg
					</label>
					<span class="inp-fix inp-fix--lg">
						<select name="select" id="select" class="inp-select" disabled>
							<option value="">
								Vyberte hodnotu
							</option>
							<option value="1">
								Hodnota 1
							</option>
							<option value="2">
								Hodnota 2
							</option>
							<option value="3">
								Hodnota 3
							</option>
							<option value="4">
								Hodnota 4
							</option>
						</select>
					</span>
				</p>
				<p>
					<label for="textarea" class="inp-label">
						Textarea
					</label>
					<span class="inp-fix">
						<textarea name="textarea" id="textarea" class="inp-text" cols="40" rows="6"></textarea>
					</span>
				</p>
				<p>
					<label for="disabled" class="inp-label">
						Disabled defaultní input
					</label>
					<span class="inp-fix">
						<input type="text" name="disabled" id="disabled" class="inp-text" disabled>
					</span>
				</p>
				<p class="has-error">
					<label for="error" class="inp-label">
						Defaultní input s errorem
					</label>
					<span class="inp-fix inp-fix--sm">
						<input type="text" name="error" id="error" class="inp-text">
						{('cross')|icon, 'inp-fix__icon inp-fix__icon--error'}
					</span>
					<span class="inp-error">
						Zadejte e-mail ve správném formátu.
					</span>
				</p>
				<p class="has-error">
					<label for="error" class="inp-label">
						Defaultní input s errorem
					</label>
					<span class="inp-fix">
						<input type="text" name="error" id="error" class="inp-text">
						{('cross')|icon, 'inp-fix__icon inp-fix__icon--error'}
					</span>
					<span class="inp-error">
						Zadejte e-mail ve správném formátu.
					</span>
				</p>
				<p class="has-error">
					<label for="error" class="inp-label">
						Defaultní input lg s errorem
					</label>
					<span class="inp-fix inp-fix--lg">
						<input type="text" name="error" id="error" class="inp-text">
						{('cross')|icon, 'inp-fix__icon inp-fix__icon--error'}
					</span>
					<span class="inp-error">
						Zadejte e-mail ve správném formátu.
					</span>
				</p>
				<p class="is-ok">
					<label for="error" class="inp-label">
						Defaultní input OK
					</label>
					<span class="inp-fix">
						<input type="text" name="error" id="error" class="inp-text">
						{('check')|icon, 'inp-fix__icon inp-fix__icon--ok'}
					</span>
				</p>
				<h2>
					Checkboxy
				</h2>
				<label class="inp-item inp-item--checkbox">
					<input type="checkbox" name="checkbox" value="1" class="inp-item__inp">
					<span class="inp-item__text">
						Checkbox 1
					</span>
				</label>
				<label class="inp-item inp-item--checkbox">
					<input type="checkbox" name="checkbox" value="1" class="inp-item__inp" checked>
					<span class="inp-item__text">
						Checkbox 2
					</span>
				</label>
				<label class="inp-item inp-item--checkbox has-error">
					<input type="checkbox" name="checkbox" value="1" class="inp-item__inp" checked>
					<span class="inp-item__text">
						Checkbox 3
					</span>
				</label>
				<label class="inp-item inp-item--checkbox is-ok">
					<input type="checkbox" name="checkbox" value="1" class="inp-item__inp" checked>
					<span class="inp-item__text">
						Checkbox 4
					</span>
				</label>
				<label class="inp-item inp-item--checkbox">
					<input type="checkbox" name="checkbox" value="1" class="inp-item__inp" disabled>
					<span class="inp-item__text">
						Checkbox 5
					</span>
				</label>
				<label class="inp-item inp-item--checkbox">
					<input type="checkbox" name="checkbox" value="1" class="inp-item__inp" checked disabled>
					<span class="inp-item__text">
						Checkbox 6
					</span>
				</label>
				<h2>
					Radia
				</h2>
				<label class="inp-item inp-item--radio">
					<input type="radio" name="radio" value="1" class="inp-item__inp">
					<span class="inp-item__text">
						Radio 1
					</span>
				</label>
				<label class="inp-item inp-item--radio">
					<input type="radio" name="radio" value="1" class="inp-item__inp" checked>
					<span class="inp-item__text">
						Radio 2
					</span>
				</label>
				<label class="inp-item inp-item--radio has-error">
					<input type="radio" name="radio" value="1" class="inp-item__inp" checked>
					<span class="inp-item__text">
						Radio 3
					</span>
				</label>
				<label class="inp-item inp-item--radio is-ok">
					<input type="radio" name="radio" value="1" class="inp-item__inp" checked>
					<span class="inp-item__text">
						Radio 4
					</span>
				</label>
				<label class="inp-item inp-item--radio">
					<input type="radio" name="radio2" value="1" class="inp-item__inp" disabled>
					<span class="inp-item__text">
						Radio 5
					</span>
				</label>
				<label class="inp-item inp-item--radio">
					<input type="radio" name="radio2" value="1" class="inp-item__inp" checked disabled>
					<span class="inp-item__text">
						Radio 6
					</span>
				</label>
				<h2>
					Tlačítka
				</h2>
				<p>
					<a href="#" class="btn btn--rainbow">
						<span class="btn__text">
							Odeslat
						</span>
					</a>
				</p>
				<p>
					<a href="#" class="btn btn--icon">
						<span class="btn__text">
							{('cross')|icon, 'btn__icon'}
							<span class="u-vhide">Zavřít</span>
						</span>
					</a>
				</p>
				<p>
					<a href="#" class="btn btn--micro">
						<span class="btn__text">
							<span class="btn__inner">
								{('envelope')|icon, 'btn__icon'}
								Odeslat
							</span>
						</span>
					</a>
				</p>
				<p class="is-loading">
					<a href="#" class="btn btn--xl btn--loader">
						<span class="btn__text">
							<span class="btn__inner">
								{('envelope')|icon, 'btn__icon'}
								Odeslat
							</span>
							<span class="btn__info">
								Druhý řádek textu
							</span>
						</span>
					</a>
					<a href="#" class="btn btn--xl btn--loader">
						<span class="btn__text">
							<span class="btn__inner">
								{('envelope')|icon, 'btn__icon'}
								Odeslat
							</span>
						</span>
					</a>
					<a href="#" class="btn btn--lg btn--loader">
						<span class="btn__text">
							<span class="btn__inner">
								{('envelope')|icon, 'btn__icon'}
								Odeslat
							</span>
						</span>
					</a>
					<a href="#" class="btn btn--loader">
						<span class="btn__text">
							<span class="btn__inner">
								{('envelope')|icon, 'btn__icon'}
								Odeslat
							</span>
						</span>
					</a>
					<a href="#" class="btn btn--sm btn--loader">
						<span class="btn__text">
							<span class="btn__inner">
								{('envelope')|icon, 'btn__icon'}
								Odeslat
							</span>
						</span>
					</a>
					<a href="#" class="btn btn--xs btn--loader">
						<span class="btn__text">
							<span class="btn__inner">
								{('envelope')|icon, 'btn__icon'}
								Odeslat
							</span>
						</span>
					</a>
				</p>
				<p>
					<a href="#" class="btn btn--xl is-disabled">
						<span class="btn__text">
							<span class="btn__inner">
								{('envelope')|icon, 'btn__icon'}
								Odeslat
							</span>
							<span class="btn__info">
								Druhý řádek textu
							</span>
						</span>
					</a>
					<a href="#" class="btn btn--xl">
						<span class="btn__text">
							<span class="btn__inner">
								{('envelope')|icon, 'btn__icon'}
								Odeslat
							</span>
							<span class="btn__info">
								Druhý řádek textu
							</span>
						</span>
					</a>
					<a href="#" class="btn btn--xl">
						<span class="btn__text">
							<span class="btn__inner">
								{('envelope')|icon, 'btn__icon'}
								Odeslat
							</span>
						</span>
					</a>
					<a href="#" class="btn btn--lg">
						<span class="btn__text">
							<span class="btn__inner">
								{('envelope')|icon, 'btn__icon'}
								Odeslat
							</span>
						</span>
					</a>
					<a href="#" class="btn">
						<span class="btn__text">
							<span class="btn__inner">
								{('envelope')|icon, 'btn__icon'}
								Odeslat
							</span>
						</span>
					</a>
					<a href="#" class="btn btn--sm">
						<span class="btn__text">
							<span class="btn__inner">
								{('envelope')|icon, 'btn__icon'}
								Odeslat
							</span>
						</span>
					</a>
					<a href="#" class="btn btn--xs">
						<span class="btn__text">
							<span class="btn__inner">
								{('envelope')|icon, 'btn__icon'}
								Odeslat
							</span>
						</span>
					</a>
				</p>
				<p>
					<a href="#" class="btn btn--secondary btn--xl is-disabled">
						<span class="btn__text">
							<span class="btn__inner">
								{('envelope')|icon, 'btn__icon'}
								Odeslat
							</span>
							<span class="btn__info">
								Druhý řádek textu
							</span>
						</span>
					</a>
					<a href="#" class="btn btn--secondary btn--xl">
						<span class="btn__text">
							<span class="btn__inner">
								{('envelope')|icon, 'btn__icon'}
								Odeslat
							</span>
							<span class="btn__info">
								Druhý řádek textu
							</span>
						</span>
					</a>
					<a href="#" class="btn btn--secondary btn--xl">
						<span class="btn__text">
							<span class="btn__inner">
								{('envelope')|icon, 'btn__icon'}
								Odeslat
							</span>
						</span>
					</a>
					<a href="#" class="btn btn--secondary btn--lg">
						<span class="btn__text">
							Odeslat
						</span>
					</a>
					<a href="#" class="btn btn--secondary">
						<span class="btn__text">
							Odeslat
						</span>
					</a>
					<a href="#" class="btn btn--secondary btn--sm">
						<span class="btn__text">
							Odeslat
						</span>
					</a>
					<a href="#" class="btn btn--secondary btn--xs">
						<span class="btn__text">
							Odeslat
						</span>
					</a>
				</p>
				<p>
					<a href="#" class="btn btn--gray btn--xl is-disabled">
						<span class="btn__text">
							<span class="btn__inner">
								{('envelope')|icon, 'btn__icon'}
								Odeslat
							</span>
							<span class="btn__info">
								Druhý řádek textu
							</span>
						</span>
					</a>
					<a href="#" class="btn btn--gray btn--xl">
						<span class="btn__text">
							<span class="btn__inner">
								{('envelope')|icon, 'btn__icon'}
								Odeslat
							</span>
							<span class="btn__info">
								Druhý řádek textu
							</span>
						</span>
					</a>
					<a href="#" class="btn btn--gray btn--xl">
						<span class="btn__text">
							<span class="btn__inner">
								{('envelope')|icon, 'btn__icon'}
								Odeslat
							</span>
						</span>
					</a>
					<a href="#" class="btn btn--gray btn--lg">
						<span class="btn__text">
							Odeslat
						</span>
					</a>
					<a href="#" class="btn btn--gray">
						<span class="btn__text">
							Odeslat
						</span>
					</a>
					<a href="#" class="btn btn--gray btn--sm">
						<span class="btn__text">
							Odeslat
						</span>
					</a>
					<a href="#" class="btn btn--gray btn--xs">
						<span class="btn__text">
							Odeslat
						</span>
					</a>
				</p>
				<p>
					<a href="#" class="btn btn--bd btn--xl is-disabled">
						<span class="btn__text">
							<span class="btn__inner">
								{('envelope')|icon, 'btn__icon'}
								Odeslat
							</span>
							<span class="btn__info">
								Druhý řádek textu
							</span>
						</span>
					</a>
					<a href="#" class="btn btn--bd btn--xl">
						<span class="btn__text">
							<span class="btn__inner">
								{('envelope')|icon, 'btn__icon'}
								Odeslat
							</span>
							<span class="btn__info">
								Druhý řádek textu
							</span>
						</span>
					</a>
					<a href="#" class="btn btn--bd btn--xl">
						<span class="btn__text">
							<span class="btn__inner">
								{('envelope')|icon, 'btn__icon'}
								Odeslat
							</span>
						</span>
					</a>
					<a href="#" class="btn btn--bd btn--lg">
						<span class="btn__text">
							Odeslat
						</span>
					</a>
					<a href="#" class="btn btn--bd">
						<span class="btn__text">
							Odeslat
						</span>
					</a>
					<a href="#" class="btn btn--bd btn--sm">
						<span class="btn__text">
							Odeslat
						</span>
					</a>
					<a href="#" class="btn btn--bd btn--xs">
						<span class="btn__text">
							Odeslat
						</span>
					</a>
				</p>
				<p>
					<a href="#" class="btn btn--bd btn--black btn--xl is-disabled">
						<span class="btn__text">
							<span class="btn__inner">
								{('envelope')|icon, 'btn__icon'}
								Odeslat
							</span>
							<span class="btn__info">
								Druhý řádek textu
							</span>
						</span>
					</a>
					<a href="#" class="btn btn--bd btn--black btn--xl">
						<span class="btn__text">
							<span class="btn__inner">
								{('envelope')|icon, 'btn__icon'}
								Odeslat
							</span>
							<span class="btn__info">
								Druhý řádek textu
							</span>
						</span>
					</a>
					<a href="#" class="btn btn--bd btn--black btn--xl">
						<span class="btn__text">
							<span class="btn__inner">
								{('envelope')|icon, 'btn__icon'}
								Odeslat
							</span>
						</span>
					</a>
					<a href="#" class="btn btn--bd btn--black btn--lg">
						<span class="btn__text">
							Odeslat
						</span>
					</a>
					<a href="#" class="btn btn--bd btn--black">
						<span class="btn__text">
							Odeslat
						</span>
					</a>
					<a href="#" class="btn btn--bd btn--black btn--sm">
						<span class="btn__text">
							Odeslat
						</span>
					</a>
					<a href="#" class="btn btn--bd btn--black btn--xs">
						<span class="btn__text">
							Odeslat
						</span>
					</a>
				</p>
				<p>
					<a href="#" class="btn btn--gray btn--bd btn--xl is-disabled">
						<span class="btn__text">
							<span class="btn__inner">
								{('envelope')|icon, 'btn__icon'}
								Odeslat
							</span>
							<span class="btn__info">
								Druhý řádek textu
							</span>
						</span>
					</a>
					<a href="#" class="btn btn--gray btn--bd btn--xl">
						<span class="btn__text">
							<span class="btn__inner">
								{('envelope')|icon, 'btn__icon'}
								Odeslat
							</span>
							<span class="btn__info">
								Druhý řádek textu
							</span>
						</span>
					</a>
					<a href="#" class="btn btn--gray btn--bd btn--xl">
						<span class="btn__text">
							<span class="btn__inner">
								{('envelope')|icon, 'btn__icon'}
								Odeslat
							</span>
						</span>
					</a>
					<a href="#" class="btn btn--gray btn--bd btn--lg">
						<span class="btn__text">
							Odeslat
						</span>
					</a>
					<a href="#" class="btn btn--gray btn--bd">
						<span class="btn__text">
							Odeslat
						</span>
					</a>
					<a href="#" class="btn btn--gray btn--bd btn--sm">
						<span class="btn__text">
							Odeslat
						</span>
					</a>
					<a href="#" class="btn btn--gray btn--bd btn--xs">
						<span class="btn__text">
							Odeslat
						</span>
					</a>
				</p>
				<p>
					<a href="#" class="btn btn--white btn--xl is-disabled">
						<span class="btn__text">
							<span class="btn__inner">
								{('envelope')|icon, 'btn__icon'}
								Odeslat
							</span>
							<span class="btn__info">
								Druhý řádek textu
							</span>
						</span>
					</a>
					<a href="#" class="btn btn--white btn--xl">
						<span class="btn__text">
							<span class="btn__inner">
								{('envelope')|icon, 'btn__icon'}
								Odeslat
							</span>
							<span class="btn__info">
								Druhý řádek textu
							</span>
						</span>
					</a>
					<a href="#" class="btn btn--white btn--xl">
						<span class="btn__text">
							<span class="btn__inner">
								{('envelope')|icon, 'btn__icon'}
								Odeslat
							</span>
						</span>
					</a>
					<a href="#" class="btn btn--white btn--lg">
						<span class="btn__text">
							Odeslat
						</span>
					</a>
					<a href="#" class="btn btn--white">
						<span class="btn__text">
							Odeslat
						</span>
					</a>
					<a href="#" class="btn btn--white btn--sm">
						<span class="btn__text">
							Odeslat
						</span>
					</a>
					<a href="#" class="btn btn--white btn--xs">
						<span class="btn__text">
							Odeslat
						</span>
					</a>
				</p>
				<div class="u-bgc-dark u-mb-sm" style="padding: 1rem">
					<p>
						<a href="#" class="btn btn--bd btn--white btn--xl is-disabled">
							<span class="btn__text">
								<span class="btn__inner">
									{('envelope')|icon, 'btn__icon'}
									Odeslat
								</span>
								<span class="btn__info">
									Druhý řádek textu
								</span>
							</span>
						</a>
						<a href="#" class="btn btn--bd btn--white btn--xl">
							<span class="btn__text">
								<span class="btn__inner">
									{('envelope')|icon, 'btn__icon'}
									Odeslat
								</span>
								<span class="btn__info">
									Druhý řádek textu
								</span>
							</span>
						</a>
						<a href="#" class="btn btn--bd btn--white btn--xl">
							<span class="btn__text">
								<span class="btn__inner">
									{('envelope')|icon, 'btn__icon'}
									Odeslat
								</span>
							</span>
						</a>
						<a href="#" class="btn btn--bd btn--white btn--lg">
							<span class="btn__text">
								Odeslat
							</span>
						</a>
						<a href="#" class="btn btn--bd btn--white">
							<span class="btn__text">
								Odeslat
							</span>
						</a>
						<a href="#" class="btn btn--bd btn--white btn--sm">
							<span class="btn__text">
								Odeslat
							</span>
						</a>
						<a href="#" class="btn btn--bd btn--white btn--xs">
							<span class="btn__text">
								Odeslat
							</span>
						</a>
					</p>
				</div>
				{* <p>
					<a href="#" class="btn btn--icon btn--lg">
						<span class="btn__text">
							{('envelope')|icon, 'btn__icon'}
						</span>
					</a>
					<a href="#" class="btn btn--icon btn--secondary">
						<span class="btn__text">
							{('envelope')|icon, 'btn__icon'}
						</span>
					</a>
				</p> *}
				<h2>
					Obsah nad patičkou
				</h2>
				{control newsletterForm, [type: 'article']}
				{control newsletterForm, [type: 'ebook']}
				{control newsletterForm}
				{include $templates.'/part/box/services.latte', showHighlighted: true}
				{include $templates.'/part/box/services.latte'}
				{include $templates.'/part/menu/social.latte'}
				{include $templates.'/part/box/benefits.latte'}
				<h2>
					Pomocné třídy
				</h2>
				<div class="u-table-responsive">
					<table>
						<thead>
							<tr>
								<th>
									Třída
								</th>
								<th>
									Popis a ukázka
								</th>
							</tr>
						</thead>
						<tbody>
							<tr>
								<th>
									.u-c-red
								</th>
								<td class="u-c-red">
									Červený text
								</td>
							</tr>
							<tr>
								<th>
									.u-c-orange
								</th>
								<td class="u-c-orange">
									Oranžový text
								</td>
							</tr>
							<tr>
								<th>
									.u-c-text
								</th>
								<td class="u-c-text">
									Defaultní text
								</td>
							</tr>
							<tr>
								<th>
									.u-c-white
								</th>
								<td class="u-c-white">
									Bílý text
								</td>
							</tr>
							<tr>
								<th>
									.u-c-green
								</th>
								<td class="u-c-green">
									Zelený text
								</td>
							</tr>
							<tr>
								<th>
									.u-c-primary
								</th>
								<td class="u-c-primary">
									Primární (zelený) text
								</td>
							</tr>
							<tr>
								<th>
									.u-c-secondary
								</th>
								<td class="u-c-secondary">
									Sekundární (oranžový) text
								</td>
							</tr>
							<tr>
								<th>
									.u-c-gray-dark
								</th>
								<td class="u-c-gray-dark">
									Tmavě šedý text
								</td>
							</tr>
							<tr>
								<th>
									.u-c-gray
								</th>
								<td class="u-c-gray">
									Šedý text
								</td>
							</tr>
							<tr>
								<th>
									.u-bgc-white
								</th>
								<td class="u-bgc-white">
									Bílé pozadí
								</td>
							</tr>
							<tr>
								<th>
									.u-bgc-sand-light-3
								</th>
								<td class="u-bgc-sand-light-3">
									Pískové pozadí
								</td>
							</tr>
							<tr>
								<th>
									.u-bgc-default
								</th>
								<td class="u-bgc-default">
									Defaultní pozadí
								</td>
							</tr>
							<tr>
								<th>
									.u-ta-l
								</th>
								<td class="u-ta-l">
									Zarovnání textu doleva
								</td>
							</tr>
							<tr>
								<th>
									.u-ta-r
								</th>
								<td class="u-ta-r">
									Zarovnání textu doprava
								</td>
							</tr>
							<tr>
								<th>
									.u-ta-c
								</th>
								<td class="u-ta-c">
									Zarovnání textu na střed
								</td>
							</tr>
							<tr>
								<th>
									.u-ta-j
								</th>
								<td class="u-ta-j">
									Zarovnání textu do bloku
								</td>
							</tr>
							<tr>
								<th>
									.u-whs-nw
								</th>
								<td>
									Nezalomitelný text
								</td>
							</tr>
							<tr>
								<th>
									.u-tt-l
								</th>
								<td class="u-tt-l">
									Malá písmena
								</td>
							</tr>
							<tr>
								<th>
									.u-tt-u
								</th>
								<td class="u-tt-u">
									Velká písmena
								</td>
							</tr>
							<tr>
								<th>
									.u-tt-c
								</th>
								<td class="u-tt-c">
									Velká pouze první písmena
								</td>
							</tr>
							<tr>
								<th>
									.u-fw-l
								</th>
								<td class="u-fw-l">
									Tenké písmo
								</td>
							</tr>
							<tr>
								<th>
									.u-fw-n
								</th>
								<td class="u-fw-n">
									Normální písmo
								</td>
							</tr>
							<tr>
								<th>
									.u-fw-b
								</th>
								<td class="u-fw-b">
									Tučné písmo
								</td>
							</tr>
							<tr>
								<th>
									.u-fs-i
								</th>
								<td class="u-fs-i">
									Kurzíva
								</td>
							</tr>
							<tr>
								<th>
									.u-line-clamp
								</th>
								<td>
									Ukončení dlouhého textu třemi tečkami
								</td>
							</tr>
							<tr>
								<th>
									.u-va-t
								</th>
								<td class="u-va-t">
									Zarovnání nahoru
								</td>
							</tr>
							<tr>
								<th>
									.u-va-m
								</th>
								<td class="u-va-m">
									Zarovnání na střed
								</td>
							</tr>
							<tr>
								<th>
									.u-va-b
								</th>
								<td class="u-va-b">
									Zarovnání dolů
								</td>
							</tr>
							<tr>
								<th>
									.u-fl-l
								</th>
								<td>
									Float vlevo
								</td>
							</tr>
							<tr>
								<th>
									.u-fl-r
								</th>
								<td>
									Float vpravo
								</td>
							</tr>
							<tr>
								<th>
									.u-clearfix
								</th>
								<td>
									Clearování floatů
								</td>
							</tr>
							<tr>
								<th>
									.u-vhide
								</th>
								<td>
									Skrytí obsahu (zůstává zobrazitelný čtečkám)
								</td>
							</tr>
							<tr>
								<th>
									.u-js-hide
								</th>
								<td>
									Skrytí obsahu (pouze při zapnutém javascriptu)
								</td>
							</tr>
							<tr>
								<th>
									.u-out
								</th>
								<td>
									Vypozicování obsahu mimo viewport
								</td>
							</tr>
							<tr>
								<th>
									.u-js-out
								</th>
								<td>
									Vypozicování obsahu mimo viewport (pouze při zapnutém javascriptu)
								</td>
							</tr>
							<tr>
								<th>
									.u-d-n
								</th>
								<td>
									Skrytí obsahu
								</td>
							</tr>
							<tr>
								<th>
									.u-d-b
								</th>
								<td>
									Zobrazení obsahu
								</td>
							</tr>
							<tr>
								<th>
									.u-d-n@sm
								</th>
								<td>
									Skrytí obsahu na všech breakpointech větších než SM (většinou &lt; 480px)
								</td>
							</tr>
							<tr>
								<th>
									.u-d-b@sm
								</th>
								<td>
									Zobrazení obsahu na všech breakpointech větších než SM (většinou &lt; 480px)
								</td>
							</tr>
							<tr>
								<th>
									.u-d-n@md
								</th>
								<td>
									Skrytí obsahu na všech breakpointech větších než MD (většinou &lt; 750px)
								</td>
							</tr>
							<tr>
								<th>
									.u-d-b@md
								</th>
								<td>
									Zobrazení obsahu na všech breakpointech větších než MD (většinou &lt; 750px)
								</td>
							</tr>
							<tr>
								<th>
									.u-d-n@lg
								</th>
								<td>
									Skrytí obsahu na všech breakpointech větších než LG (většinou &lt; 1000px)
								</td>
							</tr>
							<tr>
								<th>
									.u-d-b@lg
								</th>
								<td>
									Zobrazení obsahu na všech breakpointech větších než LG (většinou &lt; 1000px)
								</td>
							</tr>
							<tr>
								<th>
									.u-d-n@xl
								</th>
								<td>
									Skrytí obsahu na všech breakpointech větších než XL (většinou &lt; 1200px)
								</td>
							</tr>
							<tr>
								<th>
									.u-d-b@xl
								</th>
								<td>
									Zobrazení obsahu na všech breakpointech větších než XL (většinou &lt; 1200px)
								</td>
							</tr>
							<tr>
								<th>
									.u-mb-0
								</th>
								<td>
									Spodní odsazení s nulovou velikostí
								</td>
							</tr>
							<tr>
							<tr>
								<th>
									.u-mb-sm
								</th>
								<td>
									Spodní odsazení v "sm" velikosti
								</td>
							</tr>
							<tr>
								<th>
									.u-mb-md
								</th>
								<td>
									Spodní odsazení v "md" velikosti
								</td>
							</tr>
							<tr>
								<th>
									.u-mb-lg
								</th>
								<td>
									Spodní odsazení v "lg" velikosti
								</td>
							</tr>
							<tr>
								<th>
									.u-mb-xl
								</th>
								<td>
									Spodní odsazení v "xl" velikosti
								</td>
							</tr>
							<tr>
								<th>
									.u-mb-last-0
								</th>
								<td>
									Odebere spodní odsazení posledního podřízeného prvku.
								</td>
							</tr>
							<tr>
								<th>
									.u-no-print
								</th>
								<td>
									Skrytí obsahu pro tisk
								</td>
							</tr>
						</tbody>
					</table>
				</div>

				<h2>
					Přidání do porovnávače
				</h2>

				<div class="u-maw-8-12 tw-mx-auto">
					<div class="row-main tw-px-[3.5rem] md:tw-p-[6rem]">
						<div class="grid grid--x-sm grid--y-sm">
							<div class="grid__cell size--6-12@md">
								<div class="tw-bg-status-valid-light tw-rounded-xl tw-p-[2.4rem_2rem] md:tw-p-[4.4rem] tw-h-full tw-content-center tw-text-center">
									<h2 class="tw-mb-[0.8rem] tw-text-status-valid">
										{('check-circle')|icon, 'tw-block tw-mx-auto md:tw-mb-[0.8rem] tw-w-[2.6rem] md:tw-w-[6rem]'}
										{_"compare_add_title"}
									</h2>
									<p class="tw-mb-[1.2rem] md:tw-mb-[2.4rem] md:tw-text-[1.5rem]">
										{php $categoryProductsCount = 1}
										{capture $category}<a href="#">Drony</a>{/capture}
										{_"compare_add_category"|noescape|replace: '%category', $category->__toString()}
										<b class="tw-block">{$categoryProductsCount} {_($categoryProductsCount|plural: "products_1", "products_2", "products_3")}</b>
									</p>
									<p class="tw-mb-0">
										<a href="#" class="btn btn--secondary btn--lg">
											<span class="btn__text">
												{_"btn_show_comparer"}
											</span>
										</a>
									</p>
								</div>
							</div>
							<div class="grid__cell size--6-12@md">
								<div class="md:tw-py-[2rem] u-mb-last-0">
									<p class="h4 tw-mb-[1.6rem] md:tw-mb-[2rem] tw-text-center">
										{_"compare_add_another"}
									</p>
									<div class="tw-max-w-[36rem] tw-mx-auto">
										{* TODO BE *}
										<p class="tw-h-[64rem]">
											TODO: b-product
										</p>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<h2>
					Grid
				</h2>

				<div>
					<h2> Defaultní grid (mobil portrait) </h2>
					<div class="grid">
						<div class="grid__cell size--1-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--2-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--2-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--2-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--2-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--2-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--2-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--3-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
						</div>
						<div class="grid__cell size--3-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
						</div>
						<div class="grid__cell size--3-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
						</div>
						<div class="grid__cell size--3-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
						</div>
						<div class="grid__cell size--4-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
						</div>
						<div class="grid__cell size--4-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
						</div>
						<div class="grid__cell size--4-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
						</div>
						<div class="grid__cell size--5-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 5/12 </p>
						</div>
						<div class="grid__cell size--7-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 7/12 </p>
						</div>
						<div class="grid__cell size--5-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 5/12 </p>
						</div>
						<div class="grid__cell size--7-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 7/12 </p>
						</div>
						<div class="grid__cell size--6-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 6/12 </p>
						</div>
						<div class="grid__cell size--6-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 6/12 </p>
						</div>
						<div class="grid__cell size--7-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 7/12 </p>
						</div>
						<div class="grid__cell size--5-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 5/12 </p>
						</div>
						<div class="grid__cell size--8-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 8/12 </p>
						</div>
						<div class="grid__cell size--4-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
						</div>
						<div class="grid__cell size--9-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 9/12 </p>
						</div>
						<div class="grid__cell size--3-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
						</div>
						<div class="grid__cell size--10-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 10/12 </p>
						</div>
						<div class="grid__cell size--2-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--11-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 11/12 </p>
						</div>
						<div class="grid__cell size--1-12">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 12/12 </p>
						</div>
					</div>
					<h2> Grid od breakpointu sm (mobil landscape) </h2>
					<div class="grid">
						<div class="grid__cell size--1-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--2-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--2-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--2-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--2-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--2-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--2-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--3-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
						</div>
						<div class="grid__cell size--3-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
						</div>
						<div class="grid__cell size--3-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
						</div>
						<div class="grid__cell size--3-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
						</div>
						<div class="grid__cell size--4-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
						</div>
						<div class="grid__cell size--4-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
						</div>
						<div class="grid__cell size--4-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
						</div>
						<div class="grid__cell size--5-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 5/12 </p>
						</div>
						<div class="grid__cell size--7-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 7/12 </p>
						</div>
						<div class="grid__cell size--5-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 5/12 </p>
						</div>
						<div class="grid__cell size--7-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 7/12 </p>
						</div>
						<div class="grid__cell size--6-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 6/12 </p>
						</div>
						<div class="grid__cell size--6-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 6/12 </p>
						</div>
						<div class="grid__cell size--7-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 7/12 </p>
						</div>
						<div class="grid__cell size--5-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 5/12 </p>
						</div>
						<div class="grid__cell size--8-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 8/12 </p>
						</div>
						<div class="grid__cell size--4-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
						</div>
						<div class="grid__cell size--9-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 9/12 </p>
						</div>
						<div class="grid__cell size--3-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
						</div>
						<div class="grid__cell size--10-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 10/12 </p>
						</div>
						<div class="grid__cell size--2-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--11-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 11/12 </p>
						</div>
						<div class="grid__cell size--1-12@sm">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 12/12 </p>
						</div>
					</div>
					<h2> Grid od breakpointu md (tablet) </h2>
					<div class="grid">
						<div class="grid__cell size--1-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--2-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--2-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--2-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--2-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--2-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--2-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--3-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
						</div>
						<div class="grid__cell size--3-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
						</div>
						<div class="grid__cell size--3-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
						</div>
						<div class="grid__cell size--3-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
						</div>
						<div class="grid__cell size--4-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
						</div>
						<div class="grid__cell size--4-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
						</div>
						<div class="grid__cell size--4-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
						</div>
						<div class="grid__cell size--5-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 5/12 </p>
						</div>
						<div class="grid__cell size--7-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 7/12 </p>
						</div>
						<div class="grid__cell size--5-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 5/12 </p>
						</div>
						<div class="grid__cell size--7-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 7/12 </p>
						</div>
						<div class="grid__cell size--6-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 6/12 </p>
						</div>
						<div class="grid__cell size--6-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 6/12 </p>
						</div>
						<div class="grid__cell size--7-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 7/12 </p>
						</div>
						<div class="grid__cell size--5-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 5/12 </p>
						</div>
						<div class="grid__cell size--8-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 8/12 </p>
						</div>
						<div class="grid__cell size--4-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
						</div>
						<div class="grid__cell size--9-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 9/12 </p>
						</div>
						<div class="grid__cell size--3-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
						</div>
						<div class="grid__cell size--10-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 10/12 </p>
						</div>
						<div class="grid__cell size--2-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--11-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 11/12 </p>
						</div>
						<div class="grid__cell size--1-12@md">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 12/12 </p>
						</div>
					</div>
					<h2> Grid od breakpointu lg (desktop) </h2>
					<div class="grid">
						<div class="grid__cell size--1-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--2-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--2-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--2-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--2-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--2-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--2-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--3-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
						</div>
						<div class="grid__cell size--3-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
						</div>
						<div class="grid__cell size--3-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
						</div>
						<div class="grid__cell size--3-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
						</div>
						<div class="grid__cell size--4-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
						</div>
						<div class="grid__cell size--4-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
						</div>
						<div class="grid__cell size--4-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
						</div>
						<div class="grid__cell size--5-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 5/12 </p>
						</div>
						<div class="grid__cell size--7-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 7/12 </p>
						</div>
						<div class="grid__cell size--5-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 5/12 </p>
						</div>
						<div class="grid__cell size--7-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 7/12 </p>
						</div>
						<div class="grid__cell size--6-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 6/12 </p>
						</div>
						<div class="grid__cell size--6-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 6/12 </p>
						</div>
						<div class="grid__cell size--7-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 7/12 </p>
						</div>
						<div class="grid__cell size--5-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 5/12 </p>
						</div>
						<div class="grid__cell size--8-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 8/12 </p>
						</div>
						<div class="grid__cell size--4-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
						</div>
						<div class="grid__cell size--9-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 9/12 </p>
						</div>
						<div class="grid__cell size--3-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
						</div>
						<div class="grid__cell size--10-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 10/12 </p>
						</div>
						<div class="grid__cell size--2-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--11-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 11/12 </p>
						</div>
						<div class="grid__cell size--1-12@lg">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 12/12 </p>
						</div>
					</div>
					<h2> Grid od breakpointu xl (desktop – large) </h2>
					<div class="grid">
						<div class="grid__cell size--1-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--1-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell size--2-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--2-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--2-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--2-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--2-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--2-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--3-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
						</div>
						<div class="grid__cell size--3-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
						</div>
						<div class="grid__cell size--3-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
						</div>
						<div class="grid__cell size--3-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
						</div>
						<div class="grid__cell size--4-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
						</div>
						<div class="grid__cell size--4-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
						</div>
						<div class="grid__cell size--4-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
						</div>
						<div class="grid__cell size--5-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 5/12 </p>
						</div>
						<div class="grid__cell size--7-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 7/12 </p>
						</div>
						<div class="grid__cell size--5-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 5/12 </p>
						</div>
						<div class="grid__cell size--7-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 7/12 </p>
						</div>
						<div class="grid__cell size--6-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 6/12 </p>
						</div>
						<div class="grid__cell size--6-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 6/12 </p>
						</div>
						<div class="grid__cell size--7-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 7/12 </p>
						</div>
						<div class="grid__cell size--5-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 5/12 </p>
						</div>
						<div class="grid__cell size--8-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 8/12 </p>
						</div>
						<div class="grid__cell size--4-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 4/12 </p>
						</div>
						<div class="grid__cell size--9-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 9/12 </p>
						</div>
						<div class="grid__cell size--3-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 3/12 </p>
						</div>
						<div class="grid__cell size--10-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 10/12 </p>
						</div>
						<div class="grid__cell size--2-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 2/12 </p>
						</div>
						<div class="grid__cell size--11-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 11/12 </p>
						</div>
						<div class="grid__cell size--1-12@xl">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 1/12 </p>
						</div>
						<div class="grid__cell">
							<p style="background: #f0f0f0; padding: 2rem; text-align: center;"> 12/12 </p>
						</div>
					</div>
					<h2> Auto šířka buňek gridu </h2>
					<div class="grid">
						<div class="grid__cell size--auto">
							<p> Lorem ipsum </p>
						</div>
						<div class="grid__cell size--auto">
							<p> Dolor sit amet </p>
						</div>
						<div class="grid__cell size--auto">
							<p> Lorem ipsum </p>
						</div>
						<div class="grid__cell size--auto">
							<p> Dolor sit amet </p>
						</div>
						<div class="grid__cell size--auto">
							<p> Lorem ipsum </p>
						</div>
						<div class="grid__cell size--auto">
							<p> Dolor sit amet </p>
						</div>
						<div class="grid__cell size--auto">
							<p> Lorem ipsum </p>
						</div>
						<div class="grid__cell size--auto">
							<p> Dolor sit amet </p>
						</div>
					</div>
					<h2> Auto šírka buňky gridu s vyplněním celého řádku </h2>
					<div class="grid">
						<div class="grid__cell size--autogrow">
							<p> Lorem ipsum </p>
						</div>
						<div class="grid__cell size--autogrow">
							<p> Dolor sit amet </p>
						</div>
					</div>
					<h2> Horizontální zarovnání </h2>
					<div class="grid">
						<div class="grid__cell size--4-12">
							<p style="background: #f0f0f0; padding: 2rem;">
								<strong> Defaultní - left </strong>
							</p>
						</div>
					</div>
					<div class="grid grid--center">
						<div class="grid__cell size--4-12">
							<p style="background: #f0f0f0; padding: 2rem;">
								<strong> Defaultní - center </strong>
							</p>
						</div>
					</div>
					<div class="grid grid--right">
						<div class="grid__cell size--4-12">
							<p style="background: #f0f0f0; padding: 2rem;">
								<strong> Defaultní - right </strong>
							</p>
						</div>
					</div>
					<div class="grid grid--space-between">
						<div class="grid__cell size--3-12">
							<p style="background: #f0f0f0; padding: 2rem;">
								<strong> Defaultní - space-between </strong>
							</p>
						</div>
						<div class="grid__cell size--3-12">
							<p style="background: #f0f0f0; padding: 2rem;"> Lorem ipsum dolor </p>
						</div>
					</div>
					<h2> Vertikální zarovnání </h2>
					<div class="grid">
						<div class="grid__cell size--6-12">
							<p style="background: #f0f0f0; padding: 2rem;">
								<strong> Defaultní – top </strong>
							</p>
						</div>
						<div class="grid__cell size--6-12">
							<p style="background: #f0f0f0; padding: 2rem;"> Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolores, iste, provident adipisci doloribus, sit tempora ex molestias illo soluta repellendus quae ipsam aut porro debitis iure praesentium! Sapiente, expedita, dolores! </p>
						</div>
					</div>
					<div class="grid grid--middle">
						<div class="grid__cell size--6-12">
							<p style="background: #f0f0f0; padding: 2rem;">
								<strong> Defaultní – middle </strong>
							</p>
						</div>
						<div class="grid__cell size--6-12">
							<p style="background: #f0f0f0; padding: 2rem;"> Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolores, iste, provident adipisci doloribus, sit tempora ex molestias illo soluta repellendus quae ipsam aut porro debitis iure praesentium! Sapiente, expedita, dolores! </p>
						</div>
					</div>
					<div class="grid grid--bottom">
						<div class="grid__cell size--6-12">
							<p style="background: #f0f0f0; padding: 2rem;">
								<strong> Defaultní – bottom </strong>
							</p>
						</div>
						<div class="grid__cell size--6-12">
							<p style="background: #f0f0f0; padding: 2rem;"> Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolores, iste, provident adipisci doloribus, sit tempora ex molestias illo soluta repellendus quae ipsam aut porro debitis iure praesentium! Sapiente, expedita, dolores! </p>
						</div>
					</div>
					<h3> Vertikální zarovnání na buňkách </h3>
					<div class="grid grid--middle">
						<div class="grid__cell grid__cell--top size--6-12">
							<p style="background: #f0f0f0; padding: 2rem;">
								<strong> Defaultní – top </strong>
								<br> Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolores, iste, provident adipisci doloribus, sit tempora ex molestias illo soluta repellendus quae ipsam aut porro debitis iure praesentium! Sapiente, expedita, dolores!
							</p>
						</div>
						<div class="grid__cell size--6-12">
							<p style="background: #f0f0f0; padding: 2rem;"> Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolores, iste, provident adipisci doloribus, sit tempora ex molestias illo soluta repellendus quae ipsam aut porro debitis iure praesentium! Sapiente, expedita, dolores! Lorem ipsum dolor sit amet, consectetur adipisicing elit. Quam rem quia accusamus. Quibusdam dolore aliquid, doloribus modi rem? Maiores nemo ipsam delectus repellat similique quibusdam, blanditiis accusamus ea ullam repudiandae. </p>
						</div>
					</div>
					<div class="grid">
						<div class="grid__cell grid__cell--middle size--6-12">
							<p style="background: #f0f0f0; padding: 2rem;">
								<strong> Defaultní – middle </strong>
								<br> Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolores, iste, provident adipisci doloribus, sit tempora ex molestias illo soluta repellendus quae ipsam aut porro debitis iure praesentium! Sapiente, expedita, dolores!
							</p>
						</div>
						<div class="grid__cell size--6-12">
							<p style="background: #f0f0f0; padding: 2rem;"> Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolores, iste, provident adipisci doloribus, sit tempora ex molestias illo soluta repellendus quae ipsam aut porro debitis iure praesentium! Sapiente, expedita, dolores! Lorem ipsum dolor sit amet, consectetur adipisicing elit. Quam rem quia accusamus. Quibusdam dolore aliquid, doloribus modi rem? Maiores nemo ipsam delectus repellat similique quibusdam, blanditiis accusamus ea ullam repudiandae. </p>
						</div>
					</div>
					<div class="grid">
						<div class="grid__cell grid__cell--bottom size--6-12">
							<p style="background: #f0f0f0; padding: 2rem;">
								<strong> Defaultní – bottom </strong>
								<br> Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolores, iste, provident adipisci doloribus, sit tempora ex molestias illo soluta repellendus quae ipsam aut porro debitis iure praesentium! Sapiente, expedita, dolores!
							</p>
						</div>
						<div class="grid__cell size--6-12">
							<p style="background: #f0f0f0; padding: 2rem;"> Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolores, iste, provident adipisci doloribus, sit tempora ex molestias illo soluta repellendus quae ipsam aut porro debitis iure praesentium! Sapiente, expedita, dolores! Lorem ipsum dolor sit amet, consectetur adipisicing elit. Quam rem quia accusamus. Quibusdam dolore aliquid, doloribus modi rem? Maiores nemo ipsam delectus repellat similique quibusdam, blanditiis accusamus ea ullam repudiandae. </p>
						</div>
					</div>
					<h2> Srovnání výšek následující prvku v buňce </h2>
					<div class="grid">
						<div class="grid__cell grid__cell--eq size--6-12">
							<p style="background: #f0f0f0; padding: 2rem;"> Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolores, iste, provident adipisci doloribus, sit tempora ex molestias illo soluta repellendus quae ipsam aut porro debitis iure praesentium! Sapiente, expedita, dolores! </p>
						</div>
						<div class="grid__cell size--6-12">
							<p style="background: #f0f0f0; padding: 2rem;"> Lorem ipsum dolor sit amet, consectetur adipisicing elit. Dolores, iste, provident adipisci doloribus, sit tempora ex molestias illo soluta repellendus quae ipsam aut porro debitis iure praesentium! Sapiente, expedita, dolores! Lorem ipsum dolor sit amet, consectetur adipisicing elit. Quam rem quia accusamus. Quibusdam dolore aliquid, doloribus modi rem? Maiores nemo ipsam delectus repellat similique quibusdam, blanditiis accusamus ea ullam repudiandae. </p>
						</div>
					</div>
					<h2> Pořadí sloupců </h2>
					<div class="grid">
						<div class="grid__cell size--1-12 order--12">
							<p style="background: #f0f0f0; padding: 2rem;"> 1 -&gt; 12 </p>
						</div>
						<div class="grid__cell size--1-12 order--11">
							<p style="background: #f0f0f0; padding: 2rem;"> 2 -&gt; 11 </p>
						</div>
						<div class="grid__cell size--1-12 order--10">
							<p style="background: #f0f0f0; padding: 2rem;"> 3 -&gt; 10 </p>
						</div>
						<div class="grid__cell size--1-12 order--9">
							<p style="background: #f0f0f0; padding: 2rem;"> 4 -&gt; 9 </p>
						</div>
						<div class="grid__cell size--1-12 order--8">
							<p style="background: #f0f0f0; padding: 2rem;"> 5 -&gt; 8 </p>
						</div>
						<div class="grid__cell size--1-12 order--7">
							<p style="background: #f0f0f0; padding: 2rem;"> 6 -&gt; 7 </p>
						</div>
						<div class="grid__cell size--1-12 order--9">
							<p style="background: #f0f0f0; padding: 2rem;"> 7 -&gt; 9 </p>
						</div>
						<div class="grid__cell size--1-12 order--5">
							<p style="background: #f0f0f0; padding: 2rem;"> 8 -&gt; 5 </p>
						</div>
						<div class="grid__cell size--1-12 order--4">
							<p style="background: #f0f0f0; padding: 2rem;"> 9 -&gt; 4 </p>
						</div>
						<div class="grid__cell size--1-12 order--3">
							<p style="background: #f0f0f0; padding: 2rem;"> 10 -&gt; 3 </p>
						</div>
						<div class="grid__cell size--1-12 order--2">
							<p style="background: #f0f0f0; padding: 2rem;"> 11 -&gt; 2 </p>
						</div>
						<div class="grid__cell size--1-12 order--1">
							<p style="background: #f0f0f0; padding: 2rem;"> 12 -&gt; 1 </p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

{/block}

