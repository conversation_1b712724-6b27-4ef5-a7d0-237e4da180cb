{block content}
	{snippet content}
		<div class="row-main">
			<div class="u-maw-8-12 u-mx-auto">
				{include $templates.'/part/box/header.latte', class: 'b-header--simple tw-mb-[2rem] md:tw-mb-[3.2rem]'}

				{php $dummyFaqItems = [
					(object)[
						'question'=> 'What is Lorem Ipsum?',
						'answer' => 'Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.'
					],
				]}

				{include $templates.'/part/crossroad/faq.latte', title: false, items: $dummyFaqItems, btn: false}
			</div>
			{control customContentRenderer, [ccClass: 'u-maw-8-12 u-mx-auto']}
			{include $templates.'/part/box/services.latte'}
			{include $templates.'/part/box/benefits.latte'}
		</div>
	{/snippet}
{/block}
