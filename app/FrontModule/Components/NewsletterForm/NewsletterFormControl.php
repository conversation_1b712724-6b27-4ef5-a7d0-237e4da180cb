<?php declare(strict_types = 1);

namespace App\FrontModule\Components\NewsletterForm;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\Model\Mutation\MutationHolder;
use App\Model\TranslatorDB;
use Nette\Application\UI;
use Nette\Application\UI\Form;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use App\FrontModule\Components\HasAntispamInput;
use function assert;

/**
 * @property-read DefaultTemplate $template
 */
final class NewsletterFormControl extends UI\Control
{

	use HasAntispamInput;

	public function __construct(
		private readonly NewsletterFormFactory $newsletterFormFactory,
		private readonly MutationHolder $mutationHolder,
		private readonly TranslatorDB $translator,
		private readonly MessageForFormFactory $messageForFormFactory,
	)
	{
	}


	public function render(mixed $params = null): void
	{
		$this->template->params = $params;
		$this->template->setTranslator($this->translator);
		$this->template->pages = $this->mutationHolder->getMutation()->pages;
		$this->template->render(__DIR__ . '/newsletterForm.latte');
	}


	protected function createComponentForm(): Form
	{
		$form = $this->newsletterFormFactory->create();
		assert($form instanceof Form);

		$this->attachAntispamTo($form);

		$form->onSuccess[] = $this->formSucceeded(...);
		$form->onError[] = $this->formError(...);

		return $form;
	}


	public function formError(): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSucceeded(Form $form): void
	{
		$this->flashMessage('newsletterAdded', 'ok');

		if ($this->presenter->isAjax()) {
			$form['email']->setValue('');
			$this->redrawControl();
		} else {
			$this->redirect('this');
		}
	}


	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

}
