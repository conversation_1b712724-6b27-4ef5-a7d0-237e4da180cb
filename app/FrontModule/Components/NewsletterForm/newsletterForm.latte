{php $control['form']->action .= "#frm-newsletterForm-form"}

{snippet form}
	{php $type = $params['type'] ?? 'default'}
	{php $class = $params['class'] ?? 'u-mb-sm u-mb-md@md'}
	<form n:name="form" n:class="f-newsletter, $class, block-loader, $type ? 'f-newsletter--' . $type" novalidate='novalidate' data-naja data-naja-loader="body">
		<div class="f-newsletter__inner">
			{define #inpBlock}
				{default $inpIcon = false}

				{control messageForForm, $flashes, $form, TRUE, ['timeToggle'=>true]}

				<p class="f-newsletter__inp-wrap">
					{include '../inp.latte', class: 'f-newsletter__inp', icon: $inpIcon ? 'envelope-outline' : false, noP: true, form: $form, name: email, labelClass: 'u-vhide', placeholderLang: 'form_label_enter_email'}
					<button type="submit" class="f-newsletter__btn btn btn--lg">
						<span class="btn__text">
							{_"btn_newsletter"}
						</span>
					</button>
				</p>
			{/define}

			{if $type == 'ebook'}
				<p class="f-newsletter__img u-mb-0">
					<img src="/static/img/illust/newsletter-ebook.webp" alt="" loading="lazy" width="265" height="324">
				</p>
				<div class="f-newsletter__main u-mb-last-0">
					<h2 class="f-newsletter__title">
						{_"title_newsletter_ebook"}
					</h2>
					<p n:if="isset($pages->personalData)" class="f-newsletter__info">
						{capture $link}{plink $pages->personalData}{/capture}
						{_"form_newsletter_ebook"|replace:'%link', $link->__toString()|noescape}
					</p>
					{include #inpBlock}
				</div>
			{elseif $type == 'article'}
				<div class="f-newsletter__main u-mb-last-0">
					<h2 class="f-newsletter__title">
						{_"title_newsletter_article"}
					</h2>

					<div class="u-maw-4-12 u-mx-auto">
						{include #inpBlock, inpIcon: true}
					</div>

					<p n:if="isset($pages->personalData)" class="f-newsletter__info">
						{capture $link}{plink $pages->personalData}{/capture}
						{_"form_agree"|replace:'%link', $link->__toString()|noescape}
					</p>
				</div>
				<p class="f-newsletter__img u-mb-0 u-d-n@md">
					<img src="/static/img/illust/newsletter.webp" alt="" loading="lazy" width="217" height="151">
				</p>
			{else}
				<div class="f-newsletter__main u-mb-last-0">
					<h2 class="f-newsletter__title">
						{_"newsletter_title"}
					</h2>

					{include #inpBlock, inpIcon: true}

					<p n:if="isset($pages->personalData)" class="f-newsletter__info">
						{capture $link}{plink $pages->personalData}{/capture}
						{_"form_agree"|replace:'%link', $link->__toString()|noescape}
					</p>
				</div>
				<p class="f-newsletter__img u-mb-0">
					<img src="/static/img/illust/newsletter.webp" alt="" loading="lazy" width="217" height="151">
				</p>
			{/if}
		</div>

		{*ANTISPAM*}
		{var $formTranslator = $form->getTranslator()}
		{php $form->setTranslator(null)}
		{if isset($form['antispamNoJs'])}
			<p n:class="$form['antispamNoJs']->hasErrors() ? 'has-error' : 'u-js-hide'" data-controller="antispam">
				<label n:name="antispamNoJs">
					{$form['antispamNoJs']->caption|noescape} {if $form['antispamNoJs']->isRequired()}*{/if}
				</label>
				<span class="inp-fix">
					<input n:name="antispamNoJs" class="inp-text" data-antispam-target="input">
				</span>
			</p>
		{/if}
		{php $form->setTranslator($formTranslator)}
		{*/ANTISPAM*}

		<div class="block-loader__loader"></div>

		{if isset($flashes[0]) && $flashes[0]->type == "ok" && $flashes[0]->message == "newsletterAdded"}
			<script>
				dataLayer.push({
					'event' : 'action.optin.newsletter'
				});
			</script>
		{/if}
	</form>
{/snippet}
