<?php declare(strict_types = 1);

namespace App\FrontModule\Components\CurrencyDetector;

use App\FrontModule\Components\CurrencyDetector\Exception\CurrencyDetectorException;
use App\Model\Currency\CurrencyHelper;
use App\Model\Orm\IpAddress\IpAddressModel;
use App\Model\Orm\State\State;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserRepository;
use App\Model\TranslatorDB;
use Jaybizzle\CrawlerDetect\CrawlerDetect;
use Nette\Application\UI\Control;
use Throwable;

final class CurrencyDetector extends Control
{

	public function __construct(
		private readonly ?User $userEntity,
		private readonly IpAddressModel $ipAddressModel,
		private readonly CrawlerDetect $crawlerDetect,
		private readonly TranslatorDB $translator,
		private readonly UserRepository $userRepository,
	)
	{}

	public function render(): void
	{
		if ($this->crawlerDetect->isCrawler()) {
			return;
		}

		try {
			$this->getTemplate()->showBanner = $this->showBanner() && $this->isUserWithoutPrefferedCurrency();
			$this->getTemplate()->isCzechCrown = CurrencyHelper::getCurrency()->getCurrencyCode() === CurrencyHelper::CURRENCY_CZK;

			$this->getTemplate()->setTranslator($this->translator);
			$this->getTemplate()->setFile(__DIR__ . '/templates/currencyDetector.latte');
			$this->getTemplate()->render();
		} catch (Throwable $exception) {
			//just catch
			bdump($exception);
		}
	}

	private function isUserWithoutPrefferedCurrency(): bool
	{
		if ($this->userEntity === null) {
			return true;
		}

		return $this->userEntity->preferredCurrency === null;
	}

	private function showBanner(): bool
	{
		return $this->ipAddressModel->getCountryCode() !== State::CODE_CZ && $this->ipAddressModel->isCountryInEurope();
	}

	public function handleChangeCurrency(): void
	{
		CurrencyHelper::setCurrencyCode(CurrencyHelper::CURRENCY_EUR);

		if ($this->userEntity !== null) {
			$this->userEntity->preferredCurrency = CurrencyHelper::getCurrencyCode();
			$this->userRepository->persistAndFlush($this->userEntity);
		}

		$this->redrawControl('currencyBanner');
	}

}
