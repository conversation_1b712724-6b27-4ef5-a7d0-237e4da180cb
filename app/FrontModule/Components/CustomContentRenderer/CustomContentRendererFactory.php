<?php

declare(strict_types=1);

namespace App\FrontModule\Components\CustomContentRenderer;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;
use Nextras\Orm\Entity\IEntity;

interface CustomContentRendererFactory
{
	public function create(
		IEntity $object,
		Mutation $mutation,
		PriceLevel $priceLevel,
		State $state,
	): CustomContentRenderer;
}
