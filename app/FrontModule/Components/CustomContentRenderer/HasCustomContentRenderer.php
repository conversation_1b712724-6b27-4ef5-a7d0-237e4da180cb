<?php declare(strict_types = 1);

namespace App\FrontModule\Components\CustomContentRenderer;

use Nextras\Orm\Entity\IEntity;

trait HasCustomContentRenderer
{

	private CustomContentRendererFactory $customContentRendererFactory;

	public function injectCustomContentRendererFactory(CustomContentRendererFactory $customContentRendererFactory): void
	{
		$this->customContentRendererFactory = $customContentRendererFactory;
	}

	protected function createComponentCustomContentRenderer(): CustomContentRenderer
	{
		assert($this->object instanceof IEntity);
		return $this->customContentRendererFactory->create($this->object, $this->mutation, $this->priceLevel, $this->currentState);
	}

}
