
{var $props = [
	templateDirectory: (isset($props['templateDirectory'])) ? $props['templateDirectory'] : $defaultTemplateDirectory,
	customContent: (isset($props['customContent'])) ? $props['customContent'] : $defaultObjectCC,
	ccClass: (isset($props['ccClass'])) ? $props['ccClass'] : false,
	isLocked: (isset($props['isLocked'])) ? $props['isLocked'] : false,
]}

{if !Nette\Utils\Strings::endsWith($props['templateDirectory'], '/')}
	{var $props['templateDirectory'] = $props['templateDirectory'].'/'}
{/if}

{var $sameTemplateCounter = 0}
{var $prevItemTemplate = ''}
{var $hasAudio = false}

{foreach $props['customContent'] as $key=>$item}
	{var $templateName = substr($key, 0, strpos($key, '____'))}
	{if $templateName == 'audio'}
		{var $hasAudio = true}
	{/if}
{/foreach}

<div n:tag-if="$hasAudio && !$props['isLocked']" class="audio-content tw-contents">
	{foreach $props['customContent'] as $key=>$item}
		{var $item = $item[0]} {*remove first level of group*}
		{var $templateName = substr($key, 0, strpos($key, '____'))}
		{var $tocName = $item->tocName ?? false}

		{if $prevItemTemplate !== $templateName}
			{php $sameTemplateCounter = 0}
			{php $prevItemTemplate = $templateName}
		{else}
			{php $sameTemplateCounter++}
		{/if}

		<div n:tag-if="$tocName && !$props['isLocked']" id="{$tocName|webalize}">
			{if $props['isLocked'] && $templateName == 'content'}
				{* Uzamčený obsah - zobraz pouze první CC "content" s ořezaným obsahem *}
				{embed $templates.'/part/box/locked.latte', class: $props['ccClass'], pages: $pages, object: $object, mutation: $mutation}
					{block content}
						{include $props['templateDirectory'].$templateName.'.latte', truncate: 2000, customContentItem=>$item, parentIterator=>$iterator, sameTemplateCounter=>$sameTemplateCounter}
					{/block}
				{/embed}

				{breakIf true}
			{elseif !$props['isLocked']}
				{include $props['templateDirectory'].$templateName.'.latte',
					ccClass: $props['ccClass'],
					customContentItem=>$item,
					parentIterator=>$iterator,
					sameTemplateCounter=>$sameTemplateCounter
				}
			{/if}
		</div>

	{/foreach}
</div>
