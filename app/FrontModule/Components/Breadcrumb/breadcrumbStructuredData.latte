
{if count($breadcrumbs) > 0}

	<script type="application/ld+json">
	{
		"@context": "https://schema.org",
		"@type": "BreadcrumbList",
		"itemListElement": [
		{foreach $breadcrumbs as $key=>$i}


			{if $i instanceOf App\Model\Orm\Routable}
				{capture $url}{plink $i}{/capture}
			{else}
				{var $url = $mutation->getBaseUrlWithPrefix()}
			{/if}

			{
				"@type": "ListItem",
				"position": {$key+1},
				"name": {$i->nameAnchor},
				"item": {$url}
			}{if !$iterator->last},{/if}
		{/foreach}
		]
	}
	</script>
{/if}
