{default $class = false}

<nav {*n:if="count($breadcrumbs) > 2"*} n:class="m-breadcrumb, $class">
	{define #bc}
		{default $class = false}
		<p n:class="m-breadcrumb__wrap, u-mb-0">
			<strong class="u-vhide">
				{_breadcrumb_title}
			</strong>

			{foreach $breadcrumbs as $key=>$i}
				{if $i instanceof App\PostType\Page\Model\Orm\Tree}
					{var $nameAnchor = $i->getNameAnchorBreadcrumb()}
				{else}
					{var $nameAnchor = $i->nameAnchor}
				{/if}

				{if !$iterator->first && !$iterator->last}
					{('angle-right')|icon, 'm-breadcrumb__separator'}
				{/if}
				{if $iterator->first}
					<a href="{plink $i, category: null}" class="u-no-decoration">
						{if str_contains($class, 'm-breadcrumb--inverse')}
							<img src="/static/img/illust/symbol-inverse.svg" alt="{$nameAnchor}" loading="lazy" width="25" height="25">
						{else}
							<img src="/static/img/illust/symbol.svg" alt="{$nameAnchor}" loading="lazy" width="25" height="25">
						{/if}
					</a>
				{elseif $iterator->last}
					{if !$object instanceof App\Model\Orm\ProductLocalization\ProductLocalization}
						{('angle-right')|icon, 'm-breadcrumb__separator'}<span class="m-breadcrumb__link">{$nameAnchor}{if isset($_GET[search]) && $iterator->isLast() && $object->uid == 'search'}: {$_GET[search]}{/if}</span>
					{/if}
				{else}
					<a href="{plink $i, category: null, page: null}" class="m-breadcrumb__link">{$nameAnchor}</a>
				{/if}
			{/foreach}
		</p>
	{/define}

	{* {php $lastCategory = $breadcrumbs[count($breadcrumbs) - 2] ?? false}
	{if $lastCategory}
		{include #bc, class: 'u-d-n u-d-b@md'}
		<p class="m-breadcrumb__wrap u-mb-0 u-d-n@md">
			{('angle-left-bold')|icon, 'm-breadcrumb__separator'}
			<a href="{plink $lastCategory}" class="m-breadcrumb__link">{$lastCategory->nameAnchor}</a>
		</p>
	{else} *}
		{include #bc}
	{* {/if} *}
</nav>
