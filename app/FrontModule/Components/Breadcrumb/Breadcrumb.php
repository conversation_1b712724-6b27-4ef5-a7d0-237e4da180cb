<?php declare(strict_types = 1);

namespace App\FrontModule\Components\Breadcrumb;

use App\FrontModule\Components\MainCategories\MainCategories;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Orm;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\Routable;
use App\Model\StaticPage\StaticPage;
use App\Model\TranslatorDB;
use ArrayIterator;
use Nette\Application\UI\Control;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use stdClass;

/**
 * @property-read DefaultTemplate $template
 */
final class Breadcrumb extends Control
{

	public function __construct(
		private readonly Routable|StaticPage $object,
		private readonly MutationHolder $mutationHolder,
		private readonly Orm $orm,
		private readonly TranslatorDB $translator,
	)
	{
	}


	private function initTemplate(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->breadcrumbs = $this->getBreadcrumb();
		$template->object = $this->object;
		$template->mutation = $this->mutationHolder->getMutation();
		$template->FE_TEMPLATE_DIR = FE_TEMPLATE_DIR;
	}


	public function render(array $args = []): void
	{
		$this->initTemplate();
		if (isset($args['class'])) {
			$this->template->class = $args['class'];
		}
		if (isset($args['variant'])) {
			$this->template->variant = $args['variant'];
		}

		$this->template->render(__DIR__ . '/breadcrumb.latte');
	}


	public function renderStructuredData(): void
	{
		$this->initTemplate();
		$this->template->render(__DIR__ . '/breadcrumbStructuredData.latte');
	}


	private function getBreadcrumb(): array
	{
		$ret = [];

		$path = [];
		if ($this->object instanceof ProductVariant) {
			$path = $this->object->product->path;
		} elseif (isset($this->object->path)) {
			$path = $this->object->path;
		}

		assert(is_array($path) || $path instanceof ArrayIterator);

		if ($path instanceof ArrayIterator && $path->count()
			|| is_array($path) && count($path)
		) {
			foreach ($path as $p) {
				if (is_int($p)) {
					$tree = $this->orm->tree->getById($p);
				} else {
					$tree = $p;
				}

				if ($tree && $tree->public) {
					$ret[] = $tree;
				}
			}
		}

		$ret[] = $this->object;


		if ($mainCategory = $this->filterMainCategory()) {
			$ret[] = $mainCategory;
		}

		if ($order = $this->filterOrder()) {
			$ret[] = $order;
		}

		return $ret;
	}


	public function filterMainCategory(): ?stdClass
	{
		if ($this->getPresenter()->getAction() !== 'detail') {
			return null;
		}

		if (($filterCategory = $this->getPresenter()->getComponent('mainCategories', false)) === null || ($selectedCategory = $filterCategory->getSelectedCategory()) === null) {
			return null;
		}

		$category = new stdClass();
		$category->nameAnchor = $selectedCategory->nameAnchor;

		return $category;
	}

	public function filterOrder(): ?stdClass
	{
		if (($orderHash = $this->getPresenter()->getParameter('orderHash')) === null) {
			return null;
		}

		$order = $this->orm->order->getBy(['hash' => $orderHash]);

		if($order === null) {
			return null;
		}

		$filter = new stdClass();
		$filter->nameAnchor = str_replace(['%s', '%i'], [$order->orderNumber, $order->orderNumber], $this->translator->translate('order_detail_title'));

		return $filter;
	}

}
