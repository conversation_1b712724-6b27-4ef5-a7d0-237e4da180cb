{snippet form}
	{default $class = false}
	{form form class=>'f-login', data-naja=>'', novalidate=>'novalidate', data-naja-history=>"off"}
		{control messageForForm, $flashes, $form}

		{include '../inp.latte', pClass=>'f-login__inp', labelClass: 'u-fw-n', form=>$form, name=>username}
		{include '../inp.latte', pClass=>'f-login__inp u-mb-sm', labelClass: 'u-fw-n', form=>$form, name=>password, password: true}

		<p class="u-ta-c u-mb-sm">
			<button type="submit" class="btn btn--block btn--secondary btn--loader">
				<span class="btn__text">
					{_btn_login}
				</span>
			</button>
		</p>

		<p class="u-ta-c u-mb-sm">
			<a href="{plink $pages->lostPassword}" data-naja="" data-naja-history="off" data-modal='{"medium": "fetch", "modalClass": "b-modal--xs"}' data-snippetid="snippet--userFormArea">{_link_lost_password}</a>
		</p>

		{* <p class="text-divider u-mb-sm">
			<span class="text-divider__text">{_"header_login_or_socials"}</span>
		</p>

		<p class="f-login__social">
			{control googleLogin}
		</p>
		<p class="f-login__social u-mb-0">
			{control facebookLogin}
		</p> *}
		{* <p class="f-login__social">
			{control seznamLogin}
		</p> *}

		<p class="u-ta-c u-mb-0"> {* u-pt-sm u-mb-sm *}
			{_"user_login_promo_title"}
			<a href="{plink $pages->registration}" data-naja="" data-naja-history="off" data-modal='{"medium": "fetch", "modalClass": "b-modal--xs"}' data-snippetid="snippet--userFormArea">{$pages->registration->nameAnchor}</a>
		</p>

		{* {php $registrationBenefits = $object->cf->registration_benefits->content ?? false}
		<div n:if="$registrationBenefits" class="b-bubble">
			<div class="b-bubble__box u-mb-last-0">
				{$registrationBenefits|tables|lazyLoading|obfuscateEmailAddresses|noescape}
			</div>
		</div> *}

		{if count($flashes) > 0 && $flashes[0]->type == 'ok'}
			<script>
				location.reload();
			</script>
		{/if}
	{/form}
{/snippet}
