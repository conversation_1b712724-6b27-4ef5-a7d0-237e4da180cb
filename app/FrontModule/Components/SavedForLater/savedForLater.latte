{*<form n:class="f-basket, $class, block-loader" n:name="form" data-naja data-naja-loader="body" data-naja-force-redirect data-naja-history="off">*}
<form n:name="form" data-naja data-naja-loader="body" data-naja-force-redirect data-naja-history="off" class="b-layout-basket__postponed" n:if="$products->count() || $classEvents->count()">
	<div class="f-basket__head f-basket__head--center">
		<h2 class="f-basket__title">
			{_"postponed_title"}
		</h2>
	</div>
	<div class="b-cart">
		<table class="b-cart__table">
			<tbody class="b-cart__body">
				{foreach $products as $productItem}{include productItem}{/foreach}
				{foreach $classEvents as $classItem}{include classItem}{/foreach}
			</tbody>
		</table>
	</div>
</form>

{define productItem}
	<tr class="b-cart__row">
		{varType App\Model\Orm\Order\Product\ProductItem $productItem}
		{var $variant = $productItem->variant}
		{var $link = $presenter->link($productItem->variant->product, ['v' => $variant->id])}
		{php $productDto = $productDtoProviderCallback($productItem->variant->product, $variant)}
		<td class="b-cart__img">
			<a href="{$link}">
				{* todo: img--contain class pouze pro produkty, které nejsou kurzy *}
				{if $productItem->variant && $productItem->variant->firstImage}
					<img class="img img--4-3 img--contain" src="{$productItem->variant->firstImage->getSize('sm')->src}" alt="" loading="lazy">
				{else}
					<img class="img img--4-3 img--contain" src="/static/img/illust/noimg.svg" alt="" loading="lazy"/>
				{/if}
			</a>
			{include $templates.'/part/core/type.latte', class: 'b-cart__type flag--type', product: $productItem->variant->product}
		</td>
		<td class="b-cart__content">
			<p class="tw-mb-0">
				<a href="{$link}" class="b-cart__link u-fw-b prefetch">
					{$productItem->getName()}
				</a>
			</p>
			<p class="b-cart__variant tw-mb-0">
				Definice varianty (velikost, barva apod.)
			</p>
			{ifset $changedItems[$productItem->id]}
				<p class="b-cart__availability">
					{var App\Model\Orm\Order\RefreshResult $changedItem = $changedItems[$productItem->id]}
					{if $changedItem->availableAmount === 0}
						<b class="u-c-red">
							{_"cart_availability_not_for_sale"} {* Neprodejný *}
						</b>
					{elseif $changedItem->amount > $changedItem->availableAmount}
						<b class="u-c-green">
							{_"cart_availability_available"} {$changedItem->availableAmount} {_"stock_piece"} {* Skladem *}
						</b>
					{include $templates.'/part/core/availability.latte', class: 'u-d-b', showTooltip: false, variant: $productItem->variant, productDto=>$productDto}
						<b class="u-c-help">
							{_"cart_availability_unavailable"} {$changedItem->amount - $changedItem->availableAmount} {_"stock_piece"} {* Nedostupný *}
						</b>
					{/if}
				</p>
			{else}
				{include $templates.'/part/core/availability.latte', class: 'b-cart__availability', variant: $productItem->variant, productDto=>$productDto}
			{/ifset}
		</td>
		<td class="b-cart__price-unit">
			{$productItem->unitPriceVat|money} / {_"stock_piece"}
		</td>
		<td class="b-cart__count">
			{include $templates . '/part/form/part/count.latte',
				autosubmit: true,
				variant: $variant,
				input: $control['form']['products'][$productItem->variant->id]['quantity'],
				maxAmount: $productItem->getMaxAvailableAmount(),
				class: false
			}
		</td>
		<td class="b-cart__btn">
			<a n:href="addToCartItem!, productItemId: $productItem->id" data-naja data-naja-history="off" data-naja-loader="body" class="btn">
				<span class="btn__text">
					{_"btn_buy"}
				</span>
			</a>
		</td>
		<td class="b-cart__more">
			{include menu, removeUrl: $control->link('removeSavedForLaterItem', ['productItemId' => $productItem->id])}
		</td>
	</tr>
{/define}

{define classItem}
	<tr class="b-cart__row">
		{varType App\Model\Orm\Order\Class\ClassItem $classItem}
		{var $variant = $classItem->getProduct()->firstVariant}
		{var $link = $presenter->link($classItem->getProduct(), ['v' => $variant->id])}
		{php $productDto = $productDtoProviderCallback($classItem->getProduct(), $variant)}
		<td class="b-cart__img">
			<a href="{$link}">
				{if $classItem->getProduct()->firstVariant && $classItem->getProduct()->firstVariant->firstImage}
					<img class="img img--4-3" src="{$classItem->getProduct()->firstVariant->firstImage->getSize('sm')->src}" alt="" loading="lazy">
				{else}
					<img class="img img--4-3" src="/static/img/illust/noimg.svg" alt="" loading="lazy"/>
				{/if}
			</a>
			{include $templates.'/part/core/type.latte', class: 'b-cart__type flag--type tw-min-h-[2.2rem] tw-text-[1.1rem]', product: $classItem->getProduct()}
		</td>
		<td class="b-cart__content">
			<p class="tw-mb-0">
				<a href="{$link}" class="b-cart__link u-fw-b prefetch">
					{$classItem->getName()}
				</a>
			</p>
			<p class="b-cart__variant tw-mb-0">
				{*Definice varianty (velikost, barva apod.)*}
				{$classItem->getDesc()}

			</p>

				{if $classItem->classEvent !== null}
					Počet volných míst: {$classItem->getMaxAvailableAmount()}
				{else}
					{* TODO popis online kurzu v kosiku *}
				{/if}
				<div n:if="$classItem->hasRequalification()" style="color:red;"> uhrada UP 82%</div> {* TODO *}
				{*include $templates.'/part/core/availability.latte', class: 'b-cart__availability', variant: $variant, productDto=>$productDto*}

		</td>
		<td class="b-cart__price-unit" n:if="!$cartPricesWithVatModeProvider->isActive">
			{$classItem->getUnitPriceVat(2)|money} / {_"stock_piece"}
		</td>
		<td class="b-cart__price-unit" n:if="$cartPricesWithVatModeProvider->isActive">
			{$classItem->getUnitPrice(2)|money} / {_"stock_piece"}
		</td>
		<td class="b-cart__count">
			{include $templates . '/part/form/part/count.latte',
				autosubmit: true,
				variant: $variant,
				input: $control['form']['classEvents'][$classItem->getIdentifier()]['quantity'],
				maxAmount: $classItem->getMaxAvailableAmount(),
				class: false
			}
		</td>
		<td class="b-cart__btn">
			<a n:href="addToCartClass!, classItemId: $classItem->id" data-naja data-naja-history="off" data-naja-loader="body" class="btn">
				<span class="btn__text">
					{_"btn_buy"}
				</span>
			</a>
		</td>
		<td class="b-cart__more">
			{include menu, removeUrl: $control->link('removeSavedForLaterClass', ['classItemId' => $classItem->id])}
		</td>
	</tr>
{/define}

{define menu}
	{embed $templates.'/part/core/tooltip.latte', class: 'b-more', placement: 'bottom-start', settings: '{"trigger": "click"}'}
		{block btn}
			<button type="button" class="b-more__btn as-link">{('dots')|icon}</button>
		{/block}
		{block content}
			<div class="b-more__content" data-controller="toggle-class">
				<button type="button" class="b-more__close as-link js-tooltip-close">
					{('cross')|icon}
					<span class="u-vhide">{_"btn_close"}</span>
				</button>
				<div class="b-more__menu">
					<p n:ifset="$removeUrl">
						<button type="button" class="b-more__link as-link" data-action="toggle-class#toggle" data-toggle-class="is-open-remove">{_"btn_remove_from_saved_for_later"}</button>
					</p>
				</div>
				<div class="b-more__remove" n:ifset="$removeUrl">
					<p class="u-fw-b u-ta-c">
						{_"more_remove_title_saved_for_later"}
					</p>
					<p class="b-more__btns tw-mb-0">
						<a href="{$removeUrl}" class="btn btn--bd" data-naja data-naja-history="off" data-naja-loader="body">
							<span class="btn__text">
								{_"yes"}
							</span>
						</a>
						<button type="button" class="btn btn--bd" data-action="toggle-class#toggle" data-toggle-class="is-open-remove">
							<span class="btn__text">
								{_"no"}
							</span>
						</button>
					</p>
				</div>
			</div>
		{/block}
	{/embed}
{/define}
