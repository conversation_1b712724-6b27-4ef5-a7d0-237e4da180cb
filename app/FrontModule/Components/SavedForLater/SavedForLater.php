<?php

namespace App\FrontModule\Components\SavedForLater;

use App\FrontModule\Components\Cart\Cart;
use App\FrontModule\Components\Cart\CartPricesWithVatModeProvider;
use App\FrontModule\Presenters\BasePresenter;
use App\Model\DTO\Product\ProductDtoProvider;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Order\OrderItem;
use App\Model\Orm\Order\Product\ProductItem;
use App\Model\Orm\Orm;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\ShoppingCart\SavedForLaterProvider;
use App\Model\TranslatorDB;
use Nette\Application\UI\Control;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Forms\Controls\TextInput;
use Nette\Utils\ArrayHash;
use Nette\Application\UI;

/**
 * @property-read DefaultTemplate $template
 * @property-read Control $parent
 * @property-read BasePresenter $presenter
 */
class SavedForLater extends Control
{

	private Cart $cart;

	private array $amounts;

	public function __construct(
		private readonly Orm $orm,
		private readonly TranslatorDB $translator,
		private readonly MutationHolder $mutationHolder,
		private readonly SavedForLaterProvider $savedForLaterProvider,
		private readonly ProductDtoProvider $productDtoProvider,
		private readonly CartPricesWithVatModeProvider $cartPricesWithVatModeProvider,
	)
	{
		$this->onAnchor[] = $this->initCart(...);
		$this->onAnchor[] = $this->initSaveForLater(...);
	}

	public function initCart(): void
	{
		if ($this->parent instanceof Cart) {
			$this->cart = $this->parent;
		}
	}

	public function initSaveForLater(): void
	{
		$this->amounts = $this->savedForLaterProvider->getAmounts();
	}


	public function getTemplateParameters(): array
	{
		return [
			'translator' => $this->translator,
			'object' => $this->presenter->getObject(),
			'templates' => FE_TEMPLATE_DIR,
			'pages' => $this->mutationHolder->getMutation()->pages,
			'mutation' => $this->mutationHolder->getMutation(),
			'cartPricesWithVatModeProvider' => $this->cartPricesWithVatModeProvider,
		];
	}

	public function beforeRender(): void
	{
		$this->savedForLaterProvider->refresh();

		$this->template->setTranslator($this->translator);
		$this->template->setParameters($this->getTemplateParameters());

		$this->template->productDtoProviderCallback = function (Product $product, ProductVariant $variant) {
			return $this->productDtoProvider->get($product, $variant);
		};
	}
	public function render(): void
	{
		$this->beforeRender();

		$this->template->products = $this->savedForLaterProvider->getProducts();
		$this->template->classEvents = $this->savedForLaterProvider->getClassEvents();

		$this->template->render(__DIR__ . '/savedForLater.latte');
	}

	public function createComponentForm(): UI\Form
	{
		$form = new UI\Form();
		$form->setTranslator($this->translator);

		$products = $form->addContainer('products');
		/** @var ProductItem $productItem */
		foreach ($this->savedForLaterProvider->getProducts() as $productItem) {
			$container = $products->addContainer($productItem->variant->id);
			$container->addInteger('quantity', 'quantity')->setDefaultValue($productItem->amount);
		}

		$classEvents = $form->addContainer('classEvents');

		foreach ($this->savedForLaterProvider->getClassEvents() as $classItem) {
			$container = $classEvents->addContainer($classItem->getIdentifier() ?? 0);
			$container->addInteger('quantity', 'quantity')->setDefaultValue($classItem->amount);
		}

		$form->addSubmit('send');

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];

		return $form;
	}

	public function formError(UI\Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSucceeded(UI\Form $form, ArrayHash $values): void
	{
		foreach ($values->products as $productVariantId => $data) {
			$key = 'products-' . $productVariantId;
			if ($data->quantity !== $this->amounts[$key]) {
				$variant = $this->orm->productVariant->getById($productVariantId);
				$productItem = $this->savedForLaterProvider->getProductItem($variant);
				$this->savedForLaterProvider->subtract($productItem, $data->quantity);
			}
		}

		foreach ($values->classEvents as $classEventIdentifier => $data) {
			[$productId, $classEventId, $priceLevelId] = explode('_', $classEventIdentifier);
			$key = 'classEvents-' . $classEventIdentifier;
			if ($data->quantity !== $this->amounts[$key]) {
				$product = $this->orm->product->getById($productId);
				$classEvent = $classEventId > 0 ? $this->orm->classEvent->getById($classEventId) : null;
				$priceLevel = $priceLevelId > 0 ? $this->orm->priceLevel->getById($priceLevelId) : null;
				$classItem = $this->savedForLaterProvider->getClassItem($product, $classEvent, $priceLevel);
				$this->savedForLaterProvider->subtract($classItem, $data->quantity);
			}
		}

		$this->initSaveForLater();

		foreach ($this->amounts as $key => $amount) {
			[$type, $keyId] = explode('-', $key);
			/** @var TextInput $control */
			$control = $this['form'][$type][$keyId]['quantity']; // @phpstan-ignore-line
			$control->setValue($amount);
		}

		if (!$this->presenter->isAjax()) {
			$this->presenter->redirect('this');
		}

		$this->redrawControl();
		$this->getPresenterIfExists()?->redrawControl('cart');
	}

	public function handleAddToCartItem(int $productItemId): void
	{
		$productItem = $this->orm->orderProduct->getById($productItemId);
		$this->doAdd($productItem);
	}

	public function handleAddToCartClass(int $classItemId): void
	{
		$classItem = $this->orm->orderClassEvent->getById($classItemId);
		$this->doAdd($classItem);
	}

	public function doAdd(OrderItem $orderItem): void
	{
		$this->initCart();
		$this->savedForLaterProvider->toCart($orderItem);
		$this->cart->initCart();

		if (!$this->presenter->isAjax()) {
			$this->presenter->redirect('this');
		}

		$this->presenter->redrawControl();
	}

	public function handleRemoveSavedForLaterItem(int $productItemId): void
	{
		$productItem = $this->orm->orderProduct->getById($productItemId);
		$this->doRemove($productItem);
	}

	public function handleRemoveSavedForLaterClass(int $classItemId): void
	{
		$classItem = $this->orm->orderClassEvent->getById($classItemId);
		$this->doRemove($classItem);
	}

	private function doRemove(OrderItem $orderItem): void
	{
		$this->savedForLaterProvider->remove($orderItem);

		if (!$this->presenter->isAjax()) {
			$this->presenter->redirect('this');
		}

		$this->presenter->redrawControl();
	}

}
