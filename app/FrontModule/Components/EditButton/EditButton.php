<?php declare(strict_types = 1);

namespace App\FrontModule\Components\EditButton;

use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\Routable;
use App\PostType\Calendar\Model\Orm\CalendarLocalization;
use App\PostType\CalendarTag\Model\Orm\CalendarTag\CalendarTagLocalization;
use App\PostType\Discount\Model\Orm\DiscountLocalization\DiscountLocalization;
use App\PostType\Brand\Model\Orm\BrandLocalization;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\CommonTree;
use App\Model\Orm\User\User;
use App\Model\TranslatorDB;
use App\PostType\Author\Model\Orm\AuthorLocalization;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalization;
use App\PostType\Tag\Model\Orm\TagLocalization\TagLocalization;
use LogicException;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;

/**
 * @property-read DefaultTemplate $template
 */
final class EditButton extends UI\Control
{

	public function __construct(
		private readonly ?Routable $routable,
		private readonly ?User $user,
		private readonly TranslatorDB $translator,
	)
	{
	}


	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->add('showEditButton', $this->routable !== null && $this->user !== null && in_array($this->user->role, [User::ROLE_ADMIN, User::ROLE_DEVELOPER]));
		if ($this->routable !== null) {
			$this->template->add('link', $this->getLink($this->routable));
		}

		$this->template->render(__DIR__ . '/editButton.latte');
	}


	private function getLink(Routable $routable): string
	{
		return match (get_class($routable)) {
			CatalogTree::class, CommonTree::class => $this->presenter->link(':Page:Admin:Page:default', ['id' => $routable->getId()]),
			BlogTagLocalization::class =>  $this->presenter->link(':BlogTag:Admin:BlogTag:edit', ['id' => $routable->getId()]),
			BlogLocalization::class =>  $this->presenter->link(':Blog:Admin:Blog:edit', ['id' => $routable->getId()]),
			CalendarTagLocalization::class =>  $this->presenter->link(':CalendarTag:Admin:Calendar:edit', ['id' => $routable->getId()]),
			CalendarLocalization::class =>  $this->presenter->link(':Calendar:Admin:Calendar:edit', ['id' => $routable->getId()]),
			AuthorLocalization::class =>  $this->presenter->link(':Author:Admin:Author:edit', ['id' => $routable->getId()]),
			TagLocalization::class =>  $this->presenter->link(':Tag:Admin:Tag:edit', ['id' => $routable->getId()]),
			ProductLocalization::class => $this->getLinkForProductVariant($routable),
			DiscountLocalization::class =>  $this->presenter->link(':Discount:Admin:Discount:edit', ['id' => $routable->getId()]),
			BrandLocalization::class => $this->presenter->link(':Brand:Admin:Brand:edit', ['id' => $routable->getId()]),
			default => throw new LogicException(sprintf("Unknown Routable object class '%s' for EditButton.", get_class($routable)))
		};
	}


	private function getLinkForProductVariant(Routable $routable): string
	{
		assert($routable instanceof ProductLocalization);
		$parent = $routable->getParent();

		return $this->presenter->link(':Admin:Product:edit', ['id' => $parent->id]);
	}

}
