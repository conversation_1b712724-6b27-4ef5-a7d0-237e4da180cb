<?php declare(strict_types = 1);

namespace App\FrontModule\Components\AddToMyLibrary;

use App\Event\AddToWishlist;
use App\FrontModule\Components\ProductBox\ProductBox;
use App\FrontModule\Presenters\Product\ProductPresenter;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\MyLibrary\MyLibraryModel;
use App\Model\Orm\MyLibraryProduct\MyLibraryProduct;
use App\Model\Orm\MyLibraryProduct\MyLibraryProductRepository;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\State\State;
use App\Model\Orm\User\User;
use App\Model\TranslatorDB;
use Brick\Money\Currency;
use Nette\Application\UI\Control;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;

final class AddToMyLibrary extends Control
{

	private bool $isInMyLibrary;

	public function __construct(
		private readonly Product $product,
		private readonly ?User $userEntity,
		private readonly Mutation $mutation,
		private readonly PriceLevel $priceLevel,
		private readonly State $state,
		private readonly Currency $currency,
		private readonly MyLibraryProductRepository $myLibraryProductRepository,
		private readonly TranslatorDB $translator,
		private readonly EventDispatcherInterface $eventDispatcher,
		private readonly MyLibraryModel $myLibraryModel,
	)
	{
		$this->onAnchor[] = $this->init(...);
	}

	private function init(): void
	{
		$this->isInMyLibrary = ($this->userEntity !== null) ? $this->myLibraryModel->isProductLibrary($this->userEntity, $this->product) : false;
	}

	public function render(): void
	{
		$this->getTemplate()->isInMyLibrary = $this->isInMyLibrary;
		$this->getTemplate()->product = $this->product;
		$this->getTemplate()->inProductBox = $this->getParent() instanceof ProductBox;
		$this->getTemplate()->inProductDetail = $this->getParent() instanceof ProductPresenter;
		$this->getTemplate()->userEntity = $this->userEntity;
		$this->getTemplate()->clubPage = $this->mutation->pages->loyaltyProgram;

		$this->getTemplate()->setFile(__DIR__ . '/templates/addToMyLibrary.latte');

		$this->getTemplate()->setTranslator($this->translator);
		$this->getTemplate()->render();
	}
	public function renderText(): void
	{
		$this->getTemplate()->setFile(__DIR__ . '/templates/addToMyLibraryText.latte');
		$this->getTemplate()->setTranslator($this->translator);
		$this->getTemplate()->render();
	}


	final public function handleAdd(): void
	{
		if ($this->userEntity === null) {
			return;
		}

		$libraryProduct = new MyLibraryProduct();
		$libraryProduct->myLibrary = $this->userEntity->library; // @phpstan-ignore-line
		$libraryProduct->product = $this->product; // @phpstan-ignore-line

		$this->myLibraryProductRepository->persistAndFlush($libraryProduct);

		$this->isInMyLibrary = true;

		$productLocalization = $libraryProduct->product->getLocalization($this->mutation);
		$this->eventDispatcher->dispatch(
			new AddToWishlist(
				productLocalization: $productLocalization,
				variant: $productLocalization->product->firstVariant,
				mutation: $this->mutation,
				state: $this->state,
				priceLevel: $this->priceLevel,
				currency: $this->currency,
			)
		);

		$this->myLibraryModel->isProductLibraryInvalidate();
		$this->redraw();
	}

	final public function handleRemove(): void
	{
		if (!$myProduct = $this->myLibraryProductRepository->getBy(['product' => $this->product->id])) {
			return;
		}

		$this->myLibraryProductRepository->removeAndFlush($myProduct);
		$this->isInMyLibrary = false;
		$this->myLibraryModel->isProductLibraryInvalidate();
		$this->redraw();
	}

	private function redraw(): void
	{
		$parent = $this->getParent();
		if ($parent instanceof ProductBox) {
			$parent->redrawControl('addToMyLibraryTag');
			$parent->redrawControl('addToMyLibraryLink');
		} elseif ($parent instanceof ProductPresenter) {
			$parent->redrawControl('addToMyLibraryTag');
		}
	}

}
