<?php declare(strict_types = 1);

namespace App\FrontModule\Components\AddToMyLibrary;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\State\State;
use App\Model\Orm\User\User;
use Brick\Money\Currency;

interface AddToMyLibraryFactory
{

	public function create(
		Product $product,
		?User $userEntity,
		Mutation $mutation,
		PriceLevel $priceLevel,
		State $state,
		Currency $currency,
	): AddToMyLibrary;

}
