{snippet controlAddToMyLibraryText}
	{if !$isInMyLibrary}
		{* Není v knihovně *}
		{if $userEntity === null}
			{* Uživtel není př<PERSON>š<PERSON>ý *}
			<button type="button" class="b-product__library-text u-c-gray as-link" data-controller="show-login" data-action="show-login#open" data-show-login-msg-value="{_'login_add_to_library_msg'|noescape}">
				{_"add_to_my_library"}
			</button>
		{elseif !$userEntity->isClubMember}
			{* Uživtel není členem klubu *}
			<a href="{plink $clubPage}" target="_blank" class="b-product__library-text u-c-gray">{_"add_to_my_library"}</a>
		{else}
			{* Přidání do knihovny *}
			<a n:href="add! productId: $product->id" class="b-product__library-text u-c-gray" data-naja data-naja-history="off">{_"add_to_my_library"}</a>
		{/if}
	{else}
		{* Je v knihovně *}
		<a n:href="remove! productId: $product->id" class="b-product__library-text u-c-gray" data-naja data-naja-history="off">{_"remove_from_my_library"}</a>
	{/if}
{/snippet}
