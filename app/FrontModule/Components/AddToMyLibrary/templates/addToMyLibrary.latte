
{snippet controlAddToMyLibrary}
	{define #linkContent}
		{if $inProductBox}
			{($icon)|icon}
			<span class="u-vhide">{_$text}</span>
		{else}
			{($icon)|icon, 'item-icon__icon'}
			<span class="item-icon__text">{_$text}</span>
		{/if}
	{/define}

	{if $inProductDetail}
		{php $icon = 'bookmark'}
		{php $iconActive = 'bookmark-full'}
	{else}
		{php $icon = 'library-small'}
		{php $iconActive = 'library-small-interactive'}
	{/if}


	{if !$isInMyLibrary}
		{* Není v knihovně *}
		{if $userEntity === null}
			{* Uživtel není p<PERSON>ihlášený *}
			<button type="button" n:class="$inProductBox ? 'b-product__library link-mask__unmask' : 'b-product-detail__library as-link item-icon'" data-controller="show-login" data-action="show-login#open" data-show-login-msg-value="{_'login_add_to_library_msg'|noescape}">
				{include #linkContent, icon: $icon, text: "add_to_my_library"}
			</button>
		{elseif !$userEntity->isClubMember}
			{* Uživtel není členem klubu *}
			<a href="{plink $clubPage}" target="_blank" n:class="$inProductBox ? 'b-product__library link-mask__unmask' : 'b-product-detail__library item-icon'">
				{include #linkContent, icon: $icon, text: "add_to_my_library"}
			</a>
		{else}
			{* Přidání do knihovny *}
			<a n:href="add!" n:class="$inProductBox ? 'b-product__library link-mask__unmask' : 'b-product-detail__library item-icon'" data-naja data-naja-history="off">
				{include #linkContent, icon: $icon, text: "add_to_my_library"}
			</a>
		{/if}
	{else}
		{* Je v knihovně *}
		<a n:href="remove!" n:class="$inProductBox ? 'b-product__library link-mask__unmask' : 'b-product-detail__library item-icon', is-active" data-naja  data-naja data-naja-history="off">
			{include #linkContent, icon: $iconActive, text: "remove_from_my_library"}
		</a>
	{/if}
{/snippet}
