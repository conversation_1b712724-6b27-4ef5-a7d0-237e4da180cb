<?php declare(strict_types = 1);

namespace App\FrontModule\Components\MyLibrary;

use App\FrontModule\Components\CatalogProducts\CatalogProducts;
use App\FrontModule\Components\CatalogProducts\CatalogProductsData;
use App\FrontModule\Components\CatalogProducts\CatalogProductsFactory;
use App\FrontModule\Presenters\HasPagerLimits;
use App\FrontModule\Presenters\Pageable;
use App\Model\ElasticSearch\Product\Repository;
use App\Model\ElasticSearch\Product\ResultReader;
use App\Model\Link\LinkFactory;
use App\Model\Orm\MyLibraryProduct\MyLibraryProductRepository;
use App\Model\Orm\Orm;
use App\Model\Orm\Routable;
use App\Model\Setup;
use App\Model\TranslatorDB;
use Nette\Application\UI\Control;

final class MyLibrary extends Control implements Pageable
{

	use HasPagerLimits;

	private array $libraryProductIds;

	public function __construct(
		private readonly Routable $routable,
		private readonly \App\Model\Orm\MyLibrary\MyLibrary|null $myLibrary,
		private readonly Setup $setup,
		private readonly int $page,
		private readonly TranslatorDB $translator,
		private readonly LinkFactory $linkFactory,
		private readonly Orm $orm,
		private readonly MyLibraryProductRepository $libraryProductRepository,
		private readonly Repository $esProductRepository,
		private readonly CatalogProductsFactory $catalogProductsFactory,
		private readonly ResultReader $productResultReader,
	)
	{
		$this->onAnchor[] = $this->init(...);
	}

	public function init(): void
	{
		$this->libraryProductIds = $this->getProducts();
	}

	final public function render(): void
	{
		$this->getTemplate()->libraryShareLink = $this->createShareLink();
		$this->getTemplate()->showLibrary = $this->shouldBeRendered();
		$this->getTemplate()->isSharedLibrary = $this->isSharedLibrary();
		$this->getTemplate()->libraryProducts = $this->libraryProductIds;
		$this->getTemplate()->templates = FE_TEMPLATE_DIR;
		$this->getTemplate()->mutation = $this->setup->mutation;
		$this->getTemplate()->priceLevel = $this->setup->priceLevel;
		$this->getTemplate()->state = $this->setup->state;

		$this->getTemplate()->setTranslator($this->translator);
		$this->getTemplate()->setFile(__DIR__ . '/templates/myLibrary.latte');
		$this->getTemplate()->render();
	}

	private function getProducts(): array
	{
		$productIds = [];

		if ($this->myLibrary !== null) {
			$productIds = $this->libraryProductRepository->findLibraryProductIds($this->myLibrary);
		}

		return $productIds;
	}

	private function shouldBeRendered(): bool
	{
		return $this->myLibrary !== null;
	}

	public function createShareLink(): string|null
	{
		if ($this->myLibrary === null) {
			return null;
		}

		if (!$sharedLibraryPage = $this->orm->mutation->getDefault()->pages->sharedLibrary) { // @phpstan-ignore-line
			return null;
		}

		return $this->linkFactory->linkTranslateToNette($sharedLibraryPage, ['sharedLibraryUid' => $this->myLibrary->uid]);
	}

	private function isSharedLibrary(): bool
	{
		if ($this->myLibrary === null) {
			return true;
		}

		return $this->setup->userEntity?->id !== $this->myLibrary->user->getPersistedId();
	}




	public function getMyLibrary(): ?\App\Model\Orm\MyLibrary\MyLibrary
	{
		return $this->myLibrary;
	}


	protected function createComponentCatalogProducts(): CatalogProducts
	{
		$paramsToTemplate = [
			'productTitle' => 'h3',
			'ajaxPage' => true,
			'class' => 'u-mb-xs',
			'cleanFilterParam' => [],
		];

		$findCatalogProductsDataCallback = function (int $limit, int $offset) {
			$itemsObject = $this->esProductRepository->findByIds(
				ids: $this->libraryProductIds,
				mutation: $this->setup->mutation,
				onStockOnly: false,
				count: $limit,
				from: $offset,
			);

			return new CatalogProductsData(
				$this->productResultReader->mapResultToEntityCollection($itemsObject, $this->libraryProductIds),
				$itemsObject->getTotalHits(),
			);
		};

		return $this->catalogProductsFactory->create(
			$this->routable,
			$this->setup,
			$findCatalogProductsDataCallback,
			$this->page,
			$paramsToTemplate,
			'catalog',
			'library',
		);
	}

}
