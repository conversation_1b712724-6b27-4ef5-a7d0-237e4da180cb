{snippet myLibraryList}
	<p class="b-library__tools u-mb-xs">
		<a href="https://www.facebook.com/sharer/sharer.php?u={$libraryShareLink}" class="item-icon" target="_blank" rel="noopener noreferrer">
			{('facebook')|icon, 'item-icon__icon'}
			<span class="item-icon__text">{_share_facebook}</span>
		</a>
		<a href="mailto:?subject={_'library_share_email_subject'}&body={$libraryShareLink}" class="item-icon" target="_blank" rel="noopener noreferrer">
			{('envelope')|icon, 'item-icon__icon'}
			<span class="item-icon__text">
				{_share_email}
			</span>
		</a>
		<span class="b-library__copy" data-controller="copy" data-copy-content-value="{$libraryShareLink}">
			<button type="button" class="item-icon as-link" data-action="copy#copy">
				{('copy')|icon, 'item-icon__icon'}
				<span class="item-icon__text">{_copy_library_list}</span>
			</button>
			<span class="b-library__copied item-icon u-c-green">
				{('check')|icon, 'item-icon__icon'}
				<span class="item-icon__text">{_"copied"}</span>
			</span>
		</span>
	</p>

	<hr class="u-mt-0 u-mb-xs">

	<h2 class="h4 u-mt-0 u-mb-xs">
		{if $isSharedLibrary}{_"products_in_shared_library"}
		{else}{_"products_in_my_library"}{/if}
	</h2>


	{if $showLibrary && count($libraryProducts)}
		{control catalogProducts}
	{else}
		<p class="u-mb-xs">
			{_"library_is_empty"}
		</p>
	{/if}
{/snippet}
