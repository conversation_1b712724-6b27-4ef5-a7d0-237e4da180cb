<nav class="header__service m-service" n:if="$menu">
	<ul class="m-service__list">
		{foreach $menu as $item}
			{php $link = $item->page->link}
			{php $type = $link->toggle}

			<li class="m-service__item" n:if="$type">
				{php $page = isset($link->systemHref) && isset($link->systemHref->page) ? $link->systemHref->page->getEntity() ?? false : false}
				{php $hrefName = ($link->systemHref??->hrefName ?? false) ?: ($link->customHref??->hrefName ?? false)}
				{php $href = $link->customHref??->href ?? false}


				{if $type == 'systemHref' && $page}
					<a href="{plink $page}" n:class="m-service__link, $item->selected ? is-active">
						{if $hrefName}
							{$hrefName}
						{else}
							{$page->nameAnchor}
						{/if}
					</a>
				{elseif $type == 'customHref' && $href && $hrefName}
					<a href="{$href}" class="m-service__link" target="_blank" rel="noopener noreferrer">
						{$hrefName}
					</a>
				{/if}
			</li>
		{/foreach}
	</ul>
</nav>

