<?php declare(strict_types = 1);

namespace App\FrontModule\Components\ServiceMenu;

use App\Model\MenuService;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Routable;
use App\Model\Orm\RoutableEntity;
use App\Model\Pages;
use App\Model\StaticPage\StaticPage;
use App\Model\TranslatorDB;
use Nette\Application\UI\Control;

class ServiceMenu extends Control
{

	private readonly Pages $pages;


	public function __construct(
		private readonly Routable|StaticPage $object,
		private readonly MenuService $menuService,
		private readonly TranslatorDB $translator,
		private readonly MutationHolder $mutationHolder,
	)
	{
		$mutation = $this->mutationHolder->getMutation();
		$this->pages = $mutation->pages;
	}


	public function render(): void
	{
		$this->initTemplate();
		$this->template->menu = $this->menuService->getUserMenu($this->object, true, $this->mutationHolder->getMutation()->cf->serviceMenu ?? []);
		$this->template->render(__DIR__ . '/serviceMenu.latte');
	}


	private function initTemplate(): void
	{
		$this->getTemplate()->setTranslator($this->translator);
		$this->getTemplate()->pages = $this->pages;
		$this->getTemplate()->FE_TEMPLATE_DIR = FE_TEMPLATE_DIR;
	}
}
