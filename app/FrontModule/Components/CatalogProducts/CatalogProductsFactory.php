<?php

declare(strict_types=1);

namespace App\FrontModule\Components\CatalogProducts;

use App\Model\Orm\Routable;
use App\Model\Setup;
use App\Model\StaticPage\StaticPage;
use Closure;

interface CatalogProductsFactory
{

	/**
	 * @phpstan-param Closure(int $limit, int $offset): CatalogProductsData $findCatalogProductsDataCallback
	 */
	public function create(
		Routable|StaticPage $routable,
		Setup $setup,
		Closure $findCatalogProductsDataCallback,
		int $pageNumber,
		array $paramsToTemplate,
		string $scriptId,
		string $listId,
	): CatalogProducts;

}
