<?php declare(strict_types = 1);

namespace App\FrontModule\Components\CatalogProducts;

use App\Components\VisualPaginator\VisualPaginator;
use App\Components\VisualPaginator\VisualPaginatorFactory;
use App\FrontModule\Components\ProductBox\ProductBox;
use App\FrontModule\Components\ProductBox\ProductBoxFactory;
use App\Model\ConfigService;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductRepository;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\Routable;
use App\Model\Orm\Traits\HasGTMViewport;
use App\Model\Setup;
use App\Model\StaticPage\StaticPage;
use App\Model\TranslatorDB;
use Closure;
use Nette\Application\UI\Control;
use Nette\Application\UI\Multiplier;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Utils\ArrayList;
use Nextras\Orm\Collection\ICollection;

/**
 * @property-read DefaultTemplate $template
 */
final class CatalogProducts extends Control
{

	use HasGTMViewport;

	/** @var ICollection<Product>|ArrayList<Product|ProductVariant>  */
	private ICollection|ArrayList $products;

	private int $totalCount;

	private CatalogProductsData $catalogProductsData;

	private int $itemPerPage;

	public array $onAfterRender = [];

	private ?array $productsById = null;

	private ?array $productVariantsById = null;

	/**
	 * @phpstan-param Closure(int $limit, int $offset): CatalogProductsData $findCatalogProductsDataCallback
	 */
	public function __construct(
		private readonly Routable|StaticPage $routable,
		private readonly Setup $setup,
		private readonly Closure $findCatalogProductsDataCallback,
		private readonly int $pageNumber,
		private readonly array $paramsToTemplate,
		private readonly string $scriptId,
		private readonly string $listId,
		private readonly TranslatorDB $translator,
		private readonly ProductBoxFactory $productBoxFactory,
		private readonly ConfigService $configService,
		private readonly VisualPaginatorFactory $visualPaginatorFactory,
		private readonly ProductRepository $productRepository,
	)
	{
		$this->onAnchor[] = $this->init(...);
	}

	public function setItemPerPage(int $itemPerPage): void
	{
		$this->itemPerPage = $itemPerPage;
	}

	private function init(): void
	{
		$itemsPerPage = $this->itemPerPage ?? $this->configService->get('shop', 'productsPaging');
		$this['pager']->object  = $this->routable;
		$this['pager']->special = true;
		$this['pager']->setTranslator($this->translator);
		$paginator = $this['pager']->getPaginator();
		$paginator->setPage($this->pageNumber);
		$paginator->itemsPerPage = $itemsPerPage;

		$catalogProductsData = ($this->findCatalogProductsDataCallback)($paginator->itemsPerPage, $paginator->offset);
		assert($catalogProductsData instanceof CatalogProductsData);

		$this->catalogProductsData = $catalogProductsData;
		$paginator->itemCount = max($catalogProductsData->totalCount, 0);

		$this->initGTMViewport($this->catalogProductsData->products, $this->scriptId, $this->listId, $this->routable->getNameTitle(), $paginator->getFirstItemOnPage());

		foreach ($this->onAfterRender as $afterRender) {
			$afterRender($this->catalogProductsData->products);
		}
	}

	public function render(): void
	{
		$template = $this->template;
		$template->showMoreBtn = true;
		$this->template->templates = FE_TEMPLATE_DIR;

		foreach ($this->paramsToTemplate as $key => $value) {
			$this->getTemplate()->$key = $value;
		}

		$template->setTranslator($this->translator);
		$template->FE_TEMPLATE_DIR = FE_TEMPLATE_DIR;
		$template->products = $this->catalogProductsData->products;
		$this->template->render(__DIR__ . '/catalogProducts.latte');
	}


	/**
	 * @return Multiplier<ProductBox>
	 */
	public function createComponentProductBox(): Multiplier
	{
		if ( $this->productsById === null) {
			$this->productsById = $this->catalogProductsData->getProductsById();
		}

		return new Multiplier(function ($productId) {
			$item = $this->productsById[$productId]; //?? $this->productRepository->getById($productId);
			assert($item instanceof Product);
			$parametersToTemplate = [
				'class' => false,
				'showAddToMyLibrary' => true,
				'listId' => '',
				'listName' => '',
				'dataAttrs' => ['gtm-event' => 'view_item_list', 'gtm-item' => $this->template->productsGtmItem[$item->id] ?? null],
			];
			return $this->productBoxFactory->create($item, $item->firstVariant, $this->setup, $parametersToTemplate);
		});
	}

	/**
	 * @return Multiplier<ProductBox>
	 */
	public function createComponentProductVariantBox(): Multiplier
	{
		if ( $this->productVariantsById === null) {
			$this->productVariantsById = $this->catalogProductsData->getProductVariantsById();
		}

		return new Multiplier(function ($productId) {
			$item = $this->productVariantsById[$productId]; //?? $this->productRepository->getById($productId);
			assert($item instanceof ProductVariant);
			$parametersToTemplate = [
				'class' => false,
				'showAddToMyLibrary' => true,
				'listId' => '',
				'listName' => '',
				'dataAttrs' => ['gtm-event' => 'view_item_list', 'gtm-item' => $this->template->productVariantsGtmItem[$item->id] ?? null],
			];
			return $this->productBoxFactory->create($item->product, $item, $this->setup, $parametersToTemplate);
		});
	}


	public function createComponentPager(): VisualPaginator
	{
		return $this->visualPaginatorFactory->create();
	}

	public function getTotalCount(): int
	{
		return $this->catalogProductsData->totalCount;
	}

}
