{default $class = false}
{default $pager = true}
{default $title = 'h2'}
{default $products = false}
{default $ajaxPage = false}
{default $cleanFilterParam = []}

<section id="productsList" n:class="c-products, $class"
		{if isset($gtmItemEvent)}
	data-controller="gtm-viewport{*if $products} intersection{/if*}"
	data-gtm-viewport-script-id-value="{$gtmScriptId}"
	data-gtm-viewport-threshold-value="{$gtmThreshold}"
	data-gtm-viewport-item-class-value="b-product"
	data-gtm-viewport-item-event-value="{$gtmItemEvent}"
{/if}
>
	{if $products->count() > 0}
		<div class="c-products__list grid grid--x-0 grid--y-0" n:snippet="productList" data-ajax-append>
			{* TODO BE: bnr přes 1 blok *}
			{* <div class="c-products__item grid__cell">
				{include $templates.'/part/box/bnr.latte', class: false}
			</div> *}

			{* Produkty *}
			{foreach $products as $key=>$product}
					<div class="c-products__item c-products__item--product grid__cell">
						{if $product instanceof App\Model\Orm\Product\Product}
							{control productBox.'-'.$product->id}
						{elseif $product instanceof App\Model\Orm\ProductVariant\ProductVariant}
							{control productVariantBox.'-'.$product->id}
						{/if}
					</div>
			{/foreach}
		</div>

		{if $pager}
			{snippet productsPagerBottom}
				{default $type = 'courses'} {* TODO *}
				{control pager, [filter => $cleanFilterParam, class=>'c-products__pager', pluralLang: $type == 'product' ? 'btn_more_products' : 'btn_more_courses', ajaxPage=>$ajaxPage, najaScroll=>'.c-products__list']}
			{/snippet}
		{/if}
	{elseif $cleanFilterParam !== []}
		{capture $link}{plink 'this', 'filter' => null, 'page' => null}{/capture}
		{php $link = urldecode(htmlspecialchars_decode($link))}
		<p class="message message--warning u-ta-c u-mb-0">
			{_message_empty_filter}
			<a href="{$link}" class="" data-naja data-naja-loader="body">
				{_btn_filter_remove}
			</a>
		</p>
	{else}
		{php $link = $presenter->getReferer()}
		<p class="message message--warning u-ta-c u-mb-0">
			{_message_empty_category}
			<a href="{$link}">
				{_btn_category_back}
			</a>
		</p>
	{/if}
</section>
