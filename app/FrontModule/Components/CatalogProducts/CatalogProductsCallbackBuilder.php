<?php

namespace App\FrontModule\Components\CatalogProducts;

use App\Model\BucketFilter\BucketFilterBuilder;
use Closure;

class CatalogProductsCallbackBuilder
{

	/**
	 * @phpstan-return Closure(int $limit, int $offset): CatalogProductsData
	 */
	public function build(BucketFilterBuilder $dataSource, ?array $bestsellerIds = []): Closure
	{
		//if ($dataSource instanceof BucketFilterBuilder) {
			return function (int $limit, int $offset) use ($bestsellerIds, $dataSource) {
				if ($bestsellerIds !== []) {
					$dataSource->getBucketFilter()->setExcludeIds($bestsellerIds);
				}
				$itemsObject = $dataSource->getItems($limit, $offset);
				return new CatalogProductsData(
					$itemsObject->items,
					$itemsObject->totalCount,
				);
			};
		//}

		//throw new LogicException(sprintf('Missing datasource for %s.', $dataSource::class));
	}

}
