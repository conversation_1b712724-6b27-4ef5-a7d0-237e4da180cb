<?php

namespace App\FrontModule\Components\CatalogProducts;

use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use Nette\Utils\ArrayList;
use Nextras\Orm\Collection\ICollection;

class CatalogProductsData
{

	private ?array $productsById = null;

	private ?array $productVariantsById = null;

	/**
	 * @param ICollection<Product>|ArrayList<Product|ProductVariant> $products
	 */
	public function __construct(
		public readonly ICollection|ArrayList $products,
		public readonly int $totalCount,
	)
	{
	}

	public function getProductsById(): array
	{
		if ($this->productsById === null) {
			$products = [];
			foreach ($this->products as $product) {
				if ($product instanceof Product) {
					$products[$product->id] = $product;
				}
			}

			$this->productsById = $products;
		}
		return $this->productsById;
	}

	public function getProductVariantsById(): array
	{
		if ($this->productsById === null) {
			$products = [];
			foreach ($this->products as $product) {
				if ($product instanceof ProductVariant) {
					$products[$product->id] = $product;
				}
			}

			$this->productVariantsById = $products;
		}
		return $this->productVariantsById;
	}

}
