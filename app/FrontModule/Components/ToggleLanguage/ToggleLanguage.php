<?php declare(strict_types = 1);

namespace App\FrontModule\Components\ToggleLanguage;

use App\Model\Mutation\BrowserMutationDetector;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Mutation\MutationRepository;
use App\Model\TranslatorDB;
use App\PostType\Core\Model\LocalizationEntity;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;

/**
 * @property-read DefaultTemplate $template
 */
final class ToggleLanguage extends UI\Control
{

	public function __construct(
		private readonly MutationRepository $mutationRepository,
		private readonly LocalizationEntity $localizationEntity,
		private readonly MutationsHolder $mutationsHolder,
		private readonly TranslatorDB $translator,
		private readonly ToggleLanguageModel $toggleLanguageModel,
		private readonly BrowserMutationDetector $browserMutationDetector,
	)
	{
	}


	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->add('selectedMutation', $this->localizationEntity->getMutation());
		$this->template->add('mutations', $this->mutationsHolder->findAll());

		$this->template->render(__DIR__ . '/toggleLanguage.latte');
	}


	public function handleChangeLanguage(int $id): void
	{
		$newMutation = $this->mutationRepository->getBy([
			'id' => $id,
			'public' => true,
		]);
		if ($newMutation !== null) {
			$sibling = $this->toggleLanguageModel->getSibling($this->localizationEntity, $newMutation);

			// force new language for browser detector
			$this->browserMutationDetector->setCookie($newMutation);

			$this->presenter->redirect($sibling, ['mutation' => $newMutation]);
		} else {
			$this->presenter->redirect($this->localizationEntity);
		}
	}

}
