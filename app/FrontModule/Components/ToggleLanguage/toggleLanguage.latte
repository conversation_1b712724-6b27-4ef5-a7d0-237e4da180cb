<div class="toggle-language" data-controller="toggle-class">
	<button class="toggle-language__btn" data-action="toggle-class#toggle" aria-expanded="false">
		<span class="toggle-language__inner">
			<span class="toggle-language__img">
				{if isset($selectedMutation->cf->mutationData->icon) && $selectedMutation->cf->mutationData->icon}
					<img src="/static/img/flags/{$selectedMutation->cf->mutationData->icon}.svg" alt="{$selectedMutation->langMenu}" width="16" height="10">
				{/if}

			</span>
			{$selectedMutation->name} [{$selectedMutation->langCode}]
			<span class="toggle-language__icon" >
				{('angle-down')|icon}
			</span>
		</span>
	</button>
	<ul class="toggle-language__list">
        {foreach $mutations as $k => $mutation}
			<li class="toggle-language__item{if $selectedMutation->id === $mutation->id} is-active{/if}">
				<a n:href="changeLanguage!, id => $mutation->id" class="toggle-language__link">
					{if isset($mutation->cf->mutationData->icon) && $mutation->cf->mutationData->icon}
						<span class="toggle-language__img">
							<img src="/static/img/flags/{$mutation->cf->mutationData->icon}.svg" alt="{$mutation->langMenu}" width="16" height="10">
						</span>
					{/if}
					{$mutation->name} [{$mutation->langCode}]
				</a>
			</li>
		{/foreach}
	</ul>
</div>
