<?php declare(strict_types = 1);

namespace App\FrontModule\Components\ToggleLanguage;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\Mutation\Mutation;
use App\PostType\Core\Model\LocalizationEntity;

class ToggleLanguageModel
{

	public function getSibling(LocalizationEntity $localizationEntity, Mutation $newMutation): LocalizationEntity
	{
		$sibling = $localizationEntity->getParent()->getLocalizations()->getBy(['mutation' => $newMutation]);

		if (
			$sibling === null
			|| $sibling instanceof BaseEntity && ! $sibling->isPublished()
		) {
			$sibling = $newMutation->rootPage;
		}


		assert($sibling instanceof LocalizationEntity);

		return $sibling;
	}

}
