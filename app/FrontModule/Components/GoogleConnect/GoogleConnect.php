<?php

declare(strict_types = 1);

namespace App\FrontModule\Components\GoogleConnect;

use App\FrontModule\Components\GoogleLogin\GoogleLogin;
use App\FrontModule\Components\GoogleLogin\GoogleLoginFactory;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\User\UserModel;
use App\Model\Orm\User\UserRepository;
use App\Model\TranslatorDB;
use Nette\Application\UI\Control;

final class GoogleConnect extends Control
{

	public function __construct(
		private readonly bool $isEnabled,
		private readonly Mutation $mutation,
		private readonly GoogleLoginFactory $googleLoginFactory,
		private readonly TranslatorDB $translator,
		private readonly UserRepository $userRepository,
		private readonly UserModel $userModel,
	)
	{
	}

	public function render(): void
	{
		if (!$this->isEnabled) {
			return;
		}

		$userId = $this->presenter->user->identity->getId();
		$userEntity = $this->userRepository->getByIdChecked($userId);

		$this->template->setTranslator($this->translator);
		$this->template->render(__DIR__ . '/GoogleConnect.latte', ['userEntity' => $userEntity]);
	}

	public function handleDisconnect(): void
	{
		$userId = $this->presenter->user->identity->getId();
		$userEntity = $this->userRepository->getByIdChecked($userId);

		$this->userModel->save($userEntity, ['googleId' => null]);

		$this->redirect('this');
	}

	protected function createComponentGoogleLogin(): GoogleLogin
	{
		return $this->googleLoginFactory->create($this->mutation);
	}

}
