<?php declare(strict_types = 1);

namespace App\FrontModule\Components\MyWatchdog;

use App\Model\Orm\Orm;
use App\Model\Orm\User\UserProvider;
use App\Model\Orm\Watchdog\Watchdog;
use App\Model\Setup;
use App\Model\TranslatorDB;
use Nette\Application\BadRequestException;
use Nette\Application\UI\Control;
use Nextras\Orm\Collection\ICollection;

final class MyWatchdog extends Control
{

	/** @var ICollection<Watchdog> */
	private ICollection $watchdogs;

	public function __construct(
		private readonly Setup $setup,
		//private readonly int $page,
		private readonly TranslatorDB $translator,
		private readonly Orm $orm,
		private readonly UserProvider $userProvider,
	)
	{
		$this->onAnchor[] = $this->init(...);
	}

	public function init(): void
	{
		if (!$this->userProvider->userSecurity->isLoggedIn()) {
			throw new BadRequestException();
		}
		$this->watchdogs = $this->orm->watchdog->findBy(['email' => $this->userProvider->userEntity?->email]);
	}

	final public function render(): void
	{
		$this->getTemplate()->templates = FE_TEMPLATE_DIR;
		$this->getTemplate()->mutation = $this->setup->mutation;
		$this->getTemplate()->priceLevel = $this->setup->priceLevel;
		$this->getTemplate()->state = $this->setup->state;
		$this->getTemplate()->watchdogs = $this->watchdogs;

		$this->getTemplate()->setTranslator($this->translator);
		$this->getTemplate()->setFile(__DIR__ . '/myWatchdog.latte');
		$this->getTemplate()->render();
	}

}
