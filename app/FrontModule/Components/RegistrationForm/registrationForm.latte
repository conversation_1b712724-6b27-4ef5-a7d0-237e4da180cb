{snippet form}
	{default $class = false}
	{form form class=>'f-login', data-naja=>'', novalidate=>'novalidate', data-naja-history=>"off"}
		{control messageForForm, $flashes, $form}

		{include '../inp.latte', form=>$form, name=>email}
		{include '../inp.latte', form=>$form, name=>password, password=>true}
		{include '../inp.latte', form=>$form, name=>isNewsletter}
		{include '../inp.latte', form=>$form, name=>agree, agreeLabel=>true}
		<p>
			<button type="submit" class="btn btn--block">
				<span class="btn__text">
					{_btn_register}
				</span>
			</button>
		</p>

		{* <p class="text-divider">
			<span class="text-divider__text">{_"header_login_or_socials"}</span>
		</p> *}

		{* <p class="f-login__social">
			{control googleLogin}
		</p>
		<p class="f-login__social u-mb-0">
			{control facebookLogin}
		</p> *}

		<p class="u-ta-c u-mb-0"> {* u-pt-sm *}
			{_'registration_have_account'}
			 <a href="{plink $pages->userLogin}" data-naja="" data-naja-history="off" data-modal='{"medium": "fetch", "modalClass": "b-modal--xs"}' data-snippetid="snippet--userFormArea">{_'btn_login'}</a>
		</p>

	{/form}
{/snippet}
