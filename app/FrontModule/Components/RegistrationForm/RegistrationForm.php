<?php declare(strict_types = 1);

/** @noinspection PhpRedundantCatchClauseInspection */

/** @noinspection PhpUnusedParameterInspection */

namespace App\FrontModule\Components\RegistrationForm;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\Exceptions\UserException;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Mutation\Mutation;
use App\FrontModule\Components\FacebookLogin\FacebookLogin;
use App\FrontModule\Components\FacebookLogin\FacebookLoginFactory;
use App\FrontModule\Components\GoogleLogin\GoogleLogin;
use App\FrontModule\Components\GoogleLogin\GoogleLoginFactory;
use App\Model\Orm\NewsletterEmail\NewsletterEmailModel;
use App\Model\Orm\Orm;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\PostType\Page\Model\Orm\Tree;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserModel;
use App\Model\Security\Acl;
use App\Model\TranslatorDB;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Security\AuthenticationException;
use Nette\Utils\ArrayHash;
use Nette\DI\Attributes\Inject;
use App\Model\Orm\UserHash\UserHash;
use App\Model\Orm\UserHash\UserHashModel;
use Throwable;
use Tracy\Debugger;
use App\Model\Email\CommonFactory;
use App\Model\Form\FormThrottler;

/**
 * @property-read DefaultTemplate $template
 */
final class RegistrationForm extends UI\Control
{

	private Mutation $mutation;

	#[Inject]
	public GoogleLoginFactory $googleLoginFactory;

	#[Inject]
	public FacebookLoginFactory $facebookLoginFactory;

	public function __construct(
		private readonly Tree $object,
		private readonly UserModel $userModel,
		private readonly Orm $orm,
		private readonly TranslatorDB $translator,
		MutationHolder $mutationHolder,
		private readonly MessageForFormFactory $messageForFormFactory,
		private readonly UserHashModel $userHashModel,
		private readonly CommonFactory $commonEmailFactory,
		private readonly FormThrottler $formThrottler,
		private readonly NewsletterEmailModel $newsletterEmailModel,
	)
	{
		$this->mutation = $mutationHolder->getMutation();
	}

	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->object = $this->object;
		$this->template->pages = $this->mutation->pages;
		$this->template->render(__DIR__ . '/registrationForm.latte');
	}


	protected function createComponentForm(): UI\Form
	{
		$form = new UI\Form();
		$form->setTranslator($this->translator);

		$form->addEmail('email', 'form_label_email')
			->setRequired('E-mail is required');
		$form->addPassword('password', 'form_label_password')
			->setRequired();
		$form->addCheckbox('agree')->setRequired();
		$form->addCheckbox('isNewsletter', $this->translator->translate('form_label_newsletter'))->setTranslator(null)
			->setDefaultValue(1);

		$form->addSubmit('save', 'btnRegister');

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onValidate[] = [$this, 'editFormValidate'];
		$form->onError[] = [$this, 'formError'];

		$this->formThrottler->throttleFormSubmissions($form, formType: 'Front:Registration', discriminatorName: 'email');

		return $form;
	}

	public function formError(UI\Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	public function editFormValidate(UI\Form $form, ArrayHash $values): void
	{
		$user = $this->userModel->getByEmail($values->email, $this->mutation);

		if ($user) {
			$link1 = $this->presenter->link($this->mutation->pages->userLogin, ['email' => $values->email]);
			$link2 = $this->presenter->link($this->mutation->pages->lostPassword, ['email' => $values->email]);
			$strTranslated = $this->translator->translate('mail_exist_register');

			if (strpos($strTranslated, '%link1%') !== false) {
				$strTranslated = str_replace('%link1%', $link1, $strTranslated);
			}

			if (strpos($strTranslated, '%link2%') !== false) {
				$strTranslated = str_replace('%link2%', $link2, $strTranslated);
			}

			$form->addError($strTranslated, false);
		}
	}


	public function formSucceeded(UI\Form $form, ArrayHash $values): void
	{
		$valuesAll = $form->getHttpData();
		assert(is_array($valuesAll));

		$redirect = 'this';

		try {
			$user = new User();
			$this->orm->user->attach($user);
			$user->role = Acl::ROLE_USER;
			$user->priceLevel = PriceLevel::DEFAULT_ID;
			$user->mutations->add($this->mutation);

			$this->userModel->save($user, $valuesAll);

			try {
				if (!empty($valuesAll['isNewsletter'])) {
					$this->newsletterEmailModel->subscribeUser($user, $this->mutation);
				}
			} catch (UserException $e) {
				// possibly already subscribed
			}

			$userHash = $this->userHashModel->generateHashForUser($user, UserHash::HASH_TYPE_REGISTRATION, [$valuesAll['email']]);
			$valuesAll['link'] = $this->mutation->getBaseUrlWithPrefix() . $this->presenter->link($this->mutation->pages->registrationOk, ['hashToken' => $userHash->hash]);

			unset($valuesAll['password']);

			$this->commonEmailFactory
				->create()
				->send('', $valuesAll['email'], 'newRegistration', (array) $valuesAll);

			// log all attempts - even the successful ones
			$this->formThrottler->logSubmissionAttempt($form, formType: 'Front:Registration', discriminatorName: 'email');

			$this->flashMessage('form_register_ok', 'ok');

		} catch (Throwable $e) {
			Debugger::log($e);
			$this->flashMessage('Error', 'error');
		}

		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		} else {
			$this->presenter->redirect($redirect);
		}
	}

	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

	protected function createComponentGoogleLogin(): GoogleLogin
	{
		return $this->googleLoginFactory->create($this->mutation);
	}

	protected function createComponentFacebookLogin(): FacebookLogin
	{
		return $this->facebookLoginFactory->create($this->mutation);
	}

}
