<?php declare(strict_types=1);

namespace App\FrontModule\Components;

use Nette\Application\UI;
use Nette\Utils\ArrayHash;

trait HasAntispamInput
{
	const BASE_NUMBER = 631;

	protected function attachAntispamTo(UI\Form $form): void
	{
		$baseNumber = self::BASE_NUMBER;
		$random = random_int(1,20);
		$noJsValue = $baseNumber * $random;
		$form->onValidate[] = $this->validateAntispamInForm(...); // @phpstan-ignore-line
		$form->onSuccess[] = $this->persistAntispam(...);


		$noJsValue = $this->translator->translate('please_rewrite_value') . ' <strong data-antispam-target="source">' . $noJsValue . '</strong>';
		$form->addText('antispamNoJs', $noJsValue)->setTranslator(null)->setHtmlAttribute('data-value', $noJsValue)->setRequired();
	}


	private function persistAntispam(UI\Form $form, ArrayHash $values): void
	{
		$form->reset();
		$this->redrawControl();
	}

	private function validateAntispamInForm(UI\Form $form, ArrayHash $values): bool
	{
		if ($this->antispamValidate($values)) { // validační podmínka
			return true;
		} else {
			$form->addError('message_antispam_error');
			$form['antispamNoJs']->addError('message_antispam_error_no_js');
			return false;
		}
	}


	private function antispamValidate(ArrayHash $values): bool
	{
		return $values->antispamNoJs % self::BASE_NUMBER === 0;
	}

//{*ANTISPAM*}
//<p class="u-js-hidex js-antispam{if $form['antispamNoJs']->hasErrors()} has-error{/if}">
//	<label n:name="antispamNoJs" class="js-antispamText">
//		{_$form['antispamNoJs']->caption|noescape} {if $form['antispamNoJs']->isRequired()}*{/if}
//	</label>
//	<span class="inp-fix">
//		<input n:name="antispamNoJs" class="inp-text js-antispamValue">
//	</span>
//</p>
//{*/ANTISPAM*}


}
