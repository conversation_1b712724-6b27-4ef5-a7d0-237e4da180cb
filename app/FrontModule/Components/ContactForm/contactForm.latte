{default $snippetSuffix = ""}
{snippet form}

	{php $control['form']->action .= "#frm-contactForm-form"}

	{form form class: 'f-contact block-loader u-bgc-sand-light-3 u-round u-mb-xxs u-mb-xl@md', data-naja: '', novalidate: "novalidate"}

		<div class="u-maw-7-12 u-mx-auto">
			<h2 class="f-contact__title h4 u-mb-xxs u-mb-md@md">
				{_"contact_form_title"}
			</h2>

			{control messageForForm, $flashes, $form}

			<div class="grid">
				<div class="grid__cell size--6-12@md u-mb-last-0">
					{include '../inp.latte', class: 'f-contact__item', form: $form, name: name, labelLang: 'form_label_your_name'}
					{include '../inp.latte', class: 'f-contact__item', form: $form, name: email, labelLang: 'form_label_your_email'}
					{include '../inp.latte', class: 'f-contact__item', form: $form, name: phoneNumber, labelLang: 'form_label_your_phone_number'}
				</div>
				<div class="grid__cell size--6-12@md u-mb-last-0">
					{include '../inp.latte', class: 'f-contact__item f-contact__item--textarea', form: $form, name: text, labelLang: 'form_label_question', cols: 40, rows: 5}
				</div>
			</div>

			<div class="f-contact__bottom">
				<p n:if="isset($pages->personalData)" class="f-contact__agree">
					{capture $link}{plink $pages->personalData}{/capture}
					{_"form_agree"|replace:'%link', $link->__toString()|noescape}
				</p>
				<p class="f-contact__btn">
					<button type="submit" class="btn">
						<span class="btn__text">
							{_btn_send_question}
						</span>
					</button>
				</p>
			</div>
		</div>
		{*ANTISPAM*}
		{if isset($form['antispamNoJs'])}
			<p n:class="$form['antispamNoJs']->hasErrors() ? 'has-error' : 'u-js-hide'" data-controller="antispam">
				<label n:name="antispamNoJs">
					{$form['antispamNoJs']->caption|noescape} {if $form['antispamNoJs']->isRequired()}*{/if}
				</label>
				<span class="inp-fix">
					<input n:name="antispamNoJs" class="inp-text" data-antispam-target="input">
				</span>
			</p>
		{/if}
		{*/ANTISPAM*}

		<div class="block-loader__loader"></div>

	{/form}

{/snippet}
