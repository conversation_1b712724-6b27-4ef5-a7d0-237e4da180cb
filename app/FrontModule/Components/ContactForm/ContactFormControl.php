<?php

declare(strict_types=1);

namespace App\FrontModule\Components\ContactForm;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\Model\Orm\Routable;
use Nette\Application\UI;
use Nette\Utils\ArrayHash;
use App\FrontModule\Components\HasAntispamInput;
use App\FrontModule\Components\HasError500Catcher;
use App\Model\Mutation\MutationHolder;
use App\Model\TranslatorDB;
use function assert;


final class ContactFormControl extends UI\Control
{
	use HasAntispamInput;
	use HasError500Catcher;

	private readonly string $title;

	public function __construct(
		private readonly Routable $object,
		private readonly string $type,
		private readonly ContactFormFactory $contactFormFactory,
		private readonly TranslatorDB $translator,
		private readonly MutationHolder $mutationHolder,
		private readonly MessageForFormFactory $messageForFormFactory,
	)
	{
		switch ($this->type) {
			case 'contact':
			default:
				$this->title = $this->translator->translate('contact_form_title');
				break;
		}
	}

	protected function createComponentForm(): UI\Form
	{
		$form = $this->contactFormFactory->create($this->object);
		assert($form instanceof UI\Form);

		$this->attachAntispamTo($form);

		$form->onSuccess[] = $this->formSucceeded(...);
		$form->onError[] = $this->formError(...);

		return $form;
	}

	private function formSucceeded(UI\Form $form, ArrayHash $values): void
	{
		$this->flashMessage('contact_ok', 'ok');

		if ($this->presenter->isAjax()) {
			$form->setValues([], true);
			$this->redrawControl();
		} else {
			$this->presenter->redirect('this');
		}
	}

	private function formError(UI\Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function render(): void
	{
		try {
			$this->template->title = $this->title;
			$this->template->setTranslator($this->translator);
			$this->template->object = $this->object;
			$this->template->pages = $this->mutationHolder->getMutation()->pages;
			$this->template->render(__DIR__ . '/contactForm.latte');

		} catch (\Throwable $e) {
			/** @noinspection PhpUnhandledExceptionInspection */
			$this->handleRenderError500($e);
		}
	}


	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

}
