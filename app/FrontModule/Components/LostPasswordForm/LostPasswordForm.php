<?php

declare(strict_types=1);

namespace App\FrontModule\Components\LostPasswordForm;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\Model\AdminEmails;
use App\Model\Email\CommonFactory;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\User\UserModel;
use App\Model\Orm\UserHash\UserHash;
use App\Model\Orm\UserHash\UserHashModel;
use App\Model\TranslatorDB;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nextras\Orm\Entity\Entity;
use App\FrontModule\Components\SignInForm\SignInForm;
use App\Model\Form\FormThrottler;

/**
 * @property-read DefaultTemplate $template
 */
final class LostPasswordForm extends UI\Control
{
	public function __construct(
		private readonly Mutation $mutation,
		private Entity $object,
		private ?string $hash,
		private TranslatorDB $translator,
		private Orm $orm,
		private CommonFactory $commonEmailFactory,
		private UserModel $userModel,
		private UserHashModel $userHashModel,
		private MessageForFormFactory $messageForFormFactory,
		private AdminEmails $adminEmails,
		private readonly FormThrottler $formThrottler,
	)
	{
	}


	public function render(): void
	{
		$this->template->object = $this->object;
		$this->template->pages = $this->mutation->pages;
		$this->template->setTranslator($this->translator);
		$this->template->render(__DIR__ . '/lostPasswordForm.latte');
	}


	public function renderReset(): void
	{
		$this->template->object = $this->object;
		$this->template->pages = $this->mutation->pages;
		$this->template->setTranslator($this->translator);
		$this->template->render(__DIR__ . '/resetPasswordForm.latte');
	}


	protected function createComponentForm(): UI\Form
	{
		$form = new UI\Form;
		$form->setTranslator($this->translator);
		$form->addEmail('username', 'email')
			->setRequired('form_enter_username');

		if (isset($_GET['email'])) {
			$form->setDefaults(['username' => $_GET['email']]);
		} elseif ($this->presenter->getHttpRequest()->getCookie(SignInForm::COOKIE_NAME_LOGIN_EMAIL_AUTH_ERROR)) {
			$form->setDefaults(['username' => $this->presenter->getHttpRequest()->getCookie(SignInForm::COOKIE_NAME_LOGIN_EMAIL_AUTH_ERROR)]);
		}

		$form->addSubmit('send', 'Send');

		// call method signInFormSucceeded() on success
		$form->onSuccess[] = [$this, 'formSuccess'];
		$form->onError[] = [$this, 'formError'];

		$this->formThrottler->throttleFormSubmissions($form, formType: 'Front:LostPassword', discriminatorName: 'username');

		return $form;
	}


	public function formError(UI\Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	public function formSuccess(UI\Form $form): void
	{
		$valuesAll = $form->getHttpData();
		assert(is_array($valuesAll));
		if ($this->adminEmails->isDeveloperEmail($valuesAll['username'])) {
			$this->flashMessage('form_send_reset_password', 'ok');
			if ($this->presenter->isAjax()) {
				$this->redrawControl();
			} else {
				$this->redirect('this');
			}

			return;
		}

		$userEntity = $this->orm->user->getByEmail($valuesAll['username'], $this->mutation);

		if ($userEntity && $userEntity->id) {
			$existingRecentHash = $this->orm->userHash->getBy([
				'user' => $userEntity,
				'type' => UserHash::TYPE_LOST_PASSWORD,
				'createdTime>=' => (new \DateTimeImmutable())->modify('-2 minutes'),
			]);

			if ($existingRecentHash === null) {
				$valuesAll['email'] = $userEntity->email;
				$userHash = $this->userHashModel->generateHashForUser($userEntity, UserHash::TYPE_LOST_PASSWORD, [$valuesAll['email']], 1);
				$valuesAll['link'] = $this->mutation->getBaseUrlWithPrefix() . $this->presenter->link($this->mutation->pages->resetPassword, ['hashToken' => $userHash->hash]);

				$this->commonEmailFactory
					->create()
					->send('', $valuesAll['email'], 'lostPassword', (array) $valuesAll);
			}
		}

		// log all attempts - even the successful ones (i.e. email exists in users table)
		$this->formThrottler->logSubmissionAttempt($form, formType: 'Front:LostPassword', discriminatorName: 'username');

		$this->flashMessage('form_send_reset_password', 'ok');

		$form['username']->setValue('');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		} else {
			$this->redirect('this');
		}
	}

	protected function createComponentFormReset(): UI\Form
	{
		$form = new UI\Form;
		bd($this->hash);
		$form->setTranslator($this->translator);
		$form->addHidden('hash', $this->hash);
		$form->addPassword('password', 'form_label_password')->setRequired();
		$form->addPassword('passwordVerify', 'form_label_password2')
			->setRequired()
			->addRule(UI\Form::Equal, 'form_password_not_same', $form['password']);

		$form->addSubmit('send', 'Send');

		// call method signInFormSucceeded() on success
		$form->onSuccess[] = [$this, 'formResetSuccess'];
		$form->onError[] = [$this, 'formResetError'];
		return $form;
	}


	public function formResetError(UI\Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	public function formResetSuccess(UI\Form $form): void
	{
		$valuesAll = $form->getHttpData();
		assert(is_array($valuesAll));
		$hashObject = $this->orm->userHash->getBy(['hash' => $valuesAll['hash']]);

		$userEntity = $hashObject->user;


		if (!$userEntity->id) {
			$form['password']->addError("user_not_found");
			$this->flashMessage($this->translator->translate("user_not_found"), 'error');
		}

		$this->userModel->save($userEntity, $valuesAll, $userEntity->id);
		$this->userModel->deleteHash($userEntity, $hashObject);
		$this->flashMessage('form_reset_password', 'ok');
		$this->presenter->flashMessage('form_reset_password', 'ok');

		$form['password']->setValue('');
		$form['passwordVerify']->setValue('');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		} else {
			$this->presenter->redirect($this->mutation->pages->userLogin);
		}
	}


	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

}
