{default $class = 'u-mb-lg'}

<div class="c-animals {$class}">
	<div class="b-scroll">
		<div class="b-scroll__inner">
			<div class="c-animals__list">
				<div class="c-animals__item">
					<table class="c-animals__table">
						<thead>
						<tr>
							<td class="u-text-center">

							</td>
							<td class="u-text-center">
								{_'animal_name'}
							</td>
							<td class="u-text-center">
								{_'animal_type'}
							</td>

						</tr>
						</thead>
						<tbody>
						{foreach $animals as $animal}
							{varType App\PostType\UserAnimal\Model\Orm\UserAnimal $animal}
							<tr>
								<td class="u-text-center">
								{if $animal->libraryImage}
										{php $img = $animal->libraryImage->getSize('sm')}
										<img loading="lazy" src="{$img->src}" alt="" width="100" />
								{/if}
								</td>
								<td class="u-text-center">
									<a href="{plink $mutation->pages->userAnimalEdit id => $animal->id}">
										{$animal->name}
									</a>
								</td>
								<td class="c-animals__status-cell">
									{$animal->userAnimalType->getLocalization($mutation)->name}
								</td>

							</tr>
						{/foreach}
						</tbody>
					</table>
					<p class="u-ta-c">
						<a class="c-button" href="{plink $mutation->pages->userAnimalEdit}">
							{_'animal_add'}
						</a>
					</p>
				</div>
			</div>

		</div>
	</div>
</div>
