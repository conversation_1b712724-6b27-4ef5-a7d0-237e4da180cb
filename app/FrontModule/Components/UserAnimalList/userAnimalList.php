<?php declare(strict_types = 1);

namespace App\FrontModule\Components\UserAnimalList;

use App\Components\VisualPaginator\VisualPaginator;
use App\Components\VisualPaginator\VisualPaginatorFactory;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\RoutableEntity;
use App\Model\Orm\User\User;
use App\Model\TranslatorDB;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;

/**
 * @property-read DefaultTemplate $template
 */
final class userAnimalList extends UI\Control
{
	private const ItemsPerPage = 10;

	public function __construct(
		private readonly RoutableEntity $object,
		private readonly User $user,
		private readonly Mutation $mutation,
		private readonly Orm $orm,
		private readonly TranslatorDB $translator,
		private readonly VisualPaginatorFactory $visualPaginatorFactory,
	)
	{
	}

	public function beforeRender(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->mutation = $this->mutation;
		$this->template->pages = $this->mutation->pages;
		$this->template->object = $this->object;
		$this->template->translator = $this->translator;
		$this->template->templates = FE_TEMPLATE_DIR;

	}

	public function render(): void
	{
		$this->beforeRender();

		$animals = $this->orm->userAnimal->findBy(['user' => $this->user])->orderBy('name');

		$paginator = $this['pager']->getPaginator();
		$paginator->setItemsPerPage(self::ItemsPerPage);
		$paginator->setItemCount($animals->countStored());

		$this->template->ordersCount = $paginator->getItemCount();
		$this->template->animals = $animals->limitBy($paginator->getItemsPerPage(), $paginator->getOffset());
		$this->template->showPager = true;

		$this->template->render(__DIR__ . "/userAnimalList.latte");
	}

	protected function createComponentPager(): VisualPaginator
	{
		$visualPaginator = $this->visualPaginatorFactory->create();
		$visualPaginator->setTranslator($this->translator);
		return $visualPaginator;
	}


}
