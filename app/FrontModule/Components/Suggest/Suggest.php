<?php declare(strict_types = 1);

namespace App\FrontModule\Components\Suggest;

use App\Event\SearchClick;
use App\FrontModule\Components\Suggest\Components\SuggestProducts\SuggestProducts;
use App\FrontModule\Components\Suggest\Components\SuggestProducts\SuggestProductsFactory;
use App\Model\BucketFilter\Result;
use App\Model\FulltextSearch\FulltextSearchBucketFilterProvider;
use App\Model\FulltextSearch\SearchFactory;
use App\Model\FulltextSearch\SuggestProductProviderFactory;
use App\Model\FulltextSearch\UserHistory;
use App\Model\Orm\Product\Product;
use App\Model\Setup;
use App\Model\TranslatorDB;
// use App\PostType\Writer\Model\Orm\WriterLocalization\WriterLocalizationRepository;
use Nette\Application\Responses\TextResponse;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nextras\Orm\Collection\EmptyCollection;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;

/**
 * @property-read DefaultTemplate $template
 */
final class Suggest extends UI\Control
{

	private bool $userHasVisitedProducts;

	private Result $productResultObject;

	public function __construct(
		private readonly Setup $setup,
		private readonly TranslatorDB $translator,
		private readonly SuggestProductProviderFactory $suggestProductProviderFactory,
		private readonly UserHistory $searchUserHistory,
		private readonly SearchFactory $fulltextSearchFactory,
		// private readonly WriterLocalizationRepository $writerLocalizationRepository,
		private readonly EventDispatcherInterface $eventDispatcher,
		private readonly SuggestProductsFactory $suggestProductsFactory,
		private readonly FulltextSearchBucketFilterProvider $fulltextSearchBucketFilterProvider,
	)
	{
	}

	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		// $this->userHasVisitedProducts = $this->suggestProductProviderFactory->create($this->setup->mutation)->countByUser() > 0;
//		$this->template->userHasVisitedProducts = $this->userHasVisitedProducts;

		$exampleTexts = (isset($this->setup->mutation->cf->suggestItems) && is_array($this->setup->mutation->cf->suggestItems)) ? array_map(fn(\stdClass $item) => $item->text, $this->setup->mutation->cf->suggestItems) : [];

		$this->template->exampleTexts = $exampleTexts;
		$this->template->userHasSearchedTexts = false;
		$this->template->products = new EmptyCollection();

		$this->initTemplate();

		$this->template->render(__DIR__ . '/suggest.latte');
	}

	public function handleOpen(): void
	{
		$this->userHasVisitedProducts = true;
		$userHasSearchedTexts = true;
		// $findPopularAuthors = function () {
		// 	return [];
		// };
		$this->productResultObject = $this->suggestProductProviderFactory->create($this->setup->mutation)->getByUser();
		if ($this->productResultObject->totalCount === 0) {
			$this->productResultObject = $this->suggestProductProviderFactory->create($this->setup->mutation)->getTopProducts();
			// $this->userHasVisitedProducts = false;
		}

		$searchHistory = $this->searchUserHistory->findSearchedTextForUser();
		if ($searchHistory === []) {
			$userHasSearchedTexts = false;
			$searchHistory = $this->searchUserHistory->findSearchedText($this->setup->mutation);
			// $findPopularAuthors = function () {
			// 	return $this->writerLocalizationRepository->findBy([])->limitBy(20)->orderBy('score')->fetchAll();
			// };
		}

		$searchHistoryItems = [];
		if ($this->setup->mutation->pages->search !== null) {
			foreach ($searchHistory as $name) {
				$searchHistoryItems[] = ['page' => $this->setup->mutation->pages->search, 'parameters' => ['search' => $name], 'name' => $name];
			}
		}

		$this['suggestProducts']->setProducts($this->productResultObject->items);

		$this->template->searchHistoryItems = $searchHistoryItems === [] ? null : $searchHistoryItems;
		$this->template->products = $this->productResultObject->items;
		$this->template->userHasSearchedTexts = $userHasSearchedTexts;
		// $this->template->userHasVisitedProducts = $this->userHasVisitedProducts;
		// $this->template->findPopularAuthors = $findPopularAuthors();

		$this->initTemplate();

		$this->eventDispatcher->dispatch(
			new SearchClick()
		);

		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}
	public function handleSearch(): void
	{
		$textToSearch = $this->presenter->request->getPost('search');
		$fulltextResults = $this->fulltextSearchFactory->create($this->setup->mutation, $textToSearch, 5)->search();
		$this->productResultObject = Result::empty();

		if ($textToSearch !== '') {
			$bucketFilterBuilder = $this->fulltextSearchBucketFilterProvider->get($textToSearch, $this->getPresenter(), $this->setup->mutation, $this->setup->state, $this->setup->priceLevel, []);

			$this->productResultObject = $bucketFilterBuilder->getItems(10);
		}

		$hasSomeResults = $this->productResultObject->count > 0
			|| count(
				array_filter($fulltextResults, fn(\App\Model\FulltextSearch\Result $result) => $result->hasItems())
			) > 0;

		$this->template->setTranslator($this->translator);
		$this->template->hasResults = $hasSomeResults;
		$this->template->productResultObject = $this->productResultObject;
		$this->template->search = $textToSearch;
		$this->template->fulltextResults = $fulltextResults;

		$this->initTemplate();

		$test = $this->template->renderToString(__DIR__ . '/results.latte');

		$this->presenter->sendResponse(
			new TextResponse($test)
		);
	}

	/**
	 * @return void
	 */
	public function initTemplate(): void
	{
		$this->template->mutation = $this->setup->mutation;
		$this->template->translator = $this->translator;
		$this->template->templates = FE_TEMPLATE_DIR;
		$this->template->state = $this->setup->state;
		$this->template->priceLevel = $this->setup->priceLevel;
	}


	protected function createComponentResultSuggestProducts(): SuggestProducts
	{
		$title = $this->translator->translate('suggest_title_products') . ' (' . $this->productResultObject->totalCount . ')';
		$products = $this->productResultObject->items;

		return $this->suggestProductsFactory->create($title, $this->setup, $products);
	}

	protected function createComponentSuggestProducts(): SuggestProducts
	{
		if ($this->userHasVisitedProducts) {
			$title = $this->translator->translate('suggest_title_visited');
		} else {
			$title = $this->translator->translate('suggest_title_popular');
		}
		/** @var EmptyCollection<Product> $products */
		$products = new EmptyCollection();//$this->productResultObject->items;
		return $this->suggestProductsFactory->create($title, $this->setup, $products);
	}

}
