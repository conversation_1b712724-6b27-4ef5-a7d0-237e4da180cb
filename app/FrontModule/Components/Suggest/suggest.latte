<form class="header__search f-search f-search--header overlay-pseudo" n:if="$mutation->pages->search !== null"
	  action='{plink $mutation->pages->search, search=>null, filter=>null}' id="form-search"
	  data-controller="suggest etarget toggle-class"
	  data-suggest='{link search, search=>null, filter=>null}'
>
	<button type="button" class="f-search__toggle as-link" data-action="toggle-class#toggle">
		{('search')|icon, 'f-search__icon'}
		<span class="u-vhide">{_"btn_search"}</span>
	</button>

	<div class="f-search__holder">
		{* Vyhledávací pole *}
		<p class="f-search__wrap u-mb-0">
			<span class="f-search__inp-fix inp-fix">
				<label for="search" class="u-vhide" data-toggle-class-target="inp">{_"form_label_search"}</label>
				<input type="search" id="search" name="search" class="f-search__inp inp-text"
					placeholder="{_'header_search_placeholder'}"
					autocomplete="off"
					data-suggest-target="input">
			</span>
			<button type="submit" class="f-search__btn btn btn--icon">
				<span class="btn__text">
					{('search')|icon, 'btn__icon'}
					<span class="u-vhide">{_"btn_search"}</span>
				</span>
			</button>
			<button type="button" class="f-search__close btn btn--icon" data-action="toggle-class#toggle">
				<span class="btn__text">
					{('close')|icon, 'f-search__icon'}
					<span class="u-vhide">{_"btn_close"}</span>
				</span>
			</button>
			<span class="f-search__arrow"></span>
		</p>

		{* Focus na vyhledávací pole *}
		<div class="f-search__before b-suggest" n:snippet="suggest">
			<div class="b-suggest__box" data-controller="gtm-viewport" data-gtm-viewport-script-id-value="suggest">
				<div class="b-suggest__main u-mb-last-0">
					<p class="tw-flex tw-items-center tw-gap-[0.8rem] tw-text-[1.5rem] tw-font-bold tw-py-[1.2rem] tw-mb-0 after:tw-content-[''] after:tw-block after:tw-h-[0.1rem] after:tw-flex-1 after:tw-bg-tile-light after:tw-top-[0.1rem] after:tw-relative">
						TODO BE: {_"suggest_title_searched"} (10)
					</p>
					<ul class="u-reset-ul tw-text-[1.4rem] tw-mb-[0.8rem]">
						<li><a href="#" class="b-suggest-results__link tw-flex tw-gap-[1rem] tw-items-center tw-p-[0.4rem_0.8rem] tw-rounded-md tw-no-underline tw-duration-300 tw-transition-[background-color,color]">Recenze Mavic mini je tu! A ktomu 2 videjka{('angle-right')|icon, 'tw-flex-[0_0_auto] tw-ml-auto'}</a></li>
						<li><a href="#" class="b-suggest-results__link tw-flex tw-gap-[1rem] tw-items-center tw-p-[0.4rem_0.8rem] tw-rounded-md tw-no-underline tw-duration-300 tw-transition-[background-color,color]">Recenze Mavic mini je tu! A ktomu 2 videjka{('angle-right')|icon, 'tw-flex-[0_0_auto] tw-ml-auto'}</a></li>
						<li><a href="#" class="b-suggest-results__link tw-flex tw-gap-[1rem] tw-items-center tw-p-[0.4rem_0.8rem] tw-rounded-md tw-no-underline tw-duration-300 tw-transition-[background-color,color]">Recenze Mavic mini je tu! A ktomu 2 videjka{('angle-right')|icon, 'tw-flex-[0_0_auto] tw-ml-auto'}</a></li>
					</ul>
					{* Má historii - Naposledy hledané *}
					{* {if $userHasSearchedTexts}
						{include './part/suggest-links.latte', title: $translator->translate("suggest_title_searched"), items: $searchHistoryItems}
					{/if} *}

					<p class="tw-flex tw-items-center tw-gap-[0.8rem] tw-text-[1.5rem] tw-font-bold tw-py-[1.2rem] tw-mb-0 after:tw-content-[''] after:tw-block after:tw-h-[0.1rem] after:tw-flex-1 after:tw-bg-tile-light after:tw-top-[0.1rem] after:tw-relative">
						TODO BE: {_"suggest_title_most_searched"} (10)
					</p>
					<ul class="u-reset-ul tw-text-[1.4rem] tw-mb-[0.8rem]">
						<li><a href="#" class="b-suggest-results__link tw-flex tw-gap-[1rem] tw-items-center tw-p-[0.4rem_0.8rem] tw-rounded-md tw-no-underline tw-duration-300 tw-transition-[background-color,color]">Recenze Mavic mini je tu! A ktomu 2 videjka{('angle-right')|icon, 'tw-flex-[0_0_auto] tw-ml-auto'}</a></li>
						<li><a href="#" class="b-suggest-results__link tw-flex tw-gap-[1rem] tw-items-center tw-p-[0.4rem_0.8rem] tw-rounded-md tw-no-underline tw-duration-300 tw-transition-[background-color,color]">Recenze Mavic mini je tu! A ktomu 2 videjka{('angle-right')|icon, 'tw-flex-[0_0_auto] tw-ml-auto'}</a></li>
						<li><a href="#" class="b-suggest-results__link tw-flex tw-gap-[1rem] tw-items-center tw-p-[0.4rem_0.8rem] tw-rounded-md tw-no-underline tw-duration-300 tw-transition-[background-color,color]">Recenze Mavic mini je tu! A ktomu 2 videjka{('angle-right')|icon, 'tw-flex-[0_0_auto] tw-ml-auto'}</a></li>
					</ul>
					{* Ostatní také hledají *}
					{* {if !$userHasSearchedTexts}
						{include './part/suggest-links.latte', title: $translator->translate("suggest_title_most_searched"), items: $searchHistoryItems}
					{/if} *}
				</div>
			</div>
		</div>

		{* Vyhledávání *}
		<div class="f-search__searching b-suggest">
			<div class="b-suggest__box">
				<div class="b-suggest__main u-mb-last-0">
					<p class="b-suggest__info u-ta-c">
						{_"suggest_searching"|noescape}
					</p>
				</div>
			</div>
		</div>

		{* Výsledny vyhledávání *}
		<div class="f-search__results b-suggest" data-suggest-target="wrap"></div>
	</div>
</form>
