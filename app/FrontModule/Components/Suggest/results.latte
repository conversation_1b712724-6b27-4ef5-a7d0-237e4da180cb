<div class="b-suggest__box b-suggest__box--wide">
	{php $topProduct = false} {* TODO BE *}
	{if $hasResults}
		<div class="b-suggest__main">
			<div class="b-suggest-results">
				<div class="b-suggest-results__grid grid grid--y-0">
					<div class="grid__cell size--6-12@md size--3-12@xxl md:tw-order-1">
						<p class="tw-flex tw-items-center tw-gap-[0.8rem] tw-text-[1.5rem] tw-font-bold tw-py-[1.2rem] tw-mb-0 after:tw-content-[''] after:tw-block after:tw-h-[0.1rem] after:tw-flex-1 after:tw-bg-tile-light after:tw-top-[0.1rem] after:tw-relative">
							TODO BE: {_"suggest_title_may_search"}
						</p>
						<ul class="u-reset-ul tw-text-[1.4rem] tw-mb-[0.8rem]">
							<li><a href="#" class="b-suggest-results__link tw-flex tw-gap-[1rem] tw-items-center tw-p-[0.4rem_0.8rem] tw-rounded-md tw-no-underline tw-duration-300 tw-transition-[background-color,color]">DJI Mavic{('angle-right')|icon, 'tw-flex-[0_0_auto] tw-ml-auto'}</a></li>
							<li><a href="#" class="b-suggest-results__link tw-flex tw-gap-[1rem] tw-items-center tw-p-[0.4rem_0.8rem] tw-rounded-md tw-no-underline tw-duration-300 tw-transition-[background-color,color]">DJI Mavic{('angle-right')|icon, 'tw-flex-[0_0_auto] tw-ml-auto'}</a></li>
							<li><a href="#" class="b-suggest-results__link tw-flex tw-gap-[1rem] tw-items-center tw-p-[0.4rem_0.8rem] tw-rounded-md tw-no-underline tw-duration-300 tw-transition-[background-color,color]">DJI Mavic{('angle-right')|icon, 'tw-flex-[0_0_auto] tw-ml-auto'}</a></li>
						</ul>
						<p class="tw-flex tw-items-center tw-gap-[0.8rem] tw-text-[1.5rem] tw-font-bold tw-py-[1.2rem] tw-mb-0 after:tw-content-[''] after:tw-block after:tw-h-[0.1rem] after:tw-flex-1 after:tw-bg-tile-light after:tw-top-[0.1rem] after:tw-relative">
							TODO BE: {_"suggest_title_categories"} (18)
						</p>
						<ul class="u-reset-ul tw-text-[1.4rem] tw-mb-[0.8rem]">
							<li><a href="#" class="b-suggest-results__link tw-flex tw-gap-[1rem] tw-items-center tw-p-[0.4rem_0.8rem] tw-rounded-md tw-no-underline tw-duration-300 tw-transition-[background-color,color]">Příslušenství Mavic Air 2{('angle-right')|icon, 'tw-flex-[0_0_auto] tw-ml-auto'}</a></li>
							<li><a href="#" class="b-suggest-results__link tw-flex tw-gap-[1rem] tw-items-center tw-p-[0.4rem_0.8rem] tw-rounded-md tw-no-underline tw-duration-300 tw-transition-[background-color,color]">Příslušenství Mavic Air 2{('angle-right')|icon, 'tw-flex-[0_0_auto] tw-ml-auto'}</a></li>
							<li><a href="#" class="b-suggest-results__link tw-flex tw-gap-[1rem] tw-items-center tw-p-[0.4rem_0.8rem] tw-rounded-md tw-no-underline tw-duration-300 tw-transition-[background-color,color]">Příslušenství Mavic Air 2{('angle-right')|icon, 'tw-flex-[0_0_auto] tw-ml-auto'}</a></li>
						</ul>
						<p class="tw-flex tw-items-center tw-gap-[0.8rem] tw-text-[1.5rem] tw-font-bold tw-py-[1.2rem] tw-mb-0 after:tw-content-[''] after:tw-block after:tw-h-[0.1rem] after:tw-flex-1 after:tw-bg-tile-light after:tw-top-[0.1rem] after:tw-relative">
							TODO BE: {_"suggest_title_dronzone"} (18)
						</p>
						<ul class="u-reset-ul tw-text-[1.4rem] tw-mb-[0.8rem]">
							<li><a href="#" class="b-suggest-results__link tw-flex tw-gap-[1rem] tw-items-center tw-p-[0.4rem_0.8rem] tw-rounded-md tw-no-underline tw-duration-300 tw-transition-[background-color,color]">Recenze Mavic mini je tu! A ktomu 2 videjka{('angle-right')|icon, 'tw-flex-[0_0_auto] tw-ml-auto'}</a></li>
							<li><a href="#" class="b-suggest-results__link tw-flex tw-gap-[1rem] tw-items-center tw-p-[0.4rem_0.8rem] tw-rounded-md tw-no-underline tw-duration-300 tw-transition-[background-color,color]">Recenze Mavic mini je tu! A ktomu 2 videjka{('angle-right')|icon, 'tw-flex-[0_0_auto] tw-ml-auto'}</a></li>
							<li><a href="#" class="b-suggest-results__link tw-flex tw-gap-[1rem] tw-items-center tw-p-[0.4rem_0.8rem] tw-rounded-md tw-no-underline tw-duration-300 tw-transition-[background-color,color]">Recenze Mavic mini je tu! A ktomu 2 videjka{('angle-right')|icon, 'tw-flex-[0_0_auto] tw-ml-auto'}</a></li>
						</ul>
						{foreach $fulltextResults as $fulltextResult}
							{varType App\Model\FulltextSearch\Result $fulltextResult}
							{if $fulltextResult->hasItems()}
								{include './part/suggest-links.latte', title: $translator->translate("suggest_title_" . $fulltextResult->name), items: $fulltextResult->items}
							{/if}
						{/foreach}
					</div>
					<div n:class="grid__cell, $topProduct ? 'size--6-12@xxl' : 'size--9-12@xxl', 'md:tw-order-3 xxl:tw-order-2'">
						{varType App\Model\BucketFilter\Result $productResultObject}
						{if $productResultObject->count > 0}
							{control resultSuggestProducts}
						{/if}
					</div>
					<div n:if="$topProduct" class="grid__cell size--6-12@md size--3-12@xxl md:tw-order-2 xxl:tw-order-3">
						<p class="tw-flex tw-items-center tw-gap-[0.8rem] tw-text-[1.5rem] tw-font-bold tw-py-[1.2rem] tw-mb-0 after:tw-content-[''] after:tw-block after:tw-h-[0.1rem] after:tw-flex-1 after:tw-bg-tile-light after:tw-top-[0.1rem] after:tw-relative">
							{_"suggest_title_top_product"}
						</p>
						TODO BE: product box
					</div>
				</div>
			</div>
			<p class="tw-mt-[1.6rem] tw-pt-[1.6rem] tw-border-[0.1rem] tw-border-solid tw-border-tile-light tw-border-l-0 tw-border-r-0 tw-border-b-0 tw-text-center" n:if="isset($mutation->pages->search) && $mutation->pages->search !== null">
				<a href="{plink $mutation->pages->search, 'search' => $search}" class="btn">
					<span class="btn__text">
						{_"btn_show_all_results"}
					</span>
				</a>
			</p>
		</div>
	{else}
		<div class="b-suggest__main u-mb-last-0">
			<p class="b-suggest__info u-ta-c">
				{_suggest_empty}
			</p>
		</div>
	{/if}
</div>
