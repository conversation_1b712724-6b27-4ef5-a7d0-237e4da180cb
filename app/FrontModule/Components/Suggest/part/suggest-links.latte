{default $title = false}
{default $inline = false}
{default $items = []}

{* {if count($items)} *}
	<p class="tw-flex tw-items-center tw-gap-[0.8rem] tw-text-[1.5rem] tw-font-bold tw-py-[1.2rem] tw-mb-0 after:tw-content-[''] after:tw-block after:tw-h-[0.1rem] after:tw-flex-1 after:tw-bg-tile-light after:tw-top-[0.1rem] after:tw-relative">
		{$title}
	</p>
	<ul n:ifcontent class="u-reset-ul tw-text-[1.4rem] tw-mb-[0.8rem]">
		<li n:foreach="$items as $item">
			{if $item instanceOf App\Model\Orm\Routable}
				<a href="{plink $item}" class="b-suggest-results__link tw-flex tw-gap-[1rem] tw-items-center tw-p-[0.4rem_0.8rem] tw-rounded-md tw-no-underline tw-duration-300 tw-transition-[background-color,color]" data-list-id="{$title}" data-list-name="{$item->nameAnchor}">
					{$item->nameAnchor}
					{('angle-right')|icon, 'tw-flex-[0_0_auto] tw-ml-auto'}
				</a>
			{else}
				<a href="{plink $item['page'], ...$item['parameters']}" class="b-suggest-results__link tw-flex tw-gap-[1rem] tw-items-center tw-p-[0.4rem_0.8rem] tw-rounded-md tw-no-underline tw-duration-300 tw-transition-[background-color,color]" data-list-id="{$title}" data-list-name="{$item->nameAnchor}">
					{$item['name']}
					{('angle-right')|icon, 'tw-flex-[0_0_auto] tw-ml-auto'}
				</a>
			{/if}

		</li>
	</ul>
{* {/if} *}
