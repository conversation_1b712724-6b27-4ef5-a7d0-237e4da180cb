<?php

declare(strict_types=1);

namespace App\FrontModule\Components\Suggest\Components\SuggestProducts;

use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Setup;
use Nette\Utils\ArrayList;
use Nextras\Orm\Collection\ICollection;

interface SuggestProductsFactory
{

	/**
	 * @param ICollection<Product>|ArrayList<Product|ProductVariant> $products
	 */
	public function create(
		string $title,
		Setup $setup,
		ICollection|ArrayList $products,
	): SuggestProducts;

}
