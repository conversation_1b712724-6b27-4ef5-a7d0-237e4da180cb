<p class="tw-flex tw-items-center tw-gap-[0.8rem] tw-text-[1.5rem] tw-font-bold tw-py-[1.2rem] tw-mb-0 after:tw-content-[''] after:tw-block after:tw-h-[0.1rem] after:tw-flex-1 after:tw-bg-tile-light after:tw-top-[0.1rem] after:tw-relative">
	{$title}
</p>
<ul class="u-reset-ul">
	<li n:foreach="$products as $product">
		<a href="{plink $product}" class="b-suggest-results__link tw-flex tw-gap-[1rem] tw-items-center tw-p-[0.3rem_0.8rem_0.3rem_0.3rem] tw-rounded-md tw-no-underline tw-duration-300 tw-transition-[background-color,color]">
			{if $product->firstImage}
				<img class="tw-flex-[0_0_auto] tw-w-[12.8rem] tw-rounded-sm tw-bg-white img img--4-3" src="{$product->firstImage->getSize('xs')->src}" alt="{$product->nameAnchor}" loading="lazy">
			{else}
				<img class="tw-flex-[0_0_auto] tw-w-[12.8rem] tw-rounded-sm tw-bg-white img img--4-3" src="/static/img/illust/noimg.svg" alt="" loading="lazy">
			{/if}
			{$product->nameAnchor}
			{('angle-right')|icon, 'tw-flex-[0_0_auto] tw-ml-auto'}
		</a>
	</li>
</ul>