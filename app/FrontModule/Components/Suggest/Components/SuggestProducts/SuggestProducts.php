<?php declare(strict_types = 1);

namespace App\FrontModule\Components\Suggest\Components\SuggestProducts;

use App\FrontModule\Components\ProductBox\ProductBox;
use App\FrontModule\Components\ProductBox\ProductBoxFactory;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Setup;
use App\Model\TranslatorDB;
use Nette\Application\UI;
use Nette\Application\UI\Multiplier;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Utils\ArrayList;
use Nextras\Orm\Collection\ICollection;

/**
 * @property-read DefaultTemplate $template
 */
final class SuggestProducts extends UI\Control
{

	/**
	 * @param ICollection<Product>|ArrayList<Product|ProductVariant> $products
	 */
	public function __construct(
		private readonly string $title,
		private readonly Setup $setup,
		private ICollection|ArrayList $products,
		private readonly TranslatorDB $translator,
		private readonly ProductBoxFactory $productBoxFactory,
	)
	{
	}


	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->title = $this->title;
		$this->template->products = $this->products;
		$this->template->render(__DIR__ . '/suggestProducts.latte');
	}

	/**
	 * @param ICollection<Product>|ArrayList<Product|ProductVariant> $products
	 */
	public function setProducts(ICollection|ArrayList $products): void
	{
		$this->products = $products;
	}

	/**
	 * @return Multiplier<ProductBox>
	 */
	public function createComponentProductBox(): Multiplier
	{
		$products = [1 => new Product()]; // @todo ArrayList<Product|ProductVariant>$this->products->fetchPairs('id');

		return new Multiplier(function ($productId) use ($products) {
			$product = $products[$productId];
			$parametersToTemplate = [
				'class' => 'b-product--suggest',
				'hrefClass' => 'suggest-click',
				'listId' => $this->title,
				'listName' => $product->name,
			];

			assert($product instanceof Product);

			return $this->productBoxFactory->create($product, null, $this->setup, $parametersToTemplate);
		});
	}

}
