{define #variantPicker}
	{default $title = false}
	{default $showSizeHelper = false} {* TODO BE *}
	{default $showImages = false} {* TODO BE *}
	{default $items = []} {* TODO BE *}

	<div class="b-product-action__variants tw-mb-[2.4rem]">
		<p class="tw-flex tw-flex-wrap tw-gap-[0.4rem] tw-justify-between tw-mb-[0.8rem] tw-text-[1.4rem]">
			{$title}:
			<a n:if="$showSizeHelper" href="#" class="item-icon tw-text-help tw-gap-[0.4rem]"> {* TODO BE: modal link *}
				{('meter')|icon, 'item-icon__icon'}
				<span class="item-icon__text">
					{_"variants_size_helper"}
				</span>
			</a>
		</p>
		<ul class="u-reset-ul tw-flex tw-flex-wrap tw-gap-[0.8rem]">
			<li n:foreach="$items as $i => $item">
				{php $isDisabled = $i == 0} {* TODO BE *}
						{php $isActive = $i == 1} {* TODO BE *}

				<a n:tag="$isActive ? span"{if !$isActive} href="{$item->href}"{/if} n:class="btn, btn--bd, tw-relative, $isActive ? 'is-active' : 'btn--gray', btn--icon" {if $isDisabled}disabled{/if}>
							<span n:class="btn__text, $showImages ? tw-p-0">
								<img n:if="$showImages" src="{$item->src}" alt="" loading="lazy" width="92" {if $isActive}height="69"{else}height="71"{/if} n:class="$isDisabled ? tw-grayscale" />
								<span n:tag-if="$showImages" class="u-vhide">{$item->value}</span>
							</span>
					<img n:if="$item->hasDiscount ?? false" class="tw-absolute tw-top-[-0.4rem] tw-right-[-0.4rem]" src="/static/img/illust/discount.svg" alt="" loading="lazy" width="30" height="30">
				</a>
			</li>
		</ul>
	</div>
{/define}


{* TOTO prijde napojit*}
{dump $validCombinations}
{foreach $pickerParameters as $pickerParameter}
	{dump $pickerParameter}
{/foreach}

{include #variantPicker, title: 'Vyberte velikost', showSizeHelper: true, items: [(object) array('value' => "S", 'href' => "#"), (object) array('value' => "M", 'href' => "#"), (object) array('value' => "L", 'href' => "#"), (object) array('value' => "5XL", 'href' => "#")]}
{include #variantPicker, title: 'Vyberte barvu', showImages: true, items: [(object) array('value' => "Modrá", 'href' => "#", 'src' => '/static/img/illust/dummy-course.png'), (object) array('value' => "Zelená", 'href' => "#", 'src' => '/static/img/illust/dummy-course.png', 'hasDiscount' => true), (object) array('value' => "Oranžová", 'href' => "#", 'src' => '/static/img/illust/dummy-product.png')]}
