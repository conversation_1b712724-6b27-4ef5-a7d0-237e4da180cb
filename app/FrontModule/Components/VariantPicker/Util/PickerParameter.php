<?php

namespace App\FrontModule\Components\VariantPicker\Util;

use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\ProductVariant\ProductVariant;

class PickerParameter
{

	/**
	 * @param Parameter $parameter
	 * @param array<ProductVariant> $variants
	 * @param array<ParameterValue> $parameterValues
	 * @param ParameterValue|null $activeParameterValue
	 */
	public function __construct(
		public readonly Parameter $parameter,
		public array $variants = [],
		public array $parameterValues = [],
		public ?ParameterValue $activeParameterValue = null,
	)
	{
	}

}
