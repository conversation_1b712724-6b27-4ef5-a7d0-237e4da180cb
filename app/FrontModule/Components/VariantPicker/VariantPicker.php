<?php declare(strict_types = 1);

namespace App\FrontModule\Components\VariantPicker;

use App\FrontModule\Components\VariantPicker\Util\PickerParameter;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\TranslatorDB;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;

/**
 * @property-read DefaultTemplate $template
 */
final class VariantPicker extends UI\Control
{

	private array $pickerParameters = [];

	private array $validCombinations = [];

	public function __construct(
		private readonly ProductLocalization $productLocalization,
		private readonly ProductVariant $productVariant,
		private readonly TranslatorDB $translator,
	)
	{
		$this->onAnchor[] = $this->init(...);
	}

	private function init(): void
	{
		$this->setParameterToVariantSetup();
	}

	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->pickerParameters = $this->pickerParameters;
		$this->template->validCombinations = $this->validCombinations;
		$this->template->templates = FE_TEMPLATE_DIR;
		$this->template->render(__DIR__ . '/variantPicker.latte');
	}

	protected function handleVariantChange(string $variantId): void
	{
	}

	private function setParameterToVariantSetup(): void
	{
		foreach ($this->productLocalization->product->activeVariants as $activeVariant) {
			$parameterValues = $activeVariant->getFilledVariantParameterValues();
			array_walk($parameterValues, function (ParameterValue $parameterValue) use ($activeVariant) {
				$this->readParameter($activeVariant, $parameterValue);
			});

			$this->validCombinations[] = implode('-', array_map(
				fn(ParameterValue $parameterValue) => $parameterValue->id,
				$parameterValues
			));
		}

		foreach ($this->productVariant->getFilledVariantParameterValues() as $parameterValue) {
			$this->pickerParameters[$parameterValue->parameter->id]->activeParameterValue = $parameterValue;
		}
	}

	private function readParameter(ProductVariant $activeVariant, ParameterValue $parameterValue): void
	{
		if (!isset($this->pickerParameters[$parameterValue->parameter->id])) {
			$this->pickerParameters[$parameterValue->parameter->id] = new PickerParameter($parameterValue->parameter);
		}
		$this->pickerParameters[$parameterValue->parameter->id]->variants[$activeVariant->id] = $activeVariant;
		$this->pickerParameters[$parameterValue->parameter->id]->parameterValues[$parameterValue->id] = $parameterValue;
	}

}
