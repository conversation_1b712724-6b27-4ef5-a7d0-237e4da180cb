{varType App\Model\Mutation $mutation}
{default $nameSuffix = ''}

<div n:class="b-step2__inp-group, u-mb-last-0, f-open,  $form['companyTab'.$nameSuffix]->value ? is-open" data-controller="toggle-class"> {*!$shoppingCart->useDeliveryAddress() ? 'u-d-n',*}
	<p class="u-mb-0">
		<label class="inp-item inp-item--checkbox">
			{var $inputName = "companyTab$nameSuffix"}
			<input n:name=$inputName value="1" class="inp-item__inp" data-action="change->toggle-class#toggleInp">
			<span class="inp-item__text">
				{_'title_company_form'}
			</span>
		</label>
	</p>
	<div class="f-open__box u-pt-sm">
		{include '../../../../Components/inp.latte', class: 'b-step2__inp', form=>$form, name=>'inv_ic'.$nameSuffix, required=>true, showError: true, data: ['ares-target' => 'ic']}
		{* TODO read data from ARES
		<div class="grid__cell size--5-12 size--4-12@sm">
			<p class="b-step2__btn">
				<button type="button" class="btn btn--bd btn--loader" data-ares-target="btn" data-action="ares#fetch">
					<span class="btn__text">
						{_"btn_ares"}
					</span>
				</button>
			</p>
		</div>
		*}
		{include '../../../../Components/inp.latte', class: 'b-step2__inp', form=>$form, name=>'inv_company'.$nameSuffix, required=>true, validate: true, showError: true, data: ['ares-target' => 'companyName']}
		{include '../../../../Components/inp.latte', class: 'b-step2__inp', form=>$form, name=>'inv_dic'.$nameSuffix, validate: true, showError: true, data: ['ares-target' => 'dic']}
		{* {include '../../../../Components/inp.latte', class: 'b-step2__inp', form=>$form, name=>'inv_invoice_pay'.$nameSuffix, showError: true, labelLang: 'form_label_invoice_pay'} *}
	</div>
</div>

