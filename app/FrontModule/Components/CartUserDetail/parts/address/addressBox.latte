{if $address ?? false}
	{if empty($address->delCity)}
		{* Dodací = Fakturační *}
		<p class="tw-mb-[0.8rem]">
			{implode('<br>', array_filter([
				$address->invCompany ?? false,
				$address->invFirstname . ' ' . $address->invLastname,
				$address->invStreet,
				$address->invCity . ', ' . $address->invZip,
				($address->invIc ?? false) ? $translator->translate("form_label_ic") . ': ' . $address->invIc : false,
				($address->invDic ?? false) ? $translator->translate("form_label_dic") . ': ' . $address->invDic : false,
				(($address->invPhone ?? false)|phoneFormat)
			]))|noescape}
		</p>
	{else}
		{* Odlišná dodací adresa *}
		<div class="grid grid--y-0">
			<div class="grid__cell size--6-12@sm">
				<p class="tw-mb-[0.8rem]">
					<strong class="u-d-b u-mb-xxs">{_'delivery_address_title'}</strong>
					{implode('<br>', array_filter([
						$address->delCompany ?? false,
						$address->delFirstname . ' ' . $address->delLastname,
						$address->delStreet,
						$address->delCity . ', ' . $address->delZip,
						($address->delIc ?? false) ? $translator->translate("form_label_ic") . ': ' . $address->delIc : false,
						($address->delDic ?? false) ? $translator->translate("form_label_dic") . ': ' . $address->delDic : false,
						(($address->delPhone ?? false)|phoneFormat)
					]))|noescape}
				</p>
			</div>
			<div class="grid__cell size--6-12@sm">
				<p class="tw-mb-[0.8rem]">
					<strong class="u-d-b u-mb-xxs">{_'invoice_address_title'}</strong>
					{implode('<br>', array_filter([
						$address->invCompany ?? false,
						$address->invFirstname . ' ' . $address->invLastname,
						$address->invStreet,
						$address->invCity . ', ' . $address->invZip,
						($address->invIc ?? false) ? $translator->translate("form_label_ic") . ': ' . $address->invIc : false,
						($address->invDic ?? false) ? $translator->translate("form_label_dic") . ': ' . $address->invDic : false,
						(($address->invPhone ?? false)|phoneFormat)
					]))|noescape}
				</p>
			</div>
		</div>
	{/if}

	<p n:if="$address->addressNote ?? false" class="tw-mb-[0.8rem]">
		{$address->addressNote}
	</p>
{/if}
