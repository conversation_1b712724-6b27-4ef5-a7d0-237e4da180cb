{default $class = false}
{default $content = $object->cf->registration_benefits?->content ?? false}

<div n:class="f-order-register, $class">
	<p class="u-mb-0" n:if="$isRegistrationRequired">
		{_'order_registration_required'}
	</p>

	<div class="grid grid--middle">
		<div class="grid__cell size--7-12@lg">
			{include '../../../inp.latte', class: false, form: $form, name: password, validate: true, showError: true}
			{include '../../../inp.latte', class: false, form: $form, name: passwordVerify, validate: true, showError: true}
		</div>
		<div n:if="$content" class="f-order-register__benefits grid__cell size--5-12@lg">
			{$content|noescape}
		</div>
	</div>
</div>
