{default $nameSuffix = ''}
{default $class = 'u-mb-xxs'}


<div n:class="b-step2__inp-group, u-mb-last-0, f-open, $class, $form['deliveryTab'.$nameSuffix]->value ? is-open" data-controller="toggle-class" n:if="$shoppingCart->useDeliveryAddress()">
	<p class="u-mb-0">
		<label class="inp-item inp-item--checkbox">
			{var $inputName = 'deliveryTab'.$nameSuffix}
			<input n:name=$inputName value="1" class="inp-item__inp" data-action="change->toggle-class#toggleInp">
			<span class="inp-item__text">
				{_'title_delivery_form'}
			</span>
		</label>
	</p>
	<div n:class="f-open__box, u-pt-xs, u-mb-last-0" data-controller="address-suggest">
		{include '../../../../Components/inp.latte', class: 'b-step2__inp', form: $form, name: 'del_firstname'.$nameSuffix, required: true, validate: true, showError: true}
		{include '../../../../Components/inp.latte', class: 'b-step2__inp', form: $form, name: 'del_lastname'.$nameSuffix, required: true, validate: true, showError: true}
		{include '../../../../Components/inp.latte', class: 'b-step2__inp', form: $form, name: 'del_company'.$nameSuffix}
		{include '../../../../Components/inp.latte', class: 'b-step2__inp', form: $form, name: 'del_phone'.$nameSuffix, type: 'tel'}
		{include '../../../../Components/inp.latte', class: 'b-step2__inp', form: $form, name: 'del_street'.$nameSuffix, required: true, validate: true, inpClass: 'smartform-street-and-number smartform-instance-del'.$nameSuffix, showError: true}

		{if isset($form['del_countrycode'.$nameSuffix])}
			{include '../../../../Components/inp.latte', class: 'b-step2__inp', form: $form, name: 'del_countrycode'.$nameSuffix, validate: true, required: true, showError: true}
		{/if}

		<div class="grid">
			<div class="grid__cell size--7-12 size--8-12@sm">
				{include '../../../../Components/inp.latte', class: 'b-step2__inp', form: $form, name: 'del_city'.$nameSuffix, required: true, validate: true, inpClass: 'smartform-city smartform-instance-del'.$nameSuffix, showError: true}
			</div>
			<div class="grid__cell size--5-12 size--4-12@sm">
				{include '../../../../Components/inp.latte', class: 'b-step2__inp', form: $form, name: 'del_zip'.$nameSuffix, required: true, validate: true, inpClass: 'smartform-zip smartform-instance-del'.$nameSuffix, showError: true}
			</div>
		</div>

		{include $templates.'/part/form/part/state.latte', name: 'del_state'}
	</div>
</div>
