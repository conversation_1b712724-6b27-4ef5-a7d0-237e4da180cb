<form n:name="signInForm" class="f-basket u-mb-last-0" novalidate="novalidate" data-naja data-naja-loader="body" data-naja-history="off">
	<h2 class="b-step2__title h3">
		{_'title_personal_info'}
	</h2>

	{control formMessage, $flashes, $form}

	<div class="b-step2__inp-group">
		<p class="b-step2__inp">
			<a n:href="changeEmail!" data-naja data-naja-history="off" data-naja-loader="body">{_order_change_email}</a>
		</p>

		{include '../../inp.latte', class: 'b-step2__inp', form: $form, name: email, validate: true, showError: true}
		{include '../../inp.latte', class: 'b-step2__inp', form: $form, name: password, required: true, validate: true, showError: true}

		<p class="b-step2__inp">
			{capture $link}{plink $pages->userChangePassword}{/capture}
			{_'note_step2_forgot_password'|replace:'%link', $link->__toString()|noescape}
		</p>
	</div>

	<p class="b-step2__inp">
		<button n:name="save" class="btn btn--block">
			<span class="btn__text">
				{_'btn_login'}
			</span>
		</button>
	</p>
	<p class="b-step2__inp">
		<button n:name="next" class="btn btn--secondary btn--block">
			<span class="btn__text">
				{_'btn_step2_continue_without_signin'}
			</span>
		</button>
	</p>
</form>
