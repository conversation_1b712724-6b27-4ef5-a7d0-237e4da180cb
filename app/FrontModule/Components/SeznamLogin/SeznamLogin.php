<?php declare(strict_types = 1);

namespace App\FrontModule\Components\SeznamLogin;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserModel;
use App\Model\Orm\User\UserRepository;
use App\Model\Security\Acl;
use App\Model\SocialLoginService;
use App\Model\TranslatorDB;
use League\OAuth2\Client\Token\AccessToken;
use Nette\Application\Attributes\CrossOrigin;
use Nette\Application\UI\Control;
use Nette\Security\SimpleIdentity;
use Nette\Utils\Strings;
use Throwable;
use WebChemistry\OAuth2\Client\Seznam\Provider\SeznamUser;

final class SeznamLogin extends Control
{

	public function __construct(
		private readonly bool $isEnabled,
		private readonly Mutation $mutation,
		private readonly SocialLoginService $socialLoginService,
		private readonly TranslatorDB $translator,
		private readonly UserRepository $userRepository,
		private readonly UserModel $userModel,
	)
	{
	}

	public function render(): void
	{
		if (!$this->isEnabled) {
			return;
		}

		$this->getTemplate()->setTranslator($this->translator);
		$this->getTemplate()->setFile(__DIR__ . '/templates/seznamLogin.latte');
		$this->getTemplate()->render();
	}

	public function handleLogin(): void
	{
		$this->presenter->redirectUrl($this->socialLoginService->getSeznam()->getAuthorizationUrl());
	}

	#[CrossOrigin]
	public function handleResponse(): void
	{
		if (!$userData = $this->getUserData()) {
			$this->getPresenter()->flashMessage($this->translator->translate('seznam_login_get_datafailed'), 'error');
			$this->getPresenter()->redirect('this');
		}

		$this->logInById($userData);
		$this->pairLoggedByEmail($userData);
		$this->register($userData);

		$this->getPresenter()->flashMessage($this->translator->translate('seznam_login_failed'));
		$this->getPresenter()->redirect('this');
	}

	private function logInById(SeznamUser $userData): void
	{
		$user = $this->userRepository->getBySeznamId($userData->getId());

		if ($user === null) {
			return;
		}

		$this->getPresenter()->getUser()->login(new SimpleIdentity($user->id, $user->role));
		$this->redirectToUserSection();
	}

	private function pairLoggedByEmail(SeznamUser $userData): void
	{
		if (Strings::length($userData->getEmail()) === 0) {
			return;
		}

		if (!$user = $this->userRepository->getByEmail($userData->getEmail(), $this->mutation)) {
			return;
		}

		if ($user->seznamId === null) {
			$user->seznamId = $userData->getId();
			$this->userRepository->persistAndFlush($user);
		}

		$this->getPresenter()->getUser()->login(new SimpleIdentity($user->id, $user->role));
		$this->redirectToUserSection();
	}

	private function register(SeznamUser $userData): void
	{
		if (Strings::length($userData->getEmail()) === 0) {
			return;
		}

		if ($this->userRepository->getByEmail($userData->getEmail(), $this->mutation)) {
			return;
		}

		$user = new User();
		$this->userRepository->attach($user);
		$user->role = Acl::ROLE_USER;
		$user->priceLevel = PriceLevel::DEFAULT_ID;
		$user->mutations->add($this->mutation);

		$user = $this->userModel->save($user, [
			'email' => $userData->getEmail(),
			'seznamId' => $userData->getId(),
		]);

		$this->presenter->getUser()->login(new SimpleIdentity($user->id, $user->role));
		$this->presenter->getHttpResponse()->setCookie('registrationComplete', '1', '+1 minute');
		$this->redirectToUserSection();
	}

	private function redirectToUserSection(): never
	{
		$this->getPresenter()->redirect($this->mutation->pages->userSection);
	}

	private function getUserData(): SeznamUser|null
	{
		if (!$code = $this->getPresenter()->getRequest()->getParameter('code')) {
			return null;
		}

		/**
		 * @var AccessToken $token
		 */
		$token = $this->socialLoginService->getSeznam()->getAccessToken('authorization_code', [
			'code' => $code,
		]);

		try {
			/**
			 * @var SeznamUser $userData
			 */
			$userData = $this->socialLoginService->getSeznam()->getResourceOwner($token);
			return $userData;

		} catch (Throwable $e) {
			// Failed to get user details
			bdump($e);
		}

		return null;
	}

}
