{varType App\Model\Pages $pages}
{default $class = false}

<div n:class="b-register, $class">
	<div class="b-register__login">
		<p class="message message--error u-mb-xs u-d-n" data-login-target="msg">
			<span class="item-icon">
				{('alert')|icon, 'item-icon__icon'}
				<span class="item-icon__text"></span>
			</span>
		</p>
		<p class="h3 u-mt-0 u-mb-xs">
			{_"header_title_login"}
		</p>
		{snippet boxLogin}
			{control signInFormHeader}
		{/snippet}
	</div>
	<div class="b-register__register">
		<p>
			{_"user_login_promo_title"}
			<a href="{plink $pages->registration}" class="btn">
				<span class="btn__text">
					{$pages->registration->nameAnchor}
				</span>
			</a>
		</p>
		{include $templates.'/part/box/content.latte', class=>'b-register__content u-mb-xs', content=>$pages->registration->cf->register_benefits??->content ?? false}
	</div>
</div>
