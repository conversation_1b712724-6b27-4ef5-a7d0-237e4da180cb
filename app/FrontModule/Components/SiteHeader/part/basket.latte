{varType App\Model\Pages $pages}
{default $class = false}

<div n:class="b-basket, $class, overlay-pseudo" n:if="$pages->precart !== null" n:snippet="headerCart" data-controller="hover touch-open etarget toggle-class">
	{var App\Model\ShoppingCart\ShoppingCart $shoppingCart = $presenter->shoppingCart}

	{define #linkBlock}
		{default $main = false}
		<p n:class="b-basket__btn, $main ? b-basket__btn--main : b-basket__btn--inner, u-mb-0">
			<a href="{plink $pages->cart}" class="b-basket__link prerender-hover" data-action="touch-open#open" aria-expanded="false">
				<span class="b-basket__icon-holder">
					{('basket')|icon, 'b-basket__icon'}
					<b class="b-basket__count">{$shoppingCart->getTotalCount()}</b>
				</span>
				<span class="b-basket__content">
					{_"shopping_basket"}
					<span class="b-basket__total u-d-b">
						{App\Model\Orm\Price::from($shoppingCart->getTotalPriceVat(precision: 2))->asMoney()|money}
					</span>
				</span>
			</a>
		</p>
	{/define}

	{include #linkBlock, main: true}

	<div class="b-basket__box">
		<div class="b-basket__top">
			{include #linkBlock}
			<p class="u-mb-0">
				<a n:if="isset($pages->cart)" href="{plink $pages->cart}" class="btn btn--secondary">
					<span class="btn__text">
						{_"btn_enter_basket"}
					</span>
				</a>
			</p>
			<p class="b-basket__toggle u-mb-0">
				<button type="button" class="btn btn--icon" data-action="toggle-class#toggle">
					<span class="btn__text">
						{('cross')|icon}
						<span class="u-vhide">{_"btn_close"}</span>
					</span>
				</button>
			</p>
		</div>
		<div class="b-basket__main">
			{control cart:header}
		</div>
	</div>
</div>
