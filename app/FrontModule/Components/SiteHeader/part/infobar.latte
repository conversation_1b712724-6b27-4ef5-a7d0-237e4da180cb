{default $class = false}
{default $msg = false}
{default $isAbsolute = false}

{if $msg}
	{php $msgCookieId = 'msg-' . $msg->id . '-' . $msg->editedTime?->getTimestamp()} {*date('Y-m-d-H-i-s', strtotime((string)$msg->editedTime))*}
	{php $emoji = $msg->cf->settings->emoji ?? false}
	{php $bg = $msg->cf->settings->bg ?? 'warning'}

	<div n:ifcontent n:class="message, message--sm, $bg ? 'message--'.$bg, $class" data-controller="message" data-message-id-value="{$msgCookieId}">
		<div n:if="$emoji" class="message__emoji">{$emoji}</div>
		<div class="message__content u-mb-last-0">
			{$msg->description|noescape}
		</div>
		{if $msg->closable}
		<button type="button" class="message__close as-link" data-action="message#close">
			{('cross')|icon}
			<span class="u-vhide">
				{_"btn_close"}
			</span>
		</button>
		{/if}
	</div>
{/if}
