{varType App\Model\Pages $pages}
{default $class = false}

{php $isLogged = $userEntity !== null}

<div n:if="$pages->userSection !== null && $pages->userLogin !== null" n:class="b-login, $class, $isLogged ? overlay-pseudo" {if $isLogged}data-controller="hover touch-open etarget"{/if}>
	{if $isLogged}
		{* {if !$isOrder} *}
			{* přih<PERSON>ášený *}
			<p class="u-mb-0">
				<a href="{plink $pages->userSection}" class="b-login__link b-login__link--logged" data-action="touch-open#open" aria-expanded>
					{('user')|icon, 'b-login__icon'}
					{* TODO BE podmínka *}
					{* <span class="b-login__notification notification"></span> *}
					<span class="b-login__name">
						{if $userEntity->firstname ?? null}
							{$userEntity->firstname} {if $userEntity->lastname ?? null}{$userEntity->lastname|first}.{/if}
						{else}
							{_'header_logged_fallback'}
						{/if}
					</span>
				</a>
			</p>
			<div class="b-login__box">
				<div class="b-login__inner">
					{control userMenu}
				</div>
			</div>
		{* {/if} *}
	{else}
		{* login / registrace *}
		<p class="u-mb-0">
			<a href="{plink $pages->userLogin}" class="b-login__link" data-modal='{"medium": "fetch", "modalClass": "b-modal--xs"}' data-snippetid="snippet--userFormArea">
				{('user')|icon, 'b-login__icon'}
				<span class="u-vhide">
					{$pages->userLogin->nameAnchor}
				</span>
			</a>
		</p>
	{/if}
</div>
