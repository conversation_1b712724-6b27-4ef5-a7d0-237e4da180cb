<?php declare(strict_types = 1);

namespace App\FrontModule\Components\Robots;

use App\Model\ConfigService;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\Routable;
use App\Model\Orm\RoutableEntity;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\Model\StaticPage\StaticPage;
use App\Model\TranslatorDB;
use App\PostType\Page\Model\Orm\Tree;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nextras\Orm\Entity\Entity;

/**
 * @property-read DefaultTemplate $template
 */
final class Robots extends UI\Control
{

	private ?string $forceState = null;

	public function __construct(
		private readonly Routable|StaticPage $object,
		private readonly Mutation $mutation,
		private readonly TranslatorDB $translator,
		private readonly ConfigService $configService,
	)
	{
	}

	public function setForceState(?string $forceState): void
	{
		$this->forceState = $forceState;
	}

	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->object = $this->object;
		// default setup from config
		$this->template->robots = $this->configService->get('mutations', $this->mutation->langCode, 'robots');

		$this->getTemplate()->forceNoIndex = $this->hasForceNoIndex();
		$this->getTemplate()->hasForceState = $this->hasForceState();
		$this->getTemplate()->forceState = $this->forceState;
		$this->getTemplate()->isSystemPage = $this->isSystemPage();
		$this->getTemplate()->noFollowDamagedProduct = $this->noFollowDamagedProduct();

		if ($this->object instanceof CatalogTree) {
			$this->template->render(__DIR__ . '/catalog.latte');
		} else {
			$this->template->render(__DIR__ . '/common.latte');
		}
	}

	public function isSystemPage(): bool
	{
		$pathIds = [];
		if (isset($this->object->path)) {
			foreach ($this->object->path as $pathItem) {
				if (is_object($pathItem) && isset($pathItem->id)) {
					$pathIds[] = $pathItem->id;
				} else {
					$pathIds[] = $pathItem;
				}
			}
		}

		return in_array($this->configService->get('mutations', $this->mutation->langCode, 'systemPageId'), $pathIds);
	}

	public function hasForceNoIndex(): bool
	{
		if (! $this->object instanceof RoutableEntity) {
			return false;
		}

		if (! $this->object->getMetadata()->hasProperty('forceNoIndex')) {
			return false;
		}

		if (!isset($this->object->forceNoIndex)) {
			return false;
		}

		if ($this->object->forceNoIndex === null) {
			return false;
		}

		return (bool) $this->object->forceNoIndex;
	}

	public function hasForceStateNoIndex(): bool
	{
		if (!$this->hasForceState()) {
			return false;
		}

		return $this->forceState === 'noindex, follow';
	}

	public function hasForceState(): bool
	{
		return $this->forceState !== null;
	}

	public function noFollowDamagedProduct(): bool
	{
		if (!($this->object instanceof ProductLocalization)) {
			return false;
		}

		if (!$this->object->product->isDamaged) {
			return false;
		}

		return $this->object->product->damageLevel > 1;
	}

}
