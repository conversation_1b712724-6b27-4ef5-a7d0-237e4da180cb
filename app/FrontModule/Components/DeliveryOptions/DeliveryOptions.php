<?php declare(strict_types = 1);

namespace App\FrontModule\Components\DeliveryOptions;

use App\FrontModule\Components\Breadcrumb\Breadcrumb;
use App\FrontModule\Components\Breadcrumb\BreadcrumbFactory;
use App\FrontModule\Components\CartFreeDelivery\CartFreeDelivery;
use App\FrontModule\Components\CartFreeDelivery\CartFreeDeliveryFactory;
use App\Infrastructure\Latte\Filters;
use App\Model\Orm\DeliveryMethod\DeliveryMethodConfigurationRepository;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\State\State;
use App\Model\Orm\User\UserProvider;
use App\Model\Security\User;
use App\Model\TranslatorDB;
use Brick\Money\Currency;
use Nette\Application\UI\Control;
use Nette\DI\Attributes\Inject;

final class DeliveryOptions extends Control
{

	#[Inject]
	public CartFreeDeliveryFactory $cartFreeDeliveryFactory;

	#[Inject]
	public BreadcrumbFactory $breadcrumbFactory;

	public function __construct(
		private readonly ProductVariant $variant,
		private readonly Mutation $mutation,
		private readonly State $state,
		private readonly PriceLevel $priceLevel,
		private readonly Currency $currency,
		private readonly UserProvider $userProvider,
		private readonly TranslatorDB $translator,
		private readonly DeliveryMethodConfigurationRepository $deliveryMethodConfigurationRepository,
	)
	{
	}

	public function render(): void
	{
		[$this->getTemplate()->deliveryOptionsPickUp, $this->getTemplate()->deliveryOptionsPhysical] = $this->prepareDeliveryOptions();
		$this->getTemplate()->templates = FE_TEMPLATE_DIR;
		$this->getTemplate()->priceLevel = $this->priceLevel;
		$this->getTemplate()->state = $this->state;
		$this->getTemplate()->mutation = $this->mutation;
		$this->getTemplate()->variant = $this->variant;
		$this->getTemplate()->currency = $this->currency;

		$this->getTemplate()->setTranslator($this->translator);
		$this->getTemplate()->addFilter('formatMoney', Filters::formatMoney(...));/** @phpstan-ignore-line */
		$this->getTemplate()->setFile(__DIR__ . '/templates/deliveryOptions.latte');

		$this->getTemplate()->render();
	}

	private function prepareDeliveryOptions(): array
	{
		$deliveryOptionsPickUp = [];

		$deliveryOptionPhysical = [];

		$deliveryOptionOnline = [];

		foreach ($this->deliveryMethodConfigurationRepository->getAvailable($this->mutation, $this->state, $this->priceLevel, $this->currency) as $deliveryOption) {

			$deliveryType = $deliveryOption->getDeliveryMethod()->getDeliveryType();

			if ($deliveryType->isOnline()) {
				$deliveryOptionOnline[] = $deliveryOption;
				continue;
			}

			if ($deliveryType->isStore() || $deliveryType->isPickUp()) {
				$deliveryOptionsPickUp[] = $deliveryOption;
				continue;
			}

			$deliveryOptionPhysical[] = $deliveryOption;
		}

		if ($this->variant->product->isElectronic) {
			return [$deliveryOptionOnline, []];
		}

		return [$deliveryOptionsPickUp, $deliveryOptionPhysical];
	}

	protected function createComponentCartFreeDelivery(): CartFreeDelivery
	{
		return $this->cartFreeDeliveryFactory->create(
			mutation: $this->mutation,
			state: $this->state,
			priceLevel: $this->priceLevel,
			userProvider: $this->userProvider,
		);
	}

	protected function createComponentBreadcrumb(): Breadcrumb
	{
		return $this->breadcrumbFactory->create($this->variant->product->getLocalization($this->mutation));
	}

}
