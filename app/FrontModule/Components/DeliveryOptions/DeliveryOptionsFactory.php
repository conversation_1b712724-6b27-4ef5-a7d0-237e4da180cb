<?php

namespace App\FrontModule\Components\DeliveryOptions;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\State\State;
use App\Model\Orm\User\UserProvider;
use App\Model\Security\User;
use Brick\Money\Currency;

interface DeliveryOptionsFactory
{

	public function create(ProductVariant $variant, Mutation $mutation, State $state, PriceLevel $priceLevel, Currency $currency, UserProvider $userProvider): DeliveryOptions;

}
