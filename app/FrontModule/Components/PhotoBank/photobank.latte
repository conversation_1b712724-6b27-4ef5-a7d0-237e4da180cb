{default $class = 'u-mb-md u-mb-2xl@md'}

<div n:class="$class">
	{include $templates.'/part/menu/tabs-btns.latte', items: ['Natáčení eventů','E-shop','Natáčení svatby','Livestream','Armáda','Zemědělství a lesnictví','Armáda','Zemědělství a lesnictví', 'Armáda 2']}

	<div class="grid grid--x-xs grid--x-sm@md grid--x-md@lg grid--y-xs grid--y-sm@md grid--y-md@lg">
		{default $references = [1, 2, 3, 4, 5, 6]} {* TODO *}
		{foreach $references as $reference}
			<div class="grid__cell size--6-12@md size--4-12@lg">
			{include $templates.'/part/box/photo.latte', class: false}
			</div>
		{/foreach}
	</div>
	TODO: paging component
</div>
