<?php declare(strict_types = 1);

namespace App\FrontModule\Components\PhotoBank;


use App\Components\VisualPaginator\VisualPaginator;
use App\Components\VisualPaginator\VisualPaginatorFactory;
use App\Model\Orm\CardPayment\CardPayment;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Order\OrderModel;
use App\Model\Orm\Order\OrderState;
use App\Model\Orm\Order\Payment\BankTransferPaymentInformation;
use App\Model\Orm\Order\Payment\CardPaymentInformation;
use App\Model\Orm\Orm;
use App\Model\Orm\PaymentMethod\BankTransfer;
use App\Model\Orm\PaymentMethod\Card;
use App\Model\Orm\PaymentMethod\PaymentMethodRegistry;
use App\Model\Orm\RoutableEntity;
use App\Model\Orm\User\User;
use App\Model\Setup;
use App\Model\TranslatorDB;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nextras\Orm\Collection\ICollection;

/**
 * @property-read DefaultTemplate $template
 */
final class PhotoBank extends UI\Control
{
	public function __construct(
		private readonly RoutableEntity $object,
		private readonly TranslatorDB $translator,
		private readonly Setup $setup,

	)
	{
	}

	public function beforeRender(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->mutation = $this->setup->mutation;
		$this->template->pages = $this->setup->mutation->pages;
		$this->template->object = $this->object;
		$this->template->translator = $this->translator;
		$this->template->templates = FE_TEMPLATE_DIR;

	}

	public function render(): void
	{
		$this->beforeRender();

		$this->template->render(__DIR__ . "/photobank.latte");
	}
}
