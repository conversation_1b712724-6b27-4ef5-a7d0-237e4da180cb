<?php declare(strict_types = 1);

namespace App\FrontModule\Components\StructuredData\Facade\Product;

use App\FrontModule\Components\StructuredData\Facade\StructuredDataFacadeInterface;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\PostType\Publisher\Model\Orm\PublisherLocalization\PublisherLocalizationModel;

/**
 * @method ProductLocalization getData()
 */
final class StructuredDataProductFacade extends StructuredDataProductDefaultFacade implements StructuredDataFacadeInterface
{

	public function createStructuredData(): array
	{
		return [
			'@context' => $this->createContext(),
			'@type' => $this->createType(),
			'name' => $this->createName(),
			'description' => $this->createDescription(),
			'image' => $this->createImage(),
			'brand' => $this->createBrand(),
			'releaseDate' => $this->createReleaseDate(),
			'offers' => $this->createOffers(),
			'aggregateRating' => $this->createAggregateRating(),
			'review' => $this->createReview(),
		];
	}

	private function createReleaseDate(): string|null
	{
		return $this->createPublishDate();
	}

	private function createBrand(): array
	{
		return [];
	}

	private function createDescription(): string|null
	{
		return $this->getData()->content ?: null;
	}

	protected function createType(): string
	{
		return 'Product';
	}

}
