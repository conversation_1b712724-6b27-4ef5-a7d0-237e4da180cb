<?php declare(strict_types = 1);

namespace App\FrontModule\Components\StructuredData\Facade\Product;

use App\FrontModule\Components\StructuredData\Facade\StructuredDataFacadeInterface;
use App\Model\Orm\ProductLocalization\ProductLocalization;

/**
 * @method ProductLocalization getData()
 */
final class StructuredDataAudioBookFacade extends StructuredDataProductDefaultFacade implements StructuredDataFacadeInterface
{

	public function createStructuredData(): array
	{
		return [
			'@context' => $this->createContext(),
			'@type' => $this->createType(),
			'image' => $this->createImage(),
			'name' => $this->createName(),
			'datePublished' => $this->createPublishDate(),
			'isbn' => $this->createIsbn(),
			'numberOfPages' => $this->createNumberOfPages(),
			'inLanguage' => $this->createLanguage(),
			'offers' => $this->createOffers(),
			'aggregateRating' => $this->createAggregateRating(),
			'review' => $this->createReview(),
		];
	}


	protected function createType(): string
	{
		return 'Audiobook';
	}



}
