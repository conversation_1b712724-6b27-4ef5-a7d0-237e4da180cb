<?php declare(strict_types = 1);

namespace App\FrontModule\Components\StructuredData\Facade;

use App\Model\Link\LinkFactory;
use App\Model\Orm\Orm;
use App\Model\Setup;

interface StructuredDataFacadeInterface
{

	public function getStructuredData(): string;

	public static function instance(): StructuredDataFacadeInterface;

	public function setData(mixed $data): StructuredDataFacadeInterface;

	public function setOrm(Orm $orm): StructuredDataFacadeInterface;

	public function setLinkFactory(LinkFactory $linkFactory): StructuredDataFacadeInterface;

}
