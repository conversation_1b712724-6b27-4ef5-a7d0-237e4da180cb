<?php declare(strict_types = 1);

namespace App\FrontModule\Components\StructuredData\Facade;

use App\Model\Link\LinkFactory;
use App\Model\Orm\Orm;
use App\Model\Setup;
use Exception;
use Nette\Utils\Json;

abstract class StructuredDataFacade implements StructuredDataFacadeInterface
{

	private mixed $data;

	private array $structuredData = [];

	private LinkFactory $linkFactory;

	private Orm $orm;

	private function __construct()
	{
	}

	final public static function instance(): StructuredDataFacadeInterface
	{
		return new static(); // @phpstan-ignore-line
	}

	abstract protected function validateData(mixed $data): mixed;

	final public function getStructuredData(): string
	{
		foreach ($this->createStructuredData() as $propertyName => $value) {

			if ($value === null) {
				continue;
			}

			if ($value === []) {
				continue;
			}

			if ($value === '') {
				continue;
			}

			if (isset($this->structuredData[$propertyName])) {
				continue;
			}

			$this->structuredData[$propertyName] = $value;
		}

		return Json::encode($this->structuredData, pretty: true);
	}

	protected function createStructuredData(): array
	{
		throw new Exception();
	}

	final protected function getData(): mixed
	{
		return $this->validateData($this->data);
	}

	final public function setData(mixed $data): static
	{
		$this->data = $data;
		return $this;
	}

	final public function setLinkFactory(LinkFactory $linkFactory): StructuredDataFacadeInterface
	{
		$this->linkFactory = $linkFactory;
		return $this;
	}

	final protected function getLinkFactory(): LinkFactory
	{
		return $this->linkFactory;
	}

	public function setOrm(Orm $orm): StructuredDataFacadeInterface
	{
		$this->orm = $orm;
		return $this;
	}

	public function getOrm(): Orm
	{
		return $this->orm;
	}

	protected function createContext(): string
	{
		return 'https://schema.org';
	}

}
