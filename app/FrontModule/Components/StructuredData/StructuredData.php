<?php declare(strict_types = 1);

namespace App\FrontModule\Components\StructuredData;

use App\FrontModule\Components\StructuredData\Facade\StructuredDataFacadeInterface;
use Nette\Application\UI\Control;

final class StructuredData extends Control
{

	public function __construct(
		private readonly StructuredDataFacadeInterface $structuredDataFacade
	)
	{
	}

	final public function render(): void
	{
		$this->getTemplate()->structuredData = $this->structuredDataFacade->getStructuredData();

		$this->getTemplate()->setFile(__DIR__ . '/templates/structuredData.latte');
		$this->getTemplate()->render();
	}

}
