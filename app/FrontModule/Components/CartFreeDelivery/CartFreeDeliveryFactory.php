<?php

declare(strict_types=1);

namespace App\FrontModule\Components\CartFreeDelivery;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;
use App\Model\Orm\User\UserProvider;
use Nette\Security\User;

interface CartFreeDeliveryFactory
{
	public function create(
		Mutation $mutation,
		State $state,
		PriceLevel $priceLevel,
		UserProvider $userProvider,
	): CartFreeDelivery;

}
