<?php declare(strict_types = 1);

namespace App\FrontModule\Components\OpenGraph\Mappers;

use App\FrontModule\Components\OpenGraph\DTO\CalendarDto;
use App\FrontModule\Components\OpenGraph\DTO\CategoryDto;
use App\FrontModule\Components\OpenGraph\DTO\DefaultDto;
use App\FrontModule\Components\OpenGraph\DTO\OpenGraphDtoInterface;
use App\FrontModule\Components\OpenGraph\DTO\ProductDto;
use App\FrontModule\Components\OpenGraph\DTO\SharedLibraryDto;
use App\FrontModule\Components\OpenGraph\Enums\OpenGraphMappingType;
use App\FrontModule\Components\OpenGraph\DTO\ArticleDto;

readonly final class MapperDto
{

	public function __construct(
		private ProductDto $productDto,
		private CategoryDto $categoryDto,
		private DefaultDto $defaultDto,
		private SharedLibraryDto $sharedLibraryDto,
		private ArticleDto $articleDto,
		private CalendarDto $calendarDto,
	)
	{
	}

	final public function map(OpenGraphMappingType $graphMappingType): OpenGraphDtoInterface
	{
		return match ($graphMappingType) {
			OpenGraphMappingType::product => $this->productDto,
			OpenGraphMappingType::category => $this->categoryDto,
			OpenGraphMappingType::default => $this->defaultDto,
			OpenGraphMappingType::article => $this->articleDto,
			OpenGraphMappingType::calendar => $this->calendarDto,
			OpenGraphMappingType::sharedLibrary => $this->sharedLibraryDto,
		};
	}

}
