<?php declare(strict_types = 1);

namespace App\FrontModule\Components\OpenGraph;

use App\FrontModule\Components\OpenGraph\Enums\OpenGraphMappingType;
use App\FrontModule\Components\OpenGraph\Enums\OpenGraphType;
use App\FrontModule\Presenters\BasePresenter;
use App\Model\ConfigService;
use App\Model\Setup;
use App\Model\TranslatorDB;
use Nette\Application\UI\Control;

/**
 * @property-read BasePresenter $presenter
 */
final class OpenGraph extends Control
{

	public function __construct(
		private readonly OpenGraphType $openGraphType,
		private readonly Setup $setup,
		private readonly mixed $entity,
		private readonly Resolver $resolver,
		private readonly TranslatorDB $translator,
		private readonly ConfigService $configService,
	)
	{
	}

	final public function render(): void
	{
		$openGraphMappingType = OpenGraphMappingType::resolve($this->openGraphType, $this->entity);

		$this->getTemplate()->dto = $this->resolver->getDto($openGraphMappingType)
			->setData($this->entity)
			->setSetup($this->setup)
			->setConfig($this->configService->get('openGraph'))
			->setPresenter($this->presenter)
			->init();
		$this->getTemplate()->setTranslator($this->translator);
		$this->getTemplate()->setFile($this->resolver->getTemplate($openGraphMappingType));

		$this->getTemplate()->render();
	}

}
