<?php declare(strict_types = 1);

namespace App\FrontModule\Components\OpenGraph\Enums;

use App\FrontModule\Components\OpenGraph\DTO\Util\ProductData;
use App\PostType\Calendar\Model\Orm\CalendarLocalization;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Blog\Model\Orm\BlogLocalization;

enum OpenGraphMappingType: string
{

	case product = 'product';
	case article = 'article';
	case calendar = 'calendar';
	case category = 'category';
	case default = 'default';
	case sharedLibrary = 'sharedLibrary';

	final public static function resolve(OpenGraphType $openGraphType, mixed $data): self
	{
		return match ($openGraphType) {
			OpenGraphType::default => self::resolveDefault($data),
		};
	}

	private static function resolveDefault(mixed $data): self
	{
		if ($data instanceof ProductData) {
			return self::product;
		}

		if ($data instanceof CommonTree && $data->uid === Tree::UID_SHARED_LIBRARY) {
			return OpenGraphMappingType::sharedLibrary;
		}

		if ($data instanceof CatalogTree) {
			return OpenGraphMappingType::category;
		}

		if ($data instanceof BlogLocalization) {
			return OpenGraphMappingType::article;
		}

		if ($data instanceof CalendarLocalization) {
			return OpenGraphMappingType::calendar;
		}

		return OpenGraphMappingType::default;
	}

}
