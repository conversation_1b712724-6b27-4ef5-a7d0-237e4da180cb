<?php declare(strict_types = 1);

namespace App\FrontModule\Components\OpenGraph\DTO;

use App\FrontModule\Presenters\BasePresenter;
use App\Model\Setup;

interface OpenGraphDtoInterface
{

	public function setData(mixed $data): OpenGraphDtoInterface;
	public function setSetup(Setup $setup): OpenGraphDtoInterface;
	public function setConfig(array $config): OpenGraphDtoInterface;

	public function setPresenter(BasePresenter $presenter): OpenGraphDtoInterface;

	public function init(): OpenGraphDtoInterface;

}
