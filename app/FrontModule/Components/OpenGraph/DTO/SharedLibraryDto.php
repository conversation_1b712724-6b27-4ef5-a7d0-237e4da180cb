<?php declare(strict_types = 1);

namespace App\FrontModule\Components\OpenGraph\DTO;

use App\FrontModule\Components\MyLibrary\MyLibrary;
use App\PostType\Page\Model\Orm\CommonTree;

/**
 * @method CommonTree getData()
 */
final class SharedLibraryDto extends OpenGraphDto implements OpenGraphDtoInterface
{

	private MyLibrary $myLibraryControl;

	public function __construct()
	{
	}

	public function init(): self
	{
		$this->myLibraryControl = $this->getPresenter()->getComponent('sharedLibrary');

		return $this;
	}

	public function getTitle(): string
	{
		return ($this->getData()->nameTitle ?? $this->getData()->name ?? null) . ' ' . $this->myLibraryControl->getMyLibrary()->user->name;
	}


	public function getUrl(): string|null
	{
		try {
			return $this->myLibraryControl->createShareLink();
		} catch (\Throwable $exception) {
			//TODO just try
		}

		return '';
	}

	public function getDescription(): string|null
	{
		return $this->getData()->description ?? null;
	}


	public function getImage(): string|null
	{
		return $this->getData()->mutation->getOgImageSharedLibrary('lg') ?? null;
	}

}
