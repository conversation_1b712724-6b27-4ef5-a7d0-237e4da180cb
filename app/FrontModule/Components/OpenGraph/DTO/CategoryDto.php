<?php declare(strict_types = 1);

namespace App\FrontModule\Components\OpenGraph\DTO;

use App\Model\CustomField\LazyValue;
use App\Model\Link\LinkFactory;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\Model\Orm\ImageEntity;

/**
 * @method CatalogTree getData()
 */
final class CategoryDto extends OpenGraphDto implements OpenGraphDtoInterface
{

	public function __construct(
		private readonly LinkFactory $linkFactory,
	)
	{
	}
	public function getTitle(): string|null
	{
		return $this->getData()->nameTitle ?: null;
	}

	public function getUrl(): string
	{
		return $this->linkFactory->linkTranslateToNette($this->getData(), ['mutation' => $this->getData()->mutation]);
	}

	public function getDescription(): string
	{
		if (!empty($this->getData()->cf->catalog_header->content)) {
			$content = $this->getData()->cf->catalog_header->content;
		} elseif (!empty($this->getData()->description)) {
			$content = $this->getData()->description;
		} else {
			$content = $this->getData()->annotation;
		}
		return $this->truncate($content);
	}

	protected function getImageEntity(): null|ImageEntity
	{
		if (isset($this->getData()->cf->base->crossroadImage) && $this->getData()->cf->base->crossroadImage instanceof LazyValue) {
			$entity = $this->getData()->cf->base->crossroadImage->getEntity();
			return $entity instanceof ImageEntity ? $entity : null;
		}
		return null;
	}

}
