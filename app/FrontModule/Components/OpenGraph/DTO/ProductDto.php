<?php declare(strict_types = 1);

namespace App\FrontModule\Components\OpenGraph\DTO;

use App\FrontModule\Components\OpenGraph\DTO\Util\ProductData;
use App\Model\DTO\Product\ProductDtoProvider;
use App\Model\Link\LinkFactory;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ParameterValue\ParameterValueRepository;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ImageEntity;
use App\FrontModule\Components\OpenGraph\Enums\OpenGraphMappingType;

/**
 * @method ProductData getData()
 */
final class ProductDto extends OpenGraphDto implements OpenGraphDtoInterface
{

	public function __construct(
		private readonly ProductDtoProvider $productDtoProvider,
		private readonly LinkFactory $linkFactory,
		private readonly ParameterValueRepository $parameterValueRepository,
	)
	{
	}

	final public function getLanguages(): array
	{
		$languages = $this->parameterValueRepository->findValues($this->getProduct(), Parameter::UID_LANGUAGE);
		$texts = [];
		foreach ($languages as $language) {
			$texts[] = $language->internalValue;
		}

		return $texts;
	}

	public function getTitle(): string
	{
		return $this->getData()->productLocalization->getNameTitle();
	}

	public function getType(): string
	{
		return OpenGraphMappingType::product->value;
	}

	public function getUrl(): string
	{
		return $this->linkFactory->linkTranslateToNette(
			$this->getData()->productLocalization,
			[
				'mutation' => $this->getMutation(),
				'v' => $this->getVariant()->id,
			]
		);
	}

	public function getDescription(): string|null
	{
		return $this->getData()->productLocalization->getDescription();
	}

	public function getImage(): string|null
	{
		if ($this->getProductCacheDto()->firstImageObjectMd !== null) {
			return $this->getMutation()->getBaseUrlWithPrefix() . $this->getProductCacheDto()->firstImageObjectMd->src;
		}

		if (!isset($this->getProduct()->firstImage->url)) {
			return null;
		}

		return $this->getMutation()->getBaseUrlWithPrefix() . $this->getProduct()->firstImage->url;
	}

	public function getImageAlt(): string|null
	{
		return $this->getProductCacheDto()->firstImageAlt ?? $this->getProduct()->firstImage?->getAlt($this->getMutation()) ?? null;
	}

	private function getProductCacheDto(): \App\Model\DTO\Product\ProductDto
	{
		return $this->productDtoProvider->get($this->getProduct(), $this->getVariant());
	}

	public function getProduct(): Product
	{
		return $this->getData()->productLocalization->product;
	}

	/**
	 * @return mixed
	 */
	public function getMutation()
	{
		return $this->getData()->productLocalization->mutation;
	}

	/**
	 * @return mixed
	 */
	public function getVariant(): mixed
	{
		return $this->getData()->variant;
	}

	protected function getImageEntity(): null|ImageEntity
	{
		return $this->getProduct()->firstImage;
	}

}
