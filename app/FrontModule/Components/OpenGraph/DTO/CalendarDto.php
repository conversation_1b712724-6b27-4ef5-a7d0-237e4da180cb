<?php declare(strict_types = 1);

namespace App\FrontModule\Components\OpenGraph\DTO;

use App\FrontModule\Components\OpenGraph\Enums\OpenGraphMappingType;
use App\Model\Link\LinkFactory;
use App\Model\Orm\ImageEntity;

final class CalendarDto extends OpenGraphDto
{

	public function __construct(
		private readonly LinkFactory $linkFactory,
	)
	{
	}

	public function getTitle(): string|null
	{
		return $this->getData()->nameTitle ?? null;
	}

	public function getType(): string
	{
		return OpenGraphMappingType::article->value;
	}


	public function getUrl(): string|null
	{
		try {
			return $this->linkFactory->linkTranslateToNette($this->getData(), ['mutation' => $this->getData()->mutation]);
		} catch (\Throwable $exception) {
			//TODO just try
		}

		return null;
	}

	public function getDescription(): string
	{
		if (!empty($this->getData()->cf->settings->annotation)) {
			$content = $this->getData()->cf->settings->annotation;
		} elseif (!empty($this->getData()->description)) {
			$content = $this->getData()->description;
		} else {
			$content = $this->getData()->annotation;
		}
		return $this->truncate($content);
	}

	protected function getImageEntity(): null|ImageEntity
	{
		$cf = $this->getData()->cf->settings;
		return isset($cf->mainImage) ? $cf->mainImage->getEntity() ?? null : null;
	}

}
