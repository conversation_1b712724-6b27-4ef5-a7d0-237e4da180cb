<?php declare(strict_types = 1);

namespace App\FrontModule\Components\OpenGraph\DTO;

use App\Model\Link\LinkFactory;

final class DefaultDto extends OpenGraphDto
{

	public function __construct(
		private readonly LinkFactory $linkFactory,
	)
	{
	}

	public function getTitle(): string|null
	{
		if (!empty($this->getData()->nameTitle)) {
			return $this->getData()->nameTitle;
		}
		if (!empty($this->getData()->title)) {
			return $this->getData()->title;
		}

		return null;
	}


	public function getUrl(): string
	{
		try {
			return $this->linkFactory->linkTranslateToNette($this->getData(), ['mutation' => $this->getData()->mutation]);
		} catch (\Throwable $exception) {
			//TODO just try
		}

		return '';
	}

	public function getDescription(): string
	{
		if (!empty($this->getData()->description)) {
			$content = $this->getData()->description;
		} else {
			$content = $this->getData()->annotation;
		}
		return $this->truncate($content);
	}

}
