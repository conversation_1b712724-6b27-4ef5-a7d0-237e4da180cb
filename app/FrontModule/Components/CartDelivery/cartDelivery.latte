<div class="u-maw-11-12 u-mx-auto">
	<h1 class="u-vhide">{$object->name}</h1>
	{include $templates.'/part/box/order-steps.latte', class: 'tw-pt-[2.8rem] lg:tw-pt-[5.2rem] tw-mb-[0.8rem] md:tw-mb-[1.2rem]', currentStep: 2}

	{snippet step1Content}
		<div n:ifcontent class="u-mb-last-0 u-mb-sm u-mb-md@lg">
			<p n:foreach="$flashes as $flash" class="message message--{$flash->type} u-mb-xs">
				<span class="message__emoji">{if $flash->type == 'ok'}✅{else}⚠️{/if}</span>
				<span class="message__content">
					{$flash->message}
				</span>
			</p>
		</div>

		<div class="b-layout-basket b-layout-basket--step1">
			<form n:name="form" class="f-basket" data-naja data-naja-loader="body" data-naja-force-redirect>
				<div class="b-layout-basket__main u-mb-last-0">
					<div class="f-basket__head">
						<h2 class="f-basket__title h3">
							1. {_"cart_delivery_title"}
						</h2>
					</div>
					{* {include $templates.'/part/form/part/country.latte', class: 'u-mb-0'} *}
					{include $templates.'/part/form/delivery.latte', class: false, form: $form}

					<div class="f-basket__head">
						<h2 class="f-basket__title h3">
							2. {_"cart_payment_title"}
						</h2>
					</div>
					{include $templates.'/part/form/payment.latte', class: false, form: $form}
				</div>

				{include $templates.'/part/box/cart-summary.latte', class: 'b-layout-basket__side'}

				{* Pokračovat *}
				<p class="b-layout-basket__next">
					{default App\Model\Orm\Order\Delivery\OrderDelivery $delivery = $shoppingCart->getDelivery()}
					{default App\Model\Orm\Order\Payment\OrderPayment $payment = $shoppingCart->getPayment()}
					{php $continueEnabled = ($delivery ?? false) && ($payment ?? false)}

					<span n:if="!$continueEnabled" class="f-basket__bubble">
						<span class="item-icon">
							{('info')|icon, 'item-icon__icon'}
							<span class="item-icon__text">
								{_"msg_pick_delivery_payment"}
							</span>
						</span>
					</span>
					<button n:name="next" type="submit" class="btn btn--xl btn--secondary btn--loader" {if !$continueEnabled}disabled{/if}>
						<span class="btn__text">
							<span class="btn__inner">
								{_"btn_order_continue_delivery"}
								{('arrow-right-thin')|icon, 'btn__icon'}
							</span>
						</span>
					</button>
				</p>

				{* Zpět do košíku *}
				<p class="b-layout-basket__prev">
					<a href="{plink $pages->cart}" class="btn btn--bd">
						<span class="btn__text">
							{_"btn_back_cart"}
						</span>
					</a>
				</p>
			</form>
		</div>

	{/snippet}
</div>
