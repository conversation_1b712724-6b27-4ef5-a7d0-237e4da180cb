<?php declare(strict_types = 1);

namespace App\FrontModule\Components\CartDelivery;

use App\FrontModule\Components\Cart\Cart;
use App\FrontModule\Presenters\BasePresenter;
use App\Model\DTO\Product\ProductDtoProvider;
use App\Model\Orm\DeliveryMethod\DeliveryMethodConfiguration;
use App\Model\Orm\DeliveryMethod\DeliveryMethodConfigurationRepository;
use App\Model\Orm\DeliveryMethod\Electronic;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PaymentMethod\Certificate;
use App\Model\Orm\PaymentMethod\PaymentMethodConfiguration;
use App\Model\Orm\PaymentMethod\PaymentMethodConfigurationRepository;
use App\Model\Orm\Price;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\State\State;
use App\Model\Pages;
use App\Model\ShoppingCart\ShoppingCartInterface;
use App\Model\TranslatorDB;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Forms\Controls\SubmitButton;
use Nette\Utils\ArrayHash;
use Tracy\Debugger;

/**
 * @property-read DefaultTemplate $template
 * @property-read BasePresenter $presenter
 * @property-read UI\Control $parent
 */
final class CartDelivery extends UI\Control
{

	private Pages $pages;

	private Mutation $mutation;

	private State $state;

	private PriceLevel $priceLevel;

	/** @var DeliveryMethodConfiguration[] */
	private array $deliveryMethods = [];

	/** @var PaymentMethodConfiguration[] */
	private array $paymentMethods = [];

	public function __construct(
		private readonly TranslatorDB $translator,
		private readonly ShoppingCartInterface $shoppingCart,
		private readonly DeliveryMethodConfigurationRepository $deliveryMethodConfigurationRepository,
		private readonly PaymentMethodConfigurationRepository $paymentMethodConfigurationRepository,
		//		private readonly StateRepository $stateRepository,
		private readonly ProductDtoProvider $productDtoProvider,
	)
	{
		$this->onAnchor[] = $this->init(...);
		$this->onAnchor[] = $this->initMethods(...);
		$this->onAnchor[] = $this->initActive(...);
	}

	private function init(): void
	{
		assert($this->parent instanceof Cart);

		$this->template->setTranslator($this->translator);
		$this->template->setParameters($this->parent->getTemplateParameters());

		$this->pages = $this->template->pages;
		$this->mutation = $this->template->mutation;
		$this->priceLevel = $this->template->priceLevel;

//		$this->countries = $this->mutation->states->toCollection();
		//$this->countrySelect = $this->countries->count() >= 1;
	}

	private function initMethods(): void
	{
		assert($this->parent instanceof Cart);

		$this->state = $this->shoppingCart->getCountry();
		$this->template->state = $this->state;

		$this->parent->refreshCart();
		$this->template->flashes = array_merge($this->template->flashes, $this->parent->template->flashes);

		$this->loadDeliveryMethods();
		$this->loadPaymentMethods();
	}

	private function initActive(): void
	{
		if (!$this->presenter->isAjax()) {
			if (!$this->shoppingCart->getDelivery()?->deliveryMethod->isAllowed($this->priceLevel, $this->state, $this->shoppingCart->getCurrency(), $this->shoppingCart->getTotalWeight())) {
				$this->shoppingCart->setDelivery(null);
			}
			if (!$this->shoppingCart->getPayment()?->paymentMethod->isAllowed($this->priceLevel, $this->state, $this->shoppingCart)) {
				$this->shoppingCart->setPayment(null);
			}

			$this->setDefaultDelivery();
			$this->loadPaymentMethods();
			$this->setDefaultPayment();
		}

//		if ($this->countrySelect) {
//			$this['form']['country']->setDefaultValue($this->preselectCountry());
//		}
		$activeDeliveryMethodId = isset($this->deliveryMethods[$this->shoppingCart->getDelivery()?->deliveryMethod->id]) ? $this->shoppingCart->getDelivery()?->deliveryMethod->id : null;
		$this['form']['deliveryMethod']->setDefaultValue($activeDeliveryMethodId);
		$activePaymentMethodId = isset($this->paymentMethods[$this->shoppingCart->getPayment()?->paymentMethod->id]) ? $this->shoppingCart->getPayment()?->paymentMethod->id : null;
		$this['form']['paymentMethod']->setDefaultValue($activePaymentMethodId);
	}


	private function loadDeliveryMethods(): void
	{
		$this->deliveryMethods = $this->deliveryMethodConfigurationRepository->getAvailable($this->mutation, $this->state, $this->priceLevel, $this->shoppingCart->getCurrency())->fetchPairs('id');
		foreach ($this->deliveryMethods as $id => $deliveryMethod) {
			try {
				if (!$deliveryMethod->getDeliveryMethod()->isAvailableForOrder($this->shoppingCart)) {
					unset($this->deliveryMethods[$id]);
				}
				$deliveryMethod->getDeliveryMethod();
			} catch (\Throwable $e) {
				unset($this->deliveryMethods[$id]);
				Debugger::log($e, Debugger::EXCEPTION);
			}
		}
		$this['form']['deliveryMethod']->setItems($this->deliveryMethods);
		$this['form']['pickupPointId']->setDefaultValue($this->shoppingCart->getPickupPointId());

		if (($selectedDeliveryMethodId = $this->shoppingCart->getDelivery()?->deliveryMethod->id ?? null) !== null && !isset($this->deliveryMethods[$selectedDeliveryMethodId])) {
			$this->shoppingCart->setDelivery(null);
			$this->shoppingCart->setPayment(null);
		}

		if ($this->shoppingCart->hasElectronicProduct(strict: true) && ($electronicDeliveryMethodConfiguration = $this->deliveryMethodConfigurationRepository->getBy(['deliveryMethodUniqueIdentifier' => Electronic::ID])) !== null) {
			$this->shoppingCart->setDelivery($electronicDeliveryMethodConfiguration);
		}
	}

	private function loadPaymentMethods(): void
	{
		if ($this->shoppingCart->hasDelivery()) {
			$this->paymentMethods = $this->paymentMethodConfigurationRepository->getAvailableByDelivery($this->shoppingCart->getDelivery()->deliveryMethod, $this->mutation, $this->state, $this->priceLevel, $this->shoppingCart->getCurrency())->fetchPairs('id');
		} else {
			$this->paymentMethods = $this->paymentMethodConfigurationRepository->getAvailable($this->mutation, $this->state, $this->priceLevel, $this->shoppingCart->getCurrency())->fetchPairs('id');
		}

		foreach ($this->paymentMethods as $id => $paymentMethod) {
			if (!$paymentMethod->getPaymentMethod()->isAvailableForOrder($this->shoppingCart)) {
				unset($this->paymentMethods[$id]);
			}
		}

		if ($this->shoppingCart->getTotalPriceWithDeliveryVat()->isLessThanOrEqualTo(0)) {
			$this->shoppingCart->setPayment(null);
			$this->paymentMethods = [];
			if (($pm = $this->paymentMethodConfigurationRepository->getBy(['mutation' => $this->mutation, 'paymentMethodUniqueIdentifier' => Certificate::ID, 'public' => true])) !== null) {
				assert($pm instanceof PaymentMethodConfiguration);
				$this->paymentMethods[$pm->id] = $pm;
				$this->shoppingCart->setPayment($pm);
			}
		}

		foreach ($this->paymentMethods as $id => $paymentMethod) {
			try {
				$paymentMethod->getPaymentMethod();
			} catch (\Throwable $e) {
				unset($this->paymentMethods[$id]);
				Debugger::log($e, Debugger::EXCEPTION);
			}
		}

		$this['form']['paymentMethod']->setItems($this->paymentMethods);
	}


	private function resetPayment(): void
	{
		if ($this->shoppingCart->hasPayment() && !isset($this->paymentMethods[$this->shoppingCart->getPayment()->paymentMethod->id])) {
			$this->shoppingCart->setPayment(null);
		}
	}

	private function setDefaultPayment(bool $withFormValueSet = false): void
	{
		/*return; // DK-1127 Defaultně nebude předvybraná žádná platební ani dopravní metoda
		if ( ! $this->shoppingCart->hasPayment() && $this->paymentMethods !== []) {
			foreach ($this->paymentMethods as $paymentMethod) {
				if ($paymentMethod->isAllowed($this->priceLevel, $this->state, $this->shoppingCart)) {
					$this->shoppingCart->setPayment($paymentMethod);
					if ($withFormValueSet) {
						$this['form']['paymentMethod']->setValue($paymentMethod->id);
					}
				}
			}
		}*/
	}

	private function setDefaultDelivery(): void
	{
		/*return; // DK-1127 Defaultně nebude předvybraná žádná platební ani dopravní metoda
		if ( ! $this->shoppingCart->hasDelivery() && $this->deliveryMethods !== []) {
			foreach ($this->deliveryMethods as $deliveryMethod) {
				if ($deliveryMethod->isAllowed($this->priceLevel, $this->state, $this->shoppingCart->getCurrency(), $this->shoppingCart->getTotalWeight())) {
					$this->shoppingCart->setDelivery($deliveryMethod);
					break;
				}
			}
		}*/
	}

	private function beforeRender(): void
	{
		//$this->template->countrySelect = $this->countrySelect;
		// $this->template->selectedCountry = $this->shoppingCart->getCountry();
		$this->template->deliveryMethods = $this->deliveryMethods;
		$this->template->paymentMethods = $this->paymentMethods;

		$this->template->productDtoProviderCallback = function (Product $product, ProductVariant $variant) {
			return $this->productDtoProvider->get($product, $variant);
		};

		$this->template->setParameters([
			'totalPrice' => $this->shoppingCart->getTotalPrice(),
			'totalPriceVat' => $this->shoppingCart->getTotalPriceVat(),
			'totalPriceWithDelivery' => Price::from($this->shoppingCart->getTotalPriceWithDelivery(precision: 2))->asMoney(),
			'totalPriceWithDeliveryVat' => Price::from($this->shoppingCart->getTotalPriceWithDeliveryVat(precision: 2))->asMoney(),
		]);
	}

	public function render(): void
	{
		$this->beforeRender();
		$this->template->render(__DIR__ . '/cartDelivery.latte');
	}

	protected function createComponentForm(): UI\Form
	{
		$form = new UI\Form();
		$form->setTranslator($this->translator);

//		if ($this->countrySelect) {
//			$form->addHidden('country', $this->preselectCountry());
////			$form->addSelect('country', 'delivery_country', $this->countries->fetchPairs('id', 'name'));
//		}

		$form->addRadioList('deliveryMethod', 'deliveryMethod', $this->deliveryMethods);
		$form->addRadioList('paymentMethod', 'paymentMethod', $this->paymentMethods);

		$form->addText('pickupPointId', 'pickupPointId')->setNullable()->addCondition($form::Filled, true)->addRule($form::Integer, 'integer_only');

		$form->addSubmit('next', 'btn_order_continue');

		$form->onValidate[] = $this->formValidate(...);
		$form->onSuccess[] = $this->formSuccess(...);
		$form->onError[] = $this->formError(...);

		return $form;
	}

	private function formValidate(UI\Form $form, ArrayHash $values): void
	{
		if ($form->isSubmitted() instanceof SubmitButton && $form->isSubmitted()->getName() === 'next') {
			if (!$this->shoppingCart->hasDelivery()) {
				bd('a');
				$form->addError('order_select_delivery_method');
			}

			if (!$this->shoppingCart->hasPayment()) {
				bd('b');
				$form->addError('order_select_payment_method');
			}

			if (isset($this->deliveryMethods[$values->deliveryMethod]) && $this->deliveryMethods[$values->deliveryMethod]->getDeliveryMethod()->supportsPickup() && $values->pickupPointId === null) {
				bd('c');
				$form->addError('select_pickup_point');
			}
		}
	}

	private function formSuccess(UI\Form $form, ArrayHash $values): void
	{
//		$skipOthers = false;
		$isNext = $form->isSubmitted() instanceof SubmitButton && $form->isSubmitted()->getName() === 'next';

		if ($isNext) {
			$this->presenter->redirect($this->pages->step2);
		}

//		if ($this->countrySelect && $values->country !== $this->shoppingCart->getCountry()->id) {
//			$country = $this->stateRepository->getById($values->country);
//			if ($country !== null) {
//				$this->shoppingCart->setCountry($country);
//				$this->initMethods();
//				$this->resetDelivery();
//				$this->resetPayment();
//			}
//			$skipOthers = true;
//		}

//		if (!$skipOthers) {
			if ($values->deliveryMethod !== null && $this->shoppingCart->getDelivery()?->deliveryMethod->id !== $values->deliveryMethod) {
				$dm = $this->deliveryMethods[$values->deliveryMethod];
				$this->shoppingCart->setDelivery($dm);
				if ($dm->getDeliveryMethod()->supportsPickup()) {
					$this->shoppingCart->setPickupPoint($values->pickupPointId);
				}
				$this->initMethods();
				$this->resetPayment();
				$this->setDefaultPayment(withFormValueSet: true);
			}

			if ($values->paymentMethod !== null && $this->shoppingCart->getPayment()?->paymentMethod->id !== $values->paymentMethod && ($paymentMethod = $this->paymentMethods[$values->paymentMethod] ?? null) !== null) {
				$this->shoppingCart->setPayment($paymentMethod);
			}
//		}

		$this->redrawControl();
		$this->presenter->redrawControl('tags');
	}

	private function formError(UI\Form $form): void
	{
		bdump($form->getErrors());
		foreach ($form->getErrors() as $error) {
			$this->flashMessage($error, 'error');
		}

		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function handleChangeDelivery(): void
	{
		$this->shoppingCart->setDelivery(null);
		$this->shoppingCart->setPayment(null);
		$this->initActive();

		if (!$this->presenter->isAjax()) {
			$this->presenter->redirect($this->pages->step1);
		}
		$this->redrawControl();
	}

	public function handleChangePayment(): void
	{
		$this->shoppingCart->setPayment(null);
		$this->initActive();

		if ( ! $this->presenter->isAjax()) {
			$this->presenter->redirect($this->pages->step1);
		}
		$this->redrawControl();
	}

}
