<?php declare(strict_types = 1);

namespace App\FrontModule\Components\Categories;

use App\Model\ElasticSearch\Product\Repository;
use App\Model\MenuService;
use App\Model\Orm\Mutation\Mutation;
use App\Model\TranslatorDB;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\TreeRepository;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;


/**
 * @property-read DefaultTemplate $template
 */
final class Categories extends UI\Control
{
	private ?array $categoriesProductCount = null;
	private ?array $menu = null;
	private Mutation $mutation;
	public function __construct(
		private readonly CatalogTree $catalogTree,
		private readonly TranslatorDB $translator,
		private readonly MenuService $menuService,
		private readonly Repository $esProductRepository,
		private readonly TreeRepository $treeRepository,
	) {

		$this->onAnchor[] = $this->init(...);
	}

	private function init(): void
	{
		$this->mutation = $this->catalogTree->getMutation();
		$this->fillCategoriesProductCount();;
	}

	public function render() : void
	{
		$this->template->setTranslator($this->translator);
		$this->template->siblings = $this->getMenu();
		$this->template->categoriesProductCount = $this->categoriesProductCount;
		$this->template->templates = FE_TEMPLATE_DIR;
		$this->template->render(__DIR__ . '/categories.latte');
	}
	public function renderCrossroad() : void
	{
		$this->template->setTranslator($this->translator);

		$this->template->object = $this->catalogTree;
		$this->template->crossroad = $this->catalogTree->crossroad;
		$this->template->categoriesProductCount = $this->categoriesProductCount;
		$this->template->templates = FE_TEMPLATE_DIR;
		$this->template->render(__DIR__ . '/crossroad.latte');
	}

	public function fillCategoriesProductCount(): void
	{
		$siblingsInPathIds = $this->getSiblingsInPathIds();
		$this->categoriesProductCount = $this->esProductRepository->getProductCountsForCategories($this->mutation, $siblingsInPathIds);
	}

	private function getSiblingsInPathIds(): array
	{
		$completePathIds = $this->catalogTree->path;
		$completePathIds[] = $this->catalogTree->id;
		// remove unnecessary root id
		unset($completePathIds[0]);

		return array_merge($completePathIds, $this->treeRepository->findChildIds($completePathIds));
	}

	public function getMenu(): array
	{
		if ($this->menu === null) {
			$this->menu = $this->menuService->getMenu($this->mutation, $this->catalogTree->getParentIdByLevel(2), $this->catalogTree);
		}
		return $this->menu;
	}

}
