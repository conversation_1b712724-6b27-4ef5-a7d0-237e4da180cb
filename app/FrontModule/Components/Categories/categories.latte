{default $class = 'u-d-n u-d-b@lg'}

{define #siblings}
	{default $siblings = []}
	{default $nestLvl = 2}
	{php $submenu = $node->submenu ?? false}


	{foreach $siblings as $node}
		{var $hasSubmenu = $node->hasSubmenu}
		<li id="m-categories-{$node->page->id}" n:if="isset($categoriesProductCount[$node->page->id]) && $categoriesProductCount[$node->page->id] > 0" class="m-categories__item">
			<a href="{plink $node->page, page: null}" n:class="m-categories__link, $node->selected ? is-active, $hasSubmenu ? m-categories__link--w-submenu">
				{if $hasSubmenu && $nestLvl <= 4}
					{('angle-right-bold')|icon, 'item-icon__icon'}
				{/if}
				{$node->page->name}
			</a>
			<ul n:if="$node->submenu ?? false" class="m-categories__list m-categories__list--{$nestLvl}">
				{include #siblings, siblings=>$node->submenu, nestLvl=>$nestLvl + 1}
			</ul>
		</li>
	{/foreach}
{/define}

<nav n:if="count($siblings)" n:class="m-categories, $class">
	<h2 class="h6 u-mb-xxs">
		{_title_side_categories}
	</h2>
	<ul class="m-categories__list m-categories__list--1">
		{include #siblings, siblings=>$siblings}
	</ul>
</nav>
