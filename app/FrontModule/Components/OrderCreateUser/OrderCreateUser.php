<?php declare(strict_types=1);

namespace App\FrontModule\Components\OrderCreateUser;

use App\FrontModule\Components\FormMessage\FormMessage;
use App\FrontModule\Components\FormMessage\FormMessageFactory;
use App\FrontModule\Presenters\BasePresenter;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Orm;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserModel;
use App\Model\Security\Acl;
use App\Model\TranslatorDB;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Security\AuthenticationException;
use Nette\Utils\ArrayHash;

/**
 * @property-read DefaultTemplate $template
 * @property-read BasePresenter $presenter
 * @property-read UI\Control $parent
 */
final class OrderCreateUser extends UI\Control
{

	private Mutation $mutation;

	public function __construct(
		private readonly Order $order,
		private readonly UserModel $userModel,
		private readonly Orm $orm,
		private readonly TranslatorDB $translator,
		private readonly FormMessageFactory $formMessageFactory,
		MutationHolder $mutationHolder,
	)
	{
		$this->mutation = $mutationHolder->getMutation();
	}

	public function createComponentFormMessage(): FormMessage
	{
		return $this->formMessageFactory->create();
	}

	public function createComponentForm(): UI\Form
	{
		$f = new UI\Form();
		$f->setTranslator($this->translator);
		$f->addHidden('order', $this->order->id)->addRule($f::Integer, '');
		$f->addPassword('password', 'order_user_password')->setRequired();

		$f->addSubmit('save', 'order_save_user');

		$f->onError[]   = $this->formError(...);
		$f->onValidate[] = $this->formValidate(...);
		$f->onSuccess[] = $this->formSuccess(...);

		return $f;
	}

	private function formError(UI\Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	private function formValidate(UI\Form $form, ArrayHash $values): void
	{
		$order = $this->orm->order->getById($values->order);
		if ($order === null) {
			$form->addError('order_not_found');
		}

		if ($order->user !== null) {
			$form->addError('order_has_user');
		}

		$user = $this->orm->user->getByEmail($order->email, $this->mutation);
		if ($user) {
			$form->addError('mail_exist_register');
		}
	}

	private function formSuccess(UI\Form $form, ArrayHash $values): void
	{
		$orderId = $values->order;
		unset($values->order);
		$order = $this->orm->order->getById($orderId);

		$customAddress = [];

		$values->email = $order->email;
		$values->phone = $customAddress['invPhone'] = $order->phone;
		$values->street = $customAddress['invStreet'] = $order->street;
		$values->city = $customAddress['invCity'] = $order->city;
		$values->zip = $customAddress['invZip'] = $order->zip;
		$values->state = $order->country;

		$customAddress['invState'] = $order->country->id;

		$splitName = explode(' ', $order->name);
		$firstName = $splitName[0];
		unset($splitName[0]);
		$lastName = implode(' ', $splitName);
		$values->firstname = $customAddress['invFirstname'] = $firstName;
		$values->lastname = $customAddress['invLastname'] = $lastName;

		$customAddress['invCompany'] = $order->companyName;
		$customAddress['invIc'] = $order->companyIdentifier;
		$customAddress['invDic'] = $order->vatNumber;

		$values->customAddress = [$customAddress];

		$user = new User();
		$this->orm->user->attach($user);
		$user->role = Acl::ROLE_USER;
		$user->priceLevel = PriceLevel::DEFAULT_ID;
		$user->mutations->add($this->mutation);

		$user = $this->userModel->save($user, (array) $values);

		if ($user->isPersisted()) {
			$order->user = $user;
			$this->orm->order->persistAndFlush($order);
			$this->template->order = $order;
		}

		try {
			$this->presenter->getUser()->login($values['email'], $values['password']);
		} catch (AuthenticationException $e) {
			$this->flashMessage($e->getMessage(), 'error');
		}

		$this->presenter->redirect($this->mutation->pages->step3, ['orderHash' => $order->hash, 'orderId' => $order->id]);
	}

	public function beforeRender(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->translator = $this->translator;
		$this->template->templates = FE_TEMPLATE_DIR;
	}
	public function render(): void
	{
		$this->beforeRender();

		$this->template->render(__DIR__ . '/orderCreateUser.latte');
	}

}
