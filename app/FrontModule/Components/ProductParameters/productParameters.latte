{default $class = false}
{varType App\Model\Orm\ParameterValue\ParameterValue $parameterValueDimension}
{var $areDimensionsRendered = false}
{var $dimensions = ''}

{if ($showParameters)}
	{cache 'productDetailParameters-P-' . $product->id, expire: $product->getTemplateCacheExpire(), tags: $product->getTemplateCacheTags()}

		{var $productParameters = $getProductParameters()}
		<div n:if="(count($productParameters) > 0 || count($beforeProductParameters) > 0)" n:class="b-parameters, $class">
			<dl class="b-parameters__dl">
				{foreach $beforeProductParameters as $name => $value}
					<div class="b-parameters__row">
						<dt class="b-parameters__dt">{$name}</dt>
						<dd class="b-parameters__dd">{$value}</dd>
					</div>
				{/foreach}

				{foreach $productParameters as $parameter}
					<div class="b-parameters__row">
						{if $parameter->isDimensionParameter()}
							{continueIf $areDimensionsRendered}
							<dt class="b-parameters__dt">
								{_parameter_name_dimensions}:
							</dt>
							<dd class="b-parameters__dd">
								{foreach $product->parameterValueDimensions as $parameterValueDimension}
									{$parameterValueDimension->internalValue} {sep} x {/sep}
									{last} mm{/last}
								{/foreach}
							</dd>
							{continueIf $areDimensionsRendered = true}
						{/if}

						{var $parameterValue = $product->getParameterValueByUid($parameter->uid)}
						<dt class="b-parameters__dt">
							{$parameter->title}:
						</dt>
						{if $parameter->type == "wysiwyg"}
							<dd class="b-parameters__dd">
								{$parameterValue->value|noescape}
							</dd>
						{elseif $parameter->type == "bool"}
							{if $parameterValue->value == 1}
								<dd class="b-parameters__dd">
									{_yes}
								</dd>
							{else}
								<dd class="b-parameters__dd">
									{_no}
								</dd>
							{/if}
						{else}
							<dd class="b-parameters__dd">
								{define #paramValue}
									{if in_array($parameter->uid, $detailedUids)}
										{var $postTypeLink = $control->getPostTypeLink($parameterValue)}
										<a href="{$postTypeLink}" n:tag-if="$postTypeLink !== null">{$parameterValue->value} {if $parameter->type == 'number'}{$parameter->unit}{/if}</a>
									{elseif in_array($parameter->uid, $filtrableUids)}
										{var $filter = ['dials' => [$parameter->uid => [$parameterValue->id => $parameterValue->id]]]}
										{capture $link}{plink $mainCategory, 'filter' => $filter}{/capture}
										{php $link = urldecode(htmlspecialchars_decode($link))}

										<a href="{$link}">
											{$parameterValue->value} {if $parameter->type == 'number'}{$parameter->unit}{/if}
										</a>
									{else}
										{$parameterValue->value} {if $parameter->type == 'number'}{$parameter->unit}{/if}
									{/if}
								{/define}

								{if $parameter->type == "multiselect"}
									{foreach $parameterValue as $valueObject}
										{if $parameter->uid === App\Model\Orm\Parameter\Parameter::UID_LANGUAGE}
											{first}
												{if isset($valueObject->cf->flagIcon->flagIcon->entity->url) && !$valueObject->isCzechLanguage}
													<img class="b-selected__country-flag"
														src="{$valueObject->cf->flagIcon->flagIcon->entity->url}"
														alt=""
														loading="lazy"
													>
												{/if}
											{/first}
										{/if}
										{include #paramValue, parameterValue: $valueObject}
									{/foreach}
								{else}
									{include #paramValue, parameterValue: $parameterValue}
								{/if}
							</dd>
						{/if}
					</div>
				{/foreach}
			</dl>
		</div>
	{/cache}
{/if}
