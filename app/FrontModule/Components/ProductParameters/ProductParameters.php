<?php

declare(strict_types = 1);

namespace App\FrontModule\Components\ProductParameters;

use App\Model\BucketFilter\CatalogParameter;
use App\Model\CustomField\LazyValue;
use App\Model\Orm\Orm;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\Product\Product;
use App\Model\TranslatorDB;
use App\PostType\Writer\Model\Orm\Writer\Writer;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nextras\Orm\Collection\ICollection;

/**
 * @property-read DefaultTemplate $template
 */
class ProductParameters extends UI\Control
{

	public function __construct(
		private readonly Orm $orm,
		private readonly TranslatorDB $translator,
		private readonly Product $product,
		private readonly CatalogParameter $catalogParameter,
	)
	{
	}


	public function render(): void
	{
		$this->template->setTranslator($this->translator);

		if ($mainCategory = $this->product->mainCategory) {
			$mainPageParameters = $this->catalogParameter->getParametersCfForFilter($mainCategory);
			$this->template->mainPageParametes = $mainPageParameters;
		}

		$getProductParameters = function() {
			return $this->prioritizeLanguageParameter(
				$this->orm->parameter->findDetailParametersForProduct($this->product)
			);
		};

		$filtrableUids = $detailedUids = [];
		if (isset($mainPageParameters->visibleParameters)) {
			foreach ($mainPageParameters->visibleParameters as $visibleParameter) {
				if (isset($visibleParameter->parameter) && $visibleParameter->parameter instanceof LazyValue && $visibleParameter->parameter->getEntity() !== null) {
					$parameter = $$visibleParameter->parameter->getEntity();
					if ($parameter->hasDetailPage()) {
						$detailedUids[] = $parameter->uid;
					} else {
						if ($parameter->type == Parameter::TYPE_NUMBER) {
							if ( ! isset($visibleParameter->numberAsRange) || ! $visibleParameter->numberAsRange) {
								$filtrableUids[] = $parameter->uid;
							}
						} else {
							$filtrableUids[] = $parameter->uid;
						}
					}
				}
			}
		}

		$this->template->product = $this->product;
		$this->template->mainCategory = $this->product->mainCategory;
		$this->template->filtrableUids = $filtrableUids;
		$this->template->detailedUids = $detailedUids;
		$this->getTemplate()->showParameters = $this->showParameters();
		$this->template->getProductParameters = $getProductParameters;
		$this->template->beforeProductParameters = [
			'Kód' => $this->product->firstVariant->code,
			'EAN' => $this->product->firstVariant->ean,
		];

		$this->template->render(__DIR__ . '/productParameters.latte');
	}

	/**
	 * If product has language parameter and there is no czech language value -> prepend whole parameter to the start of the array
	 * @param ICollection<Parameter> $parameters
	 */
	private function prioritizeLanguageParameter(ICollection $parameters): array
	{
		$orderedParameters = [];
		$priorityParameters = [];

		foreach ($parameters as $parameter) {
			assert($parameter instanceof Parameter);
			$hasCzechLanguage = false;

			if (!$productParameterValues = $this->product->getParameterValueByUid($parameter->uid)) {
				continue;
			}

			if ($productParameterValues instanceof ParameterValue) {
				$productParameterValues = [$productParameterValues];
			}

			foreach ($productParameterValues as $parameterValue) {
				if ($parameterValue->isLanguageParameter) {
					if ($parameterValue->isCzechLanguage) {
						$hasCzechLanguage = true;
					}
				}
			}

			if ($hasCzechLanguage) {
				$priorityParameters[] = $parameter;
			} else {
				$orderedParameters[] = $parameter;
			}
		}

		return array_merge($priorityParameters, $orderedParameters);
	}


	private function showParameters(): bool
	{
		if (!isset($this->product->mainCategory->cf)) {
			return true;
		}

		$categoryCf = $this->product->mainCategory->cf;

		if (!isset($categoryCf->categoryParametersForProductDetail->visible)) {
			return true;
		}

		return !$categoryCf->categoryParametersForProductDetail->visible;
	}

	public function getPostTypeLink(ParameterValue $parameterValue): ?string
	{
		$postType = $parameterValue->{$parameterValue->parameter->uid};

		if ($postType === null) {
			return null;
		}

		$postTypeLocalization = $postType->getLocalization($this->product->getMutation());

		return $this->getPresenter()->link($postTypeLocalization);
	}
}
