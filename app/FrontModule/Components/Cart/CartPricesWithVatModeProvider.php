<?php declare(strict_types=1);

namespace App\FrontModule\Components\Cart;

use App\Utils\DateTime;
use Nette\Http\Request;
use Nette\Http\Response;

final class CartPricesWithVatModeProvider
{
	private const string COOKIE_NAME = 'shoppingCartPricesWithVatMode';

	private bool $isSetted = false;
	private bool $settedValue = false;
	public bool $isActive {
		get {
			if ($this->isSetted) {
				return $this->settedValue;
			}
			return ($this->httpRequest->getCookie(self::COOKIE_NAME) !== null && $this->httpRequest->getCookie(self::COOKIE_NAME) === '1');
		}
		set {
			$this->httpResponse->setCookie(self::COOKIE_NAME, (string) intval($value), DateTime::from('tomorrow')->modify('-1 second'));
			$this->isSetted = true;
			$this->settedValue = $value;
		}
	}

	public function __construct(
		private readonly Request $httpRequest,
		private readonly Response $httpResponse,
	)
	{
	}

}
