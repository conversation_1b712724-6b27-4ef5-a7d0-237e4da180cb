{var App\Model\ShoppingCart\ShoppingCart $shoppingCart = $presenter->shoppingCart}
{varType App\FrontModule\Components\Cart\Cart $control}
{varType Nextras\Orm\Collection\ICollection $products}

{default $class = false}

{snippet headerCartContent}
	{if $preInit === false}
		{include $templates . '/part/box/smallbasket.latte', form: $control['form']}
	{elseif $preInit === true}
		<div class="block-loader is-loading" data-controller="intersection">
			<p class="u-vhide">
				<a n:href="initHeader!" data-naja data-naja-history="off" data-naja-unique="off" data-intersection-target="btn">
					{_"btn_show"}
				</a>
			</p>
			<div class="block-loader__loader"></div>
		</div>
	{/if}
{/snippet}
