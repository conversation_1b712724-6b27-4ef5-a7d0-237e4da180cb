{varType Nextras\Orm\Collection\ICollection $items}
{snippet cartContent}
	{if $shoppingCart->getTotalCount() > 0}
		<h1 class="u-vhide">{$object->name}</h1>
		{include $templates.'/part/box/order-steps.latte', class: 'tw-pt-[2.8rem] lg:tw-pt-[5.2rem] tw-mb-[0.8rem] md:tw-mb-[1.2rem]', currentStep: 1}

		<div n:ifcontent class="u-mb-last-0 u-mb-sm u-mb-md@lg">
			<div class="u-mb-xs">
				{control freeDelivery}
			</div>

			<p n:foreach="$flashes as $flash" class="message message--{$flash->type} u-mb-xs">
				<span class="message__emoji">{if $flash->type == 'ok'}✅{else}⚠️{/if}</span>
				<span class="message__content">
					{$flash->message}
				</span>
			</p>
		</div>

		<div class="b-layout-basket">
			{include $templates.'/part/form/basket.latte', class: false, showVoucherInput: $showVoucherInput}
			{* TODO: Ještě než objednáte: Nezapomněli jste na něco? *}
			{*include $templates.'/part/crossroad/else.latte', class: 'b-layout-basket__side'*}

			{* Pokračovat *}
			<p class="b-layout-basket__next">
				<a href="{plink $pages->step1}" class="btn btn--xl btn--secondary">
					<span class="btn__text">
						<span class="btn__inner">
							{_"btn_cart_continue_delivery_shipping"}
							{('arrow-right-thin')|icon, 'btn__icon'}
						</span>
					</span>
				</a>
			</p>

			{* Předchozí stránka nebo e-shop *}
			<p class="b-layout-basket__prev">
				{php $previousPage = isset($_SERVER['HTTP_REFERER']) && str_contains($_SERVER['HTTP_REFERER'], $mutation->getBaseUrl())}
				{if $previousPage && !in_array($previousPage, ['/doprava-a-platba', '/dodaci-udaje'])}
					<a href="{$_SERVER['HTTP_REFERER']}" class="btm btn--bd">
						<span class="btn__text">
							{_"btn_back_shopping"}
						</span>
					</a>
				{elseif isset($pages->eshop)}
					<a href="{plink $pages->eshop}" class="btn btn--bd">
						<span class="btn__text">
							{_"btn_back_shopping"}
						</span>
					</a>
				{/if}
			</p>
		</div>

		{* Delší doba dodání *}
		{* <p n:if="$shoppingCart->hasExtendedDelivery()" class="u-maw-4-12 u-ml-auto u-mb-sm">
			<span class="tooltip tooltip--error tooltip--static top end">
				<span class="tooltip__content">
					{_"order_msg_prolonged"}
					<span class="tooltip__arrow"></span>
				</span>
			</span>
		</p> *}

	{else} {* Prázdný košík *}
		{include $templates.'/part/box/empty.latte'}
		{include $templates.'/part/box/bnr-img.latte', class: 'tw-rounded-md md:tw-rounded-xl tw-overflow-hidden u-mb-md u-mb-2xl@lg'}
		<div class="b-layout-basket">
			{control savedForLater}
		</div>
		{* control productListBestsellers *}
	{/if}
{/snippet}

<hr class="u-mb-md u-mb-md@lg">
{include $templates.'/part/box/benefits.latte'}


{* {if $marketingConsent}
	<div n:ifcontent class="b-basket-products u-bgc-white u-mb-last-0 u-mb-xs u-mb-sm@md">
		{php $control['productListCustomerAlsoBuy']->setTemplateParameters(['class' => 'section--products section--w-line u-mb-md u-mb-lg@md'])}
		{control productListCustomerAlsoBuy}

		{php $control['productListRecommend']->setTemplateParameters(['class' => 'section--products section--w-line u-mb-md u-mb-lg@md'])}
		{control productListRecommend}
	</div>
{/if} *}
