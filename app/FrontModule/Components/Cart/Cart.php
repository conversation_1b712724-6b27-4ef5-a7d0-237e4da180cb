<?php declare(strict_types = 1);

namespace App\FrontModule\Components\Cart;

use App\FrontModule\Components\CartDelivery\CartDelivery;
use App\FrontModule\Components\CartDelivery\CartDeliveryFactory;
use App\FrontModule\Components\CartFreeDelivery\CartFreeDelivery;
use App\FrontModule\Components\CartFreeDelivery\CartFreeDeliveryFactory;
use App\FrontModule\Components\CartGift\CartGift;
use App\FrontModule\Components\CartGift\CartGiftFactory;
use App\FrontModule\Components\CartUserDetail\CartUserDetail;
use App\FrontModule\Components\CartUserDetail\CartUserDetailFactory;
use App\FrontModule\Components\ProductList\ProductList;
use App\FrontModule\Components\ProductList\ProductListFactory;
use App\FrontModule\Components\ProductList\ProductListInterestedFactory;
use App\FrontModule\Components\SavedForLater\SavedForLater;
use App\FrontModule\Components\SiteHeader\SiteHeader;
use App\FrontModule\Presenters\BasePresenter;
use App\FrontModule\Presenters\Order\OrderPresenter;
use App\Infrastructure\Latte\Filters;
use App\Model\BucketFilter\ElasticItem\HasCategories;
use App\Model\Consent\MarketingConsent;
use App\Model\DTO\Product\ProductDtoProvider;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Order\Class\ClassItem;
use App\Model\Orm\Order\Gift\GiftItem;
use App\Model\Orm\Order\Product\ProductItem;
use App\Model\Orm\Order\Promotion\PromotionItem;
use App\Model\Orm\Order\RefreshResult;
use App\Model\Orm\Order\Voucher\VoucherItem;
use App\Model\Orm\Orm;
use App\Model\Orm\Price;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\State\State;
use App\Model\Orm\User\UserModel;
use App\Model\Orm\User\UserProvider;
use App\Model\Orm\VoucherCode\VoucherCode;
use App\Model\Security\User;
use App\Model\ShoppingCart\SavedForLaterProvider;
use App\Model\ShoppingCart\ShoppingCartInterface;
use App\Model\TranslatorDB;
use Brick\Money\Money;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Forms\Controls\TextInput;
use Nette\Http\Request;
use Nette\Http\Response;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;

/**
 * @property-read DefaultTemplate $template
 * @property-read BasePresenter $presenter
 */
final class Cart extends UI\Control
{
	/** @var ICollection<ProductItem>  */
	private ICollection $products;

	/** @var ICollection<VoucherItem> */
	private ICollection $vouchers;

	/** @var ICollection<GiftItem> */
	private ICollection $gifts;

	/** @var ICollection<PromotionItem> */
	private ICollection $promotions;

	private array $amounts;

	private bool $showFlashes = false;

	public array $orderedProductIds = [];

	private bool $forcedHeader = false;

	private bool $preInit = true;

	/** @var ICollection<ClassItem> */
	private ICollection $classEvents;


	public function __construct(
		private readonly UserProvider $userProvider,
		private readonly State $currentState,
		private readonly PriceLevel $priceLevel,
		private readonly ShoppingCartInterface $shoppingCart,
		private readonly TranslatorDB $translator,
		private readonly Orm $orm,
		private readonly MutationHolder $mutationHolder,
		private readonly CartFreeDeliveryFactory $cartFreeDeliveryFactory,
		private readonly CartDeliveryFactory $cartDeliveryFactory,
		private readonly CartUserDetailFactory $cartUserDetailFactory,
		private readonly CartGiftFactory $cartGiftFactory,
		private readonly ProductListFactory $productListFactory,
		private readonly ProductListInterestedFactory $productListInterestedFactory,
		private readonly MarketingConsent $marketingConsent,
		private readonly User $user,
		private readonly UserModel $userModel,
		private readonly ProductDtoProvider $productDtoProvider,
		private readonly CartPricesWithVatModeProvider $cartPricesWithVatModeProvider,
		private readonly SavedForLaterProvider $savedForLaterProvider,
		private readonly SavedForLater $savedForLater,
	)
	{
		$this->onAnchor[] = $this->initCart(...);
	}

	public function initCart(): void
	{
		$this->shoppingCart->initAutoItems();

		$this->products = $this->shoppingCart->getProducts();
		$this->classEvents = $this->shoppingCart->getClassEvents();
		$this->vouchers = $this->shoppingCart->getVouchers();
		$this->gifts = $this->shoppingCart->getGifts();
		$this->promotions = $this->shoppingCart->getPromotions();
		$this->amounts = $this->shoppingCart->getAmounts();
	}

	public function refreshCart(): void
	{
		if ($this->presenter->getHttpRequest()->getQuery('refreshCart') === '0') {
			$this->template->changedItems = $this->shoppingCart->getChangedItems(onlyToRemove: true);
		} else {
			if (($changed = $this->shoppingCart->refresh()) !== []) {
				$showed = [];
				/** @var RefreshResult $changedItem */
				foreach ($changed as $changedItem) {
					if (($changedItem->isGrouped && in_array($changedItem->message->text, $showed)) || $changedItem->message->text === null) {
						continue;
					}

					$message = $this->translate($changedItem->message->text, $this->translateParams($changedItem->message->params));

					if ($changedItem->item instanceof VoucherItem) {
						$this->template->voucherWarning = $message;
					} else {
						$this->flashMessage($message, $changedItem->message->type);
					}
					$showed[] = $changedItem->message->text;
				}

				$this->initCart();
			}
		}
	}

	public function getTemplateParameters(): array
	{
		return [
			'translator' => $this->translator,
			'object' => $this->presenter->getObject(),
			'templates' => FE_TEMPLATE_DIR,
			'pages' => $this->mutationHolder->getMutation()->pages,
			'mutation' => $this->mutationHolder->getMutation(),
			'state' => $this->currentState,
			'priceLevel' => $this->priceLevel,
			'shoppingCart' => $this->shoppingCart,
			'products' => $this->products,
			'classEvents' => $this->classEvents,
			'gifts' => $this->gifts,
			'promotions' => $this->promotions,
			'vouchers' => $this->vouchers,
			'totalPrice' => $this->shoppingCart->getTotalPrice(precision: 2),
			'totalPriceVatUnrounded' => $this->shoppingCart->getTotalPriceVat(includeGiftCertificates: false, withFreeDeliveryVoucher: false, precision: 2),
			'totalPriceVatWithoutCertificate' => Price::from($this->shoppingCart->getTotalPriceVat(includeGiftCertificates: false, withFreeDeliveryVoucher: false, precision: 2))->asMoney(),
			'totalGiftCertificatesPrice' => $this->shoppingCart->getTotalGiftCertificatesPrice(),
			'totalPriceVat' => Price::from($this->shoppingCart->getTotalPriceVat(precision: 2))->asMoney(),
			'totalPriceWithDelivery' => Price::from($this->shoppingCart->getTotalPriceWithDelivery(precision: 2))->asMoney(),
			'totalPriceWithDeliveryVat' => Price::from($this->shoppingCart->getTotalPriceWithDeliveryVat(precision: 2))->asMoney(),
			'marketingConsent' => $this->marketingConsent->isPersonalizationGranted(),
			'totalWeight' => $this->shoppingCart->getTotalWeight(),
			'cartPricesWithVatModeProvider' => $this->cartPricesWithVatModeProvider,
		];
	}

	public function beforeRender(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->setParameters($this->getTemplateParameters());

		$this->template->productDtoProviderCallback = function (Product $product, ProductVariant $variant) {
			return$this->productDtoProvider->get($product, $variant);
		};
	}

	public function actionDefault(): void
	{
		$this->orderedProductIds = $this->userModel->findOrderedProductIds($this->user);
	}

	public function render(): void
	{
		if ($this->forcedHeader === true) {
			$this->renderHeader();
			return;
		}

		$this->refreshCart();
		$this->beforeRender();

		$this->template->showVoucherInput = $this->orm->voucherCode->findBy([
			'voucher->mutation' => $this->mutationHolder->getMutation(),
			'voucher->public' => 1,
			'voucher->publicFrom<=' => new DateTimeImmutable(),
			'voucher->publicTo>=' => new DateTimeImmutable(),
			'isUsed' => 0
		])->countStored() > 0;

		$this->template->availableGiftCount = $this['cartGift']->availableGiftCount;
		$this->template->availableGiftText = $this['cartGift']->availableGiftText;


		$this->template->render(__DIR__ . '/cart.latte');
	}

	public function renderHeader(): void
	{
		$this->beforeRender();
		$this->template->preInit = $this->preInit;

		$this->template->render(__DIR__ . '/cartHeader.latte');
	}

	public function handleInitHeader(): void
	{
		$this->initCart();

		$this->forcedHeader = true;
		$this->preInit = false;

		$this->redrawControl();
	}


	public function createComponentFreeDelivery(): CartFreeDelivery
	{
		return $this->cartFreeDeliveryFactory->create($this->mutationHolder->getMutation(), $this->currentState, $this->priceLevel, $this->userProvider);
	}

	public function createComponentForm(): UI\Form
	{
		$form = new UI\Form();
		$form->setTranslator($this->translator);

		$products = $form->addContainer('products');
		/** @var ProductItem $productItem */
		foreach ($this->shoppingCart->getProducts() as $productItem) {
			$container = $products->addContainer($productItem->variant->id);
			$container->addInteger('quantity', 'quantity')->setDefaultValue($productItem->amount);
		}

		$classEvents = $form->addContainer('classEvents');

		foreach ($this->shoppingCart->getClassEvents() as $classItem) {
			$container = $classEvents->addContainer($classItem->getIdentifier() ?? 0);
			$container->addInteger('quantity', 'quantity')->setDefaultValue($classItem->amount);
		}

		$form->addText('voucher', 'voucher')->setNullable();

		$form->addSubmit('send');

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];

		return $form;
	}

	public function createComponentCartDelivery(): CartDelivery
	{
		return $this->cartDeliveryFactory->create();
	}

	public function createComponentCartUserDetail(): CartUserDetail
	{
		return $this->cartUserDetailFactory->create();
	}

	public function createComponentCartGift(): CartGift
	{
		return $this->cartGiftFactory->create($this->mutationHolder->getMutation(), $this->currentState, $this->priceLevel, $this->userProvider->userEntity);
	}


	public function formError(UI\Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSucceeded(UI\Form $form, ArrayHash $values): void
	{
		$flashes = [];

		foreach ($values->products as $productVariantId => $data) {
			$key = 'products-' . $productVariantId;
			if($data->quantity !== $this->amounts[$key]){
				$variant = $this->orm->productVariant->getById($productVariantId);
				if($data->quantity > $this->amounts[$key]) {
					$amount = (int) abs($data->quantity - $this->amounts[$key]);
					$quantityAdded = $this->shoppingCart->addProduct($variant, $amount);
				} else {
					$quantityAdded = $amount = (int) ($this->amounts[$key] - $data->quantity);
					$this->shoppingCart->subtractProduct($variant, $amount);
				}

				if ($this->showFlashes) {
					if ($quantityAdded === 0) {
						$flashes['error'] = $this->translate('cart_message_no_updated_quantity');
					} elseif ($quantityAdded !== $amount) {
						$flashes['warning'] = $this->translate('cart_message_no_enough_quantity');
					} else {
						$flashes['ok'] = $this->translate('cart_message_quantity_updated');
					}
				}
			}
		}

		foreach ($values->classEvents as $classEventIdentifier => $data) {
			list($productId, $classEventId, $priceLevelId) = explode('_', $classEventIdentifier);
			$key = 'classEvents-' . $classEventIdentifier;
			if($data->quantity !== $this->amounts[$key]){
				$product = $this->orm->product->getById($productId);
				$classEvent = $classEventId > 0 ? $this->orm->classEvent->getById($classEventId) : null;
				$priceLevel = $priceLevelId > 0 ? $this->orm->priceLevel->getById($priceLevelId) : null;
				if($data->quantity > $this->amounts[$key]) {
					$amount = (int) abs($data->quantity - $this->amounts[$key]);
					$quantityAdded = $this->shoppingCart->addClass($product, $classEvent, $priceLevel, $amount);
				} else {
					$quantityAdded = $amount = (int) ($this->amounts[$key] - $data->quantity);
					$this->shoppingCart->subtractClass($product, $classEvent, $priceLevel, $amount);
				}

				if ($this->showFlashes) {
					if ($quantityAdded === 0) {
						$flashes['error'] = $this->translate('cart_message_no_updated_quantity');
					} elseif ($quantityAdded !== $amount) {
						$flashes['warning'] = $this->translate('cart_message_no_enough_quantity');
					} else {
						$flashes['ok'] = $this->translate('cart_message_quantity_updated');
					}
				}
			}
		}

		if ($values->voucher !== null) {
			$voucherCode = $this->orm->voucherCode->getByCode($values->voucher, $this->mutationHolder->getMutation());
			$response = VoucherCode::ERROR_NO_EXISTS;
			if ($voucherCode !== null) {
				$response = $this->shoppingCart->addVoucher($voucherCode);
			}

			if ($response === true) {
				$this->shoppingCart->flushVouchers([$voucherCode]);

				$replace = ['%voucher%' => $voucherCode->getName()];
				$flashes['ok'] = $this->translate('cart_message_voucher_added', $replace);

			} else {
				$replace = [];
				if ($voucherCode?->voucher !== null){
					$price = Price::from(Money::of($voucherCode->voucher->minPriceOrder ?? 0, $voucherCode->voucher->getCurrency()))->asMoney();
					$replace = [
						'%minOrderPrice%' => Filters::formatMoney($price),
					];
				}
				$this->template->voucherError = $this->translate($response, $replace);
			}
			$this['form']['voucher']->setValue(null);
		}

		$this->initCart();

		foreach ($this->amounts as $key => $amount) {
			list($type, $keyId) = explode('-', $key);
			/** @var TextInput $control */
			$control = $this['form'][$type][$keyId]['quantity']; // @phpstan-ignore-line
			$control->setValue($amount);
		}


		if ($flashes !== []) {
			foreach ($flashes as $type => $message) {
				$this->flashMessage($this->translate($message), $type);
			}
		}

		if (!$this->presenter->isAjax()) {
			$this->presenter->redirect('this');
		}

		if ($this->getParent() instanceof OrderPresenter) {
			$this->getPresenterIfExists()?->redrawControl();
		} elseif ($this->getParent() instanceof SiteHeader) {
			$this->getParent()->redrawControl('headerCart');
		}

		$this->preInit = false;
		$this->forcedHeader = true;
		$this->redrawControl();

		$this->forcedHeader = false;
		$this->getPresenterIfExists()?->redrawControl('cart');
	}

	public function handleDeleteItem(int $variantId): void
	{
		$variant = $this->orm->productVariant->getById($variantId);
		if ($variant !== null) {
			$this->shoppingCart->removeProduct($variant);

			$this->initCart();

			if ($this->showFlashes) {
				$this->flashMessage($this->translate('cart_message_product_deleted'),	'error');
			}
		}

		if (!$this->presenter->isAjax()) {
			$this->presenter->redirect('this');
		}

		$this->presenter->redrawControl();
	}

	public function handleDeleteClass(string $classEventIdentifier): void
	{
		list($productId, $classEventId, $priceLevelId) = explode('_', $classEventIdentifier);
		$product = $this->orm->product->getById($productId);
		$classEvent = $classEventId > 0 ? $this->orm->classEvent->getById($classEventId) : null;
		$priceLevel = $priceLevelId > 0 ? $this->orm->priceLevel->getById($priceLevelId) : null;

		if ($product !== null) {
			$this->shoppingCart->removeClass($product, $classEvent, $priceLevel);

			$this->initCart();

			if ($this->showFlashes) {
				$this->flashMessage($this->translate('cart_message_class_event_deleted'),	'error');
			}
		}

		if (!$this->presenter->isAjax()) {
			$this->presenter->redirect('this');
		}

		$this->presenter->redrawControl();
	}

	public function handleSaveForLaterItem(int $orderItemId): void
	{
		$classItem = $this->orm->orderProduct->getById($orderItemId);
		$this->savedForLaterProvider->add($classItem);;

		$this->initCart();

		if (!$this->presenter->isAjax()) {
			$this->presenter->redirect('this');
		}

		$this->presenter->redrawControl();
	}

	public function handleSaveForLaterClass(int $orderItemId): void
	{
		$classItem = $this->orm->orderClassEvent->getById($orderItemId);
		$this->savedForLaterProvider->add($classItem);;

		$this->initCart();

		if (!$this->presenter->isAjax()) {
			$this->presenter->redirect('this');
		}

		$this->presenter->redrawControl();
	}

	public function handleDeleteVoucherItem(int $voucherCodeId): void
	{
		$voucherCode = $this->orm->voucherCode->getById($voucherCodeId);
		if ($voucherCode !== null) {
			$this->shoppingCart->removeVoucher($voucherCode);

			$this->initCart();

			if ($this->showFlashes) {
				$this->flashMessage($this->translate('cart_message_voucher_deleted'),	'error');
			}
		}

		if (!$this->presenter->isAjax()) {
			$this->presenter->redirect('this');
		}

		$this->presenter->redrawControl();
	}

	public function handleDeleteGiftItem(int $giftLocalizationId): void
	{
		$giftLocalization = $this->orm->giftLocalization->getById($giftLocalizationId);
		if ($giftLocalization !== null) {
			$this->shoppingCart->removeGift($giftLocalization);

			$this->initCart();

			if ($this->showFlashes) {
				$this->flashMessage($this->translate('cart_message_gift_deleted'),	'error');
			}
		}

		if (!$this->presenter->isAjax()) {
			$this->presenter->redirect('this');
		}

		$this->presenter->redrawControl();
	}


	public function translate(string $key, array $replace = []): string
	{
		if ( ! empty($replace)) {
			return str_replace(array_keys($replace), array_values($replace), $this->translator->translate($key));
		}
		return $this->translator->translate($key);
	}

	public function translateParams(array $params = []): array
	{
		$keysToTranslate = ['%reason%'];
		foreach ($params as $key => $value) {
			if (in_array($key, $keysToTranslate)) {
				$params[$key] = $this->translate($value, $params);
			}
		}
		return $params;
	}

	public function setShowFlashes(bool $showFlashes = true): Cart
	{
		$this->showFlashes = $showFlashes;
		return $this;
	}

	public function createComponentProductListCartInterested() : ProductList
	{
		return $this->productListInterestedFactory->create($this->currentState, $this->priceLevel, $this->userProvider->userEntity)->setTitle('smallbasket_title_picks')->setShowTitle(false);
	}

	public function createComponentProductListBestsellers() : ProductList
	{
		return $this->productListFactory->create($this->currentState, $this->priceLevel, ProductList::TYPE_BESTSELLERS, $this->userProvider->userEntity)
										->setExcludeProductIds($this->orderedProductIds)
		                                ->setLimit(21);
		                                // ->setLazyLoad();
	}

	public function createComponentProductListCustomerAlsoBuy(): ProductList
	{
		$productIds = $orderedIds = $cartIds =[];
		if ($this->marketingConsent->isPersonalizationGranted()) {
			$orderedIds = $this->userModel->findOrderedProductIds($this->user);
			$cartIds = $this->shoppingCart->getProductsIds();
			$productIds = $this->userModel->findRelatedOrderedProducts(
				productIds: $cartIds,
				orderFrom: (new \DateTimeImmutable())->modify('-1 year')
			);
		}
		return $this->productListFactory->create($this->currentState, $this->priceLevel, ProductList::TYPE_INTERESTED, $this->userProvider->userEntity)
		                                ->setIncludeProductIds(array_keys($productIds))
		                                ->setExcludeProductIds($cartIds)
		                                ->setOrderedProductIds($orderedIds)
		                                ->setCartProductIds($cartIds)
		                                ->setLimit(21)
		                                ->setAppendBestselling()
		                                ->setAppendBestsellingWithExclude()
		                                ->setTitle('productList_customer_also_buy');
	}

	public function createComponentProductListRecommend(): ProductList
	{
		$firstCategories = [];
		/** @var ProductItem $product */
		foreach ($this->shoppingCart->getProducts() as $product){
			$path = $product->variant->product->path;
			if (isset($path[1])) {
				$firstCategories[$path[1]] = $path[1];
			}
		}
		$firstCategories = array_values($firstCategories);

		$orderedIds = $cartIds =[];
		if ($this->marketingConsent->isPersonalizationGranted()) {
			$orderedIds = $this->userModel->findOrderedProductIds($this->user);
			$cartIds = $this->shoppingCart->getProductsIds();
		}

		return $this->productListFactory->create($this->currentState, $this->priceLevel, ProductList::TYPE_BESTSELLING, $this->userProvider->userEntity)
		                                ->setExcludeProductIds(array_merge($orderedIds, $cartIds))
		                                ->setLimit(21)
		                                ->setTitle('productList_interested')
		                                ->addOnElasticItemList(function (array &$items) use ($firstCategories): void {
			                                //if ($firstCategories !== []) {
			                                $items[] = new HasCategories($firstCategories);
			                                //}
		                                });
	}

	public function handleChangePriceVat(string $state): void
	{
		$this->cartPricesWithVatModeProvider->isActive = !((int) $state);

		if (!$this->presenter->isAjax()) {
			$this->presenter->redirect('this');
		}
		$this->redrawControl();
	}

	public function createComponentSavedForLater(): SavedForLater
	{
		return $this->savedForLater;
	}
}
