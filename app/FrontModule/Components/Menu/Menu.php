<?php declare(strict_types = 1);

namespace App\FrontModule\Components\Menu;

use App\FrontModule\Components\TopTitle\TopTitleFactory;
use App\Model\Image\ImageObjectFactory;
use App\Model\Orm\Routable;
use App\Model\Setup;
use App\Model\StaticPage\StaticPage;
use App\Model\TranslatorDB;
use App\PostType\MenuMain\Model\Orm\MenuMainLocalization\MenuMainLocalizationRepository;
use Nette\Application\UI;
use Nette\Application\UI\Multiplier;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use App\Model\MenuService;
use Nette\Forms\Container;
use Nextras\Orm\Collection\ICollection;

/**
 * @property-read DefaultTemplate $template
 */
final class Menu extends UI\Control
{
	public const MENU_METADATA_CACHE_TAG = 'menuMetadata';
	private ?array $menu = null;

	public function __construct(
		private readonly Routable|StaticPage $object,
		private readonly Setup $setup,
		private readonly MenuService $menuService,
		private readonly ImageObjectFactory $imageObjectFactory,
		private readonly TranslatorDB $translator,
		private readonly MenuMainLocalizationRepository $menuMainLocalizationRepository,
		private readonly TopTitleFactory $topTitleFactory,
//		private readonly UserProductTopList $userProductTopList,
//
//		private readonly Repository $esProductRepository,
//		private readonly WriterLocalizationRepository $writerLocalizationRepository,
//		private readonly ThemeLocalizationRepository $themeLocalizationRepository,
//		private readonly ProductLocalizationRepository $productLocalizationRepository,
//		private readonly CacheFactory $cacheFactory,
	)
	{

	}

	private function initTemplate(): void
	{
		$this->template->setTranslator($this->translator);

		$this->template->pages = $this->setup->mutation->pages;
		$this->template->object = $this->object;
		$this->template->imageObjectFactory = $this->imageObjectFactory;
		$this->template->FE_TEMPLATE_DIR = FE_TEMPLATE_DIR;
	}

	private function initMenu(): void
	{
		$menuItemEntities = $this->menuMainLocalizationRepository->findBy(['mutation' => $this->setup->mutation, 'public' => 1])->orderBy('menuMain->order', ICollection::ASC_NULLS_LAST);
		$this->menu = $this->menuService->getMenu(
			$this->setup->mutation,
			$this->setup->mutation->rootId,
			$this->object,
			$menuItemEntities,
			true,
			[],
			$this->getPresenter()->getHttpRequest(),
		);

//		$menuMetadata = $this->getMenuMetadata($menuItemEntities);
//		foreach ($this->menu as $menuItem) {
//			if (isset($menuItem->page) && $menuItem->page instanceof CatalogTree) {
//				assert($menuItem instanceof stdClass);
//				$menuItem->writers = $menuMetadata->getWritersByCategoryId($menuItem->page->id);
//				$menuItem->themes = $menuMetadata->getThemesByCategoryId($menuItem->page->id);
//				$menuItem->topProduct = $menuMetadata->getTopProductByCategoryId($menuItem->page->id);
//			}
//		}
	}

	public function getMenuItems(): array
	{
		if ($this->menu === null) {
			$this->initMenu();
		}
		return $this->menu;
	}

	public function render(): void
	{
		$this->initTemplate();

		$this->template->menu = $this->getMenuItems();

		$this->template->mutation = $this->setup->mutation;
		$this->template->render(__DIR__ . '/mainMenu.latte');
	}

	/**
	 * @return Multiplier<Container>
	 */
	protected function createComponentTopTitle(): Multiplier
	{
		$multiplier = $this->createMultiplier();

		foreach ($this->menu as $menuItem) {
			if (!isset($menuItem->page)){
				continue;
			}

			if (isset($menuItem->topProduct) && $menuItem->topProduct !== null) {
				$multiplier->addComponent(
					$this->topTitleFactory->create(
						$this->setup,
						$menuItem->topProduct,
					),
					(string)$menuItem->page->id
				);
			}
		}
		return $multiplier;
	}

	/**
	 * @return Multiplier<Container>
	 */
	private function createMultiplier(): Multiplier
	{
		return new Multiplier(function () {
			return new Container;
		});
	}
//
//	/**
//	 * @param ICollection<MenuMainLocalization> $menuItemEntities
//	 */
//	private function getMenuMetadata(ICollection $menuItemEntities): MenuMetadata
//	{
//		$catalogIds = array_filter(
//			$menuItemEntities->fetchAll(),
//			fn(IEntity $menuItemEntity) => $menuItemEntity instanceof MenuMainLocalization && $menuItemEntity->getPage() !== null
//		);
//		$catalogIds = array_map(
//			fn(MenuMainLocalization $menuItemEntity) => (int) $menuItemEntity->getPage()->id,
//			$catalogIds
//		);
//
//		$cache = $this->cacheFactory->create('elasticData');
//		$cacheKey = 'topMenuMetadata-' . implode('|',$catalogIds);
//		$menuMetaData = $cache->load($cacheKey);
//		if ($menuMetaData === null) {
//			$menuMetaData = $this->esProductRepository->getMainMenuMetadata($this->setup->mutation, $catalogIds);
//			$cache->save($cacheKey, $menuMetaData, [
//				$cache::Expire => '30 minutes',
//				$cache::Tags => [self::MENU_METADATA_CACHE_TAG]
//			]);
//		}
//		[$writersByCategoryIds, $themesByCategoryIds, $topProductByCategoryIds] = $menuMetaData;
//
//		$userTopProductIds = $this->userProductTopList->getTopProductIds($this->setup->userEntity);
//		if ($userTopProductIds !== []) {
//			$userTopProductByCategoryIds = $this->esProductRepository->getUserMainMenuMetadata($this->setup->mutation, $catalogIds, $userTopProductIds);
//			foreach ($topProductByCategoryIds as $categoryId=>$topProductByCategoryId) {
//				if (isset($userTopProductByCategoryIds[$categoryId])) {
//					$topProductByCategoryIds[$categoryId] = $userTopProductByCategoryIds[$categoryId];
//				}
//			}
//		}
//
//		foreach ($writersByCategoryIds as $key=>$writersByCategoryId) {
//			if ($writersByCategoryId === []) {
//				continue;
//			}
//			$writersByCategoryIds[$key] = $this->writerLocalizationRepository->findByIdOrder($writersByCategoryId)->findBy(['mutation' => $this->setup->mutation])->fetchAll();
//		}
//		foreach ($themesByCategoryIds as $key=>$themesByCategoryId) {
//			if ($themesByCategoryId === []) {
//				continue;
//			}
//			$themesByCategoryIds[$key] = $this->themeLocalizationRepository->findByIdOrder($themesByCategoryId)->findBy(['mutation' => $this->setup->mutation])->fetchAll();
//		}
//		foreach ($topProductByCategoryIds as $key=>$topProductByCategoryId) {
//			if ($topProductByCategoryId === []) {
//				$topProductByCategoryIds[$key] = null;
//				continue;
//			}
//			$topProductByCategoryIds[$key] = $this->productLocalizationRepository->getBy([
//				'id' => $topProductByCategoryId,
//				'mutation' => $this->setup->mutation,
//			]);
//		}
//
//		return new MenuMetadata($writersByCategoryIds, $themesByCategoryIds, $topProductByCategoryIds);
//	}

}
