<ul class="m-main__list">
	{foreach $menu as $m}
		{php $categories = $m->categories ?? []}
		{php $hasSubmenu = count($categories)}
		{php $isBig = $m->isBig ?? false}
		{php $name = $m->name ?? $m->page->nameAnchor}
		{php $color = $m->color ?? false}
		{php $icon = $m->icon ?? false}

		{ifset $m->page}
			<li n:class="m-main__item, $isBig ? m-main__item--btn, $hasSubmenu ? overlay-pseudo"{if $hasSubmenu} data-controller="hover touch-open etarget"{/if} data-touch-open-init-size-value="900">
				<a href="{plink $m->page}" n:class="$isBig ? 'm-main__btn btn btn--rainbow btn--block' : m-main__link, $color, ($landingDetail ?? false) && $m->page->id == $landingDetail->parent->id || $m->active || $m->selected ? is-active, $m->page->uid == 'eshop' ? prerender : prerender-hover"{if $hasSubmenu} data-action="touch-open#open"{/if} data-list-id="{$m->name ?? $m->page->nameAnchor}">
					<span n:tag-if="$isBig" class="btn__text">
						<span n:tag-if="$hasSubmenu" n:class="$isBig ? btn__inner">
							{$name}
							{if $hasSubmenu && !$isBig}{('angle-down')|icon, 'm-main__arrow'}{/if}
							{if $hasSubmenu && $isBig}{('angle-down'|icon, 'btn__icon')}{/if}
						</span>
					</span>
				</a>
				<img n:if="$icon" class="m-main__icon" src="{$icon->getSize('xs')->src}" alt="" loading="lazy" width="20" height="20">
				{if $hasSubmenu}
					<button n:if="!$isBig" type="button" class="m-main__toggle as-link" data-action="header#toggleSubmenu">
						{('arrow-right')|icon}
						<span class="u-vhide">{_"show_more"}</span>
					</button>
					{include './submenu.latte', name: $name, page: $m->page, side: $m->side, categories: $categories}
				{/if}

			</li>
		{else}
			<li n:class="m-main__item, $isBig ? m-main__item--btn">
				<a href="{$m->link}" n:class="$isBig ? 'm-main__btn btn btn--rainbow btn--block' : m-main__link, $color, $m->active || $m->selected ? is-active">
					<span n:tag-if="$isBig" class="btn__text">
						{$m->name}
					</span>
				</a>
				<img n:if="$icon" class="m-main__icon" src="{$icon->getSize('xs')->src}" alt="" loading="lazy" width="20" height="20">
			</li>
		{/ifset}
	{/foreach}
</ul>
