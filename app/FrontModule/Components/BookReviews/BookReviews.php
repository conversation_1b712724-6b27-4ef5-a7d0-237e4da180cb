<?php declare(strict_types = 1);

namespace App\FrontModule\Components\BookReviews;


use App\Model\Orm\Orm;
use App\Model\TranslatorDB;
use Nette\Application\UI\Control;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nextras\Orm\Collection\ICollection;


/**
 * @property-read DefaultTemplate $template
 */
final class BookReviews extends Control
{
	public function __construct(
		private readonly TranslatorDB $translator,
		private readonly Orm $orm,
	)
	{
	}

	public function render(int $limit = 5, ?string $class = null): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->templates = FE_TEMPLATE_DIR;
		$this->template->class = $class;

		$this->template->reviews = $this->orm->review->findAll()->limitBy($limit)->orderBy('date', ICollection::DESC);

		$this->template->render(__DIR__ . '/bookReviews.latte');
	}
}
