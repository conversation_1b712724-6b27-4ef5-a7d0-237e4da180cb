<?php declare(strict_types = 1);

namespace App\FrontModule\Components\UserAnimalForm;

use App\AdminModule\Presenters\User\Components\ShellForm\FormData\BaseFormData;
use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\Exceptions\LogicException;
use App\Model\Form\CommonFormFactory;
use App\Model\Orm\LibraryImage\LibraryImageModel;
use App\Model\Orm\LibraryTree\LibraryTree;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\User\UserProvider;
use App\Model\TranslatorDB;
use App\PostType\UserAnimal\AdminModule\Components\UserAnimalForm\UserAnimalFormData;
use App\PostType\UserAnimal\Model\Orm\UserAnimal;
use App\PostType\UserAnimal\Model\Orm\UserAnimalModel;
use App\PostType\UserAnimal\Model\Orm\UserAnimalTypeLocalizationRepository;
use Nette\Application\AbortException;
use Nette\Application\ForbiddenRequestException;
use Nette\Application\UI;
use Nette\Application\UI\Form;
use Nette\Http\FileUpload;
use Nette\Utils\ArrayHash;
use Throwable;

final class UserAnimalForm extends UI\Control
{



	public function __construct(
		private readonly Mutation $mutation,
		private readonly UserAnimal|null $userAnimal,
		private readonly UserProvider $userProvider,
		private readonly TranslatorDB $translator,
		private readonly MessageForFormFactory $messageForFormFactory,
		private readonly CommonFormFactory $commonFormFactory,
		private readonly UserAnimalTypeLocalizationRepository $userAnimalTypeLocalizationRepository,
		private readonly Orm $orm,
		private readonly UserAnimalModel $userAnimalModel,
		private readonly LibraryImageModel $libraryImageModel

	) {

	}

	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->userAnimal = $this->userAnimal;
		$this->template->render(__DIR__ . '/userAnimalForm.latte');
	}

	/**
	 * Edit form factory.
	 *
	 * @return Form
	 */
	protected function createComponentForm(): Form
	{
		$form = $this->commonFormFactory->create();
		$animalTypes = $this->userAnimalTypeLocalizationRepository->getUserAnimalTypes($this->mutation)->fetchPairs('userAnimalType->id', 'name');

		$form->addUpload('avatar', 'avatar');
		$form->addText('name', 'animal_name')
			->setDefaultValue($this->userAnimal->name ?? '')
			->setRequired();
		$form->addSelect('userAnimalTypeId', 'animal_type', $animalTypes)
			->setRequired();

		$animalType = $this->userAnimal->userAnimalType->id ?? null;
		if ($animalType) {
			$form['userAnimalTypeId']->setDefaultValue($animalType);
		}

		$form->addHidden("animalId", $this->userAnimal->id ?? null);

		$form->addSubmit('save', 'btnSave');

		$form->onError[] = [$this, 'formError'];
		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formError(Form $form): void
	{
		bd($form->getErrors());

		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	/**
	 * @param Form $form
	 * @param ArrayHash $values
	 * @throws AbortException
	 */
	public function formSucceeded(Form $form, ArrayHash $values): void
	{

		try {
			$userId = $this->userProvider->userEntity->id;
			if ($this->userAnimal !== null && $userId !== $this->userAnimal->user->id) {
				throw new LogicException('Unknown user' . $userId);
			}

			$values = $this->processDataNewImage($values);

			if ($this->userAnimal !== null) {
					$animal = $this->userAnimal;
				} else {
					$animal = new UserAnimal();
				}

			$values->userId = $userId;

			$this->userAnimalModel->save($animal, $values);

			$this->flashMessage('form_profil_ok', 'ok');


		} catch (Throwable $e) {
			$this->flashMessage('Error', 'error');
		}

		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		} else {
			$this->presenter->redirect($this->mutation->pages->userAnimal);
		}
	}


	protected function processDataNewImage(ArrayHash $data): ArrayHash
	{
		if (isset($data->avatar) && $data->avatar instanceof FileUpload && $data->avatar->isOk()) {
			$options = [];
			/*if (isset($data['avatarPositions']) && !empty($data['avatarPositions'])) {
				$options['crop'] = explode(',', $data['avatarPositions']);    // z form přijde "0,202,780,982" = [topLeftX, topLeftY, bottomRightX, bottomRightY] http://foliotek.github.io/Croppie/#documentation
				$options['square'] = true;
			}*/
			$image = $this->libraryImageModel->addFromFileUpload($data->avatar, LibraryTree::USER_ANIMAL_ID);
			$data->imageId = $image->id;
		}

		return $data;
	}

	/**
	 * @throws AbortException
	 */
	public function handleDelete(): void {
		if ($this->userAnimal) {
			try {
				if ($this->userAnimal->user->id != $this->presenter->getUser()->id) {
					throw new ForbiddenRequestException(sprintf('Deleting animal ID %s is not allowed for user ID %s', $this->userAnimal->user->id, $this->presenter->getUser()->id));
				}

				$this->userAnimalModel->delete($this->userAnimal);
				$this->presenter->flashMessage('pet_delete_ok', 'ok');

			} catch (\Throwable $e) {
				$this->presenter->flashMessage('operation_failed', 'error');
			}
		}
		$this->presenter->redirect($this->mutation->pages->userAnimal);
	}

	public function handleDeleteImage(): void {
		if ($this->userAnimal) {
			try {
				if ($this->userAnimal->user->id != $this->userProvider->userEntity->id) {
					throw new ForbiddenRequestException(sprintf('Deleting animal ID %s is not allowed for user ID %s', $this->userAnimal->user->id, $this->presenter->getUser()->id));
				}

				if ($this->userAnimal->libraryImage) {
					$image = $this->orm->libraryImage->getById($this->userAnimal->libraryImage->id);

					$this->userAnimal->libraryImage = NULL;
					$this->orm->userAnimal->persistAndFlush($this->userAnimal);

					$this->libraryImageModel->deleteImage($image);
					$this->orm->libraryImage->removeAndFlush($image);

					$this->flashMessage('animal_image_deleted', 'ok');
				}
			} catch (\Throwable $e) {
				$this->flashMessage('operation_failed', 'error');
			}
		}
		$this->redirect('this');
	}

	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

}
