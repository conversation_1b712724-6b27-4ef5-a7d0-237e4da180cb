{snippet form}

	{form form class: 'tw-mb-[2.4rem] md:tw-mb-[3.2rem] max-md:tw-pt-[1.2rem] tw-border-solid tw-border-tile tw-border-l-0 tw-border-b-0 tw-border-r-0 md:tw-border-none', autocomplete: 'off', novalidate: "novalidate"}
		{control messageForForm, $flashes, $form}
		<h2 class="h3 tw-mb-[1.2rem]">{_'title_personal_info'}</h2>

		<div class="b-user-address md:tw-border-solid md:tw-border-tile md:tw-border-[0.1rem] md:tw-rounded-xl md:tw-p-[4rem_6rem]">
			<div class="u-maw-4-12">
				<div data-controller="address-suggest">
					{include '../inp.latte', class: 'tw-mb-[1.2rem]', form: $form, name: name, required: true, validate: true}
					{include '../inp.latte', class: 'tw-mb-[1.2rem]', form: $form, name: userAnimalTypeId, required: true, validate: true}
				</div>
				{ifset $userAnimal->libraryImage}
					<p class="tw-mb-0">
						{php $img = $userAnimal->libraryImage->getSize('sm')}
						<img loading="lazy" src="{$img->src}" alt="" width="100" />
						<a n:href="deleteImage!" class="f-avatar__btn f-avatar__btn--delete js-confirm" data-target="#popup-remove-pet-image">
							{('cross')|icon}
							<span class="u-vhide">
								{_remove}
							</span>
						</a>
					</p>
				{else}
					{include '../inp.latte', class: 'tw-mb-[1.2rem]', form: $form, name: avatar, required: true, validate: true}
				{/ifset}

				<p class="tw-mb-0">
					<button class="btn btn--lg" n:name="save">
						<span class="btn__text">
							{_'btn_save'}
						</span>
					</button>
				</p>
				{ifset $userAnimal}
				<p class="tw-mb-0">
					<a n:href="delete!" class="f-avatar__btn f-avatar__btn--delete js-confirm" data-target="#popup-remove-pet-image">
							{_animal_remove}
					</a>
				</p>
				{/ifset}
			</div>
		</div>
	{/form}


{/snippet}
