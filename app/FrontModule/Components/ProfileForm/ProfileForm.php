<?php declare(strict_types = 1);

/** @noinspection PhpRedundantCatchClauseInspection */

/** @noinspection PhpUnusedParameterInspection */

namespace App\FrontModule\Components\ProfileForm;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\Exceptions\LogicException;
use App\Exceptions\UserException;
use App\Model\Form\CommonFormFactory;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\NewsletterEmail\NewsletterEmailModel;
use App\PostType\Page\Model\Orm\Tree;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserModel;
use App\Model\TranslatorDB;
use Nette\Application\AbortException;
use Nette\Application\UI;
use Nette\Application\UI\Form;
use Nette\Utils\ArrayHash;
use App\FrontModule\Components\HasError500Catcher;
use Throwable;
use Nette\Forms\Control;

final class ProfileForm extends UI\Control
{

	use HasError500Catcher;

	const CA_ADDRESS_REQUIRED_FIELDS = [
		'ca_firstname',
		'ca_lastname',
		'ca_street',
		'ca_city',
		'ca_zip',
	];

	public function __construct(
		private readonly Tree $object,
		private readonly User $user,
		private readonly UserModel $userModel,
		private readonly MutationHolder $mutationHolder,
		private readonly NewsletterEmailModel $newsletterEmailModel,
		private readonly TranslatorDB $translator,
		private readonly MessageForFormFactory $messageForFormFactory,
		private readonly CommonFormFactory $commonFormFactory,

	) {}

	public function render(): void
	{
		try {
			$this->template->setTranslator($this->translator);

			$this->template->customAddress = $this->user->customAddress;
			$this->template->object = $this->object;
			$this->template->userEntity = $this->user;
			$this->template->caAddressRequiredFileds = self::CA_ADDRESS_REQUIRED_FIELDS;

			$this->template->render(__DIR__ . '/profileForm.latte');

		} catch (Throwable $e) {
			/** @noinspection PhpUnhandledExceptionInspection */
			$this->handleRenderError500($e);
		}
	}

	/**
	 * Edit form factory.
	 *
	 * @return Form
	 */
	protected function createComponentForm(): Form
	{
		$form = $this->commonFormFactory->create();

		$form->addHidden('id', $this->user->id);
		$form->addText('firstname', 'form_label_firstname')->setRequired();
		$form->addText('lastname', 'form_label_lastname')->setRequired();
		$form->addEmail('email', 'form_label_email')
			->setRequired()
			->setDisabled();

		$states = $this->mutationHolder->getMutation()->states->toCollection()->fetchPairs('id', 'name');
 		$phonePrefixBaseCountry = $this->mutationHolder->getMutation()->states->toCollection()->fetch()->code;

		$form->addText('phone', 'form_label_phone')->setRequired();
		$form->addText('street', 'form_label_street')->setRequired();
		$form->addText('city', 'form_label_city')->setRequired();
		$form->addText('zip', 'form_label_zip')->setRequired();
		$form->addSelect('state', 'form_label_state', $states)->setRequired();

		$form->addCheckbox('companyTab');
		if (!empty($this->user->company)) {
			$form['companyTab']->setDefaultValue(true);
		}
		/** @var Control $companyTabControl */
		$companyTabControl = $form['companyTab'];  // @phpstan-ignore-line

		$form->addText('ic', 'form_label_ic')
			->addRule(Form::MaxLength, null, 20)
			->addConditionOn($companyTabControl, Form::Filled)
			->setRequired();
		$form->addText('company', 'form_label_company')
			->addConditionOn($companyTabControl, Form::Filled)
			->setRequired();;
		$form->addText('dic', 'form_label_dic')
			->addRule(Form::MaxLength, null, 20);

		$form->addCheckbox('isNewsletter', $this->translator->translate('form_label_newsletter'))->setTranslator(null);

		// vytvorim prvky podle zadanych udaju
		/*if ($this->user->customAddress) {
			foreach ($this->user->customAddress as $k => $i) {
				bdump($i);
				$form->addCheckbox('ca_isRemove_' . $k, 'form_label_remove_address');
				$form->addText('ca_firstname_' . $k, 'form_label_firstname')->setDefaultValue($i->firstname);
				$form->addText('ca_lastname_' . $k, 'form_label_lastname')->setDefaultValue($i->lastname);
				$form->addText('ca_company_' . $k, 'form_label_company')->setDefaultValue($i->company);
				$form->addText('ca_phone_' . $k, 'form_label_phone')->setDefaultValue($i->phone)->setHtmlAttribute('data-baseCountry', $phonePrefixBaseCountry);
				$form->addText('ca_street_' . $k, 'form_label_street')->setDefaultValue($i->street);
				$form->addText('ca_city_' . $k, 'form_label_city')->setDefaultValue($i->city);
				$form->addText('ca_zip_' . $k, 'form_label_zip')->setDefaultValue($i->zip);
				$form->addSelect('ca_state_' . $k, 'form_label_state', $states);

				if (isset($states[$i->state])) {
					$form['ca_state_' . $k]->setDefaultValue($i->state);
				}
			}
		}

		// pridam jeden prvek pro vlozeni novych udaju
		$form->addCheckbox('ca_isNew', 'form_label_add_address');
		$form->addText('ca_firstname', 'form_label_firstname');
		$form->addText('ca_lastname', 'form_label_lastname');
		$form->addText('ca_company', 'form_label_company');
		$form->addText('ca_phone', 'form_label_phone')->setHtmlAttribute('data-baseCountry', $phonePrefixBaseCountry);
		$form->addText('ca_street', 'form_label_street');
		$form->addText('ca_city', 'form_label_city');
		$form->addText('ca_zip', 'form_label_zip');
		$form->addSelect('ca_state', 'form_label_state', $states);
*/
		$defaultData = $this->user->toArray();

		if (!empty($defaultData['state'])) {
			if (!isset($states[$defaultData['state']->id])) {
				unset($defaultData['state']);
			} else {
				$defaultData['state'] = $defaultData['state']->id;
			}
		}

		if ($this->newsletterEmailModel->isSubscribedUser($this->user->email, $this->mutationHolder->getMutation())) {
			$defaultData['isNewsletter'] = true;
		}

		$form->setDefaults($defaultData);
		$form->addSubmit('save', 'btnSave');

		$form->onValidate[] = [$this, 'formValidate'];
		$form->onError[] = [$this, 'formError'];
		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formValidate(UI\Form $form, ArrayHash $values): void
	{
		$valuesAll = (array)$form->getHttpData();

		// nova dodaci adresa
		/*if (!empty($valuesAll['ca_isNew'])) {
			foreach (self::CA_ADDRESS_REQUIRED_FIELDS as $caKey) {
				if (empty($valuesAll[$caKey])) {
					$form->addError('form_error');
					$form[$caKey]->addError('form_error');
				}
			}
		}

		// stavajici dodaci adresy
		if ($this->user->customAddress) {
			foreach ($this->user->customAddress as $k => $i) {
				if (empty($valuesAll['ca_isRemove_' . $k])) { // adresu nechce smazat = validujeme
					foreach (self::CA_ADDRESS_REQUIRED_FIELDS as $caKey) {
						$caKey .= '_' . $k;

						if (empty($valuesAll[$caKey])) {
							$form->addError('form_error');
							$form[$caKey]->addError('form_error');
						}
					}
				}
			}
		}*/
	}

	public function formError(Form $form): void
	{
		bd($form->getErrors());

		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	/**
	 * @param Form $form
	 * @param ArrayHash $values
	 * @throws AbortException
	 */
	public function formSucceeded(Form $form, ArrayHash $values): void
	{
		$valuesAll = (array) $form->getHttpData();

		try {
			if ($this->user->id !== intval($values['id'])) {
				throw new LogicException('Unknown user' . $values['id']);
			}

			try {
				if (!empty($valuesAll['isNewsletter'])) {
					$this->newsletterEmailModel->subscribeUser($this->user, $this->mutationHolder->getMutation());
				} else {
					$this->newsletterEmailModel->unsubscribeUser($this->user, $this->mutationHolder->getMutation());
				}
			} catch (UserException $e) {
				// possibly already subscribed/unsubscribed
			}

			$newAddresses = [];
			// aktu vlozene adresy
			/*if ($this->user->customAddress) {
				foreach ($this->user->customAddress as $k => $i) {
					if (!empty($valuesAll['ca_isRemove_' . $k])) { // adresu chce smazat = neukladame
						continue;
					}
					$newAddressLine = [];

					// form hodnoty by mely obsahovat vsechny pole
					$newAddressLine['firstname'] = $valuesAll['ca_firstname_' . $k];
					$newAddressLine['lastname'] = $valuesAll['ca_lastname_' . $k];
					$newAddressLine['company'] = $valuesAll['ca_company_' . $k];
					$newAddressLine['phone'] = $valuesAll['ca_phone_' . $k];
					$newAddressLine['street'] = $valuesAll['ca_street_' . $k];
					$newAddressLine['city'] = $valuesAll['ca_city_' . $k];
					$newAddressLine['zip'] = $valuesAll['ca_zip_' . $k];
					$newAddressLine['state'] = $valuesAll['ca_state_' . $k];
					$newAddresses[] = $newAddressLine;
				}
			}

			$newAddressLine = [];
			bd($valuesAll);
			if (!empty($valuesAll['ca_isNew'])) {
				$newAddressLine['firstname'] = $valuesAll['ca_firstname'];
				$newAddressLine['lastname'] = $valuesAll['ca_lastname'];
				$newAddressLine['company'] = $valuesAll['ca_company'];
				$newAddressLine['phone'] = $valuesAll['ca_phone'];
				$newAddressLine['street'] = $valuesAll['ca_street'];
				$newAddressLine['city'] = $valuesAll['ca_city'];
				$newAddressLine['zip'] = $valuesAll['ca_zip'];
				$newAddressLine['state'] = $valuesAll['ca_state'];
				$newAddresses[] = $newAddressLine;
			}

			bd($newAddresses);
			$valuesAll['customAddress'] = $newAddresses;*/

			// nepotrebujeme hesla
			unset($valuesAll['email']);
			unset($valuesAll['isNewsletter']);
			unset($valuesAll['password']);
			unset($valuesAll['passwordVerify']);

			$this->userModel->save($this->user, $valuesAll, $this->user->id);

			$this->flashMessage('form_profil_ok', 'ok');


		} catch (Throwable $e) {
			$this->flashMessage('Error', 'error');
		}

		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		} else {
			$this->presenter->redirect('this');
		}
	}


	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

}
