{snippet form}

	{form form class: 'tw-mb-[2.4rem] md:tw-mb-[3.2rem] max-md:tw-pt-[1.2rem] tw-border-solid tw-border-tile tw-border-l-0 tw-border-b-0 tw-border-r-0 md:tw-border-none', autocomplete: 'off', novalidate: "novalidate"}
		{control messageForForm, $flashes, $form}
		<h2 class="h3 tw-mb-[1.2rem]">{_'title_personal_info'}</h2>

		<div class="b-user-address md:tw-border-solid md:tw-border-tile md:tw-border-[0.1rem] md:tw-rounded-xl md:tw-p-[4rem_6rem]">
			<div class="u-maw-4-12">
				<div data-controller="address-suggest">
					{include '../inp.latte', class: 'tw-mb-[1.2rem]', form: $form, name: firstname, required: true, validate: true}
					{include '../inp.latte', class: 'tw-mb-[1.2rem]', form: $form, name: lastname, required: true, validate: true}
					{include '../inp.latte', class: 'tw-mb-[1.2rem]', form: $form, name: email, disabled: true}
					{include '../inp.latte', class: 'tw-mb-[1.2rem]', form: $form, name: phone, type: 'tel', required: true, validate: true}
					{include '../inp.latte', class: 'tw-mb-[1.2rem]', form: $form, name: street, inpClass: 'smartform-street-and-number', validate: true}
					{include '../inp.latte', class: 'tw-mb-[1.2rem]', form: $form, name: city, inpClass: 'smartform-city', validate: true}

					<div class="tw-mb-[2.4rem]">
						<div class="grid">
							<div class="grid__cell size--3-12@sm">
								{include '../inp.latte', class: 'tw-mb-0', form: $form, name: zip, inpClass: 'smartform-zip', validate: true}
							</div>
							<div class="grid__cell size--9-12@sm">
								{include '../inp.latte', class: 'tw-mb-0', form: $form, name: state, validate: true}
							</div>
						</div>
					</div>
				</div>

				<div n:class="f-open, $form['companyTab']->value ? is-open, 'tw-mb-[2.4rem]'" data-controller="toggle-class">
					<p class="tw-mb-[1.2rem]">
						<label class="inp-item inp-item--checkbox">
							<input n:name="companyTab" value="1" class="inp-item__inp" data-action="change->toggle-class#toggleInp">
							<span class="inp-item__text">{_'title_company_info'}</span>
						</label>
					</p>
					<div class="f-open__box">
						{include '../inp.latte', class: 'tw-mb-[1.2rem]', form: $form, name: 'company', validate: true}
						{include '../inp.latte', class: 'tw-mb-[1.2rem]', form: $form, name: 'ic', validate: true}
						{include '../inp.latte', class: 'tw-mb-[1.2rem]', form: $form, name: 'dic', validate: true}
					</div>
				</div>

				<h2 class="h3 tw-mb-[1.2rem]">{_'title_user_other'}</h2>
				{include '../inp.latte', class: 'tw-mb-[1.2rem]', form: $form, name: 'isNewsletter', validate: true}
				<p class="tw-text-help tw-text-[1.4rem] tw-mb-0">{_"user_newsletter_text"}</p>

				<p class="tw-mb-0">
					<button class="btn btn--lg" n:name="save">
						<span class="btn__text">
							{_'btn_save'}
						</span>
					</button>
				</p>
			</div>
		</div>
	{/form}


{/snippet}
