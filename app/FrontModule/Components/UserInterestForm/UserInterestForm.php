<?php

declare(strict_types=1);

namespace App\FrontModule\Components\UserInterestForm;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\Model\Email\CommonFactory;
use App\Model\Orm\User\UserModel;
use App\Model\Orm\UserInterest\UserInterest;
use App\Model\Orm\UserInterest\UserInterestRepository;
use App\Model\Time\CurrentDateTimeProvider;
use App\PostType\Page\Model\Orm\Tree;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserRepository;
use App\Model\TranslatorDB;
use Nette\Application\AbortException;
use Nette\Application\UI;
use Nette\Utils\ArrayHash;
use App\Exceptions\LogicException;
use App\Model\Mutation\MutationHolder;

final class UserInterestForm extends UI\Control
{


	public function __construct(
		private readonly Tree $object,
		private readonly User $user,
		private readonly UserModel $userModel,
		private readonly UserInterestRepository $userInterestRepository,
		private readonly CurrentDateTimeProvider $currentDateTimeProvider,
		private readonly TranslatorDB $translator,
		private readonly MessageForFormFactory $messageForFormFactory,
	) {}

	public function render(): void
	{
		$this->template->setTranslator($this->translator);

		$this->template->object = $this->object;
		$this->template->userEntity = $this->user;

		$this->template->render(__DIR__ . '/userInterestForm.latte');
	}

	protected function createComponentForm(): UI\Form
	{
		$form = new UI\Form;
		$form->setTranslator($this->translator);

		$interests = $this->userInterestRepository->getInterests()->fetchPairs("uid", "name");
		$userInterests = json_decode($this->user->interestsJson ?? '', true);

		$defaultValues = [];
		foreach ($interests as $uid => $name) {
			if (!empty($uid) && isset($userInterests[$uid])) {
				$defaultValues[] = $uid;
			}
		}

		$form->addCheckboxList('interests', '', $interests)
			->setDefaultValue($defaultValues);

		$form->addSubmit('save', 'btnSave');

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];

		return $form;
	}

	public function formError(UI\Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	/**
	 * @throws LogicException
	 * @throws UI\InvalidLinkException
	 * @throws AbortException
	 */
	public function formSucceeded(UI\Form $form, ArrayHash $values): void
	{
		try {
			$selectedInterests = $values->interests;

			$this->saveUserInterests($values->interests);

			$this->flashMessage('form_profil_ok', 'ok');

		} catch (\Throwable $e) {
			$this->flashMessage('Error', 'error');
		}

		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		} else {
			$this->presenter->redirect('this');
		}
	}


	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

	private function saveUserInterests(array $selectedInterests): void
	{
		$changed = false;
		$validInterests =[];

		$existingInterests = (array)$this->user->interests;

		foreach ($selectedInterests as $interestUid) {
			$validInterests[$interestUid] = $interestUid;
			if (!isset($existingInterests[$interestUid])) {
				$changed = true;
				$existingInterests[$interestUid] = $this->currentDateTimeProvider->getCurrentDateTime();
			}
		}

		foreach ($existingInterests as $uid => $interestDate) {
			if (!in_array($uid, $validInterests, true)) {
				$changed = true;
				unset($existingInterests[$uid]);
			}
		}

		if ($changed) {
			$data = [
				'interests' => $existingInterests,
				'interestsUpdated' => $this->currentDateTimeProvider->getCurrentDateTime(),
			];

			$this->userModel->save($this->user, $data, $this->user->id);
		}
	}
}
