<?php


declare(strict_types = 1);

namespace App\FrontModule\Components\ProductList;

use App\Model\Consent\MarketingConsent;
use App\Model\LastVisitedProduct;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;
use App\Model\Orm\Traits\HasStaticCache;
use App\Model\Orm\User\UserModel;
use App\Model\Security\User;
use App\Model\ShoppingCart\ShoppingCartInterface;
use Nextras\Dbal\Utils\DateTimeImmutable;

final class ProductListInterestedFactory
{

	use HasStaticCache;

	public function __construct(
		private readonly ProductListFactory $productListFactory,
		private readonly UserModel $userModel,
		private readonly MarketingConsent $marketingConsent,
		private readonly ShoppingCartInterface $shoppingCart,
		private readonly User $userSecurity,
		private readonly LastVisitedProduct $lastVisitedProduct,
	)
	{
	}

	public function create(State $state, PriceLevel $priceLevel, ?\App\Model\Orm\User\User $userEntity, ?DateTimeImmutable $ordersFrom = null, ?DateTimeImmutable $relatedOrderedFrom = null, int $limit = 21): ProductList
	{
			$cartProductIds = $relatedProductIds = $orderedProductIds = [];
			if ($this->marketingConsent->isPersonalizationGranted()) {
				$orderedProductIds = $this->userModel->findOrderedProductIds($this->userSecurity, $ordersFrom);
				$cartProductIds    = $this->shoppingCart->getProductsIds();
				$relatedProductIds = $this->userModel->findRelatedOrderedProducts(
					productIds: array_merge(
						$orderedProductIds,
						$this->lastVisitedProduct->getProductIds()
					),
					orderFrom: ($relatedOrderedFrom ?? (new \DateTimeImmutable())->modify('-1 year'))
				);
			}

			return $this->productListFactory->create($state, $priceLevel, ProductList::TYPE_INTERESTED, $userEntity)
				->setIncludeProductIds(array_keys($relatedProductIds))
				->setOrderedProductIds($orderedProductIds)
				->setCartProductIds($cartProductIds)
				->setLimit($limit)
				->setAppendBestselling();
	}

}
