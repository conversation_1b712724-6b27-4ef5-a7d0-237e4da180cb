<?php declare(strict_types = 1);

namespace App\FrontModule\Components\ProductList;

use App\Event\ProductListView;
use App\FrontModule\Components\ProductBox\ProductBox;
use App\FrontModule\Components\ProductBox\ProductBoxFactory;
use App\FrontModule\Components\ProductList\SetupCreator\SetupCreator;
use App\FrontModule\Components\ProductList\SetupCreator\SetupCreatorRegistry;
use App\Model\BucketFilter\Result;
use App\Model\BucketFilter\Sort;
use App\Model\Currency\CurrencyHelper;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\RoutableEntity;
use App\Model\Orm\State\State;
use App\Model\Orm\User\User;
use App\Model\Setup;
use App\Model\TagManager\GTM\Tag\GTMViewItemList;
use App\Model\TranslatorDB;
use Closure;
use Nette\Application\UI;
use Nette\Application\UI\Multiplier;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Utils\Json;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;

/**
 * @property-read DefaultTemplate $template
 */
final class ProductList extends UI\Control
{

	public const TYPE_NEWS = 'news';
	public const TYPE_BESTSELLERS = 'bestsellers';
	public const TYPE_UPCOMMING = 'upcoming';
	public const TYPE_INTERESTED = 'interested';
	public const TYPE_BESTSELLING = 'bestselling';
	public const TYPE_CERTIFICATES = 'certificates';
	public const TYPE_LAST_VISITED = 'lastVisited';

	private Mutation $mutation;

	private iterable $products = [];

	private int $productCount = 0;

	private int $limit = 5;

	private int $offset = 0;

	private bool $lazyLoad = false;

	private bool $showTitle = true;

	private ?string $title = null;

	private array $titleParameters = [];

	private ?array $includeProductIds = null;

	private ?array $excludeProductIds = null;

	private ?array $orderedProductIds = null;

	private ?array $cartProductIds = null;

	private bool $appendBestselling = false;

	private bool $appendBestsellingWithExclude = false;

	private string $templateFile = __DIR__ . '/productList.latte';

	private array $templateParameters = [];

	private array $onElasticItemList = [];

	private array $onSortItem = [];

	private array $onBestsellingElasticItemList = [];

	private ?RoutableEntity $object = null;

	private SetupCreator $setupCreator;

	public function __construct(
		private readonly string $type,
		private readonly TranslatorDB $translator,
		private readonly MutationHolder $mutationHolder,
		private readonly State $state,
		private readonly PriceLevel $priceLevel,
		private readonly SetupCreatorRegistry $setupCreatorRegistry,
		private readonly EventDispatcherInterface $eventDispatcher,
		private readonly ProductBoxFactory $productBoxFactory,
		private readonly ?User $userEntity = null,
	)
	{
		$this->mutation = $this->mutationHolder->getMutation();
		$this->onAnchor[] = $this->init(...);
	}

	public function setTitleParameters(array $titleParameters): ProductList
	{
		$this->titleParameters = $titleParameters;
		return $this;
	}

	/**
	 * @return string
	 */
	public function getTitle(): string
	{
		return $this->title ?? 'product_list_' . $this->type;
	}

	public function setObject(?RoutableEntity $object): ProductList
	{
		$this->object = $object;
		return $this;
	}


	private function init(): void
	{
		$this->setupCreator = clone $this->setupCreatorRegistry->get($this->type)
			->setSetupCreatorRegistry($this->setupCreatorRegistry)
			->setAppendBestselling($this->appendBestselling)
			->setAppendBestsellingWithExclude($this->appendBestsellingWithExclude)
			->setCartProductIds($this->cartProductIds)
			->setOrderedProductIds($this->orderedProductIds)
			->setExcludeProductIds($this->excludeProductIds)
			->setIncludeProductIds($this->includeProductIds)
			->setObject($this->object);

		foreach ($this->onElasticItemList as $callback) {
			$this->setupCreator->addOnElasticItemList($callback);
		}

		foreach ($this->onBestsellingElasticItemList as $callback) {
			$this->setupCreator->addOnBestsellingElasticItemList($callback);
		}

		foreach ($this->onSortItem as $callback) {
			$this->setupCreator->addOnSortItem($callback);
		}
		$this->setupCreator->init();

		if (!$this->lazyLoad || $this->presenter->isAjax()) {
			$items = $this->getItems($this->limit, $this->offset);
			$this->products = $items->items;
			$this->productCount = $items->totalCount;

			if ($this->productCount > 0) {

				$this->eventDispatcher->dispatch(
					new ProductListView(
						variants: $this->products,
						mutation: $this->mutation,
						state: $this->state,
						priceLevel: $this->priceLevel,
						currency: CurrencyHelper::getCurrency(),
						listId: $this->getName(),
						listName: $this->translator->translate($this->getTitle())
					)
				);
			}
		}
	}


	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->translator = $this->translator;
		$this->template->type = $this->type;
		$this->template->mutation = $this->mutation;
		$this->template->priceLevel = $this->priceLevel;
		$this->template->state = $this->state;
		$this->template->products = $this->products;
		$this->template->preInit = $this->lazyLoad;
		$this->template->templates = FE_TEMPLATE_DIR;
		$this->template->param = $this->templateParameters;

		$this->template->showTitle = $this->showTitle;
		$this->template->title = $this->getTitle();
		$this->template->titleParameters = $this->titleParameters;
		$this->template->object = $this->object;

		$event = new ProductListView(
			variants: [], //$this->products,
			mutation: $this->mutation,
			state: $this->state,
			priceLevel: $this->priceLevel,
			currency: CurrencyHelper::getCurrency(),
			listId: $this->getName(),
			listName: $this->translator->translate($this->getTitle())
		);

		$gtmEvent = new GTMViewItemList($event);
		$this->template->gtmItemEvent = Json::encode($gtmEvent->getData());

		if (!$this->lazyLoad) {
			$i = 0;
			/** @var Product $product */
			foreach ($this->products as $product) {
				$this->template->productsGtmItem[$product->id] = Json::encode(
					$product->getLocalization($this->mutation)->getGTMData(
						$product->firstVariant,
						currency: CurrencyHelper::getCurrency(),
						index: $i,
						itemListId: $this->getName(),
						itemListName: $this->translator->translate($this->getTitle()),
					)
				);
				$i++;
			}
		}

		$this->template->render($this->templateFile);
	}

	public function handleInit(): void
	{
		$this->lazyLoad = false;

		if ($this->presenter->isAjax()) {
			$this->redrawControl('productList');
			$this->presenter->redrawControl('tags');
		}
	}

	private function getItems(int $limit, int $offset): Result
	{
		return $this->setupCreator->getItems($limit, $offset);
	}

	public function getProductCount(): int
	{
		return $this->productCount;
	}

	public function setLimit(int $limit): ProductList
	{
		$this->limit = $limit;
		return $this;
	}

	public function setOffset(int $offset): ProductList
	{
		$this->offset = $offset;
		return $this;
	}

	public function setLazyLoad(bool $lazyLoad = true): ProductList
	{
		$this->lazyLoad = $lazyLoad;
		return $this;
	}

	public function setShowTitle(bool $showTitle = true): ProductList
	{
		$this->showTitle = $showTitle;
		return $this;
	}

	public function setTitle(?string $title): ProductList
	{
		$this->title = $title;
		return $this;
	}

	public function setTemplateFile(string $templateFile): ProductList
	{
		$this->templateFile = __DIR__ . '/' . $templateFile;
		return $this;
	}

	public function setTemplateParameters(array $templateParameters): ProductList
	{
		$this->templateParameters = $templateParameters;
		return $this;
	}

	public function setIncludeProductIds(?array $includeProductIds): ProductList
	{
		$this->includeProductIds = $includeProductIds;
		//$this->setupCreator->setIncludeProductIds($includeProductIds);
		return $this;
	}
	public function setExcludeProductIds(?array $excludeProductIds): ProductList
	{
		$this->excludeProductIds = $excludeProductIds;
		//$this->setupCreator->setExcludeProductIds($excludeProductIds);
		return $this;
	}

	public function setOrderedProductIds(?array $orderedProductIds): ProductList
	{
		$this->orderedProductIds = $orderedProductIds;
		//$this->setupCreator->setOrderedProductIds($orderedProductIds);
		return $this;
	}

	public function setCartProductIds(?array $cartProductIds): ProductList
	{
		$this->cartProductIds = $cartProductIds;
		//$this->setupCreator->setCartProductIds($cartProductIds);
		return $this;
	}

	public function setAppendBestselling(bool $appendBestselling = true): ProductList
	{
		$this->appendBestselling = $appendBestselling;
		//$this->setupCreator->setAppendBestselling($appendBestselling);
		return $this;
	}

	public function setAppendBestsellingWithExclude(bool $appendBestsellingWithExclude = true): ProductList
	{
		$this->appendBestsellingWithExclude = $appendBestsellingWithExclude;
		return $this;
	}

	/**
	 * @phpstan-param  Closure(array &$items): (void) $callback
	 */
	public function addOnElasticItemList(Closure $callback): ProductList
	{
		$this->onElasticItemList[] = $callback;
		return $this;
	}

	/**
	 * @phpstan-param  Closure(Sort &$sort): (void) $callback
	 */
	public function addOnSortItem(Closure $callback): ProductList
	{
		$this->onSortItem[] = $callback;
		return $this;
	}

	/**
	 * @phpstan-param  Closure(array &$items): (void) $callback
	 */
	public function addOnBestsellingElasticItemList(Closure $callback): ProductList
	{
		$this->onBestsellingElasticItemList[] = $callback;
		return $this;
	}


	/**
	 * @return Multiplier<ProductBox>
	 */
	public function createComponentProductBox(): Multiplier
	{
		$products = [];
		foreach ($this->products as $product) {
			$products[$product->id] = $product;
		}

		$setup = new Setup(
			$this->mutation,
			$this->state,
			$this->priceLevel,
			$this->userEntity,
		);
		return new Multiplier(function ($productId) use ($products, $setup) {
			$product = $products[$productId];
			assert($product instanceof Product);
			$parametersToTemplate = [
				'class' => false,
				'showAddToMyLibrary' => true,
				'listId' => $this->getName(),
				'listName' => $this->translator->translate($this->getTitle()),
				'dataAttrs' => ['gtm-event' => 'view_item_list', 'gtm-item' => $this->template->productsGtmItem[$product->id] ?? null],
			];

			return $this->productBoxFactory->create($product, null, $setup, $parametersToTemplate);
		});
	}

}
