<?php declare(strict_types = 1);

namespace App\FrontModule\Components\ProductList\SetupCreator;

final class SetupCreatorRegistry
{

	/** @var array<string, SetupCreator> */
	private array $setupCreators = [];

	/**
	 * @param SetupCreator[] $setupCreators
	 */
	public function __construct(
		array $setupCreators,
	)
	{
		foreach ($setupCreators as $setupCreator) {
			$this->setupCreators[$setupCreator->getType()] = $setupCreator;
		}
	}

	/**
	 * @return list<SetupCreator>
	 */
	public function list(): array
	{
		return array_values($this->setupCreators);
	}

	public function get(string $type, bool $throw = true): SetupCreator
	{
		return $this->setupCreators[$type] ?? throw new \InvalidArgumentException(sprintf('SetupCreator with ID "%s" not found.', $type));
	}

}
