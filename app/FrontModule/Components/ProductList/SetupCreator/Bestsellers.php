<?php declare(strict_types = 1);

namespace App\FrontModule\Components\ProductList\SetupCreator;

use App\FrontModule\Components\ProductList\ProductList;
use App\Model\BucketFilter\ElasticItem\IsInBestseller;
use App\Model\BucketFilter\Sort;

final class Bestsellers extends BaseSetupCreator
{

	public function getType(): string
	{
		return ProductList::TYPE_BESTSELLERS;
	}

	public function getElasticItemList(): array
	{
		$items = parent::getElasticItemList();
		$items[] = new IsInBestseller();
		return $items;
	}

	public function getSort(): Sort
	{
		$sort = parent::getSort();
		$sort->addByBestseller();
		return $sort;
	}

}
