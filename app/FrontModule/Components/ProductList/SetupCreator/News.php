<?php declare(strict_types = 1);

namespace App\FrontModule\Components\ProductList\SetupCreator;

use App\FrontModule\Components\ProductList\ProductList;
use App\Model\BucketFilter\ElasticItem\IsInNew;
use App\Model\BucketFilter\Sort;

final class News extends BaseSetupCreator
{

	public function getType(): string
	{
		return ProductList::TYPE_NEWS;
	}

	public function getElasticItemList(): array
	{
		$items = parent::getElasticItemList();
		$items[] = new IsInNew();
		return $items;
	}

	public function getSort(): Sort
	{
		$sort = parent::getSort();
		$sort->addByNewest();
		return $sort;
	}

}
