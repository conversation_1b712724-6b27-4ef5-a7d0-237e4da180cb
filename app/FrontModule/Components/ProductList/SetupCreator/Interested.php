<?php declare(strict_types = 1);

namespace App\FrontModule\Components\ProductList\SetupCreator;

use App\FrontModule\Components\ProductList\ProductList;
use App\Model\BucketFilter\ElasticItem\HasScore;
use App\Model\BucketFilter\ElasticItem\IsAvailable;
use App\Model\Orm\Product\Product;
use Nextras\Orm\Collection\ICollection;

final class Interested extends BaseSetupCreator
{

	public function getType(): string
	{
		return ProductList::TYPE_INTERESTED;
	}

	public function init(): void
	{
		/**
		 * @param ICollection<Product> $items
		 */
		$this->onAfterQuery[] = function (ICollection &$items, int &$itemsCount, int $limit, int $offset) {
			if ($this->appendBestselling && $itemsCount < $limit) {
				$excludeArray = [];
				$excludeArray[] = $this->includeProductIds;
				$excludeArray[] = $this->cartProductIds;
				$excludeArray[] = $this->orderedProductIds;

				if ($this->appendBestsellingWithExclude) {
					$excludeArray[] = $this->excludeProductIds;
				}

				$setupCreator = $this->setupCreatorRegistry->get(ProductList::TYPE_BESTSELLING);

				foreach ($this->onBestsellingElasticItemList as $callback) {
					$setupCreator->addOnElasticItemList($callback);
				}

				$setupCreator->setExcludeProductIds(array_merge(...$excludeArray));

				$esResult = $setupCreator->runEsQuery(($limit - $itemsCount), 0);

				$appendSearchItems = $this->productResultReader->mapResultToEntityCollection($esResult);

				$itemsCount += $esResult->count();
				/** @var ICollection<Product> $items */
				$items = $this->createCollection($items, $appendSearchItems);
			}
		};
	}

	public function getElasticItemList(): array
	{
		$items = parent::getElasticItemList();
		$items[] = new IsAvailable();
		$items[] = new HasScore(HasScore::GREATER_THAN_OR_EQUAL, 0);
		return $items;
	}

}
