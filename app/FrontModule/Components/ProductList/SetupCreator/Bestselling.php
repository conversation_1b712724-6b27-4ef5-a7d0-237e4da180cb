<?php declare(strict_types = 1);

namespace App\FrontModule\Components\ProductList\SetupCreator;

use App\FrontModule\Components\ProductList\ProductList;
use App\Model\BucketFilter\ElasticItem\HasScore;
use App\Model\BucketFilter\ElasticItem\IsAvailable;
use App\Model\BucketFilter\Sort;

final class Bestselling extends BaseSetupCreator
{

	public function getType(): string
	{
		return ProductList::TYPE_BESTSELLING;
	}

	public function getElasticItemList(): array
	{
		$items = parent::getElasticItemList();
		$items[] = new IsAvailable();
		$items[] = new HasScore(HasScore::GREATER_THAN_OR_EQUAL, 0);
		return $items;
	}

	public function getSort(): Sort
	{
		$sort = parent::getSort();
		$sort->addSoldCountHalfYear();
		return $sort;
	}

}
