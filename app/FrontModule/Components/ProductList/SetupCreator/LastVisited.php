<?php declare(strict_types = 1);

namespace App\FrontModule\Components\ProductList\SetupCreator;

use App\FrontModule\Components\ProductList\ProductList;
use App\Model\BucketFilter\Result;
use Nette\InvalidArgumentException;

final class LastVisited extends BaseSetupCreator
{

	public function getType(): string
	{
		return ProductList::TYPE_LAST_VISITED;
	}

	public function init(): void
	{
		$this->onBeforeQuery[] = function (): void {
			if ($this->includeProductIds === []) {
				throw new InvalidArgumentException('Last visited products is empty.');
			}
		};
	}

	public function getItems(int $limit, int $offset): Result
	{
		try {
			return parent::getItems($limit, $offset);
		} catch (InvalidArgumentException $e) {
			return Result::empty();
		}
	}

}
