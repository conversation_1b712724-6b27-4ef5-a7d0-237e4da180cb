<?php declare(strict_types = 1);

namespace App\FrontModule\Components\ProductList\SetupCreator;

use App\FrontModule\Components\ProductList\ProductList;
use App\Model\BucketFilter\ElasticItem\IsInPrepare;
use App\Model\BucketFilter\Sort;

final class Upcomming extends BaseSetupCreator
{

	public function getType(): string
	{
		return ProductList::TYPE_UPCOMMING;
	}

	public function getElasticItemList(): array
	{
		$items = parent::getElasticItemList();
		$items[] = new IsInPrepare();
		return $items;
	}

	public function getSort(): Sort
	{
		$sort = parent::getSort();
		$sort->addByDatePublished();
		$sort->addByOldest();
		return $sort;
	}

}
