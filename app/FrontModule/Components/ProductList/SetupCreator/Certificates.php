<?php declare(strict_types = 1);

namespace App\FrontModule\Components\ProductList\SetupCreator;

use App\FrontModule\Components\ProductList\ProductList;
use App\Model\BucketFilter\ElasticItem\IsElectronic;
use App\Model\BucketFilter\ElasticItem\IsProductTypeCertificate;

final class Certificates extends BaseSetupCreator
{

	public function getType(): string
	{
		return ProductList::TYPE_CERTIFICATES;
	}

	public function getElasticItemList(): array
	{
		$items = parent::getElasticItemList();
		$items[] = new IsElectronic();
		$items[] = new IsProductTypeCertificate();

		return $items;
	}

}
