<?php declare(strict_types = 1);

namespace App\FrontModule\Components\ProductList\SetupCreator;

use App\Model\BucketFilter\ElasticItem\HasPrice;
use App\Model\BucketFilter\ElasticItem\IsPublic;
use App\Model\BucketFilter\ElasticItem\ProductIds;
use App\Model\BucketFilter\ElasticItem\QuestionableElasticItem;
use App\Model\BucketFilter\Result;
use App\Model\BucketFilter\Sort;
use App\Model\ElasticSearch\IndexModel;
use App\Model\ElasticSearch\Product\ResultReader;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\EsIndex\EsIndex;
use App\Model\Orm\Orm;
use App\Model\Orm\Product\Product;
use App\Model\Orm\RoutableEntity;
use App\Model\Orm\Traits\HasStaticCache;
use Closure;
use Elastica\Query;
use Elastica\Query\AbstractQuery;
use Elastica\QueryBuilder;
use Elastica\ResultSet;
use Nette\InvalidArgumentException;
use Nette\Utils\Arrays;
use Nextras\Orm\Collection\ArrayCollection;
use Nextras\Orm\Collection\ICollection;
use Tracy\Debugger;
use Tracy\ILogger;

abstract class BaseSetupCreator implements SetupCreator
{

	use HasStaticCache;

	protected ?EsIndex $esIndex = null;

	protected ?array $includeProductIds = null;

	protected ?array $excludeProductIds = null;

	protected ?array $orderedProductIds = null;

	protected ?array $cartProductIds = null;

	protected bool $appendBestselling = false;

	protected bool $appendBestsellingWithExclude = false;

	protected SetupCreatorRegistry $setupCreatorRegistry;

	protected array $onBeforeQuery = [];

	protected array $onAfterQuery = [];

	protected array $onElasticItemList = [];

	protected array $onBestsellingElasticItemList = [];

	protected array $onSortItem = [];

	protected ?RoutableEntity $object = null;

	public function __construct(
		protected readonly IndexModel $indexModel,
		protected readonly MutationHolder $mutationHolder,
		protected readonly ResultReader $productResultReader,
		protected readonly Orm $orm,
	)
	{
	}

	abstract public function getType(): string;

	public function init(): void
	{
	}

	public function getSort(): Sort
	{
		$sort = new Sort($this->getType());
		foreach ($this->onSortItem as $callback) {
			$callback($sort);
		}
		return $sort;
	}

	public function getElasticItemList(): array
	{
		$items = [];
		$items[] = new IsPublic();
		$items[] = new HasPrice($this->orm->priceLevel->getDefault(), $this->orm->state->getDefault($this->mutationHolder->getMutation()));

		if (($includeProductIds = $this->includeProductIds) !== null) {
			$items[] = new ProductIds($includeProductIds);
		}

		foreach ($this->onElasticItemList as $callback) {
			$callback($items);
		}

		return $items;
	}

	public function getNotElasticItemList(): array
	{
		$items = [];
		if (($excludeProductIds = $this->excludeProductIds) !== null && $excludeProductIds !== []) {
			$items[] = new ProductIds($excludeProductIds);
		}

		return $items;
	}

	public function getItems(int $limit, int $offset): Result
	{
		Arrays::invoke($this->onBeforeQuery, $limit, $offset);
		try {
			$arguments = func_get_args();
			//$arguments[] = spl_object_id($this);
			$arguments[] = $this->object;

			return $this->loadCache($this->createCacheKey('product_list_items' . $this->getType(), ...$arguments), function () use ($limit, $offset) {
				$elasticResult = $this->runEsQuery($limit, $offset);

				/** @var ICollection<Product> $items */
				$items = $this->productResultReader->mapResultToEntityCollection(
					result: $elasticResult,
					exactOrder: $this->includeProductIds,
				);
				$itemsCount = $elasticResult->count();
				$itemsTotalCount = $elasticResult->getTotalHits();

				foreach ($this->onAfterQuery as $callback) {
					$callback($items, $itemsCount, $limit, $offset);
				}

				return Result::from($items, $itemsCount, $itemsTotalCount);
			});
		} catch (\Throwable $e) {
			bdump($e);
			Debugger::log($e, ILogger::EXCEPTION);
			return Result::empty();
		}
	}

	public function setObject(?RoutableEntity $object): static
	{
		$this->object = $object;
		return $this;
	}

	/**
	 * @param ICollection<Product> ...$collections
	 * @return ArrayCollection<Product>
	 */
	protected function createCollection(ICollection ...$collections): ArrayCollection
	{
		$results = array_map(fn(ICollection $collection) => $collection->fetchAll(), $collections);
		return new ArrayCollection(array_merge(...$results), $this->orm->product);
	}

	public function runEsQuery(int $limit, int $offset): ResultSet
	{
		$query = new Query();
		$query->setFrom($offset);
		$query->setSize($limit);

		// add sorting to es query
		foreach ($this->getSort()->getSentences() as $key => $direction) {
			$query->addSort([
				$key => $direction,
			]);
		}

		$boolQuery = $this->getBaseQuery();
		$query->setQuery($boolQuery);

		if ($this->getEsIndex() === null) {
			throw new InvalidArgumentException('ES index not exists');
		}

		return $this->indexModel->getIndex($this->esIndex)->search($query);
	}

	public function runOrmQuery(int $limit, int $offset): ICollection
	{
		$includeProductIds = $this->includeProductIds;
		if (($excludeProductList = $this->excludeProductIds) !== null) {
			foreach ($excludeProductList as $productId) {
				if (($keyToDelete = array_search($productId, $includeProductIds)) !== false) {
					unset($includeProductIds[$keyToDelete]);
				}
			}
			$includeProductIds = array_values($includeProductIds);
		}

		return $this->orm->product->findFilteredProducts($includeProductIds)->limitBy($limit, $offset);
	}

	private function getEsIndex(): ?EsIndex
	{
		if ($this->esIndex === null) {
			$this->esIndex = $this->orm->esIndex->getProductLastActive($this->mutationHolder->getMutation());
		}

		return $this->esIndex;
	}

	private function getBaseQuery(): AbstractQuery
	{
		$b = new QueryBuilder();
		$boolQuery = $b->query()->bool();

		$baseQueryBuilder = new QueryBuilder();
		$baseQuery = $baseQueryBuilder->query()->bool();

		// Add must conditions to ES query
		/** @var QuestionableElasticItem $baseItem */
		foreach ($this->getElasticItemList() as $baseItem) {
			$condition = $baseItem->getCondition();
			if ($condition !== null) {
				$baseQuery->addMust($baseItem->getCondition());
			}
		}

		$boolQuery->addMust($baseQuery);

		// Add must not condition to ES query
		/** @var QuestionableElasticItem $baseNotItem */
		foreach ($this->getNotElasticItemList() as $baseNotItem) {
			$condition = $baseNotItem->getCondition();
			if ($condition !== null) {
				$boolQuery->addMustNot($baseNotItem->getCondition());
			}
		}

		return $boolQuery;
	}


	public function setIncludeProductIds(?array $includeProductIds): static
	{
		$this->includeProductIds = $includeProductIds;
		return $this;
	}

	public function setExcludeProductIds(?array $excludeProductIds): static
	{
		$this->excludeProductIds = $excludeProductIds;
		return $this;
	}

	public function setOrderedProductIds(?array $orderedProductIds): static
	{
		$this->orderedProductIds = $orderedProductIds;
		return $this;
	}

	public function setCartProductIds(?array $cartProductIds): static
	{
		$this->cartProductIds = $cartProductIds;
		return $this;
	}

	public function setAppendBestselling(bool $appendBestselling = true): static
	{
		$this->appendBestselling = $appendBestselling;
		return $this;
	}

	public function setAppendBestsellingWithExclude(bool $appendBestsellingWithExclude = true): static
	{
		$this->appendBestsellingWithExclude = $appendBestsellingWithExclude;
		return $this;
	}

	public function setSetupCreatorRegistry(SetupCreatorRegistry $setupCreatorRegistry): static
	{
		$this->setupCreatorRegistry = $setupCreatorRegistry;
		return $this;
	}

	public function addOnElasticItemList(Closure $callback): static
	{
		$this->onElasticItemList[] = $callback;
		return $this;
	}

	public function addOnBestsellingElasticItemList(Closure $callback): static
	{
		$this->onBestsellingElasticItemList[] = $callback;
		return $this;
	}

	public function addOnSortItem(Closure $callback): static
	{
		$this->onSortItem[] = $callback;
		return $this;
	}

}
