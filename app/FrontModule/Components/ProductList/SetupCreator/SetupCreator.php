<?php declare(strict_types = 1);

namespace App\FrontModule\Components\ProductList\SetupCreator;

use App\Model\BucketFilter\ElasticItem\QuestionableElasticItem;
use App\Model\BucketFilter\Result;
use App\Model\BucketFilter\Sort;
use App\Model\Orm\Product\Product;
use App\Model\Orm\RoutableEntity;
use Closure;
use Elastica\ResultSet;
use Nextras\Orm\Collection\ICollection;

interface SetupCreator
{

	public function init(): void;
	public function getType(): string;

	public function getSort(): Sort;

	/**
	 * @return QuestionableElasticItem[]
	 */
	public function getElasticItemList(): array;

	/**
	 * @return QuestionableElasticItem[]
	 */
	public function getNotElasticItemList(): array;

	public function runEsQuery(int $limit, int $offset): ResultSet;

	/**
	 * @return ICollection<Product>
	 */
	public function runOrmQuery(int $limit, int $offset): ICollection;

	public function getItems(int $limit, int $offset): Result;

	public function setIncludeProductIds(?array $includeProductIds): static;

	public function setObject(?RoutableEntity $object): static;
	public function setExcludeProductIds(?array $excludeProductIds): static;
	public function setOrderedProductIds(?array $orderedProductIds): static;
	public function setCartProductIds(?array $cartProductIds): static;
	public function setAppendBestselling(bool $appendBestselling = true): static;
	public function setAppendBestsellingWithExclude(bool $appendBestsellingWithExclude = true): static;
	public function setSetupCreatorRegistry(SetupCreatorRegistry $setupCreatorRegistry): static;

	/**
	 * @phpstan-param  Closure(array &$items): (void) $callback
	 */
	public function addOnElasticItemList(Closure $callback): static;

	/**
	 * @phpstan-param  Closure(array &$items): (void) $callback
	 */
	public function addOnBestsellingElasticItemList(Closure $callback): static;

	/**
	 * @phpstan-param  Closure(Sort &$sort): (void) $callback
	 */
	public function addOnSortItem(Closure $callback): static;

}
