<?php declare(strict_types = 1);

namespace App\FrontModule\Components\OrderHistory;


use App\Components\VisualPaginator\VisualPaginator;
use App\Components\VisualPaginator\VisualPaginatorFactory;
use App\Model\Orm\CardPayment\CardPayment;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Order\OrderModel;
use App\Model\Orm\Order\OrderState;
use App\Model\Orm\Order\Payment\BankTransferPaymentInformation;
use App\Model\Orm\Order\Payment\CardPaymentInformation;
use App\Model\Orm\Orm;
use App\Model\Orm\PaymentMethod\BankTransfer;
use App\Model\Orm\PaymentMethod\Card;
use App\Model\Orm\PaymentMethod\PaymentMethodRegistry;
use App\Model\Orm\RoutableEntity;
use App\Model\Orm\User\User;
use App\Model\TranslatorDB;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nextras\Orm\Collection\ICollection;

/**
 * @property-read DefaultTemplate $template
 */
final class OrderHistory extends UI\Control
{
	private const ItemsPerPage = 10;

	public function __construct(
		private readonly RoutableEntity $object,
		private readonly ?User $user,
		private readonly Mutation $mutation,
		private readonly Orm $orm,
		private readonly OrderModel $orderModel,
		private readonly TranslatorDB $translator,
		private readonly VisualPaginatorFactory $visualPaginatorFactory
	)
	{
	}

	public function beforeRender(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->mutation = $this->mutation;
		$this->template->pages = $this->mutation->pages;
		$this->template->object = $this->object;
		$this->template->translator = $this->translator;
		$this->template->templates = FE_TEMPLATE_DIR;

	}

	public function render(): void
	{
		$this->beforeRender();

		$orders = $this->orm->order->findBy(['user' => $this->user, 'state!=' => OrderState::Draft])->orderBy('id', ICollection::DESC);

		$paginator = $this['pager']->getPaginator();
		$paginator->setItemsPerPage(self::ItemsPerPage);
		$paginator->setItemCount($orders->countStored());

		$this->template->ordersCount = $paginator->getItemCount();
		$this->template->orders = $orders->limitBy($paginator->getItemsPerPage(), $paginator->getOffset());
		$this->template->showPager = true;

		$this->template->render(__DIR__ . "/orderHistory.latte");
	}

	public function renderDetail(string $hash): void
	{
		$this->beforeRender();
		$where = ['hash' => $hash];
		if ($this->user !== null) {
			$where['user'] = $this->user;
		}

		$this->template->order = $this->orm->order->getBy($where);

		$this->template->render(__DIR__ . "/orderHistoryDetail.latte");
	}


	public function handleCancel(string $hash): void
	{
		$order = $this->orm->order->getBy(['user' => $this->user, 'hash' => $hash]);
		if ($order !== null) {
			$this->orderModel->cancel($order, true);
			$this->flashMessage('order_canceled', 'error');
		}

		if (!$this->presenter->isAjax()) {
			$this->presenter->redirect('this');
		}
		$this->presenter->redrawControl();
	}

	public function handleChangeToCardPayment(string $orderHash, bool $redirectToGate = false): void
	{
		$order = $this->orm->order->getBy(['hash' => $orderHash]);

		if ($order !== null && $order->payment->information instanceof BankTransferPaymentInformation) {
			$this->orderModel->changePayment($order, Card::ID);
		}

		if (!$this->presenter->isAjax()) {
			if ($redirectToGate) {
				$this->presenter->redirect('Order:pay', ['orderId' => $order->id, 'orderHash' => $order->hash]);
			}
			$this->presenter->redirect('this');
		}
		$this->presenter->redrawControl();
	}

	public function handleChangeToBankPayment(string $orderHash): void
	{
		$order = $this->orm->order->getBy(['hash' => $orderHash]);
		if ($order !== null && $order->payment->information instanceof CardPaymentInformation) {
			$this->orderModel->changePayment($order, BankTransfer::ID);
		}

		if (!$this->presenter->isAjax()) {
			$this->presenter->redirect('this');
		}
		$this->presenter->redrawControl();
	}

	protected function createComponentPager(): VisualPaginator
	{
		$visualPaginator = $this->visualPaginatorFactory->create();
		$visualPaginator->setTranslator($this->translator);
		return $visualPaginator;
	}


}
