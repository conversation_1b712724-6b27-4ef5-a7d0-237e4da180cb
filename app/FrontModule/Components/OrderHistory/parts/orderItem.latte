{default $class = 'u-mb-lg'}
{default $showStatus = false}
{default $showPager = false}

<div class="c-orders {$class}">
	<div class="b-scroll">
		<div class="b-scroll__inner">
			<div class="c-orders__list">
				<div class="c-orders__item">
					<table class="c-orders__table">
						<thead>
						<tr>
							<td class="u-text-center">
								{_'order_number'}
							</td>
							<td class="u-text-center">
								{_'order_status'}
							</td>
							<td class="u-text-center">
								{_'order_created'}
							</td>
							<td class="u-text-center" width="150px">
								{_'order_delivery'}
							</td>
							<td class="u-text-center">
								{_'order_payment'}
							</td>
							<td class="u-text-right">
								{_'order_total_price_vat'|noescape}
							</td>
						</tr>
						</thead>
						<tbody>
						{foreach $orders as $order}
							{varType App\Model\Orm\Order\Order $order}
							<tr>
								<td class="u-text-center">
									<a href="{plink $mutation->pages->userOrderHistory $order->hash}">
										{$order->orderNumber}
									</a>
								</td>
								<td class="c-orders__status-cell">
									<span n:if="$showStatus" class="c-orders__status c-orders__status--{$order->state->value}">
										{_'order_status_' . $order->state->value}
									</span>
								</td>
								<td class="u-text-center">
									{$order->placedAt|date:'j. n. Y'}
								</td>
								<td class="u-text-center">
									{$order->delivery->getName()}
								</td>
								<td class="u-text-center">
									{$order->payment->getName()}
									<span n:if="($paymentState = $order->payment->information->getState()) !== null">{_'order_payment_status_user_' . $paymentState->value|lower}</span>
								</td>
								<td class="u-text-right">
									{$order->getTotalPriceWithDeliveryVat()|money}
								</td>
							</tr>
						{/foreach}
						</tbody>
					</table>

				</div>
			</div>

		</div>
	</div>
</div>
