<?php declare(strict_types = 1);

namespace App\FrontModule\Components\InfoBox;

use Nette\Application\UI\Control;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;

/**
 * @property-read DefaultTemplate $template
 */
final class InfoBox extends Control
{

	public function __construct()
	{}

	public function render(array $args = []): void
	{
		$memoryPeak = memory_get_peak_usage(true) / 1024 / 1024;
		$this->template->memoryPeak = $memoryPeak;
		$this->template->render(__DIR__ . '/infoBox.latte');
	}

}
