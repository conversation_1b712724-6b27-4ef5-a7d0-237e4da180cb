<?php declare(strict_types = 1);

namespace App\FrontModule\Components\TopTitle;

use App\FrontModule\Components\AddToCart\AddToCartFactory;
use App\FrontModule\Components\ProductBox\ProductBox;
use App\FrontModule\Components\ProductBox\ProductBoxFactory;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\State\State;
use App\Model\Setup;
use App\Model\TranslatorDB;
use Nette\Application\UI\Control;
use Nette\Application\UI\Multiplier;
use Nette\Forms\Container;

final class TopTitle extends Control
{

	private Product $topProduct;

	public function __construct(
		private readonly Setup $setup,
		private readonly ProductLocalization $productLocalization,
		private readonly TranslatorDB $translator,
		private readonly ProductBoxFactory $productBoxFactory,
	)
	{
	}

	public function render(): void
	{
		$this->topProduct = $this->productLocalization->product;

		$this->getTemplate()->titleTop = $this->topProduct;
		$this->getTemplate()->addToCartPrefix = 'topTitle';
		$this->getTemplate()->mutation = $this->setup->mutation;
		$this->getTemplate()->priceLevel = $this->setup->priceLevel;
		$this->getTemplate()->state = $this->setup->state;
		$this->getTemplate()->templates = FE_TEMPLATE_DIR;
		$this->getTemplate()->setTranslator($this->translator);
		$this->getTemplate()->setFile(__DIR__ . '/templates/topTitle.latte');
		$this->getTemplate()->render();
	}


	public function createComponentProductBox(): ProductBox
	{
		$parametersToTemplate = [];
		return $this->productBoxFactory->create($this->topProduct, null, $this->setup, $parametersToTemplate);
	}


}
