<?php declare(strict_types = 1);

namespace App\FrontModule\Components\CartGift;

use App\FrontModule\Components\Cart\Cart;
use App\FrontModule\Presenters\BasePresenter;
use App\Model\Currency\CurrencyHelper;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;
use App\Model\Orm\User\User;
use App\Model\ShoppingCart\ShoppingCartInterface;
use App\Model\TranslateData;
use App\Model\TranslatorDB;
use App\PostType\Gift\Model\Orm\Gift\GiftLocalization;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nextras\Orm\Collection\ICollection;

/**
 * @property-read DefaultTemplate $template
 * @property-read BasePresenter $presenter
 */
final class CartGift extends UI\Control
{

	/** @var ICollection<GiftLocalization>  */
	public ICollection $gifts;

	public array $availableGifts = [];

	public int $availableGiftCount = 0;

	public null|false|TranslateData $availableGiftText = false;

	public function __construct(
		private readonly TranslatorDB $translator,
		private readonly Mutation $mutation,
		private readonly PriceLevel $priceLevel,
		private readonly State $state,
		private readonly ?User $user,
		private readonly Orm $orm,
		private readonly ShoppingCartInterface $shoppingCart,
	)
	{
		$this->onAnchor[] = $this->init(...);
	}

	public function init(): void
	{
		$this->gifts = $this->orm->giftLocalization->findAllPublicInMutation($this->mutation, CurrencyHelper::getCurrencyCode());

		$y = 0;
		$gifts = [];
		/** @var GiftLocalization $gift */
		foreach ($this->gifts as $gift) {
			if ($gift->isActive($this->shoppingCart) && $gift->isAvailable($this->shoppingCart)) {
				$this->availableGifts[] = $gift;
			} else {
				$gifts[$gift->gift->minPrice?->toFloat() ?? (100000 + $y)] = $gift;
				$y++;
			}
		}

		if ($this->availableGifts === [] && $gifts !== []) {
			/** @var GiftLocalization $firstGift */
			$firstGift               = array_shift($gifts);
			$this->availableGiftText = $firstGift->getAvailableText($this->shoppingCart);
		} elseif ($this->availableGifts !== []) {
			$this->availableGiftText = null;
		}

		$this->availableGiftCount = count($this->availableGifts);
	}

	public function render(): void
	{
		$this->template->templates = FE_TEMPLATE_DIR;
		$this->template->setTranslator($this->translator);
		$this->template->shoppingCart = $this->shoppingCart;
		$this->template->priceLevel = $this->priceLevel;
		$this->template->state = $this->state;
		$this->template->userEntity = $this->user;
		$this->template->gifts = $this->gifts;

		$this->template->render(__DIR__ . '/cartGift.latte');
	}

	public function handleAddGift(int $id): void
	{
		if (($giftLocalization = $this->orm->giftLocalization->getById($id)) !== null) {
			$this->shoppingCart->flushGifts();
			$this->shoppingCart->addGift($giftLocalization);
			$this->shoppingCart->flushCache();
		}

		assert($this->getParent() instanceof Cart);
		$this->getParent()->initCart();

		if (!$this->presenter->isAjax()) {
			$this->presenter->redirect($this->mutation->pages->cart);
		}
		$this->presenter->redrawControl();
	}

}
