{varType App\Model\ShoppingCart\ShoppingCart $shoppingCart}
{default $class = false}

<div id="gifts" n:class="c-gifts, $class">
	<div class="c-gifts__group">
		<h2 class="c-gifts__title">
			{_"title_gifts"}
		</h2>
		<div class="c-gifts__carousel embla" data-controller="embla">
			<div class="embla__viewport" data-embla-target="viewport">
				<div class="c-gifts__list grid grid--scroll embla__container">
					<div n:foreach="$gifts as $giftLocalization" n:if="$giftLocalization->isActive($shoppingCart, $userEntity)" class="c-gifts__item grid__cell">
						{include $templates.'/part/box/product-gift.latte', class: false, giftLocalization: $giftLocalization}
					</div>
				</div>
			</div>
			<button class="embla__btn embla__btn--prev" disabled="disabled" type="button" data-action="embla#prev" data-embla-target="prevButton">
				{('arrow-left')|icon}
				<span class="u-vhide">{_btn_prev}</span>
			</button>
			<button class="embla__btn embla__btn--next" disabled="disabled" type="button" data-action="embla#next" data-embla-target="nextButton">
				{('arrow-right')|icon}
				<span class="u-vhide">{_btn_next}</span>
			</button>
		</div>
	</div>
</div>

