{default $class = false}
{default $cols = false}
{default $rows = false}
{if is_string($name)}
	{default $type = isset($form[$name]) ? $form[$name]->getOption('type') : false}
	{default $label = isset($form[$name]) ? ($type == 'checkbox' ? $form[$name]->caption : $form[$name]->label->getText()) : ''}
	{default $disabled = isset($form[$name]) ? $form[$name]->isDisabled() : false}
	{default $required = isset($form[$name]) ? $form[$name]->isRequired() : false}
	{default $hasError = $form[$name]->errors ?? false}
{elseif $name instanceof Nette\Forms\Control}
	{default $type = $name->getOption('type')}
	{default $label = $type === 'checkbox' ? $name->caption : $name->label->getText()}
	{default $disabled = $name->isDisabled()}
	{default $required = $name->isRequired()}
	{default $hasError = $name->errors ?? false}
{/if}

{default $labelLang = false}
{default $inpClass = false}
{default $inpFixClass = false}
{default $labelClass = false}
{default $labelReplace = false}
{default $agreeLabel = false}
{default $validate = false}
{default $showError = false}
{default $noP = false}
{default $placeholderLang = false}
{default $password = false}
{default $tabindex = false}
{default $dataAction = false}
{default $data = []}
{default $placeholder = ''}
{default $icon = false}

{if $validate}{php $inpClass = $inpClass . ' inp-validate'}{/if}

{if is_string($placeholderLang)}{capture $placeholder}{translate}{$placeholderLang}{/translate}{/capture}{/if}
{capture $label}{if $labelLang}{translate}{$labelLang}{/translate}{else}{$label}{/if}{if $label && $type != 'checkbox'}:{/if}{if $required && $label} <span class="inp-required">*</span>{/if}{/capture}

<p n:tag="$noP ? 'span'" n:class="$hasError ? has-error, $class">
	{if $type == 'checkbox'}
		<label n:class="inp-item, inp-item--checkbox, $labelClass">
			<input n:if="!($name instanceof Nette\Forms\Control)" type="checkbox" name="{$name}" value="1"{if $form[$name]->value == 1} checked="checked"{/if} n:class="inp-item__inp, $inpClass"{if $tabindex} tabindex="{$tabindex}"{/if}{if $dataAction} data-action="{$dataAction}"{/if}>
			<input n:if="($name instanceof Nette\Forms\Control)" n:name="$name" n:class="inp-item__inp, $inpClass"{if $dataAction} data-action="{$dataAction}"{/if}>
			<span class="inp-item__text">
				{if $labelReplace}
					{$label|replace:"%link%",$labelReplace|noescape}
				{elseif $agreeLabel && $pages->personalData !== null}
					{_form_label_agree_text} <a href="{plink $pages->personalData}" data-modal='{"medium": "fetch", "modalClass": "b-modal--8-12"}' data-snippetid="snippet--content">{_form_label_agree_link}</a> <span class="inp-required">*</span>
				{else}
					{_$label|noescape}
				{/if}
			</span>
		</label>
	{elseif $type == 'radio'}
		<span n:class="inp-label, $labelClass">{$label|noescape}</span>

		<span class="inp-items">
			{foreach $form[$name]->items as $k=>$i}
				<label class="inp-items__item inp-item inp-item--radio">
					<input type="radio" name="{$name}" n:class="inp-item__inp, $inpClass" value="{$k}"{if $form[$name]->value == $k} checked="checked"{/if}{if $tabindex} tabindex="{$tabindex}"{/if}{if $dataAction} data-action="{$dataAction}"{/if}>
					<span>
						{translate}{$i|noescape}{/translate}
					</span>
				</label>
			{/foreach}
		</span>
	{else}
		<label n:name="{$name}" n:class="inp-label, $labelClass">{$label|noescape}</label>

		{if $type == 'file'}
			{capture $dataText}{_select_file}{/capture}
			{input $name class=>implode(' ', ['inp-file', $inpClass]), data-text=>$dataText, tabindex=>$tabindex}
		{elseif $type == 'select'}
			<span n:class="inp-fix, inp-fix--select, $inpFixClass">
				{input $name class=>implode(' ', ['inp-select', $inpClass]), tabindex=>$tabindex, aria-describedby=>$name.'_error'}
			</span>
			<span id="{$name.'_error'}" n:if="$validate && $showError" class="inp-error"></span>
		{elseif $type == 'tel'}
			<span n:class="inp-fix, $inpFixClass" data-controller="inp-phone">
				{input $name, type=>$type, class=>implode(' ', ['inp-text', $inpClass]), data-inp-phone-target=>'inp', placeholder=>$placeholder, tabindex=>$tabindex, aria-descibedby=>$name.'_error'}
				{if $validate || $hasError}{('cross')|icon, 'inp-fix__icon inp-fix__icon--error'}{/if}
				{if $validate}{('check')|icon, 'inp-fix__icon inp-fix__icon--ok'}{/if}
			</span>
			<span id="{$name.'_error'}" class="inp-error u-d-n">{_'error_invalid_phone'}</span>
		{elseif $type == 'textarea'}
			<span class="inp-fix">
				{input $name class=>implode(' ', ['inp-text', $inpClass]), cols=>$cols, rows=>$rows, disabled=>$disabled, placeholder=>$placeholder, tabindex=>$tabindex}
			</span>
		{else}
			{if $password}
				<span n:class="inp-fix, $inpFixClass" data-controller="password">
					{input $name class=>implode(' ', ['inp-text', $inpClass]), cols=>$cols, rows=>$rows, disabled=>$disabled, placeholder=>$placeholder, data-password-target=>"inp", tabindex=>$tabindex}
					<button type="button" class="inp-fix__link as-link" data-action="click->password#toggle">
						{('eye')|icon, 'inp-fix__icon'}
						<span class="u-vhide">
							{_"show_password"}
						</span>
					</button>
				</span>
			{else}
				<span n:class="inp-fix, $inpFixClass">
					{if $icon}{($icon)|icon, 'inp-fix__icon'}{/if}
					<input n:name="$name" class="{implode(' ', ['inp-text', $inpClass])}"
							{if $disabled} disabled="disabled"{/if}
							{if $placeholder} placeholder="{$placeholder}"{/if}
							{if $tabindex} tabindex="{$tabindex}"{/if}
							aria-describedby="{$name . '_error'}"
						  	{foreach $data as $key => $value}
								data-{$key}="{$value}"
						  	{/foreach}>
					{if $validate || $hasError}{('cross')|icon, 'inp-fix__icon inp-fix__icon--error'}{/if}
					{if $validate}{('check')|icon, 'inp-fix__icon inp-fix__icon--ok'}{/if}
				</span>
				<span id="{$name.'_error'}" n:if="$validate && $showError" class="inp-error"></span>
			{/if}
		{/if}
	{/if}
</p>
