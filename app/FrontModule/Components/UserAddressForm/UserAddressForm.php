<?php

declare(strict_types=1);

namespace App\FrontModule\Components\UserAddressForm;


use App\Event\UserUpdate;
use App\Exceptions\LogicException;
use App\FrontModule\Components\CartUserDetail\CartUserDetail;
use App\FrontModule\Components\FormMessage\FormMessage;
use App\FrontModule\Components\FormMessage\FormMessageFactory;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Orm;
use App\Model\Orm\RoutableEntity;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserModel;
use App\Model\Translator;
use App\Model\TranslatorDB;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Forms\Control;
use Nette\Forms\Form;
use Nette\Utils\ArrayHash;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Tracy\Debugger;

/**
 * @property-read DefaultTemplate $template
 */
final class UserAddressForm extends UI\Control
{
	public function __construct(
		private readonly RoutableEntity $object,
		private readonly User $user,
		private readonly Orm $orm,
		private readonly UserModel $userModel,
		private readonly MutationHolder $mutationHolder,
		private readonly Translator $translator,
		private readonly TranslatorDB $translatorDb,
		private readonly FormMessageFactory $formMessageFactory,
		private readonly EventDispatcherInterface $eventDispatcher,
	)
	{
	}

	public function render(): void
	{
		$this->template->setTranslator($this->translatorDb);

		$this->template->customAddress = $this->user->customAddress;
		$this->template->object = $this->object;
		$this->template->userEntity = $this->user;
		$this->template->translator = $this->translator;

		$this->template->render(__DIR__ . '/userAddressForm.latte');

	}

	protected function createComponentForm(): UI\Form
	{
		$form = new UI\Form;
		$form->setTranslator($this->translatorDb);

		$form->addHidden('id', $this->user->id);

		$states = $this->mutationHolder->getMutation()->states->toCollection()->fetchPairs('id', 'name');
		$phonePrefixBaseCountry = $this->mutationHolder->getMutation()->states->toCollection()->fetch()->code;

		// vytvorim prvky podle zadanych udaju
		if ($this->user->customAddress) {
			foreach ($this->user->customAddress as $k => $i) {
				$form->addCheckbox('ca_isRemove_' . $k, 'form_label_remove_address');
				/** @var Control $removeCheckboxControl */
				$removeCheckboxControl = $form['ca_isRemove_' . $k]; // @phpstan-ignore-line

				$form->addText('address_title_' . $k, 'form_label_address_title')
					->setDefaultValue($i->addressTitle??'')
					->addConditionOn($removeCheckboxControl, ~Form::Filled)
					->setRequired();

				// fakturacni
				$form->addText('inv_firstname_' . $k, 'form_label_firstname')
				     ->setDefaultValue($i->invFirstname)
				     ->addConditionOn($removeCheckboxControl, ~Form::Filled)
				     ->setRequired();

				$form->addText('inv_lastname_' . $k, 'form_label_lastname')
				     ->setDefaultValue($i->invLastname)
				     ->addConditionOn($removeCheckboxControl, ~Form::Filled)
				     ->setRequired();

				$form->addText('inv_phone_' . $k, 'form_label_phone')
				     ->setDefaultValue($i->invPhone)
				     ->setHtmlAttribute('data-baseCountry', $phonePrefixBaseCountry);

				$form->addText('inv_street_' . $k, 'form_label_street')
				     ->setDefaultValue($i->invStreet)
				     ->addConditionOn($removeCheckboxControl, ~Form::Filled)
				     ->setRequired();

				$form->addText('inv_city_' . $k, 'form_label_city')
				     ->setDefaultValue($i->invCity)
				     ->addConditionOn($removeCheckboxControl, ~Form::Filled)
				     ->setRequired();

				$form->addText('inv_zip_' . $k, 'form_label_zip')
				     ->setDefaultValue($i->invZip)
				     ->addConditionOn($removeCheckboxControl, ~Form::Filled)
				     ->setRequired();

				$form->addSelect('inv_state_' . $k, 'form_label_state', $states)
				     ->addConditionOn($removeCheckboxControl, ~Form::Filled)
				     ->setRequired();

				if (isset($i->invState, $states[$i->invState])) {
					$form['inv_state_' . $k]->setDefaultValue($i->invState);
				}

				// firma
				$form->addCheckbox('companyTab_' . $k);
				if ($i->invCompany) { // ma firmu = musim rozbalit checkbox
					$form['companyTab_' . $k]->setDefaultValue(true);
				}
				/** @var Control $companyTabControl */
				$companyTabControl = $form['companyTab_' . $k];  // @phpstan-ignore-line

				$form->addText('inv_company_' . $k, 'form_label_company')
				     ->setDefaultValue($i->invCompany ?? '')
				     ->addConditionOn($removeCheckboxControl, ~Form::Filled)
				     ->addConditionOn($companyTabControl, Form::Filled)
				     ->setRequired();

				$form->addText('inv_ic_' . $k, 'form_label_ic')->setDefaultValue($i->invIc ?? '')
				     ->addConditionOn($removeCheckboxControl, ~Form::Filled)
				     ->addConditionOn($companyTabControl, Form::Filled)
				     ->setRequired();

				$form->addText('inv_dic_' . $k, 'form_label_dic')
				     ->setDefaultValue($i->invDic ?? '');

				// dodaci
				$form->addCheckbox('deliveryTab_' . $k);
				if (!empty($i->delCity)) { // ma dodaci = musim rozbalit checkbox
					$form['deliveryTab_' . $k]->setDefaultValue(true);
				}
				/** @var Control $deliveryTabControl */
				$deliveryTabControl = $form['deliveryTab_' . $k]; // @phpstan-ignore-line

				$form->addText('del_firstname_' . $k, 'form_label_firstname')->setDefaultValue($i->delFirstname ?? '')
				     ->addConditionOn($removeCheckboxControl, ~Form::Filled)
				     ->addConditionOn($deliveryTabControl, Form::Filled)
				     ->setRequired();

				$form->addText('del_lastname_' . $k, 'form_label_lastname')->setDefaultValue($i->delLastname ?? '')
				     ->addConditionOn($removeCheckboxControl, ~Form::Filled)
				     ->addConditionOn($deliveryTabControl, Form::Filled)
				     ->setRequired();

				$form->addText('del_phone_' . $k, 'form_label_phone')
				     ->setDefaultValue($i->delPhone ?? '')
				     ->setHtmlAttribute('data-baseCountry', $phonePrefixBaseCountry);

				$form->addText('del_street_' . $k, 'form_label_street')->setDefaultValue($i->delStreet ?? '')
				     ->addConditionOn($removeCheckboxControl, ~Form::Filled)
				     ->addConditionOn($deliveryTabControl, Form::Filled)
				     ->setRequired();

				$form->addText('del_city_' . $k, 'form_label_city')->setDefaultValue($i->delCity ?? '')
				     ->addConditionOn($removeCheckboxControl, ~Form::Filled)
				     ->addConditionOn($deliveryTabControl, Form::Filled)
				     ->setRequired();

				$form->addText('del_zip_' . $k, 'form_label_zip')->setDefaultValue($i->delZip ?? '')
				     ->addConditionOn($removeCheckboxControl, ~Form::Filled)
				     ->addConditionOn($deliveryTabControl, Form::Filled)
				     ->setRequired();

				$form->addSelect('del_state_' . $k, 'form_label_state', $states)
				     ->addConditionOn($removeCheckboxControl, ~Form::Filled)
				     ->addConditionOn($deliveryTabControl, Form::Filled)
				     ->setRequired();

				if (isset($i->delState, $states[$i->delState])) {
					$form['del_state_' . $k]->setDefaultValue($i->delState ?? '');
				}

				$form->addText('del_company_' . $k, 'form_label_company')->setDefaultValue($i->delCompany ?? '');

				$form->addTextArea('address_note_' . $k, 'form_label_address_note')->setDefaultValue($i->addressNote ?? '');

				$form->addCheckbox('is_default_' . $k, 'form_label_default_address')->setDefaultValue($i->isDefault ?? false);
			}
		}

		// add one element for inserting new data - by parameter newAddress in URL can be checked
		$form->addCheckbox('ca_isNew', 'form_label_add_address');
		if ($this->presenter->getParameter('newAddress') === '1') {
			$form['ca_isNew']->setDefaultValue(true);
		}

		// section checkboxes
		$form->addCheckbox('companyTab');
		$form->addCheckbox('deliveryTab');

		$form->addText('address_title', 'form_label_address_title');

		// fakturacni
		$form->addText('inv_firstname', 'form_label_firstname');
		$form->addText('inv_lastname', 'form_label_lastname');
		$form->addText('inv_phone', 'form_label_phone')->setHtmlAttribute('data-baseCountry', $phonePrefixBaseCountry);
		$form->addText('inv_street', 'form_label_street');
		$form->addText('inv_city', 'form_label_city');
		$form->addText('inv_zip', 'form_label_zip');
		$form->addSelect('inv_state', 'form_label_state', $states);
		$form->addText('inv_company', 'form_label_company');
		$form->addText('inv_ic', 'form_label_ic');
		$form->addText('inv_dic', 'form_label_dic');

		// dodaci
		$form->addText('del_firstname', 'form_label_firstname');
		$form->addText('del_lastname', 'form_label_lastname');
		$form->addText('del_phone', 'form_label_phone')->setHtmlAttribute('data-baseCountry', $phonePrefixBaseCountry);
		$form->addText('del_street', 'form_label_street');
		$form->addText('del_city', 'form_label_city');
		$form->addText('del_zip', 'form_label_zip');
		$form->addSelect('del_state', 'form_label_state', $states);
		$form->addText('del_company', 'form_label_company');

		$form->addTextArea('address_note', 'form_label_address_note');

		$form->addCheckbox('is_default', 'form_label_default_address');

		// rules
		foreach (CartUserDetail::CA_ADDRESS_REQUIRED_FIELDS + CartUserDetail::CA_ADDRESS_DELIVERY_REQUIRED_FIELDS as $key) {
			$form[$key]->addConditionOn($form['ca_isNew'], Form::Filled)
			           ->setRequired();
		}
		foreach (CartUserDetail::DELIVERY_ADDRESS_REQUIRED_FIELDS as $key) {
			$form[$key]->addConditionOn($form['ca_isNew'], Form::Filled)
			           ->addConditionOn($form['deliveryTab'], Form::Filled)
			           ->setRequired();
		}

		foreach (['inv_company', 'inv_ic'] as $key) {
			$form[$key]->addConditionOn($form['ca_isNew'], Form::Filled)
			           ->addConditionOn($form['companyTab'], Form::Filled)
			           ->setRequired();
		}

		$form->addHidden('freeTransit', $this->user->freeTransit);
		$form->addHidden('payWithInvoice', $this->user->payWithInvoice);

		$form->addSubmit('save', 'btnSave');

		$form->onError[] = [$this, 'formError'];
		$form->onSuccess[] = [$this, 'formSucceeded'];
		return $form;
	}


	public function formError(UI\Form $form): void
	{
		/*$errorMessages = [];

		foreach ($form->getComponents() as $name => $control) {
				if ($control->hasErrors()) {
					$errorMessages[] = $control->getLabel() . ': ' . implode(', ', $control->getErrors());
				}
		}

		if (!empty($errorMessages)) {
			bd($errorMessages);
		}*/

		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	public function formSucceeded(UI\Form $form, ArrayHash $values): void
	{
		$valuesAll = (array)$form->getHttpData();
		try {

			if ($this->user->id != $values['id']) {
				throw new LogicException('Unknown user' . $values['id']);
			}

			$newAddresses = [];
			// aktu vlozene adresy
			if ($this->user->customAddress) {
				foreach ($this->user->customAddress as $k => $i) {
					$newAddress = CartUserDetail::getAddressLine($valuesAll, '_' . $k);
					$newAddress['addressTitle'] = $valuesAll['address_title_' . $k];
					$newAddress['isDefault'] = $i->isDefault ?? false;
					$newAddress['last'] = $i->last ?? false;
					$newAddresses[] = $newAddress;

					if ($k === 0) {
						$this->eventDispatcher->dispatch(
							new UserUpdate($newAddress, $this->user->email, $this->user)
						);
					}
				}
			}

			if (!empty($valuesAll['ca_isNew'])) {
				$newAddress = CartUserDetail::getAddressLine($valuesAll);
				$newAddress['addressTitle'] = $valuesAll['address_title'];
				$newAddress['isDefault'] = $valuesAll['is_default'] ?? false;
				$newAddress['last'] = false;

				//set new address as default - remove all other default addresses and sort - default is first
				if (isset($valuesAll['is_default']) && $valuesAll['is_default']) {
					$newAddress['isDefault'] = true;
					$newAddressesOrdered[0] = $newAddress;
					$newAddressKey = 1;
					foreach ($newAddresses as $k => $address) {
						$address['isDefault'] = false;
						$newAddressesOrdered[$newAddressKey] = $address;
						$newAddressKey++;
					}
					$newAddresses = $newAddressesOrdered;
				} else {
					$newAddresses[] = $newAddress;
				}

				if ($this->user->customAddress !== null && count($this->user->customAddress) === 0) {
					$this->eventDispatcher->dispatch(
						new UserUpdate($newAddress, $this->user->email, $this->user)
					);
				}
			}

			$valuesAll['customAddress'] = $newAddresses;

			// nepotrebujeme hesla
			unset($valuesAll['email']);
			unset($valuesAll['isNewsletter']);
			unset($valuesAll['password']);
			unset($valuesAll['passwordVerify']);

			$valuesAll['freeTransit'] = (bool) $valuesAll['freeTransit'];
			$valuesAll['payWithInvoice'] = (bool) $valuesAll['payWithInvoice'];

			$this->userModel->save($this->user, $valuesAll, $this->user->id);

			$this->flashMessage('form_profil_ok', 'ok');

		} catch (\Throwable $e) {
			bdump($e);
			$this->flashMessage('Error', 'error');
		}

		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		} else {
			$this->presenter->redirect('this#addresses');
		}
	}

	public function handleSetDefaultCustomAddress(int $key): void
	{
		try {
			if (isset($this->user->customAddress)) {
				if ($this->user->customAddress) {
					$newAddresses = [];
					$newAddressKey = 1;
					foreach ($this->user->customAddress as $k => $address) {
						if ($k == $key) {
							$address->isDefault = true;
							$newAddresses[0] = $address;
						} else {
							$address->isDefault = false;
							$newAddresses[$newAddressKey] = $address;
							$newAddressKey++;
						}
					}
					ksort($newAddresses);
					$this->user->customAddress = $newAddresses;
					$this->orm->user->persistAndFlush($this->user);

					$this->flashMessage('form_profil_ok', 'ok');
				}
			}

		} catch (\Exception $e) {
			Debugger::log($e, Debugger::EXCEPTION);
			$this->flashMessage('Error', 'error');
		}

		if (!$this->presenter->isAjax()) {
			$this->presenter->redirect('this#addresses');
		}
		$this->redrawControl();
	}

	public function handleDeleteCustomAddress(?int $key = null): void
	{
		try {
			if (isset($key, $this->user->customAddress)) {

				if ($this->user->customAddress) {
					$addresses = [];

					foreach ($this->user->customAddress as $k => $address) {
						if ($k == $key) {
							continue;
						}
						$addresses[] = $address;
					}
					$this->user->customAddress = $addresses;
					$this->orm->user->persistAndFlush($this->user);

					$this->flashMessage('form_profil_address_deleted_ok', 'ok');
				}
			}

		} catch (\Exception $e) {
			Debugger::log($e, Debugger::EXCEPTION);
			$this->flashMessage('Error', 'error');
		}

		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		} else {
			$this->presenter->redirect('this#addresses');
		}
	}

	public function createComponentFormMessage(): FormMessage
	{
		return $this->formMessageFactory->create();
	}
}
