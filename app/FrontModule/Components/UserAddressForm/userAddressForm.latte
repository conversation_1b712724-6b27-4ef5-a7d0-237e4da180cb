{snippet form}

	{form form autocomplete: 'off',  class: 'tw-mb-[2.4rem] md:tw-mb-[3.2rem] max-md:tw-pt-[1.2rem] tw-border-solid tw-border-tile tw-border-l-0 tw-border-b-0 tw-border-r-0 md:tw-border-none', autocomplete: 'off', novalidate: "novalidate"}
		{control formMessage, $flashes, $form}
		<h2 class="h3 tw-mb-[1.2rem]">{_'title_personal_addresses'}</h2>

		<div class="c-user-address u-maw-4-12">
			{* Adresy *}
			{if $customAddress}
				{php $showMore = count($customAddress) >= 3}
				<div class="c-address c-address--profile tw-mb-[1.2rem]" {if $showMore}data-controller="toggle-class"{/if}>
					<div n:tag-if="$showMore" class="c-address__limiter">
						<div n:foreach="$customAddress as $k => $address" class="tw-border-solid tw-border-[0.1rem] tw-border-tile tw-rounded-md tw-mb-[-0.1rem] tw-p-[1.6rem] md:tw-p-[2rem_2.8rem] tw-text-[1.3rem] md:tw-text-[1.4rem]">
							<p class="tw-flex tw-justify-between tw-mb-[0.8rem]">
								<b class="tw-text-[1.4rem] md:tw-text-[1.5rem]">{$address->addressTitle??$address->invFirstname . " " . $address->invLastname}</b>
								{if !isset($address->isDefault) || !$address->isDefault}
									<a n:href="setDefaultCustomAddress!, key:  $k" class="tw-text-[1.4rem]">{_'form_profile_address_set_default'}</a>
								{else}
									<span class="tw-flex-[0_0_auto] flag flag--green-light tw-font-bold tw-min-h-[2.2rem] tw-h-[2.2rem] tw-text-[1.1rem]">{_'form_profile_default_address'}</span>
								{/if}
							</p>

							{include '../CartUserDetail/parts/address/addressBox.latte', address: $address}

							<p class="tw-flex tw-gap-[1.2rem] tw-mb-0">
								<button type="button" class="b-user-address__link as-link u-c-help item-icon" data-controller="toggle-class" data-action="toggle-class#toggle" aria-expanded="false" data-toggle-content=".f-open--{$iterator->getCounter()}">
									{('pencil')|icon, 'item-icon__icon'}
									<span class="item-icon__text">
										{_'btn_change'}
									</span>
								</button>
								{capture $confirmMsg}
									{if empty($address->delStreet)}
										{$address->invFirstname} {$address->invLastname}, {$address->invStreet}, {$address->invZip} {$address->invCity}
									{else}
										{$address->delFirstname} {$address->delLastname}, {$address->delStreet}, {$address->delZip} {$address->delCity}
									{/if}
								{/capture}

								<a n:href="deleteCustomAddress!, key:  $k" class="u-c-help item-icon" data-controller="confirm" data-action="confirm#confirm" data-confirm-message-value="{_confirm_delete_address|replace:'%address',preg_replace('/[\t\n]/', '', $confirmMsg)}">
									{('trash')|icon, 'item-icon__icon'}
									<span class="item-icon__text">
										{_'btn_delete'}
									</span>
								</a>
							</p>

							{* Formulář pro editaci *}
							<div n:class="b-user-address, f-open, 'f-open--'.$iterator->getCounter()">
								<div class="f-open__box tw-pt-[1.2rem]">
									{include 'addressBox.latte', form: $form, nameSuffix: '_'.$k}
								</div>
							</div>
						</div>
						<p n:if="$showMore" class="c-address__more">
							<button type="button" class="item-icon as-link" data-action="toggle-class#toggle">
								<span class="item-icon__text">
									{_"btn_show_all_addresses"}
								</span>
								{('angle-down')|icon, 'item-icon__icon'}
							</button>
						</p>
					</div>
				</div>
			{/if}

			{* Přidat *}
			<div n:class="f-open, $form['ca_isNew']->value ? is-open">
				<label class="as-link tw-bg-bg tw-rounded-md [&:has(input:checked)]:tw-rounded-b-none tw-transition-[border-radius] tw-duration-300 tw-flex tw-p-[1.3rem_1.6rem] md:tw-p-[2.4rem_1.6rem] tw-text-[1.4rem]">
					<input n:name="ca_isNew" class="f-open__inp u-vhide" value="1"{if $form['ca_isNew']->value} checked{/if}>
					<span class="b-user-address__new item-icon">
						{('plus')|icon, 'item-icon__icon'}
						<span class="item-icon__text">
							{if $customAddress}
								{_'add_address_more'}
							{else}
								{_'add_address'}
							{/if}
						</span>
					</span>
				</label>

				<div class="f-open__box tw-border-[0.1rem] tw-border-solid tw-border-tile tw-border-t-0 tw-rounded-bl-md tw-rounded-br-md tw-p-[1.6rem] md:tw-p-[2rem_2.8rem]">
					<h2 class="h4 tw-mb-[1.6rem]">
						{_'invoice_address_title'}
					</h2>
					{include 'addressBox.latte', form: $form}
				</div>
			</div>
		</div>
	{/form}
{/snippet}
