<?php declare(strict_types = 1);

namespace App\FrontModule\Components\ProductBox;

use App\FrontModule\Components\AddToCart\AddToCart;
use App\FrontModule\Components\AddToCart\AddToCartFactory;
use App\FrontModule\Components\AddToMyLibrary\AddToMyLibrary;
use App\FrontModule\Components\AddToMyLibrary\AddToMyLibraryFactory;
use App\Model\DTO\Product\ProductDto;
use App\Model\DTO\Product\ProductDtoProvider;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductModel;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Setup;
use App\Model\TranslatorDB;
use Nette\Application\UI\Control;

final class ProductBox extends Control
{

	private ProductDto $productDto;

	public function __construct(
		private readonly Product $product,
		private readonly ?ProductVariant $variant,
		private readonly Setup $setup,
		private readonly array $parametersToTemplate,
		private readonly AddToCartFactory $addToCartFactory,
		private readonly AddToMyLibraryFactory $addToMyLibraryFactory,
		private readonly TranslatorDB $translator,
		private readonly ProductDtoProvider $productDtoProvider,
		private readonly ProductModel $productModel,
	)
	{
		$this->onAnchor[] = $this->init(...);
	}


	private function init(): void
	{
		$this->productDto = $this->productDtoProvider->get($this->product, $this->variant);
	}

	public function render(): void
	{
		$this->getTemplate()->templates = FE_TEMPLATE_DIR;
		$this->getTemplate()->productEntity = $this->product;
		$this->getTemplate()->productDto = $this->productDto;
		$this->getTemplate()->priceLevel = $this->setup->priceLevel;
		$this->getTemplate()->state = $this->setup->state;
		$this->getTemplate()->mutation = $this->setup->mutation;
		$this->getTemplate()->lazyLoading = false;
		$this->getTemplate()->longLangs = false;
		$this->getTemplate()->showAddToMyLibrary = false;
		$this->getTemplate()->class = '';
		$this->getTemplate()->variant = $this->variant;
		$this->getTemplate()->hasVariants = $this->product->activeVariants->count() > 1;
		$this->getTemplate()->hasLastMinute = false;

		foreach ($this->parametersToTemplate as $key => $value) {
			$this->getTemplate()->$key = $value;
		}

		if ($this->product->isCourse()) {
			$this->getTemplate()->hasLastMinute = $this->productModel->hasLastMinuteOption($this->product);
		}

		$this->getTemplate()->setTranslator($this->translator);
		$this->getTemplate()->setFile(__DIR__ . '/productBox.latte');
		$this->getTemplate()->render();
	}

	protected function createComponentAddToCart(): AddToCart
	{
		return $this->addToCartFactory->create($this->product, $this->productDto, $this->setup->state, $this->setup->priceLevel);
	}

	protected function createComponentAddToMyLibrary(): AddToMyLibrary
	{
		return $this->addToMyLibraryFactory->create($this->product, $this->setup->userEntity, $this->setup->mutation, $this->setup->priceLevel, $this->setup->state, $this->setup->mutation->getSelectedCurrency());
	}

}
