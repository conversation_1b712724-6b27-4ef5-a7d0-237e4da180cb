<?php declare(strict_types = 1);

namespace App\FrontModule\Components\Bestsellers;

use App\Event\ProductListView;
use App\FrontModule\Components\ProductBox\ProductBox;
use App\FrontModule\Components\ProductBox\ProductBoxFactory;
use App\Model\BucketFilter\BucketFilterBuilder;
use App\Model\BucketFilter\Result;
use App\Model\Currency\CurrencyHelper;
use App\Model\Orm\Product\Product;
use App\Model\Setup;
use App\Model\TranslatorDB;
use Nette\Application\UI\Control;
use Nette\Application\UI\Multiplier;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nextras\Orm\Collection\ICollection;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;

/**
 * @property-read DefaultTemplate $template
 */
final class Bestsellers extends Control
{

	private Result $bestsellersObject;

	public function __construct(
		private readonly Setup $setup,
		private readonly BucketFilterBuilder $bucketFilterBuilder,
		private readonly int $bestsellerCount,
		private readonly TranslatorDB $translator,
		private readonly ProductBoxFactory $productBoxFactory,
		private readonly EventDispatcherInterface $eventDispatcher,
	)
	{
		$this->onAnchor[] = $this->init(...);
	}


	public function init (): void
	{
		$this->bestsellersObject = $this->bucketFilterBuilder->getBestsellers($this->bestsellerCount);
	}

	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->FE_TEMPLATE_DIR = FE_TEMPLATE_DIR;
		$template->bestsellers = $this->bestsellersObject->items;

		if ($this->bestsellersObject->count > 0) {
			$this->eventDispatcher->dispatch(
				new ProductListView(
					variants: $this->bestsellersObject->items,
					mutation: $this->setup->mutation,
					state: $this->setup->state,
					priceLevel: $this->setup->priceLevel,
					currency: CurrencyHelper::getCurrency(),
					listId: 'bestsellers',
					listName: 'Nejprodávanější' //'bestsellers_title'
				)
			);
		}
		$this->template->render(__DIR__ . '/bestsellers.latte');
	}


	/**
	 * @return Multiplier<ProductBox>
	 */
	public function createComponentProductBox(): Multiplier
	{
		$products = [1 => new Product()]; // @todo ArrayList<Product|ProductVariant> $this->bestsellersObject->items->fetchPairs('id');
		$productIds = array_keys($products);

		return new Multiplier(function (string $productId) use ($products, $productIds) {

			$productId = (int) $productId;
			$parametersToTemplate = [
				'class' => false,
				'lazyLoading' => isset($productIds[0]) ? (int) $productIds[0] !== $productId : false,
				'showAddToMyLibrary' => true,
			];
			$product = $products[$productId];
			assert($product instanceof Product);
			return $this->productBoxFactory->create($product, null, $this->setup, $parametersToTemplate);
		});
	}

	public function getBestsellerIds(): array
	{
		return $this->bestsellersObject->itemIds;
	}

}
