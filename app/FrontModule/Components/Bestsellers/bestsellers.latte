{default $fetchFirst = true}

<section n:if="$bestsellers && $bestsellers->count() > 0" n:class="c-bestsellers" data-gtm-viewport-script-id-value="bestsellers" data-controller="gtm-viewport">
	<h2 class="c-bestsellers__title">
		{_bestsellers_title}
	</h2>
	<div class="c-bestsellers__box">
		<div class="c-bestsellers__carousel embla" data-controller="embla">
			<div class="embla__holder">
				<div class="embla__viewport" data-embla-target="viewport">
					<div class="c-bestsellers__grid grid grid--scroll embla__container" >
						<div n:foreach="$bestsellers as $product" class="grid__cell size--6-12 size--4-12@sm size--3-12@md">
							{control productBox.'-'.$product->id}
						</div>
					</div>
				</div>
			</div>
			<button class="embla__btn embla__btn--prev" disabled="disabled" type="button" data-action="embla#prev" data-embla-target="prevButton">
				{('arrow-left')|icon}
				<span class="u-vhide">{_btn_prev}</span>
			</button>
			<button class="embla__btn embla__btn--next" disabled="disabled" type="button" data-action="embla#next" data-embla-target="nextButton">
				{('arrow-right')|icon}
				<span class="u-vhide">{_btn_next}</span>
			</button>
		</div>
	</div>
</section>
