<?php declare(strict_types = 1);

namespace App\FrontModule\Components\MainCategories;

use App\AdminModule\Components\Tree\Tree;
use App\FrontModule\Components\Robots\Robots;
use App\Model\TranslatorDB;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\TreeRepository;
use Nette\Application\BadRequestException;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;

/**
 * @property-read DefaultTemplate $template
 */
final class MainCategories extends UI\Control
{
	private array $categoriesId = [];
	private ?int $selectedCategory = null;

	private array $categories = [];

	public function __construct(
		private readonly TranslatorDB $translator,
		private readonly TreeRepository $treeRepository,
		private readonly Robots $robots,
	) {
	}


	public function render(): void
	{
		$this->template->setTranslator($this->translator);

		$this->template->categories             = $this->categories;
		$this->template->categoriesProductCount = $this->categoriesId;
		$this->template->selectedCategory       = $this->selectedCategory;

		$this->template->render(__DIR__ . '/mainCategories.latte');
	}

	public function setCategoriesId(array $categoriesId): void
	{
		$this->categoriesId = $categoriesId;
		$this->categories = $this->treeRepository->findByIds(array_keys($this->categoriesId))->fetchPairs('id');
	}

	public function setSelectedCategory(?int $selectedCategory): void
	{
		$this->selectedCategory = $selectedCategory;

		if ($this->selectedCategory !== null) {
			$this->robots->setForceState('noindex, follow');
		}
	}

	public function getSelectedCategory(): ?CatalogTree
	{
		if($this->selectedCategory !== null) {
			return $this->treeRepository->getById($this->selectedCategory); // @phpstan-ignore-line
		}
		return null;
	}
}
