{snippet currencySelected}
	<form novalidate="novalidate" n:name="currencyToggle" data-naja data-naja-loader="body" data-naja-force-redirect>
		<nav class="m-currency" data-controller="hover toggle-class etarget">
			<p class="m-currency__btn u-mb-0">
				<button type="button" class="header-link item-icon item-icon--arrow" data-action="toggle-class#toggle" aria-expanded="false">
				<span class="item-icon__text">
					<span class="header-link__secondary">{_"header_currency"}</span> {$currencySelected}
				</span>
					{('angle-down-bold')|icon, 'item-icon__icon'}
				</button>
			</p>
			<p class="m-currency__box">
				{foreach $currencies as $currency}
					<label class="inp-item inp-item--radio">
						<input type="radio" name="currency" value="{$currency}" class="inp-item__inp" {if $currencySelected === $currency}checked{/if} data-controller="autosubmit" data-action="change->autosubmit#submitForm">
						<span class="inp-item__text">
						{$currency}
					</span>
					</label>
				{/foreach}
			</p>
			<button type="submit" class="u-js-hide as-link">{_"btn_submit"}</button>
		</nav>
	</form>
{/snippet}

