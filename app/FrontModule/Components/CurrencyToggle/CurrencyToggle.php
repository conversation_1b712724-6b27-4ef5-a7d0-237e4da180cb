<?php declare(strict_types = 1);

namespace App\FrontModule\Components\CurrencyToggle;

use App\FrontModule\Presenters\Error\ErrorPresenter;
use App\Model\Currency\CurrencyHelper;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\User\UserRepository;
use App\Model\ShoppingCart\ShoppingCartInterface;
use App\Model\TranslatorDB;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;

final class CurrencyToggle extends Control
{

	public function __construct(
		private readonly Mutation $mutation,
		private readonly TranslatorDB $translatorDB,
		private readonly ShoppingCartInterface $shoppingCart,
		private readonly UserRepository $userRepository,
	)
	{
	}

	public function render(): void
	{
		$this->getTemplate()->currencySelected = CurrencyHelper::getCurrencyCode();
		$this->getTemplate()->currencies = CurrencyHelper::CURRENCIES;
		$this->getTemplate()->setTranslator($this->translatorDB);
		$this->getTemplate()->setFile(__DIR__ . '/Templates/currencyToggle.latte');
		$this->getTemplate()->render();
	}

	protected function createComponentCurrencyToggle(): Form
	{
		$form = new Form();
		$form->setMappedType(CurrencyToggleData::class);
		$form->addHidden('path', ($this->presenter instanceof ErrorPresenter) ? ltrim($this->presenter->getHttpRequest()->getUrl()->getPath(), '/') : null)->setNullable();
		$form->addRadioList('currency', 'currency', $this->prepareCurrencyList())
			->setDefaultValue(CurrencyHelper::getCurrencyCode());

		$form->addSubmit('submit');

		$form->onSuccess[] = $this->formSuccess(...);

		return $form;
	}

	private function formSuccess(Form $form, CurrencyToggleData $formvalues): void
	{
		CurrencyHelper::setCurrencyCode($formvalues->currency);
		$this->getTemplate()->currencySelected = CurrencyHelper::getCurrencyCode();
		$this->mutation->setSelectedCurrency(CurrencyHelper::getCurrency());
		$this->shoppingCart->setCurrency(CurrencyHelper::getCurrencyCode());

		if ($this->shoppingCart->getUserEntity()) {
			$this->shoppingCart->getUserEntity()->preferredCurrency = CurrencyHelper::getCurrencyCode();
			$this->userRepository->persistAndFlush($this->shoppingCart->getUserEntity());
		}

		if ($formvalues->path !== null) {
			$this->presenter->redirectUrl($formvalues->path);
		}

		$this->presenter->redrawControl();
	}

	private function prepareCurrencyList(): array
	{
		return array_combine(CurrencyHelper::CURRENCIES, CurrencyHelper::CURRENCIES);
	}

}
