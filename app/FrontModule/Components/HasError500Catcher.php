<?php

declare(strict_types=1);

namespace App\FrontModule\Components;

use App\BasePresenter;
use App\Model\ConfigService;
use Nette\Application\Application;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Throwable;
use <PERSON>\Debugger;
use <PERSON>\ILogger;

/**
 * @property-read BasePresenter $presenter
 * @property-read DefaultTemplate $template
 */
trait HasError500Catcher
{

	private ConfigService $configService;

	public function injectConfigService(ConfigService $configService): void
	{
		$this->configService = $configService;
	}

	/**
	 * @throws Throwable
	 */
	protected function handleRenderError500(Throwable $e): void
	{
		Debugger::log($e, ILogger::EXCEPTION);

		// Debug mode + DEV: neon - catchExceptions: true = zobrazi se 500 sablona; false = zobrazi se Tracy
		if ($this->configService->isDev()) {
			$application = $this->presenter->getContext()->getByType(Application::class, false);

			if (!$application || !$application->errorPresenter) {
				throw $e;
			}
		}

		$this->template->render(FE_TEMPLATE_DIR . '/Error/500inner.latte');
	}

}
