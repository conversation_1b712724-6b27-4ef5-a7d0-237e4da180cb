<?php declare(strict_types=1);

namespace App\FrontModule\Components\OrderBenefitCardPay;

use App\FrontModule\Components\FormMessage\FormMessage;
use App\FrontModule\Components\FormMessage\FormMessageFactory;
use App\FrontModule\Presenters\BasePresenter;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\CardPayment\CardPayment;
use App\Model\Orm\CardPayment\CardPaymentProcessor;
use App\Model\Orm\CardPayment\PaymentGateway\BenefitPaymentGateway;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\Payment\CardPaymentInformation;
use App\Model\Orm\Orm;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserModel;
use App\Model\Security\Acl;
use App\Model\TranslatorDB;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Security\AuthenticationException;
use Nette\Utils\ArrayHash;


/**
 * @property-read DefaultTemplate $template
 * @property-read BasePresenter $presenter
 * @property-read UI\Control $parent
 */
final class OrderBenefitCardPay extends UI\Control
{
	private Mutation $mutation;

	public function __construct(
		private readonly Order $order,
		private readonly Orm $orm,
		private readonly TranslatorDB $translator,
		private readonly FormMessageFactory $formMessageFactory,
		private readonly CardPaymentProcessor $cardPaymentProcessor,
		MutationHolder $mutationHolder,
	)
	{
		$this->mutation = $mutationHolder->getMutation();
	}

	public function createComponentFormMessage(): FormMessage
	{
		return $this->formMessageFactory->create();
	}

	public function createComponentForm(): UI\Form
	{
		$f = new UI\Form();
		$f->setTranslator($this->translator);
		$f->addHidden('order', $this->order->id)->addRule($f::Integer, '');
		$f->addText('account', 'form_label_account')->setRequired();
		$f->addPassword('pin', 'form_label_pin')->setRequired();

		$f->addSubmit('pay', 'btn_pay_benefit');

		$f->onError[]   = $this->formError(...);
		$f->onValidate[] = $this->formValidate(...);
		$f->onSuccess[] = $this->formSuccess(...);

		return $f;
	}

	private function formError(UI\Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	private function formValidate(UI\Form $form, ArrayHash $values): void
	{

	}

	private function formSuccess(UI\Form $form, ArrayHash $values): void
	{

		$orderId = $values->order;
		unset($values->order);
		$order = $this->orm->order->getById($orderId);

		if ($order === null) {
			$form->addError('order_not_found');
		}

		$success = false;

		try {
			/** @var BenefitPaymentGateway $paymentGateway */
			$paymentGateway = $this->cardPaymentProcessor->getGateway(BenefitPaymentGateway::ID);
			$paymentGateway->createPaymentRequest($order, $values->account, $values->pin);

			$success = true;
		} catch (\Throwable $e) {
			$form->addError($e->getMessage());
		}

		assert($order->payment->information instanceof CardPaymentInformation);
		/** @var CardPayment $cardPayment */
		foreach ($order->payment->information->payments as $cardPayment) {
			$this->cardPaymentProcessor->processCheckPayment($cardPayment);
		}

		if ($success) {
			$this->presenter->redirect($this->mutation->pages->step3, ['orderHash' => $order->hash, 'orderId' => $order->id, 'paymentStatus' => 'PAID']);
		}


	}

	public function beforeRender(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->translator = $this->translator;
		$this->template->templates = FE_TEMPLATE_DIR;
	}
	public function render(): void
	{
		$this->beforeRender();

		$this->template->render(__DIR__ . '/orderBenefitCardPay.latte');
	}
}
