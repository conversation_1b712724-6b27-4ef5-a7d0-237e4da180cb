{snippet orderCreateUserForm}
	<form class="u-mb-xs" n:name="form" novalidate="novalidate" data-naja data-naja-history="off" data-naja-loader="body" data-naja-force-redirect>

		{control formMessage, flashes: $flashes, form: $form}


		{include '../inp.latte', form: $form, name: 'account', labelClass: 'u-vhide', validate: true, class: false, showError: true, placeholderLang: 'form_label_account'}
		{include '../inp.latte', form: $form, name: 'pin', labelClass: 'u-vhide', validate: true, class: false, showError: true, placeholderLang: 'form_label_pin'}

		<p class="u-mb-0">
			<button n:name="pay" type="submit" class="inp-fix__btn btn btn--secondary btn--loader">
				<span class="btn__text">
					{_'btn_pay_benefit'}
				</span>
			</button>
		</p>
	</form>
{/snippet}
