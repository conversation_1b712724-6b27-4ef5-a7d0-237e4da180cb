<?php

declare(strict_types=1);

namespace App\FrontModule\Components\AddToCart;

use App\Model\DTO\Product\ProductDto;
use App\Model\Orm\ClassEvent\ClassEvent;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\State\State;

interface AddToCartFactory
{
	public function create(
		Product $product,
		ProductDto $productDto,
		State $currentState,
		PriceLevel $priceLevel,
		?ProductVariant $productVariant = null,
		?ClassEvent $classEvent = null,
		string $type = AddToCart::TYPE_CATALOG
	): AddToCart;
}
