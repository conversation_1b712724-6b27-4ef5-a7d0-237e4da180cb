<?php declare(strict_types = 1);

namespace App\FrontModule\Components\AddToCart;


use App\Event\AddToCart as AddToCartEvent;
use App\Event\Provider\AddToCartClickedProvider;
use App\Model\Currency\CurrencyHelper;
use App\Model\DTO\Product\ProductDto;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\ClassEvent\ClassEvent;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\State\State;
use App\Model\ShoppingCart\ShoppingCartInterface;
use App\Model\TranslatorDB;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Http\Request;
use Nette\Http\Session;
use Nette\Http\SessionSection;
use Nette\Utils\ArrayHash;
use Nette\Utils\Random;
use Psr\EventDispatcher\EventDispatcherInterface;


/**
 * @property-read DefaultTemplate $template
 */
final class AddToCart extends UI\Control
{
	public const TYPE_CATALOG = 'catalog';

	public const TYPE_DETAIL = 'detail';

	private string $templateFile = 'addToCart.latte';

	private ?string $modalClass = null;

	private SessionSection $session;

	private Mutation $mutation;

	public function __construct(
		private readonly Product $product,
		private readonly ProductDto $productDto,
		private readonly ShoppingCartInterface $shoppingCart,
		private readonly TranslatorDB $translator,
		private readonly MutationHolder $mutationHolder,
		private readonly State $currentState,
		private readonly PriceLevel $priceLevel,
		private readonly Request $request,
		private readonly Session $sessionConatiner,
		private readonly EventDispatcherInterface $eventDispatcher,
		private readonly AddToCartClickedProvider $addToCartClickedProvider,
		private readonly ?ProductVariant $productVariant = null,
		private readonly ?ClassEvent $classEvent = null,
		private readonly string $type = self::TYPE_CATALOG,
	)
	{
		$this->onAnchor[] = $this->init(...);
	}

	private function init(): void
	{
		$this->session = $this->sessionConatiner->getSection(self::class);
		$this->mutation = $this->mutationHolder->getMutation();

		if ($this->type === self::TYPE_DETAIL) {
			$this->templateFile = 'addToCartDetail.latte';
		}

		if (!$this->request->isAjax()) {
			$this->session->remove();
		}
	}

	public function render(string $class = '', string $btnClass = '', string $inpClass = '', string $listId = '', string $listName = ''): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->templates = FE_TEMPLATE_DIR;
		$this->template->productDto = $this->productDto;
		$this->template->product = $this->product;
		$this->template->variant = $this->productVariant ?? $this->product->firstVariant;
		$this->template->mutation = $this->mutation;
		$this->template->pages = $this->mutation->pages;
		$this->template->state = $this->currentState;
		$this->template->priceLevel = $this->priceLevel;
		$this->template->modalClass = $this->modalClass;
		$this->template->class = $class;
		$this->template->btnClass = $btnClass;
		$this->template->inpClass = $inpClass;
		$this->template->listId = $listId;
		$this->template->listName = $listName;

		$this['form']['productVariantId']->setValue($this->productVariant->id ?? $this->product->firstVariant->id);
		$this['form']['classEventId']->setValue($this->classEvent?->id);

		$this->template->render(__DIR__ . '/' . $this->templateFile);
	}

	public function createComponentForm(): UI\Form
	{
		$form = new UI\Form();
		$form->setTranslator($this->translator);
		$form->addHidden('productVariantId');
		$form->addHidden('classEventId')->setNullable();
		$form->addInteger("quantity", "quantity")->setDefaultValue(1);
		$form->addSubmit('send', 'add_to_cart');

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];

		return $form;
	}

	public function formError(UI\Form $form): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSucceeded(UI\Form $form, ArrayHash $values): void
	{
		$quantity = (int) $values->quantity;
		if ($quantity === 0) {
			if (!$this->presenter->isAjax()) {
				$this->presenter->redirect('this');
			}
			$this->presenter->redrawControl();
			return;
		}

		$variant = $this->product->activeVariants->getById($values->productVariantId);
		$product = $variant->product;

		if ($product->isCourse()) {
			$classEvent = null;
			if ($values->classEventId !== null) {
				$classEvent = $this->product->classEvents->toCollection()->getById($values->classEventId);
			}
			$added = $this->shoppingCart->addClass($product, $classEvent, $this->priceLevel,  amount: $quantity);
		} else {
			$added = $this->shoppingCart->addProduct($variant, $quantity);
		}

		do {
			$scId = Random::generate();
		} while (isset($this->session[$scId]));

		$this->session->set($scId, [
			'variantId' => $variant->id,
			'classEventId' => $values->classEventId,
			'quantity' => $quantity,
			'quantityAdded' => $added,
			'previousUrl' => $this->request->getUrl()
		]);

		$page = $this->mutation->pages->precart;
		$pageQuery = ['scId' => $scId];

		if (!$this->presenter->isAjax()) {
			$this->presenter->redirect($page, $pageQuery);
		}

		if ($added > 0) {
			$listId = $listName = null;
			if ((int) $this->addToCartClickedProvider->id === $this->product->id) {
				$listId = $this->addToCartClickedProvider->listId;
				$listName = $this->addToCartClickedProvider->listName;
			}
			$this->eventDispatcher->dispatch(
				new AddToCartEvent(
					shoppingCart: $this->shoppingCart,
					productLocalization: $this->product->getLocalization($this->mutation),
					variant: $variant,
					mutation: $this->mutation,
					state: $this->currentState,
					priceLevel: $this->priceLevel,
					currency: CurrencyHelper::getCurrency(),
					quantity: $added,
					listId: $listId,
					listName: $listName,
				)
			);
		}

		$this->presenter->payload->modalTarget = $this->presenter->link($page, $pageQuery);

		$this->presenter->redrawControl();
		$this->presenter->redrawControl('homepage', false);
	}



	public function setModalClass(?string $modalClass = null): AddToCart
	{
		$this->modalClass = $modalClass;
		return $this;
	}
}
