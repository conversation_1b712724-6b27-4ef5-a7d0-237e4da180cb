{varType App\Model\Orm\Product\Product $product}
{var $hasPrice = $productDto->productAvailabilityHasPrice}
{var $showCart = $productDto->productAvailabilityShowCartCatalog}
{default $listId = ''}
{default $listName = ''}

{if $showCart && $hasPrice}
	<form class="f-add f-add--normal {$class}" n:name="form"{* data-naja data-naja-loader="body" data-naja-modal-target data-naja-modal="snippet--precart" data-naja-modal-class="b-modal--prebasket" data-naja-history="off"*}>
		<input class="u-vhide" n:name="quantity" data-step="1">
		<button n:name="send" n:class="b-dates__btn, btn,($upDiscount ?? false) ? 'btn--bd btn--gray btn--sm' : 'btn--secondary', $btnClass" data-id="{$product->id}" data-list-id="{$listId}" data-list-name="{$listName}">
			<span class="btn__text">
				{_"btn_buy"}
			</span>
			{*<span n:if="$presenter->shoppingCart->hasProductId($product->id)" class="btn__text">
				{('check')|icon, 'btn__icon'}
				{_"btn_in_basket"}
			</span>*}
		</button>
	</form>
	{*}<a href="#" n:class="b-dates__btn, btn, ($upDiscount ?? false) ? 'btn--bd btn--gray btn--sm' : 'btn--secondary'">
						<span class="btn__text">
							{_"btn_buy"}
						</span>
					</a>*}
	{*elseif $product->isInStock && $hasPrice}
		<a href="{plink $product}" class="btn">Vyberte variant</a>
	{else}
		<a href="{plink $product}" class="btn">Detail produktu</a>*}
{/if}
