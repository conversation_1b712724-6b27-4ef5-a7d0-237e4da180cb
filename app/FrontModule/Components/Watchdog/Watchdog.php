<?php

declare(strict_types=1);

namespace App\FrontModule\Components\Watchdog;

use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\User\UserProvider;
use App\Model\Orm\Watchdog\Watchdog as WatchdogEntity;
use App\Model\Orm\Watchdog\WatchdogRepository;
use App\Model\TranslatorDB;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Utils\ArrayHash;

/**
 * @property-read DefaultTemplate $template
 */
class Watchdog extends Control
{

	public function __construct(
		private readonly ProductLocalization $productLocalization,
		private readonly TranslatorDB $translator,
		private readonly UserProvider $userProvider,
		private readonly WatchdogRepository $watchdogRepository,
	)
	{
		$this->onAnchor[] = $this->init(...);
	}

	private function init(): void
	{
		$this['form']->setDefaults(['email' => $this->userProvider->userEntity?->email]);
	}

	private function beforeRender(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->FE_TEMPLATE_DIR = FE_TEMPLATE_DIR;
		$this->template->productLocalization = $this->productLocalization;
		$this->template->mutation = $this->productLocalization->mutation;
	}

	public function render(): void
	{
		$this->beforeRender();
		$this->template->render(__DIR__ . '/watchdog.latte');
	}

	public function createComponentForm(): Form
	{
		$form = new Form();
		$form->setTranslator($this->translator);

		$form->addText('email', 'watchdog_label_email')->addRule($form::Email, 'error_invalid_email')->setRequired();
		$form->addSubmit('save', 'save');

		$form->onSuccess[] = function (Form $form, ArrayHash $values): void {
			if ($this->watchdogRepository->getBy(['email' => $values->email, 'product->id' => $this->productLocalization->product->id]) === null) {
				$watchdog          = new WatchdogEntity();
				$watchdog->email   = $values->email;
				$watchdog->product = $this->productLocalization->product;

				$this->watchdogRepository->persistAndFlush($watchdog);
			}

			$this->template->success = true;
			$this->redrawControl('form');
		};

		return $form;
	}

}
