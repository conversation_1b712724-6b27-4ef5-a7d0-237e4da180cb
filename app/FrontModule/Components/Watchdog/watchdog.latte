{varType App\Model\Orm\ProductLocalization\ProductLocalization $productLocalization}



{snippet form}

	<h3>H<PERSON><PERSON><PERSON><PERSON><PERSON> pes pro produkt {$productLocalization->name}</h3>

	{ifset $success}
		<p>{_watchdog_success_message}</p>
		<a href="#">{_btn_watchdog_close_modal}</a> {* TODO: kelly*}
	{else}
		<form n:name="form" class="f-review block-loader" data-naja data-naja-history="off">
			{include $FE_TEMPLATE_DIR . '/../Components/inp.latte', form: $form, name: email, required: true, validate: true, showError: true}

			{if $mutation->pages->personalData !== null}
				{_form_label_agree_text} <a href="{plink $mutation->pages->personalData}" target="_blank">{_form_label_agree_link}</a>
			{/if}

			<p class="f-review__bottom">
				<button n:name="save" class="f-review__btn btn">
						<span class="btn__text">
							{_"btn_send_watchdog"}
						</span>
				</button>
			</p>

			<div class="block-loader__loader"></div>

		</form>

	{/ifset}
{/snippet}
