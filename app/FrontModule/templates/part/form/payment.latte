{default $class = false}
{default $showLimit = 3}

<div n:class="f-method, f-method--paymentMethod, $class">
	<div class="f-method__helper">
		<ul class="f-method__list">
			{php $paymentOptionsCount = 0}
			{foreach $form['paymentMethod']->items as $item}
				{varType App\Model\Orm\PaymentMethod\PaymentMethodConfiguration $item}
				{continueIf $item->getPaymentMethod()->getGroup() !== null}
				{php $paymentOptionsCount++}
				{php $isSelected = $shoppingCart->hasPayment() && $shoppingCart->getPayment()->paymentMethod->id == $item->id}
				{php $isAllowed = $item->isAllowed($priceLevel, $state, $shoppingCart)}
				{* {php $isDisabled = !$shoppingCart->hasDelivery()} *}

				{include $templates.'/part/form/part/method-item.latte',
					class: ($iterator->getCounter() > $showLimit || $shoppingCart->hasPayment()) && !$isSelected ? 'u-d-n' : false,
					name:'paymentMethod',
					id: $item->id,
					item: $item,
					title: $item->name,
					imgs: $item->cf->deliveryPayment??->icon ?? [],
					tooltipContent: $item->tooltip,
					price: $item->priceVat($priceLevel, $state, $shoppingCart),
					isRecommended: $item->isRecommended,
					isGift: false,
					isSelected: count($form['paymentMethod']->items) > 1 && $isSelected,
					isAllowed: $isAllowed,
					control: $control,
				}
					{* isDisabled: $isDisabled, *}
			{/foreach}

			{* Platebni metody ktere sou seskupeny v GROUP *}
			{* {var $groups = []}
			{foreach $form['paymentMethod']->items as $item}
				{varType App\Model\Orm\PaymentMethod\PaymentMethodConfiguration $item}
				{continueIf $item->getPaymentMethod()->getGroup() === null}
				{var $group = $item->getPaymentMethod()->getGroup()}
				{php $groups[$group->getId()][$item->id] = $item}
			{/foreach}

			{var $name = 'paymentMethod'}
			{foreach $groups as $groupId => $items}
				{php $isSelected = $shoppingCart->hasPayment() && in_array($shoppingCart->getPayment()->paymentMethod->id, array_keys($items))}

				{embed $templates.'/part/form/part/method-item.latte',
					class: $shoppingCart->hasPayment() && !$isSelected ? 'u-d-n' : '',
					name:'paymentMethod',
					id: $groupId,
					item: $group,
					title: $group->name,
					imgs: $group->imgs ?? [],
					tooltipContent: $group->tooltip,
					price: false,
					isRecommended: $group->isRecommended,
					isGift: $group->isGift,
					isSelected: count($form['paymentMethod']->items) > 1 && $isSelected,
					isAllowed: true,
					isGroup: true,
					isDisabled: $isDisabled,
					control: $control,
				}
					{block otherOptions}
						<div class="f-method__other">
							{foreach $items as $item}
								{var $price = $item->priceVat($priceLevel, $state, $shoppingCart)}
								<label class="f-method__label f-method__label--other inp-item inp-item--radio">
									<input n:name="paymentMethod:$item->id"{if $isDisabled} disabled{/if} n:class="inp-item__inp" data-controller="autosubmit" data-action="autosubmit#submitForm">
									<span class="inp-item__text">
										<b class="f-method__title">
											{$item->name}
											{if $item->tooltip}
												{embed '../core/tooltip.latte', class: 'f-method__tooltip', placement: 'bottom'}
													{block btn}
														<button type="button" class="tooltip__info">?</button>
													{/block}
													{block content}
														{$item->tooltip}
													{/block}
												{/embed}
											{/if}
										</b>
										<b class="f-method__price">
											{if $price->isZero()}
												{_"free"}
											{else}
												{$price|money}
											{/if}
										</b>
									</span>
								</label>
							{/foreach}
						</div>
					{/block}
				{/embed}
			{/foreach} *}
		</ul>

		<p n:if="count($form['paymentMethod']->items) > $showLimit || ($shoppingCart->hasPayment() && count($form['paymentMethod']->items) > 1)" class="f-method__more u-mb-0">
			<button type="button" class="item-icon as-link" data-controller="toggle-class" aria-expanded="false" data-action="toggle-class#toggle" data-toggle-content=".f-method--paymentMethod .f-method__item.u-d-n" data-toggle-class="u-d-n">
				<span class="item-icon__text">
					{_"cart_payment_toggle"}
					{if $shoppingCart->hasPayment()}
						({$paymentOptionsCount - 1})
					{else}
						({$paymentOptionsCount - $showLimit})
					{/if}
				</span>
				{('angle-down')|icon, 'item-icon__icon'}
			</button>
		</p>
	</div>
</div>


{* imgs:['/static/img/illust/sample.jpg'], *}
{* <div n:class="f-method, $class" n:if="$form['paymentMethod']->items === []">
	{_'payment_for_delivery_not_set'}
</div> *}
