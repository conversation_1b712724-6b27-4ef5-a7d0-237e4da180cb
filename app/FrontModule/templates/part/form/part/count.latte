{default $class = false}
{default $amount = 1}
{default $maxAmount = 1000}
{default $preorderSupply = false}
{default $autosubmit = false}

{var $variantId = $variant??->id ?? null}
{*if isset($variant)}
	{var $maxAmount = $variant->totalSupplyCount}
{/if*}


{default $disableZero = false}

{if $amount > $maxAmount}
	{var $amount = $maxAmount}
{/if}

{default $input = null}

{var $inputAmount = $input?->getValue() ?? 1}
<span n:if="$amount >= 0 && $variantId && maxAmount" n:class="inp-count, $class" data-controller="inp-count" data-inp-count-min-value="{if $disable<PERSON>ero}1{else}0{/if}" data-inp-count-max-value="{$maxAmount}"{if $preorderSupply} data-inp-count-limit-tooltip-value="{$preorderSupply}"{/if}>
	<label for="amount_{$variantId}" class="u-vhide">{_"amount"}</label>
	<button type="button" n:class="btn, inp-count__tool, inp-count__tool--minus, $disableZero && $inputAmount <= 1 ? is-disabled" data-step="-1" data-action="inp-count#changeValue" data-inp-count-target="toolMinus">
		<span class="u-vhide">-</span>
	</button>

	<input n:if="$input instanceof Nette\Forms\Control" n:name="$input" type="number" class="inp-count__inp inp-text" id="amount_{$variantId}" data-inp-count-target="inp" max="{$maxAmount}"{if $autosubmit} data-controller="autosubmit" data-action="keyup->inp-count#checkValue change->autosubmit#submitForm"{else} data-action="keyup->inp-count#checkValue"{/if}>
	{* <input n:if="$input instanceof Nette\Forms\Control" n:name="$input" type="text" class="inp-count__inp inp-text" id="amount_{$variantId}" data-inp-count-target="inp" max="{$maxAmount}"{if $autosubmit} data-controller="autosubmit mask" data-mask-settings-value='{"suffixUnit": "ks"}' data-action="input->inp-count#checkValue change->autosubmit#submitForm"{else} data-controller="mask" data-mask-settings-value='{"suffixUnit": "ks"}' data-action="input->inp-count#checkValue"{/if}> *}

	<button type="button" n:class="btn, inp-count__tool, inp-count__tool--plus, $maxAmount == $inputAmount ? is-disabled" data-step="1" data-action="inp-count#changeValue" data-inp-count-target="toolPlus">
		<span class="u-vhide">+</span>
	</button>
</span>
