{default $class = false}
{* {varType App\Model\Orm\State\State $selectedCountry} *}
<p n:if="$countrySelect" n:class="inp-country, $class">
	{_"country_select"}
	<select n:name=country class="inp-country__select" data-controller="autosubmit" data-action="autosubmit#submitForm">
	</select>
</p>

{* <span class="inp-country__box">
	<img class="inp-country__flag" src="/static/img/flags/{$selectedCountry->code}.svg" alt="{_$selectedCountry->name}" data-country-target="flag">
	<span class="inp-country__name" data-country-target="name">
		{_$selectedCountry->name}
	</span>
</span> *}