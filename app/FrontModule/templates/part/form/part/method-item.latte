{default $class = false}
{default $name = false}
{default $id = false}
{default $title = false}
{default $deliveryDate = false}
{default $price = 0}
{default $imgs = []}
{default $tooltipContent = false}
{default $isSelected = false}
{default $isRecommended = false}
{default $isGift = false}
{default $isStatic = false}
{default $isAllowed = true}
{default $freeFrom = false}
{default $item = false}
{default $isGroup = false}
{default $isDisabled = false}
{default $control = null}

{php $supportsPickup = $item instanceof App\Model\Orm\DeliveryMethod\DeliveryMethodConfiguration && $item->getDeliveryMethod()->supportsPickup()}

<li n:class="f-method__item, $class, $supportsPickup ? f-method__item--pickup, $isGroup ? f-method__item--group">
	<label n:tag-if="!$isStatic" class="f-method__label inp-item inp-item--radio" {if $supportsPickup} data-controller="delivery-picker" data-delivery-picker-config-value="{\Nette\Utils\Json::encode($item->getDeliveryMethod()->getPickupConfig($item, $price, $pickupPoint, $shoppingCart->getCountry()))}" aria-expanded="false"{/if}>
		{if $isGroup}
			<input type="radio" n:class="f-method__inp, inp-item__inp" {if !$isAllowed || $isDisabled} disabled{/if}  name="group"{if $isSelected} checked{/if} value="{$id}">
		{else}
			<input n:if="!$isStatic" n:name="$name:$id" n:class="f-method__inp, !$isStatic ? inp-item__inp" {if !$isAllowed || $isDisabled} disabled{/if}{if $supportsPickup} data-action="click->delivery-picker#openPicker skpickerselect@document->delivery-picker#onPickupPointSelect"{else} data-controller="autosubmit" data-action="autosubmit#submitForm"{/if}>
		{/if}
		<span n:class="f-method__inner, !$isStatic ? inp-item__text">
			{define #img}
				{default $showEmpty = false}
				<span n:if="count($imgs) || $showEmpty" class="f-method__img">
					{capture $sizes}
						{if count($imgs) == 1}
							width="60" height="46"
						{elseif count($imgs) == 2}
							width="60" height="30"
						{elseif count($imgs) == 3}
							width="60" height="22"
						{/if}
					{/capture}
					{foreach $imgs as $img}
						<img src="{$img->getSize('xs')->src}" alt="" loading="lazy" {$sizes|noescape}>
					{/foreach}
				</span>
			{/define}

			{if $name == 'deliveryMethod'}
				{include #img, showEmpty: true}
			{/if}

			<b class="f-method__title">
				{$title}

				{if $name == 'paymentMethod'}
					{include #img}
				{/if}

				<span class="f-method__tooltip">
					{if $tooltipContent}
						{embed '../../core/tooltip.latte', class: false, placement: 'bottom'}
							{block btn}
								<button type="button" class="tooltip__info">?</button>
							{/block}
							{block content}
								{$tooltipContent}
							{/block}
						{/embed}
					{/if}
				</span>
			</b>
			<span n:if="!$isStatic" class="f-method__recommend">
				<span n:if="$isRecommended" class="flag flag--secondary">{_"recommend"}</span>
				<span n:if="$isGift" class="flag">{_"gift"}</span>
			</span>
			<b n:if="$name == 'deliveryMethod' && !$isStatic && $isAllowed && !$isDisabled" class="f-method__delivery u-c-green">
				{include $templates . '/part/box/deliveryInfo.latte', deliveryDate: $deliveryDate,}
			</b>
			<b n:if="$price" class="f-method__price">
				{if $isAllowed}
					{if $price->isZero()}
						{_"free"}
					{else}
						{$price|money}
					{/if}
				{else}
					{ifset $isNotAllowedReason}{_$isNotAllowedReason}{else}{_'cart_method_is_not_allowed'}{/ifset}
				{/if}
			</b>
			<span n:ifcontent n:if="!$isStatic" class="f-method__desc">
				<span n:ifcontent class="u-d-b">
					{$item->desc}

					{* Výběr odběrného místa *}
					{if $supportsPickup}
						{if isset($pickupPoint) && $pickupPoint->deliveryMethod->id == $item->id}
							<span class="as-link u-d-b">
								{_"delivery_change_store"}
							</span>
							{$pickupPoint->name}, {$pickupPoint->address}
						{else}
							<span class="f-method__choose as-link">
								{_"btn_chose_pickup"}
							</span>
						{/if}
					{/if}
				</span>
			</span>
			<span n:if="$freeFrom" class="f-method__free-from">
				{_"cart_free_delivery_from"} {$freeFrom|money}
			</span>
		</span>
	</label>

	{block otherOptions}{/block}

	{if $control !== null && $control instanceof App\FrontModule\Components\CartDelivery\CartDelivery}
		{* Pozn.: Nesmí být <button> *}
		{var $changeUrl = $item instanceof App\Model\Orm\DeliveryMethod\DeliveryMethodConfiguration ? $control->link('changeDelivery!') : $control->link('changePayment!')}
		<a href="{$changeUrl}" n:if="$isSelected" class="f-method__change" data-naja data-naja-history="off" data-naja-loader="body" {*data-controller="toggle-class" data-action="toggle-class#toggle" data-toggle-content=".f-method--{$name} .f-method__item.u-d-n" data-toggle-class="u-d-n"*}>
			<span class="item-icon">
				{('angle-down')|icon, 'item-icon__icon'}
				<span class="item-icon__text">
					{_"btn_change_".$name}
				</span>
			</span>
		</a>
	{/if}
</li>
