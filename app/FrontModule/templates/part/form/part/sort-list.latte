{default $class = false}
{default $translationSubtype = ''}

{if isset($object) && $object instanceOf App\PostType\Page\Model\Orm\CatalogTree}
	{if $object->hasCourses}
		{var $translationSubtype = 'class_'}
	{else}
		{var $translationSubtype = $translationSubtype ?? ''} {* fix undefined variable $translationSubtype *}
	{/if}
{/if}

<div class="f-sort__sort" data-controller="etarget toggle-class">
	<ul n:class="f-sort__list, $class">
		{foreach $sortOptions as $sort}
			{include #sortRow sort=>$sort, defaultSort=>$sortOptions[0]}
		{/foreach}
	</ul>
</div>

{define #sortRow}
	{if ($catalogOrder ?? ($control->getParameter('order') ?? $defaultSort)) == $sort}
		<li class="f-sort__item is-active" data-action="click->toggle-class#toggle">
			<span class="f-sort__link">
				{* <span class="u-d-n@lg">{_"sort_prefix"}</span> *}
				{_'sort_'.$translationSubtype.strtolower($sort)}
				{('angle-down')|icon, 'f-sort__arrow'}
			</span>
		</li>
	{elseif isset($cleanFilterParam)}
		<li class="f-sort__item">
			<a href="{link 'this', page => null, filter => $cleanFilterParam, order=> $sort, order_set => 1}" class="f-sort__link" data-naja data-naja-loader="body" {if isset($linkSeo) && $linkSeo->hasNofollow($object, ['filter' => $cleanFilterParam])}rel="nofollow"{/if}>
				{_'sort_'.$translationSubtype.strtolower($sort)}
			</a>
		</li>
	{else}
		<li class="f-sort__item">
			<a href="{link 'order!', order => $sort, order_set => 1}" class="f-sort__link" data-naja data-naja-loader="body" data-naja-history="off">
				{_'sort_'.$translationSubtype.strtolower($sort)}
			</a>
		</li>
	{/if}
{/define}