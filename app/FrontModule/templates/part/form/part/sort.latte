{default $class = false}
{default $showing = false}
{default $showingLang = 'showing'}

<div n:snippet="filterSetup" n:class="f-sort, $class">
	<p n:if="$showing" class="f-sort__total u-mb-0">
		{php $total = $presenter['catalogProducts']['pager']->getPaginator()->getItemCount()}
		{if $total > 0}
			{_$showingLang} <b>{$total}</b>
		{/if}
	</p>
	{include $templates.'/part/form/part/sort-list.latte', class: 'f-sort__list--btns', sortOptions: $sortOptions}
	<p n:if="$pages->sortHelp" class="f-sort__info u-mb-0">
		<a n:href="$pages->sortHelp" target="_blank">{_"sort_tooltip"}</a>
{*		{embed $templates.'/part/core/tooltip.latte', class: false, btnClass: 'as-link', placement: 'bottom'}*}
{*			{block btn}*}
{*				{_"sort_tooltip"}*}
{*			{/block}*}
{*			{block content}*}
{*				{_"sort_tooltip_content"}*}
{*			{/block}*}
{*		{/embed}*}
	</p>
</div>
