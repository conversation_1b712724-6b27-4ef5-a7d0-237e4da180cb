{default $class = false}
{default $paramGroups = []}

<div n:class="f-compare, $class, 'b-compare__sticky b-compare__sticky--mask tw-flex tw-flex-col md:tw-py-[1rem] tw-z-[5] tw-bg-white'" data-controller="sticky">
	{* Switch *}
	<p class="tw-mb-[1.6rem] md:tw-mb-[2rem] tw-mt-auto">
		<label class="switch switch--lg tw-justify-center">
			<span class="switch__text">{_'compare_toggle'|split:'%toggle'|first}</span>
			<input type="checkbox" class="switch__inp" name="compare_toggle" data-action="change->compare#toggleSame change->compare#syncCheckbox" checked>
			<span class="switch__inner">
				<span class="switch__bg switch__bg--left"></span>
				<span class="switch__bg switch__bg--right"></span>
				<span class="switch__tool"></span>
			</span>
			<span class="switch__text">{_'compare_toggle'|split:'%toggle'|last}</span>
		</label>
	</p>

	{* Filtrace *}
	<div class="tw-relative" data-controller="etarget toggle-class" data-toggle-class-trigger-class-value="false">
		<p class="f-compare__toggle tw-mb-[-0.1rem]">
			<span class="inp-fix">
				<button type="button" name="select" id="select" class="inp-select as-link" data-action="toggle-class#toggle">
					{_"compare_filter_placeholder"}
				</button>
			</span>
		</p>
		<div class="f-compare__filter tw-overflow-hidden tw-flex tw-flex-col tw-absolute tw-max-h-[40rem] tw-top-full tw-left-0 tw-right-0 tw-bg-white tw-border-bd tw-border-solid tw-border-[0.1rem] tw-border-t-0 tw-rounded-bl-md tw-rounded-br-md">
			<p class="f-compare__search tw-mb-0 tw-bg-bg tw-sticky tw-top-0 tw-z-[1] tw-flex tw-border-tile tw-border-[0.1rem] tw-border-solid tw-border-l-0 tw-border-r-0">
				<span class="inp-fix tw-flex-1">
					<label for="param_search" class="u-vhide" data-toggle-class-target="inp">{_"form_label_search"}</label>
					<input type="text" id="param_search" name="param_search" class="inp-text tw-bg-transparent tw-border-none tw-p-[1rem_2rem]"
						placeholder="{_'placeholder_filter_search'}"
						autocomplete="off"
						data-action="input->compare#filter paste->compare#filter">
				</span>
				{('search')|icon, 'tw-mx-[1.6rem] tw-text-primary'}
			</p>

			<div class="tw-flex-1 tw-overflow-y-auto tw-overflow-x-hidden tw-overscroll-none tw-p-[2rem_1.6rem]">
				<ul class="f-compare__list f-compare__list--main tw-flex tw-flex-col tw-gap-[1.8rem] tw-mb-0">
					<li n:foreach="$paramGroups as $group" class="tw-flex tw-flex-col tw-gap-[1rem]" data-compare-target="filterGroup">
						<label class="inp-item inp-item--checkbox tw-font-bold">
							<input type="checkbox" name="{$group|webalize}" value="1" class="inp-item__inp" checked data-compare-target="groupCheckbox" data-action="change->compare#toggleGroup change->compare#syncCheckbox">
							<span class="inp-item__text">
								{$group}
							</span>
						</label>

						{* Parameters *}
						{php $params = ['Cena (s DPH)', 'Hmotnost v gramech', 'Max dosah']}
						<ul class="f-compare__list tw-flex tw-flex-col tw-gap-[0.8rem]">
							<li n:foreach="$params as $param" data-compare-target="filterParam">
								<label class="inp-item inp-item--checkbox">
									<input type="checkbox" name="{$group|webalize}_{$param|webalize}" value="1" class="inp-item__inp" checked data-compare-target="paramCheckbox" data-action="change->compare#toggleParam change->compare#syncCheckbox">
									<span class="inp-item__text">
										{$param}
									</span>
								</label>
							</li>
						</ul>
					</li>
				</ul>
				<p class="tw-mb-0 tw-text-[1.4rem] tw-text-help tw-text-center u-d-n" data-compare-target="filterEmpty">
					{_"compare_empty_search"}
				</p>
			</div>

			<p class="tw-mb-0 tw-sticky tw-bg-white tw-bottom-0 tw-p-[0.8rem_1.6rem]">
				<button type="button" class="btn btn--block btn--sm" data-action="toggle-class#toggle">
					<span class="btn__text">
						{_"btn_confirm"}
					</span>
				</button>
			</p>
		</div>
	</div>
</div>