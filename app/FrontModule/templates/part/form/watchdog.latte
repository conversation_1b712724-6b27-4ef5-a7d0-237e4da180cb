<p n:class="$class, u-mb-0">
	<a href="{plink $mutation->pages->watchdog, productId: $productId}" data-modal='{"medium": "fetch", "modalClass": "b-modal--8-12"}' data-snippetid="snippet--content" n:class="f-add__btn, f-add__btn--watchdog, btn, $btnClass"> {* todo link *}
		<span class="btn__text">
			{if $longLangs}
				{('dog')|icon, 'btn__icon'}
				{_"btn_watch_availability"|noescape}
			{else}
				{_"btn_watch_availability_box"|noescape}
			{/if}
		</span>
	</a>
</p>
