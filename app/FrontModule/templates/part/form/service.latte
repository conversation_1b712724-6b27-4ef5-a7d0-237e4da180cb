{default $class = false}
{default $cf = $object->cf->serviceForm}

<form action="?" class="f-service tw-text-[1.4rem]">
	<div class="md:tw-rounded-xl md:tw-border-solid md:tw-border-tile md:tw-border-[0.1rem] md:tw-p-[5.2rem] tw-mb-[2rem] u-mb-last-0">
		<div class="tw-max-w-[44rem] tw-mx-auto">
			{* Obecné *}
			<div class="u-mb-last-0 tw-mb-[2rem] md:tw-mb-[3.2rem]">
				<p>
					TODO: inps
				</p>

				{* TODO FE: přenést do inp.latte *}
				<p class="tw-mb-[0.8rem]">
					<span class="inp-file" data-controller="file-input">
						<span class="inp-label">{_"form_label_file"|noescape}</span>
						<label class="tw-cursor-pointer tw-flex tw-gap-[0.8rem] tw-items-center tw-bg-white tw-p-[1rem] tw-rounded-md tw-border-[0.1rem] tw-border-solid tw-border-bd" data-file-input-target="dropzone" data-action="dragover->file-input#dragover dragend->file-input#dragend dragleave->file-input#dragleave drop->file-input#drop">
							<span class="inp-file__btn btn btn--bd btn--gray"><span class="btn__text">{_"form_label_file_btn"|noescape}</span></span>
							<span class="tw-text-[1.5rem] tw-text-placeholder">{_"form_label_file_placeholder"|noescape}</span>
							<input class="u-vhide" type="file" name="files[]" multiple="" data-file-input-target="input" data-action="change->file-input#fileInputChange" accept="image/*">
						</label>
						<span class="[&:not(:empty)]:tw-pt-[1.2rem] tw-flex tw-flex-wrap tw-text-[1.3rem] tw-gap-[0.4rem]" data-file-input-target="list"></span>

						<template data-file-input-target="template">
							<span class="f-file__item">
								<button type="button" class="as-link tw-border-solid tw-border-tile tw-border-[0.1rem] tw-rounded-full tw-no-underline tw-text-text tw-flex tw-items-center tw-gap-[0.4rem] tw-p-[0.3rem_1rem] tw-min-h-[2.9rem]" data-action="click->file-input#removeFile">
									<span class="file-name"></span>
									{('cross')|icon, 'tw-text-red tw-w-[1.2rem]'}
									<span class="file-input__error" hidden></span>
								</button>
							</span>
						</template>
					</span>
				</p>

				<div n:if="$cf->photos ?? false" class="tw-group" data-controller="toggle-class">
					<p class="tw-mb-0">
						<button type="button" class="as-link tw-no-underline item-icon tw-gap-[0.4rem]" data-action="toggle-class#toggle">
							<span class="item-icon__text">
								Jaké fotky nahrávat
							</span>
							{('angle-down')|icon, 'item-icon__icon group-[.is-open]:tw-rotate-180 tw-w-[1.5rem]'}
						</button>
					</p>
					<div class="u-mb-last-0 tw-hidden group-[.is-open]:tw-block tw-pt-[0.4rem]">
						{$cf->photos|noescape}
					</div>
				</div>
			</div>

			{* Diagnostika *}
			<div n:if="$cf->diagnostics ?? false" class="u-mb-last-0 tw-mb-[2rem] md:tw-mb-[3.2rem]">
				<h2 class="h3 tw-mb-[1.2rem]">
					{_"title_service_form_diagnostics"}
				</h2>
				<div class="u-mb-last-0 tw-rounded-md tw-border-solid tw-border-[0.1rem] tw-border-tile tw-p-[1.6rem_2rem] tw-text-[1.4rem] tw-mb-[-0.1rem] tw-text-help">
					{$cf->diagnostics|noescape}
				</div>
			</div>

			{* Express *}
			<div n:if="$cf->expressService ?? false" class="u-mb-last-0 tw-mb-[2rem] md:tw-mb-[3.2rem]">
				<h2 class="h3 tw-mb-[1.2rem]">
					{_"title_service_form_express"}
				</h2>
				<div class="tw-rounded-md tw-border-solid tw-border-[0.1rem] tw-border-tile tw-p-[1.6rem_2rem] tw-text-[1.4rem] tw-mb-[-0.1rem]">
					<p>
						TODO: inps
					</p>
					<div class="u-mb-last-0 tw-text-help">
						{$cf->expressService|noescape}
					</div>
				</div>
			</div>

			{* Doplňkové služby *}
			<div class="u-mb-last-0 tw-mb-[2rem] md:tw-mb-[3.2rem]">
				<h2 class="h3 tw-mb-[1.2rem]">
					{_"title_service_form_addons"}
				</h2>
				<div class="u-mb-last-0 tw-rounded-md tw-border-solid tw-border-[0.1rem] tw-border-tile tw-p-[1.6rem_2rem] tw-mb-[-0.1rem]">
					TODO: inps
				</div>
				<div class="u-mb-last-0 tw-rounded-md tw-border-solid tw-border-[0.1rem] tw-border-tile tw-p-[1.6rem_2rem] tw-mb-[-0.1rem]">
					TODO: inp
				</div>
				<div class="u-mb-last-0 tw-rounded-md tw-border-solid tw-border-[0.1rem] tw-border-tile tw-p-[1.6rem_2rem] tw-mb-[-0.1rem]">
					TODO: inp
				</div>
			</div>

			{* Příslušenství *}
			<div class="u-mb-last-0 tw-mb-[2rem] md:tw-mb-[3.2rem]">
				<h2 class="h3 tw-mb-[1.2rem]">
					{_"title_service_form_accessories"}
				</h2>
				{$cf->accessories ?? false|noescape}
				<p>
					TODO: inp
				</p>
			</div>

			{* Odesílatel *}
			<div class="u-mb-last-0 tw-mb-[2rem] md:tw-mb-[3.2rem]">
				{if $user->loggedIn}
					<h2 class="h3 tw-mb-[1.2rem]">
						{_"title_service_form_sender"}
					</h2>
					<p>
						TODO: address inps / address select (jako v objednávce)
					</p>
				{else}
					<div class="tw-flex tw-items-center tw-gap-[1rem] tw-justify-between tw-mb-[1.2rem] md:tw-mb-[2rem]">
						<h2 class="h3 tw-mb-0">
							{_"title_service_form_sender"}
						</h2>
						<p class="tw-mb-0">
							<a href="{plink $pages->userLogin}" data-modal='{"medium": "fetch", "modalClass": "b-modal--xs"}' data-snippetid="snippet--userFormArea">
								{_"btn_login"}
							</a>
						</p>
					</div>
					<p>
						TODO: address inps / address select (jako v objednávce)
					</p>
				{/if}
			</div>

			<div class="u-mb-last-0 tw-mb-[2rem] md:tw-mb-[3.2rem]">
				<h2 class="h3 tw-mb-[1.2rem]">
					{_"title_service_form_delivery"}
				</h2>
				<div class="u-mb-last-0 tw-rounded-md tw-border-solid tw-border-[0.1rem] tw-border-tile tw-p-[1.6rem_2rem] tw-mb-[-0.1rem]">
					TODO: inp
				</div>
				<div class="u-mb-last-0 tw-rounded-md tw-border-solid tw-border-[0.1rem] tw-border-tile tw-p-[1.6rem_2rem] tw-mb-[-0.1rem]">
					TODO: inp
				</div>
			</div>
		</div>
	</div>
	<div class="tw-text-center tw-mb-0 tw-flex tw-justify-center tw-items-center tw-gap-[2rem] tw-flex-wrap">
		<p class="tw-mb-0">
			TODO: agree
		</p>
		<p class="tw-mb-0">
			<button type="submit" class="btn btn--secondary btn--xl">
				<span class="btn__text">
					<span class="btn__inner">
						{_"btn_send"}
						{('arrow-right-long')|icon, 'btn__icon'}
					</span>
				</span>
			</button>
		</p>
	</div>
</form>