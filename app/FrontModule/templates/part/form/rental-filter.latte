{default $class = 'tw-mb-[1.6rem] md:tw-mb-[2rem]'}

<form action="?" n:class="tw-group, $class" data-controller="toggle-class">
	<div class="tw-rounded-lg tw-bg-primary-150 tw-p-[1.2rem_1.2rem_0.8rem] md:tw-p-[0.6rem_2rem_0.6rem_0.6rem] tw-flex tw-flex-wrap md:tw-flex-nowrap tw-items-center tw-text-[1.4rem] tw-gap-[0.8rem_1.6rem]">
		<p class="tw-mb-0 tw-flex-[0_0_auto] tw-w-[8.4rem]">
			<img class="img img--4-3 tw-rounded-md tw-bg-white" src="/static/img/illust/noimg.svg" alt="">
		</p>
		<p class="tw-mb-0 max-md:tw-w-[calc(100%-8.4rem-1.6rem)] tw-flex-[0_0_auto] md:tw-flex-1">
			<b class="tw-text-[1.5rem]">Kompletní sety</b> <span class="tw-text-help">(9)</span>
		</p>
		<hr class="tw-w-full md:tw-hidden tw-border-tile tw-mt-0 tw-mb-0">
		<p class="tw-mb-0 max-md:tw-flex-1">
			<button type="button" class="as-link item-icon tw-gap-[0.4rem]" data-action="toggle-class#toggle" data-toggle-class="is-filter-open">
				<span class="item-icon__text group-[.is-filter-open]:tw-hidden">{_"btn_show_filter"}</span>
				<span class="item-icon__text tw-hidden group-[.is-filter-open]:tw-block">{_"btn_hide_filter"}</span>
				{('angle-down')|icon, 'item-icon__icon tw-w-[1.2rem] group-[.is-filter-open]:tw-rotate-180'}
			</button>
		</p>
		<div class="tw-flex-[0_0_auto] tw-w-[16.4rem]">
			{php $sortOptions = ['cheapest']}
			{include $templates.'/part/form/part/sort-list.latte', class: 'f-sort__list--select', sortOptions: $sortOptions}
		</div>
	</div>
	<div class="tw-pt-[0.4rem] tw-hidden group-[.is-filter-open]:tw-block">
		<div class="tw-flex tw-flex-col sm:tw-flex-row tw-gap-[0.4rem] tw-flex-wrap">
			{embed $templates.'/part/core/inp-group.latte', name: 'Max foto rozlišení', count: 0}
				{block content}
					TODO BE: checkboxes
				{/block}
			{/embed}
			{embed $templates.'/part/core/inp-group.latte', name: 'Maximální čas letu', count: 0}
				{block content}
					TODO BE: checkboxes
				{/block}
			{/embed}
			{embed $templates.'/part/core/inp-group.latte', name: 'Senzory proti překážkám', count: 3}
				{block content}
					TODO BE: checkboxes
				{/block}
			{/embed}
			{embed $templates.'/part/core/inp-group.latte', name: 'Vlastnosti', count: 0}
				{block content}
					TODO BE: checkboxes
				{/block}
			{/embed}
			{embed $templates.'/part/core/inp-group.latte', name: 'Značka', count: 0}
				{block content}
					TODO BE: checkboxes
				{/block}
			{/embed}
			{embed $templates.'/part/core/inp-group.latte', name: 'Další filtr na druhém řádku', count: 0}
				{block content}
					TODO BE: checkboxes
				{/block}
			{/embed}
		</div>
	</div>
</form>