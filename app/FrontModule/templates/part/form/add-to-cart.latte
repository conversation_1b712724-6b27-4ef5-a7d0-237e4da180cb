{varType App\Model\ProductVariant $variant}
{default $class = false}

<div n:class="f-add-to-cart, $class">
	{* Přidat do košíku *}
	{if $variant->productAvailability->isShowCartDetail($mutation, $priceLevel, $state) ?? false}
		{control addToCart, class:'f-add-to-cart__add', btnClass:'f-add-to-cart__btn btn--md'}
	{/if}
	{* Hlídat dostupnost *}
	{if ($variant->productAvailability->isShowWatchdog() ?? false) || !$variant->productAvailability->hasPrice($mutation, $priceLevel, $state)}
		{include watchdog.latte, class: 'f-add-to-cart__add', btnClass: 'f-add-to-cart__btn btn--md', longLangs: true}
	{/if}
	{* Podobné produkty *}
	{if $variant->productAvailability->isShowSimilar() ?? false}
		{include similar.latte, class: 'u-mb-0'}
	{/if}
</div>
