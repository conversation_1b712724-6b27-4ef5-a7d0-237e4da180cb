{default $class = false}
{default $showLimit = 3}
{var App\Model\Orm\Order\PickupPoint\PickupPoint $pickupPoint = $shoppingCart->getPickupPoint()}
{var $pickupPointMarkerUrls = ["pickupPointsResponsePath" => "pickupPoints"]}

<div n:class="f-method, f-method--deliveryMethod, $class" data-controller="pickup-points">
	<div class="f-method__helper">
		<ul class="f-method__list">
			{foreach $form['deliveryMethod']->items as $item}
				{varType App\Model\Orm\DeliveryMethod\DeliveryMethodConfiguration $item}
				{php $isSelected = $shoppingCart->hasDelivery() && $shoppingCart->getDelivery()->deliveryMethod->id == $item->id}
				{php $isAllowed = $item->isAllowed($priceLevel, $state, $shoppingCart->getCurrency(), $shoppingCart->getTotalWeight())}

				{if $item->getDeliveryMethod()->getPickupMarkersUrl($item, $state) !== null}
					{php $pickupPointMarkerUrls['markersApiUrl'][] = $item->getDeliveryMethod()->getPickupMarkersUrl($item, $state)}
				{/if}

				{include $templates.'/part/form/part/method-item.latte',
					class: ($iterator->getCounter() > $showLimit || $shoppingCart->hasDelivery()) && !$isSelected ? 'u-d-n' : false,
					name:'deliveryMethod',
					id: $item->id,
					item: $item,
					title: $item->name,
					imgs: $item->cf->deliveryPayment??->icon ?? [],
					tooltipContent:$item->tooltip,
					price: $item->priceVat($priceLevel, $state, $shoppingCart),
					templates: $templates,
					deliveryDate: $shoppingCart->getWorstDeliveryDate($item),
					isRecommended: $item->isRecommended,
					isGift:  $item->isGift,
					isSelected: count($form['deliveryMethod']->items) > 1 && $isSelected,
					isAllowed: $isAllowed,
					pickupPoint: $pickupPoint,
					control: $control,
				}
			{/foreach}
		</ul>
		<p n:if="count($form['deliveryMethod']->items) > $showLimit || ($shoppingCart->hasDelivery() && count($form['deliveryMethod']->items) > 1)" class="f-method__more u-mb-0">
			<button type="button" class="item-icon as-link" data-controller="toggle-class" aria-expanded="false" data-action="toggle-class#toggle" data-toggle-content=".f-method--deliveryMethod .f-method__item.u-d-n" data-toggle-class="u-d-n">
				<span class="item-icon__text">
					{_"cart_delivery_toggle"}
					{if $shoppingCart->hasDelivery()}
						({count($form['deliveryMethod']->items) - 1})
					{else}
						({count($form['deliveryMethod']->items) - $showLimit})
					{/if}
				</span>
				{('angle-down')|icon, 'item-icon__icon'}
			</button>
		</p>
		<input n:name="pickupPointId" class="u-vhide" data-controller="autosubmit" data-action="change->autosubmit#submitForm">
	</div>
</div>

<script type="text/javascript" src="{$basePath}/static/js/deliveryPickerCore.js?t={$webVersion ?? filemtime(\WWW_DIR.'/static/js/deliveryPickerCore.js')}"></script>
<script type="text/javascript">
	SkDeliveryPicker.init({\Nette\Utils\Json::encode($pickupPointMarkerUrls)|noescape});
</script>
