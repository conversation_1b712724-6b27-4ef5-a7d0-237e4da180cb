{default $crossroad = isset($object->crossroad) ? $object->crossroad : new ArrayIterator()}
{default $title = h2}
{default $customTitle = false}
{default $pager = true}
{default $class = "u-mb-lg"}

<section n:if="$crossroad->count() > 0" n:class="c-articles, $class">
	<h2 n:if="$customTitle" class="c-articles__title">
		{translate}{$customTitle}{/translate}
	</h2>

	<div class="c-articles__list grid" n:snippet="articleList" data-ajax-append>
		{foreach $crossroad as $c}
			<div class="c-articles__item grid__cell size--4-12">
				{include '../box/article.latte', c=>$c, class=>''}
			</div>
		{/foreach}
	</div>

	{if $pager}
		{snippet articlesPagerBottom}
			{control pager, [class => 'u-pt-lg', showMoreBtn=>true]}
		{/snippet}
	{/if}
</section>
