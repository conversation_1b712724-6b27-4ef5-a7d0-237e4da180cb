{default $class = 'u-mb-md u-mb-xl@md'}
{default $specLayout = false}
{default $title = false}
{default $titleLang = false}
{default $pager = true}
{default $showAllBtn = false}
{default $items = []}
{default $showMore = false}
{default $carousel = false}
{default $titleTag = false}

<section n:if="count($items) || $pager" n:class="c-events, $class, !$specLayout ? 'holder holder--lg'">
	<h2 n:if="$titleLang || $title" n:class="h2, !$specLayout ? u-ta-c, 'u-mb-sm u-mb-md@md'">
		{if $title}
			{$title}
		{elseif $titleLang}
			{_$titleLang}
		{/if}
	</h2>
	{if count($items)}
		<div n:tag-if="$carousel" class="embla" data-controller="embla">
			<div n:tag-if="$carousel" class="embla__holder">
				<div n:tag-if="$carousel" class="embla__viewport" data-embla-target="viewport">
					<div n:class="grid, 'grid--x-md@md grid--y-md@md', $carousel ? 'grid--scroll embla__container'">
						<div n:foreach="$items as $item" n:class="grid__cell, $specLayout ? 'size--6-12@md' : 'size--6-12@md size--4-12@xl'">
							{include $templates.'/part/box/event.latte', item: $item, class: false}
						</div>
					</div>
				</div>

				{if $carousel}
					<button class="embla__btn embla__btn--prev" disabled="disabled" type="button" data-action="embla#prev" data-embla-target="prevButton">
						{('angle-l')|icon}
						<span class="u-vhide">{_btn_prev}</span>
					</button>
					<button class="embla__btn embla__btn--next" disabled="disabled" type="button" data-action="embla#next" data-embla-target="nextButton">
						{('angle-r')|icon}
						<span class="u-vhide">{_btn_next}</span>
					</button>
				{/if}
			</div>
		</div>
		<p n:if="isset($pages->events) && $showAllBtn" class="u-ta-c u-mb-0 u-pt-md u-pt-lg@md">
			<a href="{plink $pages->events}" class="btn btn--secondary">
				<span class="btn__text">
					{_"btn_all_events"}
				</span>
			</a>
		</p>
		{if $pager}
			{snippet articlesPagerBottom}
				{control pager, []}
			{/snippet}
		{/if}
	{else}
		<p class="h2 u-ta-c u-mb-0">
			{_"empty_events"}
		</p>
	{/if}

	<p n:if="$showMore && isset($pages->events)" class="u-ta-c u-mb-0 u-pt-sm u-pt-md@md">
		<a href="{plink $pages->events}" class="btn btn--secondary btn--outline">
			<span class="btn__text">
				{_"btn_show_more"}
			</span>
		</a>
	</p>
</section>
