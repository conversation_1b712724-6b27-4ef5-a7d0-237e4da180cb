{default $class = false}
{default $themes = []}

<div n:if="count($themes)" n:class="c-tiles, $class">
	<h2 class="h1 u-mb-xxs u-mb-xs@md">
		{_'theme_trending_title'}
	</h2>
	<ul class="c-tiles__grid grid">
		<li n:foreach="$themes as $theme" class="c-tiles__cell grid__cell">
			{capture $href}{plink $theme}{/capture}
			{php $image = isset($theme->cf->gallery) && isset($theme->cf->gallery->mainImage) ? $theme->cf->gallery->mainImage->getEntity() ?? false : false}
			{include '../box/tile.latte', class=>false, title=>$theme->name, image=>$image, link=>$href->__toString()}
		</li>
	</ul>
</div>
