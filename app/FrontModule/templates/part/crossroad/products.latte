{default $class = 'u-mb-md u-mb-2xl@md'}
{default $title = false}
{default $items = []}

<section n:if="count($items)" n:class="section, $class">
	<h2 n:if="$title" class="tw-mb-[0.8rem] md:tw-mb-[1.6rem]">{$title}</h2>
	<div class="section__carousel embla" data-controller="embla">
		<div class="embla__holder">
			<div class="embla__viewport" data-embla-target="viewport">
				<div class="section__grid grid grid--x-0 grid--y-0 grid--scroll embla__container">
					<div n:foreach="$items as $product" class="section__cell grid__cell">
						TODO BE: productBox
						{* {control 'productBox-' . $product->id} *}
					</div>
				</div>
			</div>
		</div>
		<button class="embla__btn embla__btn--prev" disabled="disabled" type="button" data-action="embla#prev" data-embla-target="prevButton">
			{('arrow-left')|icon}
			<span class="u-vhide">{_btn_prev}</span>
		</button>
		<button class="embla__btn embla__btn--next" disabled="disabled" type="button" data-action="embla#next" data-embla-target="nextButton">
			{('arrow-right')|icon}
			<span class="u-vhide">{_btn_next}</span>
		</button>
	</div>
</section>