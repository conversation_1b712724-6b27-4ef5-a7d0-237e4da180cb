{default $class = false}

<div n:class="c-else, $class">
	<h2 class="c-else__title u-ta-c">
		{_"basket_else_title"|noescape}
	</h2>
	<div class="c-else__carousel embla" data-controller="embla" data-embla-init-value="lgDown">
		<div class="embla__viewport" data-embla-target="viewport">
			<ul class="c-else__grid grid grid--x-0 grid--y-0 grid--scroll embla__container">
				{for $i = 0; $i < 6; $i++}
					<li class="c-else__cell grid__cell">
						{include $templates.'/part/box/else.latte'}
					</li>
				{/for}
			</ul>
		</div>
		<button class="embla__btn embla__btn--prev" disabled="disabled" type="button" data-action="embla#prev" data-embla-target="prevButton">
			{('arrow-left')|icon}
			<span class="u-vhide">{_btn_prev}</span>
		</button>
		<button class="embla__btn embla__btn--next" disabled="disabled" type="button" data-action="embla#next" data-embla-target="nextButton">
			{('arrow-right')|icon}
			<span class="u-vhide">{_btn_next}</span>
		</button>
	</div>
</div>