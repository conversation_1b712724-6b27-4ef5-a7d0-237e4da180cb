{default $class = 'u-mb-md u-mb-2xl@md'}
{default $title = false}
{default $annot = false}

<section n:class="c-projects, $class">
	<div class="u-mb-last-0 u-mb-xs u-mb-sm@md">
		<h2 n:if="$title" class="u-mb-xs">{$title}</h2>
		<p n:if="$annot">{$annot}</p>
	</div>
	<div class="grid grid--x-sm grid--y-xs grid--y-sm@md">
		{for $i = 0; $i < 7; $i++}
			<div n:class="c-projects__item, grid__cell, 'size--6-12@md size--3-12@xl', $i >= 4 ? u-d-n">
				{include $templates.'/part/box/project.latte'}
			</div>
		{/for}
	</div>
	{* zobrazit pouze pokud je projektů více jak 4 *}
	<p class="c-projects__more u-ta-c">
		<button type="button" class="btn btn--bd" data-controller="toggle-class" data-action="toggle-class#toggle" data-toggle-class="u-d-n" data-toggle-content=".c-projects__item.u-d-n">
			<span class="btn__text">
				<span class="btn__inner">
					{_"btn_show_all_projects"}
					{('angle-down')|icon, 'btn__icon'}
				</span>
			</span>
		</button>
	</p>
</section>