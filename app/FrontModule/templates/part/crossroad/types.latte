{default $class = 'u-mb-md u-mb-3xl@md'}
{default $cf = $object->cf->types ?? false}

<div n:if="$cf" n:class="c-types, $class">
	{define #section}
		<div class="c-types__head u-mb-last-0">
			<h2 n:if="$title" class="c-types__title">{$title}</h2>
			<p n:if="$annot">{$annot}</p>
			{($icon)|icon, 'c-types__icon'}
		</div>
		<ul n:if="count($items)" class="c-types__list">
			<li n:foreach="$items as $item" class="c-types__item link-mask">
				{php $type = $item->link->toggle}
				{php $page = isset($item->link->systemHref) && isset($item->link->systemHref->page) ? $item->link->systemHref->page->getEntity() ?? false : false}
				{php $href = $item->link->customHref??->href ?? false}
				{php $hrefNameSystem = $item->link->systemHref??->hrefName ?? false}
				{php $hrefNameCustom = $item->link->customHref??->hrefName ?? false}

				{if $type == 'systemHref' && $page}
					<span>
						<b class="c-types__name">
							{if $hrefNameSystem}
								{$hrefNameSystem}
							{else}
								{$page->nameAnchor}
							{/if}
						</b>
						{$item->desc}
					</span>
					<a href="{plink $page}" class="c-types__btn btn btn--micro link-mask__link">
						<span class="btn__text">
							<span class="btn__inner">
								{_"btn_more"}
								{('angle-right')|icon, 'btn__icon'}
							</span>
						</span>
					</a>
				{elseif $type == 'customHref' && $href && $hrefNameCustom}
					<span>
						<b class="c-types__name">{$hrefNameCustom}</b>
						{$item->desc}
					</span>
					<a href="{$href}" class="c-types__btn btn btn--micro link-mask__link" target="_blank" rel="noopener noreferrer">
						<span class="btn__text">
							<span class="btn__inner">
								{_"btn_more"}
								{('angle-right')|icon, 'btn__icon'}
							</span>
						</span>
					</a>
				{/if}

			</li>
		</ul>
	{/define}

	{* TODO: napojit jednotlivé položky listu *}
	<div class="c-types__grid grid">
		<div n:if="$cf->licence ?? false" class="grid__cell size--6-12@md">
			{include #section,
				title: $cf->licence->title ?? false,
				annot: $cf->licence->desc ?? false,
				icon: 'ridicak',
				items => $cf->licence->items ?? [],
			}
		</div>
		<div n:if="$cf->course ?? false" class="grid__cell size--6-12@md">
			{include #section,
				title: $cf->course->title ?? false,
				annot: $cf->course->desc ?? false,
				icon: 'dron-hand-thin',
				items => $cf->course->items ?? [],
			}
		</div>
	</div>
</div>