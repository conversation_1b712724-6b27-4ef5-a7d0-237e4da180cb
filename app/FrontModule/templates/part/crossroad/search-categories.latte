{default $class = 'u-mb-xs u-mb-sm@md'}
{default $maxVisible = 5}

<div n:class="c-search-categories, box, box--sand, $class" n:if="$fulltextResults !== []">
	{* todo links, counts, show more (data-controller, data-action, ...) *}
	<div class="c-search-categories__grid grid">
		{foreach $fulltextResults as $fulltextResult}
			{varType App\Model\FulltextSearch\Result $fulltextResult}
			<div class="c-search-categories__cell c-search-categories__cell--categories grid__cell u-mb-last-0" data-controller="toggle-class">
				<p class="u-mb-xxs">
					{_"search_category_" . strtolower($fulltextResult->name)} ({count($fulltextResult->items)})
				</p>
				<ul class="c-search-categories__list">
					{php $visibleItems = 0}
					{foreach $fulltextResult->items as $item}
						{if $item instanceOf App\Model\Orm\Routable}
							{php $visibleItems++}
							<li n:class="c-search-categories__item, $visibleItems > $maxVisible ? 'c-search-categories__item--hidden'">
								<a n:href="$item">{$item->nameAnchor}</a>
							</li>
						{/if}
					{/foreach}
				</ul>
				<p n:if="$visibleItems > $maxVisible" class="c-search-categories__more">
					<button type="button" class="as-link" data-action="toggle-class#toggle" data-toggle-content=".c-search-categories__item--hidden" aria-expanded="false">
						<span>
							{_"show_more"}
						</span>
						<span>
							{_"show_less"}
						</span>
					</button>
				</p>
			</div>
		{/foreach}
	</div>
</div>
