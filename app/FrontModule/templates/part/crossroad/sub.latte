{default $class = 'u-mb-md u-mb-2xl@md'}
{default $crossroad = $object->crossroad ?? []}

<section n:if="count($crossroad)" n:class="c-sub, $class">
	<ul class="grid grid--x-sm grid--y-sm">
		<li n:foreach="$crossroad as $c" n:if="$c->public && !$c->hideInMenu" class="grid__cell size--6-12@lg size--4-12@xl">
			<div class="tw-rounded-xl tw-p-[3.2rem_2rem_2.4rem] md:tw-p-[3.2rem] link-mask tw-border-solid tw-border-[0.1rem] tw-border-tile hover:tw-border-gray-500 tw-transition-[border-color] tw-duration-300 tw-h-full tw-flex tw-gap-[1.6rem] md:tw-gap-[2.4rem]">
				<div class="tw-flex-1 tw-flex tw-flex-col">
					<div class="u-mb-last-0 tw-mb-[1.2rem] md:tw-mb-[2rem]">
						<h2 class="h3 tw-mb-[0.4rem] md:tw-mb-[0.8rem] tw-text-balance">
							<a href="{plink $c}" class="c-sub__link link-mask__link tw-no-underline">
								{$c->nameAnchor} {('angle-right')|icon, 'tw-w-[1.2rem]'}
							</a>
						</h2>
						<p n:if="$c->cf->base->crossroadAnnotation ?? false" class="tw-text-help md:tw-text-[1.5rem]">
							{$c->cf->base->crossroadAnnotation}
						</p>
					</div>
					<p class="tw-mb-0 tw-mt-auto">
						<a href="{plink $c}" class="btn btn--lg link-mask__unmask">
							<span class="btn__text">
								{_"btn_find_more"}
							</span>
						</a>
					</p>
				</div>
				<img n:if="$c->cf->base->crossroadImage ?? false" class="tw-relative tw-flex-[0_0_auto] tw-top-[-1.2rem] md:tw-top-[-2rem] tw-self-start md:tw-w-[8rem] tw-aspect-auto" src="{$c->cf->base->crossroadImage->getSize('xs')->src}" alt="" loading="lazy" width="60" height="60">
			</div>
		</li>
	</ul>
</section>