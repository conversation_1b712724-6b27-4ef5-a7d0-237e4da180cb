{default $class = 'u-mb-md u-mb-2xl@md'}
{default $title = false}

<div n:class="$class">
	<h2 n:if="$title" class="tw-mb-[1.2rem] md:tw-mb-[2.4rem]">{$title}</h2>

	{include $templates.'/part/menu/tabs-btns.latte', items: ['Natáčení eventů','E-shop','Natáčení svatby','Livestream','Armáda','Zemědělství a lesnictví','Armáda','Zemědělství a lesnictví']}

	<div class="grid grid--x-xs grid--x-sm@md grid--y-xs grid--y-sm@md">
		<div n:for="$i = 0; $i < 6; $i++" class="grid__cell size--6-12@md size--4-12@lg">
			{include $templates.'/part/box/reference.latte', text: 'TODO: napojit reference', name: 'Dummy name', info: 'Dummy info'}
		</div>
	</div>

	{* {control pager, [filter => $cleanFilterParam, pluralLang: 'btn_more_showcase', ajaxPage=>$ajaxPage]} *}
	TODO: paging component
</div>
