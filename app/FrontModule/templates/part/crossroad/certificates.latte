{default $class = 'u-mb-md u-mb-2xl@md'}
{default $title = false}
{default $items = []}

<div n:class="$class, u-maw-8-12, u-mx-auto">
	<h2 n:if="title" class="tw-mb-[1.2rem] mdtw-mb-[2.4rem]">{$title}</h2>
	<ul n:if="count($items)" class="grid grid--x-xs grid--y-xs grid--x-sm@md grid--y-sm@md u-ta-c">
		<li n:foreach="$items as $item" n:class="grid__cell, $iterator->isFirst() ? 'size--6-12@sm' : 'size--6-12'">
			<p class="u-mb-0 tw-bg-bg tw-rounded-md md:tw-rounded-xl tw-p-[2rem] md:tw-p-[4rem] link-mask tw-h-full">
				<a n:tag-if="$item->link ?? false" href="#" class="link-mask__link">
					{php $image = isset($item->image) ? $item->image->getEntity() ?? false : false}
					{php $text = $item->text ?? false}
					<img n:class="u-d-b, $text ? 'u-mx-auto tw-mb-[1.2rem] md:tw-mb-[2.4rem] tw-h-[8rem] md:tw-h-[12rem]'" n:if="$image" src="{$image->getSize('sm')->src}" alt="{$image->getAlt($mutation)}" loading="lazy">
				</a>
				{$text}
			</p>
		</li>
	</ul>
</div>