{default $class = 'u-mb-md u-mb-2xl@md'}
{default $title = false}
{default $items = []}

<div n:class="c-services, $class, u-maw-8-12, u-mx-auto">
	<h2 n:if="$title" class="u-mb-sm">
		{$title}
	</h2>
	<div n:if="count($items)" class="c-services__grid grid">
		<div n:foreach="$items as $item" class="grid__cell size--4-12@md link-mask">
			<div class="c-services__inner u-mb-last-0">
				{php $img = isset($item->image) ? $item->image->getEntity() ?? false : false}
				<p n:if="$img" class="c-services__img">
					<img class="img img--contain" src="{$img->getSize('md')->src}" alt="{$img->getAlt($mutation)}" loading="lazy">
				</p>
				<h3 n:if="$item->link ?? false" class="c-services__name">
					{php $type = $item->link->toggle}
					{php $page = isset($item->link->systemHref) && isset($item->link->systemHref->page) ? $item->link->systemHref->page->getEntity() ?? false : false}
					{php $href = $item->link->customHref??->href ?? false}
					{php $hrefNameSystem = $item->link->systemHref??->hrefName ?? false}
					{php $hrefNameCustom = $item->link->customHref??->hrefName ?? false}

					{if $type == 'systemHref' && $page}
						<a href="{plink $page}" n:ifcontent class="c-services__link link-mask__link">
							{if $hrefNameSystem}
								{$hrefNameSystem}
							{else}
								{$page->nameAnchor}
							{/if}
						</a>
					{elseif $type == 'customHref' && $href && $hrefNameCustom}
						<a href="{$href}" class="c-services__link link-mask__link" target="_blank" rel="noopener noreferrer">
							{$hrefNameCustom}
						</a>
					{/if}
				</h3>
				<p n:if="$item->annot ?? false">
					{$item->annot}
				</p>
			</div>
		</div>
	</div>
</div>