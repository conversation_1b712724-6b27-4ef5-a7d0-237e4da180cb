{default $class = false}
{default $items = $object->cf->presskit ?? []}

<div n:if="count($items)" n:class="c-presskit, $class">
	<h2 class="c-presskit__title h4">
		{_"title_presskit"}
	</h2>
	<ul class="c-presskit__list">
		<li n:foreach="$items as $item" class="c-presskit__item item-icon link-mask">
			{php $file = isset($item->file) ? $item->file->getEntity() ?? false : false}
			{php $ext = pathinfo($file->name)['extension']}
			{php $icon = 'file'}

			{if in_array($ext, ['jpg', 'png', 'gif', 'jpeg', 'webp'])}
				{php $icon = 'img'}
			{elseif in_array($ext, ['svg'])}
				{php $icon = 'svg'}
			{/if}

			{($icon)|icon, 'item-icon__icon'}
			<span class="item-icon__text">
				<a href="{$file->url}" class="link-mask__link" download>{$file->name}</a>
				<span class="c-presskit__size">({$file->size|formatSize})</span>
			</span>
		</li>
	</ul>
	<p class="c-presskit__btn u-mb-0">
		{* TODO stáhnout vše link + velikost *}
		<a href="#" class="btn">
			<span class="btn__text">
				<span class="btn__inner">
					{('download')|icon, 'btn__icon'}
					{_"btn_download_all_zip"} (18,3 MB)
				</span>
			</span>
		</a>
	</p>
</div>