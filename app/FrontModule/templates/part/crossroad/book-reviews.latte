{default $class = false}
{default $reviews = []}

<section n:class="section, section--reviews, $class">
	<div class="section__header">
		<h2>
			{_"book_reviews_title"}
		</h2>
		<p>
			<a href="https://magazin.dobre-knihy.cz" target="_blank">{_"book_reviews_link"}</a>
		</p>
	</div>
	<div class="section__carousel embla" data-controller="embla">
		<div class="embla__holder">
			<div class="embla__viewport" data-embla-target="viewport">
				<div class="section__grid grid grid--scroll grid--y-0 embla__container">
					<div class="section__cell grid__cell" n:foreach="$reviews as $review">
						{include $templates.'/part/box/book-review.latte', review: $review, class:false}
					</div>
				</div>
			</div>
		</div>
		<button class="embla__btn embla__btn--prev" disabled="disabled" type="button" data-action="embla#prev" data-embla-target="prevButton">
			{('arrow-left')|icon}
			<span class="u-vhide">{_btn_prev}</span>
		</button>
		<button class="embla__btn embla__btn--next" disabled="disabled" type="button" data-action="embla#next" data-embla-target="nextButton">
			{('arrow-right')|icon}
			<span class="u-vhide">{_btn_next}</span>
		</button>
	</div>
</section>
