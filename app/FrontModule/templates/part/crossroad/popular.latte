{default $class = 'u-mb-xs'}
{default $items = []}
{default $titleLang = 'popular_title_writers'}

<div  n:if="count($items)" n:class="c-popular, $class, u-bgc-sand-light-3, u-round">
	<ul class="c-popular__list grid grid--middle">
		<li class="c-popular__item grid__cell">
			<h2 class="h4 u-mb-0">
				{_$titleLang}
			</h2>
		</li>
		<li n:foreach="$items as $item" n:class="c-popular__item, grid__cell, $iterator->getCounter() > 6 ? is-mobile-hidden">
			{php $image = isset($item->cf->gallery) && isset($item->cf->gallery->mainImage) ? $item->cf->gallery->mainImage->getEntity() ?? false : false}
			{include '../core/person.latte', image=>$image, personLink=>$item, name=>$item->name, position=>false, link=>false, class=>'c-popular__person u-mb-0'}
		</li>
	</ul>
	<p class="c-popular__btn u-pt-xxs u-mb-0 u-d-n@md">
		<button type="button" class="btn btn--shadow btn--sm btn--arrow" data-controller="toggle-class" aria-expanded="false" data-action="toggle-class#toggle" data-toggle-class="is-mobile-hidden" data-toggle-content=".c-popular__item.is-mobile-hidden">
			<span class="btn__text">
				{_"btn_show_more_authors"}
				{('angle-down')|icon, 'btn__icon'}
			</span>
		</button>
	</p>
</div>
