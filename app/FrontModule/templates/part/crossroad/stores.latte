{default $class = 'u-mb-md u-mb-2xl@md'}
{default $stores = $pages->contact?->crossroad}
{default $storesCf = $object->cf->stores ?? []}

<div n:class="c-stores, $class">
	<div class="c-stores__grid grid">
		<div n:foreach="$stores as $store" class="grid__cell size--6-12@md">
			{php $storeCf = $store->cf->store_info ?? false}
			{embed $templates.'/part/box/store.latte', class: false, title: $store->nameAnchor, img: $storeCf ? $storeCf->images?->fetch() : false}
				{block content}
					<div class="u-mb-last-0">
						<p n:if="$storeCf->address ?? false" class="u-mb-0">
							{$storeCf->address|texy|noescape}
						</p>
						<p n:if="$storeCf->hours ?? false" class="u-c-help">
							{$storeCf->hours}
						</p>
					</div>
					<p>
						<a href="{plink $store}" class="btn btn--sm btn--bd link-mask__link">
							<span class="btn__text">
								{_"btn_link_store"}
							</span>
						</a>
					</p>
				{/block}
			{/embed}
		</div>
		<div n:foreach="$storesCf as $store" class="grid__cell size--6-12@md">
			{php $img = isset($store->img) ? $store->img->getEntity() ?? false : false}
			{embed $templates.'/part/box/store.latte', class: false, title: $store->title ?? fales, img: $img}
				{block content}
					<div n:if="$store->content ?? false" class="u-mb-last-0">
						{$store->content|noescape}
					</div>
					<p n:if="$store->btn ?? false">
						{php $type = $store->btn->toggle}
						{php $page = isset($store->btn->systemHref) && isset($store->btn->systemHref->page) ? $store->btn->systemHref->page->getEntity() ?? false : false}
						{php $href = $store->btn->customHref??->href ?? false}
						{php $hrefNameSystem = $store->btn->systemHref??->hrefName ?? false}
						{php $hrefNameCustom = $store->btn->customHref??->hrefName ?? false}

						{if $type == 'systemHref' && $page}
							<a href="{plink $page}" n:ifcontent class="btn btn--sm btn--bd link-mask__link">
								<span class="btn__text">
									{if $hrefNameSystem}
										{$hrefNameSystem}
									{else}
										{$page->nameAnchor}
									{/if}
								</span>
							</a>
						{elseif $type == 'customHref' && $href && $hrefNameCustom}
							<a href="{$href}" class="btn btn--sm btn--bd link-mask__link" target="_blank" rel="noopener noreferrer">
								<span class="btn__text">
									{$hrefNameCustom}
								</span>
							</a>
						{/if}
					</p>
				{/block}
			{/embed}
		</div>
	</div>
</div>