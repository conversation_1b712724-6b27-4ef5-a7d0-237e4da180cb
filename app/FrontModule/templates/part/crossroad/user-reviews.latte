{default $class = 'u-mb-xs'}
{default $title = false}
{default $reviews = []}
{default $type = false}

<div n:if="count($reviews)" n:class="c-user-reviews, $class">
	<h2 n:if="$title" class="c-user-reviews__title h4">
		{_$title}
	</h2>
	<ul class="c-user-reviews__list">
		<li n:foreach="$reviews as $review" class="c-user-reviews__item">
			{include $templates.'/part/box/user-review.latte', class: false, review: $review, product: $type == 'rated' ? $review->product : $review}
		</li>
	</ul>
</div>
