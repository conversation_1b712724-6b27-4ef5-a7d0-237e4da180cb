{default $class = false}

<div n:class="c-extra-services, $class, u-maw-8-12, u-mx-auto">
	<h2 class="c-extra-services__title u-ta-c">
		{_"prebasket_services_title"|noescape}
	</h2>
	<div class="c-extra-services__wrap">
		<p class="c-extra-services__product">
			<a href="{plink $variant->product, v: $variant->id}">
				{* img--contain pouze pro ne-kurzy *}
				{if $variant->firstImage}
					{php $img = $variant->firstImage->getSize('sm-4-3')}
					<img class="img img--4-3 img--contain" src="{$img->src}" alt="{$variant->name}" loading="lazy">
				{else}
					<img class="img img--4-3 img--contain" src="/static/img/illust/noimg.svg" alt="" loading="lazy">
				{/if}
				{$variant->name}
			</a>
		</p>
		<ul class="c-extra-services__items">
			{for $i = 0; $i < 4; $i++}
				{php $active = $i == 1}

				<li n:class="c-extra-services__item, $active ? is-active, link-mask">
					<span n:if="$active" class="c-extra-services__check">{('check')|icon}</span>
					<div class="c-extra-services__info u-mb-last-0">
						<p class="c-extra-services__name">
							<a href="#" class="c-extra-services__link link-mask__link">Pojištění proti krádeži a poškození na 1 rok</a>
							{embed $templates.'/part/core/tooltip.latte', class: 'c-extra-services__tooltip tooltip--gray link-mask__unmask', placement: 'right'}
								{block btn}
									{('info')|icon}
								{/block}
								{block content}
									tooltip
								{/block}
							{/embed}
						</p>
						<p>
							ERV Evropská pojišťovna
						</p>
					</div>
					<p class="c-extra-services__price">
						3 390 Kč
					</p>
					<p class="c-extra-services__btn">
						{if $active}
							<a href="#" class="btn btn--block btn--loader link-mask__unmask"> {* is-loading *}
								<span class="btn__text">
									<span class="btn__inner">
										{('cross')|icon, 'btn__icon'}
										{_"btn_throw"}
									</span>
								</span>
							</a>
						{else}
							<a href="#" class="btn btn--block btn--loader link-mask__unmask"> {* is-loading *}
								<span class="btn__text">
									{_"btn_want"}
								</span>
							</a>
						{/if}
					</p>
				</li>
			{/for}
		</ul>
	</div>
</div>
