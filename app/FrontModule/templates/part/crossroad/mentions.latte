{default $class = 'u-mb-sm u-mb-2xl@lg'}
{default $cf = $pages->media->cf->mentions ?? false}

<section n:if="$cf" n:class="c-mentions, $class">
	<div class="u-maw-11-12 u-mx-auto">
		<h2 n:if="$cf->title ?? false" class="c-mentions__title">
			{$cf->title}
		</h2>
		<p n:if="$cf->annot" class="c-mentions__annot">
			{$cf->annot|texy|noescape}
		</p>

		{php $logos = $cf->logos??->fetchAll() ?? []}
		<p n:if="count($logos)" class="c-mentions__logos">
			<img n:foreach="$logos as $logo" class="c-mentions__logo img img--2-1" src="{$logo->getSize('sm')->src}" alt="$logo->getAlt($mutation)" loading="lazy" width="121" height="68">
		</p>
		<div class="c-mentions__grid grid">
			{for $i = 0; $i < 3; $i++}
				<div class="grid__cell size--6-12@md size--4-12@lg">
					{include $templates.'/part/box/mention.latte', class: false}
				</div>
			{/for}
		</div>
		<p class="c-mentions__more">
			<a href="#" class="btn btn--bd btn--lg">
				<span class="btn__text">
					{_"btn_all_articles"}
				</span>
			</a>
		</p>
	</div>
</section>