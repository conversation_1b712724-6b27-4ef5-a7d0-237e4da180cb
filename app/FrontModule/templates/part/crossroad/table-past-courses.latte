{default $class = 'tw-mb-[2.4rem] md:tw-mb-[3.2rem]'}
{default $title = false}
{default $emptyMsg = false}
{default $items = []}

<div n:if="count($items) || $emptyMsg" n:class="$class">
	<h2 n:if="$title" class="h3 tw-mb-[1.2rem]">{$title}</h2>
	{if count($items)}
		<table class="c-table tw-grid-cols-[auto_1fr] md:tw-grid-cols-[max-content_auto_auto_auto] tw-text-[1.3rem] md:tw-text-[1.5rem]">
			<thead>
				<tr class="u-c-help tw-text-[1.3rem] tw-gap-[0.8rem] md:tw-gap-[3rem] xxxl:tw-gap-[6rem]">
					<th>{_"course_form"}</th>
					<th>{_"course_name"}</th>
					<th>{_"course_date"}</th>
					{*<th>{_"course_materials"}</th>*}
				</tr>
			</thead>
			<tbody>
			{foreach $items as $classItem}
				<tr class="tw-gap-[0.8rem] md:tw-gap-[3rem] xxxl:tw-gap-[6rem] tw-p-[1.2rem_1.6rem] md:tw-p-[2rem]">
					<td><span n:if="$classItem->product->getCourseTypeParameterValue() !== null" class="flag flag--sm flag--purple-light">{$classItem->product->getCourseTypeParameterValue()->value}</span></td>
					<td class="max-md:tw-row-start-1 max-md:tw-col-start-1 max-md:tw-col-end-3">
						<a n:tag-if="$classItem->product !== null" href="{plink $classItem->product}" class="tw-font-semibold">{$classItem->productName}</a>
					</td>
					<td>
						{if !empty($classItem->classEventDate)}
						{$classItem->classEventDate}
						{/if}
						{*<span class="tw-text-[1.3rem] max-md:tw-ml-[0.4rem] md:tw-block tw-text-status-invalid">Přístup k videu do 14. 5. 2024</span>*}
					</td>
					{*
					<td class="max-md:tw-col-start-1 max-md:tw-col-end-3">
						<ul class="u-reset-ul tw-text-[1.3rem] md:tw-text-[1.4rem] u-c-help tw-flex tw-flex-col tw-gap-[0.8rem]">
							{include #fileItem, text: 'Certifikát o absolvování', link: '#', ext: 'pdf'}
							{include #fileItem, text: 'Podklady ke stažení', link: '#', ext: 'zip'}
							{include #fileItem, text: 'Tabulka o výpočtu zrychlení', link: '#', ext: 'xls'}
							{include #videoItem, text: 'Videozáznam', link: '#'}

							<li class="tw-flex">
								<span class="item-icon tw-flex tw-flex-1 tw-p-[0.4rem_0.8rem] md:tw-p-[0.8rem_1.2rem] tw-rounded-sm tw-bg-primary-150">
									{('basket')|icon, 'item-icon__icon tw-text-primary'}
									<span class="item-icon__text">
										<a href="#">Koupit obnovení přístupu k videu</a><br>
										<span class="tw-flex tw-gap-[0.8rem] tw-text-[1.2rem] md:tw-text-[1.3rem]">
											<s>3 500 Kč</s>
											<b class="tw-text-status-invalid">-8 %</b>
											<b class="tw-text-black">3 000 Kč</b>
										</span>
									</span>
								</span>
							</li>
						</ul>
					</td>
					*}
				</tr>
			{/foreach}
			</tbody>
		</table>
	{else}
		<p class="message message--md u-mb-0">
			<span class="message__emoji">👉</span>
			<span class="message__content">
				{$emptyMsg|noescape}
			</span>
		</p>
	{/if}
</div>

{define #fileItem}
	{default $ext = false}
	{default $text = false}
	{default $link = false}

	<li n:if="$text && $link && $ext" class="tw-flex">
		<span class="item-icon tw-px-[0.8rem] md:tw-px-[1.2rem]">
			{include $templates.'/part/core/file-icon.latte', class: 'item-icon__icon tw-text-icon-minor', ext: $ext}
			<span class="item-icon__text">
				<a href="{$link}" download>{$text}</a> <span n:if="$ext" class="tw-text-[1.2rem]">({$ext|upper})</span>
			</span>
		</span>
	</li>
{/define}

{define #videoItem}
	{default $link = false}
	{default $text = false}

	<li n:if="$text && $link" class="tw-flex">
		<span class="item-icon tw-px-[0.8rem] md:tw-px-[1.2rem]">
			{('play')|icon, 'item-icon__icon tw-text-icon-minor'}
			<span class="item-icon__text">
				<a href="{$link}" data-modal>{$text}</a>
			</span>
		</span>
	</li>
{/define}
