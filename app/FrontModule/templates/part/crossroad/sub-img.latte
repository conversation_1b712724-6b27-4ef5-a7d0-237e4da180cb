{default $class = 'u-mb-sm u-mb-md@md'}
{default $crossroad = $object->crossroad ?? []}
{default $title = false}
{default $titleTag = $title ? 'h3' : 'h2'}


<section n:if="count($crossroad)" n:class="c-articles, $class">
	<h2>{$title}</h2>
	<div class="c-articles-carousel__grid grid grid--x-xs grid--y-xs grid--x-sm@md grid--y-sm@md">
		<div n:foreach="$crossroad as $c" n:if="$c->public && !$c->hideInMenu" class="c-articles-carousel__cell grid__cell">
			<article class="b-article">
				<p class="b-article__img">
					{php $img = isset($c->cf->base->crossroadImage) ? $c->cf->base->crossroadImage->getEntity() ?? false : false}

					{if $img}
						<img class="img img--16-9" src="{$img->getSize('md-16-9')->src}" alt="{$img->getAlt($mutation)}" loading="lazy">
					{else}
						<img class="img img--16-9" src="/static/img/illust/noimg.svg" alt="" loading="lazy">
					{/if}
				</p>
				<div class="b-article__content u-mb-last-0">
					<h2 n:tag="$titleTag" class="b-article__title h4">
						<a href="{plink $c}" class="b-article__link link-mask__link">
							{$c->nameAnchor}
						</a>
					</h2>
					<p n:if="$c->cf->base->crossroadAnnotation ?? false" class="b-article__desc">
						{$c->cf->base->crossroadAnnotation}
					</p>
				</div>
			</article>
		</div>
	</div>
</section>