{default $class = 'u-mb-md u-mb-xl@md'}
{default $title = false}
{default $items = []}

<section {* n:if="count($items)" *} n:class="c-terms, $class">
	<h2 n:if="$title" class="tw-mb-[1.2rem] md:tw-mb-[2.4rem]">{$title}</h2>
	<div class="c-terms__carousel embla" data-controller="embla">
		<div class="embla__viewport" data-embla-target="viewport">
			<div class="grid grid--y-0 grid--x-sm grid--scroll embla__container">
				{for $i = 0; $i < 4; $i++}
					<div class="c-terms__cell grid__cell">
						{include $templates.'/part/box/term.latte', titleTag: $title ? 'h3' : 'h2'}
					</div>
				{/for}
			</div>
		</div>
		<button class="embla__btn embla__btn--prev" disabled="disabled" type="button" data-action="embla#prev" data-embla-target="prevButton">
			{('arrow-left')|icon}
			<span class="u-vhide">{_btn_prev}</span>
		</button>
		<button class="embla__btn embla__btn--next" disabled="disabled" type="button" data-action="embla#next" data-embla-target="nextButton">
			{('arrow-right')|icon}
			<span class="u-vhide">{_btn_next}</span>
		</button>
	</div>
</section>