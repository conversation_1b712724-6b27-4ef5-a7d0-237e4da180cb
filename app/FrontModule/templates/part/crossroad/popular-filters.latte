{default $class = 'u-mb-xs u-mb-sm@lg'}
{default $items = $object->cf->popularFilters ?? []}

<div n:if="count($items)" n:class="b-filters, b-filters--popular, $class">
	<p class="b-filters__title">
		{_"popular_filters"}
	</p>
	<ul class="b-filters__grid grid">
		<li n:foreach="$items as $item" n:if="($item->link ?? false) && ($item->text ?? false)" class="grid__cell size--auto">
			<a href="{$item->link}" class="b-filters__flag flag flag--blue-light">{$item->text}</a>
		</li>
	</ul>
</div>