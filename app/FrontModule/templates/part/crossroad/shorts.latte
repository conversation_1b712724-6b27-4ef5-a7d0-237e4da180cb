{default $class = 'u-mb-md u-mb-2xl@md'}

<div n:class="c-shorts, $class">
	<h2 class="c-shorts__title h3">{_"title_shorts"}</h2>

	<div class="c-shorts__carousel embla" data-controller="embla">
		<div class="embla__viewport" data-embla-target="viewport">
			<div class="c-shorts__grid grid grid--scroll embla__container grid--x-xs grid--x-sm@md grid--y-0">
				{for $i = 0; $i < 10; $i++}
					<div class="c-shorts__cell grid__cell">
						{include $templates.'/part/box/short.latte', class: false}
					</div>
				{/for}
			</div>
		</div>
		<button class="embla__btn embla__btn--prev" disabled="disabled" type="button" data-action="embla#prev" data-embla-target="prevButton">
			{('arrow-left')|icon}
			<span class="u-vhide">{_btn_prev}</span>
		</button>
		<button class="embla__btn embla__btn--next" disabled="disabled" type="button" data-action="embla#next" data-embla-target="nextButton">
			{('arrow-right')|icon}
			<span class="u-vhide">{_btn_next}</span>
		</button>
	</div>

	<p n:if="$pages->contact??->cf->socials->youtube ?? false" class="c-shorts__more">
		<a href="{$pages->contact->cf->socials->youtube}/shorts" class="btn btn--bd btn--lg" target="_blank" rel="noopener noreferrer">
			<span class="btn__text">
				{_"btn_shorts"}
			</span>
		</a>
	</p>
</div>