{default $class = 'u-mb-md u-mb-3xl@md'}
{default $cf = $object->cf->fleet ?? false}

<div n:if="$cf" n:class="c-fleet, $class">
	<div class="c-fleet__bg">
		<img src="/static/img/illust/dragon.webp" alt="" loading="lazy">
	</div>
	<div class="c-fleet__head u-maw-6-12 u-mx-auto u-mb-last-0">
		<h2 n:if="$cf->title ?? false" class="c-fleet__title h1">
			{$cf->title} <span class="c-fleet__emoji">🐲</span>
		</h2>
		<p n:if="$cf->annot ?? false" class="c-fleet__annot">{$cf->annot|texy|noescape}</p>
	</div>
	<div n:if="count($cf->items ?? [])" class="c-fleet__carousel embla" data-controller="embla">
		<div class="embla__viewport" data-embla-target="viewport">
			<div class="c-fleet__grid grid grid--scroll grid--y-0 embla__container">
				<div n:foreach="$cf->items as $item" class="c-fleet__cell grid__cell">
					{include $templates.'/part/box/product-condensed.latte', item: $item}
				</div>
			</div>
			<button class="embla__btn embla__btn--prev" disabled="disabled" type="button" data-action="embla#prev" data-embla-target="prevButton">
				{('arrow-left')|icon}
				<span class="u-vhide">{_btn_prev}</span>
			</button>
			<button class="embla__btn embla__btn--next" disabled="disabled" type="button" data-action="embla#next" data-embla-target="nextButton">
				{('arrow-right')|icon}
				<span class="u-vhide">{_btn_next}</span>
			</button>
		</div>
		{* <div class="embla__progress"><div class="embla__progress-inner" data-embla-target="progress"></div></div> *}
	</div>
</div>