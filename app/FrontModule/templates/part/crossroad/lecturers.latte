{default $class = 'u-mb-md u-mb-3xl@md'}
{default $cf = $object->cf->lecturers ?? false}

<div n:if="$cf" n:class="c-lecturers, $class">
	<div class="c-lecturers__head u-maw-6-12 u-mx-auto u-mb-last-0">
		<h2 n:if="$cf->title ?? false" class="c-lecturers__title h1">{$cf->title}</h2>
		<p n:if="$cf->annot ?? false" class="c-lecturers__annot">{$cf->annot|texy|noescape}</p>
	</div>
	<div n:if="count($cf->items ?? [])" class="c-lecturers__carousel embla" data-controller="embla">
		<div class="embla__viewport" data-embla-target="viewport">
			<div class="c-lecturers__grid grid grid--scroll grid--y-0 embla__container">
				<div n:foreach="$cf->items as $item" class="c-lecturers__cell grid__cell">
					<div class="c-lecturers__item">
						{php $img = isset($item->image) ? $item->image->getEntity() ?? false : false}
						{if $img}
							<img class="c-lecturers__img" src="{$img->getSize('lg-9-16')->src}" alt="" loading="lazy">
						{else}
							<img class="c-lecturers__img" src="/static/img/illust/noimg.svg" alt="" loading="lazy">
						{/if}

						<p n:if="$item->video ?? false" class="c-lecturers__play">
							<a href="{$item->video}" data-modal>
								{('play')|icon}
								<span class="u-vhide">{_"btn_play"}</span>
							</a>
						</p>

						<div n:ifcontent class="c-lecturers__bubble u-mb-last-0">
							{('bubble-ear')|icon, 'c-lecturers__bubble-ear'}
							<p n:if="$item->name ?? false" class="c-lecturers__name h4">
								{$item->name}
							</p>
							<p n:if="$item->text ?? false">
								{$item->text}
							</p>
						</div>
					</div>
				</div>
			</div>
			<button class="embla__btn embla__btn--prev" disabled="disabled" type="button" data-action="embla#prev" data-embla-target="prevButton">
				{('arrow-left')|icon}
				<span class="u-vhide">{_btn_prev}</span>
			</button>
			<button class="embla__btn embla__btn--next" disabled="disabled" type="button" data-action="embla#next" data-embla-target="nextButton">
				{('arrow-right')|icon}
				<span class="u-vhide">{_btn_next}</span>
			</button>
		</div>
		{* <div class="embla__progress"><div class="embla__progress-inner" data-embla-target="progress"></div></div> *}
	</div>
</div>