{default $class = 'u-mb-md u-mb-2xl@md'}

<div id="positions" n:class="c-positions, $class, u-maw-8-12, u-mx-auto">
	<h2 class="u-mb-xs u-mb-sm@md">
		{_"positions_title"}
	</h2>
	<div class="grid grid--y-sm">
		<div class="grid__cell size--7-12@md">
			<ul class="c-positions__list">
				{for $i = 0; $i < 5; $i++}
					<li n:class="c-positions__item, $i > 3 ? u-d-n">
						<a href="#" class="c-positions__link">
							Specialista
							{('angle-right')|icon}
						</a>
					</li>
				{/for}
			</ul>
			<p class="c-positions__more u-pt-xs u-pt-sm@md u-mb-0">
				<a href="#" class="btn btn--bd" data-controller="toggle-class" data-action="toggle-class#toggle" data-toggle-content=".c-positions__item.u-d-n" data-toggle-class="u-d-n">
					<span class="btn__text">
						<span class="btn__inner">
							{_"btn_show_all_positions"}
							{('angle-down')|icon, 'btn__icon'}
						</span>
					</span>
				</a>
			</p>
		</div>
		<div class="grid__cell size--5-12@md">
			<div class="c-positions__offer link-mask">
				<h2 class="h4 u-mb-xxs">
					{_"positions_offer_title"}
				</h2>
				<p>
					{_"positions_offer_text"}
				</p>
				<p class="u-mb-0">
					<a href="#" class="btn link-mask__link"> {* TODO link / modal? *}
						<span class="btn__text">
							{_"positions_offer_btn"}
						</span>
					</a>
				</p>
			</div>
		</div>
	</div>
</div>
