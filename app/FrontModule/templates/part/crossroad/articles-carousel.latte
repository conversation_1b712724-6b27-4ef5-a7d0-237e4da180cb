{default $class = 'u-mb-md u-mb-2xl@md'}
{default $title = false}
{default $gradient = false}
{default $carousel = true}
{default $btn = false}

<div n:class="c-articles-carousel, $class">
	<h2 n:if="$title" class="c-articles-carousel__title">{$title}</h2>
	<img n:if="$gradient" class="c-articles-carousel__bg" src="/static/img/illust/articles-bg.webp" alt="" loading="lazy">
	<div n:tag-if="$carousel" class="c-articles-carousel__carousel b-sections__carousel embla" data-controller="embla">
		<div n:tag-if="$carousel" class="embla__viewport" data-embla-target="viewport">
			<div n:class="c-articles-carousel__grid, grid, 'grid--x-xs grid--x-sm@md grid--y-xs grid--y-sm@md', $carousel ? 'grid--scroll embla__container'">
				{for $i = 0; $i < 10; $i++}
					<div class="c-articles-carousel__cell grid__cell">
						{include $templates.'/part/box/article.latte', titleTag: 'h3'}
					</div>
				{/for}
			</div>
			<button class="embla__btn embla__btn--prev" disabled="disabled" type="button" data-action="embla#prev" data-embla-target="prevButton">
				{('arrow-left')|icon}
				<span class="u-vhide">{_btn_prev}</span>
			</button>
			<button class="embla__btn embla__btn--next" disabled="disabled" type="button" data-action="embla#next" data-embla-target="nextButton">
				{('arrow-right')|icon}
				<span class="u-vhide">{_btn_next}</span>
			</button>
		</div>
		{* <div class="embla__progress"><div class="embla__progress-inner" data-embla-target="progress"></div></div> *}
	</div>
	<p n:if="$btn" class="c-articles-carousel__btn">
		{php $type = $btn->toggle}
		{php $page = isset($btn->systemHref) && isset($btn->systemHref->page) ? $btn->systemHref->page->getEntity() ?? false : false}
		{php $href = $btn->customHref??->href ?? false}
		{php $hrefNameSystem = $btn->systemHref??->hrefName ?? false}
		{php $hrefNameCustom = $btn->customHref??->hrefName ?? false}

		{if $type == 'systemHref' && $page}
			<a href="{plink $page}" n:ifcontent class="btn btn--bd btn--lg">
				<span class="btn__text">
					{if $hrefNameSystem}
						{$hrefNameSystem}
					{else}
						{$page->nameAnchor}
					{/if}
				</span>
			</a>
		{elseif $type == 'customHref' && $href && $hrefNameCustom}
			<a href="{$href}" class="btn btn--bd btn--lg" target="_blank" rel="noopener noreferrer">
				<span class="btn__text">
					{$hrefNameCustom}
				</span>
			</a>
		{/if}
	</p>
</div>