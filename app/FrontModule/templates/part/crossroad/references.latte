{default $class = 'u-mb-md u-mb-2xl@md'}
{default $references = $object->cf->references ?? []}
{default $title = $translator->translate('title_references')}
{default $titleClass = false}

<div n:class="c-references, $class">
	<h2 n:if="$title" n:class="c-references__title, $titleClass">
		{$title}
	</h2>
	<div n:if="count($references)" class="c-references__carousel embla" data-controller="embla">
		<div class="embla__viewport" data-embla-target="viewport">
			<div class="c-references__grid grid grid--scroll grid--y-0 embla__container">
				<div n:foreach="$references as $reference" class="c-references__cell grid__cell">
					{include $templates.'/part/box/reference.latte',
						imgPerson: isset($reference->imgPerson) ? $reference->imgPerson->getEntity() ?? false : false,
						imgCompany: isset($reference->imgCompany) ? $reference->imgCompany->getEntity() ?? false : false,
						text: $reference->text ?? false,
						name: $reference->name ?? false,
						info: $reference->info ?? false
					}
				</div>
			</div>
			<button class="embla__btn embla__btn--prev" disabled="disabled" type="button" data-action="embla#prev" data-embla-target="prevButton">
				{('arrow-left')|icon}
				<span class="u-vhide">{_btn_prev}</span>
			</button>
			<button class="embla__btn embla__btn--next" disabled="disabled" type="button" data-action="embla#next" data-embla-target="nextButton">
				{('arrow-right')|icon}
				<span class="u-vhide">{_btn_next}</span>
			</button>
		</div>
		{* <div class="embla__progress"><div class="embla__progress-inner" data-embla-target="progress"></div></div> *}
	</div>
</div>