{default $class = false}
{default $title = false}
{default $items = ['A', 'B', 'C', 'D', 'E', 'F', 'G', '1-9']} {* TODO *}
{default $faqLink = false}

<div n:class="$class">
	<h2 n:if="$title" class="tw-mb-[1.2rem] md:tw-mb-[2.4rem]">{$title}</h2>
	<ul n:class="u-reset-ul, 'tw-flex tw-flex-wrap tw-gap-[0.4rem] md:tw-gap-[0.8rem]'">
		<li n:foreach="$items as $item" class="u-fw-b tw-text-[1.4rem]">
			{php $isDisabled = $iterator->counter == 3} {* TODO *}

			{if $isDisabled}
				<span class="tw-flex tw-items-center tw-rounded-md tw-px-[1.7rem] md:tw-px-[2.1rem] tw-h-[4.1rem] md:tw-h-[4.5rem] tw-border-tile tw-border-solid tw-border-[0.1rem] u-c-gray tw-text-placeholder">
					{$item}
				</span>
			{else}
				<a href="#" class="tw-flex tw-items-center tw-rounded-md tw-px-[1.7rem] md:tw-px-[2.1rem] tw-h-[4.1rem] md:tw-h-[4.5rem] tw-no-underline tw-bg-bg tw-border-transparent tw-border-solid tw-border-[0.1rem]">
					{$item}
				</a>
			{/if}
		</li>
		<li n:if="$faqLink && isset($pages->faq)" class="u-fw-b tw-text-[1.4rem]">
			<a href="{plink $pages->faq}" class="tw-flex tw-items-center tw-rounded-md tw-px-[1.7rem] md:tw-px-[2.1rem] tw-h-[4.1rem] md:tw-h-[4.5rem] tw-no-underline tw-bg-bg tw-border-transparent tw-border-solid tw-border-[0.1rem]">
				Často kladené otázky
			</a>
		</li>
	</ul>
</div>