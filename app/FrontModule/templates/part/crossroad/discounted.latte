{default $class = false}

<div n:class="c-discounted, $class">
	<div class="c-discounted__head u-maw-8-12 u-mx-auto u-mb-last-0">
		<h2 class="c-discounted__title u-c-red u-ta-c">
			{_"prebasket_offer_title"}
		</h2>
		<p class="message message--error">
			<span class="message__emoji">👌</span>
			<span class="message__content">
				{capture $coursesLink}{plink $pages->courses}{/capture}
				{_"prebasket_offer_message"|noescape|replace:'%link', $coursesLink->__toString()}
			</span>
		</p>
	</div>
	<div class="c-discounted__carousel embla" data-controller="embla">
		<div class="embla__viewport" data-embla-target="viewport">
			<div class="c-discounted__grid grid grid--scroll grid--x-0 grid--y-0 embla__container">
				{for $i = 0; $i < 5; $i++}
					<div class="c-discounted__cell grid__cell">
						{include $templates.'/part/box/discounted.latte', class: false, active: $i == 1}
					</div>
				{/for}
			</div>
		</div>
		<button class="embla__btn embla__btn--prev" disabled="disabled" type="button" data-action="embla#prev" data-embla-target="prevButton">
			{('arrow-left')|icon}
			<span class="u-vhide">{_btn_prev}</span>
		</button>
		<button class="embla__btn embla__btn--next" disabled="disabled" type="button" data-action="embla#next" data-embla-target="nextButton">
			{('arrow-right')|icon}
			<span class="u-vhide">{_btn_next}</span>
		</button>
	</div>
</div>