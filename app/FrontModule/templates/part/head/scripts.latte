<script>
	(function () {
		var className = document.documentElement.className;
		className = className.replace('no-js', 'js');

		(function() {
			var mediaHover = window.matchMedia('(hover: none), (pointer: coarse), (pointer: none)');
			mediaHover.addListener(function(media) {
				document.documentElement.classList[media.matches ? 'add' : 'remove']('no-hoverevents');
				document.documentElement.classList[!media.matches ? 'add' : 'remove']('hoverevents');
			});
			className += (mediaHover.matches ? ' no-hoverevents' : ' hoverevents');
		})();

		// fix iOS zoom issue: https://docs.google.com/document/d/1KclJmXyuuErcvit-kwCC6K2J7dClRef43oyGVCqWxFE/edit#heading=h.sgbqg5nzhvu9
		var ua = navigator.userAgent.toLowerCase();
		var isIOS = /ipad|iphone|ipod/.test(ua) && !window.MSStream;

		if (isIOS === true) {
			var viewportTag = document.querySelector("meta[name=viewport]");
			viewportTag.setAttribute("content", "width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no");
		}

		document.documentElement.className = className;

		// Scrollbar width
		var calcScrollbar = function() {
			var scrollBarWidth = window.innerWidth - document.documentElement.clientWidth;;
			document.documentElement.style.setProperty('--scrollbar-width', scrollBarWidth+'px');
		};
		document.documentElement.style.overflowY = 'scroll';
		calcScrollbar();
		document.documentElement.style.overflowY = null;
	}());

	{* Cookie lišta *}
	{if $mutation->isEnabledCookieModal}
		(function () {
			var match = document.cookie.match(new RegExp('(^| )SKcookieConsent=([^;]+)'));
			var isSkipped = location.search.search('skipConsent=1') > 0;
			document.documentElement.dataset.showCookie = !isSkipped;
			if (match) {
				try {
					var cookieState = JSON.parse(match[2]);
					if(cookieState.id && cookieState.datetime && cookieState.storages) {
						document.documentElement.dataset.showCookie = false;
						window.cookieState = cookieState;
					};
				} catch (error) {}
			}
			// GTM consent init
			window.dataLayer = window.dataLayer || [];
			function gtag() {
				window.dataLayer.push(arguments);
			}
			gtag('consent', 'default', window.cookieState && window.cookieState.storages && !isSkipped ? window.cookieState.storages : {
				functionality_storage: 'granted',
				security_storage: 'granted',
				ad_storage: !isSkipped ? 'denied' : 'granted',
				analytics_storage: !isSkipped ? 'denied' : 'granted',
				personalization_storage: !isSkipped ? 'denied' : 'granted',
				ad_user_data: !isSkipped ? 'denied' : 'granted',
				ad_personalisation: !isSkipped ? 'denied' : 'granted',
			});
		}());
	{/if}
</script>
