{* {dump $object->template} *}
{* {dump $object->uid} *}

<link rel="stylesheet" href="/static/css/common.css?v={$webVersion}">

{if isset($object->uid) && $object->uid == 'title' || in_array($object->template, [':Front:Page:academy', ':Blog:Front:Blog:default'])}
	{* Landing *}
	<link rel="stylesheet" href="/static/css/landing.css?v={$webVersion}">
{elseif in_array($object->template, [':Front:Catalog:default', ':Front:Product:detail'])}
	{* Eshop / Catalog *}
	<link rel="stylesheet" href="/static/css/eshop.css?v={$webVersion}">
{elseif str_contains($object->template, ':Front:User:')}
	{* User *}
	<link rel="stylesheet" href="/static/css/user.css?v={$webVersion}">
{elseif in_array($object->template, [':Front:Precart:default', ':Front:Order:default', ':Front:Order:step1', ':Front:Order:step2', ':Front:Order:step3', ':Front:Page:rentalStep1', ':Front:Page:rentalStep2', ':Front:Page:rentalStep3'])}
	{* Order *}
	<link rel="stylesheet" href="/static/css/order.css?v={$webVersion}">
	<link rel="stylesheet" href="/static/css/delivery-picker.css?v={$webVersion}">
{else}
	{* Other *}
	<link rel="stylesheet" href="/static/css/other.css?v={$webVersion}">
{/if}
<link rel="stylesheet" href="/static/css/layout-utilities.css?v={$webVersion}">


{* <link rel="stylesheet" href="/static/css/style.css?v={$webVersion}"> *}

<link rel="stylesheet" href="/static/css/tailwind.css?v={$webVersion}">

<link rel="stylesheet" href="/static/css/print.css?v={$webVersion}" media="print">
{if isset($user) && $user->isDeveloper()}
	<link rel="stylesheet" href="/static/css/admin.css?v={$webVersion}">
{/if}
