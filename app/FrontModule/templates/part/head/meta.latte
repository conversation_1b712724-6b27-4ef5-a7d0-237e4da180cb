{control openGraph}

{* favicons *}
<link rel="icon" type="image/png" href="{$mutation->getBaseUrl()}/static/img/favicon/favicon-32x32.png" sizes="32x32" />
<link rel="icon" type="image/svg+xml" href="{$mutation->getBaseUrl()}/static/img/favicon/favicon.svg" />
<link rel="shortcut icon" href="{$mutation->getBaseUrl()}/static/img/favicon/favicon.ico" />
<link rel="apple-touch-icon" sizes="180x180" href="{$mutation->getBaseUrl()}/static/img/favicon/apple-touch-icon.png" />
<meta name="apple-mobile-web-app-title" content="{_'title'}" />
<link rel="manifest" href="{$mutation->getBaseUrl()}/static/img/favicon/site.webmanifest" />
