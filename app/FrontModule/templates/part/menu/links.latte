{default $class = 'm-links--default'}
{default $links = $object->cf->links ?? []}
{default $gridCellClass = 'size--6-12 size--4-12@lg size--3-12@xl'}
{if str_contains($class, 'm-links--hp')}
	{php $gridCellClass = 'size--6-12 size--4-12@sm size--12-12@lg'}
{/if}

<nav n:if="count($links)" n:class="m-links, $class">
	<ul class="m-links__list grid">
		{foreach $links as $link}
			{php $link = $link->item ?? $link}
			<li n:class="grid__cell, $gridCellClass">
				{php $type = $link->link->toggle}
				{php $page = isset($link->link->systemHref) && isset($link->link->systemHref->page) ? $link->link->systemHref->page->getEntity() ?? false : false}
				{php $hrefName = ($link->link->systemHref??->hrefName ?? false) ?: ($link->link->customHref??->hrefName ?? false)}
				{php $href = $link->link->customHref??->href ?? false}
				{php $icon = isset($link->icon) ? $link->icon->getEntity() ?? false : false}

				{if $type == 'systemHref' && $page}
					<a href="{plink $page}" n:ifcontent class="m-links__link box">
						<span class="m-links__icon">
							<img n:if="$icon" src="{$icon->getSize('xs')->src}" alt="" width="25" height="25">
						</span>
						{if $hrefName}
							{$hrefName}
						{else}
							{$page->nameAnchor}
						{/if}
					</a>
				{elseif $type == 'customHref' && $href && $hrefName}
					<a href="{$href}" class="m-links__link box">
						<span class="m-links__icon">
							<img n:if="$icon" src="{$icon->getSize('xs')->src}" alt="" width="25" height="25">
						</span>
						{$hrefName}
					</a>
				{/if}
			</li>
		{/foreach}
	</ul>
</nav>