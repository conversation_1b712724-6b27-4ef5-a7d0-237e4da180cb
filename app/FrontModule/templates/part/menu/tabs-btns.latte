{default $items = []}

<div class="m-tabs tw-mb-[2rem] md:tw-mb-[3.2rem] is-limited" data-controller="limit-tabs">
	<div class="m-tabs__tabs tw-flex tw-flex-wrap tw-gap-[0.4rem] md:tw-gap-[0.8rem] tw-overflow-hidden">
		<ul class="u-reset-ul tw-contents tw-gap-[0.4rem] md:tw-gap-[0.8rem]" data-limit-tabs-target="list">
			<li class="tw-flex-[0_0_auto]">
				<a href="#" class="m-tabs__btn btn btn--bd"><span class="btn__text">{_"showcase_tab_all"}</span></a>
			</li>
			<li n:foreach="$items as $item" class="tw-flex-[0_0_auto]">
				<a href="#" class="m-tabs__btn btn btn--gray"><span class="btn__text">{$item}</span></a>
			</li>
		</ul>
		<p class="u-mb-0 tw-flex-[0_0_auto]">
			<button type="button" class="m-tabs__btn m-tabs__btn--more btn btn--gray tw-invisible" data-limit-tabs-target="showAllBtn" data-action="limit-tabs#toggleTabs">
				<span class="btn__text">
					<span class="btn__inner">
						{_"btn_all_tags"}
						{('angle-down'|icon, 'btn__icon')}
					</span>
					<span class="btn__inner">
						{_"btn_less_tags"}
						{('angle-down'|icon, 'btn__icon')}
					</span>
				</span>
			</button>
		</p>
	</div>
	<form n:if="count($items) > 10" action="?" class="m-tabs__search f-search">
		<p class="f-search__wrap u-mb-0">
			<span class="f-search__inp-fix inp-fix">
				<label for="search" class="u-vhide">{_"form_label_search_tabs"}</label>
				<input type="search" id="search" name="search" class="f-search__inp inp-text" placeholder="{_'form_label_search_tabs'}">
			</span>
			<button type="submit" class="f-search__btn btn btn--icon">
				<span class="btn__text">
					{('search')|icon, 'btn__icon'}
					<span class="u-vhide">{_"btn_search"}</span>
				</span>
			</button>
		</p>
	</form>
</div>