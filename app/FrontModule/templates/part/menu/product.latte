{varType App\Model\Orm\Product\Product $product}
{default $class = false}
{default $sections = []}
{default $condition = true}

<nav n:if="count($sections) && $condition" n:class="m-product, $class" data-sections-target="menu">
	<ul class="m-product__list">
		<li n:foreach="$sections as $section" class="m-product__item">
			{continueIf $section === null}
			<a href="#{$section['id']}" n:class="m-product__link, $iterator->isFirst() ? is-active" data-sections-target="item">
				{_($section['titleShort'] ?? $section['title'])}

				{php $flag = $section['flag'] ?? false}
				<span n:if="$flag" n:class="flag, $flag['class'] ?? false">
					{$flag['text']}
					{if $flag['icon'] ?? false}{($flag['icon'])|icon, 'flag__icon'}{/if}
				</span>
			</a>
		</li>
	</ul>
</nav>
