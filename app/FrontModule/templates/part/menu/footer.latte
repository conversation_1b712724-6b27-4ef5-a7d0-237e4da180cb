{ifset $props}
	{foreach $props['data'] as $item}
		<div n:if="isset($item['list']) && $item['list']" class="footer__cell footer__cell--menu grid__cell">
			<nav class="footer__nav m-footer">
				<h2 n:if="isset($item['title']) && $item['title']" class="footer__title m-footer__title h5">
					{$item['title']}
				</h2>
				<ul class="m-footer__list">
					{foreach $item['list'] as $item}
						<li n:ifcontent class="m-footer__item">
							{php $type = $item->toggle}
							{php $page = isset($item->systemHref) && isset($item->systemHref->page) ? $item->systemHref->page->getEntity() ?? false : false}
							{php $href = $item->customHref??->href ?? false}
							{php $hrefNameSystem = $item->systemHref??->hrefName ?? false}
							{php $hrefNameCustom = $item->customHref??->hrefName ?? false}

							{if $type == 'systemHref' && $page}
								<a href="{plink $page}" n:ifcontent class="m-footer__link">
									{if $hrefNameSystem}
										{$hrefNameSystem}
									{else}
										{$page->nameAnchor}
									{/if}
								</a>
							{elseif $type == 'customHref' && $href && $hrefNameCustom}
								<a href="{$href}" class="m-footer__link" target="_blank" rel="noopener noreferrer">
									{$hrefNameCustom}
								</a>
							{/if}
						</li>
					{/foreach}
				</ul>
			</nav>
		</div>
	{/foreach}
{/ifset}
