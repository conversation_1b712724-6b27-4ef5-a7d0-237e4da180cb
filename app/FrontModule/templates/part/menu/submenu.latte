{default $class = false}
{default $categories = []}
{default $limitCategory = 12}
{default $limitSubcategory = 6}
{default $page = false}
{default $uid = $page->uid ?? false}
{default $type = 'menu'} {* menu | standalone *}

<div n:if="count($categories)" n:class="m-submenu, $class, $uid ? 'm-submenu--' . $uid, $type ? 'm-submenu--' . $type" data-controller="toggle-class">
	<ul class="m-submenu__list m-submenu__list--1">
		<li n:foreach="$categories as $category" class="m-submenu__item">
			{if $iterator->counter == $limitCategory && count($categories) >= $limitCategory}
				{* Zobrazit všechny kategorie *}
				<div class="m-submenu__box link-mask">
					<a href="{plink $page}" class="m-submenu__all item-icon link-mask__link">
						<span class="item-icon__text">
							{_"btn_submenu_more_categories"}
						</span>
						{('angle-right')|icon, 'item-icon__icon'}
					</a>
				</div>
				{breakIf true}
			{else}
				{php $desc = $category->desc ?? false}
				{php $link = $category->link ?? false}
				{php $subitems = $category->items ?? []}
				{php $page = isset($link->systemHref) && isset($link->systemHref->page) ? $link->systemHref->page->getEntity() ?? false : false}
				{php $img = isset($page->cf->base->crossroadImage) ? $page->cf->base->crossroadImage->getEntity() ?? false : false}

				{php $hasImgOrDescription = $desc || count($subitems)}

				<div n:class="m-submenu__box, !$hasImgOrDescription ? m-submenu__box--sm, $category->bg ?? false, u-mb-last-0, $iterator->counter >= $limitCategory ? is-desktop-hidden">
					{define #link}
						{default $class = false}
						{default $arrow = false}

						{php $type = $link->toggle}
						{php $page = $link->systemHref??->page ?? false} {* todo *}
						{php $href = $link->customHref??->href ?? false}
						{php $hrefNameSystem = $link->systemHref??->hrefName ?? false}
						{php $hrefNameCustom = $link->customHref??->hrefName ?? false}

						{if $type == 'systemHref' && $page}
							<a href="{plink $page}" n:ifcontent n:class="m-submenu__link, $class">
								{if $page->uid == 'blog'}

									<img src="/static/img/illust/dron-zone.svg" alt="" loading="lazy" width="141" height="28">
								{elseif $hrefNameSystem}
									{$hrefNameSystem}
								{else}
									{$page->nameAnchor}
								{/if}
								{if $arrow}{('angle-right')|icon, 'm-submenu__arrow'}{/if}
							</a>
						{elseif $type == 'customHref' && $href && $hrefNameCustom}
							<a href="{$href}" n:class="m-submenu__link, $class" target="_blank" rel="noopener noreferrer">
								{$hrefNameCustom}
								{if $arrow}{('angle-right')|icon, 'm-submenu__arrow'}{/if}
							</a>
						{/if}
					{/define}

					{* Obrázek *}
					<img n:if="$img" class="m-submenu__decor" src="{$img->getSize('md')->src}" alt="" loading="lazy">

					{* Nadpis *}
					<p n:if="$link" class="m-submenu__title h5">
						{include #link, link: $link, arrow: true, class: 'link-mask__link'}
						<button n:if="count($subitems)" type="button" class="m-submenu__toggle as-link" data-action="header#toggleSubmenu2">
							{('arrow-right')|icon}
							<span class="u-vhide">{_"show_more"}</span>
						</button>
					</p>

					{* Popis *}
					<p n:if="$desc" class="m-submenu__desc u-c-help">{$desc|texy|noescape}</p>

					{* Podmenu *}
					<div n:if="count($subitems)" class="m-submenu__holder">
						<div class="b-submenu__head">
							<p class="b-submenu__back">
								<button type="button" class="b-submenu__btn btn" data-action="header#toggleSubmenu2">
									<span class="btn__text">
										<span class="btn__inner">
											{('arrow-left')|icon, 'btn__icon'}
											{_"btn_back"}
										</span>
									</span>
								</button>
							</p>
							<p class="b-submenu__title">
								<span class="h3 u-mb-0">
									{php $type = $link->toggle}
									{php $page = isset($link->systemHref) && isset($link->systemHref->page) ? $link->systemHref->page->getEntity() ?? false : false}
									{php $hrefNameSystem = $link->systemHref??->hrefName ?? false}
									{php $hrefNameCustom = $link->customHref??->hrefName ?? false}

									{if $type == 'systemHref' && $page}
										{if $hrefNameSystem}
											{$hrefNameSystem}
										{else}
											{$page->nameAnchor}
										{/if}
									{elseif $type == 'customHref' && $href && $hrefNameCustom}
										{$hrefNameCustom}
									{/if}
								</span>
								<a href="#">{_"btn_submenu_view_all"}</a>
							</p>
						</div>

						<ul n:if="count($subitems)" class="m-submenu__list--2">
							<li n:foreach="$subitems as $subitem" n:class="m-submenu__item, $iterator->counter > $limitSubcategory ? 'is-hidden'">
								{include #link, link: $subitem->link, class: 'link-mask__unmask'}
							</li>
						</ul>
					</div>
					<p n:if="count($subitems) > $limitSubcategory" class="m-submenu__more-wrap">
						<button type="button" class="m-submenu__more as-link item-icon link-mask__unmask" data-action="toggle-class#toggle" data-toggle-class="is-open">
							<span class="item-icon__text">
								{_"btn_more_submenu"}
							</span>
							{('angle-down')|icon, 'item-icon__icon'}
						</button>
					</p>
				</div>
			{/if}
		</li>
	</ul>
</div>
