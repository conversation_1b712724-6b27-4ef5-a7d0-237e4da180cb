{default $class = false}

<nav n:if="$menu" n:class="m-user, $class">
	<ul class="m-user__list">
		{foreach $menu as $item}
			{php $uid = $item->page->uid ?? false}
			{if $uid}
				{continueIf $uid === 'myLibrary' && !$userEntity?->isClubMember}
				<li n:class="m-user__item, $uid == 'userLogout' ? m-user__item--logout">
					<a href="{plink $item->page}" n:class="m-user__link, item-icon, $item->selected ? is-active">
						<span class="m-user__icon-holder item-icon__icon">
							{switch $uid}
								{case 'userSection'} {('house')|icon}
								{case 'userAddress'} {('pin-outline')|icon}
								{case 'userChangePassword'} {('password')|icon}
								{case 'userOrderHistory'} {('box')|icon}
								{case 'userCourses'} {('school')|icon}
								{case 'userAssociation'} {('association')|icon}
								{case 'userServices'} {('dashboard')|icon}
								{case 'userClaims'} {('claims')|icon}
								{case 'userService'} {('service')|icon}
								{case 'userRental'} {('dron-hand')|icon}
								{case 'loyaltyProgram'} {('discount')|icon}
								{case 'buyout'} {('dron-arrows')|icon}
								{case 'userInterest'} {('eye')|icon}
								{case 'userLogout'} {('logout')|icon}
								{case 'userAnimal'} {('heart')|icon}
								{case 'myReviews'} {('star')|icon}
								{default}
							{/switch}
							{* TODO BE podmínka *}
							{* <span n:if="$uid != 'userLogout'" class="m-user__notification notification"></span> *}
						</span>
						<span class="item-icon__text">
							{$item->page->nameAnchor}
						</span>
					</a>
				</li>
			{else}
				<li class="m-user__item">
					<a href="{plink $item->page}" n:class="m-user__link, $item->selected ? is-active">
						{$item->page->nameAnchor}
					</a>
				</li>
			{/if}
		{/foreach}
	</ul>
</nav>
