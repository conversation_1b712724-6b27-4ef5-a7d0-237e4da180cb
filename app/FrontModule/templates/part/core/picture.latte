{default $class = false}
{default $img = false}
{default $type = false}
{default $alt = ''}
{default $lazyLoading = true}
{default $fetchPriority = false}

<picture n:if="$img && $img->getEntity() !== null" n:class="$class">
	{php $imgLg = $img->getSize(implode("_", array_filter([$type, 'desktop'])))}
	{php $imgMd = $img->getSize(implode("_", array_filter([$type, 'tablet'])))}
	{php $imgSm = $img->getSize(implode("_", array_filter([$type, 'mobile'])))}
	<source media="(min-width: 1000px)" srcset="{$imgLg->src}" width="{$imgLg->width}" height="{$imgLg->height}">
	<source media="(min-width: 750px)" srcset="{$imgMd->src}" width="{$imgMd->width}" height="{$imgMd->height}">
	<img src="{$imgSm->src}" alt="{$alt}"{if $lazyLoading} loading="lazy"{/if}{if $fetchPriority} fetchpriority="{$fetchPriority}"{/if} width="{$imgSm->width}" height="{$imgSm->height}">
</picture>
