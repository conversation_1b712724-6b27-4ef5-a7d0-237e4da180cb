{default $class = false}
{default $icon = false}
{default $arrow = false}

<p n:class="toggle, $class, u-mb-0, 'u-d-n@lg'">
	<button type="button" class="toggle__btn as-link" data-action="toggle-class#toggle" aria-expanded="false">
		<span n:tag-if="$icon" class="item-icon">
			{if $icon}{($icon)|icon, 'item-icon__icon'}{/if}
			<span n:tag-if="$icon" class="item-icon__text">
				{block text}{/block}
			</span>
		</span>

		{if $arrow}
			{('angle-down')|icon, 'toggle__arrow'}
		{else}
			<span class="b-burger">
				<span class="b-burger__inner">
					<span></span>
					<span></span>
					<span></span>
					<span></span>
				</span>
				<span class="u-vhide">{_"menu"}</span>
			</span>
		{/if}
	</button>
</p>