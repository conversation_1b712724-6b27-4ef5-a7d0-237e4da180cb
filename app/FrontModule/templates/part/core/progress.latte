{default $class = false}
{default $percentage = 0}
{default $count = false}
{default $points = []}

<span n:class="progress, $class">
	<span class="progress__bar">
		<span class="progress__bar progress__bar--active" style="width: {$percentage|noescape}%"></span>
		<span n:foreach="$points as $point" n:class="progress__point, $percentage >= $point['percentage'] ? is-active" style="left: {$point['percentage']|noescape}%">
			<span n:class="progress__bubble"> {* $point['percentage'] < 20 ? is-flipped *}
				{php $icon = $point['icon'] ?? false}
				{if $icon}
					<span class="item-icon">
						<span class="item-icon__text">
							{$point['text']|noescape}
						</span>
						{($icon)|icon, 'item-icon__icon'}
					</span>
				{else}
					{$point['text']|noescape}
				{/if}
			</span>
		</span>
	</span>
	<span n:if="$count" class="progress__count">
		{$count}&times;
	</span>
</span>