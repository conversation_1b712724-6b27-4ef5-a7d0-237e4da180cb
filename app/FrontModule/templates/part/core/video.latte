{default $class = false}
{default $link = false}
{default $ratio = '16-9'}
{default $poster = false}
{default $autoplay = false}
{default $loop = false}
{default $controls = true}

<div n:if="$link" n:class="video, $class">
	{if strpos($link, 'youtube')}
		{php $urlObject =  new \Nette\Http\Url($link)}
		{php $id = $urlObject->getQueryParameter('v')}
		<lite-youtube videoid="{$id}"{if $poster} style="background-image: url('{$poster}');"{/if}></lite-youtube>
	{elseif strpos($link, 'vimeo')}
		{php $id = str_replace('/', '', str_replace('https://vimeo.com/', '', $link))}
		<lite-vimeo videoid="{$id}"{if $poster} style="background-image: url('{$poster}');"{/if}></lite-vimeo>
	{/if}
</div>