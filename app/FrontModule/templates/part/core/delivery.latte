{varType App\Model\Orm\ProductLocalization\ProductLocalization $object}
{varType App\Model\Orm\Product\Product $product}
{varType App\Model\Orm\ProductVariant\ProductVariant $variant}

{default $class = false}
{default $isDetail = false}
{default $showStoreText = true}

{*
{dump $object->isInStock} // bool skladem celkove
{dump $object->isInStockDefault} // bool je skladem na centralnim skladu
{dump $object->isInStockSupplier} // bool je skladem u dodavatele
{dump $object->suppliesByStock} // sklady podle ID
{dump $object->suppliesByStockAlias} // sklady podle aliasu
{dump $object->totalSupplyCount} // abs pocet na sklade celkem
{dump $object->suplyCountStockDefault} // abs pocet na defaultnim sklade
{dump $object->suplyCountStockSupplier} // abs pocet u dodavatele
*}

{*
{productAvailability->getType() === App\Model\Orm\ProductVariant\Availability\CustomProductAvailability::TYPE_PREORDER}

TYPE_ON_STOCK = 'onstock'; // Skladem na prodejne
TYPE_ON_STOCK_SUPPLIER = 'onstock_supplier'; // Skladem do x dnu;
TYPE_TO_ORDER = 'to_order'; // Na objednavku
TYPE_PREORDER = 'preorder'; // Predobjednávky
TYPE_OUT_OF_STOCK = 'out_of_stock'; // Dočasne vyprodano
TYPE_NOT_FOR_SALE = 'not_for_sale'; // Trvale vyprodáno
*}

{php $type = $productDto->productAvailabilityType}
{php $status = 'unavailable'}
{php $icon = 'cross'}
{if $type == 'preorder'}
	{php $status = 'prepare'}
	{php $icon = 'check'}
{elseif in_array($type, ['onstock', 'onstock_supplier'])}
	{php $status = 'available'}
	{php $icon = 'check'}
{/if}

{var $selectedCurrency = App\Model\Currency\CurrencyHelper::getCurrency()}
{php $deliveryText = $variant->productAvailability->getDeliveryText($mutation, $state, $priceLevel, $selectedCurrency) ?? false}
{php $storeText = $variant->productAvailability->getStoreText($state) ?? false}

{php $deliveryText = $productDto->productAvailabilityDeliveryText}
{php $storeText = $productDto->productAvailabilityStoreText}

<p n:if="!$productDto->isElectronic && ($deliveryText || $storeText)" n:ifcontent n:class="availability, availability--delivery, 'availability--' . $status, $class, item-icon">
	{($icon)|icon, 'item-icon__icon'}
	<span class="item-icon__text">
		{if $deliveryText !== false}
		{translate($deliveryText)}
		{/if}
	{if $storeText && $showStoreText}
			{translate($storeText)|noescape}
		{/if}
	</span>
</p>

{* <span>showCart: <strong>{$variant->productAvailability->isShowAddCart() ? 'yes' : 'no'}</strong></span><br>
<span>showWatchdog: <strong>{$variant->productAvailability->isShowWatchdog() ? 'yes' : 'no'}</strong></span><br>
<span>showSimilar: <strong>{$variant->productAvailability->isShowSimilar() ? 'yes' : 'no'}</strong></span><br> *}
