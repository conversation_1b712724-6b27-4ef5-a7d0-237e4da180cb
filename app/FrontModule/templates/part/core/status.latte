{default $order = false}

{if $order}

	{* case Draft = 'draft';
	case Placed = 'placed';
	case Canceled = 'canceled';
	case Declined = 'declined';
	case Prepared = 'prepared';
	case Dispatched = 'dispatched';
	case Imported = 'imported'; *}

	{* <span class="flag flag--sm flag--red-light">Čeká na platbu</span>
	<span class="flag flag--sm flag--orange-light">Zpracováváme</span>
	<span class="flag flag--sm flag--orange-light">Na cestě</span>
	<span class="flag flag--sm flag--red-light">Čeká na vyzvednutí</span>
	<span class="flag flag--sm flag--green-light">Dodané</span>
	<span class="flag flag--sm flag--gray">Vrácené</span>
	<span class="flag flag--sm flag--purple-light">Reklamace</span> *}

	{php $status = $order->state->value}

	{if ($paymentState = $order->payment?->information?->getState()) !== null}
		<span class="flag flag--sm flag--red-light">{_'order_status_payment_' . $paymentState->value|lower}</span>
	{else}
		{php $color = false}
		{if in_array($status, ['draft', 'placed', 'prepared', 'dispatched'])}
			{php $color == 'orange-light'}
		{elseif in_array($status, ['canceled', 'declined', 'placed'])}
			{php $color == 'red-light'}
		{else}
			{php $color == 'green-light'}
		{/if}

		<span n:class="flag, flag--sm, $color ? 'flag--' . $color">{_'order_status_' . $status}</span>
	{/if}
{/if}
