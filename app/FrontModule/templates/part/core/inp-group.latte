{default $class = false}
{default $name = false}
{default $count = 0}

<fieldset n:class="inp-group, $class, 'tw-group tw-text-[1.4rem] tw-relative'" data-controller="etarget toggle-class">
	<legend class="tw-w-full">
		<button type="button" class="as-link tw-w-full tw-rounded-md tw-bg-bg group-[.is-open]:tw-bg-primary-150 item-icon tw-p-[1rem_1.8rem] tw-gap-[0.6rem] tw-text-primary tw-no-underline group-[.is-open]:tw-rounded-bl-none group-[.is-open]:tw-rounded-br-none" data-action="toggle-class#toggle">
			<b class="item-icon__text tw-flex tw-gap-[0.6rem] tw-items-center">
				{$name}
				<span n:if="$count" class="tw-w-[1.8rem] tw-rounded-full tw-bg-primary tw-text-white tw-text-[0.9rem] tw-flex tw-aspect-square tw-justify-center tw-items-center tw-pt-[0.1rem]">{$count}</span>
			</b>
			{('angle-down')|icon, 'item-icon__icon tw-w-[1.5rem] group-[.is-open]:tw-rotate-180 max-sm:tw-ml-auto'}
		</button>
	</legend>
	<div class="tw-bg-primary-150 tw-rounded-md tw-rounded-tl-none tw-rounded-tr-none tw-hidden group-[.is-open]:tw-block tw-p-[1.6rem_2.4rem_2.4rem] tw-absolute tw-left-0 tw-top-[100%] tw-min-w-[100%] tw-z-[1]">
		{block content}{/block}
	</div>
</fieldset>