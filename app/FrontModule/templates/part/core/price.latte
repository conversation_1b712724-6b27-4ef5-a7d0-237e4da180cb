{varType App\Model\Orm\Product\Product $productEntity}
{varType App\Model\DTO\Product\ProductDto $productDto}
{var $selectedCurrency = App\Model\Currency\CurrencyHelper::getCurrency()}
{var $priceVat = $productDto->priceVat}
{var $price = $productDto->price}
{var $priceInfo = $productEntity->getPriceInfo($mutation, $priceLevel, $state)}

{php $discountAmount = $productDto->priceInfoDiscountAmount}
{php $discountPercentage = $productDto->priceInfoDiscountPercentage}
{php $originalPrice = $productDto->priceInfoOriginalPrice}
{var $tooltipText = $productDto->tooltipText}

{default $class = false}
{default $isDetail = false}

{* Sepecální barva boxu pro cenu *}
{default $specialType = $product->cf->priceBox->type ?? 'default'} {* bomb | dark *}
{default $specialTextLang = $specialType == 'bomb' ? 'special_price_bomb' : 'special_price_dark'}

{*
	Cena produktu se nove urcuje podle mutace a cenove hladiny
	u ceny s DPH je navic potreba predat aktualni stat (urcuje sazbu DPH)

	{dump $mutation} // object \App\Model\Mutation
	{dump $priceLevel} // object \App\Model\PriceLevel
	{dump $state} // object \App\Model\State

	{dump $price} // float cena bez DPH
	{dump $priceVat} // float cena s DPH
*}

{if $specialType != 'default'}
{* {if false} *}
	<p n:if="!$priceVat->isZero() && !$productEntity->isOld && !in_array($productDto->productAvailabilityType, ['out_of_stock'])" n:class="price-special, $priceVat->isZero() ? 'price--soldout', $class, 'price-special--' . $specialType, $isDetail ? price-special--detail">
		<span class="price-special__box">
			<span class="price-special__top">
				{_$specialTextLang}{if $isDetail}: {if ($oldPriceDiscountPercentage = $priceInfo->getOldPriceDiscountPercentage()) !== null}-{$oldPriceDiscountPercentage} %{/if}
					{if $tooltipText}
						{embed $templates.'/part/core/tooltip.latte', class: 'price-special__tooltip', btnClass=>'as-link', placement: 'right'}
							{block btn}
								{('info')|icon}
							{/block}
							{block content}
								{$tooltipText|noescape}
							{/block}
						{/embed}
					{/if}
				{/if}
			</span>
			<span class="price-special__main">
				<s n:if="$originalPrice" class="price__old">
					{$originalPrice|money}
				</s>
				<strong>
					{$priceVat|money}
				</strong>
			</span>
		</span>
		<span n:if="$isDetail" class="price-special__notax">{$price|money} {_"price_without_tax"}</span>
	</p>
{else}
	<p n:if="!$priceVat->isZero() && !$productEntity->isOld && !in_array($productDto->productAvailabilityType, ['out_of_stock'])" n:class="price, $priceVat->isZero() ? 'price--soldout', $class, $isDetail ? price--detail">
		<span n:ifcontent class="price__original">
			<s n:if="$originalPrice" class="price__old">
				{$originalPrice|money}
			</s>
			<span class="price__discount flag flag--rainbow" n:if="($oldPriceDiscountPercentage = $priceInfo->getOldPriceDiscountPercentage()) !== null">
				-{$oldPriceDiscountPercentage} %
			</span>
			{if $tooltipText}
				{embed $templates.'/part/core/tooltip.latte', class: 'price-special__tooltip link-mask__unmask', btnClass=>'as-link', placement: 'right'}
					{block btn}
						{('info')|icon}
					{/block}
					{block content}
						{$tooltipText|noescape}
					{/block}
				{/embed}
			{/if}
		</span>
		<span class="price__main u-d-b">
			<strong>{$priceVat|money}</strong>{if $isDetail} {_"price_tax"}{/if}
		</span>
		<span n:if="$isDetail" class="price__notax">{$price|money} {_"price_without_tax"}</span>
	</p>
{/if}
