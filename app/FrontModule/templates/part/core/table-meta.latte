{default $class = 'u-mb-sm u-mb-md@md'}
{default $settings = $object->cf->settings ?? false}

<table n:class="table-meta, $class">
	<tbody>
		<tr n:if="$settings->author ?? false">
			<th>{_"author"}</th>
			<td>
				{$settings->author}
			</td>
		</tr>
		<tr n:if="$settings->address ?? false">
			<th>{_"address"}</th>
			<td>
				{$settings->address}
			</td>
		</tr>
		<tr n:if="($settings->validity_from ?? false) || ($settings->validity_to ?? false)">
			<th>{_"validity"}</th>
			<td>
				{capture $monthNumber}{$settings->validity_from|date:"n"}{/capture}
				<time datetime='{$settings->validity_from|date:"Y-m-d"}'>{$settings->validity_from|date:"j."} {_"month_".$monthNumber->__toString()} {$settings->validity_from|date:"Y"}</time>

				{if ($settings->validity_from ?? false) && ($settings->validity_to ?? false)} - {/if}

				{capture $monthNumber}{$settings->validity_to|date:"n"}{/capture}
				<time datetime='{$settings->validity_to|date:"Y-m-d"}'>{$settings->validity_to|date:"j."} {_"month_".$monthNumber->__toString()} {$settings->validity_to|date:"Y"}</time>
			</td>
		</tr>
		<tr n:if="$settings->date ?? false">
			<th>{_"date"}</th>
			<td>
				{capture $monthNumber}{$settings->date|date:"n"}{/capture}
				<time datetime='{$settings->date|date:"Y-m-d"}'>{$settings->date|date:"j."} {_"month_".$monthNumber->__toString()} {$settings->date|date:"Y"}</time>
			</td>
		</tr>
		<tr n:if="$settings->contact_person ?? false">
			<th>{_"contact_person"}</th>
			<td>
				{$settings->contact_person}
			</td>
		</tr>
		<tr n:if="$settings->mail ?? false">
			<th>{_"email"}</th>
			<td>
				<a href="mailto:{$settings->mail}">{$settings->mail}</a>
			</td>
		</tr>
		<tr n:if="$settings->phone ?? false">
			<th>{_"form_label_phone"}</th>
			<td>
				<a href="tel:{$settings->phone|replace:' ',''}">{$settings->phone}</a>
			</td>
		</tr>
		<tr n:if="$settings->for ?? false">
			<th>{_"meta_for"}</th>
			<td>
				{$settings->for}
			</td>
		</tr>
		<tr n:if="$settings->lectors ?? false">
			<th>{_"lectors"}</th>
			<td>
				{$settings->lectors}
			</td>
		</tr>
		<tr n:if="$settings->length ?? false">
			<th>{_"length"}</th>
			<td>
				{$settings->length}
			</td>
		</tr>
		{define #dateRow}
			<tr n:ifset="$settings->$name">
				<th>{_$name}</th>
				<td>
					{capture $monthNumber}{$settings->$name|date:"n"}{/capture}
					<time datetime='{$settings->$name|date:"Y-m-d"}'>{$settings->$name|date:"j."} {_"month_".$monthNumber->__toString()}
					{$settings->$name|date:"Y"}{if ($settings->$name|date:"H:i") !== '00:00'}, {$settings->$name|date:"H:i"}{/if}
					</time>
				</td>
			</tr>
		{/define}
		{include #dateRow name: from}
		{include #dateRow name: to}
		<tr n:if="$settings->place ?? false">
			<th>{_"place"}</th>
			<td>
				{$settings->place}
			</td>
		</tr>
		<tr n:if="$settings->people_count ?? false">
			<th>{_"people_count"}</th>
			<td>
				{$settings->people_count}
			</td>
		</tr>
		<tr n:if="$settings->price ?? false">
			<th>{_"price"}</th>
			<td>
				{$settings->price}
			</td>
		</tr>
		<tr n:if="$settings->price_members ?? false">
			<th>{_"price_members"}</th>
			<td>
				{$settings->price_members}
			</td>
		</tr>
		<tr n:if="$settings->discount ?? false">
			<th>{_"discount"}</th>
			<td>
				{$settings->discount}
			</td>
		</tr>
		<tr n:if="$object->facultyLocalization->name ?? false">
			<th>{_"faculty"}</th>
			<td>
				{$object->facultyLocalization->name}
			</td>
		</tr>
		<tr n:if="$settings->organiser ?? false">
			<th>{_"organiser"}</th>
			<td>
				{$settings->organiser}
			</td>
		</tr>
		<tr>
			<th>{_"published"}</th>
			<td>
				{capture $monthNumber}{$object->publicFrom|date:"n"}{/capture}
				<time datetime='{$object->publicFrom|date:"Y-m-d"}'>{$object->publicFrom|date:"j."} {_"month_".$monthNumber->__toString()} {$object->publicFrom|date:"Y"}</time>
			</td>
		</tr>
		<tr n:if="$settings->map_link ?? false">
			<th>{_"map_link"}</th>
			<td>
				<a href="{$settings->map_link|externalLink}" target="_blank" rel="noopener noreferrer">{$settings->map_link}</a>
			</td>
		</tr>
		<tr n:if="$settings->link ?? false">
			<th>{_"external_link"}</th>
			<td>
				<a href="{$settings->link|externalLink}" target="_blank" rel="noopener noreferrer">{$settings->link}</a>
			</td>
		</tr>
		<tr n:if="$settings->external_link ?? false">
			<th>{_"external_link"}</th>
			<td>
				<a href="{$settings->external_link|externalLink}" target="_blank" rel="noopener noreferrer">{$settings->external_link}</a>
			</td>
		</tr>
		{* <tr>
			<th>{_"url"}</th>
			<td>
				<a href="{$mutation->getBaseUrl()}{plink $object}">{$mutation->getBaseUrl()}{plink $object}</a>
			</td>
		</tr> *}
	</tbody>
</table>
