{varType App\Model\Orm\ProductLocalization\ProductLocalization $object}
{varType App\Model\Orm\Product\Product $productEntity}
{varType App\Model\Orm\ProductVariant\ProductVariant $variant}
{varType App\Model\DTO\Product\ProductDto $productDto}

{default $class = false}
{default $isDetail = false}

{*
{dump $object->isInStock} // bool skladem celkove
{dump $object->isInStockDefault} // bool je skladem na centralnim skladu
{dump $object->isInStockSupplier} // bool je skladem u dodavatele
{dump $object->suppliesByStock} // sklady podle ID
{dump $object->suppliesByStockAlias} // sklady podle aliasu
{dump $object->totalSupplyCount} // abs pocet na sklade celkem
{dump $object->suplyCountStockDefault} // abs pocet na defaultnim sklade
{dump $object->suplyCountStockSupplier} // abs pocet u dodavatele
*}

{*
{$variant->productAvailabilityType === App\Model\Orm\ProductVariant\Availability\CustomProductAvailability::TYPE_PREORDER}
}
TYPE_ON_STOCK = 'onstock'; // Skladem na prodejne
TYPE_ON_STOCK_SUPPLIER = 'onstock_supplier'; // Skladem do x dnu;
TYPE_TO_ORDER = 'to_order'; // Na objednavku
TYPE_PREORDER = 'preorder'; // Predobjednávky
TYPE_OUT_OF_STOCK = 'out_of_stock'; // Dočasne vyprodano
TYPE_NOT_FOR_SALE = 'not_for_sale'; // Trvale vyprodáno
*}

{php $status = 'unavailable'}
{php $icon = 'cross'}

{php $type = $productDto->productAvailabilityType}
{if in_array($type, [
	App\Model\Orm\ProductVariant\Availability\CustomProductAvailability::TYPE_PREORDER,
	App\Model\Orm\ProductVariant\Availability\CustomProductAvailability::TYPE_WAITING,
])}
	{php $status = 'prepare'}
	{php $icon = 'check'}
{elseif in_array($type, [
	App\Model\Orm\ProductVariant\Availability\CustomProductAvailability::TYPE_ON_STOCK,
	App\Model\Orm\ProductVariant\Availability\CustomProductAvailability::TYPE_ON_STOCK_SUPPLIER,
	App\Model\Orm\ProductVariant\Availability\CustomProductAvailability::TYPE_TO_DOWNLOAD
])}
	{php $status = 'available'}
	{php $icon = 'check'}
{/if}

{php $detailText = $productDto->productAvailabilityText}
{php $shortText = $productDto->productAvailabilityShortText}
{php $infoText = $productDto->productAvailabilityInfoText}


<p n:if="$detailText || $shortText" n:class="availability, $class, 'availability--' . $status, u-fw-b, $isDetail ? item-icon">
	{if $isDetail}{($icon)|icon, 'item-icon__icon'}{/if}
	<span n:tag-if="$isDetail" class="item-icon__text">
		{if $isDetail}
			{translate($detailText)}
		{else}
			{translate($shortText)}
		{/if}
	</span>
</p>
<p n:if="str_contains($class, 'availability--detail')  && $infoText" n:class="availability, availability--delivery, $class"> {*&& $type == 'out_of_stock'*}
	{translate($infoText)}
</p>
