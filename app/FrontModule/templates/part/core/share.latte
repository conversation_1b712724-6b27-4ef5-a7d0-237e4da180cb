{default $class = 'u-mb-sm u-mb-md@md'}
{default $fbLang = 'btn_share_facebook'}
{default $twLang = 'btn_share_twitter'}

<p n:class="inline-btns, $class">
	<a href="https://www.facebook.com/sharer/sharer.php?u={$mutation->getBaseUrl()}{plink $object}" class="btn btn--secondary btn--icon-l btn--sm" target="_blank" rel="noopener noreferrer">
		<span class="btn__text">
			{('facebook')|icon, 'btn__icon'}
			{_$fbLang}
		</span>
	</a>
	<a href="https://twitter.com/intent/tweet?url={$mutation->getBaseUrl()}{plink $object}" class="btn btn--twitter btn--icon-l btn--sm" target="_blank" rel="noopener noreferrer">
		<span class="btn__text">
			{('x')|icon, 'btn__icon'}
			{_$twLang}
		</span>
	</a>

	{php $from = $object->cf->settings??->from ?? false}
	{php $to = $object->cf->settings??->to ?? false}
	{if $from && $to}
		<add-to-calendar-button
			name="{$object->name}"
			startDate="{$from|date('Y-m-d')}"
			endDate="{$to|date('Y-m-d')}"
			startTime="{$from|date('H:i')}"
			endTime="{$to|date('H:i')}"
			location="{$object->cf->settings??->address ?? false}"
			options="'Apple','Google','iCal','Outlook.com','Yahoo'"
			lightMode="bodyScheme"
			label="{_'btn_add_to_calendar'}"
			iCalFileName="{$object->name|webalize}">
		</add-to-calendar-button>
	{/if}
</p>