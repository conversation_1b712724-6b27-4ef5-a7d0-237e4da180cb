{default $class = false}

{varType App\Model\DTO\ProductDto $productDto}
{var $discountDate = $productDto->discountDate}

{var $discountDateFrom = $discountDate['from']}
{var $discountDateTo = $discountDate['to']}
{var $actualDate = new \DateTime()}
{if $actualDate < $discountDateTo}
	{var $timeRemaining = $discountDateTo?->diff($actualDate)}
	{php $d = $timeRemaining?->days ?: 0}
	{php $h = $timeRemaining?->h ?: 0}
	{php $m = $timeRemaining?->i ?: 0}
	{php $s = $timeRemaining?->s ?: 0}

	{php $showDays = $d > 0}
	{php $showHours = $d > 0 || $h > 0}
	{php $showMins = true}
	{php $showSecs = !$showHours}

	<p {*n:if="$discountDateTo !== null && ($discountDateFrom === null || ($discountDateFrom !== null && $discountDateFrom < $actualDate))"*} n:class="countdown, $class" data-controller="countdown">
		{capture $countdown}
			<span n:class="!$showDays ? u-d-n" data-countdown-target="days" data-value="{$d}" data-langs='["{_day_plural_1}", "{_day_plural_2}", "{_day_plural_3}"]'>
				{capture $daysLang}{$d|plural: "day_plural_1", "day_plural_2", "day_plural_3"}{/capture}
				<b>{$d}</b> {_$daysLang}
			</span>
			<span n:class="!$showDays ? u-d-n" data-countdown-target="conjunctionDaysHours">{_"and"}</span>
			<span n:class="!$showHours ? u-d-n" data-countdown-target="hours" data-value="{$h}" data-langs='["{_hours}", "{_hours}", "{_hours}"]'>
				<b>{$h}</b> {_"hours"}
			</span>

			<span n:class="!$showMins ? u-d-n" data-countdown-target="minutes" data-value="{$m}" data-langs='["{_mins}", "{_mins}", "{_mins}"]'>
				<b>{$m}</b> {_"mins"}
			</span>
			<span n:class="!$showSecs ? u-d-n" data-countdown-target="conjunctionMinsSecs">{_"and"}</span>
			<span n:class="!$showSecs ? u-d-n" data-countdown-target="seconds" data-value="{$s}" data-langs='["{_secs}", "{_secs}", "{_secs}"]'>
				<b>{$s}</b> {_"secs"}
			</span>
		{/capture}

		{_"countdown"|replace:"%countdown",(string)$countdown|noescape}
	</p>
{/if}
