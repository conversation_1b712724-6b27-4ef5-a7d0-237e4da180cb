{varType App\Model\Orm\Product\Product $productEntity}
{varType App\Model\DTO\Product\ProductDto $productDto}

{default $class = false}
{default $productEntity = $productEntity ?? $product}

<ul n:if="$productEntity" n:ifcontent n:class="flags, $class">
	{cache cacheKey('productTag-Px', $productEntity, $mutation), expire: '10 minutes', tags: $productEntity->getTemplateCacheTags()}
		<li n:foreach="$productEntity->findMainTags() as $tagLocalization">
			{var $color = $tagLocalization->tag->color, $icon = false}
			{var $tagType = $tagLocalization->tag->type}
			{if $color == 'cz'}
				{var $color = 'yellow-gradient', $icon = 'cz'}
			{/if}

			<span n:class="flag, 'flag--' . $color"> {* trida pro odkazy: link-mask__unmask *}
				{if $icon}{($icon)|icon, 'flag__icon'}{/if}
				{if $tagType == App\PostType\Tag\Model\TagType::promoPrice}
					- {$productDto->priceInfoDiscountPercentage} %
				{else}
					{$tagLocalization->name}
				{/if}
			</span>
		</li>
	{/cache}
</ul>
