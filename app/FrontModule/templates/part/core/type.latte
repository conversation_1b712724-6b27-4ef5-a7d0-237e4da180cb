{default $class = 'flag--type'}
{php $productEntity = isset($productEntity) ? $productEntity : $product}
{php $courseType = $productEntity->getCourseTypeParameterValue()}

<span n:if="$courseType !== null" n:class="flag, 'flag--' . $courseType->getColorClass(), $class">
	{$courseType->value}
	{if ($tooltip = $courseType->getTooltip($mutation)) !== ''}
		{embed $templates.'/part/core/tooltip.latte', class: 'tooltip--gray link-mask__unmask', placement: 'right'}
			{block btn}
				{('info')|icon, 'tooltip__icon'}
			{/block}
			{block content}
				{$tooltip}
			{/block}
		{/embed}
	{/if}
</span>
