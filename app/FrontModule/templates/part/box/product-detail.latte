{varType App\Model\Orm\Product\Product $product}
{varType App\Model\DTO\Product\ProductDto $productDto}
{default $class = false}
{default $isReviewDetail = false}

{php $isOld = $productDto->isOld}

<div n:class="b-product-detail, $class, $isOld ? b-product-detail--soldout">
	{* Horní část levé strány - název, hodnocení *}
	<div class="b-product-detail__name">
		{snippetArea breadcrumbArea}
			{control breadcrumb, class=>'b-product-detail__bc', variant=>$variant}
		{/snippetArea}
		<h1 class="b-product-detail__title">
			{if $isReviewDetail}{_"review_title_detail_prefix"}{/if} {$variant->name}
		</h1>
		<p n:if="!$isOld" class="b-product-detail__info">

			{* Recenze *}
			<span n:if="$product->reviewsPublic->count() && $product->reviewInfo['percent'] >= 85" class="b-product-detail__rating">
				{include $templates.'/part/core/stars.latte', class=>false, rating: $product->reviewInfo['percent'], stars=>$product->reviewAverage}

				{php $count = $product->reviewsPublic->count()}
				{capture $pluralLang}{$count|plural: "reviews_1", "reviews_2", "reviews_3" }{/capture}
				<a href="#rating">{$count} {_$pluralLang->__toString()}</a>
			</span>

			{* Videorecenze *}
			{php $tag = $product->getTag(App\PostType\Tag\Model\TagType::videoReview)}
			{ifset $tag}
				{php $tagLocalization = $tag->getLocalization($mutation)}
				<span n:if="$tagLocalization" class="flag flag--blue-light">
					{('play')|icon, 'flag__icon'}
					{$tagLocalization->name}
				</span>
			{/ifset}
		</p>
	</div>

	{* Galerie *}
	<div class="b-product-detail__gallery u-mb-last-0">
		{include $templates .'/part/box/product-gallery.latte', class: false, isOld: $isOld}

		{if !$isOld}
			{include $templates.'/part/core/flags.latte', class=>'b-product-detail__flags'}
			{include $templates.'/part/core/badge.latte', class: 'b-product-detail__badge', badge: $object->cf->badge ?? false}
		{/if}
	</div>

	{* Spodní část pravé strany - popis, cena, přidání do košíku, ... *}
	<div class="b-product-detail__content">
		{if $isOld}
			{* Vyprodaný produkt *}
			{include $templates.'/part/box/soldout.latte'}
		{else}
			<div n:if="$productLocalization->annotation ?? false" class="b-product-detail__annot u-mb-last-0">
				{$productLocalization->annotation|noescape}
			</div>

			{include $templates.'/part/box/product-action.latte'}

		{/if}

		{*
		<p class="b-product-detail__action">
			<a href="#" class="b-action">
				<span class="b-action__icon">{('compare')|icon}</span>
				{_"btn_compare"}
			</a>
			<a href="#" class="b-action">
				<span class="b-action__icon">{('graph')|icon}</span>
				{_"btn_watch"}
			</a>
		</p>
		*}
	</div>


	{* Kontaktní osoba *}
	{* TODO: pouze pokud nemá varianty *}
	{include $templates.'/part/box/help.latte', class: 'b-product-detail__contact'}
</div>

{* {include $templates.'/part/crossroad/variants.latte', class=>false} *}
{* {include $templates.'/part/box/selected.latte', class=>'b-product-detail__selected'} *}
{* {if $product->isDamaged}
	{include $templates . '/part/box/damaged.latte', class=>false}
{/if} *}


{* {php $preview = $object->cf->productImagePages??->image ?? []}
<div n:ifcontent class="b-product-detail__links">
	<p class="u-mb-0">
		<a n:if="count($preview)" href="{plink $pages->productGallery, productLocalizationId: $productLocalization->id}" class="item-icon" data-modal='{"medium": "fetch"}' data-snippetid="snippet--productPreview">
			{('example')|icon, 'item-icon__icon'}
			<span class="item-icon__text">{_"btn_preview"}</span>
		</a>
	</p>
	{snippet addToMyLibrary}
		{control addToMyLibrary, $product}
	{/snippet}
</div> *}

{* {if !in_array($variant->productAvailability->getType(), ['out_of_stock'])}
	{include $templates . '/part/box/product-benefits.latte', class=>'u-d-n u-d-b@md u-mb-xs'}
{/if} *}

	{* {if !$product->isCertificate}
		<p class="b-product-detail__author">
			<span n:ifcontent>
				{foreach $product->writers as $writerLocalization}
					{if $writerLocalization->isCollective}
						{$writerLocalization->nameAnchor}{sep}, {/sep}
					{else}
						<a href="{plink $writerLocalization}">{$writerLocalization->name}</a>{sep}, {/sep}
					{/if}
				{/foreach}
			</span>
			{if $product->hasTag(App\PostType\Tag\Model\TagType::bestseller) || $product->hasTag(App\PostType\Tag\Model\TagType::top)}
				<span class="flag flag--black flag--bd">
					<span class="u-d-n@sm">{_"flag_bestseller_top_short"}</span>
					<span class="u-d-n u-d-b@sm">{_"flag_bestseller_top"}r</span>
				</span>
			{/if}
		</p>
	{/if} *}
