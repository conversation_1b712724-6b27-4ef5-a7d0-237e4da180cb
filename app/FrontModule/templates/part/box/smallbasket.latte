{var App\Model\ShoppingCart\ShoppingCart $shoppingCart = $presenter->shoppingCart}
{varType App\FrontModule\Components\Cart\Cart $control}
{varType Nextras\Orm\Collection\ICollection $products}
{varType Nextras\Orm\Collection\ICollection $classEvents}
{varType Nextras\Orm\Collection\ICollection $vouchers}
{varType Nette\Application\UI\Form $form}

{default $class = false}

<div n:class="b-smallbasket, $class">
	<div class="b-smallbasket__main">
		{define #products}
			{default $class = false}
			<form n:if="$products->count()" n:name="$form" data-naja n:class="b-smallbasket__form, $class, block-loader">
				<ul class="b-smallbasket__list">
					<li class="b-smallbasket__item" n:foreach="$products as $productItem">
						{php $product = $productItem->variant->product}
						{varType App\Model\Orm\Order\Product\ProductItem $productItem}
						{var $link = $presenter->link($product, ['v' => $productItem->variant->id])}

						<a href="{$link}" class="b-smallbasket__img img img--contain prefetch" n:cache="cacheKey('smallImageBasket', $product), Nette\Caching\Cache::Expire => '30 minutes'">
							{if $product->firstImage}
								{php $img = $product->firstImage->getSize('sm')}
								<img src="{$img->src}" alt="{$productItem->getName()}" loading="lazy">
							{else}
								<img src="/static/img/illust/noimg.svg" alt="" loading="lazy">
							{/if}
						</a>
						<span class="b-smallbasket__name u-mb-last-0">
							<a href="{$link}" class="b-smallbasket__link prefetch u-d-b">
								{$productItem->getName()}
							</a>
							<span class="b-smallbasket__info u-c-help">
								{* TODO *}
								červené, vel. M
							</span>
						</span>
						<span class="b-smallbasket__price">
							{$productItem->unitPriceVat|money} / ks

						</span>
						<span class="b-smallbasket__amount">
							{$productItem->amount} ks
						</span>
						{include menu, removeUrl: $control->link('deleteItem!', ['variantId' => $productItem->variant->id])}

						{* TODO *}
						{* <span class="b-smallbasket__extras">
							extras
						</span> *}

						{* {include $templates.'/part/core/type.latte', class: false, product: $product} *}
						{* {include $templates.'/part/form/part/count.latte', autosubmit: true, class: 'inp-count--xs', variant: $productItem->variant, input: $form['products'][$productItem->variant->id]['quantity'], maxAmount: $productItem->getMaxAvailableAmount()} *}
					</li>
					{* <li n:foreach="$vouchers as $voucherItem" class="b-smallbasket__item">
						{varType App\Model\Orm\Order\Voucher\VoucherItem $voucherItem}
						<span class="b-smallbasket__img img img--contain">

						</span>
						<span class="b-smallbasket__name u-mb-last-0">
							{$voucherItem->getName()}
						</span>
						<span class="b-smallbasket__count">

						</span>
						<b class="b-smallbasket__info">
							{$voucherItem->unitPriceVat|money}
						</b>
					</li> *}
				</ul>
				<div class="block-loader__loader"></div>
			</form>
		{/define}
		{define #classEvents}
			{default $class = false}
			<form n:if="$classEvents->count()" n:name="$form" data-naja n:class="b-smallbasket__form, $class, block-loader">
				<ul class="b-smallbasket__list">
					<li class="b-smallbasket__item" n:foreach="$classEvents as $classItem">
						{varType App\Model\Orm\Order\Class\ClassItem $classItem}
						{php $product = $classItem->getProduct()}

						{var $link = $presenter->link($product, ['v' => $product->firstVariant->id])}

						<a href="{$link}" class="b-smallbasket__img tw-relative" n:cache="cacheKey('smallImageBasket', $product), Nette\Caching\Cache::Expire => '30 minutes'">
							{if $product->firstImage}
								{php $img = $product->firstImage->getSize('sm')}
								<img class="img img--4-3 img--contain" src="{$img->src}" alt="{$classItem->getName()}" loading="lazy">
							{else}
								<img class="img img--4-3 img--contain" src="/static/img/illust/noimg.svg" alt="" loading="lazy">
							{/if}

							{include $templates.'/part/core/type.latte', product: $product, class: 'flag--type flag--sm'}
						</a>
						<span class="b-smallbasket__name u-mb-last-0">
							<a href="{$link}" class="b-smallbasket__link prefetch u-d-b">
								{$classItem->getName()}
							</a>
							<span n:if="!empty($classItem->classEventDate)" class="b-smallbasket__info u-c-help">
								{$classItem->classEventDate}, {$classItem->classEventTime}
							</span>
						</span>
						<span class="b-smallbasket__price">
							{$classItem->unitPriceVat|money} / ks

						</span>
						<span class="b-smallbasket__amount">
							{$classItem->amount} ks
						</span>
						{include menu, removeUrl: $control->link('deleteClass!', ['classEventIdentifier' => $classItem->getIdentifier()])}

						{* TODO *}
						{* <span class="b-smallbasket__extras">
							extras
						</span> *}

						{* {include $templates.'/part/core/type.latte', class: false, product: $product} *}
						{* {include $templates.'/part/form/part/count.latte', autosubmit: true, class: 'inp-count--xs', variant: $productItem->variant, input: $form['products'][$productItem->variant->id]['quantity'], maxAmount: $productItem->getMaxAvailableAmount()} *}
					</li>
					{* <li n:foreach="$vouchers as $voucherItem" class="b-smallbasket__item">
						{varType App\Model\Orm\Order\Voucher\VoucherItem $voucherItem}
						<span class="b-smallbasket__img img img--contain">

						</span>
						<span class="b-smallbasket__name u-mb-last-0">
							{$voucherItem->getName()}
						</span>
						<span class="b-smallbasket__count">

						</span>
						<b class="b-smallbasket__info">
							{$voucherItem->unitPriceVat|money}
						</b>
					</li> *}
				</ul>
				<div class="block-loader__loader"></div>
			</form>
		{/define}

		{if $products->count() === 0 && $classEvents->count() === 0}
			{php $emptyText = $pages->cart->cf->emptyBasket->header->content ?? false}
			<div n:if="$emptyText" class="b-smallbasket__empty u-mb-last">
				{$emptyText|noescape}
			</div>
		{else}
			{include #products, products=>$products}
			{include #classEvents, classEvents: $classEvents}
		{/if}
	</div>
	<div n:ifcontent class="b-smallbasket__bottom">
		{control freeDelivery}

		{* Banner *}
		{if $products->count() === 0 && $classEvents->count() === 0}
		{include $templates.'/part/box/bnr-img.latte', class: 'b-smallbasket__bnr u-mb-0'}
		{/if}
	</div>
</div>


{* {if $shoppingCart->showForgottenCart()}
		<p class="h5 u-mb-0">
			{_"smallbasket_title_forgot"}
		</p>
		<p class="u-mb-xs">
			{_"smallbasket_text_forgot"}
		</p>
		{include #products, class: 'u-mb-sm', products: $products}

		<div class="b-smallbasket__bottom u-bgc-white u-ta-r">
			<p class="u-ta-r u-mb-xxs">
				{_"smallbasket_total"} {$presenter->shoppingCart->getTotalPriceVat()|money}
			</p>
			<p class="b-smallbasket__btns u-mb-0">
				<a href="{$presenter->link('emptyCart!')}" class="btn btn--white btn--sm">
					<span class="btn__text">
						{_"btn_empty_basket"}
					</span>
				</a>
				<a href="{$presenter->link('emptyCart!')}" class="btn btn--secondary btn--sm">
					<span class="btn__text">
						{_"btn_enter_basket"}
					</span>
				</a>
			</p>
		</div> *}


{* {php $control['productListCartInterested']->setTemplateParameters(['class' => 'section--products section--w-line'])}
{control productListCartInterested} *}


{define menu}
	{embed $templates.'/part/core/tooltip.latte', class: 'b-smallbasket__more', placement: 'bottom-start', settings: '{"trigger": "click"}'}
		{block btn}
			<button type="button" class="b-smallbasket__remove as-link">
				{('cross')|icon}
				<span class="u-vhide">{_"btn_remove"}</span>
			</button>
		{/block}
		{block content}
			<div class="b-more__content is-open-remove" data-controller="toggle-class">
				<div class="b-more__remove">
					<p class="u-fw-b u-ta-c">{_"more_remove_title"}</p>
					<p class="b-more__btns tw-mb-0">
						<a href="{$removeUrl}" class="btn btn--bd" data-naja data-naja-history="off" data-naja-loader="body">
							<span class="btn__text">{_"yes"}</span>
						</a>
						<button type="button" class="btn btn--bd js-tooltip-close">
							<span class="btn__text">{_"no"}</span>
						</button>
					</p>
				</div>
			</div>
		{/block}
	{/embed}
{/define}