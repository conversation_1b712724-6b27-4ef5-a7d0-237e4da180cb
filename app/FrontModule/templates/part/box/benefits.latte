{default $class = false}
{default $items = $mutation->cf->benefits->items ?? []}

<div n:if="count($items)" n:class="b-benefits, $class">
	<ul class="b-benefits__list">
		<li n:foreach="$items as $item" class="b-benefits__item">
			{php $icon = isset($item->icon) ? $item->icon->getEntity() ?? false : false}
			<img n:if="$icon" class="b-benefits__icon" src="{$icon->getSize('xs')->src}" alt="" loading="lazy" width="50" height="50">
			{$item->text}
		</li>
	</ul>
</div>