{default $class = 'u-mb-md u-mb-2xl@md'}
{default $storeTitle = false}
{default $storeAnnot = false}

<div n:class="$class, u-maw-10-12, u-mx-auto">
	<div class="grid grid--y-sm">
		<div class="grid__cell size--6-12@xl">
			{include $templates.'/part/box/help.latte', class: 'b-help--big'}
		</div>
		<div class="grid__cell size--6-12@xl">
			{php $img = isset($pages->contact->cf->contacts->store_image) ? $pages->contact->cf->contacts->store_image->getEntity() ?? false : false}
			{php $address = $pages->contact->cf->contacts??->store_address_full ?? false}
			{php $hours = $pages->contact->cf->contacts??->phone_hours ?? false}

			<div class="xl:tw-pt-[6rem]">
				<div class="tw-relative tw-rounded-md md:tw-rounded-xl tw-overflow-hidden tw-flex">
					<img class="tw-flex-[0_0_auto] tw-w-full" src="{$img->getSize('md-16-9')->src}" alt="" loading="lazy">
					<div class="tw-p-[3.2rem_2.4rem] md:tw-p-[6rem] tw-bg-gradient-to-b tw-from-[rgba(0,0,0,0.6)] tw-from-15% tw-to-transparent tw-to-64% u-mb-last-0 tw-flex-[0_0_auto] tw-ml-[-100%] tw-w-full tw-flex tw-flex-col tw-justify-center">
						<div class="u-mb-last-0 tw-mb-[1.6rem] md:tw-mb-[2rem] tw-text-center">
							<h2 n:if="$storeTitle" class="tw-text-white tw-mb-[0.4rem]">{$storeTitle}</h2>
							<p n:if="$storeAnnot" class="tw-text-white md:tw-text-[1.5rem]">{$storeAnnot}</p>
						</div>
						<p n:ifcontent class="tw-bg-bg tw-rounded-md md:tw-rounded-xl tw-max-w-[33rem] tw-w-full tw-mx-auto tw-p-[2rem_1.2rem_2rem_2.4rem] md:tw-p-[2.4rem_1.6rem_2.4rem_2.8rem] tw-flex tw-gap-[0.8rem] tw-mb-[0.8rem]">
							<span class="tw-flex-1 tw-border-[0.1rem] tw-border-solid tw-border-tile tw-border-t-0 tw-border-b-0 tw-border-l-0 tw-pr-[0.8rem]">
								{$address|texy|noescape}
								{$hours}
								<a n:if="$pages->shopInPrague ?? false" class="tw-block" href="{plink $pages->shopInPrague}">{_"btn_more_store"}</a>
							</span>
							{('pin-outline')|icon, 'tw-text-primary tw-w-[4rem] tw-self-start'}
						</p>
						<p n:if="isset($pages->serviceForm)" class="message message--sm message--warning tw-max-w-[33rem] tw-mx-auto">
							<span class="message__emoji">👌</span>
							<span class="message__content">
								{_"msg_buyin_servis_form"|noescape} <a href="{plink $pages->serviceForm}" target="_blank" rel="noopener noreferrer">{_"msg_buyin_servis_form_link"}</a>.
							</span>
						</p>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>