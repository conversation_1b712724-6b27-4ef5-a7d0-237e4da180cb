<article class="b-brand-teaser link-mask">
	<div class="b-brand-teaser__img">
		<div class="img img--portrait img--fill">
			{if $c->getFirstImage() !== null}
				{php $img = $c->getFirstImage()->getSize('photo')}
				<img src="{$img->src}" alt="" width="{$img->width}" height="{$img->height}" loading="lazy">
			{else}
				<img src="/static/img/illust/noimg.svg" alt="" width="260" height="308" loading="lazy">
			{/if}
		</div>
	</div>
	<div class="b-brand-teaser__content">
		<h3 class="b-brand-teaser__title">
			<a n:href="$c" class="b-brand-teaser__link link-mask__link">
				{$c->name}
			</a>
		</h3>
		<p n:ifset="$c->cf->base->position" class="b-brand-teaser__desc">
			{$c->cf->base->position}
		</p>
		<p n:ifset="$c->cf->base->annotation" class="b-brand-teaser__annot u-mb-0">
			{$c->cf->base->annotation}
		</p>
	</div>
</article>
