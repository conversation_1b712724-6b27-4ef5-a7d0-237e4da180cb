{default $class = false}
{default $cf = $object->cf->didyouknow ?? false}

{php $img = isset($cf->img) ? $cf->img->getEntity() ?? false : false}
{php $page = isset($cf->page) ? $cf->page->getEntity() ?? false : false}

<article n:if="$page" n:class="b-didyouknow, $class, u-maw-4-12, u-mx-auto, link-mask">
	<p n:if="$img" class="b-didyouknow__img">
		<img class="img" src="{$img->getSize('sm')->src}" alt="{$img->getAlt($mutation)}" loading="lazy">
	</p>
	<div class="b-didyouknow__box">
		<p class="u-c-help">
			{_"didyouknow"}
		</p>
		<p class="b-didyouknow__title">
			{if $cf->text ?? false}
				{$cf->text}
			{else}
				{$page->nameAnchor}
			{/if}
		</p>
		<p class="b-didyouknow__more">
			<a href="{plink $page}" class="item-icon link-mask__link">
				<span class="item-icon__text">
					{_"btn_more_info"}
				</span>
				{('arrow-right-thin')|icon, 'item-icon__icon'}
			</a>
		</p>
	</div>
</article>