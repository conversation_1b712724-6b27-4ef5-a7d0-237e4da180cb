{varType App\Model\DTO\Product\ProductDto $productDto}
{default $class = false}

{php $discountAmount = $productDto->priceInfoDiscountAmount}
{php $discountPercentage = $productDto->priceInfoDiscountPercentage}
{php $originalPrice = $productDto->priceInfoOriginalPrice}

<p n:if="($discountAmount || $discountPercentage) && !$productDto->isOld && !in_array($productDto->productAvailabilityType, ['out_of_stock']) && !$productDto->price->isZero()"  n:class="b-saving, $class">
	{if $discountAmount}
		<b>{_"price_saving"}</b>
		{$discountAmount|money}
	{elseif $originalPrice}
		<s>{$originalPrice|money}</s>
	{/if}

	{var $tooltipText = $productDto->tooltipText}
	{if $tooltipText}
		{embed $templates.'/part/core/tooltip.latte', btnClass=>'as-link link-mask__unmask'}
			{block btn}
				{('info')|icon, class: 'tooltip__icon'}
			{/block}
			{block content}
				{$tooltipText|noescape}
			{/block}
		{/embed}
	{/if}
	<span n:if="$discountPercentage" class="flag flag--red">{_"discount"} {$discountPercentage} %</span>
</p>
