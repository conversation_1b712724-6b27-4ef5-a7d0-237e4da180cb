{default $class = 'u-mb-sm u-mb-xl@md'}
{default $cf = $object->cf->buyin_prices ?? false}

<div n:if="$cf" n:class="$class, u-maw-8-12, u-mx-auto">
	<h2 n:if="$cf->title ?? false" class="tw-mb-[1.2rem] md:tw-mb-[2.4rem]">{$cf->title}</h2>
	<ul n:if="count($cf->items ?? [])" class="u-reset-ul tw-grid tw-grid-cols-[auto_1fr_max-content] md:tw-grid-cols-3 md:tw-text-[1.5rem] tw-gap-[0.4rem]">
		<li n:foreach="$cf->items as $item" class="tw-grid tw-grid-cols-subgrid tw-col-span-full">
			<span class="tw-grid tw-col-span-full tw-grid-cols-subgrid tw-bg-bg tw-rounded-md tw-p-[1rem_1.8rem] tw-gap-[1rem] tw-items-center">
				<span>
					<b n:if="$item->name ?? false" class="tw-block">{$item->name}</b>
					{$item->specification ?? false}
				</span>
				<span n:if="$item->state ?? false" class="tw-text-center">{$item->state}</span>
				<b n:if="$item->price ?? false" class="tw-text-right">{$item->price}</b>
			</span>
		</li>
	</ul>
</div>