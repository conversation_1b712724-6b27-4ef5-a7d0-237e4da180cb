{default $class = false}
{default $cfPerson = $pages->contact->cf->contact<PERSON>erson ?? false}
{default $cfContacts = $pages->contact->cf->contacts ?? false}

<p n:if="$cfPerson || $cfContacts" n:class="b-person-footer, $class">
	{php $name = $cfPerson->name ?? false}
	{php $img = isset($cfPerson->image) ? $cfPerson->image->getEntity() ?? false : false}
	{include $templates.'/part/core/person.latte', class: 'b-person-footer__person', img: $img, name: $name}

	<span n:ifcontent class="u-mb-last-0">
		<span n:if="$cfContacts->phone ?? false" class="b-person-footer__item item-icon">
			{('phone')|icon, 'item-icon__icon'}
			<span class="item-icon__text">
				<a href="tel:{$cfContacts->phone|replace:' ',''}" class="u-d-b">{$cfContacts->phone}</a>
				<span n:if="$cfContacts->phone_hours ?? false" class="b-person-footer__info">({$cfContacts->phone_hours})</span>
			</span>
		</span>
		<span n:if="$cfContacts->mail ?? false" class="b-person-footer__item item-icon">
			{('envelope')|icon, 'item-icon__icon'}
			<span class="item-icon__text">
				<a href="mailto:{$cfContacts->mail}" class="u-d-b">{$cfContacts->mail}</a>
			</span>
		</span>
	</span>
</p>