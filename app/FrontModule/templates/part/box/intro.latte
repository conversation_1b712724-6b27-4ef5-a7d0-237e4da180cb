{default $class = 'u-mb-md u-mb-xl@md'}
{default $name = $seoLink??->name ?? $object->name}
{default $annotation = $seoLink??->description ?? $object->annotation ?? $object->description ?? null}
{default $bg = isset($object->cf->headerType->image) ? $object->cf->headerType->image->getEntity() ?? false : false}

<div n:class="b-intro, holder, holder--lg, $class, u-mb-last-0">
	<img n:if="$bg" class="b-intro__bg" srcset="
			{$bg->getSize('sm')->src} 320w,
			{$bg->getSize('md')->src} 560w,
			{$bg->getSize('lg')->src} 750w,
			{$bg->getSize('xl-2-1')->src} 1200w,
			{$bg->getSize('2xl-2-1')->src} 1920w"
		sizes="(max-width: 90rem) 100vw,
			144rem"
		src="{$bg->getSize('2xl')->src}"
		alt="{$bg->getAlt($mutation)}" fetchpriority="high">
	<div class="b-intro__content">
		<h1 class="b-intro__title u-mb-0 title">
			{php $words = explode(' ', $name)}
			<span n:foreach="$words as $word" class="title__item">{$word|noescape}</span>
		</h1>
		<p n:if="$annotation" class="b-intro__annot">
			<span>
				{$annotation}
			</span>
		</p>
		{block content}{/block}
	</div>
</div>