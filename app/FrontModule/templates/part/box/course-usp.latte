{default $class = false}
{default App\Model\Orm\Product\Product $productEntity = $productEntity ?? $product}
{varType App\Model\Orm\ProductLocalization\ProductLocalization $productLocalization}

{var $usps = $productLocalization->cf->productUsp ?? []}
<ul n:class="b-course-usp, $class" n:if="$usps !== []">
	<li class="b-course-usp__box" n:foreach="$usps as $usp">
		{('usp')|icon, 'b-course-usp__icon'}
		{$usp->name}
	</li>
</ul>
