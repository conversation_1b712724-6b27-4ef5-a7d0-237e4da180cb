{default $class = 'u-mb-0'}
{default $product = false}
{default $variant = false}

<p n:if="$product && $variant" n:class="b-enter-product, $class, link-mask">
		{if $product->firstImage}
			<img src="{$product->firstImage->getSize('sm')->src}" alt="{$variant->nameAnchor}" loading="lazy">
		{else}
			<img src="/static/img/illust/noimg.svg" alt="" loading="lazy">
		{/if}
	<b class="h2 u-mt-0 u-mb-0">
		{_"enter_product"}
		<a href="{plink $product}" class="u-d-b link-mask__link">{$variant->nameAnchor}</a>
	</b>
</p>
