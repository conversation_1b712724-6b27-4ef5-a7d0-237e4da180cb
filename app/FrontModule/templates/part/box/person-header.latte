{default $class = false}
{default $cfPerson = $pages->contact->cf->contactPerson ?? false}
{default $cfContacts = $pages->contact->cf->contacts ?? false}

<p n:if="$cfPerson || $cfContacts" n:class="b-person-header, $class">
	{php $name = $cfPerson->name ?? false}
	{php $img = isset($cfPerson->image) ? $cfPerson->image->getEntity() ?? false : false}
	{include $templates.'/part/core/person.latte', class: 'b-person-header__person', img: $img, name: $name}

	<span n:if="isset($cfContacts->phone)">
		<span class="b-person-header__text">
			{_"header_contact_text"}
		</span>
		<span class="b-person-header__call">
			<a href="tel:{$cfContacts->phone|replace:' ',''}" class="b-person-header__link">{$cfContacts->phone}</a> <span n:if="isset($cfContacts->phone_hours)">({$cfContacts->phone_hours})</span>
		</span>
	</span>
</p>
