{default $class = false}

<div n:class="b-help, $class">
	<div n:tag-if="str_contains($class, 'big')" class="b-help__helper">
		<img n:if="str_contains($class, 'big')" class="b-help__decor" src="/static/img/illust/dragonscale.webp" alt="" loading="lazy" width="538" height="403">
		<p class="b-help__title h4 u-ta-c">
			{_"contact_person_product_title"|noescape}
		</p>
		<p class="b-help__box u-mb-0">
			{default $cfPerson = $pages->contact->cf->contactPerson ?? false}
			{default $cfContacts = $pages->contact->cf->contacts ?? null}

			{php $name = $cfPerson->name ?? false}
			{php $img = isset($cfPerson->image) ? $cfPerson->image->getEntity() ?? false : false}
			{include $templates.'/part/core/person.latte', class: 'b-help__person', img: $img, name: $name}

			<span n:ifcontent class="b-help__contact u-mb-last-0">
				<a href="tel:{$cfContacts->phone|replace:' ',''}" n:if="$cfContacts->phone ?? false" class="b-help__phone item-icon">
					{('phone-outline-bold')|icon, 'item-icon__icon'}
					<span class="item-icon__text">
						{_"contact_person_call"} {$cfContacts->phone}
					</span>
				</a>
				<span class="b-help__hours">
					{_"contact_person_shift"}:<br> {$name} ({$cfContacts?->phone_hours ?? ''})
				</span>
			</span>
		</p>
	</div>
</div>
