{default $class = 'u-mb-md u-mb-xl@md'}
{default $cf = $object->cf->reviews ?? false}

<article n:class="b-our-reviews, $class">
	<h2 n:if="$cf->title ?? false" class="b-our-reviews__title u-mb-md@md">
		{$cf->title}
	</h2>
	<ul class="b-our-reviews__grid grid">
		{define #box}
			<li n:if="$cf->$type ?? false" class="grid__cell size--6-12@sm size--3-12@xl">
				<p class="b-our-reviews__box b-our-reviews__box--{$type} box u-mb-0">
					<b n:if="$cf->$type->title ?? false">
						{$cf->$type->title}
					</b>
					<span n:if="$cf->$type->count ?? false" class="b-our-reviews__count u-d-b">
						{$cf->$type->count}
					</span>
					<span class="b-our-reviews__rating item-icon">
						{if $type == 'heureka'}
							<img class="item-icon__icon" src="/static/img/illust/certificates/heureka.png" alt="" loading="lazy" width="60" height="60">
						{else}
							<span class="item-icon__icon">
								{($type)|icon}
							</span>
						{/if}
						<span class="item-icon__text">
							{$cf->$type->average}<br>
							{include $templates.'/part/core/stars.latte', stars=>$cf->$type->stars ?? 0}
						</span>
					</span>
				</p>
			</li>
		{/define}

		{include #box, type=>'zbozi'}
		{include #box, type=>'heureka'}
		{include #box, type=>'google'}
		{include #box, type=>'facebook'}
	</ul>
</article>