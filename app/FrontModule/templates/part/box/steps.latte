{default $class = 'u-mb-md u-mb-2xl@md'}
{default $title = false}
{default $items = []}
{default $bd = false}

<div n:class="b-steps, $class, $bd ? b-steps--bd">
	<div class="u-maw-8-12 u-mx-auto u-mb-last-0">
		<div class="b-steps__inner tw-mb-[2.8rem] md:tw-mb-[4.8rem]">
			<h2 n:if="$title" class="b-steps__title">
				{$title}
			</h2>
			<div class="b-steps__right u-mb-last-0">
				<ol n:if="count($items)" class="b-steps__list tw-mb-[1.2rem] md:tw-mb-[2.4rem]">
					<li n:foreach="$items as $item" n:if="$item->text ?? false" class="b-steps__item">
						<span n:if="!$iterator->isLast()" class="b-steps__arrow"></span>
						<span>
							{$item->text|texy|noescape}
						</span>
					</li>
				</ol>
				{block rightExtra}{/block}
			</div>
		</div>
		{block bottomExtra}{/block}
	</div>
</div>