{default $class = false}
{varType App\Model\Orm\ProductVariant\ProductVariant $variant}

<div n:class="b-added, $class">
	<div class="b-added__left u-mb-last-0">
		<h1 class="b-added__title item-icon">
			{('check-circle')|icon, 'item-icon__icon'}
			<span class="item-icon__text">
				{_"prebasket_added_text"}
			</span>
		</h1>
		<div class="b-added__product">
			<p class="b-added__img u-mb-0">
				<a href="{plink $variant->product, v: $variant->id}">
					{if $variant->firstImage}
						{php $img = $variant->firstImage->getSize('sm-4-3')}
						<img class="img img--4-3 img--contain" src="{$img->src}" alt="{$variant->name}" fetchpriority="high">
					{else}
						<img class="img img--4-3 img--contain" src="/static/img/illust/noimg.svg" alt="" fetchpriority="high">
					{/if}
				</a>
			</p>
			<div class="u-mb-last-0">
				<p class="b-added__name u-fw-b u-mb-0">
					<a href="{plink $variant->product, v: $variant->id}" class="b-added__link">
						{if $quantityAdded > 1}{$quantityAdded}&times;{/if}
						{$variant->name}
					</a>
				</p>
				{* TODO
				<p class="b-added__annot u-c-help">
					Definice varianty (velikost, barva apod.)
				</p>
				<p class="u-c-green u-fw-b">
					+ Školení v hodnotě 3 000 Kč jako dárek
				</p>
				*}
			</div>
		</div>
		<p>
			{*TODO : gift carousel*}
		</p>
	</div>
	<svg class="b-added__arrow" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 42 226"><path fill="currentColor" d="M1.5.5.5.9l40 112.3-40 112.3 1 .4 40-112.7L1.5.5z"/></svg>
	<div class="b-added__right" data-fixed-target="element">
		<p class="b-added__contains h5">
			{var App\Model\ShoppingCart\ShoppingCart $shoppingCart = $presenter->shoppingCart}
			{php $productCount = $shoppingCart->getTotalCount()}
			{_"precart_in_cart"} {$productCount} {_($productCount|plural: "product_type_1", "product_type_2", "product_type_3")}
			<br>
			{_"precart_in_total_of"} {$shoppingCart->getTotalPriceVat()|money}
			<span class="b-added__count b-basket__icon-holder">
				{('basket')|icon, 'b-basket__icon'}
				<span class="b-basket__count">{$productCount}</span>
			</span>
		</p>

		{control freeDelivery}

		<p class="b-added__btns u-mb-0">
			<a n:if="$lastUrl ?? false" class="b-added__btn btn btn--bd btn--xl prefetch" href="{$lastUrl}">
				<span class="btn__text">
					{_"btn_back_shopping_prebasket"}
				</span>
			</a>
			<a n:href="$pages->cart" class="b-added__btn btn btn--secondary btn--xl prerender">
				<span class="btn__text">
					<span class="u-d-n u-d-b@xl">{_"btn_go_to_cart"}</span>
					<span class="u-d-n@xl">{_"btn_enter_basket"}</span>
				</span>
			</a>
		</p>
	</div>
</div>

{* {var $priceVat = $variant->priceVat($mutation, $priceLevel, $state)}
{$priceVat|money}

<span n:if="$quantity != $quantityAdded" class="u-c-orange">
	{_"prebasket_added_less"|replace: '%quantity', (string)$quantity|replace: '%added', (string)$quantityAdded}
</span> *}

{* <button n:if="$isAjax" class="btn btn--bd js-modal-close">
	<span class="btn__text">
		{_"btn_continue_shopping"}
	</span>
</button> *}
