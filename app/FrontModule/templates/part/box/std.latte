{default $titleTag = 'h2'}
{default $text = false}
{default $icon = false}
{default $link = false}

<article nif="$link && $text" class="b-std">
	<h3 n:tag="$titleTag" class="b-std__title h5">
		<a href="{$link}" class="b-std__link box">
			<span n:tag-if="$icon" class="item-icon">
				{if $icon && !($icon->isSvg ?? false)}
					{($icon)|icon, 'item-icon__icon'}
				{elseif $icon && $icon->isSvg}
					<img class="item-icon__icon" src="{$icon->url}" alt="" loading="lazy" width="40" height="40">
				{/if}
				<span n:tag-if="$icon" class="item-icon__text">
					{$text}
				</span>
			</span>
		</a>
	</h3>
</article>