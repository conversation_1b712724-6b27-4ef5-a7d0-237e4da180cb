{default $class = 'u-mb-md u-mb-2xl@md'}
{default $topReferences = []} {* TODO *}

{* dummy content *}
{php array_push($topReferences, (object) array('toggle' => 'video', 'video' => (object) array('link' => 'https://www.youtube.com/watch?v=lFwdracJ0LU')))}
{php array_push($topReferences, (object) array('toggle' => 'video', 'video' => (object) array('link' => 'https://www.youtube.com/watch?v=lFwdracJ0LU')))}
{php array_push($topReferences, (object) array('toggle' => 'video', 'video' => (object) array('link' => 'https://www.youtube.com/watch?v=lFwdracJ0LU')))}

<section id="{_'toc_references_showcase'|webalize}" n:class="b-showcase, $class">
	<h2 class="tw-mb-[1.2rem] md:tw-mb-[2.4rem]">
		{_"title_references_showcase"}
	</h2>

	{* Vybrané reference *}
	{include $templates.'/part/box/showcase.latte', class: 'tw-mb-[2.8rem] md:tw-mb-[4.8rem]', items: $topReferences, modalGallery: 'references-top'}

	{* Další reference (taby) *}
	{include $templates.'/part/menu/tabs-btns.latte', items: ['Natáčení eventů','E-shop','Natáčení svatby','Livestream','Dron footage','Armáda','Zemědělství a lesnictví','Dron footage','Armáda','Zemědělství a lesnictví']}

	<div class="grid grid--x-xs grid--x-sm@md grid--y-xs grid--y-sm@md">
		{default $references = [1, 2, 3, 4, 5, 6]} {* TODO *}
		{foreach $references as $reference}
			<div class="grid__cell size--6-12@md size--4-12@lg">
				{if $iterator->counter == 1} {* TOOD *}
					{* Článek *}
					{include #article, class: false}
				{elseif $iterator->counter == 2} {* TODO *}
					{* Video *}
					{default $poster = false} {* TODO: custom poster (zástupný obrázek videa) *}
					{default $link = 'https://www.youtube.com/watch?v=c5FDqYxTtzk'} {* TODO *}
					{include #video, link: $link, poster: $poster, maxSize: $iterator->isFirst() ? 1540 : 750, posterSize: $iterator->isFirst() ? 'xl-16-9' : 'lg-16-9', modalGallery: 'references-top'}
				{else}
					{* Obrázek (bez prokliku) *}
					{default $src = '/static/img/illust/dummy-course.png'} {* TODO src + getSize('2xl') *}
					{default $srcThumb = '/static/img/illust/dummy-course.png'} {* TODO src + getsize: $iterator->isFirst() ? 'xl-16-9' : 'lg-16-9' *}
					{include #image, src: $src, srcThumb: $srcThumb, modalGallery: 'references-top'}
				{/if}
			</div>
		{/foreach}
	</div>
	{* {control pager, [filter => $cleanFilterParam, pluralLang: 'btn_more_showcase', ajaxPage=>$ajaxPage]} *}
	TODO: paging component
</section>

{define #video}
	{default $link = false}
	{default $poster = false}
	{default $maxSize = 750}
	{default $posterSize = 'lg-16-9'}
	{default $modalGallery = ''}

	<p class="tw-h-full u-mb-0">
		<a class="tw-h-full" href="{$link}" data-modal='{"gallery": "{$modalGallery}"}'>
			{include $templates.'/part/box/video.latte', class: 'tw-h-full tw-rounded-md md:tw-rounded-xl tw-overflow-hidden', maxSize: $maxSize, posterSize: $posterSize, poster: $poster, link: $link}
		</a>
	</p>
{/define}

{define #article}
	{default $class = false}
	{default $page = false}
	{include $templates.'/part/box/article-inside.latte', class: $class . ' tw-w-full tw-h-full', page: $page, showTags: false, showAuthor: false, showDays: false}
{/define}

{define #image}
	{default $src = false}
	{default $imgClass = false}
	{default $srcThumb = false}
	{default $modalGallery = ''}
	<p class="tw-h-full u-mb-0 tw-rounded-md md:tw-rounded-xl tw-overflow-hidden">
		<a class="tw-h-full" href="{$src}" data-modal='{"gallery": "{$modalGallery}"}'>
			<img n:class="img, img--16-9, 'hover:tw-scale-[1.05] tw-transition-transform tw-duration-300'" src="{$srcThumb}" alt="{* {$img->getAlt($mutation)} *}" loading="lazy">
		</a>
	</p>
{/define}