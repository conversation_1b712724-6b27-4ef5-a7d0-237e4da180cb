{varType App\Model\Orm\Product\Product $product}
{varType App\Model\Orm\Parameter\Parameter $parameter}
{varType App\Model\Orm\ParameterValue\ParameterValue $parameterValue}
{default $class = false}

<ul n:class="b-selected, $class">
	<li n:foreach="$parametersMain as $parameterValues">
		{php $parameterValues = $parameterValues instanceOf App\Model\Orm\ParameterValue\ParameterValue ? [$parameterValues] : $parameterValues}

		{foreach $parameterValues as $parameterValue}
			{first}{$parameterValue->parameter->name}:{/first}

			{if $parameterValue->isLanguageParameter}
				{if !$parameterValue->isCzechLanguage}
					{php $hasOneValue = $parameterValues instanceOf App\Model\Orm\ParameterValue\ParameterValue}
					{if $hasOneValue}
						{if isset($parameterValue->cf->flagIcon->flagIcon->entity->url)}
							<img class="b-selected__country-flag" src="{$parameterValue->cf->flagIcon->flagIcon->entity->url}" alt="" loading="lazy">
						{/if}
					{else}
						{first}
							{if isset($parameterValue->cf->flagIcon->flagIcon->entity->url)}
								<img class="b-selected__country-flag" src="{$parameterValue->cf->flagIcon->flagIcon->entity->url}" alt="" loading="lazy">
							{/if}
						{/first}
					{/if}
				{/if}
			{/if}

			{var $entityBindUid = $parameterValue->parameter->uid}
			{if $parameterValue->hasDetail && $entityBindUid}
				<a href="{plink $parameterValue->$entityBindUid->getLocalization($product->getMutation())}" class="b-selected__prevent">
					{$parameterValue->internalValue}
				</a>
			{elseif $product->mainCategory->isImportantParameter($parameterValue->parameter->uid)}
				{var $filter = ['dials' => [$parameterValue->parameter->uid => [$parameterValue->id => $parameterValue->id]]]}
				{capture $link}{plink $product->mainCategory, 'filter' => $filter}{/capture}
				{php $link = urldecode(htmlspecialchars_decode($link))}
				<a href="{$link}" class="b-selected__prevent">{$parameterValue->internalValue}</a>
			{else}
				<span class="b-selected__prevent">{$parameterValue->internalValue}</span>
			{/if}

			{sep}, {/sep}
		{/foreach}
	</li>
</ul>
