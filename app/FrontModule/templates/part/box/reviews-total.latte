{default $class = false}
{default $reviewInfo = $product->reviewInfo}
{default $isReviewDetail = false}

<p n:if="$reviewInfo['count'] > 0" n:class="b-reviews-total, $class, $isReviewDetail ? b-reviews-total--reviews">
	<b class="b-reviews-total__rating">{$reviewInfo['percent']} %</b>
	<span class="b-reviews-total__count">
		{include $templates.'/part/core/stars.latte', stars=>$product->reviewAverage, class=>'b-reviews-total__stars'}
		{_"rating_count"|replace:'%count', (string)$reviewInfo['count']}
	</span>
</p>

{* <div n:if="$isReviewDetail" class="b-reviews-total__add u-ta-c">
	<p n:if="!$user->isLoggedIn()" class="u-mb-xxs">
		{_"review_total_add_review_text_unlogged"}
	</p>
	<p class="u-mb-0">
		<a href="#" class="btn btn--secondary">
			<span class="btn__text">
				{_"btn_add_review"}
			</span>
		</a>
	</p>
</div> *}
{* <ul class="b-reviews-total__list">
	<li class="b-reviews-total__item">
		{include $templates.'/part/core/stars.latte', class=>'b-reviews-total__stars', stars=>5, type=>'full'}
		{include $templates.'/part/core/progress.latte', class=>'b-reviews-total__progress', count=>$reviewInfo['countByStars'][5] ?? 0, percentage=>isset($reviewInfo['countByStars'][5]) ? $reviewInfo['countByStars'][5] * 100 / $reviewInfo['count'] : 0}
	</li>
	<li class="b-reviews-total__item">
		{include $templates.'/part/core/stars.latte', class=>'b-reviews-total__stars', stars=>4, type=>'full'}
		{include $templates.'/part/core/progress.latte', class=>'b-reviews-total__progress', count=>$reviewInfo['countByStars'][4] ?? 0, percentage=>isset($reviewInfo['countByStars'][4]) ? $reviewInfo['countByStars'][4] * 100 / $reviewInfo['count'] : 0}
	</li>
	<li class="b-reviews-total__item">
		{include $templates.'/part/core/stars.latte', class=>'b-reviews-total__stars', stars=>3, type=>'full'}
		{include $templates.'/part/core/progress.latte', class=>'b-reviews-total__progress', count=>$reviewInfo['countByStars'][3] ?? 0, percentage=>isset($reviewInfo['countByStars'][3]) ? $reviewInfo['countByStars'][3] * 100 / $reviewInfo['count'] : 0}
	</li>
	<li class="b-reviews-total__item">
		{include $templates.'/part/core/stars.latte', class=>'b-reviews-total__stars', stars=>2, type=>'full'}
		{include $templates.'/part/core/progress.latte', class=>'b-reviews-total__progress', count=>$reviewInfo['countByStars'][2] ?? 0, percentage=>isset($reviewInfo['countByStars'][2]) ? $reviewInfo['countByStars'][2] * 100 / $reviewInfo['count'] : 0}
	</li>
	<li class="b-reviews-total__item">
		{include $templates.'/part/core/stars.latte', class=>'b-reviews-total__stars', stars=>1, type=>'full'}
		{include $templates.'/part/core/progress.latte', class=>'b-reviews-total__progress', count=>$reviewInfo['countByStars'][1] ?? 0, percentage=>isset($reviewInfo['countByStars'][1]) ? $reviewInfo['countByStars'][1] * 100 / $reviewInfo['count'] : 0}
	</li>
</ul> *}