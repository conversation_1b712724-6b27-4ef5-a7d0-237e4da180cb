{default $class = 'u-mb-md u-mb-2xl@md'}
{default $title = false}
{default $items = []}

<div n:class="b-features, $class">
	<h2 n:if="$title" class="b-features__title h4">{$title}</h2>
	<ul n:if="count($items)" class="b-features__list">
		<li n:foreach="$items as $item" n:if="$item->text ?? false" class="b-features__item item-icon">
			{('check-circle')|icon, 'item-icon__icon'}
			<span class="item-icon__text">
				{$item->text}
			</span>
		</li>
	</ul>
</div>