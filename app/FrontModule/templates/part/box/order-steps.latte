{default $class = false}
{default $currentStep = 1}

<div n:class="b-order-steps, $class">
	<ol class="b-order-steps__list">
		{define stepItem}
			{php $isPrevious = $step < $currentStep}
			{php $hasLink = $step <= $currentStep}

			<li n:class="b-order-steps__item, $link && $hasLink ? b-order-steps__item--w-link, $isPrevious ? b-order-steps__item--prev">
				<a n:tag="$link && $hasLink ? 'a' : 'span'"{if $link && $hasLink} href="{plink $link}"{/if} n:class="b-order-steps__btn, btn, prerender">
					<span class="btn__text">
						<span class="btn__inner">
							{if $isPrevious}{('check')|icon, 'btn__icon'}{/if}
							{_$lang}
						</span>
					</span>
				</a>
			</li>
		{/define}

		{include stepItem, step: 1, lang: 'shopping_basket', link: $pages->cart}
		{include stepItem, step: 2, lang: 'shipping_payment', link: $pages->step1}
		{include stepItem, step: 3, lang: 'personal_info', link: $pages->step2}
		{* {include stepItem, step: 4, lang: 'order_success', link: $pages->step3} *}
	</ol>
</div>
