{snippet benefits}
	{default $class = false}
	{if $userEntity !== null && ($userEntity->isClubMember ?? false)}{*	TODO CLUB IDENTIFICATION*}
		<div n:class="b-product-benefits, $class">
			<ul class="b-product-benefits__list grid grid--x-xs">
				<li class="grid__cell size--auto">
					<span class="item-icon">
						{if $product->hasFreeTransport() || ($transitFreeLowestLPrice !== null && $transitFreeLowestLPrice < $productDto->priceInfoSellingPrice->getAmount()->toFloat())}
							{('car')|icon, 'item-icon__icon'}
							<span class="item-icon__text">{_"product_benefit_free_delivery"}</span>
						{elseif $transitFreeLowestLPrice}
							{capture $benefit}
							{_"product_benefit_free_delivery_from"}
						{/capture}
							{('car')|icon, 'item-icon__icon'}
							<span class="item-icon__text">{$benefit|replace:'%price', (string)$transitFreeLowestLPrice . ' ' . $currencySymbol}</span>
						{/if}
					</span>
				</li>
				<li class="grid__cell size--auto">
					<span class="item-icon">
						{('box')|icon, 'item-icon__icon'}
						 <span class="item-icon__text">{_"product_benefit_for_regular_buys"}</span>
					</span>
				</li>
				<li class="grid__cell size--auto" n:if="isset($clubBonusTexts)">
					<span class="item-icon">
						{('delivery')|icon, 'item-icon__icon'}
						<span class="item-icon__text">{translate}{$clubBonusTexts[array_rand($clubBonusTexts)]}{/translate}</span>
					</span>
				</li>
				<li class="grid__cell size--auto">
					<span class="item-icon">
						<img src="/static/img/illust/certificates/heureka.png" alt="" loading="lazy" class="item-icon__icon">
						<span class="item-icon__text">{_"product_benefit_satisfaction"}</span>
					</span>
				</li>
			</ul>
		</div>
	{elseif $userEntity !== null && !($userEntity->isClubMember ?? false)}
		<div n:class="b-product-benefits, $class">
			<ul class="b-product-benefits__list grid grid--x-xs">
				<li class="grid__cell size--auto">
					<span class="item-icon">
						{if $product->hasFreeTransport() || ($transitFreeLowestLPrice !== null && $transitFreeLowestLPrice < $productDto->priceInfoSellingPrice->getAmount()->toFloat())}
							{('car')|icon, 'item-icon__icon'}
							<span class="item-icon__text">{_"product_benefit_free_delivery"}</span>
						{elseif $transitFreeLowestLPrice}
							{capture $benefit}
							{_"product_benefit_free_delivery_from"}
						{/capture}
							{('car')|icon, 'item-icon__icon'}
							<span class="item-icon__text">{$benefit|replace:'%price', (string)$transitFreeLowestLPrice . ' ' . $currencySymbol}</span>
						{/if}
					</span>
				</li>
				<li class="grid__cell size--auto">
					<span class="item-icon">
						{('box')|icon, 'item-icon__icon'}
						<span class="item-icon__text">{_"product_benefit_price_club"}</span>
					</span>
				</li>
				<li class="grid__cell size--auto">
					<span class="item-icon">
						{('delivery')|icon, 'item-icon__icon'}
						{if ($deliveryInDays = $product->getDeliveryInDays($mutation, $state, $priceLevel, \App\Model\Currency\CurrencyHelper::getCurrency())) !== null && $deliveryInDays <= 3}
							<span class="item-icon__text">{_"product_benefit_delivery_quick"}</span>
						{else}
							<span class="item-icon__text">{_"product_benefit_delivery_exchange_free"}</span>
						{/if}
					</span>
				</li>
				<li class="grid__cell size--auto">
					<span class="item-icon">
						<img src="/static/img/illust/certificates/heureka.png" alt="" loading="lazy" class="item-icon__icon">
						<span class="item-icon__text">{_"product_benefit_satisfaction"}</span>
					</span>
				</li>
			</ul>
		</div>

	{else}
		<div n:class="b-product-benefits, $class">
			<ul class="b-product-benefits__list grid grid--x-xs">
				<li class="grid__cell size--auto">
					<span class="item-icon">
						{if $product->hasFreeTransport() || ($transitFreeLowestLPrice !== null && $transitFreeLowestLPrice < $productDto->priceInfoSellingPrice->getAmount()->toFloat())}
							{('car')|icon, 'item-icon__icon'}
							<span class="item-icon__text">{_"product_benefit_free_delivery"}</span>
						{elseif $transitFreeLowestLPrice}
							{capture $benefit}
								{_"product_benefit_free_delivery_from"}
							{/capture}
							{('car')|icon, 'item-icon__icon'}
							<span class="item-icon__text">{$benefit|replace:'%price', (string)$transitFreeLowestLPrice . ' ' . $currencySymbol}</span>
						{/if}
					</span>
				</li>
				<li class="grid__cell size--auto">
					<span class="item-icon">
						{('box')|icon, 'item-icon__icon'}
						<span class="item-icon__text">{_"product_benefit_gifts"}</span>
					</span>
				</li>
				<li class="grid__cell size--auto">
					<span class="item-icon">
						{('delivery')|icon, 'item-icon__icon'}
						<span class="item-icon__text">{_"product_benefit_deal"}</span>
					</span>
				</li>
				<li class="grid__cell size--auto">
					<span class="item-icon">
						<img src="/static/img/illust/certificates/heureka.png" alt="" loading="lazy" class="item-icon__icon">
						<span class="item-icon__text">{_"product_benefit_satisfaction"}</span>
					</span>
				</li>
			</ul>
		</div>
	{/if}
{/snippet}
