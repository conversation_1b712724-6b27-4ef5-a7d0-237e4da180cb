<div class="b-payment-instructions tw-grid tw-grid-cols-[minmax(10.4rem,_1fr)_3fr] tw-items-start tw-gap-[1.2rem] md:tw-gap-[0.8rem_4rem]">
	<p class="u-mb-0 tw-bg-white tw-rounded-md md:tw-rounded-xl tw-p-[0.8rem] md:tw-p-[1.2rem] md:tw-row-span-3">
		<img n:if="($qrCode = $order->payment->paymentMethod->getPaymentMethod()->getQrCode($order)) !== null" src="{$qrCode|nocheck}" alt="" loading="lazy" width="207" height="207">
	</p>
	<div class="u-mb-last-0">
		<p class="h4 tw-mb-[0.8rem]">
			{_"payment_instructions_title"|noescape|replace:'%price', ($order->getTotalPriceWithDeliveryVat()|money)}
		</p>
		<p class="md:tw-text-[1.5rem]">
			{_"payment_instructions_subtitle"}
		</p>
	</div>

	{var $paymentAccount = $order->getPaymentAccount() ?? new \stdClass()}
	<div class="max-md:tw-col-end-3 max-md:tw-col-start-1 u-mb-last-0">
		<table class="tw-mb-[2.8rem]">
			{define #copy}
				{default $text = false}
				{default $textToCopy = $text}

				<button type="button" n:if="$text" class="b-payment-instructions__copy item-icon as-link tw-font-bold" data-controller="copy" data-copy-content-value="{$textToCopy}" data-action="copy#copy">
					<span class="item-icon__text">{$text}</span>
					{('copy')|icon, 'item-icon__icon'}
					{('check')|icon, 'item-icon__icon'}
				</button>
			{/define}

			{if isset($paymentAccount->bban) && $paymentAccount->bban !== ""}
				<tr>
					<td>{_"account_number"}</td>
					<td>{include #copy, text: $paymentAccount->bban}</td>
				</tr>
			{else}
				<tr>
					<td>{_"account_iban"}</td>
					<td>{include #copy, text: $paymentAccount?->iban ?? '-'}</td>
				</tr>
				<tr>
					<td>{_"account_swift"}</td>
					<td>{include #copy, text: $paymentAccount?->swift ?? '-'}</td>
				</tr>
			{/if}
			<tr>
				<td class="tw-w-[12.5rem] md:tw-w-[14rem]">{_"variable_symbol"}</td>
				<td>{include #copy, text: $order->payment->information->variableSymbol}</td>
			</tr>
			<tr>
				<td>{_"price"}</td>
				<td>{include #copy, text: ($order->getTotalPriceWithDeliveryVat()|money), textToCopy: $order->getTotalPriceWithDeliveryVat()->getAmount()}</td> {* copy only amount *}
			</tr>
		</table>

		<p class="h4 tw-mb-[0.8rem] tw-mt-0">
			{_"payment_instructions_title_gateway"}
		</p>
		<p class="md:tw-text-[1.5rem] tw-mb-[2rem]">
			{_"payment_instructions_subtitle_gateway"|noescape|replace:'%price', ($order->getTotalPriceWithDeliveryVat()|money)}
		</p>
		<p>
			<a n:href="ChangeToCardPayment!, orderHash: $order->hash, redirectToGate: true" class="btn btn--xl">
				<span class="btn__text">
					{_"btn_pay_card"}
				</span>
			</a>
		</p>
	</div>
</div>
