{default $class = false}
{default $cf = $pages->blog->cf->locked ?? false}

<div n:class="b-locked, $class">
	<div class="tw-relative tw-h-[32rem] tw-overflow-hidden after:tw-content-[''] after:tw-absolute after:tw-inset-0 after:tw-bg-gradient-to-b after:tw-from-transparent after:tw-to-white after:tw-from-0% after:tw-to-100%">
		{block content}{/block}
	</div>
	<div class="tw-bg-yellow-50 tw-p-[2.4rem] md:tw-p-[6rem] tw-rounded-md md:tw-rounded-xl u-ta-c">
		<div class="u-maw-6-12 u-mx-auto">
			<p class="tw-mb-[0.8rem] md:tw-mb-[1.2rem]">
				{('crown')|icon, 'u-d-b u-mx-auto tw-w-[5.2rem] tw-mb-[-0.8rem]'}
				<span class="flag flag--yellow-gradient">{_"flag_premium"}</span>
			</p>
			<h2 n:if="$cf->title ?? false" class="u-mt-0 tw-mb-[0.8rem] md:tw-mb-[1.2rem]">
				{$cf->title}
			</h2>
			<p n:if="$cf->text ?? false" class="tw-mb-[2rem] md:tw-mb-[2.4rem]">
				{$cf->text|texy|noescape}
			</p>
			<p class="tw-flex tw-flex-wrap tw-gap-[0.8rem]: tw-gap-[1.2rem] tw-justify-center tw-mb-[2rem] md:tw-mb-[2.4rem]">
				<a href="{plink $pages->userLogin}" class="b-locked__btn btn btn--bd btn--xl">
					<span class="btn__text">
						{_"btn_login"}
					</span>
				</a>
				<a href="{plink $pages->registration}" class="b-locked__btn btn btn--secondary btn--xl">
					<span class="btn__text">
						{_"btn_register2"}
					</span>
				</a>
			</p>
			<div n:if="$cf->benefits ?? false" class="tw-bg-white tw-p-[2rem_2.4rem] md:tw-p-[4rem] tw-rounded-md md:tw-rounded-xl u-maw-4-12 u-mx-auto tw-mb-[1.8rem] md:tw-mb-[3.2rem]">
				<div class="b-locked__content u-maw-3-12 u-mx-auto u-ta-l u-mb-last-0">
					{$cf->benefits|noescape}
				</div>
			</div>
			<p class="u-mb-0">
				<a href="#share" class="btn btn--gray btn--bd" data-modal='{"modalClass": "b-modal--4-12"}'>
					<span class="btn__text">
						{_"btn_share_article"}
					</span>
				</a>
			</p>
			<div class="u-js-hide">
				<div id="share">
					<div class="tw-px-[2.4rem] md:tw-px-[6rem]">
						<h2 class="h3">
							{_"title_share_article"}
						</h2>
						<p class="u-mb-0 u-mb-last-0">
							<a href="https://www.facebook.com/sharer/sharer.php?u={$mutation->domain}/{$object->alias}" class="b-locked__btn btn btn--gray btn--bd btn--lg btn--block tw-mb-[0.8rem]" target="_blank" rel="noopener noreferrer">
								<span class="btn__text">
									<span class="btn__inner">
										{('fb')|icon, 'btn__icon'}
										{_"facebook"}
									</span>
								</span>
							</a>
							<a href="mailto:?subject={$object->name}&body={$mutation->domain}/{$object->alias}" class="b-locked__btn btn btn--gray btn--bd btn--lg btn--block tw-mb-[0.8rem]" target="_blank" rel="noopener noreferrer">
								<span class="btn__text">
									<span class="btn__inner">
										{('envelope-outline')|icon, 'btn__icon'}
										{_"email"}
									</span>
								</span>
							</a>
							<a href="https://api.whatsapp.com/send?text={$mutation->domain}/{$object->alias}" class="b-locked__btn btn btn--gray btn--bd btn--lg btn--block tw-mb-[0.8rem]" target="_blank" rel="noopener noreferrer">
								<span class="btn__text">
									<span class="btn__inner">
										{('whatsapp')|icon, 'btn__icon tw-text-[#27D045]'}
										{_"whatsapp"}
									</span>
								</span>
							</a>
							<a href="https://x.com/share?url={$mutation->domain}/{$object->alias}" class="b-locked__btn btn btn--gray btn--bd btn--lg btn--block tw-mb-[0.8rem]" target="_blank" rel="noopener noreferrer">
								<span class="btn__text">
									<span class="btn__inner">
										{('x')|icon, 'btn__icon tw-text-[#1C2C3E]'}
										{_"twitter"}
									</span>
								</span>
							</a>
							<button type="button" class="b-locked__btn b-locked__btn--copy btn btn--gray btn--bd btn--lg btn--block tw-mb-[0.8rem]" data-controller="copy" data-action="click->copy#copy" data-copy-content-value="{$mutation->domain}/{$object->alias}">
								<span class="btn__text">
									<span class="btn__inner">
										{('link')|icon, 'btn__icon'}
										{('check')|icon, 'btn__icon'}
										{_"copy_link"}
									</span>
								</span>
							</button>
						</p>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>