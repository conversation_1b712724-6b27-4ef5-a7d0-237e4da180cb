{default $class = false}
{default $poster = false}
{default $imgClass = false}
{default $link = false}
{default $ratio = '16-9'}
{default $posterSize = 'md-16-9'}
{default $maxSize = 600}


<span n:if="$link" n:class="b-video, $class">
	{if $poster}
		<img n:class="b-video__img, img, 'img--' . $ratio, $imgClass" src="{$poster->getSize($posterSize)->src}" alt="" loading="lazy">
	{elseif strpos($link, 'youtube')}
		{php $urlObject =  new \Nette\Http\Url($link)}
		{php $id = $urlObject->getQueryParameter('v')}
		{include $templates.'/part/core/video-thumb.latte', class: 'b-video__img img img--' . $ratio . ' ' . $imgClass, id: $id}
	{elseif strpos($link, 'vimeo')}
		{php $id = str_replace('/', '', str_replace('https://vimeo.com/', '', $link))}
		<img n:class="b-video__img, img, 'img--' . $ratio, $imgClass" src="https://vumbnail.com/{$id}.jpg" alt="" loading="lazy">
	{else}
		<img n:class="b-video__img, img, 'img--' . $ratio, $imgClass" src="/static/img/illust/noimg.svg" alt="" loading="lazy">
	{/if}

	{if strpos($link, 'youtube')}
		<span class="play play--youtube">{('play-yt')|icon, 'play__icon'}</span>
	{else}
		<span class="play play--other">{('play')|icon, 'play__icon'}</span>
	{/if}
</span>