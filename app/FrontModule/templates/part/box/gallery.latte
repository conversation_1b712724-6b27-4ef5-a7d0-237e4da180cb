{default $class = 'u-mb-md u-mb-2xl@md'}
{default $images = []}
{default $content = false}

{* todo modal style without nav *}

<div n:class="b-gallery, $class, u-maw-11-12, u-mx-auto">
	<div class="b-gallery__grid">
		<div class="b-gallery__content u-mb-last-0">
			{$content|noescape}
		</div>
		<p class="b-gallery__imgs">
			{foreach $images as $image}
				{breakIf $iterator->counter == 6}

				{if $iterator->counter == 1}
					{php $srcsets = ['50vw', '33.33vw', '828px']}
				{elseif $iterator->counter == 2}
					{php $srcsets = ['50vw', '33.33vw', '456px']}
				{elseif $iterator->counter == 3}
					{php $srcsets = ['100vw', '33.33vw', '522px']}
				{else}
					{php $srcsets = ['50vw', '33.33vw', '456px']}
				{/if}

				<a class="b-gallery__link" href="{$image->getSize('xl')->src}" data-modal='{"gallery": "gallery"}'>
					<img class="b-gallery__img" srcset="
							{$image->getSize('sm')->src} 320w,
							{$image->getSize('md')->src} 560w,
							{$image->getSize('lg')->src} 750w,
							{$image->getSize('xl')->src} 1400w"
						sizes="(max-width: 1000px) {$srcsets[0]},
								(max-width: 1400px) {$srcsets[1]},
								{$srcsets[2]}"
						src="{$image->getSize('xl')->src}"
						alt="{$image->getAlt($mutation)}" loading="lazy">
				</a>
			{/foreach}
		</p>
	</div>
	<p n:ifcontent class="b-gallery__more u-ta-c">
		{foreach $images as $image}
			{if $iterator->counter == 6}
				<a href="{$image->getSize('xl')->src}" class="btn btn--bd" data-modal='{"gallery": "gallery"}'>
					<span class="btn__text">
						<span class="btn__inner">
							{_"btn_show_all_photos"}
							{('angle-down')|icon, 'btn__icon'}
						</span>
					</span>
				</a>
			{elseif $iterator->counter > 6}
				<a class="b-gallery__link u-js-hide" href="{$image->getSize('xl')->src}" data-modal='{"gallery": "gallery"}'>
					<img class="b-gallery__img" src="{$image->getSize('xl')->src}" alt="{$image->getAlt($mutation)}" loading="lazy">
				</a>
			{/if}
		{/foreach}
	</p>
</div>