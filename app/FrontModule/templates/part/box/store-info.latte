{default $class = 'u-mb-md u-mb-2xl@md'}

<div n:class="b-store-info, $class">
	<div class="b-store-info__grid grid">
		<div class="b-store-info__cell b-store-info__cell--main grid__cell">
			<div class="b-store-info__main u-mb-last-0">
				<h2 class="b-store-info__title">
					{_"title_store_info"}
				</h2>
				<p class="b-store-info__contacts">
					<span n:if="isset($object->cf->store_info->address)" class="b-store-info__contact item-icon">
						{('pin-outline')|icon, 'item-icon__icon'}
						<span class="item-icon__text">
							<b>
								{$object->cf->store_info->address|texy|noescape}
							</b>
							<a href="https://www.google.com/maps/place/{$object->cf->store_info->address|webalize}" target="_blank" rel="noopener noreferrer">{_"navigate"}</a>
						</span>
					</span>
					<span n:if="isset($object->cf->store_info->hours)" class="b-store-info__contact item-icon">
						{('time')|icon, 'item-icon__icon'}
						<span class="item-icon__text">
							{_"contact_operating_hours"}:
							<b class="u-d-b">{$object->cf->store_info->hours}</b>
						</span>
					</span>
				</p>

				{php $message = $object->cf->store_info->message ?? false}
				<div n:if="$message" class="b-store-info__msg message message--warning message--lg">
					<div n:if="$message->emoji ?? false" class="message__emoji">{$message->emoji}</div>
					<div n:if="$message->text ?? false" class="message__content u-mb-last-0">
						{$message->text|noescape}
					</div>
				</div>

				{php $transport = $object->cf->store_info->transport ?? false}
				<p n:if="$transport" class="b-store-info__msg b-store-info__msg--transport message message--lg">
					<span n:if="$transport->metro ?? false" class="item-icon">
						{('metro')|icon, 'item-icon__icon'}
						<span class="item-icon__text">
							{$transport->metro|texy|noescape}
						</span>
					</span>
					<span n:if="$transport->bus ?? false" class="item-icon">
						{('bus')|icon, 'item-icon__icon'}
						<span class="item-icon__text">
							{$transport->bus|texy|noescape}
						</span>
					</span>
					<span n:if="$transport->tram ?? false" class="item-icon">
						{('tram')|icon, 'item-icon__icon'}
						<span class="item-icon__text">
							{$transport->tram|texy|noescape}
						</span>
					</span>
					<span n:if="$transport->parking ?? false" class="item-icon">
						{('parking')|icon, 'item-icon__icon'}
						<span class="item-icon__text">
							{$transport->parking|texy|noescape}
						</span>
					</span>
				</p>
			</div>
		</div>
		<div class="b-store-info__cell b-store-info__cell--map grid__cell">
			{php $map = $object->cf->store_info->map ?? false}
			<div n:if="($map->lat ?? false) && ($map->lng ?? false)" class="b-store-info__map is-loading" data-controller="gmap" data-gmap-markers-value='[{"position": {"lat": {$map->lat}, "lng": {$map->lng}}}]'></div>
		</div>
		<div class="b-store-info__cell b-store-info__cell--photo grid__cell">
			{php $img = $object->cf->store_info->images->fetch()}
			<p n:if="$img" class="b-store-info__img u-mb-0">
				<img class="img" src="{$img->getSize('lg')->src}" alt="{$img->getAlt($mutation)}" fetchpriority="high">
			</p>
		</div>
	</div>
</div>