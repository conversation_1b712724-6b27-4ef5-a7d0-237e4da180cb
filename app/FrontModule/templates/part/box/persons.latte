{default $class = 'u-mb-md u-mb-xl@md'}
{default $persons = false}

<div n:class="b-persons, $class, u-maw-8-12, u-mx-auto">
	<div class="b-persons__grid grid">
		<div n:foreach="$persons as $person" class="b-persons__cell grid__cell size--4-12@md">
			{php $img = isset($person->image) ? $person->image->getEntity() ?? false : false}
			{include $templates.'/part/box/person.latte', class: 'b-person--horizontal', src: $img->getSize('md')->src, alt: $img->getAlt($mutation), name: $person->name ?? false, position: $person->position ?? false}
		</div>
	</div>
</div>