{default $class = 'u-mb-md u-mb-2xl@md'}
{default $cf = $object->cf->join ?? false}

<div n:if="$cf" n:class="b-join, $class">
	<div class="u-maw-8-12 u-mx-auto u-mb-last-0">
		<h2 n:if="$cf->title ?? false">
			{$cf->title}
		</h2>
		<p n:if="$cf->annot" class="u-mb-xs u-mb-sm@md">
			{$cf->annot}
		</p>
		<ol n:if="count($cf->usp ?? [])" class="b-usp__list u-fw-b u-mb-xs u-mb-sm@md">
			<li n:foreach="$cf->usp as $usp" n:if="$usp->text ?? false" class="b-usp__item">
				<div class="b-usp__box">
					{$usp->text}
				</div>
			</li>
		</ol>
		<p>
			<a href="#positions" class="btn btn--lg btn--secondary">
				<span class="btn__text">
					{_"open_positions"}
				</span>
			</a>
		</p>
	</div>
</div>