{default $class = 'u-mb-0'}
{default $link = false}
{default $images = false}
{default $slot = 0}

<p n:if="$images" n:class="$class">
	{php $type = $link->toggle ?? false}
	{php $page = isset($link->systemHref) && isset($link->systemHref->page) ? $link->systemHref->page->getEntity() ?? false : false}
	{php $hrefName = ($link->systemHref??->hrefName ?? false) ?: ($link->customHref??->hrefName ?? false)}
	{php $href = $link->customHref??->href ?? false}
	{if $page}{capture $linkText}{plink $page}{/capture}{/if}
	{php $link = $page ? $linkText->__toString() : $href}

	{if $type == 'systemHref' && $page}
		{php $text = $hrefName ? $hrefName : $page->nameAnchor}
	{else}
		{php $text = $hrefName}
	{/if}

	{var $viewportId = $presenter->fireEventViewPromotion($text, 'content_' . $slot)}

	{capture $pictureTag}
		{php $imageDesktop = isset($images->desktop) ? $images->desktop->getEntity() ?? false : false}
		{php $imageTablet = isset($images->tablet) ? $images->tablet->getEntity() ?? false : false}
		{php $imageMobile = isset($images->mobile) ? $images->mobile->getEntity() ?? false : false}

		<picture n:if="$imageDesktop" class="u-round" data-controller="gtm-viewport" data-gtm-viewport-script-id-value="{$viewportId}">
			<source n:if="$imageDesktop" media="(min-width: 750px)" srcset="{$imageDesktop->getSize('product_bnr_desktop')->src}" width="1400" height="180">
			<img class="u-round" src="{if $imageMobile}{$imageMobile->getSize('product_bnr_mobile')->src}{else}{$imageDesktop->getSize('product_bnr_mobile')->src}{/if}" alt="{$imageDesktop->getAlt($mutation)}" loading="lazy" width="750" height="362">
		</picture>
	{/capture}

	{if $link}
		<a href="{$link}"{if $type == 'customHref'} target="_blank" rel="noopener noreferrer"{/if} data-list-id="{$viewportId}" data-list-name="{$text}" class="bnr-click">{$pictureTag}</a>
	{else}
		{$pictureTag}
	{/if}
</p>
