{default $class = false}
{default $titleTag = 'h2'}

<article n:class="b-article, $class">
	<p class="b-article__img">
		{* {if $c->getFirstImage() !== null}
			{php $img = $c->getFirstImage()->getSize('md')}
			<img src="{$img->src}" alt="" width="{$img->width}" height="{$img->height}" loading="lazy">
		{else} *}
			<img class="img img--16-9" src="/static/img/illust/noimg.svg" alt="" loading="lazy">
		{* {/if} *}
		{* {include $templates.'/part/core/badge.latte', class: 'b-product__badge', badge: $variant->cf->badge ?? false} *}
		<span class="b-article__badge badge badge--yellow">{_"premium_badge"|noescape}</span>
	</p>
	<div class="b-article__content u-mb-last-0">
		<h2 n:tag="$titleTag" class="b-article__title h3">
			<a href="#" class="b-article__link link-mask__link">
				Název článku na dva řádky, u kterého se nevleze celý nadpis.
			</a>
			{* <a href="{plink $c}" class="b-article__link link-mask__link">
				{$c->nameTitle}
			</a> *}
		</h2>
		<p {*n:if="$c->annotation"*} class="b-article__desc">
			Minimum pro začínajícího pilota. K nákupu dronů na DronPro od nás zdarma.
			{* {$c->annotation|texy|noescape} *}
		</p>
	</div>
</article>