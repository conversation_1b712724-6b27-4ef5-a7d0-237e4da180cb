{default $class = false}
{default $isLg = str_contains($class, 'b-compare-product--lg')}
{default $dummyLongText = false}

<article n:class="b-compare-product, $class, 'tw-flex tw-flex-col tw-p-[1.2rem_1.6rem] tw-border-solid tw-border-[0.1rem] tw-border-tile tw-rounded-lg link-mask tw-h-full', $isLg ? 'md:tw-p-[2.4rem] md:tw-rounded-xl'">
	<div n:class="'tw-flex tw-flex-1 tw-gap-[0_1.2rem]', $isLg ? 'md:tw-flex-col md:tw-gap-0'">
		<p n:class="'tw-mb-[0] tw-max-w-[7.6rem]', $isLg ? 'md:tw-max-w-[15rem] md:tw-mx-auto md:tw-mb-[1.2rem]'">
			<img class="img img--4-3" src="/static/img/illust/noimg.svg" alt="" loading="lazy">
		</p>
		<div n:class="'u-mb-last-0 tw-mb-[0.4rem] tw-flex-1 tw-flex tw-flex-col', $isLg ? 'md:tw-mb-[1.6rem]'">
			<h3 n:class="'tw-mb-[0] tw-mt-0 tw-text-[1.3rem] tw-leading-[1.4]', $isLg ? 'md:tw-text-[1.6rem] md:tw-mb-[0.8rem] md:tw-leading-[1.6]'">
				<a href="#" class="b-compare-product__link link-mask__link tw-no-underline tw-line-clamp-2">
					{if $dummyLongText}
						Lorem ipsum dolor sit amet
					{else}
						Lorem ipsum
					{/if}
				</a>
			</h3>
			<p n:class="'tw-mb-[0] tw-text-[1.2rem] tw-leading-[1.4] tw-line-clamp-2', $isLg ? 'md:tw-text-[1.3rem] md:tw-mb-[1.2rem] md:tw-leading-[1.6]'">
				{if $dummyLongText}
					Lorem ipsum dolor sit amet consectetur adipisicing elit. Expedita neque libero fugiat, tempora cumque repellat eaque deserunt iure id facere!
				{else}
					Definice varianty (velikost, barva, apod.)
				{/if}
			</p>
			<p n:class="'availability availability--available u-fw-b tw-text-[1.2rem]', $isLg ? 'md:tw-mt-auto md:tw-text-[1.3rem]'">
				Skladem
			</p>
		</div>
	</div>
	{* {include $templates.'/part/core/availability.latte', class: 'b-product__availability', variant: $variant} *}
	<div class="b-compare-product__bottom tw-flex tw-justify-between tw-mb-0">
		<p class="tw-mb-0">
			<a href="#" class="b-compare-product__link tw-flex tw-items-center tw-gap-[1rem] link-mask__unmask tw-no-underline tw-text-[1.3rem]">
				<span class="btn btn--icon btn--gray btn--bd">
					<span class="btn__text">
						{('close')|icon, 'btn__icon'}
					</span>
				</span>
				{_"btn_compare_remove"}
			</a>
		</p>
		<form action="?">
			<button type="submit" class="btn link-mask__unmask">
				<span class="btn__text">
					{_"btn_buy"}
				</span>
			</button>
		</form>
	</div>
</article>
