{default $class = false}

{if isset($shoppingCart)}
	{var $order = $shoppingCart}
{/if}

{default $products = $order->getProducts()}
{default $classEvents = $order->getClassEvents()}
{default $vouchers = $order->getVouchers()}
{default $gifts = $order->getGifts()}
{default App\Model\Orm\Order\Delivery\OrderDelivery $delivery = $order->getDelivery()}
{default App\Model\Orm\Order\Payment\OrderPayment $payment = $order->getPayment()}
{default $totalPriceWithDeliveryVat = App\Model\Orm\Price::from($order->getTotalPriceWithDeliveryVat(precision: 2))->as<PERSON>oney()}
{default $totalPriceWithDelivery = App\Model\Orm\Price::from($order->getTotalPriceWithDelivery(precision: 2))->as<PERSON>oney()}
{* {php $showMore = count($products) >= 5} *}

<div n:class="b-cart-summary, $class" data-controller="toggle-class">
	<div class="b-cart-summary__header">
		<h2 class="h5 u-mb-0">
			{_"cart_summary_title"}
		</h2>
		<p class="b-cart-summary__toggle">
			<button type="button" class="as-link item-icon" data-action="toggle-class#toggle">
				<span class="item-icon__text">
					<span class="b-cart-summary__toggle-text">{_"btn_show"}</span>
					<span class="b-cart-summary__toggle-text">{_"btn_hide"}</span>
				</span>
				{('angle-down')|icon, 'item-icon__icon'}
			</button>
		</p>
	</div>

	<div class="b-cart-summary__box">
		<table class="b-cart-summary__table b-cart-summary__table--main">
			<tbody class="b-cart-summary__body">
				{foreach $products as $product}{include #tableRowProduct, productItem: $product}{/foreach}
				{* {foreach $promotions as $promotion}{include #tableRowPromotion, promotionItem: $promotion}{/foreach} *}
				{* {foreach $gifts as $gift}{include #tableRowGift, giftItem: $gift}{/foreach} *}
				{foreach $vouchers as $voucher}{include #tableRowVoucher, giftItem: $voucher}{/foreach}
			</tbody>
		</table>

		{* Doprava a platba *}
		<table class="b-cart-summary__table b-cart-summary__table--delivery">
			<tbody class="b-cart-summary__body">
				<tr class="b-cart-summary__row">
					<td class="b-cart-summary__icon">
						{('truck')|icon}
					</td>
					<td class="b-cart-summary__desc">
						{if $delivery ?? false}
							{$delivery->getName()}
							<span n:ifcontent class="b-cart-summary__address">
								{$delivery->getAddress()}
							</span>
						{else}
							<i class="u-c-help">{_"pick_delivery"}</i>
						{/if}
					</td>
					<td class="b-cart-summary__price u-fw-b">
						{if $delivery ?? false}
							{if !$delivery->unitPriceVat->isZero()}{$delivery->unitPriceVat|money}{else}{_"free"}{/if}
						{/if}
					</td>
				</tr>
				<tr class="b-cart-summary__row">
					<td class="b-cart-summary__icon">
						{('money')|icon}
					</td>
					<td class="b-cart-summary__desc">
						{if $payment ?? false}
							{$payment->getName()}
						{else}
							<i class="u-c-help">{_"pick_payment"}</i>
						{/if}
					</td>
					<td class="b-cart-summary__price u-fw-b">
						{if $payment ?? false}
							{if !$payment->unitPriceVat->isZero()}{$payment->unitPriceVat|money}{else}{_"free"}{/if}
						{/if}
					</td>
				</tr>
			</tbody>
		</table>
	</div>

	<hr class="b-cart-summary__divider">

	<p class="b-cart-summary__total b-cart-summary__total--vat h5">
		{_"total_price_vat"}
		<b>{$totalPriceWithDeliveryVat|money}</b>
	</p>
	<p class="b-cart-summary__total b-cart-summary__total--novat u-c-help">
		{_"total_price"}
		<span>{$totalPriceWithDelivery|money}</span>
	</p>
</div>


{define #tableRowProduct}
	{varType App\Model\Orm\Order\Product\ProductItem $productItem}
	<tr class="b-cart-summary__row b-cart-summary__row--product">
		{php $product = $productItem->variant->product}
		{varType App\Model\Orm\Order\Product\ProductItem $productItem}
		{var $link = $presenter->link($product, ['v' => $productItem->variant->id])}

		<td class="b-cart-summary__img">
			{php $whiteBg = $product->cf->product?->whiteBackground ?? false}
			<a href="{$link}">
				{* TODO podmínka pro kurzy - neobsahují třídu img--contain (platí u VŠECH obrázků na webu) *}

				{if $product->firstImage}
					{php $img = $product->firstImage->getSize('xs')}
					<img class="img img--4-3 img--contain" src="{$img->src}" alt="{$productItem->getName()}" loading="lazy">
				{else}
					<img class="img img--4-3 img--contain" src="/static/img/illust/noimg-book.svg" alt="" loading="lazy">
				{/if}
			</a>
			{include $templates.'/part/core/type.latte', class: 'b-cart-summary__flag flag--type', product: $product}
		</td>
		<td class="b-cart-summary__desc">
			<a href="{$link}" class="b-cart-summary__link prefetch">{$productItem->amount}&times; {$productItem->getName()}</a>
			{* {include $templates.'/part/core/availability.latte', class: false, showTooltip: false, variant: $productItem->variant} *}
		</td>
		<td class="b-cart-summary__price u-fw-b">{$productItem->totalPriceVat|money}</td>
		{*
		<td class="b-cart-summary__variant">
			S nabíjecí stanicí, brašnou, bateriemi a dalším vybavením navíc
		</td>
		<td class="b-cart-summary__extra">
			<p class="b-cart-summary__extra-item">
				<span class="b-cart-summary__desc">
					<b>+ Pojištění</b> proti krádeži a poškození na 1 rok TODO
				</span>
				<span class="b-cart-summary__price">
					0 Kč
				</span>
			</p>
			<p class="b-cart-summary__extra-item">
				<span class="b-cart-summary__desc">
					<b>+ Dárek</b> k produktu např. školení zdarma TODO
				</span>
				<span class="b-cart-summary__price">
					0 Kč
				</span>
			</p>
		</td>
		*}
	</tr>
{/define}

{define #tableRowGift}
	{varType App\Model\Orm\Order\Gift\GiftItem $giftItem}
	<tr class="b-cart-summary__row b-cart-summary__row--product">
		{var $link = ($product = $giftItem->giftLocalization->gift->product) !== null ? $presenter->link($product) : null}
		<td class="b-cart-summary__img">
			{php $whiteBg = $product->cf->product?->whiteBackground ?? false}
			<a n:tag="$link ? 'a' : 'span'" href="{$link}">
				{if ($image = $giftItem->giftLocalization->getImage()) !== null}
					{php $img = $image->getSize('xs')}
					<img class="img img--4-3 img--contain" src="{$img->src}" alt="{$giftItem->getName()}" loading="lazy">
				{else}
					<img class="img img--4-3 img--contain" src="/static/img/illust/noimg-book.svg" alt="" loading="lazy">
				{/if}
			</a>
		</td>
		<td class="b-cart-summary__desc">
			<a n:tag="$link ? 'a' : 'span'" href="{$link}" class="b-cart-summary__link prefetch">{_cart_item_gift} {$giftItem->amount}&times; {$giftItem->getName()}</a>
			{* {if $product !== null}
				{include $templates.'/part/core/availability.latte', class: false, showTooltip: false, variant: $product->firstVariant}
			{/if} *}
		</td>
		<td class="b-cart-summary__price u-fw-b">{$giftItem->totalPriceVat|money}</td>
	</tr>
{/define}

{define #tableRowPromotion}
	{varType App\Model\Orm\Order\Promotion\PromotionItem $promotionItem}
	<tr class="b-cart-summary__row b-cart-summary__row--product">
		{var $link = null}
		<td class="b-cart-summary__img">
			{php $whiteBg = $product->cf->product?->whiteBackground ?? false}
			<a n:tag="$link ? 'a' : 'span'" href="{$link??''}" n:class="img, img--contain, $whiteBg ? u-bgc-white">
				{*if ($image = $promotionItem->promotionLocalization->getImage()) !== null}
					{php $img = $image->getSize('xs')}
					<img src="{$img->src}" alt="{$giftItem->getName()}" loading="lazy">
				{else*}
					<img src="/static/img/illust/noimg-book.svg" alt="" loading="lazy">
				{* TODO: kelly promotion icon *}
				{*/if*}
			</a>
		</td>
		<td class="b-cart-summary__desc u-mb-last-0">
			<a n:tag="$link ? 'a' : 'span'" href="{$link??''}" class="b-cart-summary__link prefetch u-d-b">{_promotion_cart_item_text} {$promotionItem->getName()}</a>
			{*<span class="type item-icon">
				{('box')|icon, 'item-icon__icon'}
				<span class="item-icon__text">
					{_"gift"}
				</span>
			</span>*} {* TODO: kelly oznaceni ze to je promoce ? maybe *}
		</td>
		<td class="b-cart-summary__price u-fw-b">{$promotionItem->amount}&times; {$promotionItem->unitPriceVat|money}</td>
	</tr>
{/define}

{define #tableRowVoucher}
	<tr class="b-cart-summary__row b-cart-summary__row--voucher">
		<td class="b-cart-summary__desc">
			<b>
				{if in_array($voucher->voucherCode->voucher->type, [App\Model\Orm\Voucher\Voucher::TYPE_AMOUNT, App\Model\Orm\Voucher\Voucher::TYPE_PERCENT])}
					{_"discount_code"}
				{else}
					{_'gift_discount_code'}
				{/if}
			</b>
			"{$voucher->voucherCode->code}"
		</td>
		<td class="b-cart-summary__price u-fw-b">
			{$voucher->totalPriceVat|money}
		</td>
	</tr>
{/define}
