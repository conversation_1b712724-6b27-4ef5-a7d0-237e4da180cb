{default $class = false}
{default $image = false}
{default $link = false}
{default $title = false}
{default $text = false}

<div n:if="$title" n:class="b-tile, $class, link-mask, $link ? b-tile--w-link">
	<p class="b-tile__img img img--rounded">
		{if $image}
			<img src="{$image->getSize('sm')->src}" alt="{$title}" loading="lazy">
		{else}
			<img src="/static/img/illust/noimg.svg" alt="" loading="lazy">
		{/if}
	</p>
	<div class="b-tile__content">
		<h3 class="b-tile__title">
			{if $link}
				<a href="{$link}" class="b-tile__link link-mask__link">
					{$title}
					{('arrow-right')|icon, 'b-tile__arrow'}
				</a>
			{else}
				{$title}
			{/if}
		</h3>
		{$text}
	</div>
</div>