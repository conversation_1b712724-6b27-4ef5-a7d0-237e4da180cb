{default $class = 'tw-mb-[2rem] md:tw-mb-[3.2rem]'}
{default $settings = $object->cf->settings ?? false}

<header n:class="$class, u-maw-8-12, u-mx-auto, 'u-pt-sm u-pt-md@md'">
	<div class="tw-grid md:tw-gap-x-[6rem] tw-items-center tw-grid-cols-[1fr_auto] tw-mb-[0.8rem] md:tw-mb-[2rem]">
		{snippetArea breadcrumbArea}
			{control breadcrumb, [class: 'tw-mb-[0.8rem] md:tw-mb-[2.4rem] tw-col-start-1 tw-col-end-3 md:tw-col-end-1']}
		{/snippetArea}
		{include $templates.'/part/crossroad/tags.latte', class: 'tw-mb-[0.4rem] md:tw-mb-[1rem] tw-col-start-1 tw-col-end-3 md:tw-col-end-1', crossroad: $object->blogTags}
		<h1 class="u-mt-0 tw-leading-[1.5] tw-mb-[0.4rem] md:tw-mb-[1rem] tw-col-start-1 tw-col-end-3 md:tw-col-end-1">{$object->name}</h1>
		<p class="item-icon u-c-help tw-text-[1.3rem] tw-leading-[1.3rem] md:tw-text-[1.4rem] md:tw-leading-[1.6rem] tw-col-start-1 u-mb-0">
			{('time')|icon, 'item-icon__icon'}
			<span class="item-icon__text">
				{$object->readingTime} {_($object->readingTime|plural: "reading_time_1", "reading_time_2", "reading_time_3")}
			</span>
		</p>
		<ul class="u-reset-ul tw-flex tw-items-center md:tw-flex-col tw-gap-[1rem] tw-col-start-2 tw-self-end md:tw-row-start-1 md:tw-row-span-4">
			<li n:foreach="$object->authors as $author">
				{php $img = isset($author->getParent()->cf->settings->avatar) ? $author->getParent()->cf->settings->avatar->getEntity() ?? false : false}
				<a href="#author{$iterator->counter}" class="tw-text-inherit">
					{include $templates.'/part/core/author.latte', class: 'author--article', src: $img ? $img->getSize('xs')->src : false, name: $author->name}
				</a>
			</li>
		</ul>
	</div>

	{include $templates.'/part/box/toc.latte', titleLang: 'toc_title_article'}

	{php $image = isset($object->getParent()->cf->settings->mainImage) ? $object->getParent()->cf->settings->mainImage->getEntity() ?? false : false}
	<p n:if="$image" class="u-mb-0 u-maw-8-12 u-mx-auto">
		<img class="img img--16-9 tw-rounded-lg md:tw-rounded-xl" srcset="
				{$image->getSize('md-16-9')->src} 560w,
				{$image->getSize('lg-16-9')->src} 750w,
				{$image->getSize('xl-16-9')->src} 1400w,
				{$image->getSize('2xl-16-9')->src} 1920w"
			sizes="(max-width: 1000px) 100vw,
					1020px"
			src="{$image->getSize('2xl-16-9')->src}" alt="{$image->getAlt($mutation)}" loading="lazy">
	</p>
</header>
