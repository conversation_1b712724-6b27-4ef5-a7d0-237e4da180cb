{default $class = false}
{php $links = $side->items ?? []}

<div n:if="$side" n:class="b-submenu-side, $class">
	<div>
		<p n:if="$side->title ?? false" class="b-submenu-side__title h4">
			{$side->title}
		</p>
		<ul n:if="count($links)" class="b-submenu-side__list">
			<li n:foreach="$links as $link">
				{php $type = $link->link->toggle}
				{php $page = isset($link->link->systemHref) && isset($link->link->systemHref->page) ? $link->link->systemHref->page->getEntity() ?? false : false}
				{php $href = $link->link->customHref??->href ?? false}
				{php $hrefNameSystem = $link->link->systemHref??->hrefName ?? false}
				{php $hrefNameCustom = $link->link->customHref??->hrefName ?? false}

				{if $type == 'systemHref' && $page}
					<a href="{plink $page}" n:ifcontent class="b-submenu-side__link">
						{if $hrefNameSystem}
							{$hrefNameSystem}
						{else}
							{$page->nameAnchor}
						{/if}
					</a>
				{elseif $type == 'customHref' && $href && $hrefNameCustom}
					<a href="{$href}" class="b-submenu-side__link" target="_blank" rel="noopener noreferrer">
						{$hrefNameCustom}
					</a>
				{/if}
			</li>
		</ul>
	</div>
	<div class="b-submenu-side__divider"></div>
	<a n:if="isset($pages->blog)" href="{plink $pages->blog}" class="b-submenu-side__box">
		<img class="b-submenu-side__decor" src="/static/img/illust/submenu-decor.webp" alt="" loading="lazy" width="154" height="97">
		<div class="u-mb-last-0">
			<img class="b-submenu-side__zone" src="/static/img/illust/dron-zone.svg" alt="" width="141" height="28" loading="lazy">
			<p class="b-submenu-side__text">{_"submenu_side_zone"}</p>
		</div>
		<p class="u-mb-0">
			<span class="b-submenu-side__btn btn btn--icon btn--white">
				<span class="btn__text">
					{('angle-right')|icon}
					<span class="u-vhide">
						{_"btn_more"}
					</span>
				</span>
			</span>
		</p>
	</a>
</div>