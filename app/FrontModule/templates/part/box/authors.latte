{default $class = 'u-mb-sm u-mb-xl@md'}
{default $cf = $object->cf->authors ?? false}

<article n:if="$cf" n:class="b-authors, $class">
	<h2 n:if="$cf->title ?? false" class="b-authors__title">
		{$cf->title}
	</h2>
	<ul n:if="count($cf->items ?? [])" class="grid grid--x-xs@md grid--y-xs@md">
		{foreach $cf->items as $item}
			<li n:if="$item->name ?? false" class="grid__cell size--6-12 size--4-12@sm size--3-12@md size--2-12@xl">
				{php $image = isset($item->image) ? $item->image->getEntity() ?? false : false}
				{php $link = $item->link ?? false}
				{php $href = false}

				{if $link && $link->toggle == 'customHref'}
					{php $href = $link->customHref??->href ?? false}
				{elseif $link}
					{php $page = isset($link->systemHref) && isset($link->systemHref->page) ? $link->systemHref->page->getEntity() : false}
					{capture $href}{plink $page}{/capture}
					{php $href = $href->__toString()}
				{/if}

				{include $templates.'/part/box/tile.latte', title=>$item->name, text=>$item->text ?? false, image=>$image, link=>$href}
			</li>
		{/foreach}
	</ul>
</article>