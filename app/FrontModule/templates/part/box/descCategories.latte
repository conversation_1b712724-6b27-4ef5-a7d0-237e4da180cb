{varType App\PostType\Page\Model\Orm\CatalogTree $object}
{default $class = false}

{if isset($object->cf->sortCategoryDescription) && (!isset($filter->nonRoot) || $filter->nonRoot === false)}
	{var $categoryDesc = $object->cf->sortCategoryDescription}
	{var $categoryDescKey = 'content'.Nette\Utils\Strings::firstUpper($catalogOrder)}
	{ifset $categoryDesc->$categoryDescKey}
		<hr class="u-mb-xs u-mb-md@md">
		{include $templates.'/part/box/content.latte', class=>$class, content=>$categoryDesc->$categoryDescKey}
	{/ifset}
{/if}
