{default $class = false}
{default $cfPerson = $object->cf->contactPerson ?? false}
{default $cfContacts = $pages->contact->cf->contacts ?? false}

<div n:class="b-contact-media, $class">
	<h2 class="b-contact-media__title h4">
		{_"title_contact_media"}
	</h2>
	{php $name = $cfPerson->name ?? false}
	{php $img = isset($cfPerson->image) ? $cfPerson->image->getEntity() ?? false : false}
	{include $templates.'/part/core/person.latte', class: 'b-contact-media__person', img: $img, name: $name}
	<p class="b-contact-media__phone">
		<a href="tel:{$cfContacts->phone|replace:' ',''}" class="b-contact-media__link item-icon">
			{('phone-outline-bold')|icon, 'item-icon__icon'}
			<span class="item-icon__text">
				{_"call"} {$cfContacts->phone}
			</span>
		</a>
	</p>
	<p class="u-mb-0">
		<a href="mailto:{$cfContacts->phone}" class="b-contact-media__link">
			{$cfContacts->mail}
		</a>
	</p>
</div>