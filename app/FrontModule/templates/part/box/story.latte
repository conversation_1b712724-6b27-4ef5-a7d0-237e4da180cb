{default $class = 'u-mb-xs u-mb-xl@md'}
{default $cf = $object->cf->story ?? false}

{if $cf}
	{php $photos = $cf->photos ?? []}
	{php $content = $cf->content ?? false}
	{php $person = $cf->person ?? false}

	<article n:if="$photos" n:class="b-story, $class" data-controller="story">
		<div class="b-story__holder embla__viewport" data-story-target="desktop">
			<div class="b-story__items embla__container">
				<div class="b-story__main box u-mb-last-0" data-story-target="content">
					{include $templates.'/part/box/content.latte', content=>$content, class=>'b-story__content u-mb-xxs u-mb-xs@md'}
					{include $templates.'/part/core/person.latte', person=>$person ?? false, class=>false}
				</div>
				<div class="b-story__photos-holder embla__viewport" data-story-target="mobile">
					<p class="b-story__photos" data-story-target="photos">
						{foreach $photos as $photo}
							<img src="{$photo->getSize('lg')->src}" alt="{$photo->getAlt($mutation)}" loading="lazy">
						{/foreach}
					</p>
				</div>
			</div>
		</div>
		<button class="embla__btn embla__btn--prev" disabled="disabled" type="button" data-action="story#prev" data-story-target="prevButton">
			{('arrow-left')|icon}
			<span class="u-vhide">{_btn_prev}</span>
		</button>
		<button class="embla__btn embla__btn--next" disabled="disabled" type="button" data-action="story#next" data-story-target="nextButton">
			{('arrow-right')|icon}
			<span class="u-vhide">{_btn_next}</span>
		</button>
	</article>
{/if}