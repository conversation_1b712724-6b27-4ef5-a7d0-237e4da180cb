{default $class = false}
{default $showHighlighted = false}
{default $title = $mutation->cf->services->title ?? false}
{default $items = $mutation->cf->services->items ?? []}
{default $itemsHighlighted = $mutation->cf->services->items_highlighted ?? []}
{default $showTitle = count($itemsHighlighted)}

<div n:if="count($items) || (count($itemsHighlighted) && $showHighlighted)" n:class="b-services, $class, count($itemsHighlighted) && $showHighlighted ? 'u-maw-11-12 u-mx-auto'">
	<div n:tag-if="$showHighlighted" class="u-maw-11-12 u-mx-auto">
		<h2 n:if="$title && $showTitle" class="b-services__title u-maw-8-12 u-mx-auto u-mb-sm u-mb-md@md">
			{$title}
		</h2>
		<ul class="b-services__list">
			<li n:if="$showHighlighted" n:foreach="$itemsHighlighted as $item" class="b-services__item b-services__item--highlighted link-mask">
				{php $img = isset($item->image) ? $item->image->getEntity() ?? false : false}
				{php $page = isset($item->page) ? $item->page->getEntity() ?? false : false}

				<p n:if="$img" class="u-mb-0">
					<img class="b-services__img" src="{$img->getSize('md')->src}" alt="" loading="lazy">
				</p>
				<div class="b-services__content u-mb-last-0">
					<p n:if="$item->title ?? false" class="b-services__name h3">
						{$item->title}
					</p>
					<p n:if="$item->text ?? false">
						{$item->text}
					</p>
					<p n:if="($item->btnText ?? false) && $page">
						<a href="{plink $page}" class="btn btn--xs link-mask__link">
							<span class="btn__text">
								{$item->btnText}
							</span>
						</a>
					</p>
				</div>
			</li>
			<li n:foreach="$items as $item" class="b-services__item link-mask">
				{php $img = isset($item->image) ? $item->image->getEntity() ?? false : false}
				{php $page = isset($item->page) ? $item->page->getEntity() ?? false : false}

				<p n:if="$img" class="u-mb-0">
					<img class="b-services__img" src="{$img->getSize('lg')->src}" alt="" loading="lazy">
				</p>
				<div class="b-services__content u-mb-last-0">
					<p n:if="$item->title ?? false" class="b-services__name h4">
						<a n:tag-if="$page" href="{plink $page}" class="b-services__link link-mask__link">
							{$item->title}
						</a>
					</p>
					<p n:if="$item->text ?? false">
						{$item->text}
					</p>
				</div>
			</li>
		</ul>
	</div>
</div>