{default $class = 'u-maw-8-12 u-mx-auto tw-mb-[2rem] md:tw-mb-[4rem]'}

<header n:class="$class, u-mb-last-0">
	{snippetArea breadcrumbArea}
		{control breadcrumb, [class: 'b-header__breadcrumbs u-pt-sm u-pt-md@md tw-mb-[1.6rem] md:tw-mb-[2.4rem]']}
	{/snippetArea}
	<p class="flag flag--gray tw-mb-[1rem]">{_"glossary_of_terms"}</p>
	<h1 class="tw-mt-0 tw-mb-[2rem]">{$seoLink??->name ?? ($object->nameHeading ? $object->nameHeading : $object->name)}</h1>
	{include $templates.'/part/box/toc.latte', class: 'tw-mb-[2rem] md:tw-mb-[3.2rem]', titleLang: 'toc_title_article', annot: $object->annotation ?? false}

	{* TODO: img z postType configu + FE srcset *}
	<p class="tw-overflow-hidden tw-rounded-md md:tw-rounded-xl u-maw-8-12 u-mx-auto">
		<img class="img img--16-9" src="/static/img/illust/noimg.svg" alt="" fetchpriority="high">
	</p>
</header>