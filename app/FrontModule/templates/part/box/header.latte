{default $class = 'u-mb-md u-mb-2xl@md'}
{default $cf = $object->cf->header ?? false}
{default $showMore = false}
{default $extraTocItems = []}
{default $disableDescriprion = false}
{default $annotation = $object->annotation ?? false}
{default $content = $cf->content ?? false}
{default $name = $seoLink??->name ?? ($object->nameHeading ?? $object->name)}

{php $img = isset($cf->image) ? $cf->image->getEntity() ?? false : false}

<header n:class="b-header, $class, $img ? b-header--w-img-v2"> {* b-header--w-img *}
	<p n:if="$img" class="b-header__bg u-mb-0">
		<img srcset="
				{$img->getSize('sm')->src} 320w,
				{$img->getSize('md-2-1')->src} 560w,
				{$img->getSize('lg-2-1')->src} 750w,
				{$img->getSize('xl-2-1')->src} 1400w,
				{$img->getSize('2xl-2-1')->src} 1920w"
			sizes="(max-width: 1920px) 100vw,
					1920px"
			src="{$img->getSize('xl-2-1')->src}"
			alt="" fetchpriority="high">
	</p>
	<div class="b-header__main">
		{snippetArea breadcrumbArea}
			{* {default $cf = $object->cf->header ?? false} *}
			{* {php $img = isset($cf->image) ? $cf->image->getEntity() ?? false : false} *}
			{php $img = false}
			{control breadcrumb, [class: $img ? 'b-header__breadcrumbs m-breadcrumb--inverse' : 'b-header__breadcrumbs u-pt-sm u-pt-md@md']}
		{/snippetArea}
		<h1 class="b-header__title">
			{control seoTools, $name}
		</h1>
	</div>
	<div n:ifcontent class="b-header__content u-mb-last-0">
		{* {snippet categoryDescription} *}
			<div n:if="!$disableDescriprion" n:ifcontent class="b-header__text tw-mb-[1.2rem] md:tw-mb-[1.6rem]" {if $showMore}data-controller="toggle-class clamped"{/if}>
				<div n:ifcontent class="u-mb-last-0" {if $showMore}data-clamped-target="content"{/if}>
					<p n:ifcontent n:if="$annotation">
						{str_replace('<br>', '', ($annotation|texy))|noescape}
					</p>
					{if $content}
						{$content|tables|lazyLoading|obfuscateEmailAddresses|noescape}
					{/if}
				</div>
				<p n:if="$showMore" class="b-header__more u-mb-0">
					<button type="button" class="as-link u-d-n" data-action="toggle-class#toggle" data-clamped-target="btn">
						{_"btn_show_more"}
					</button>
				</p>
			</div>
		{* {/snippet} *}


		{* Extra obsah (přes embed) *}
		<div n:ifcontent class="b-header__extra tw-mb-[1.2rem] md:tw-mb-[1.6rem]">
			{block extra}{/block}
		</div>

		{* Obsah stránky *}
		{include $templates.'/part/box/toc.latte', extraTocItems: $extraTocItems}
	</div>

	{* Dekorace v pravém horním rohu *}
	{php $decor = isset($cf->decor) ? $cf->decor->getEntity() ?? false : false}
	<img n:if="$decor" class="b-header__decor img img--4-3 img--contain" src="{$decor->getSize('md')->src}" alt="" fetchpriority="high">

	<div n:ifcontent class="b-header__side">
		<div n:ifcontent class="b-header__holder">
			{php $extra = $cf->extra ?? false}
			{switch $extra->type ?? false}
				{case 'video'}
					{php $link = $extra->video->link ?? false}
					<p n:if="$link" class="u-mb-0">
						<a href="{$extra->video->link}" class="b-header__video" data-modal>
							{include $templates.'/part/box/video.latte', class: false, maxSize: 950, poster: isset($extra->video->image) ? $extra->video->image->getEntity() ?? false : false, link: $link}
						</a>
					</p>
				{case 'links'}
					{include $templates.'/part/crossroad/links.latte', class: false, cf: $extra->links}
				{default}
					{block side}{/block}
			{/switch}
		</div>
	</div>
</header>
