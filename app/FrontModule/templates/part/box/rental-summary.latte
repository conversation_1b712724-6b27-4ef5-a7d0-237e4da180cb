{default $class = 'tw-mb-[3.2rem]'}

<div n:class="$class, 'tw-rounded-md md:tw-rounded-xl tw-bg-bg tw-p-[2rem_3.6rem_3.6rem]'">
	<h2 class="h5 tw-text-center tw-mb-[1.6rem]">{_"rental_summary_title"}</h2>

	{if $isEmpty}
		<p class="tw-text-center tw-text-placeholder tw-text-[1.4rem] tw-mb-[1.6rem] tw-py-[2rem]">{_"rental_summary_empty"}</p>
	{else}
		<ul class="u-reset-ul tw-grid tw-gap-[0.2rem] tw-grid-cols-[max-content_1fr_max-content] tw-text-[1.4rem]">
			<li n:for="$i = 0; $i < 3; $i++" class="tw-grid tw-grid-cols-subgrid tw-col-span-full">
				<p class="tw-mb-0 tw-bg-white tw-rounded-md tw-p-[1.2rem_1.6rem] tw-grid tw-grid-cols-subgrid tw-col-span-full tw-gap-[0.6rem_1.2rem]">
					<img class="img img--4-3 tw-w-[7.2rem] tw-row-start-1 tw-row-end-3 tw-rounded-sm" src="/static/img/illust/noimg.svg" alt="" loading="lazys">
					<b class="">Ovladač DJI Basic</b>
					<span class="tw-text-right">
						<b>1 346 Kč</b><span class="tw-text-help tw-text-[1.3rem]">/{_"day_plural_1"}</span>
					</span>
					<span class="">
						TODO: count
						{* {include $templates.'/part/form/part/count.latte'} *}
					</span>
					<a href="#" class="tw-text-right tw-self-center">{_"btn_change"}</a>
				</p>
			</li>
		</ul>
	{/if}

	<hr class="tw-mb-[1.6rem]">
	<p class="h5 tw-flex tw-justify-between tw-mt-0 tw-mb-[2rem]">
		<b>{_"rental_total"}</b>
		<b>0 Kč</b>
	</p>
	<p class="tw-mb-0 tw-text-center">
		{if $isEmpty}
			<button type="submit" class="btn btn--secondary btn--lg btn--block"{if $isEmpty} disabled{/if}>
				<span class="btn__text tw-text-left tw-leading-[1.5]">
					<span class="btn__inner">
						Před pokračováním<br> vyberte kategorii
						{('arrow-right-long')|icon, 'btn__icon'}
					</span>
				</span>
			</button>
		{else}
			<button type="submit" class="btn btn--secondary btn--lg btn--block"{if $isEmpty} disabled{/if}>
				<span class="btn__text tw-text-left tw-leading-[1.5]">
					<span class="btn__inner">
						Pokračovat na výběr termínu
						{('arrow-right-long')|icon, 'btn__icon'}
					</span>
				</span>
			</button>
		{/if}
	</p>
</div>