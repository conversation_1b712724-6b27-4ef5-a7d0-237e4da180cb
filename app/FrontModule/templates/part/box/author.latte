<article class="b-author-teaser link-mask">
	<div class="b-author-teaser__img">
		<div class="img img--portrait img--fill">
			{if $c->getFirstImage() !== null}
				{php $img = $c->getFirstImage()->getSize('photo')}
				<img src="{$img->src}" alt="" width="{$img->width}" height="{$img->height}" loading="lazy">
			{else}
				<img src="/static/img/illust/noimg.svg" alt="" width="260" height="308" loading="lazy">
			{/if}
		</div>
	</div>
	<div class="b-author-teaser__content">
		<h3 class="b-author-teaser__title">
			<a n:href="$c" class="b-author-teaser__link link-mask__link">
				{$c->name}
			</a>
			<span class="b-author-teaser__value">
				({$c->blogsPublic->count()})
			</span>
		</h3>
		<p n:ifset="$c->cf->base->position" class="b-author-teaser__desc">
			{$c->cf->base->position}
		</p>
		<p n:ifset="$c->cf->base->annotation" class="b-author-teaser__annot u-mb-0">
			{$c->cf->base->annotation}
		</p>
	</div>
</article>
