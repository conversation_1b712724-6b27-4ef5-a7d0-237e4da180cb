{default $class = 'u-mb-md u-mb-2xl@md'}
{default $cf = $pages->faq->cf->faq ?? false}
{default $limitWidth = true}
{default $title = $cf->title ?? false}
{default $items = $cf->items ?? []}
{default $btn = $cf->btn ?? 'ask'}

<div n:class="b-faq, $class">
	<div n:tag-if="$limitWidth" class="u-maw-8-12 u-mx-auto">
		<div class="b-faq__main">
			<h2 n:if="$title" n:class="b-faq__title">{$title}</h2>
			<ul n:if="count($items)" class="b-faq__list">
				<li n:foreach="$items as $item" class="b-faq__item b-faq__item--question u-mb-last-0">
					{('bubble-ear')|icon, 'b-faq__bubble-ear'}
					<p n:if="$item->question ?? false" class="b-faq__question u-fw-b">
						{$item->question}
						<span class="b-faq__icon">{('question-bubble')|icon}</span>
					</p>
					<p n:if="$item->answer ?? false">
						{$item->answer|texy|noescape}
					</p>
				</li>
			</ul>
		</div>

		{if $btn == 'ask'}
			<p class="b-faq__btn u-pt-sm u-pt-md@md u-mb-0 u-ta-c">
				<b>{_"faq_ask_text"|noescape}</b>
				<a href="#" class="btn btn--bd btn--black" data-modal='{"medium": "fetch"}' data-snippetid="snippet--content"> {* TODO snippet & link to form *}
					<span class="btn__text">
						{_"btn_ask_question"}
					</span>
				</a>
			</p>
		{elseif $btn == 'more' && isset($pages->faq)}
			<p class="b-faq__btn u-pt-sm u-mb-0 u-ta-c">
				<a href="{plink $pages->faq}" class="btn btn--bd btn--black">
					<span class="btn__text">
						{_"btn_all_faq"}
					</span>
				</a>
			</p>
		{/if}
	</div>
</div>