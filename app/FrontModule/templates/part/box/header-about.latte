{default $class = 'u-mb-md u-mb-xl@md'}
{default $cf = $object->cf->header_about ?? false}

<header n:class="b-header-about, $class">
	{php $img = isset($cf->image) ? $cf->image->getEntity() ?? false : false}
	<p class="b-header-about__bg">
		<img n:if="$img" srcset="
				{$img->getSize('sm')->src} 320w,
				{$img->getSize('md-2-1')->src} 560w,
				{$img->getSize('lg-2-1')->src} 750w,
				{$img->getSize('xl-2-1')->src} 1400w,
				{$img->getSize('2xl-2-1')->src} 1920w"
			sizes="(max-width: 1920px) 100vw,
					1920px"
			src="{$img->getSize('xl-2-1')->src}"
			alt="" fetchpriority="high">
	</p>
	<div class="b-header-about__content">
		<div class="row-main">
			<div class="u-maw-10-12 u-mx-auto u-mb-last-0">
				{snippetArea breadcrumbArea}
					{control breadcrumb, [class: 'm-breadcrumb--inverse']}
				{/snippetArea}
				<h1 class="b-header-about__title u-mt-0">
					{$object->nameHeading ? $object->nameHeading : $object->name}
				</h1>
			</div>
		</div>
	</div>
	<div class="b-header-about__bottom">
		<div class="row-main">
			<div class="b-header-about__box u-maw-10-12 u-mx-auto">
				<div class="b-header-about__grid grid grid--x-xl">
					<div class="b-header-about__cell grid__cell u-mb-last-0">
						<p class="leading tw-text-[1.5rem] md:w-text-[1.8rem]" n:if="$object->annotation ?? false">
							{$object->annotation|texy|noescape}
						</p>
					</div>
					<div class="b-header-about__cell grid__cell">
						{include $templates.'/part/crossroad/links.latte', class: 'b-header-about__links', cf: $cf->links}
					</div>
				</div>
			</div>
		</div>
	</div>
</header>