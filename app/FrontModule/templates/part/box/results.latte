{default $class = 'u-mb-md u-mb-2xl@md'}
{default $title1 = false}
{default $text = false}
{default $image = false}
{default $btn = false}
{default $title2 = false}
{default $persons = []}

<div n:class="b-results, $class, u-maw-8-12, u-mx-auto">
	<div class="grid grid--x-sm">
		<div class="grid__cell size--6-12@md">
			<div class="b-results__box">
				<h2 n:if="$title1" class="h3">
					{$title1}
				</h2>
				<p n:if="$text || $image" class="b-results__text">
					<span n:ifcontent>
						{$text}
					</span>
					<img n:if="$image" class="b-results__img img" src="{$image->getSize('md')->src}" alt="{$image->getAlt($mutation)}" loading="lazy" width="191" height="191">
				</p>
				<p n:ifcontent>
					{php $type = $btn->toggle}
					{php $page = isset($btn->systemHref) && isset($btn->systemHref->page) ? $btn->systemHref->page->getEntity() ?? false : false}
					{php $href = $btn->customHref??->href ?? false}
					{php $hrefNameSystem = $btn->systemHref??->hrefName ?? false}
					{php $hrefNameCustom = $btn->customHref??->hrefName ?? false}

					{if $type == 'systemHref' && $page}
						<a href="{plink $page}" n:ifcontent class="btn btn--bd btn--gray">
							<span class="btn__text">
								{if $hrefNameSystem}
							</span>
								{$hrefNameSystem}
							{else}
								{$page->nameAnchor}
							{/if}
						</a>
					{elseif $type == 'customHref' && $href && $hrefNameCustom}
						<a href="{$href}" class="btn btn--bd btn--gray" target="_blank" rel="noopener noreferrer">
							<span class="btn__text">
								{$hrefNameCustom}
							</span>
						</a>
					{/if}
				</p>
			</div>
		</div>
		<div class="grid__cell size--6-12@md">
			<div class="b-results__box">
				<h2 n:if="$title2" class="h3">
					{$title2}
				</h2>
				<div n:ifcontent class="b-results__persons">
					{foreach $persons as $person}
						{php $img = isset($person->image) ? $person->image->getEntity() ?? false : false}
						{include $templates.'/part/box/person.latte', class: 'b-results__person', src: $img->getSize('md')->src, alt: $img->getAlt($mutation), name: $person->name ?? false, position: $person->position ?? false}
					{/foreach}
				</div>
			</div>
		</div>
	</div>
</div>