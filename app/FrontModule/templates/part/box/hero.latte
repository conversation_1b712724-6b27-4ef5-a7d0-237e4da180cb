{default $class = 'u-mb-md u-mb-3xl@md'}
{default $cf = $object->cf->landing_header ?? false}
{default $slides = $cf->slides ?? []}

{php $hasCarousel = count($slides) > 1}

<div n:class="b-hero, $class">
	<h1 class="u-vhide">
		{$object->nameHeading ? $object->nameHeading : $object->name}
	</h1>
	<div n:class="b-hero__carousel, $hasCarousel ? embla" {if $hasCarousel}data-controller="embla" data-embla-autoplay-value="6000"{/if}>
		<div n:tag-if="$hasCarousel" class="embla__viewport" data-embla-target="viewport">
			<div n:class="grid, grid--scroll, grid--x-0, grid--y-0, $hasCarousel ? embla__container">
				<div n:foreach="$slides as $slide" class="b-hero__item grid__cell">
					<div class="b-hero__slide">
						<div class="b-hero__bg">
							{php $img = isset($slide->image) ? $slide->image->getEntity() ?? false : false}
							<img n:if="$img" srcset="
									{$img->getSize('sm')->src} 320w,
									{$img->getSize('md-2-1')->src} 560w,
									{$img->getSize('lg-2-1')->src} 750w,
									{$img->getSize('xl-2-1')->src} 1400w,
									{$img->getSize('2xl-2-1')->src} 1920w"
								sizes="(max-width: 1920px) 100vw,
										1920px"
								src="{$img->getSize('xl-2-1')->src}"
								alt="" fetchpriority="high">
						</div>
						<div class="b-hero__content">
							<div class="row-main u-mb-last-0">
								<p n:ifcontent class="b-hero__title h0 u-mt-0">
									{$slide->title1 ?? false}
									<span n:ifcontent class="outline u-d-b">
										{$slide->title2 ?? false}
									</span>
								</p>
								<p n:if="$slide->annot" class="b-hero__annot h3 u-mt-0">
									{$slide->annot}
								</p>
								{php $btns = $slide->btns ?? []}
								<p n:if="count($btns)" class="b-hero__btns">
									{foreach $btns as $btn}
										{php $type = $btn->btn->toggle}
										{php $page = isset($btn->btn->systemHref) && isset($btn->btn->systemHref->page) ? $btn->btn->systemHref->page->getEntity() ?? false : false}
										{php $href = $btn->btn->customHref??->href ?? false}
										{php $hrefNameSystem = $btn->btn->systemHref??->hrefName ?? false}
										{php $hrefNameCustom = $btn->btn->customHref??->hrefName ?? false}

										{if $type == 'systemHref' && $page}
											<a href="{plink $page}" n:ifcontent n:class="b-hero__btn, btn, btn--xl, $iterator->counter != 1 ? 'btn--bd btn--white'">
												<span class="btn__text">
													{if $hrefNameSystem}
														{$hrefNameSystem}
													{else}
														{$page->nameAnchor}
													{/if}
												</span>
											</a>
										{elseif $type == 'customHref' && $href && $hrefNameCustom}
											<a href="{$href}" n:class="b-hero__btn, btn, btn--xl, $iterator->counter != 1 ? 'btn--bd btn--white'" target="_blank" rel="noopener noreferrer">
												<span class="btn__text">
													{$hrefNameCustom}
												</span>
											</a>
										{/if}
									{/foreach}
								</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		{if $hasCarousel}
			<button class="embla__btn embla__btn--prev" disabled="disabled" type="button" data-action="embla#prev" data-embla-target="prevButton">
				{('arrow-left')|icon}
				<span class="u-vhide">{_btn_prev}</span>
			</button>
			<button class="embla__btn embla__btn--next" disabled="disabled" type="button" data-action="embla#next" data-embla-target="nextButton">
				{('arrow-right')|icon}
				<span class="u-vhide">{_btn_next}</span>
			</button>
			<div class="embla__dots is-disabled" data-embla-target="dots"></div>
			<script type="text/template" class="embla-dot-template">
				<button class="embla__dot" type="button"><span class="embla__dot-inner">%i</span></button>
			</script>
		{/if}
	</div>

	<div class="b-hero__categories">
		<div class="row-main">
			{include $templates.'/part/crossroad/hero-categories.latte', categories: $cf->categories ?? []}
		</div>
	</div>
</div>