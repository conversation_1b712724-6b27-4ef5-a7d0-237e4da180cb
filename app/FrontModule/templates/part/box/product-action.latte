{varType App\Model\Orm\Product\Product $product}
<div class="b-product-action">
	<div class="b-product-action__info">
		<p class="b-product-action__name h4 u-mb-0">
			{$variant->name}
		</p>
	</div>

	{* TODO BE: variants *}
	{*control variantPicker*}

	{snippet addToCartPrice}
		<div class="b-product-action__price-marketing">
			<div class="b-product-action__price">
				{include $templates.'/part/core/countdown.latte', class: 'b-product-action__countdown countdown--detail'}
				{include $templates.'/part/core/price.latte', class: false, isDetail: true}
			</div>
			{* TODO marketing skryt *}
			<div n:if="false" class="b-product-action__marketing">
				{* TODO - napojit všechny marketingové akce a dárky *}
				{embed $templates.'/part/box/marketing.latte', class: 'u-mb-0', icon: 'gift'}
					{block content}
						{_"marketing_gift"}
						{php $items = [
							['name' => 'SPZ na dron', 'link' => '#'],
							['name' => 'zalaminovaný řidičák', 'link' => '#']
						]}
						{foreach $items as $key => $item}
							{if $key === count($items) - 1} a {/if}
							<a href="{$item['link']}">{$item['name']}</a>{if $key < count($items) - 2}, {/if}
						{/foreach}
					{/block}
				{/embed}
				{* {if isset($pages->shopInPrague)}
					{embed $templates.'/part/box/marketing.latte', class: 'u-mb-0', icon: 'pin-outline'}
						{block content}
							{_"marketing_try"|noescape|replace: '%link', $presenter->link($pages->shopInPrague)}
						{/block}
					{/embed}
				{/if} *}
				{* {embed $templates.'/part/box/marketing.latte', class: 'u-mb-0', icon: 'truck'}
					{block content}
						{_"marketing_delivery"}
					{/block}
				{/embed} *}
				{* {embed $templates.'/part/box/marketing.latte', class: 'u-mb-0', icon: 'truck', selected: true}
					{block content}
						{_"marketing_delivery_free"}
					{/block}
				{/embed} *}

				{* TODO link *}
				{embed $templates.'/part/box/marketing.latte', class: 'u-mb-0', icon: 'bomb'}
					{block content}
						{_"marketing_extra_discount"|noescape|replace: '%link', '#'}
					{/block}
				{/embed}
			</div>
		</div>

			<div class="b-product-action__availability">
				{include $templates.'/part/core/availability.latte', isDetail: true}
				{include $templates.'/part/core/delivery.latte', isDetail: true}
			</div>

			{* Přidat do košíku *}
			{if $productDto->productAvailabilityIsShowCartDetail}
				{control addToCart, class: false, btnClass:'f-add-to-cart__btn btn--md'}
			{/if}

			{php $type = $variant->productAvailability->getType() ?? false}
			{if in_array($type, ['onstock', 'onstock_supplier', 'preorder']) && !$product->isCertificate}
				<p class="u-mb-0">
					<a href="{plink $pages->popupDeliveryOptions, productLocalizationId: $productLocalization->id}" class="b-user-review__link f-add-to-cart__options" data-modal='{"medium": "fetch", "modalClass": "b-modal--8-12"}' data-snippetid="snippet--content">{_"delivery_options"}</a>
				</p>
			{/if}

			{* Hlídat dostupnost *}
			{if ($variant->productAvailability->isShowWatchdog() ?? false) || !$variant->productAvailability->hasPrice($mutation, $priceLevel, $state)}
				{include $templates. '/part/form/watchdog.latte', class: false, btnClass: 'f-add-to-cart__btn btn--md', longLangs: true, productId: $product->id}
			{/if}
			{* Podobné produkty *}
			{if $variant->productAvailability->isShowSimilar() ?? false}
				{include $templates. '/part/form/similar.latte', class: fales}
			{/if}
				{/snippet}
</div>

