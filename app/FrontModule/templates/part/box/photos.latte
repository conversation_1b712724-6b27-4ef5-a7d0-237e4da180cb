{default $class = 'u-mb-sm u-mb-lg@md'}
{default $images = []}

<p n:class="b-photos, $class, embla" data-controller="embla" data-embla-settings-value='{if str_contains($class, "full-width")}{"loop": "true", "dragFree": "true"}{else}{"loop": "true"}{/if}'>
	<span class="b-photos__helper">
		<span class="embla__viewport" data-embla-target="viewport">
			<span class="embla__container">
				{foreach $images as $image}
					<img src="{$image->getSize('lg')->src}" alt="{$image->getAlt($mutation)}" loading="lazy">
				{/foreach}
			</span>
		</span>
	</span>
	<button class="embla__btn embla__btn--prev" disabled="disabled" type="button" data-action="embla#prev" data-embla-target="prevButton">
		{('arrow-left')|icon}
		<span class="u-vhide">{_btn_prev}</span>
	</button>
	<button class="embla__btn embla__btn--next" disabled="disabled" type="button" data-action="embla#next" data-embla-target="nextButton">
		{('arrow-right')|icon}
		<span class="u-vhide">{_btn_next}</span>
	</button>
</p>