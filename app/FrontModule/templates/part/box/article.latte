{default $class = false}
{default $titleTag = 'h2'}

<article n:class="b-article, $class, link-mask">
	<p class="b-article__img">
		{if $c->getFirstImage() !== null}
			{php $img = $c->getFirstImage()->getSize('md-16-9')}
			<img class="img img--16-9" src="{$img->src}" alt="" width="{$img->width}" height="{$img->height}" loading="lazy">
		{else}
			<img class="img img--16-9" src="/static/img/illust/noimg.svg" alt="" loading="lazy">
		{/if}

		{*first tag*}
		{var $tag = $c->blogTags->fetch()}
		<span n:if="$tag" class="b-article__flag flag flag--gray-light">{$tag->name}</span>
		{*first autor*}
		{var $author = $c->authors->fetch()}
		{if $author}
		{include $templates.'/part/core/author.latte', class: 'b-article__author'}
		{/if}
	</p>
	<div class="b-article__content">
		<h2 n:tag="$titleTag" class="b-article__title h4">
			<a href="{plink $c}" class="b-article__link link-mask__link">
				{$c->nameTitle}
			</a>
		</h2>
		<p n:if="$c->annotation" class="b-article__desc">
			{$c->annotation|texy|noescape}
		</p>
		<div class="b-article__bottom">
			{*<p class="b-article__stats item-icon" >
			{('heart')|icon, 'item-icon__icon'}
			123
			</p>
			<p class="b-article__stats item-icon" >
			{('comment')|icon, 'item-icon__icon'}
			16
			</p>*}
			<p class="b-article__date">
				{* {$c->publicFrom|date:"j. n. Y"} *}
				{php $daysAgo = 2}
				{_"before"} {$daysAgo} {_($daysAgo|plural: "day_plural_1", "day_plural_2", "day_plural_3")}
			</p>
		</div>
	</div>
</article>
