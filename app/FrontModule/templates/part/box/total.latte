{default $class = false}

<div n:class="b-total, $class">
	<table class="b-total__table">
		<tbody>
			{* {var Brick\Money\Money $totalDiscountVat = $shoppingCart->getTotalDiscountVat()}
			{if !$totalDiscountVat->isZero()}
				<tr>
					<td>{_"cart_total_before_discount"}</td>
					<td>{$shoppingCart->getTotalOriginalPriceVat()|money}</td>
				</tr>
				<tr class="u-c-red">
					<td>{_"cart_total_saving"}</td>
					<td>{$totalDiscountVat|money}</td>
				</tr>
			{/if} *}

			{* Voucher *}
			{php $vouchers = $vouchers->findBy(['voucherCode->voucher->type' => [\App\Model\Orm\Voucher\Voucher::TYPE_AMOUNT, \App\Model\Orm\Voucher\Voucher::TYPE_PERCENT]])}
			<tr n:foreach="$vouchers as $voucherItem" class="u-c-red">
				<td>{_$voucherItem->getTitle()} {$voucherItem->getVoucherValue()} ({$voucherItem->voucherCodeString})</td>
				<td>&minus;{$voucherItem->getVoucherValue()}</td>
			</tr>

			<tr>
				<td>Celkem bez DPH</td>
				<td>{$totalPrice|money}</td>
			</tr>
			{var $vatValue = $totalPriceVatUnrounded->minus($totalPrice)}
			<tr>
				<td>DPH 21%</td>
				<td>{$vatValue|money}</td>
			</tr>
			{var $rounding = $totalPriceVatWithoutCertificate->minus($totalPrice->plus($vatValue))}
			<tr n:if="!$rounding->isZero()">
				<td>Zaokrouhlení</td>
				<td>{$rounding|money}</td>
			</tr>
		</tbody>
		<tfoot class="u-fw-b">
			<tr>
				<td>{_"cart_total_vat"}</td>
				<td class="b-total__price">{$totalPriceVatWithoutCertificate|money}</td>
			</tr>
		</tfoot>
	</table>
</div>
