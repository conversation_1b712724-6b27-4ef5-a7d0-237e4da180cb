{default $class = false}

{var $vouchers = $shoppingCart->getVouchers()}
{var $isOpen = isset($voucherError) || isset($voucherWarning)}

<div n:class="b-voucher, $class, $isOpen ? is-open" data-controller="toggle-class">
	{if count($vouchers) > 0}
		{* Aktivní kód *}
		<p class="b-voucher__code u-mb-0" n:foreach="$vouchers as $voucherItem">
			{varType App\Model\Orm\Order\Voucher\VoucherItem $voucherItem}
			<span class="item-icon">
				{('check')|icon, 'item-icon__icon u-c-green'}
				<span class="item-icon__text">
					{$voucherItem->voucherCode->code}
					<b class="b-voucher__discount">
						{_$voucherItem->getTitle()} {$voucherItem->getVoucherValue()}
					</b>
				</span>
			</span>
			<a class="b-voucher__remove" n:href="deleteVoucherItem! voucherCodeId: $voucherItem->voucherCode->id" data-naja data-naja-history="off" data-naja-loader="body">{_"btn_remove"}</a>
		</p>
	{else}
		<p class="b-voucher__toggle u-mb-0">
			<button type="button" class="as-link" data-action="toggle-class#toggle">
				{_"cart_voucher_toggle"}
			</button>
		</p>
		{* Input pro přidání *}
		<p n:class="b-voucher__wrap, u-mb-0, isset($voucherError) ? has-error">
			<label for="voucher" class="u-vhide">
				{_"cart_voucher_placeholder"}
			</label>
			<span class="b-voucher__inp-fix inp-fix">
				{('voucher')|icon, 'inp-fix__icon'}
				<input n:name="voucher" class="b-voucher__inp inp-text" placeholder="{_'cart_voucher_placeholder'}">
				<button type="submit" class="btn" n:name="send">
					<span class="btn__text">
						{_"cart_voucher_btn"}
					</span>
				</button>
			</span>
			<span class="inp-error" n:ifset="$voucherError">
				{$voucherError|noescape}
			</span>
		</p>
	{/if}
</div>


{* <p n:ifset="$voucherWarning" class="u-maw-4-12 u-ml-auto u-mb-sm">
	<span class="tooltip tooltip--error tooltip--static top end">
		<span class="tooltip__content">
			{$voucherWarning}
			<span class="tooltip__arrow"></span>
		</span>
	</span>
</p> *}
