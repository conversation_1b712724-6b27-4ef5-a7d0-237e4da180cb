{default $class = false}

<header n:class="b-header-position, $class, u-maw-8-12, u-mx-auto, 'u-pt-sm u-pt-md@md'">
	<div class="b-header-position__grid grid grid--bottom">
		<div class="b-header-position__cell b-header-position__cell--main grid__cell u-mb-last-0">
			{snippetArea breadcrumbArea}
				{control breadcrumb}
			{/snippetArea}
			<h1 class="b-header-position__title u-mt-0">
				{$object->nameHeading ? $object->nameHeading : $object->name}
			</h1>
			<p n:ifcontent n:if="$object->annotation ?? false">
				{str_replace('<br>', '', ($object->annotation|texy))|noescape}
			</p>
		</div>
		<div class="b-header-position__cell b-header-position__cell--side grid__cell">
			<p class="b-header-position__box u-mb-0">
				<b class="item-icon">
					{('pin-outline')|icon, 'item-icon__icon'}
					<span class="item-icon__text">
						Praha
					</span>
				</b>
				<b class="item-icon">
					{('time')|icon, 'item-icon__icon'}
					<span class="item-icon__text">
						Full time / Part time
					</span>
				</b>
			</p>
		</div>
	</div>
</header>