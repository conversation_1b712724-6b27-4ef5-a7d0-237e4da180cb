{default $class = false}
{default $cf = $object->cf->base ?? false}
{default $img = $cf && isset($cf->image_main) ? $cf->image_main->getEntity() ?? false : false}
{default $name = $seoLink??->name ?? ($object->nameHeading ? $object->nameHeading : $object->name)}
{default $annotation = $seoLink??->description ?? $object??->annotation ?? null}
{default $date = false}

{if $object instanceof App\PostType\Page\Model\Orm\Tree}
	{var $name = $object->getTitle()}
{/if}
<header n:class="b-annot, $class, u-mb-last-0, $annotation || $img ? b-annot--w-content">
	<h1 class="b-annot__title">
		{control seoTools, $name}
	</h1>

	<figure n:if="$img" class="b-annot__figure img img--7-3 u-mb-0">
		<img src="{$img->getSize('library')->src}" alt="" fetchpriority="high">
	</figure>
</header>

