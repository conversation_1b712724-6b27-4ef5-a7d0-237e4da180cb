{default $class = false}
{varType App\Model\Orm\User\User $userEntity}
<div n:class="b-user, $class">
	<div n:ifcontent class="b-user__head u-mb-last-0">
		<p n:if="$userEntity->firstname ?? null" class="b-user__title h5">
			{$userEntity->firstname}{if $userEntity->lastname ?? null} {$userEntity->lastname}{/if}
		</p>

		{if isset($pages->userSection->cf->loyaltyClub->registered->loayltyLink)
		&& ($clubPage = $pages->userSection->cf->loyaltyClub->registered->loayltyLink) instanceOf App\Model\CustomField\LazyValue
		&& $clubPage->getEntity()}

			<p class="b-user__membership item-icon">
				{('membership')|icon, 'item-icon__icon'}
				<span class="item-icon__text">
					{capture $link}{plink $clubPage}{/capture}
					{if $userEntity->isClubMember ?? false}
						{_"user_menu_member"|replace:'%link', $link->__toString()|noescape}
					{else}
						{_"user_menu_non_member"|replace:'%link', $link->__toString()|noescape}
					{/if}
				</span>
			</p>

		{/if}
	</div>
	{block content}{/block}
</div>
