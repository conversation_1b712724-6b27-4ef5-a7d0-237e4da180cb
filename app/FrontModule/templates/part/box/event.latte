{default $class = false}

{php $externalUrl = $item->cf->settings->external_link ?? false}

{capture $eventBlock}
	{php $img = isset($item->cf->base->mainImage) ? $item->cf->base->mainImage->getEntity() ?? false : false}
	<div n:if="$img" class="b-event__img img img--7-5">
		<img src="{$img->getSize('md')->src}" alt="{$item->nameAnchor}" loading="lazy">
	</div>
	<div class="b-event__wrap">
		{php $from = $item->cf->settings??->from ?? false}
		<time n:if="$from" class="b-event__date date font-secondary" datetime="{$from|date:'Y-m-d'}">
			<span class="date__day">{$from|date('j')}</span>
			{capture $monthNumber}{$from|date:"n"}{/capture}
			<span class="date__month">{_"month_".$monthNumber->__toString()}</span>
			<span class="date__year">{$from|date('Y')}</span>
		</time>
		<h2 class="b-event__title h3">
			{$item->nameAnchor}
			{if $externalUrl}{('link')|icon}{/if}
		</h2>
	</div>
{/capture}

{if $externalUrl}
	<a href="{$externalUrl|externalLink}" n:if="$item" n:class="b-event, $class" target="_blank" rel="noopener noreferrer">
		{$eventBlock}
	</a>
{else}
	<a href="{plink $item filter => null}" n:if="$item" n:class="b-event, $class">
		{$eventBlock}
	</a>
{/if}