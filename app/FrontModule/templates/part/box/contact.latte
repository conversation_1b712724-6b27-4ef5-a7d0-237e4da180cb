{default $class = false}
{default $cfPerson = $pages->contact->cf->contactPerson ?? false}
{default $cfContacts = $pages->contact->cf->contacts ?? false}

<article n:ifcontent n:class="b-contact, $class">
	<div n:if="$cfContacts !== false" class="b-contact__inner">
		<h2 class="b-contact__title h3">
			{php $name = $cfPerson->name ?? false}
			{php $img = isset($cfPerson->image) ? $cfPerson->image->getEntity() ?? false : false}
			{include $templates.'/part/core/person.latte', class: 'b-contact__person', img: $img, alt: $name}
			{_"contact_title"|noescape}
		</h2>
		<p n:if="$cfContacts->phone" class="b-contact__phone link-mask">
			<span class="b-contact__icon">
				{('phone-outline')|icon}
			</span>
			<span>
				<a href="tel:{$cfContacts->phone|replace:' ',''}" class="b-contact__link link-mask__link">
					{_"call"} {$cfContacts->phone}
				</a>
				<span n:if="$cfContacts->phone_hours">{$cfContacts->phone_hours}</span>
			</span>
		</p>
		<p n:if="$cfContacts->mail" class="b-contact__mail">
			<a href="mailto:{$cfContacts->mail}" class="item-icon">
				{('envelope-outline')|icon, 'item-icon__icon'}
				<span class="item-icon__text">
					{$cfContacts->mail}
				</span>
			</a>
		</p>
	</div>
</article>
