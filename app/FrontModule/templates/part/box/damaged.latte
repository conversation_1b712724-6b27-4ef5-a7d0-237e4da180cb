{default $class = false}
{default $damageLevel = 1}

{if isset($product->damagedParent)}
	{var $selectedCurrency = App\Model\Currency\CurrencyHelper::getCurrency()}
	{var $priceDamaged = $product->price($mutation, $priceLevel, $state, $selectedCurrency)}
	{var $priceDamagedParent = $product->damagedParent->price($mutation, $priceLevel, $state, $selectedCurrency)}
	{var $discountValue = $priceDamagedParent->minus($priceDamaged)}
		<div n:class="b-damaged, $class, message, message--error">
			<p class="b-damaged__title item-icon u-c-secondary">
				{('damaged')|icon, 'item-icon__icon'}
					<b class="item-icon__text">
						{_"damaged_title_".$damageLevel}

						{if !$discountValue->isNegativeOrZero()}
							{_"damaged_title_discount"|replace:'%discount', $discountValue->__toString()}
						{/if}
					</b>
			</p>
			<p class="u-mb-0">
				{_"damaged_content_".$damageLevel}
			</p>
		</div>
{/if}
