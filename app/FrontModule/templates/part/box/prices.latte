{default $class = 'u-mb-sm u-mb-xl@md'}
{default $cf = $object->cf->prices ?? false}

<div n:if="$cf" n:class="$class, u-maw-8-12, u-mx-auto">
	<h2 n:if="$cf->title ?? false" class="tw-mb-[1.2rem] md:tw-mb-[2.4rem]">{$cf->title}</h2>
	<ul n:if="count($cf->items ?? [])" class="u-reset-ul tw-grid tw-grid-cols-2 md:tw-grid-cols-[1fr_max-content] md:tw-text-[1.5rem] tw-gap-[0.4rem]">
		<li n:foreach="$cf->items as $item" class="tw-grid tw-grid-cols-subgrid tw-col-span-full">
			<span class="tw-grid tw-col-span-full tw-grid-cols-subgrid tw-bg-bg tw-rounded-md tw-p-[1rem_1.8rem] tw-gap-[1rem] tw-items-center">
				{php $price = $item->price ?? false}
                <span n:class="!$price ? tw-col-span-2">{$item->name}</span>
				<b n:if="$price" class="tw-text-right">{$price}</b>
			</span>
		</li>
	</ul>
</div>