{default $class = false}
{default $items = []}
{default $btns = []}

<p n:if="count($items)" n:class="b-top, $class">
	<span class="b-top__inner">
		<span n:if="count($btns)" class="tw-flex tw-flex-col tw-gap-[0.8rem]">
			{foreach $btns as $btn}
				{php $btnClass = $btn->class ?? 'btn--secondary'}
				{php $link = $btn->link ?? false}
				{php $lang = $btn->lang ?? false}
				{php $download = $btn->download ?? false}
				{php $icon = $btn->icon ?? false}
				{php $blank = $btn->blank ?? false}
				{php $naja = $btn->naja ?? false}

				<a n:if="$link && $lang" href="{$link}" class="b-top__btn btn {$btnClass} btn--lg" {if $download}download{/if} {if $blank}target="_blank" rel="noopener noreferrer"{/if} {if $naja}data-naja="" data-naja-history="off" data-modal='{"medium": "fetch"}' data-snippetid="snippet--content"{/if}>
					<span class="btn__text">
						<span n:tag-if="$icon" class="btn__inner">
							{if $icon}{($icon)|icon, 'btn__icon'}{/if}
							{_$lang}
						</span>
					</span>
				</a>
			{/foreach}
		</span>
		<img n:if="count($btns)" class="b-top__arrow" src="/static/img/illust/angle.svg" alt="" loading="lazy" width="16" height="68">
		<span n:if="count($items)" class="b-top__items">
			<span n:foreach="$items as $item" class="b-top__item item-icon">
				{('usp')|icon, 'item-icon__icon'}
				<span class="item-icon__text">
					{$item->text ?? $item}
				</span>
			</span>
		</span>
	</span>
</p>