{default $class = false}
{var $products = $presenter->shoppingCart->getProducts()}
{var $form = $presenter['siteHeader']['cart']['form']}

<dialog n:if="$presenter->shoppingCart->showForgottenCart($object)" id="modal-0" class="b-modal b-modal--nonsticky is-opened" open tabindex="0">
	<div class="b-modal__wrapper u-maw-5-12" data-scroll-lock-scrollable="">
		{* <div class="b-modal__header js-modal-header">
			<button type="button" class="b-modal__close" data-action="toggle-class#toggle" data-toggle-class="is-opened">
				<span class="b-modal__close-text u-vhide">Close</span>
				{('close-sm')|icon}
			</button>
		</div> *}

		{define #products}
			{default $class = false}
			<form n:if="$products->count()" n:name="$form" data-naja n:class="b-smallbasket__form, $class, block-loader">
				<ul class="b-smallbasket__list">
					<li class="b-smallbasket__item" n:foreach="$products as $productItem">
						{php $product = $productItem->variant->product}
						{php $productDto = $productDtoProviderCallback($product, $productItem->variant)}
						{varType App\Model\Orm\Order\Product\ProductItem $productItem}
						{var $link = $presenter->link($product, ['v' => $productItem->variant->id])}

						<a href="{$link}" class="b-smallbasket__img img img--contain">
							{if $product->firstImage}
								{php $img = $product->firstImage->getSize('sm')}
								<img src="{$img->src}" alt="{$productItem->getName()}" loading="lazy">
							{else}
								<img src="/static/img/illust/noimg.svg" alt="" loading="lazy">
							{/if}
						</a>
						<span class="b-smallbasket__name u-mb-last-0">
							<a href="{$link}" class="b-smallbasket__link">
								{$productItem->getName()}
							</a>
							{include $templates.'/part/core/type.latte', class: false, product: $product}
						</span>
						<span class="b-smallbasket__count">
							{include $templates.'/part/form/part/count.latte', autosubmit: true, class: 'inp-count--xs', variant: $productItem->variant, input: $form['products'][$productItem->variant->id]['quantity'], maxAmount: $productItem->getMaxAvailableAmount()}
						</span>
						<div class="b-smallbasket__info u-fw-b u-ta-r">
							{$productItem->totalPriceVat|money}
							{include $templates.'/part/core/availability.latte', class=>false, variant=>$productItem->variant, showTooltip=>false, productDto=>$productDto}
						</div>
					</li>
					{* <li n:foreach="$vouchers as $voucherItem" class="b-smallbasket__item">
						{varType App\Model\Orm\Order\Voucher\VoucherItem $voucherItem}
						<span class="b-smallbasket__img img img--contain">

						</span>
						<span class="b-smallbasket__name u-mb-last-0">
							{$voucherItem->getName()}
						</span>
						<span class="b-smallbasket__count">

						</span>
						<b class="b-smallbasket__info">
							{$voucherItem->unitPriceVat|money}
						</b>
					</li> *}
				</ul>
				<div class="block-loader__loader"></div>
			</form>
		{/define}

		<div class="b-modal__content js-modal-content">
			<div class="b-modal__slide js-modal-slide is-loaded is-active">
				<div class="b-modal__inner">
					<div class="b-smallbasket u-bgc-default">
						<p class="h5 u-mb-0">
							{_"smallbasket_title_forgot"}
						</p>
						<p class="u-mb-xs">
							{_"smallbasket_text_forgot"}
						</p>
						{include #products, class: 'u-mb-sm', products: $products}

						<div class="b-smallbasket__bottom u-bgc-white u-ta-r">
							<p class="u-ta-r u-mb-xxs">
								{_"smallbasket_total"} {$presenter->shoppingCart->getTotalPriceVat()|money}
							</p>
							<p class="b-smallbasket__btns u-mb-0">
								<a href="{$presenter->link('emptyCart!')}" class="btn btn--white btn--sm">
									<span class="btn__text">
										{_"btn_empty_basket"}
									</span>
								</a>
								<a href="{$presenter->link('toCart!')}" class="btn btn--secondary btn--sm">
									<span class="btn__text">
										{_"btn_enter_basket"}
									</span>
								</a>
							</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="b-modal__bg js-modal-bg"></div>
</dialog>
