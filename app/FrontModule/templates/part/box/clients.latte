{default $class = 'u-mb-md u-mb-2xl@md'}
{default $title = false}
{default $items = false}

<div n:class="$class">
	<h2 n:if="$title" class="tw-mb-[1.2rem] md:tw-mb-[2.4rem]">
		{$title}
	</h2>
	<p n:if="count($items)" class="tw-grid md:tw-gap-[1.2rem] tw-grid-cols-3 lg:tw-grid-cols-6">
		{foreach $items as $item}
			{php $image = isset($item->image) ? $item->image->getEntity() ?? false : false}
			<a n:tag-if="$item->link ?? false" href="{$item->link}" target="_blank" rel="noopener noreferrer">
				<img n:if="$image" class="img img--16-9 tw-flex-2/12" src="{$image->getSize('sm-16-9')->src}" alt="{$image->getAlt($mutation)}" loading="lazy">
			</a>
		{/foreach}
	</p>
</div>