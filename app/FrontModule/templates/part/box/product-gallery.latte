{varType App\Model\Orm\Product\Product $product}
{default $class = false}
{default $isOld = false}
{var $title = $variant->name}
{capture $safeTitle}{$title|replace:'"','\"'}{/capture}

{varType App\Model\Orm\ProductLocalization\ProductLocalization $object}
{varType App\Model\Orm\Product\Product $product}
{varType App\Model\Orm\ProductVariant\ProductVariant $variant}
{php $availabilityType = $variant->productAvailability->getType() ?? false}

{php $videos = (array)($variant->cf->videos ?? [])}
{php $images = (array)($variant->images ?? [])}

{* Produkt - videa na konci *}
{php $items = array_merge($images, $videos)}

<div n:class="b-product-gallery, $class, u-mb-last-0" data-controller="modal-gallery product-gallery">
	{define #preview}
		{default $isThumbs = false}

		<button n:tag-if="$isThumbs" type="button" class="b-product-gallery__thumb-btn as-link" data-action="product-gallery#goTo" data-slide="{$iterator->counter0}">
			{if $item->video ?? false}
				{php $imgThumb = '/static/img/illust/noimg.svg'}
				{php $medium = 'youtube'}
				{php $link = $item->video}

				{if strpos($link, 'youtube')}
					{php $urlObject =  new \Nette\Http\Url($link)}
					{php $id = $urlObject->getQueryParameter('v')}
					{php $url="https://i3.ytimg.com/vi/{$id}/maxresdefault.jpg"}
					{php $headers = @get_headers($url)}

					{if strpos($headers[0],'200')===false} {* neeesixtuje *}
						{php $imageName = 'hqdefault'}
					{else} {* existuje *}
						{php $imageName = 'maxresdefault'}
					{/if}

					{php $imgThumb = 'https://images.weserv.nl/?url=https://i3.ytimg.com/vi/' . $id. '/' . $imageName . '.jpg&w=200'} {* todo thumb size *}
				{elseif strpos($link, 'vimeo')}
					{php $id = str_replace('/', '', str_replace('https://vimeo.com/', '', $link))}
					{php $imgThumb = 'https://vumbnail.com/' . $id . '.jpg'}
					{php $medium = 'vimeo'}
				{/if}

				<a n:tag-if="!$isThumbs" href="{$item->video}" class="b-product-gallery__link" data-modal-gallery data-modal='{"gallery": "product", "medium": "{$medium}", "thumbUrl": "{$imgThumb}", "title": "{$safeTitle}"}'>
					{include $templates.'/part/box/video.latte', class: false, link: $item->video, poster: isset($item->image) ? $item->image->getEntity() ?? false : false, posterSize: 'xl-4-3', ratio: '4-3'}
					{* {include $templates.'/part/box/video.latte', class: false, imgClass: 'img--contain', link: $item->video, poster: isset($item->image) ? $item->image->getEntity() ?? false : false, posterSize: 'xl-4-3', ratio: '4-3'} *}
				</a>
			{elseif $item instanceOf App\Model\Orm\ProductImage\ProductImage}
				{php $imgLink = $item->getSize('2xl')->src}
				{php $imgThumb = $item->getSize('md-4-3')->src}
				<a n:tag-if="!$isThumbs" href="{$imgLink}" class="b-product-gallery__link" data-modal-gallery data-modal='{"gallery": "product", "medium": "image", "thumbUrl": "{$imgThumb}", "title": "{$safeTitle}"}'>
					{if $isThumbs}
						<img n:class="img, img--4-3, img--contain" src="{$item->getSize('sm-4-3')->src}" alt="{$item->getAlt($mutation)}" {if $iterator->isFirst()}fetchpriority="high"{else}loading="lazy"{/if}>
					{else}
						<img n:class="img, img--4-3, img--contain"
							srcset="
								{$item->getSize('sm-4-3')->src} 320w,
								{$item->getSize('md-4-3')->src} 560w,
								{$item->getSize('lg-4-3')->src} 750w,
								{$item->getSize('xl-4-3')->src} 1400w"
							sizes="(max-width: 1000px) 100vw,
									(max-width: 1620px) 50vw,
									810px"
							src="{$item->getSize('xl-4-3')->src}"
							alt="{$item->getAlt($mutation)}" {if $iterator->isFirst()}fetchpriority="high"{else}loading="lazy"{/if}>
					{/if}
				</a>
			{/if}
		</button>

		{* RWD: img srcset *}
	{/define}

	{* Hlavní carousel *}
	<div class="b-product-gallery__main">
		{if $isOld}
			<p class="b-product-gallery__soldout">
				{if isset($images[0])}
					<img n:class="img, img--4-3, img--contain" src="{$images[0]->getSize('xl')->src}" alt="$images[0]->getAlt($mutation)}" fetchpriority="high">
				{else}
					<img n:class="img, img--4-3, img--contain" src="/static/img/illust/noimg.svg" alt="" fetchpriority="high">
				{/if}
				<span class="flag flag--red">
					{_"product-gallery-not-for-sale"}
				</span>
			</p>
		{elseif count($items)}
			<div class="b-product-gallery__carousel embla" data-product-gallery-target="carousel">
				<div class="b-product-gallery__helper">
					<div class="embla__viewport">
						<div class="grid grid--scroll grid--x-0 grid--y-0 embla__container">
							<div n:foreach="$items as $item" n:ifcontent class="grid__cell">
								{include #preview, item: $item}
							</div>
						</div>
					</div>
				</div>
				<div class="b-product-gallery__nav">
					<div class="b-product-gallery__dots">
						<div class="embla__dots is-disabled" data-product-gallery-target="dots"></div>
					</div>
					<p class="b-product-gallery__total u-mb-0">
						{capture $pluralImages}{count($images)|plural: "images_1", "images_2", "images_3" }{/capture}
						{capture $pluralVideos}{count($videos)|plural: "videos_1", "videos_2", "videos_3" }{/capture}
						{count($images)} {_$pluralImages} + {count($videos)} {_$pluralVideos}
					</p>
				</div>
				<script type="text/template" class="embla-dot-template">
					<button class="embla__dot" type="button">%i</button>
				</script>
			</div>
		{else}
			<img n:class="img, img--4-3, img--contain" src="/static/img/illust/noimg.svg" alt="" fetchpriority="high">
		{/if}
	</div>

	{* Thumbs *}
	<div n:if="count($items) && !$isOld" class="b-product-gallery__thumbs embla" data-product-gallery-target="thumbs">
		<div class="embla__viewport">
			<ul class="b-product-gallery__list grid embla__container">
				<li n:foreach="$items as $item" n:ifcontent n:class="b-product-gallery__item, grid__cell, $iterator->isFirst() ? is-active">
					{include #preview, item: $item, isThumbs: true}
				</li>
			</ul>
		</div>
	</div>
</div>


{* Zobrazit více *}
{* {var $imageThumbCount = 4}
{var $thumbCount = 0}

{if count($videos)}
	{var $thumbCount = $imageThumbCount - 1}
	{var $imageThumbCount = $imageThumbCount - 1}
{/if}

{var $showBtnMore = false}
{if (count($images) - 1) > $imageThumbCount}
	{var $thumbCount = $imageThumbCount}
	{var $imageThumbCount = $imageThumbCount - 1}
	{var $showBtnMore = true}
{/if} *}
{* <li n:if="$showBtnMore" class="b-product-gallery__item b-product-gallery__item--more grid__cell">
	{foreach $images as $i}
		{if $iterator->counter > $thumbCount}
			{php $imgXs = $i->getSize('xs')}
			{php $img2xl= $i->getSize('2xl')}

			{if $iterator->counter == $thumbCount + 1}
				<a href="{$img2xl->src}" class="b-product-gallery__link img" data-modal-gallery data-modal='{"gallery": "product", "medium": "image", "thumbUrl": "{$imgXs->src}", "title": "{$safeTitle}"}'>
					<img class="img img--4-3" src="{$imgXs->src}" alt="{$i->name}" loading="lazy">
					<span class="b-product-gallery__more">
						{php $photos = ($images->count() - 1) - $imageThumbCount}
						{_"gallery_thumbs_show_all"|replace:"%count", (string)$photos}
					</span>
				</a>
			{else}
				<a href="{$img2xl->src}" class="u-vhide" data-modal-gallery data-modal='{"gallery": "product", "medium": "image", "thumbUrl": "{$imgXs->src}", "title": "{$safeTitle}"}'>
					{$i->name}
				</a>
			{/if}
		{/if}
	{/foreach}
</li> *}

{* <span n:if="$availabilityType == not_for_sale" class="b-product-gallery__overlay h6 u-mb-0">
	{_"product-gallery-not-for-sale"}
</span> *}
