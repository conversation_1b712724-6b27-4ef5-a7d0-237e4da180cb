{default $cf = $pages->cookie->cf->cookies ?? null}

<div class="b-cookie" data-cookie n:if="$cf">
	<div class="b-cookie__bg"></div>
	<div class="b-cookie__box">
		<div class="b-cookie__box-inner">
			<h2 n:if="$cf->title ?? false" class="h3">
				{$cf->title|noescape}
			</h2>
			{if $cf->text ?? false}
				{$cf->text|noescape}
			{/if}

			<div class="b-cookie__settings" data-step="2">
				<h3 n:if="($cf->title ?? false) && ($cf->consentsTitle ?? false)" class="h4">
					{$cf->consentsTitle|noescape}
				</h3>

				<div n:if="($cf->necessarilyLink ?? false) && ($cf->necessarilyText ?? false)" class="b-cookie__option">
					<div class="b-cookie__option-head" data-cookie-toggle>
						<span>
							{$cf->necessarilyLink|noescape}
						</span>
						<label class="switch">
							<input type="checkbox" checked disabled class="switch__inp">
							<span class="switch__inner">
								<span class="switch__bg switch__bg--left"></span>
								<span class="switch__bg switch__bg--right"></span>
								<span class="switch__tool"></span>
							</span>
						</label>
					</div>
					<div class="b-cookie__option-body">
						{$cf->necessarilyText|noescape}
					</div>
				</div>

				<div n:if="($cf->preferenceslLink ?? false) && ($cf->preferencesText ?? false)" class="b-cookie__option">
					<div class="b-cookie__option-head" data-cookie-toggle>
						<span>
							{$cf->preferenceslLink|noescape}
						</span>
						<label class="switch">
							<input type="checkbox" data-cookie-option="personalization_storage" class="switch__inp">
							<span class="switch__inner">
								<span class="switch__bg switch__bg--left"></span>
								<span class="switch__bg switch__bg--right"></span>
								<span class="switch__tool"></span>
							</span>
						</label>
					</div>
					<div class="b-cookie__option-body">
						{$cf->preferencesText|noescape}
					</div>
				</div>

				<div n:if="($cf->analyticsLink ?? false) && ($cf->analyticsText ?? false)" class="b-cookie__option">
					<div class="b-cookie__option-head" data-cookie-toggle>
						<span>
							{$cf->analyticsLink|noescape}
						</span>
						<label class="switch">
							<input type="checkbox" data-cookie-option="analytics_storage" class="switch__inp">
							<span class="switch__inner">
								<span class="switch__bg switch__bg--left"></span>
								<span class="switch__bg switch__bg--right"></span>
								<span class="switch__tool"></span>
							</span>
						</label>
					</div>
					<div class="b-cookie__option-body">
						{$cf->analyticsText|noescape}
					</div>
				</div>

				<div n:if="($cf->marketingLink ?? false) && ($cf->marketingText ?? false)" class="b-cookie__option">
					<div class="b-cookie__option-head" data-cookie-toggle>
						<span>
							{$cf->marketingLink|noescape}
						</span>
						<label class="switch">
							<input type="checkbox" data-cookie-option="ad_storage" class="switch__inp">
							<span class="switch__inner">
								<span class="switch__bg switch__bg--left"></span>
								<span class="switch__bg switch__bg--right"></span>
								<span class="switch__tool"></span>
							</span>
						</label>
					</div>
					<div class="b-cookie__option-body">
						{$cf->marketingText|noescape}
					</div>
				</div>
			</div>
			<div class="b-cookie__btns" data-step="1">
				<p n:if="$cf->btnSetPreferences ?? false">
					<button class="as-link" type="button" data-cookie-settings>
						{$cf->btnSetPreferences}
					</button>
				</p>

				<p n:if="($cf->btnReject ?? false) || ($cf->btnConsentAndContinuation ?? false)">
					<button n:if="$cf->btnReject ?? false" class="btn btn--primary" type="button" data-cookie-reject>
						<span class="btn__text">
							<span>
								{$cf->btnReject}
							</span>
						</span>
					</button>

					<button n:if="$cf->btnConsentAndContinuation ?? false" class="btn btn--primary" type="button" data-cookie-accept>
						<span class="btn__text">
							<span>
								{$cf->btnConsentAndContinuation}
							</span>
						</span>
					</button>
				</p>
			</div>
			<div class="b-cookie__btns" data-step="2">
				<p n:if="$cf->btnConfirmSelected ?? false">
					<button class="as-link" type="button" data-cookie-save>
						{$cf->btnConfirmSelected}
					</button>
				</p>

				<p n:if="$cf->btnAcceptEverything ?? false">
					<button class="btn btn--primary" type="button" data-cookie-accept>
						<span class="btn__text">
							<span>
								{$cf->btnAcceptEverything}
							</span>
						</span>
					</button>
				</p>
			</div>
		</div>
	</div>
</div>
