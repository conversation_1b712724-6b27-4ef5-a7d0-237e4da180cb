{default $class = false}
{default $showPaymentStep = false}
{default $steps = ['payment', 'packaging', 'transport', 'delivery']}
{default $activeStep = 'payment'}
{default $deliveryDate = false}

<p n:class="b-delivery-steps, $class">
	{define #step}
		<span n:class="b-delivery-steps__step, $name == $activeStep ? is-active">
			{('order-'.$name)|icon, 'b-delivery-steps__icon'}
			{if $name === 'payment' && $name !== $activeStep}
				{_"delivery_step_".$name.'_completed'}
			{else}
				{_"delivery_step_".$name}
			{/if}
			{if $deliveryDate instanceof \DateTimeInterface && $name == 'delivery'}<br>{$deliveryDate|date:'d.m.Y'}{/if}
		</span>
	{/define}

	{foreach $steps as $name}
		{continueIf !$showPaymentStep && $name === 'payment'}
		{include #step}
		{sep}{('arrow-right-bold')|icon, 'b-delivery-steps__arrow'}{/sep}
	{/foreach}
</p>
