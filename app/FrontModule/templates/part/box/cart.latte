{varType App\Model\Pages $pages}
{* {if $forceOpen} is-hover{/if}{if $order->parentItems->count() == 0} b-header--empty{/if} *}
<div class="b-header{if $presenter->shoppingCart->getTotalProducts() === 0} b-header--empty{/if}" n:if="$pages->precart !== null" n:snippet="headerCart" data-controller="touch-open">
	{var App\Model\ShoppingCart\ShoppingCart $shoppingCart = $presenter->shoppingCart}
	<a href="{plink $pages->cart}" class="b-header__link">
		<span class="b-header__icon">
			{('test-cart')|icon}
		</span>
		<span class="b-header__name">
			{$shoppingCart->getTotalPriceVat()|money} ({$shoppingCart->getTotalCount()} ks)
		</span>
	</a>
	<div class="b-header__popup">
		{control cart:header}
	</div>
</div>
