{default $class = false}
{default $form = false}
{default $name = false}
{default $review = false}

<div class="b-upload block-loader" data-controller="file-input">
    <ul class="b-upload__list"  data-file-input-target="list">
		{if $review && isset($review->cf->attachments) && isset($review->cf->attachments->images)}
			{foreach $review->cf->attachments->images as $image}
				{php  $img = $imageResizer->getImg($image->filename, 'xs')}
				<li class="b-upload__item">
					<span class="b-upload__inner is-uploaded">
						<span class="b-upload__img img">
							<img src="{$img->src}" alt="{$image->name}" loading="lazy">
						</span>
					</span>
				</li>
			{/foreach}
		{/if}
    </ul>

    <label n:name="{$name}" class="b-upload__label" data-file-input-target="dropzone" data-action="dragover->file-input#dragover dragend->file-input#dragend dragleave->file-input#dragleave drop->file-input#drop">
        <span class="b-upload__text item-icon">
{*            {('images'), 'item-icon__icon'|icon}*}
            <span class="item-icon__text">
                {_"upload_images_label"}
            </span>
        </span>
        <span class="b-upload__btn btn btn--block">
            <span class="btn__text">
                <span>
                    {_"btn_upload_images"}
                </span>
            </span>
        </span>
    </label>
    {input $name class=>"b-upload__inp", multiple=>"multiple", accept=>"image/*,image/heic,image/heif", data-file-input-target=>"input", data-action=>"change->file-input#fileInputChange"}
	<div class="block-loader__loader"></div>
</div>
