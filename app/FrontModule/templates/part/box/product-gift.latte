{varType App\PostType\Gift\Model\Orm\Gift\GiftLocalization $giftLocalization}
{var $isAvailable = $giftLocalization->isAvailable($shoppingCart)}
{default $class = false}
{php $isSelected = false}

{foreach $shoppingCart->getGifts() ?? [] as $gift}
	{if $gift->giftLocalization->id == $giftLocalization->id}
		{php $isSelected = true}
	{/if}
{/foreach}

<div n:class="b-product, b-product--gift, $class, $giftLocalization->gift->product ? link-mask">
	<div class="b-product__img-wrap">
		<p class="b-product__img img img--book">
			{php $image = $giftLocalization->getImage() ?? false}
			{if $image}
				<img src="{$image->getSize('sm')->src}" alt="{$giftLocalization->getName()}" loading="lazy">
			{else}
				<img src="/static/img/illust/noimg.svg" alt="" loading="lazy">
			{/if}
		</p>
	</div>
	<div class="b-product__content">
		<h3 class="b-product__title">
			<a n:tag-if="$giftLocalization->gift->product" href="{plink $giftLocalization->gift->product}" class="b-product__link link-mask__link" target="_blank">
				{$giftLocalization->getName()}
			</a>
		</h3>
		{var $price = $giftLocalization->getPrice($state)}
		<p n:if="($sellingPrice = $giftLocalization->getSellingPrice($priceLevel,$state)) !== null && !$price->isZero()" class="u-fw-b u-mb-xxs">
			{$sellingPrice|money}
		</p>
		<p n:if="$price->isZero() && $isAvailable" class="u-fw-b u-c-green u-tt-c u-mb-xxs">
			{_"free"}
		</p>
		<p n:if="!$isAvailable" class="u-fw-b u-c-gray">
			{var $availableText = $giftLocalization->getAvailableText($shoppingCart)}
			{if $availableText instanceof App\Model\TranslateData}
				{translate($availableText)}
			{/if}
		</p>

		<p class="b-product__price-add">
			{if $isSelected}
				<span class="btn btn--full btn--xs is-disabled">
					<span class="btn__text">
						{('check')|icon, 'btn__icon'}
						{_"btn_selected_gift"}
					</span>
				</span>
			{else}
				<a n:if="$isAvailable" n:href="addGift! id: $giftLocalization->id" data-naja data-naja-history="off" data-naja-loader="body" class="btn btn--full btn--xs link-mask__unmask">
					<span class="btn__text" n:if="$price->isZero()">{_'btn_select'}</span>
					<span class="btn__text" n:if="$price->isGreaterThan(0)">
						{translate('buy_gift', ['%buyPrice%' => App\Infrastructure\Latte\Filters::formatMoney($price)])}
					</span>
				</a>
			{/if}
		</p>
	</div>
</div>