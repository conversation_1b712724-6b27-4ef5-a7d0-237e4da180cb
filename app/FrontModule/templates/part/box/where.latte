{default $class = 'u-mb-md u-mb-3xl@md'}
{default $cf = $object->cf->where ?? false}

{php $images = $cf->images->fetchAll() ?? []}

<div n:class="b-where, $class, u-maw-11-12, u-mx-auto">
	<div class="b-where__box">
		<h2 n:if="$cf->title ?? false" class="b-where__title h1">
			{$cf->title}
		</h2>
		<p n:if="$cf->text ?? false" class="b-where__text">
			{$cf->text|texy|noescape}
		</p>
		<p n:if="$cf->locationText ?? false" class="b-where__location item-icon">
			{('pin-outline')|icon, 'item-icon__icon'}
			<span class="item-icon__text">
				{$cf->locationText|texy|noescape}
			</span>
		</p>
	</div>
	<p class="b-where__imgs">
		<img n:if="$images[0] ?? false" class="b-where__img b-where__img--1 img" src="{$images[0]->getSize('lg')->src}" alt="{$images[0]->getAlt($mutation)}" loading="lazy">
		<img n:if="$images[1] ?? false" class="b-where__img b-where__img--2 img" src="{$images[1]->getSize('xl')->src}" alt="{$images[1]->getAlt($mutation)}" loading="lazy">
		<img n:if="$images[2] ?? false" class="b-where__img b-where__img--3 img" src="{$images[2]->getSize('lg')->src}" alt="{$images[2]->getAlt($mutation)}" loading="lazy">
		<img n:if="$images[3] ?? false" class="b-where__img b-where__img--4 img" src="{$images[3]->getSize('lg')->src}" alt="{$images[3]->getAlt($mutation)}" loading="lazy">
		<img n:if="$images[4] ?? false" class="b-where__img b-where__img--5 img" src="{$images[4]->getSize('lg')->src}" alt="{$images[4]->getAlt($mutation)}" loading="lazy">
	</p>
</div>