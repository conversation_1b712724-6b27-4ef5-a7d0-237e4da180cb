{default $class = 'u-mb-0'}
{default $total = false}
{default $current = false}
{default $delta = false}
{default $status = false}


{if $delta && $delta->getAmount()->toFloat() < 0 } {*Doprava zdarma (100 % progress)*}
	{php $status = 'free'}
{elseif  $hasItemWithFreeDelivery || $hasUserFreeDelivery || $hasFreeDeliveryVoucher}
	{php $status = 'delivery-is-free'}
{elseif false} {* TODO: dosáhl alespoň na 1 dárek *}
	{php $status = 'partly-gift'}
{elseif false} {* TODO: nedosáhl na dárek *}
	{php $status = 'no-gift'}
{/if}

{* TODO rozlišit zobrazení v hlavičce / košíku / stránce (např. předkošík) *}
{php $type = 'smallbasket'} {* smallbasket | basket | page *}

<div n:if="$total && $current && $delta" n:class="b-delivery, $status ? 'b-delivery--' . $status, $class, 'b-delivery--' . $type">
	<div class="b-delivery__inner">
		{define #delivery}
			{default $points = []}
			{default $icon = false}
			{default $textTop = false}
			{default $textBottom = false}
			{default $separatedBottom = false} {* určuje, zda je text hned pod progress barem (doprava zdarma) nebo pokud je zde více bublin, tak až pod čarou (např. více dárků) *}

			<div n:ifcontent class="b-delivery__top u-mb-last-0">
				{$textTop|noescape}
			</div>

			{include '../core/progress.latte',
				class: 'b-delivery__progress',
				percentage: min($current->getAmount()->toFloat()/$total->getAmount()->toFloat()*100, 100),
				points: $points
			}
			{* percentage: min($current->getAmount()->toFloat() * 100 / $total->getAmount()->toFloat(), 100), *}

			<div n:class="b-delivery__bottom, !$textBottom ? b-delivery__bottom--empty, $separatedBottom ? b-delivery__bottom--separated, u-mb-last-0">
				{$textBottom|noescape}
			</div>
		{/define}

		{if $status == 'free'}
			{* Má dopravu zdarma - 100% zelený progress bar *}
			{capture $textBottom}
				<p class="item-icon u-mb-0">
					{('truck')|icon, 'item-icon__icon'}
					<span class="item-icon__text">
						{_"free_delivery_text_free"|noescape}
					</span>
				</p>
			{/capture}

			{include #delivery, textBottom: $textBottom, points: [
				['percentage' => 100, 'text' => '<span class="u-c-help">' . $total->getAmount()->__toString() . '</span>']
			]}
		{elseif $status == 'no-gift'}
			{* TODO: Progress dárků, nedosáhl zatím na žádný *}
			{capture $textTop}
				{if $type == 'smallbasket'}
					<p class="u-fw-b">
						<a href="{plink $pages->cart}#gifts" class="item-icon">
							{('gift')|icon, 'item-icon__icon'}
							<span class="item-icon__text">
								{_"delivery_text_top_gift"}
							</span>
						</a>
					</p>
				{else}
					<p class="item-icon u-fw-b u-ta-c">
						{('gift')|icon, 'item-icon__icon'}
						<span class="item-icon__text">
							{_"delivery_text_top_gift_want"}
						</span>
					</p>
				{/if}
			{/capture}

			{capture $textBottom}
				<p class="u-ta-c">
					<a href="#gifts" class="b-delivery__link item-icon">
						<span class="item-icon__text">
							{_"delivery_text_bottom_gift_offer"}
						</span>
						{('angle-down')|icon, 'item-icon__icon'}
					</a>
				</p>
			{/capture}

			{include #delivery, textTop: $textTop->__toString(), textBottom: $type == 'smallbasket' ? null : $textBottom->__toString(), separatedBottom: true,
				points: [
					['percentage' => 30, 'text' => '600 Kč<br> Malý dárek', icon: 'check'],
					['percentage' => 60, 'text' => '3 000 Kč Kč<br> Střední dárek', icon: 'gift'],
					['percentage' => 90, 'text' => '5 000 Kč Kč<br> Velký dárek', icon: 'gift']
				]
			}
		{elseif $status == 'delivery-is-free'}
			<span class="b-delivery__text item-icon u-fw-b u-c-green">
				{('car')|icon, 'item-icon__icon'}
				<span class="item-icon__text">
					{_"free_delivery_text_free"}
				</span>
			</span>
		{elseif $status == 'partly-gift'}
			{* TODO: Progress dárků, dosáhl alespoň na 1 *}
			{capture $textTop}
				{if $type == 'smallbasket'}
					<p>
						<span class="item-icon">
							{('gift')|icon, 'item-icon__icon'}
							<span class="item-icon__text">
								<a href="{plink $pages->cart}#gifts" class="u-fw-b">{_"delivery_text_top_gift"}</a>
								{_"delivery_text_top_gift_pick_basket"}
							</span>
						</span>
					</p>
				{else}
					<p class="item-icon u-ta-c">
						{('gift')|icon, 'item-icon__icon'}
						<span class="item-icon__text">
							{_"delivery_text_top_gift_success"|noescape}
						</span>
					</p>
				{/if}
			{/capture}

			{capture $textBottom}
				{if $type == 'smallbasket'}
					<p>
						{_"delivery_text_bottom_needed"|noescape|replace:'%name', 'střední dárek'|replace: '%amount', '275 Kč'}
						<a href="{plink $pages->cart}#gifts">{_"delivery_text_bottom_needed_link"}</a>
					</p>
				{else}
					<p class="u-ta-c">
						<a href="#gifts" class="btn btn--bd btn--xs">
							<span class="btn__text">
								<span class="btn__inner">
									{_"btn_pick_gift2"}
									{('angle-down')|icon, 'btn__icon'}
								</span>
							</span>
						</a>
					</p>
				{/if}
			{/capture}

			{include #delivery, textTop: $textTop->__toString(), textBottom: $textBottom->__toString(), separatedBottom: true,
				points: [
					['percentage' => 30, 'text' => '600 Kč<br> Malý dárek', icon: 'check'],
					['percentage' => 60, 'text' => '3 000 Kč Kč<br> Střední dárek', icon: 'gift'],
					['percentage' => 90, 'text' => '5 000 Kč Kč<br> Velký dárek', icon: 'gift']
				]
			}
		{else}
			{* Nemá dopravu zdarma *}
			{capture $amount}{$delta|money}{/capture}
			{if ($current->getAmount()->toFloat() * 100 / $total->getAmount()->toFloat()) >= 75}
				{capture $textBottom}
					<p class="item-icon">
						{('truck')|icon, 'item-icon__icon'}
						<span class="item-icon__text">
							{_"free_delivery_text_almost"|noescape|replace:'%amount',$amount->__toString()}
						</span>
					</p>
				{/capture}
			{else}
				{capture $target}{$total|money}{/capture}
				{capture $textBottom}
					<p class="item-icon">
						{('truck')|icon, 'item-icon__icon'}
						<span class="item-icon__text">
							{_"free_delivery_text"|noescape|replace:'%total',$target->__toString()}
						</span>
					</p>
				{/capture}
			{/if}


			{include #delivery, textBottom: $textBottom->__toString(),
				points: [
					['percentage' => 100, 'text' => ($total|money)]
				]
			}

		{/if}
	</div>
</div>
