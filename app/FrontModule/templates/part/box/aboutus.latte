{default $class = 'u-mb-sm u-mb-xl@md'}
{default $cf = $object->cf->homepage_aboutus ?? false}

<div n:if="$cf" n:class="b-aboutus, $class" n:ifcontent>
	<div class="b-aboutus__text" n:ifcontent>
		<h2 n:if="$cf->heading ?? false" class="u-mb-xxs u-mb-md@md">
			{$cf->heading}
		</h2>
		<div n:if="$cf->text ?? false" class="u-mb-last-0 u-mb-xxs u-mb-xs@md letter">
			{$cf->text|noescape}
		</div>
		{include $templates . '/part/core/person.latte', class=>false, person=>$cf->person ?? false}
	</div>

	<div n:if="$cf->images" n:ifcontent class="b-aboutus__cloud">
		{foreach $cf->images as $image}
			{php $image = $image ? $image->getEntity() ?? false : false}
			<div n:if="$image" class="b-aboutus__cloud-item">
				<div n:class="b-aboutus__img, $image->isSvg ? 'b-aboutus__img--svg'">
					{if $image->isSvg}
						<img src="{$image->url}" alt="{$image->getAlt($mutation)}" loading="lazy">
					{else}
						{php $img = $image->getSize('lg')}
						<img src="{$img->src}" alt="{$image->getAlt($mutation)}" loading="lazy" width="{$img->width}" height="{$img->height}">
					{/if}
				</div>
			</div>
		{/foreach}
	</div>
</div>