{default $class = 'u-mb-md u-mb-3xl@md'}
{default $image = false}
{default $text = false}

<div n:class="b-cta-box, $class, u-maw-8-12, u-mx-auto">
	<div class="grid grid--middle grid--x-0">
		<div n:if="$image" class="grid__cell size--5-12">
			<p class="b-cta-box__img">
				<img class="img img--4-3" src="{$image->getSize('md-4-3')->src}" alt="{$image->getAlt($mutation)}" loading="lazy">
			</p>
		</div>
		<div n:if="$text" class="grid__cell size--7-12">
			<div class="b-cta-box__content u-mb-last-0">
				{$text|noescape}
			</div>
		</div>
	</div>
</div>