{default $class = false}
{varType App\Model\Orm\Review\Review $review}

<article n:class="b-book-review, $class, link-mask, box">
	<p class="b-book-review__img-holder">
		<span class="b-book-review__bg img">
			<img n:if="$review->libraryImage !== null" src="{$review->libraryImage->getSize('sm')->src}" alt="{$review->name}" loading="lazy">
			<img n:if="$review->libraryImage === null" src="/static/img/illust/noimg.svg" alt="{$review->name}" loading="lazy">
		</span>
		<span class="b-book-review__img" n:if="$review->firstProduct !== null">
			<img n:if="$review->firstProduct->firstImage !== null" src="{$review->firstProduct->firstImage->getSize('sm')->src}" alt="{$review->firstProduct->nameAnchor}" loading="lazy">
		</span>
	</p>
	<div class="b-book-review__content">
		<div class="b-book-review__holder u-mb-last-0">
			<h3 class="b-book-review__title">
				<a href="{$review->url}" target="_blank" class="link-mask__link">
					{$review->name}
				</a>
			</h3>
			<p class="b-book-review__date">
				{$review->date|date: "j. n. Y"}
			</p>
			<p>
				{$review->perex}
			</p>
		</div>
	</div>
</article>
