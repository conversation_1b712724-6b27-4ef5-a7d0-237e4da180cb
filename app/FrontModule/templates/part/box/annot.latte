{default $class = 'u-mb-md u-mb-2xl@md'}
{default $name = $seoLink??->name ?? ($object->nameHeading ?? $object->name)}
{default $annotation = $seoLink??->description ?? $object??->annotation ?? null}
{default $innerClass = 'u-maw-8-12'}
{default $cf = $object->cf->base ?? false}

{default $decor = $cf && isset($cf->decor) ? $cf->decor->getEntity() ?? false : false}
{* {default $date = false} *}

<header n:ifcontent n:class="b-annot, $class">
	<div n:class="b-annot__inner, u-mb-last-0, $innerClass">
		{control breadcrumb}
		<h1 n:if="$name" class="b-annot__title">
			{control seoTools, $name} {* SeoTools/catalog.latte / SeoTools/common.latte *}
		</h1>
		<div n:ifcontent class="b-annot__annot u-mb-last-0">
			{$annotation|tables|lazyLoading|obfuscateEmailAddresses|noescape}
		</div>
		<img n:if="$decor" class="b-annot__decor img img--4-3 img--contain" src="{$decor->getSize('md')->src}" alt="" fetchpriority>
	</div>
</header>

{* <p n:if="$date ?? false" class="b-annot__date">
	{$object->publicFrom|date:"j. n. Y"}
</p> *}
{* <figure n:if="$img" class="b-annot__figure img img--7-3 u-mb-0">
	<img src="{$img->getSize('library')->src}" alt="" fetchpriority="high">
</figure> *}
