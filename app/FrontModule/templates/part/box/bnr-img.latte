{default $class = 'u-mb-0'}
{default $cf = $mutation->cf->bnrImg ?? false}

{php $img = isset($cf->image) ? $cf->image->getEntity() ?? false : false}
{php $link = $cf->link ?? false}

<p n:if="$img" n:class="$class">
	{if $link}
		{php $type = $link->toggle}
		{php $page = isset($link->systemHref) && isset($link->systemHref->page) ? $link->systemHref->page->getEntity() ?? false : false}
		{php $href = $link->customHref??->href ?? false}
		{php $hrefNameSystem = $link->systemHref??->hrefName ?? false}
		{php $hrefNameCustom = $link->customHref??->hrefName ?? false}

		{if $type == 'systemHref' && $page}
			<a href="{plink $page}">
				<img class="img img--7-3" src="{$img->getSize('2xl')->src}" alt="{if $hrefNameSystem}{$hrefNameSystem}{else}{$page->nameAnchor}{/if}" loading="lazy">
			</a>
		{elseif $type == 'customHref' && $href}
			<a href="{$href}" target="_blank">
				<img class="img img--7-3" src="{$img->getSize('2xl')->src}" alt="{$hrefNameCustom ?? false}" loading="lazy">
			</a>
		{/if}
	{else}
		<img class="img img--7-3" src="{$img->getSize('2xl')->src}" alt="" loading="lazy">
	{/if}
</p>