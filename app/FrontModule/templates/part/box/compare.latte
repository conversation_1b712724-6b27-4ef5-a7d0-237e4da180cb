{default $class = false}
{php $products = [1, 2, 3, 4, 5]} {* TODO *}
{php $paramGroups = ['Hlavní parametry', 'Konstrukce']} {* TODO *}

<section n:class="b-compare, $class, 'tw-mb-[2.8rem] md:tw-mb-[8rem]'" style="--products-count: {count($products)}" data-header-unpin data-controller="compare">
	<div class="b-compare__inner tw-grid tw-gap-[1.6rem_0] md:tw-gap-[2.4rem_0]">
		{* Horní lišta *}
		<div class="b-compare__top tw-gap-[0.8rem] md:tw-gap-[2rem] tw-grid tw-grid-cols-subgrid tw-col-span-full md:tw-mb-[-3.4rem]">
			{include $templates.'/part/form/compare.latte', class: 'max-md:tw-order-1', paramGroups: $paramGroups}

			<div n:tag-if="count($products) >= 5" class="b-compare__limiter tw-flex tw-flex-col tw-gap-[0.8rem] md:tw-contents">
				{foreach $products as $product}
					<div class="md:tw-py-[1rem]">
						{include $templates.'/part/box/compare-product.latte', class: 'b-compare-product--lg', dummyLongText: $iterator->counter == 2}
					</div>
				{/foreach}
			</div>
			<button n:if="count($products) >= 5" type="button" class="btn btn--bd tw-mt-[1.6rem] tw-justify-self-center tw-mb-0 md:tw-hidden" data-controller="toggle-class" data-action="toggle-class#toggle" data-toggle-content=".b-compare__limiter">
				<span class="btn__text">
					<span class="btn__inner">
						{_"btn_show_more_all"}
						{('angle-down')|icon, 'btn__icon'}
					</span>
				</span>
			</button>
			<div class="tw-pointer-events-none max-md:tw-hidden">{* fill empty space *}</div>
		</div>

		{* Sticky lišta *}
		<div class="b-compare__toolbar max-md:tw-hidden tw-sticky tw-z-[10] tw-top-[0] tw-gap-[2rem] tw-grid tw-grid-cols-subgrid tw-col-span-full tw-bg-white tw-h-[17.3rem] tw-mb-[-17.3rem]" data-controller="sticky">
			{include $templates.'/part/form/compare.latte', class: false, paramGroups: $paramGroups}
			{foreach $products as $product}
				<div class="tw-py-[1rem]">
					{include $templates.'/part/box/compare-product.latte', class: false, dummyLongText: $iterator->counter == 2}
				</div>
			{/foreach}
			<div class="tw-pointer-events-none max-md:tw-hidden">{* fill empty space *}</div>
		</div>

		{* Groups *}
		<div class="tw-grid tw-gap-[0.4rem_0] md:tw-gap-[2rem_0] tw-grid-cols-subgrid tw-col-span-full">
			{foreach $paramGroups as $group}
				<div class="b-compare__group tw-grid tw-grid-cols-subgrid tw-col-span-full" data-compare-target="group">
					<p class="b-compare__group-toggle tw-col-span-full tw-bg-bg tw-rounded-md is-open" data-controller="toggle-class">
						<button type="button" class="b-compare__group-name is-open as-link tw-sticky tw-left-[var(--row-main-gutter)] tw-bg-bg tw-rounded-md tw-no-underline tw-p-[1.6rem_2rem] tw-font-bold item-icon max-md:tw-w-full max-md:tw-justify-between" data-action="toggle-class#toggle">
							<span class="item-icon__text">{$group}</span>
							{('angle-down')|icon, 'item-icon__icon'}
						</button>
					</p>

					{* Parameters *}
					{php $params = ['Cena (s DPH)', 'Hmotnost v gramech', 'Max dosah']}
					<table class="b-compare__table tw-overflow-clip tw-grid tw-grid-cols-subgrid tw-col-span-full">
						<tbody class="tw-grid tw-grid-cols-subgrid tw-col-span-full">
							{foreach $params as $param}
								<tr id="{$group|webalize}_{$param|webalize}" n:class="'tw-grid tw-grid-cols-subgrid tw-col-span-full'" data-compare-target="param">
									<td class="b-compare__sticky b-compare__sticky--mask tw-bg-white max-md:tw-font-bold" data-controller="sticky">
										{$param}
									</td>
									{foreach $products as $product}
										{php $isBest = $iterator->counter == 2} {* TODO BE - nejlepsi parametr *}
										<td n:class="$isBest ? tw-text-status-valid, 'tw-flex tw-items-center tw-gap-[0.8rem]'" data-compare-target="value">
											<img class="img img--4-3 tw-w-[6rem] md:tw-hidden" src="/static/img/illust/noimg.svg" alt="Product name" loading="lazy">
											<span class="max-md:before:tw-content-[attr(data-name)] before:tw-block before:tw-text-[1.3rem] before:tw-leading-[1.4]" data-name="Product name">
												{if $param == 'Cena (s DPH)'}
													<span class="tw-leading-[1] max-md:tw-flex tw-gap-[1.2rem] tw-items-center">
														<s class="tw-text-help tw-text-[1.3rem]">29 999 Kč</s>
														<b class="tw-block">13 690 Kč</b>
														<span class="tw-text-help  tw-text-[1.3rem]">sleva 15 %</span>
													</span>
												{else}
													<span class="max-md:tw-text-bold">nějaká hodnota {$iterator->counter}</span>
												{/if}
											</span>
										</td>
									{/foreach}
									<td class="tw-pointer-events-none max-md:tw-hidden">{* fill empty space *}</td>
								</tr>
							{/foreach}
						</tbody>
					</table>
				</div>
			{/foreach}
		</div>
	</div>
</section>