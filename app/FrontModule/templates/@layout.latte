<!DOCTYPE html>
<html lang="{$mutation->langCode}" n:class="no-js, isset($htmlClass) ? $htmlClass">
	<head>
		<meta charset="utf-8">
		{varType App\Model\Template\PartCondition $partCondition}
		<!--[if IE]><meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><![endif]-->
		{var $keywords = $seoLink??->keywords ?? $object->keywords}
		{if $keywords !== null}
			<meta name="keywords" content="{$keywords}">
		{/if}

		{var $description = $seoLink??->description ?? $object->getDescription() ?? $object->annotation}
		{if $description !== null}
			<meta name="description" content="{$description|texy:true}">
		{/if}

		{control robots}
		{control canonicalUrl}
		<meta name="viewport" content="width=device-width, initial-scale=1">

		<title n:snippet="title">
			{var $nameTitle = $seoLink??->nameTitle ?? $object->getNameTitle()}
			{if $nameTitle !== null}
				{if isset($letter)}
					{capture $letterSeparator}{_"letter_separator"}{/capture}
					{var $nameTitle = $nameTitle . $letterSeparator . $letter}
				{/if}
				{control seoTools, $nameTitle, App\FrontModule\Components\SeoTools\SeoTools::TYPE_TITLE}
			{/if}
			{if !$isHomepage} {* Přídavek za title, který se dává jen pro ne homepage stránky *}
				| {_title}
			{/if}
		</title>

		{include 'part/tracking/cookiebot/consent.latte'}

		<link rel="preconnect" href="https://fonts.googleapis.com">
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
		<link href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Poppins:wght@400;600;700;800&display=swap" rel="stylesheet">

		{include 'part/head/style.latte', object=>$object}
		{include 'part/head/scripts.latte', object=>$object}


		{include 'part/head/meta.latte'}
		{if $partCondition->canShow(App\Model\Template\PartCondition::PART_STRUCTURED_DATA)}
			{include 'part/head/structured_data.latte'}
		{/if}

		{var App\Model\TagManager\TagManager $tagManager = $presenter->tagManager}
		{foreach $tagManager->setRenderPosition(App\Model\TagManager\TagManager::RENDER_POSITION_TOP) as $tag}
			{$tag}
		{/foreach}

		{include 'part/tracking/googleAnalytics.latte' showTop=>TRUE}
		{include 'part/tracking/cookiebot/cookiebot.latte'}

		{var $scripts = [
			'/static/js/app.js?t=' . $webVersion
		] }
		{foreach $scripts as $script}
			<link rel="preload" as="script" href="{$script}">
		{/foreach}
		{*
			<link rel="dns-prefetch" href="https://www.google-analytics.com">
			<link rel="dns-prefetch" href="https://www.googletagmanager.com"> {/gtm.js}
			<link rel="preconnect" href="https://www.google.com" crossorigin>
			<link rel="preconnect" href="https://www.youtube.com" crossorigin>
			<link rel="preconnect" href="https://connect.facebook.net" crossorigin>
			<link rel="preconnect" href="https://static.doubleclick.net" crossorigin>
			<link rel="preconnect" href="https://client.crisp.chat" crossorigin>
		*}

		{include 'part/tracking/bloomreach.latte'}
	</head>

	{default $isCompare = false}
	{php $bg = (isset($object->uid) && in_array($object->uid, ['ebooks']))
		|| (($object->parent->uid ?? false) == 'ebooks')
		|| in_array($object->template, [':Blog:Front:Blog:detail'])}
	<body id="top" n:class="$bg ? has-bg" data-controller="naja modal cookieBot">
		{include 'part/tracking/googleAnalytics.latte' showBottom=>TRUE}
		{include 'part/menu/accessibility.latte'}

		<div n:snippet="header" class="is-top">
			{if $partCondition->canShow(App\Model\Template\PartCondition::PART_HEADER)}
				{control siteHeader}
			{/if}
		</div>

		<main id="main" n:class="main, $isCompare ? main--scrollable">
			{if $partCondition->canShow(App\Model\Template\PartCondition::PART_CONTENT)}
				{include #content}

				{if !$isOrder}
					{* {php $isUserPage = str_contains($object->template, ':Front:User:')} *}
					{* {php $isCatalog = str_contains($object->template, ':Front:Catalog:') || in_array($object->template, [':Writer:Front:Writer:detail', ':Publisher:Front:Publisher:detail', ':Theme:Front:Theme:detail'])} *}
					{* {php $noPadding = isset($object->uid) && $object->uid == '404'} *}

					{* <div n:if="$marketingConsent" n:ifcontent n:class="!$isUserPage && !$isCatalog ? u-bgc-white, !$noPadding ? 'u-pt-sm u-pt-lg@md'">
						<div n:ifcontent class="row-main">
							<div n:ifcontent class="u-mb-md u-mb-2xl@md u-mb-last-0">
								{control lastVisitedProducts}
							</div>
						</div>
					</div> *}
					{* {include 'part/box/benefits.latte', class=>'b-benefits--footer', items=>$object->mutation->cf->benefits_footer ?? []} *}
				{/if}
			{/if}
		</main>

		{if $partCondition->canShow(App\Model\Template\PartCondition::PART_FOOTER)}
			{include 'part/footer.latte', isOrder=>$isOrder}

			{* {if $mutation->isEnabledCookieModal}
				{include 'part/box/cookie.latte'}
			{/if} *}

			{include 'part/box/forgotten.latte'}

			{control editButton}

			{* Tags from TagManager must be at the bottom of page *}
			<div n:snippet="tags" data-naja-snippet-append data-naja-history="off">
				{var App\Model\TagManager\TagManager $tagManager = $presenter->tagManager}
				{foreach $tagManager->setRenderPosition(App\Model\TagManager\TagManager::RENDER_POSITION_BOTTOM) as $tag}
					{$tag}
				{/foreach}
			</div>
		{/if}

		<div class="body-loader__loader"></div>

		<div class="overlay"></div>

		{foreach $scripts as $script}
			<script src="{$script}"></script>
		{/foreach}

		<script>
			var smartform = smartform || {};
			smartform.beforeInit = function initialize() {
				smartform.setClientId('YwOAw9XC1m');
				// YwOAw9XC1m - SK test
				// ???? - client
			}
		</script>

		{* {if $isEnvProduction && $partCondition->canShow(App\Model\Template\PartCondition::PART_SPECULATION_RULES)}
			{include 'part/head/speculation-rules.latte'}
		{/if} *}

		<script>
			App.run({
				apiKey: {$googleApiKey},
				assetsUrl: '/static/',
				mutationId: {$mutation->id},
				exponeaTrack: {(int) ($cfg('exponea') !== null && $cfg('exponea')['projectToken'] !== '')}
			});
		</script>
	</body>
</html>
