{varType App\Model\Orm\Order\Order $order}
<h2><PERSON><PERSON><PERSON> objedn<PERSON>ky</h2>
<table>
	<thead>
	<tr>
		<th><PERSON><PERSON><PERSON></th>
		<th>Množství</th>
		<th>Cena / jedn. DPH</th>
		<th>Sazba DPH</th>
		<th>Cena s DPH</th>
	</tr>
	</thead>

	<tbody>
	{foreach $order->getBuyableItems() as $item}
		<tr>
			<td>{$item->getName()}</td>
			<td>{$item->amount}</td>
			<td>{$item->unitPriceVat|money}</td>
			<td><span n:if="$item->vatRateValue !== null">{$item->vatRateValue->toInt()}%</span></td>
			<td>{$item->totalPriceVat|money}</td>
		</tr>
	{/foreach}
	<tr>
		<td colspan="2"><strong>Doprava:</strong> {$order->delivery->getName()}</td>
		<td>{$order->delivery->totalPrice|money}</td>
		<td><span n:if="$item->vatRateValue !== null">{$item->vatRateValue->toInt()}%</span></td>
		<td>{$order->delivery->totalPriceVat|money}</td>
	</tr>
	<tr>
		<td colspan="2"><strong>Platba:</strong> {$order->payment->getName()}</td>
		<td>{$order->payment->totalPrice|money}</td>
		<td>{$order->payment->vatRate->value} <span n:if="$order->payment->vatRateValue !== null">({$order->payment->vatRateValue->toInt()}%)</span></td>
		<td>{$order->payment->totalPriceVat|money}</td>
	</tr>
	</tbody>
</table>
