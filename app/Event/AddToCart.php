<?php

declare(strict_types=1);

namespace App\Event;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\State\State;
use App\Model\ShoppingCart\ShoppingCartInterface;
use Brick\Money\Currency;
use Symfony\Contracts\EventDispatcher\Event;

final class AddToCart extends Event
{

	public function __construct(
		public readonly ShoppingCartInterface $shoppingCart,
		public readonly ProductLocalization $productLocalization,
		public readonly ProductVariant $variant,
		public readonly Mutation $mutation,
		public readonly State $state,
		public readonly PriceLevel $priceLevel,
		public readonly Currency $currency,
		public readonly int $quantity = 1,
		public readonly ?string $listId = null,
		public readonly ?string $listName = null,
	)
	{
	}

}
