<?php

declare(strict_types=1);

namespace App\Event;

use Nette\Utils\Strings;
use App\Model\Orm\User\User;
use Symfony\Contracts\EventDispatcher\Event;

final class UserUpdate extends Event
{

	public function __construct(
		public array $customAddress,
		public readonly string $email,
		public readonly ?User $user = null,
	)
	{
		$this->normalizeCustomAddress();
	}

	private function normalizeCustomAddress(): void
	{
		foreach ($this->customAddress as $key => $customAddress) {
			if (str_contains($key, 'inv_')) {
				$newKey = 'inv' . Strings::firstUpper(str_replace('inv_', '', $key));
				$this->customAddress[$newKey] = $customAddress;
			}
		}
	}

}
