<?php

declare(strict_types=1);

namespace App\Event;

use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\PostType\Page\Model\Orm\CatalogTree;
use Symfony\Contracts\EventDispatcher\Event;

final class SearchResuls extends Event
{

	public int $productCount = 0;

	public int $categoryCount = 0;

	public function __construct(
		public array $fulltextResults = [],
		public ?string $searchTerm = null,
		public bool $hasResults = false,
	)
	{
		$this->categoryCount  = count($this->fulltextResults[CatalogTree::class] ?? []);
		$this->productCount   = $this->fulltextResults[ProductLocalization::class]->totalCount;
	}

}
