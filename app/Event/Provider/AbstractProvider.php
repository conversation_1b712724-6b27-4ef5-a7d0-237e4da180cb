<?php

declare(strict_types=1);

namespace App\Event\Provider;

use Nette\Http\Request;
use Nette\Http\Response;
use Nette\Utils\Strings;

abstract class AbstractProvider
{

	public function __construct(
		private readonly Request $httpRequest,
		private readonly Response $httpResponse,
	)
	{
	}

	abstract protected function getCookiePrefix(): string;

	public function __get(string $name): ?string
	{
		return $this->httpRequest->getCookie($this->getCookiePrefix() . Strings::firstUpper($name));
	}


	public function getCookieNames(): array
	{
		$reflection = new \ReflectionClass($this);

		$docComment = $reflection->getDocComment();
		if ($docComment === false) {
			return [];
		}
		$list = [];
		preg_match_all('#@(.*?)\n#s', $docComment, $annotations);
		foreach ($annotations[1] as $annotation) {
			if (!str_starts_with($annotation, 'property-read')) {
				continue;
			}
			[, $propertyName] = explode('$', $annotation);
			$list[] = trim($this->getCookiePrefix() . Strings::firstUpper($propertyName));
		}

		return $list;
	}

	public function flush(): void
	{
		foreach ($this->getCookieNames() as $cookieName) {
			$this->httpResponse->deleteCookie($cookieName);
		}
	}

}
