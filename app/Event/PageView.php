<?php

declare(strict_types=1);

namespace App\Event;

use App\Model\Orm\Routable;
use App\Model\Orm\User\User;
use App\Model\StaticPage\StaticPage;
use Nette\Application\IPresenter;
use Nette\Http\IRequest;
use Symfony\Contracts\EventDispatcher\Event;

final class PageView extends Event
{

	public function __construct(
		public readonly IPresenter $presenter,
		public readonly Routable|StaticPage $routableEntity,
		public readonly IRequest $httpRequest,
		public readonly ?User $user = null
	)
	{
	}

}
