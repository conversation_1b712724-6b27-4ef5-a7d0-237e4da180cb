<?php declare(strict_types = 1);

namespace App\PostType\CalendarTag\Model;

use App\PostType\CalendarTag\Model\Orm\CalendarTag\CalendarTag;
use App\PostType\CalendarTag\Model\Orm\CalendarTag\CalendarTagLocalization;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\PostType\Core\Model\EntityLocalizationFacade;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;

class CalendarTagLocalizationFacade implements EntityLocalizationFacade
{

	public function __construct(
		private Orm $orm,
	)
	{
	}


	public function create(Mutation $mutation, ParentEntity|null $localizableEntity): LocalizationEntity
	{
		$localization = new CalendarTagLocalization();
		$this->orm->calendarTagLocalization->attach($localization);
		$localization->mutation = $mutation;

		if ($localizableEntity === null) {
			$localizableEntity = new CalendarTag();
			$localization->calendarTag = $localizableEntity;
		} else {
			assert($localizableEntity instanceof CalendarTag);
			$localization->calendarTag = $localizableEntity;
		}

		$this->orm->persistAndFlush($localization);

		return $localization;
	}


	public function remove(LocalizationEntity $localizableEntity): void
	{
		assert($localizableEntity instanceof CalendarTagLocalization);

		$parent = $localizableEntity->getParent();
		$this->orm->calendarTagLocalization->remove($localizableEntity);

		if ($parent->localizations->count() === 0) {
			$this->orm->calendarTag->remove($parent);
		}

		$this->orm->flush();
	}

}
