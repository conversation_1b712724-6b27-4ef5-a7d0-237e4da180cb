<?php declare(strict_types = 1);

namespace App\PostType\CalendarTag\Model\Orm\CalendarTag;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\RoutableEntity;
use App\Model\Orm\Traits\HasCustomContent;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\Calendar\Model\Orm\CalendarLocalization;
use App\PostType\Calendar\Model\Orm\CalendarLocalizationRepository;
use App\PostType\Core\Model\Editable;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Core\Model\Publishable;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;
use App\Model\Orm\JsonContainer; // phpcs:ignore

/**
 * @property int $id {primary}
 * @property string $name  {default ''}
 * @property bool $public {default false}
 * @property DateTimeImmutable|null $editedTime
 * @property int|null $edited
 * @property int $sort {default 0}
 *
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 * @property ArrayHash $customContentJson {container JsonContainer}
 *
 * RELATIONS
 * @property CalendarTag $calendarTag {M:1 CalendarTag::$localizations}
 * @property Mutation $mutation {m:1 Mutation, oneSided=true}
 *
 *
 * VIRTUAL
 * @property ArrayHash|null $cf {virtual}
 * @property ArrayHash|null $cc {virtual}
 * @property-read  ICollection<CalendarLocalization> $calendarsPublic {virtual}
 * @property-read string $template {virtual}
 */
class CalendarTagLocalization extends RoutableEntity implements LocalizationEntity, Publishable, Editable
{

	//use HasPublishable;

	use HasCustomFields;
	use HasCustomContent;

	private CalendarLocalizationRepository $calendarLocalizationRepository;

	public function injectCalendarRepository(CalendarLocalizationRepository $calendarLocalizationRepository): void
	{
		$this->calendarLocalizationRepository = $calendarLocalizationRepository;
	}


	protected function getterTemplate(): string
	{
		return ':CalendarTag:Front:CalendarTag:detail';
	}


	protected function getterPath(): array
	{
		$calendarPage = $this->mutation->pages->calendar;
		$path = $calendarPage->path;
		$path[] = $calendarPage->id;
		return $path;
	}


	/**
	 * @return ICollection<CalendarLocalization>
	 */
	protected function getterCalendarsPublic(): ICollection
	{
		$calendarIds = $this->calendarTag->calendars->toCollection()->fetchPairs(null, 'id');
		return $this->calendarLocalizationRepository
			->findBy([
					'calendar' => $calendarIds,
					'mutation' => $this->mutation,
				])
			->findBy($this->calendarLocalizationRepository->getPublicOnlyWhereParams());
	}

	public function getId(): int
	{
		return $this->id;
	}

	public function getMutation(): Mutation
	{
		return $this->mutation;
	}


	public function setMutation(Mutation $mutation): void
	{
		$this->mutation = $mutation;
	}


	public function getParent(): CalendarTag
	{
		return $this->calendarTag;
	}


	public function setParent(ParentEntity $parentEntity): void
	{
		assert($parentEntity instanceof CalendarTag);
		$this->calendarTag = $parentEntity;
	}

	public function getName(): string
	{
		return $this->name;
	}

	public function setName(string $name): void
	{
		$this->name = $name;
	}

	public function setEditorId(int $id): void
	{
		$this->edited = $id;
	}

	public function setEditedTime(DateTimeImmutable $editedTime): void
	{
		$this->editedTime = $editedTime;
	}

	public function getIsPublic(): bool
	{
		return $this->public;
	}

	public function setIsPublic(bool $isPublic): void
	{
		$this->public = $isPublic;
	}

}
