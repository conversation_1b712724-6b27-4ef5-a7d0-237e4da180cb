<?php declare(strict_types = 1);

namespace App\PostType\CalendarTag\Model\Orm\CalendarTag;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Repository\QueryForIdsByMutation;
use App\Model\Orm\Traits\HasPublicParameter;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method CalendarTagLocalization getById($id)
 * @method ICollection<CalendarTagLocalization> searchByName(string $q, array $excluded)
 * @method array findAllIds(?int $limit)
 * @extends Repository<CalendarTagLocalization>
 */
final class CalendarTagLocalizationRepository extends Repository implements QueryForIdsByMutation
{

	use HasPublicParameter;

	public static function getEntityClassNames(): array
	{
		return [CalendarTagLocalization::class];
	}

	public function findAllIdsInMutation(Mutation $mutation, ?int $limit = null): Result
	{
		$mapper = $this->mapper;
		assert($mapper instanceof CalendarTagLocalizationMapper);

		return $mapper->findAllIdsInMutation($mutation, $limit);
	}

	public function findForSelectList(?Mutation $mutation = null): array
	{
		$ret = [];
		$items = $this->findBy(array_filter(['mutation' => $mutation]))->orderBy('name');
		foreach ($items as $item) {
			assert($item instanceof CalendarTagLocalization);
			$ret[] = (object) [
				'id' => $item->getParent()->id,
				'name' => $item->name,
			];
		}

		return $ret;
	}

		public function getPublicOnlyWhereParams(): array
		{
		return [
			'public' => 1,
		];
		}

}
