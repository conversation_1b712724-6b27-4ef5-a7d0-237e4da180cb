<?php declare(strict_types = 1);

namespace App\PostType\CalendarTag\Model\Orm\CalendarTag;

use App\Model\Orm\CollectionById;
use App\Model\Orm\Searchable;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method CalendarTag getById($id)
 * @method ICollection<CalendarTag> findByExactOrder(array $ids)
 * @extends Repository<CalendarTag>
 */
final class CalendarTagRepository extends Repository implements CollectionById, Searchable
{

	public static function getEntityClassNames(): array
	{
		return [CalendarTag::class];
	}

	/**
	 * @return ICollection<CalendarTag>
	 */
	public function findByIdOrder(array $ids): ICollection
	{
		return $this->findByExactOrder($ids);
	}

	/**
	 * @return ICollection<CalendarTag>
	 */
	public function searchByName(string $string, array $excludedIds = []): ICollection
	{
		return $this->getMapper()->searchByName($string, $excludedIds);
	}

	public function getMapper(): CalendarTagMapper
	{
		$mapper = parent::getMapper();
		assert($mapper instanceof CalendarTagMapper);
		return $mapper;
	}

}
