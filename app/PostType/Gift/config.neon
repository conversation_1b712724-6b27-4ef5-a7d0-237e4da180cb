application:
	mapping:
		Gift: App\PostType\Gift\*Module\Presenters\*Presenter

parameters:
	postTypeRoutes:
		Gift: gift
cf:
	templates:
		gift:
			images:
				type: group
				items:
					mainImage:
						type: image # has size
						multiple: false
						label: "Fotografie (min. ší<PERSON>ka 320px)"


services:
	- App\PostType\Gift\Model\GiftLocalizationFacade
	- App\PostType\Gift\AdminModule\Components\GiftDataGrid\GiftDataGridPrescription
	- App\PostType\Gift\AdminModule\Components\Form\GiftLocalizationFormPrescription
	- App\PostType\Gift\AdminModule\Components\ShellForm\ShellFormPrescription
	- App\PostType\Gift\AdminModule\Components\Products\ProductsFactory
