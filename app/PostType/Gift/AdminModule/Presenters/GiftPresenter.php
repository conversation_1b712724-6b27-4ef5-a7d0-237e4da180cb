<?php declare(strict_types = 1);

namespace App\PostType\Gift\AdminModule\Presenters;

use App\AdminModule\Components\ItemSearch\FoundItem;
use App\AdminModule\Components\ItemSearch\ItemSearch;
use App\AdminModule\Components\ItemSearch\ItemSearchFactory;
use App\AdminModule\Presenters\BasePresenter;
use App\Model\ElasticSearch\All\Repository;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Orm\Product\Product;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGrid;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGridFactory;
use App\PostType\Core\AdminModule\Components\Form\Form;
use App\PostType\Core\AdminModule\Components\Form\FormFactory;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellForm;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellFormFactory;
use App\PostType\Gift\AdminModule\Components\Form\GiftLocalizationFormPrescription;
use App\PostType\Gift\AdminModule\Components\GiftDataGrid\GiftDataGridPrescription;
use App\PostType\Gift\AdminModule\Components\Products\ProductsFactory;
use App\PostType\Gift\AdminModule\Components\ShellForm\ShellFormPrescription;
use App\PostType\Gift\Model\GiftLocalizationFacade;
use App\PostType\Gift\Model\Orm\Gift\GiftLocalization;
use App\PostType\Gift\AdminModule\Components\Products\Products;
use Nette\Application\Attributes\Persistent;
use Nextras\Dbal\Drivers\Exception\UniqueConstraintViolationException;

final class GiftPresenter extends BasePresenter
{

	private const ORM_REPOSITORY_NAME = 'gift';

	#[Persistent]
	public int $id;

	private GiftLocalization $giftLocalization;

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
		private readonly FormFactory $formFactory,
		private readonly ShellFormFactory $shellFormFactory,
		private readonly ShellFormPrescription $shellFormPrescription,
		private readonly GiftDataGridPrescription $giftDataGridPrescription,
		private readonly GiftLocalizationFacade $giftLocalizationFacade,
		private readonly GiftLocalizationFormPrescription $giftLocalizationFormPrescription,
		private readonly ProductsFactory $productsFactory,
		private readonly ItemSearchFactory $itemSearchFactory,
		private readonly EsIndexRepository $esIndexRepository,
		private readonly Repository $esAllRepository,
	)
	{
	}

	public function actionEdit(int $id): void
	{
		$giftLocalization = $this->orm->giftLocalization->getById($id);
		if ($giftLocalization === null) {
			$this->redirect('default');
		}

		$this->giftLocalization = $giftLocalization;
	}

	public function actionProducts(int $id): void
	{
		$giftLocalization = $this->orm->giftLocalization->getById($id);
		if ($giftLocalization === null) {
			$this->redirect('default');
		}

		$this->id = $id;
		$this->giftLocalization = $giftLocalization;
	}


	public function renderProducts(int $id): void
	{
		$this->template->add('sectionTitle', $this->translator->translate('gift_product_search_title'));
		$this->template->add('giftLocalization', $this->giftLocalization);
	}

	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create(self::ORM_REPOSITORY_NAME, $this->orm->giftLocalization->findAll(), $this->giftDataGridPrescription->getPrescription());
	}

	public function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create(
			entity: null,
			facade: $this->giftLocalizationFacade,
			formDefinition: $this->shellFormPrescription->getPrescription()
		);
	}

	protected function createComponentForm(): Form
	{
		$userEntity = $this->userEntity;
		$rawPostData = $this->getRequest()->getPost();

		return $this->formFactory->create(
			entityLocalizationFacade: $this->giftLocalizationFacade,
			entityLocalization: $this->giftLocalization,
			userEntity: $userEntity,
			formDefinition: $this->giftLocalizationFormPrescription->getPrescription($this->giftLocalization, $rawPostData)
		);
	}

	protected function createComponentProducts(): Products
	{
		return $this->productsFactory->create($this->giftLocalization);
	}

	public function createComponentProductSearch(): ItemSearch
	{
		$clickItemFunction = function ($foundItemId): void {
			try {
				$this->giftLocalization->getParent()->products->add($foundItemId);
				$this->orm->persistAndFlush($this->giftLocalization->getParent());
			} catch (UniqueConstraintViolationException) {
				// ignore duplicity
			}
			$this->redrawControl('productList');
		};

		$searchItemFunction = function (string $searchString): array {
			$esIndex = $this->esIndexRepository->getAllLastActive($this->orm->mutation->getRsDefault());
			if ($esIndex !== null) {
				/** @var array<Product> $products */
				$products = $this->esAllRepository->searchProduct($esIndex, $searchString)->fetchAll();
				return array_map(fn(Product $product) => new FoundItem($product->id, '[' . $product->firstVariant->code . '] ' . $product->internalName . ($product->isDamaged ? ' (' . $product->damagedType . ')' : '')), $products);
			}
			return [];
		};

		return $this->itemSearchFactory->create($searchItemFunction, $clickItemFunction);
	}

}
