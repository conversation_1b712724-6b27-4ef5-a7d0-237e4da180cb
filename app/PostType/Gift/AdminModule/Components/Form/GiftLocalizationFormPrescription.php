<?php declare(strict_types=1);

namespace App\PostType\Gift\AdminModule\Components\Form;

use App\Model\Currency\CurrencyHelper;
use App\Model\CustomField\SuggestUrls;
use App\Model\Orm\Orm;
use App\Model\Orm\Price;
use App\PostType\Core\AdminModule\Components\Form\Builder;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\CustomFormExtender;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\CommonTemplatePart;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\RelationTemplatePart;
use App\PostType\Core\AdminModule\Components\Form\Definition\FormDefinition;
use App\PostType\Core\AdminModule\Components\Form\Definition\RelationInfoFactory;
use App\PostType\Core\AdminModule\Components\Form\Handler;
use App\PostType\Gift\AdminModule\Components\Form\FormData\GiftLocalizationFormData;
use App\PostType\Gift\Model\Orm\Gift\GiftLocalization;
use Brick\Math\BigDecimal;
use Brick\Money\Money;
use Nette\Application\UI\Form;
use Nextras\Dbal\Utils\DateTimeImmutable;

readonly class GiftLocalizationFormPrescription
{

	public function __construct(
		private RelationInfoFactory $relationInfoFactory,
		private SuggestUrls $urls,
		private Builder $coreBuilder,
		private Handler $coreHandler,
		private Orm $orm,
	)
	{
	}

	public function getPrescription(GiftLocalization $giftLocalization, array $rawPostData): FormDefinition
	{
		$form = new Form();
		$form->setMappedType(GiftLocalizationFormData::class);

		$extenders = [];

		$extenders[] = new CustomFormExtender(
			addHandler: function (Form $form) {
			},
			successHandler: function (Form $form, GiftLocalizationFormData $data) {
			},
			templateParts: [
				new CommonTemplatePart(
					templateFile: __DIR__ . '/templates/side.latte',
					type: CommonTemplatePart::TYPE_SIDE,
					parameters: [
						'giftLocalization' => $giftLocalization,
					]
				),
			]
		);

		$extenders[] = $this->addProduct($form, $giftLocalization);
		$extenders[] = $this->addConditions($form, $giftLocalization);

		return new FormDefinition(
			form: $form,
			extenders: $extenders,
			templateParameters: [
				'title' => $giftLocalization->getParent()->internalName,
			]
		);
	}

	private function addProduct(Form $form, GiftLocalization $giftLocalization): CustomFormExtender
	{
		$productRelationsInfo = $this->relationInfoFactory->create(
			sourceEntity: $giftLocalization->getParent(),
			propertyName: 'product',
			suggestUrl: $this->urls['searchProduct'],
			toggleName: 'Spárovat s existujícim produktem'
		);

		return new CustomFormExtender(
			addHandler: function (Form $form) use ($productRelationsInfo) {
				$this->coreBuilder->addHasOneRelation($form, $productRelationsInfo);
			},
			successHandler: function (Form $form, GiftLocalizationFormData $data) use ($productRelationsInfo, $giftLocalization) {
				$this->updateProductGiftDate($giftLocalization);
				$this->coreHandler->handleHasOneRelation($data->{$productRelationsInfo->propertyName}->id, $productRelationsInfo);
				$this->updateProductGiftDate($giftLocalization);
			},
			templateParts: [
				new RelationTemplatePart(
					relationInfo: $productRelationsInfo,
					templateFile: APP_DIR . '/PostType/Core/AdminModule/Components/Form/parts/relation.latte',
					type: RelationTemplatePart::TYPE_RELATION
				),
			]
		);
	}

	private function updateProductGiftDate(GiftLocalization $giftLocalization): void
	{
		if ($giftLocalization->getParent()->product !== null) {
			$giftLocalization->getParent()->product->giftDate = new DateTimeImmutable();
			$this->orm->product->persist($giftLocalization->getParent()->product);
		}
	}

	private function addConditions(Form $form, GiftLocalization $giftLocalization): CustomFormExtender
	{
		return new CustomFormExtender(
			addHandler: function (Form $form) use ($giftLocalization) {
				$container = $form->addContainer('conditions');
				$container->addInteger('sort', 'sort')->setDefaultValue($giftLocalization->getParent()->sort);
				$container->addCheckbox('onFirstOrder', 'gift_on_first_order')->setDefaultValue($giftLocalization->getParent()->onFirstOrder);
				$container->addFloat('minPrice', 'gift_min_price')->setNullable()->setDefaultValue($giftLocalization->getParent()->minPrice);
				$container->addInteger('minCount', 'gift_min_count')->setNullable()->setDefaultValue($giftLocalization->getParent()->minCount);
				$container->addFloat('price_amount', 'gift_price_amount')->setDefaultValue($giftLocalization->getParent()->price->amount);
				$container->addSelect('price_currency', 'gift_price_currency', CurrencyHelper::CURRENCIES_SELECT)->setDefaultValue($giftLocalization->getParent()->price->currency);
			},
			successHandler: function (Form $form, GiftLocalizationFormData $data) use ($giftLocalization) {
				//bdump($data->conditions);
				$gift = $giftLocalization->getParent();
				$gift->sort = $data->conditions->sort;
				$gift->minCount = $data->conditions->minCount;
				$gift->minPrice = $data->conditions->minPrice !== null ? BigDecimal::of($data->conditions->minPrice) : null;
				$gift->onFirstOrder = $data->conditions->onFirstOrder;
				$gift->price = Price::from(Money::of($data->conditions->price_amount, $data->conditions->price_currency));

				$this->orm->gift->persist($gift);
			},
			templateParts: [
				new CommonTemplatePart(
					templateFile: __DIR__ . '/templates/conditions.latte',
					type: CommonTemplatePart::TYPE_MAIN,
					parameters: [
						'giftLocalization' => $giftLocalization,
					]
				),
			]
		);
	}

}
