<p class="u-mb-xs">
	{varType App\PostType\Gift\Model\Orm\Gift\GiftLocalization $giftLocalization}
	{if ($product = $giftLocalization->gift->product) !== null}
		<div class="b-std u-mb-sm">
			<h2 class="b-std__title title">
				Stav připojeného produktu
			</h2>
			<div class="b-std__content">
				<div class="u-mb-sm" {if $product->firstVariant->totalSupplyCount > 0}style="color:green"{else}style="color:red"{/if}>
					<strong>{$productDto->productAvailabilityAvailabilityStateText}</strong> ({if $product->firstVariant->totalSupplyCount > 0}zobrazeno{else}nezobrazeno{/if})
				</div>
			</div>
		</div>
	{/if}

	<a href="{plink "products", id => $giftLocalization->id}" class="btn btn--full btn--grey" target="_blank">
		<span class="btn__text item-icon">
			<span class="item-icon__icon icon">
				{include $templates.'/part/icons/external-link-alt.svg'}
			</span>
			<span class="item-icon__text">
				<span class="grid-inline"> <span>Skupina produktů ({$giftLocalization->getParent()->products->countStored()})</span></span>
			</span>
		</span>
	</a>
</p>
