<?php declare(strict_types = 1);

namespace App\PostType\Gift\AdminModule\Components\Products;

use App\Model\Orm\Product\Product;
use App\PostType\Gift\Model\Orm\Gift\GiftLocalization;
use App\PostType\Gift\Model\Orm\Gift\GiftRepository;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use App\Model\Translator;
use Ublaboo\DataGrid\DataGrid;

/**
 * @property-read DefaultTemplate $template
 */
final class Products extends UI\Control
{

	public function __construct(
		private readonly GiftLocalization $giftLocalization,
		private readonly Translator $translator,
		private readonly GiftRepository $giftRepository,
	)
	{
	}

	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->render(__DIR__ . '/products.latte');
	}

	public function createComponentGrid(): DataGrid
	{
		$grid = new DataGrid();

		$grid->setDataSource($this->giftLocalization->getParent()->products->toCollection());
		$grid->addColumnText('code', 'code')->setRenderer(fn(Product $product) => $product->firstVariant->code);
		$grid->addColumnText('internalName', 'internalName')->setSortable()->setFilterText();
		$grid->addColumnText('damaged', 'isDamaged')->setRenderer(fn(Product $product) => $product->damagedType);

		$groupDelete = $grid->addGroupButtonAction('delete_button');
		$groupDelete->setClass('btn btn-xs btn-danger ajax group-delete-button');
		$groupDelete->setAttribute('data-datagrid-confirm', $this->translator->translate('delete_confirms'));
		$groupDelete->onClick[] = $this->deleteSelected(...);

		$grid->setTranslator($this->translator);

		return $grid;
	}

	public function deleteSelected(array $items): void
	{
		foreach ($items as $item) {
			$this->giftLocalization->getParent()->products->remove($item);
			$this->giftRepository->persistAndFlush($this->giftLocalization->getParent());
		}
	}

}
