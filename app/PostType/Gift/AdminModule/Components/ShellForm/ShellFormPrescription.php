<?php declare(strict_types=1);

namespace App\PostType\Gift\AdminModule\Components\ShellForm;

use App\PostType\Core\AdminModule\Components\ShellForm\Definition\FormDefinition;
use Nette\Application\UI\Form;

class ShellFormPrescription
{

	public function __construct()
	{
	}

	public function getPrescription(): FormDefinition
	{
		$extenders = [];

		return new FormDefinition(
			form: $this->createForm(),
			extenders: $extenders,
		);
	}

	private function createForm(): Form
	{
		$form = new Form();
		$form->setMappedType(GiftShellFormData::class);
		return $form;
	}

}
