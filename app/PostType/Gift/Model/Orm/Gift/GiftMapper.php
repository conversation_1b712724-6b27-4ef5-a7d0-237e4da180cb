<?php declare(strict_types = 1);

namespace App\PostType\Gift\Model\Orm\Gift;

use App\Model\Currency\CurrencyHelper;
use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Orm\Mapper\Dbal\Conventions\IConventions;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<Gift>
 */
final class GiftMapper extends DbalMapper
{

	use HasCamelCase;

	/**
	 * @return literal-string
	 */
public function getTableName(): string
{
	return 'gift';
}

	protected function createConventions(): IConventions
	{
		$conventions = parent::createConventions();
		$conventions->setMapping('price->amount', 'price_amount', toStorageCb: function ($val) {
			return $val ?? '0';
		});
		$conventions->setMapping('price->currency', 'price_currency', toStorageCb: function ($val) {
			return $val ?? CurrencyHelper::CURRENCY_CZK;
		});
		return $conventions;
	}

	public function getProductIds(int $giftId): array
	{
		return $this->connection->query('SELECT `productId` FROM `gift_x_product` WHERE `giftId` = %i', $giftId)->fetchPairs(null, 'productId');
	}

}
