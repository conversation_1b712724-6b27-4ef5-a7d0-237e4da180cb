<?php declare(strict_types = 1);

namespace App\PostType\Gift\Model\Orm\Gift;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Price;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use Brick\Math\BigDecimal;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;
use App\Model\Orm\BigDecimalContainer; // phpcs:ignore
use App\Model\Orm\JsonContainer; // phpcs:ignore


/**
 * @property int $id {primary}
 * @property string $internalName {default ''}
 *
 * @property int $sort {default 0}
 * @property bool $onFirstOrder {default false}
 * @property Price $price {embeddable}
 * @property BigDecimal|null $minPrice {wrapper BigDecimalContainer}
 * @property int|null $minCount {default null}
 *
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 *
 * RELATIONS
 * @property OneHasMany<GiftLocalization> $localizations {1:M GiftLocalization::$gift}
 * @property Product|null $product {m:1 Product, oneSided=true}
 * @property ManyHasMany<Product> $products {m:m Product, isMain=true, oneSided=true}
 *
 * VIRTUAL
 * @property ArrayHash|null $cf {virtual}
 */
final class Gift extends BaseEntity implements ParentEntity
{

	use HasCustomFields;

	private GiftRepository $giftRepository;

	public function injectService(GiftRepository $giftRepository): void
	{
		$this->giftRepository = $giftRepository;
	}

	public function getProductIds(): array
	{
		return $this->giftRepository->getProductIds($this->id);
	}

	public function getInternalName(): string
	{
		return $this->internalName;
	}

	public function setInternalName(string $internalName): void
	{
		$this->internalName = $internalName;
	}
	/**
	 * @return ICollection<GiftLocalization>
	 */
	public function getLocalizations(): ICollection
	{
		return $this->localizations->toCollection();
	}

	public function getLocalization(Mutation $mutation): LocalizationEntity
	{
		$localization = $this->getLocalizations()->getByChecked(['mutation' => $mutation]);
		assert($localization instanceof GiftLocalization);
		return $localization;
	}

}
