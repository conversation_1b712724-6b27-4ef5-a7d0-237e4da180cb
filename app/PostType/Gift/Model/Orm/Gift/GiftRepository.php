<?php declare(strict_types = 1);

namespace App\PostType\Gift\Model\Orm\Gift;

use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method Gift|null getById(int $id)
 * @method Gift|null getBy(array $conds)
 * @method ICollection<Gift> findBy(array $conds)
 * @method ICollection<Gift> findByIds(array $conds)
 * @extends Repository<Gift>
 */
final class GiftRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [Gift::class];
	}

	public function getProductIds(int $giftId): array
	{
		$mapper = $this->getMapper();
		assert($mapper instanceof GiftMapper);

		return $mapper->getProductIds($giftId);
	}

}
