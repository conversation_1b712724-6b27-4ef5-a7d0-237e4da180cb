<?php declare(strict_types = 1);

namespace App\PostType\Gift\Model\Orm\Gift;

use App\Exceptions\LogicException;
use App\Infrastructure\Latte\Filters;
use App\Model\Orm\BaseEntity;
use App\Model\Orm\LibraryImage\LibraryImage;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\JsonContainer; // phpcs:ignore
use App\Model\Orm\Order\OrderProxy;
use App\Model\Orm\Order\OrderState;
use App\Model\Orm\Price;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\State\State;
use App\Model\Orm\State\StateModel;
use App\Model\Orm\State\VatRate;
use App\Model\Orm\Traits\HasCache;
use App\Model\Orm\Traits\HasCustomContent;
use App\Model\Orm\Traits\HasCustomFields;
use App\Model\ShoppingCart\ShoppingCartInterface;
use App\Model\TranslateData;
use App\Model\VatCalculator;
use App\PostType\Core\Model\Editable;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Core\Model\Publishable;
use App\PostType\Core\Model\Validatable;
use Brick\Math\BigDecimal;
use Brick\Money\Money;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;

/**
 * @property int $id {primary}
 * @property string $name  {default ''}
 * @property bool $public {default false}
 * @property DateTimeImmutable|null $editedTime
 * @property DateTimeImmutable|null $publicFrom {default now}
 * @property DateTimeImmutable|null $publicTo {default '+100 year'}
 * @property int|null $edited
 *
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 * @property ArrayHash $customContentJson {container JsonContainer}
 *
 * RELATIONS
 * @property Gift $gift {M:1 Gift::$localizations}
 * @property Mutation $mutation {m:1 Mutation, oneSided=true}
 *
 *
 * VIRTUAL
 * @property ArrayHash|null $cf {virtual}
 * @property ArrayHash|null $cc {virtual}
 * @property-read string $template {virtual}
 */
final class GiftLocalization extends BaseEntity implements LocalizationEntity, Publishable, Editable, Validatable
{

	use HasCustomFields;
	use HasCustomContent;
	use HasCache;

	private StateModel $stateModel;

	public function injectStateModel(StateModel $stateModel): void
	{
		$this->stateModel = $stateModel;
	}

	public function getId(): int
	{
		return $this->id;
	}

	public function getMutation(): Mutation
	{
		return $this->mutation;
	}


	public function setMutation(Mutation $mutation): void
	{
		$this->mutation = $mutation;
	}


	public function getParent(): Gift
	{
		return $this->gift;
	}


	public function setParent(ParentEntity $parentEntity): void
	{
		assert($parentEntity instanceof Gift);
		$this->gift = $parentEntity;
	}

	public function getIsPublic(): bool
	{
		return $this->public;
	}

	public function setIsPublic(bool $isPublic): void
	{
		$this->public = $isPublic;
	}

	public function getName(): string
	{
		if (($product = $this->getParent()->product) !== null) {
			return $product->name;
		}
		return $this->name;
	}

	public function setName(string $name): void
	{
		$this->name = $name;
	}

	public function setEditorId(int $id): void
	{
		$this->edited = $id;
	}

	public function setEditedTime(DateTimeImmutable $editedTime): void
	{
		$this->editedTime = $editedTime;
	}

	public function getPublicFrom(): DateTimeImmutable|null
	{
		return $this->publicFrom;
	}

	public function getPublicTo(): DateTimeImmutable|null
	{
		return $this->publicTo;
	}

	public function setPublicFrom(?DateTimeImmutable $publicFrom): void
	{
		$this->publicFrom = $publicFrom;
	}

	public function setPublicTo(?DateTimeImmutable $publicTo): void
	{
		$this->publicTo = $publicTo;
	}

	public function getLink(): ?Product
	{
		return $this->getParent()->product;
	}

	public function getTotalSupplyCount(): ?int
	{
		if (($product = $this->getParent()->product) !== null) {
			return $product->totalSupplyCount;
		}
		return null;
	}

	public function getSellingPrice(PriceLevel $priceLevel, State $state): ?Money
	{
		if (($product = $this->getParent()->product) !== null) {
			return $product->priceVat($this->getMutation(), $priceLevel, $state);
		}
		return null;
	}

	public function getPrice(State $state): Money
	{
		$vatRate = $this->vatRate($state);
		if (($product = $this->gift->product) !== null) {
			$vatRate = $product->vatRate($state);
		}

		return VatCalculator::priceWithVat($this->gift->price->asMoney(), $vatRate);
	}

	public function vatRate(?State $state = null): BigDecimal
	{
		$vatRateType = VatRate::None; //$this->vatRateType($state);

		$stateId = $state ? $state->id : State::DEFAULT_ID;
		$vatRates = $this->stateModel->getAllVatRatesValues($this->getMutation());
		$rate = $vatRates[$stateId]->get($vatRateType);
		if ($rate === null) {
			throw new LogicException(sprintf('Unknown vatRate %s for Product ID %d, state ID %d', $vatRateType->name, $this->id, $stateId));// nekonzistentni stav, ktery musime odhalit, nelze pouzit defaultni sazbu, prodavalo by se spatne a neprislo by se na to -> je to tak?
		}

		return $rate;
	}

	public function getImage(): ?LibraryImage
	{
		if (($product = $this->getParent()->product) !== null && ($image = $product->firstImage?->libraryImage) !== null) {
			return $image;
		}

		$cf = ArrayHash::from((array) $this->getParent()->cf);
		if (isset($cf->images->mainImage)) {
			return $cf->images->mainImage->getEntity();
		}

		return null;
	}

	public function isActive(OrderProxy $shoppingCart): bool
	{
		return $this->loadCache($this->createCacheKey('isActive', $shoppingCart), function () use ($shoppingCart) {
			if (($stock = $this->getTotalSupplyCount()) !== null && $stock < 1) {
				return false;
			}

			if ($this->gift->onFirstOrder) {
				return $shoppingCart->getUserEntity() !== null && $shoppingCart->getUserEntity()->orders->toCollection()->findBy(['state!=' => OrderState::Draft])->countStored() === 0;
			}

			if (($products = $this->gift->getProductIds()) !== []) {
				return $shoppingCart->containsProductId($products);
			}

			return true;
		});
	}

	public function isAvailable(OrderProxy $shoppingCart): bool
	{
		return $this->loadCache($this->createCacheKey('isAvailable', $shoppingCart), function () use ($shoppingCart) {
			if ($this->gift->onFirstOrder && $this->gift->minPrice === null && $this->gift->minCount === null) {
				return true;
			}

			if (($minPrice = $this->gift->minPrice) !== null) {
				return $shoppingCart->getTotalPriceVat(includeGiftCertificates: false)->isGreaterThanOrEqualTo($minPrice);
			}

			if (($minCount = $this->gift->minCount) !== null) {
				return $shoppingCart->getTotalCount() >= $minCount;
			}

			return false;
		});
	}

	public function getAvailableText(ShoppingCartInterface $shoppingCart): ?TranslateData
	{
		$response = new TranslateData();
		if (($minPrice = $this->gift->minPrice) !== null && $shoppingCart->getTotalPriceVat()->isLessThan($minPrice)) {
			$response->message = 'gift_buy_more_price';
			$response->params = [
				'%buyPrice%' => Filters::formatMoney(Price::from(Money::of($minPrice, $this->gift->price->currency))->asMoney()->minus($shoppingCart->getTotalPriceVat(includeGiftCertificates: false))),
			];

			return $response;
		}

		if (($minCount = $this->gift->minCount) !== null && $shoppingCart->getTotalCount() < $minCount) {
			$response->message = 'gift_buy_more_products';
			$response->params = [
				'%productCount%' => ($minCount - $shoppingCart->getTotalCount()),
			];

			return $response;
		}

		return null;
	}

}
