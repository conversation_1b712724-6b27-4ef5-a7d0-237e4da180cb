<?php declare(strict_types = 1);

namespace App\PostType\Gift\Model\Orm\Gift;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Repository\QueryForIdsByMutation;
use App\Model\Orm\Traits\HasPublicParameter;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method GiftLocalization|null getById($id)
 * @method ICollection<GiftLocalization> searchByName(string $q, array $excluded)
 * @method array findAllIds(?int $limit)
 * @extends Repository<GiftLocalization>
 */
final class GiftLocalizationRepository extends Repository implements QueryForIdsByMutation
{

	use HasPublicParameter;

	public static function getEntityClassNames(): array
	{
		return [GiftLocalization::class];
	}


	public function getPublicOnlyWhereParams(): array
	{
		$ret = [
			'public' => 1,
		];

		$now = $this->getNowDateTime();

		$ret['publicFrom<='] = $now;
		$ret['publicTo>='] = $now;

		return $ret;
	}


	public function findAllIdsInMutation(Mutation $mutation, ?int $limit = null): Result
	{
		$mapper = $this->mapper;
		assert($mapper instanceof GiftLocalizationMapper);

		return $mapper->findAllIdsInMutation($mutation, $limit);
	}

	/**
	 * @return ICollection<GiftLocalization>
	 */
	public function findAllPublicInMutation(Mutation $mutation, string $currency): ICollection
	{
		return $this->findBy($this->getPublicOnlyWhereParams() + ['mutation' => $mutation, 'gift->price->currency' => $currency])->orderBy('gift->sort');
	}

}
