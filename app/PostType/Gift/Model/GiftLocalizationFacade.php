<?php declare(strict_types = 1);

namespace App\PostType\Gift\Model;

use App\PostType\Core\Model\EntityLocalizationFacade;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Gift\Model\Orm\Gift\Gift;
use App\PostType\Gift\Model\Orm\Gift\GiftLocalization;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;

readonly class GiftLocalizationFacade implements EntityLocalizationFacade
{

	public function __construct(
		private Orm $orm,
	)
	{
	}


	public function create(Mutation $mutation, ParentEntity|null $localizableEntity): LocalizationEntity
	{
		$localization = new GiftLocalization();
		$this->orm->giftLocalization->attach($localization);
		$localization->mutation = $mutation;

		if ($localizableEntity === null) {
			$localizableEntity = new Gift();
			$localization->gift = $localizableEntity;
		}

		assert($localizableEntity instanceof Gift);
		$localization->gift = $localizableEntity;

		return $localization;
	}


	public function remove(LocalizationEntity $localizableEntity): void
	{
		assert($localizableEntity instanceof GiftLocalization);

		$parent = $localizableEntity->getParent();
		$this->orm->giftLocalization->remove($localizableEntity);

		if ($parent->localizations->count() === 0) {
			$this->orm->gift->remove($parent);
		}

		$this->orm->flush();
	}

}
