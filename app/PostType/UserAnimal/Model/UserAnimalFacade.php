<?php declare(strict_types = 1);

namespace App\PostType\UserAnimal\Model;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\PostType\Core\Model\EntityFacade;
use App\PostType\UserAnimal\Model\Orm\UserAnimal;

class UserAnimalFacade implements EntityFacade
{

	public function __construct(private readonly Orm $orm)
	{
	}

	public function create(Mutation $mutation, BaseEntity|null $entity): BaseEntity
	{
		$UserAnimal = new UserAnimal();

		//$UserAnimal->mutation = $mutation;
		$this->orm->persistAndFlush($UserAnimal);

		return $UserAnimal;
	}

	public function remove(BaseEntity $entity): void
	{
		$this->orm->remove($entity);
		$this->orm->flush();
	}

}
