<?php declare(strict_types = 1);

namespace App\PostType\UserAnimal\Model\Orm;

use App\Model\Orm\Mutation\Mutation;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\OneHasMany;
use App\Model\Orm\JsonContainer;		// phpcs:ignore

// phpcs:ignore

/**
 * @property int $id {primary}
 * @property string $internalName
 *
 * RELATIONS
 * @property OneHasMany<UserAnimalTypeLocalization> $localizations {1:m UserAnimalTypeLocalization::$userAnimalType}
 * @property OneHasMany<UserAnimal> $animals {1:m UserAnimal::$userAnimalType}
 */
class UserAnimalType extends Entity
{

	public function getId(): int
	{
		return $this->id;
	}

	/**
	 * @return ICollection<UserAnimalTypeLocalization>
	 */
	public function getLocalizations(): ICollection
	{
		return $this->localizations->toCollection();
	}

	public function getLocalization(Mutation $mutation): UserAnimalTypeLocalization
	{
		$localization = $this->getLocalizations()->getByChecked(['mutation' => $mutation]);
		assert($localization instanceof UserAnimalTypeLocalization);
		return $localization;
	}

}
