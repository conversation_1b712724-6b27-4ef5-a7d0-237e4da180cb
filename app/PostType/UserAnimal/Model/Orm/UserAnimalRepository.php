<?php declare(strict_types = 1);

namespace App\PostType\UserAnimal\Model\Orm;

use App\Model\Orm\User\User;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method ICollection<UserAnimal> getAnimals(User $user)
 * @extends Repository<UserAnimal>
 */
class UserAnimalRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [UserAnimal::class];
	}

	/**
	 * @return ICollection<UserAnimal>
	 */
	public function getUserAnimals(User $user): ICollection
	{
		return $this->findBy([
			'user->id' => $user->id,
		]);
	}

}
