<?php declare(strict_types = 1);

namespace App\PostType\UserAnimal\Model\Orm;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\LibraryImage\LibraryImage;
use App\Model\Orm\User\User;
use App\Model\Orm\JsonContainer;		// phpcs:ignore

/**
 * @property int $id {primary}
 * @property string $name
 *
 * RELATIONS
 * @property User $user {m:1 User::$animals}
 * @property UserAnimalType|null $userAnimalType {m:1 UserAnimalType::$animals}
 * @property LibraryImage|null $libraryImage {1:1 LibraryImage, isMain=true, oneSided=true}
 */
class UserAnimal extends BaseEntity
{

	public function getId(): int
	{
		return $this->id;
	}

}
