<?php declare(strict_types = 1);

namespace App\PostType\UserAnimal\Model\Orm;

use App\Model\Orm\Orm;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Entity\IEntity;

class UserAnimalModel
{

	public function __construct(
		private readonly Orm $orm
	)
	{
	}

	public function save(UserAnimal $userAnimal, ArrayHash $userAnimalFormData): UserAnimal
	{
		$userAnimal->name = $userAnimalFormData->name;
		$userAnimal->userAnimalType = $this->orm->userAnimalType->getById($userAnimalFormData->userAnimalTypeId);
		$userAnimal->user = $this->orm->user->getById($userAnimalFormData->userId);
		if (isset($userAnimalFormData->imageId)) {
			$userAnimal->libraryImage = $this->orm->libraryImage->getById($userAnimalFormData->imageId);
		}

		$this->orm->persistAndFlush($userAnimal);

		return $userAnimal;
	}

	public function delete(UserAnimal $userAnimal): IEntity
	{
		return $this->orm->userAnimal->removeAndFlush($userAnimal);
	}

}
