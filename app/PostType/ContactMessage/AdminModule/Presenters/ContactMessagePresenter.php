<?php

namespace App\PostType\ContactMessage\AdminModule\Presenters;

use App\AdminModule\Presenters\BasePresenter;
use App\PostType\ContactMessage\AdminModule\Components\DataGrid\DataGrid;
use App\PostType\ContactMessage\AdminModule\Components\DataGrid\DataGridFactory;
use App\PostType\ContactMessage\Model\Orm\ContactMessage\ContactMessage;

class ContactMessagePresenter extends BasePresenter
{

	private ContactMessage $contactMessage;

	public function __construct(
		private DataGridFactory $dataGridFactory,
	)
	{
		parent::__construct();
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->getTemplate()->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte');
	}

	public function createComponentGrid(): DataGrid
	{
		$collection = $this->orm->contactMessageRepository->findBy([]);

		return $this->dataGridFactory->create($collection);
	}

	public function actionDetail(int $id): void
	{
		$this->contactMessage = $this->orm->contactMessageRepository->getById($id);

		if ($this->contactMessage === null) {
			$this->redirect('default');
		}
	}

	public function renderDetail(): void
	{
		$this->template->add('contactMessage', $this->contactMessage);
	}

	public function actionDelete(int $id): void
	{
		$recordToDelete = $this->orm->contactMessageRepository->getById($id);

		$this->orm->contactMessageRepository->removeAndFlush($recordToDelete);
		$this->flashMessage('OK', 'ok');

		$this->redirect('default');
	}

}
