<?php declare(strict_types = 1);

namespace App\PostType\ContactMessage\AdminModule\Components\DataGrid;

use App\PostType\ContactMessage\Model\Orm\ContactMessage\ContactMessage;
use Nette\Application\UI\Control;
use App\Model\Translator;
use Nextras\Orm\Collection\ICollection;
use Ublaboo\DataGrid\Column\Action\Confirmation\CallbackConfirmation;
use Ublaboo\DataGrid\Exception\DataGridException;

class DataGrid extends Control
{

	/**
	 * @param ICollection<ContactMessage> $collection
	 * @param Translator $translator
	 */
	public function __construct(
		private readonly ICollection $collection,
		private readonly Translator $translator,
	)
	{
	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);

		$template->render(__DIR__ . '/dataGrid.latte');
	}


	/**
	 * @throws DataGridException
	 */
	public function createComponentGrid(): \Ublaboo\DataGrid\DataGrid
	{
		$grid = new \Ublaboo\DataGrid\DataGrid();

		$grid->setDataSource($this->collection);
		$grid->addColumnNumber('id', 'ID')->setSortable()->setFilterText();
		$grid->addColumnText('email', 'email')->setSortable()->setFilterText();
		$grid->addColumnText('phoneNumber', 'phone');
		$grid->addColumnDateTime('createdAt', 'received')->setSortable()->setFilterDateRange();

		$grid->addAction('detail', 'Detail', ':detail')->setClass('btn btn-xs btn-primary');
		$grid->addAction('delete', 'delete_button', ':delete')
			->setConfirmation(
				new CallbackConfirmation(
					function ($item) {
						return 'Opravdu si přejete vymazat tuto zprávu?';
					}
				)
			)->setClass('btn btn-xs btn-danger');

		$grid->setTranslator($this->translator);

		return $grid;
	}

}
