{layout $templates.'/@layout-new.latte'}
{varType App\PostType\ContactMessage\Model\Orm\ContactMessage\ContactMessage $contactMessage}

{block #content}
<div class="main__main main__main--one-column">
	<div class="main__header">
		{include $templates.'/part/box/header.latte',
		props: [
		title: $translator->translate('Message') . ': #' . $contactMessage->id,
		isPageTitle: true,
		]
		}
	</div>
	<div class="main__content scroll">
		{snippet flash}
			<div n:foreach="$flashes as $flash" class="message message-{$flash->type}">{$flash->message}</div>
		{/snippet}
		<p class="u-mb-xs">
			<b>Jméno:</b>
			{$contactMessage->name}
		</p>
		<p class="u-mb-xs">
			<b>Email:</b>
			{$contactMessage->email}
		</p>
		<p class="u-mb-xs">
			<b>Telefon:</b>
			{$contactMessage->phoneNumber ?? '---'}
		</p>
		<p class="u-mb-xs">
			<b>Přijato:</b>
			{$contactMessage->createdAt|date:'d.m.Y H:i:s'}
		</p>

		<p class="u-mb-xl">
			<b>Zpráva:</b>
			{$contactMessage->text|noescape}
		</p>

	</div>
</div>
