<?php declare(strict_types=1);

namespace App\PostType\ContactMessage\Model\Orm\ContactMessage;

use Nextras\Orm\Mapper\Dbal\DbalMapper;
use App\Model\Orm\Traits\HasCamelCase;

/**
 * @extends DbalMapper<ContactMessage>
 */
class ContactMessageMapper extends DbalMapper
{

	use HasCamelCase;

	/**
	 * @return literal-string
	 */
	public function getTableName(): string
	{
		return 'contact_message';
	}

}
