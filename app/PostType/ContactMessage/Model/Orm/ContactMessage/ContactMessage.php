<?php declare(strict_types=1);

namespace App\PostType\ContactMessage\Model\Orm\ContactMessage;

use App\Model\Orm\Traits\HasCamelCase;
use App\Model\Orm\Traits\HasConsts;
use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property string|null $name
 * @property string $email
 * @property string|null $phoneNumber
 * @property string $text
 * @property string $type {enum self::TYPE_*} {default self::TYPE_CONTACT}
 * @property \DateTimeImmutable $createdAt
 */
class ContactMessage extends Entity
{

	use HasCamelCase;
	use HasConsts;

	public const string TYPE_CONTACT = 'contact';

}
