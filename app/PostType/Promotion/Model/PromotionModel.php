<?php declare(strict_types = 1);

namespace App\PostType\Promotion\Model;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Product\Product;
use App\PostType\Promotion\Model\Orm\Promotion\PromotionLocalization;
use App\PostType\Promotion\Model\Orm\Promotion\PromotionLocalizationRepository;
use Nextras\Orm\Collection\ICollection;

class PromotionModel
{

	public function __construct(
		private PromotionLocalizationRepository $promotionLocalizationRepository,
	)
	{
	}


	public function getPromotionForProduct(Product $product, Mutation $mutation): ?PromotionLocalization
	{
		$promotionLocalization = $this->promotionLocalizationRepository->findBy([
			'promotion->products->id' => $product->id,
			'mutation' => $mutation,
		])->orderBy('publicFrom', ICollection::DESC)->limitBy(1)->fetch();

		if ($promotionLocalization instanceof PromotionLocalization && $promotionLocalization->getIsPublic()) {
			return $promotionLocalization;
		}

		return null;
	}

}
