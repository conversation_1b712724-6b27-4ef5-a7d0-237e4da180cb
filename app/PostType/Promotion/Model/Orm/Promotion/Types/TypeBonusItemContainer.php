<?php declare(strict_types = 1);

namespace App\PostType\Promotion\Model\Orm\Promotion\Types;

use Nette\Utils\Json;
use Nextras\Orm\Entity\ImmutableValuePropertyWrapper;

final class TypeBonusItemContainer extends ImmutableValuePropertyWrapper
{

	public function convertToRawValue($value): string
	{
		assert($value instanceof TypeBonusItem);
		return Json::encode($value);
	}

	public function convertFromRawValue($value): TypeBonusItem
	{
		$value = TypeRawDataHelper::sanitizeValue($value);
		$jsonData = Json::decode($value);
		return new TypeBonusItem(
			$jsonData->mainProductCount ?? 0,
			$jsonData->subordinateProductCount ?? 0,
			$jsonData->discountOnSubordinate ?? 0,
		);
	}

}
