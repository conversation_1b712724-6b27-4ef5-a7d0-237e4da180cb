<?php declare(strict_types=1);

namespace App\PostType\Promotion\Model\Orm\Promotion\Types;

class TypeQuantity
{

	public readonly array $items;

	public function __construct(array $rawItems = [])
	{
		$items = [];
		foreach ($rawItems as $rawItem) {
			if (isset($rawItem->amount) && isset($rawItem->price)) {
				$item = new \stdClass();
				$item->amount = (int) $rawItem->amount;
				$item->price = $rawItem->price; // todo money
				$items[] = $item;
			}
		}

		$this->items = $items;
	}

}
