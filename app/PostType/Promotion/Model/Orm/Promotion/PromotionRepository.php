<?php declare(strict_types = 1);

namespace App\PostType\Promotion\Model\Orm\Promotion;

use App\Model\Orm\CollectionById;
use App\Model\Orm\Searchable;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method Promotion getById($id)
 * @method ICollection<Promotion> findByIds(array $ids)
 * @method ICollection<Promotion> findByExactOrder(array $ids)
 * @extends Repository<Promotion>
 */
final class PromotionRepository extends Repository implements CollectionById, Searchable
{

	public static function getEntityClassNames(): array
	{
		return [Promotion::class];
	}

	/**
	 * @return ICollection<Promotion>
	 */
	public function findByIdOrder(array $ids): ICollection
	{
		return $this->findByExactOrder($ids);
	}
	/**
	 * @return ICollection<Promotion>
	 */
	public function searchByName(string $string, array $excludedIds = []): ICollection
	{
		return $this->getMapper()->searchByName($string, $excludedIds);
	}

	public function getMapper(): PromotionMapper
	{
		$mapper = parent::getMapper();
		assert($mapper instanceof PromotionMapper);
		return $mapper;
	}

}
