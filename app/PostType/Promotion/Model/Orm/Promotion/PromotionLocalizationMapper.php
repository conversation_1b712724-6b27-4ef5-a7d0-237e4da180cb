<?php declare(strict_types = 1);

namespace App\PostType\Promotion\Model\Orm\Promotion;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<PromotionLocalization>
 */
class PromotionLocalizationMapper extends DbalMapper
{

	use HasCamelCase;

	/**
	 * @return literal-string
	 */
public function getTableName(): string
{
	return 'promotion_localization';
}
	/**
	 * @return ICollection<PromotionLocalization>
	 */
	public function searchByName(string $q, array $excluded = []): ICollection
	{
		$builder = $this->builder()
			->andWhere('name LIKE %_like_', $q);

		if ($excluded) {
			$builder->andWhere('id not in %i[]', $excluded);
		}

		return $this->toCollection($builder);
	}


	public function findAllIds(?int $limit): Result
	{
		$builder = $this->builder()->select('bt.id')
			->from($this->getTableName(), 'bt')
			->limitBy($limit);
		return $this->connection->queryByQueryBuilder($builder);
	}


	public function findAllIdsInMutation(Mutation $mutation, ?int $limit = null): Result
	{
		$builder = $this->builder()->select('btl.id')
			->from($this->getTableName(), 'btl')
			->andWhere('btl.mutationId = %i', $mutation->id)
			->limitBy($limit);
		return $this->connection->queryByQueryBuilder($builder);
	}

}
