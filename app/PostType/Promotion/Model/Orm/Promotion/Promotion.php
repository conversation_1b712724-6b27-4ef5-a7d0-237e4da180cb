<?php declare(strict_types = 1);

namespace App\PostType\Promotion\Model\Orm\Promotion;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Promotion\Model\Orm\Promotion\Types\PromotionDiscountType;
use App\PostType\Promotion\Model\Orm\Promotion\Types\PromotionType;
use App\PostType\Promotion\Model\Orm\Promotion\Types\TypeBonusItem;
use App\PostType\Promotion\Model\Orm\Promotion\Types\TypeQuantity;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Exception\NoResultException;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;
use App\Model\Orm\JsonContainer; // phpcs:ignore
use App\Model\Orm\BackedEnumWrapper; // phpcs:ignore
use App\PostType\Promotion\Model\Orm\Promotion\Types\TypeBonusItemContainer; // phpcs:ignore
use App\PostType\Promotion\Model\Orm\Promotion\Types\TypeQuantityContainer; // phpcs:ignore



/**
 * @property int $id {primary}
 * @property string $internalName {default ''}
 * @property PromotionType $type {wrapper BackedEnumWrapper}
 * @property PromotionDiscountType $discountType {default 'percent'} {wrapper BackedEnumWrapper}
 * @property TypeBonusItem $typeBonusItem {container TypeBonusItemContainer}
 * @property TypeQuantity $typeQuantity {container TypeQuantityContainer}
 *
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 *
 * RELATIONS
 * @property OneHasMany<PromotionLocalization> $localizations {1:M PromotionLocalization::$promotion}
 * @property ManyHasMany<Product> $products {m:m Product::$promotions, isMain=true}
 *
 *
 * VIRTUAL
 * @property ArrayHash|null $cf {virtual}
 */
class Promotion extends BaseEntity implements ParentEntity
{

	use HasCustomFields;

	public function getInternalName(): string
	{
		return $this->internalName;
	}

	public function setInternalName(string $internalName): void
	{
		$this->internalName = $internalName;
	}

	/**
	 * @return ICollection<PromotionLocalization>
	 */
	public function getLocalizations(): ICollection
	{
		return $this->localizations->toCollection();
	}

	/**
	 * @throws NoResultException
	 */
	public function getLocalization(Mutation $mutation): PromotionLocalization
	{
		$localization = $this->getLocalizations()->getByChecked(['mutation' => $mutation]);
		assert($localization instanceof PromotionLocalization);
		return $localization;
	}

	public static function getTypeSelectData(): array
	{
		$typeValues = array_map(fn(PromotionType $item) => $item->value, PromotionType::cases());
		return array_combine($typeValues, $typeValues);
	}

}
