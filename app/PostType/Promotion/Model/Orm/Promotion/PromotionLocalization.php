<?php declare(strict_types = 1);

namespace App\PostType\Promotion\Model\Orm\Promotion;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\JsonContainer; // phpcs:ignore
use App\Model\Orm\Traits\HasCustomContent;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\Core\Model\Editable;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Core\Model\Publishable;
use App\PostType\Core\Model\Validatable;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;

/**
 * @property int $id {primary}
 * @property string $name  {default ''}
 * @property bool $public {default false}
 * @property DateTimeImmutable|null $editedTime
 * @property DateTimeImmutable|null $publicFrom {default now}
 * @property DateTimeImmutable|null $publicTo {default '+100 year'}
 * @property int|null $edited
 *
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 * @property ArrayHash $customContentJson {container JsonContainer}
 *
 * RELATIONS
 * @property Promotion $promotion {M:1 Promotion::$localizations}
 * @property Mutation $mutation {m:1 Mutation, oneSided=true}
 *
 *
 * VIRTUAL
 * @property ArrayHash|null $cf {virtual}
 * @property ArrayHash|null $cc {virtual}
 * @property-read string $template {virtual}
 */
class PromotionLocalization extends BaseEntity implements LocalizationEntity, Publishable, Editable, Validatable
{

	use HasCustomFields;
	use HasCustomContent;

	public function getId(): int
	{
		return $this->id;
	}

	public function getMutation(): Mutation
	{
		return $this->mutation;
	}


	public function setMutation(Mutation $mutation): void
	{
		$this->mutation = $mutation;
	}


	public function getParent(): Promotion
	{
		return $this->promotion;
	}


	public function setParent(ParentEntity $parentEntity): void
	{
		assert($parentEntity instanceof Promotion);
		$this->promotion = $parentEntity;
	}

	public function getIsPublic(): bool
	{
		return $this->public;
	}

	public function setIsPublic(bool $isPublic): void
	{
		$this->public = $isPublic;
	}

	public function getName(): string
	{
		return $this->name;
	}

	public function setName(string $name): void
	{
		$this->name = $name;
	}

	public function setEditorId(int $id): void
	{
		$this->edited = $id;
	}

	public function setEditedTime(DateTimeImmutable $editedTime): void
	{
		$this->editedTime = $editedTime;
	}

	public function getPublicFrom(): DateTimeImmutable|null
	{
		return $this->publicFrom;
	}

	public function getPublicTo(): DateTimeImmutable|null
	{
		return $this->publicTo;
	}

	public function setPublicFrom(?DateTimeImmutable $publicFrom): void
	{
		$this->publicFrom = $publicFrom;
	}

	public function setPublicTo(?DateTimeImmutable $publicTo): void
	{
		$this->publicTo = $publicTo;
	}

}
