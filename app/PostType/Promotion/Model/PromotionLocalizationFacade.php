<?php declare(strict_types = 1);

namespace App\PostType\Promotion\Model;

use App\PostType\Promotion\Model\Orm\Promotion\Promotion;
use App\PostType\Promotion\Model\Orm\Promotion\PromotionLocalization;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\PostType\Core\Model\EntityLocalizationFacade;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;

class PromotionLocalizationFacade implements EntityLocalizationFacade
{

	public function __construct(
		private Orm $orm,
	)
	{
	}


	public function create(Mutation $mutation, ParentEntity|null $localizableEntity): LocalizationEntity
	{
		$localization = new PromotionLocalization();
		$this->orm->promotionLocalization->attach($localization);
		$localization->mutation = $mutation;

		if ($localizableEntity === null) {
			$localizableEntity = new Promotion();
			$localization->promotion = $localizableEntity;
		} else {
			assert($localizableEntity instanceof Promotion);
			$localization->promotion = $localizableEntity;
		}

		return $localization;
	}


	public function remove(LocalizationEntity $localizableEntity): void
	{
		assert($localizableEntity instanceof PromotionLocalization);

		$parent = $localizableEntity->getParent();
		$this->orm->promotionLocalization->remove($localizableEntity);

		if ($parent->localizations->count() === 0) {
			$this->orm->promotion->remove($parent);
		}

		$this->orm->flush();
	}

}
