cf:
	templates:
		# promotionLocalization:
			# base:
			# 	type: group
			# 	items:
			# 		content: @cf.definitions.content

application:
	mapping:
		Promotion: App\PostType\Promotion\*Module\Presenters\*Presenter

parameters:
	postTypeRoutes:
		Promotion: promotion

services:
	- App\PostType\Promotion\Model\PromotionLocalizationFacade
	- App\PostType\Promotion\Model\PromotionModel
	- App\PostType\Promotion\AdminModule\Components\ShellForm\ShellFormPrescription
	- App\PostType\Promotion\AdminModule\Components\Form\PromotionLocalizationFormPrescription
	- App\PostType\Promotion\AdminModule\Components\Products\ProductsFactory
	- App\PostType\Promotion\AdminModule\Components\PromotionsDataGrid\PromotionDataGridPrescription
