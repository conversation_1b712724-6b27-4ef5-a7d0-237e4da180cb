<?php declare(strict_types=1);

namespace App\PostType\Promotion\AdminModule\Presenters;

use App\AdminModule\Components\ItemSearch\FoundItem;
use App\AdminModule\Components\ItemSearch\ItemSearch;
use App\AdminModule\Components\ItemSearch\ItemSearchFactory;
use App\AdminModule\Presenters\BasePresenter;
use App\Model\ElasticSearch\All\Repository;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Orm\Product\Product;
use App\PostType\Promotion\AdminModule\Components\Form\PromotionLocalizationFormPrescription;
use App\PostType\Promotion\AdminModule\Components\Products\Products;
use App\PostType\Promotion\AdminModule\Components\Products\ProductsFactory;
use App\PostType\Promotion\AdminModule\Components\PromotionsDataGrid\PromotionDataGridPrescription;
use App\PostType\Promotion\AdminModule\Components\ShellForm\ShellFormPrescription;
use App\PostType\Promotion\Model\Orm\Promotion\PromotionLocalization;
use App\PostType\Promotion\Model\PromotionLocalizationFacade;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGrid;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGridFactory;
use App\PostType\Core\AdminModule\Components\Form\Form;
use App\PostType\Core\AdminModule\Components\Form\FormFactory;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellForm;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellFormFactory;
use Nette\Application\Attributes\Persistent;
use Nextras\Dbal\Drivers\Exception\UniqueConstraintViolationException;

final class PromotionPresenter extends BasePresenter
{

	private const ORM_REPOSITORY_NAME = 'promotion';

	#[Persistent]
	public int $id;

	private PromotionLocalization $promotionLocalization;

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
		private readonly FormFactory $promotionFormFactory,
		private readonly ShellFormFactory $shellFormFactory,
		private readonly PromotionLocalizationFacade $promotionLocalizationFacade,
		private readonly ShellFormPrescription $shellFormPrescription,
		private readonly PromotionLocalizationFormPrescription $promotionLocalizationFormPrescription,
		private readonly ProductsFactory $productsFactory,
		private readonly Repository $esAllRepository,
		private readonly PromotionDataGridPrescription $promotionDataGridPrescription,
		private readonly ItemSearchFactory $itemSearchFactory,
		private readonly EsIndexRepository $esIndexRepository,
	)
	{
		parent::__construct();
	}

	public function renderDefault(): void
	{
	}


	public function actionEdit(int $id): void
	{
		$promotionLocalization = $this->orm->promotionLocalization->getById($id);
		if ($promotionLocalization === null) {
			$this->redirect('default');
		}

		$this->promotionLocalization = $promotionLocalization;
	}


	public function renderEdit(int $id): void
	{
	}


	public function actionProducts(int $id): void
	{
		$promotionLocalization = $this->orm->promotionLocalization->getById($id);
		if ($promotionLocalization === null) {
			$this->redirect('default');
		}

		$this->id = $id;
		$this->promotionLocalization = $promotionLocalization;
	}


	public function renderProducts(int $id): void
	{
		$this->template->add('sectionTitle', $this->translator->translate('Promoted product'));
		$this->template->add('promotionLocalization', $this->promotionLocalization);
//		if ($this->isAjax()) {
//			$this->redrawControl();
//		}
	}


	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create(self::ORM_REPOSITORY_NAME, $this->orm->promotionLocalization->findAll(), $this->promotionDataGridPrescription->getPrescription());
	}


	protected function createComponentForm(): Form
	{
		$userEntity = $this->userEntity;
		$rawPostData = $this->request->getPost();

		return $this->promotionFormFactory->create($this->promotionLocalizationFacade, $this->promotionLocalization, $userEntity, formDefinition: $this->promotionLocalizationFormPrescription->getPrescription($this->promotionLocalization, $rawPostData));
	}

	protected function createComponentProducts(): Products
	{
		return $this->productsFactory->create($this->promotionLocalization);
	}

	public function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create(entity: null, facade: $this->promotionLocalizationFacade, formDefinition: $this->shellFormPrescription->getPrescription());
	}

	public function createComponentProductSearch(): ItemSearch
	{
		$promotionLocalization = $this->promotionLocalization;
		$clickItemFunction = function ($foundItemId) use ($promotionLocalization): void {
			try {
				$promotionLocalization->getParent()->products->add($foundItemId);
				$this->orm->persistAndFlush($promotionLocalization->getParent());
			} catch (UniqueConstraintViolationException) {
				// ignore duplicity
			}
			if ($this->isAjax()) {
				$this->redrawControl();
			} else {
				$this->redirect('products', ['id' => $promotionLocalization->id]);
			}
		};

		$searchItemFunction = function (string $searchString): array {
			$esIndex = $this->esIndexRepository->getAllLastActive($this->orm->mutation->getRsDefault());
			if ($esIndex !== null) {
				/** @var array<Product> $products */
				$products = $this->esAllRepository->searchProduct($esIndex, $searchString)->fetchAll();
				return array_map(fn(Product $product) => new FoundItem($product->id, $product->internalName), $products);
			}
			return [];
		};

		return $this->itemSearchFactory->create($searchItemFunction, $clickItemFunction);
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte');
	}

}
