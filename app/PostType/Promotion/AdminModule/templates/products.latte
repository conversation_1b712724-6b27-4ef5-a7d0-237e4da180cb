{layout $templates.'/@layout-new.latte'}
{varType App\PostType\Promotion\Model\Orm\Promotion\PromotionLocalization $promotionLocalization}
{var $dataGridShown = true}

{block #content}
<div class="main__main">
	<div class="main__header" n:snippet="header">
		{include $templates.'/part/box/header.latte',
			props: [
				title: $sectionTitle,
				hrefClose: $presenter->link('edit', [id=>$promotionLocalization->id]),
				isPageTitle: true,
			]
		}
	</div>

	<div class="main__content scroll">

		<div style="position: relative; z-index: 1">
			{control productSearch}
		</div>

		<div data-controller="DataGrid">
			<div  n:snippet="mainContent">
				{control products}
			</div>
		</div>
	</div>
	<div class="main__content-side scroll">
	</div>
</div>
