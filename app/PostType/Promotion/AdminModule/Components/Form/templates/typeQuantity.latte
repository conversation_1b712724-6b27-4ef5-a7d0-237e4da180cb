{if isset($form['typeQualityFormData'])}

	{var $anchorName = 'setup'}
	{var $icon = $templates . '/part/icons/align-left.svg'}
	{var $title = 'Nastavené promoce'}

	{var $props = [
		title: $title,
		id: $anchorName,
		icon: $icon,
		variant: 'main',
		open: true,
		classes: ['u-mb-xxs'],

	]}


	{embed $templates . '/part/box/toggle.latte', props => $props, templates => $templates}
		{block content}
		{var $items = []}
		{foreach $form['typeQualityFormData']->components as $key=>$component}
			{continueIf $key === 'newItemMarker'}
			{var $item = [
				inps: [
					[
						placeholder: 'Cena za kus',
						input: $component['price'],
					],
					[
						placeholder: 'Množství',
						input: $component['amount'],
					]
				],

			]}
			{php $item['btnsAfter'][] = [
				icon: $templates.'/part/icons/trash.svg',
				tooltip: '',
				variant: 'remove',
				data: [
					action: 'RemoveItem#remove'
				]
			]}
			{php $items[] = $item}
		{/foreach}
		<div class="">
			{var $listProps = [
				data: [
					controller: 'List',
					List-name-value:  'qualityType',
				],
				listData: [
					List-target: 'list',
				],
				addData: [
					action: 'List#add',
				],
				add: true,
				dragdrop: false,
				items: $items,
				headers: [$translator->translate('priceOneItem'), $translator->translate('amount')],
			]}
			{include $templates.'/part/box/list.latte',
				props: $listProps
			}
		</div>
		{/block}
	{/embed}
{/if}
