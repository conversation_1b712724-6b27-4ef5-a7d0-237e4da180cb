<?php declare(strict_types = 1);

namespace App\PostType\Promotion\AdminModule\Components\Products;

use App\PostType\Promotion\Model\Orm\Promotion\PromotionLocalization;
use App\PostType\Promotion\Model\Orm\Promotion\PromotionRepository;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use App\Model\Translator;
use Ublaboo\DataGrid\DataGrid;

/**
 * @property-read DefaultTemplate $template
 */
final class Products extends UI\Control
{

	public function __construct(
		private readonly PromotionLocalization $promotionLocalization,
		private readonly Translator $translator,
		private readonly PromotionRepository $promotionRepository,
	)
	{
	}

	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->render(__DIR__ . '/products.latte');
	}

	public function createComponentGrid(): DataGrid
	{
		$grid = new DataGrid();

		$grid->setDataSource($this->promotionLocalization->getParent()->products->toCollection());
		$grid->addColumnText('internalName', 'internalName')->setSortable()->setFilterText();

		$groupDelete = $grid->addGroupButtonAction('delete_button');
		$groupDelete->setClass('btn btn-xs btn-danger ajax group-delete-button');
		$groupDelete->setAttribute('data-datagrid-confirm', $this->translator->translate('delete_confirms'));
		$groupDelete->onClick[] = $this->deleteSelected(...);

		$grid->setTranslator($this->translator);

//		$grid->setTemplateFile(__DIR__ . '/dataGridTemplate.latte');

		return $grid;
	}

	public function deleteSelected(array $items): void
	{
		foreach ($items as $item) {
			$this->promotionLocalization->getParent()->products->remove($item);
			$this->promotionRepository->persistAndFlush($this->promotionLocalization->getParent());
		}
	}

}
