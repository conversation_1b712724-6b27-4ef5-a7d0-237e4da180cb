<?php declare(strict_types = 1);

namespace App\PostType\Supplier\Model\Orm\Supplier;

use App\Model\CustomField\CustomFields;
use App\Model\Orm\Supply\DTO\SupplyDTO;
use App\Model\Orm\Traits\HasStaticCache;
use App\Model\Orm\User\UserRepository;
use App\PostType\Supplier\AdminModule\Components\SupplierForm\SupplierFormData;
use Nette\Utils\ArrayHash;
use Nette\Utils\DateTime;

final class SupplierModel
{

	use HasStaticCache;

	public function __construct(
		private readonly SupplierRepository $supplierRepository,
		private readonly UserRepository $userRepository,
		private readonly CustomFields $customFields,
	)
	{
	}

	public function update(Supplier $supplier, SupplierFormData $supplierFormData): Supplier
	{
		$supplier->internalName = $supplierFormData->internalName;
		$supplier->editedBy     = $this->userRepository->getById($supplierFormData->editedBy);
		$supplier->editedTime   = DateTime::from('now')->format('c');

		if (isset($supplier->cf)) {
			if (isset($supplierFormData->setup->cf) && $supplierFormData->setup->cf !== '') {
				$supplier->setCf($this->customFields->prepareDataToSave($supplierFormData->setup->cf));
			} else {
				$supplier->setCf(new ArrayHash());
			}
		}

		$this->supplierRepository->persistAndFlush($supplier);

		return $supplier;
	}

	public function getSupplierBySupplyDto(SupplyDTO $supply): ?Supplier
	{
		if ($supply->supplierId === null) {
			return null;
		}

		$generator = function () use ($supply) {
			return $this->supplierRepository->getById($supply->supplierId);
		};

		return $this->tryLoadCache(
			$this->createCacheKey('supplier', $supply->supplierId),
			$generator
		);
	}

}
