<?php declare(strict_types = 1);

namespace App\PostType\Supplier\AdminModule\Components\SupplierForm;

use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\User\User;
use App\Model\Translator;
use App\PostType\Core\AdminModule\Components\Form\Form as CoreForm;
use App\PostType\Supplier\Model\Orm\Supplier\Supplier;
use App\PostType\Supplier\Model\Orm\Supplier\SupplierModel;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;

class SupplierForm extends Control
{

	public function __construct(
		private readonly Supplier $supplier,
		private readonly User $user,
		private readonly Translator $translator,
		private readonly MutationsHolder $mutationsHolder,
		private readonly SupplierModel $supplierModel,
	)
	{
	}

	public function render(): void
	{
		$template = $this->getTemplate();
		$template->setTranslator($this->translator);
		$template->entity             = $this->supplier;
		$template->templates          = RS_TEMPLATE_DIR;
		$template->corePartsDirectory = CoreForm::TEMPLATE_PARTS_DIR;
		$template->fileUploadLink     = $this->presenter->link(':Admin:File:upload');
		$template->mutation           = $this->mutationsHolder->getDefault();
		$template->showDeleteButton   = false;
		$template->setFile(__DIR__ . '/supplierForm.latte');
		$template->render();
	}

	protected function createComponentSupplierForm(): Form
	{
		$form = new Form();

		$form->addText('internalName', 'label_internalName');
		$form->addSubmit('send', 'send');
		$form->addContainer('setup')->addHidden('cf');

		$form->setDefaults($this->supplier->toArray());

		$form->addHidden('editedBy', $this->user->id)->addRule(\Nette\Forms\Form::Integer);

		$form->onSuccess[] = $this->formSuccess(...);
		$form->onError[]   = $this->formError(...);

		return $form;
	}

	private function formError(): void
	{
		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	private function formSuccess(Form $form, SupplierFormData $supplierFormData): void
	{
		$this->supplierModel->update($this->supplier, $supplierFormData);
	}

}
