{varType App\PostType\Page\Model\Orm\Tree $entityLocalization}
{varType App\PostType\Page\Model\Orm\Tree $parent}

<form n:name="supplierForm" class="main__main" data-controller="pagemenu">
	<div class="main__header">
		{include './parts/header.latte', entity => $entity}
	</div>

	<div class="main__content scroll">
		<ul class="message message-error" n:if="$form->hasErrors()">
			<li n:foreach="$form->errors as $error">{$error}</li>
		</ul>

		{include './parts/content/content.latte', form => $form}
		{*include $corePartsDirectory . '/content/validity.latte', form => $form*}
	</div>


	<div class="main__content-side scroll">
		{include $corePartsDirectory . '/side/btns.latte'}
		{include './parts/side/content.latte', form => $form}

	</div>
</form>

{include $templates . '/part/core/libraryOverlay.latte'}
