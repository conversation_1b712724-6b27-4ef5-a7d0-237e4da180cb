<?php declare(strict_types = 1);

namespace App\PostType\Supplier\AdminModule\Components\SupplierList;

use App\PostType\Core\AdminModule\Components\DataGrid\Definition\DataGridDefinition;
use App\PostType\Core\AdminModule\Components\DataGrid\Definition\Extenders\CustomDataGridExtender;
use Ublaboo\DataGrid\DataGrid;

class SupplierPrescription
{

	public function get(): DataGridDefinition
	{
		$dataGrid = new DataGrid();

		return new DataGridDefinition(
			dataGrid: $dataGrid,
			extenders: [
				new CustomDataGridExtender(
					improveFunction: $this->reset(...)
				),

				new CustomDataGridExtender(
					improveFunction: $this->addInternalName(...)
				),
			]
		);
	}

	private function reset(DataGrid $dataGrid): void
	{
		$dataGrid->removeColumn('name');
		$dataGrid->removeFilter('name');
	}

	private function addInternalName(DataGrid $dataGrid): void
	{
		$dataGrid->addColumnText('internalName', 'internalName', 'internalName')->setSortable()->setFilterText();
	}

}
