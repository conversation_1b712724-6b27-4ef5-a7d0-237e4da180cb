<?php declare(strict_types = 1);

namespace App\PostType\Supplier\AdminModule\Presenters;

use App\AdminModule\Presenters\BasePresenter;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGrid;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGridFactory;
use App\PostType\Supplier\AdminModule\Components\SupplierForm\SupplierForm;
use App\PostType\Supplier\AdminModule\Components\SupplierForm\SupplierFormFactory;
use App\PostType\Supplier\AdminModule\Components\SupplierList\SupplierPrescription;
use App\PostType\Supplier\Model\Orm\Supplier\Supplier;

final class SupplierPresenter extends BasePresenter
{

	private const ORM_REPOSITORY_NAME = 'Supplier';

	private Supplier $supplier;

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
		private readonly SupplierPrescription $supplierPrescription,
		private readonly SupplierFormFactory $supplierFormFactory,
	)
	{
	}

	public function actionEdit(int $id): void
	{
		/**
		 * @var Supplier|null $supplier
		 */
		$supplier = $this->orm->supplier->getById($id);

		if ($supplier === null) {
			$this->redirect('default');
		}

		$this->supplier = $supplier;
	}

	protected function createComponentSupplierForm(): SupplierForm
	{
		return $this->supplierFormFactory->create($this->supplier, $this->userEntity);
	}

	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create(self::ORM_REPOSITORY_NAME, $this->orm->supplier->findAll(), $this->supplierPrescription->get());
	}

}
