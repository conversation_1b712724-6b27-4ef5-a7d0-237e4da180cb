application:
	mapping:
		Supplier: App\PostType\Supplier\*Module\Presenters\*Presenter

parameters:
	postTypeRoutes:
		Supplier: supplier

cf:
	templates:
		supplier:
			delivery:
				type: group
				label: "Doba dodáni"
				items:
					standardDays:
						type: text
						label: "Standardní doba dodání (počet pracovních dnů)"
						inputType: number
					closingAddDays:
						type: text
						label: "Closing time pro budoucí termín naskladnění (počet dnů)"
						inputType: number
					closingTime:
						type: group
						label: "Closing time pre dny v týdnu (0-23 alebo prázdne)"
						items:
							0:
								type: text
								inputType: number
								label: "Pondělí"
							1:
								type: text
								inputType: number
								label: "Úterý"
							2:
								type: text
								inputType: number
								label: "Středa"
							3:
								type: text
								inputType: number
								label: "Čtvrtek"
							4:
								type: text
								inputType: number
								label: "Pátek"
							5:
								type: text
								inputType: number
								label: "Sobota"
							6:
								type: text
								inputType: number
								label: "Neděle"

services:
	- App\PostType\Supplier\AdminModule\Components\SupplierList\SupplierPrescription
	- App\PostType\Supplier\AdminModule\Components\SupplierForm\SupplierFormFactory
	- App\PostType\Supplier\AdminModule\Components\SupplierForm\SupplierFormData
	- App\PostType\Supplier\Model\Orm\Supplier\SupplierModel
