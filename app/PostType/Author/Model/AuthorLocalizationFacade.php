<?php declare(strict_types = 1);

namespace App\PostType\Author\Model;


use App\PostType\Author\Model\Orm\Author;
use App\PostType\Author\Model\Orm\AuthorLocalization;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\PostType\Core\Model\EntityLocalizationFacade;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;

class AuthorLocalizationFacade implements EntityLocalizationFacade
{

	public function __construct(
		private Orm $orm,
	)
	{
	}


	public function create(Mutation $mutation, ParentEntity|null $localizableEntity): LocalizationEntity
	{
		$localization = new AuthorLocalization();
		$this->orm->authorLocalization->attach($localization);
		$localization->mutation = $mutation;

		if ($localizableEntity === null) {
			$localizableEntity = new Author();
			$localization->author = $localizableEntity;
		} else {
			assert($localizableEntity instanceof Author);
			$localization->author = $localizableEntity;
		}

		$this->orm->persistAndFlush($localization);

		return $localization;
	}


	public function remove(LocalizationEntity $localizableEntity): void
	{
		assert($localizableEntity instanceof AuthorLocalization);

		$parent = $localizableEntity->getParent();
		$this->orm->authorLocalization->remove($localizableEntity);

		if ($parent->localizations->count() === 0) {
			$this->orm->author->remove($parent);
		}

		$this->orm->flush();
	}

}
