<?php declare(strict_types = 1);

namespace App\PostType\Author\Model\Orm;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Repository\QueryForIdsByMutation;
use App\Model\Orm\Traits\HasPublicParameter;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method AuthorLocalization|null getById($id)
 * @method ICollection<AuthorLocalization> searchByName(string $q, array $excluded)
 * @method ICollection<AuthorLocalization> findRandom()
 * @method array findAllIds(?int $limit)
 *
 * @extends Repository<AuthorLocalization>
 */
final class AuthorLocalizationRepository extends Repository implements QueryForIdsByMutation
{

	use HasPublicParameter;

	public static function getEntityClassNames(): array
	{
		return [AuthorLocalization::class];
	}


	public function getPublicOnlyWhereParams(): array
	{
		return [
			'public' => 1,
		];
	}


	public function findAllIdsInMutation(Mutation $mutation, ?int $limit = null): Result
	{
		$mapper = $this->mapper;
		assert($mapper instanceof AuthorLocalizationMapper);

		return $mapper->findAllIdsInMutation($mutation, $limit);
	}

}
