<?php declare(strict_types = 1);

namespace App\PostType\Author\Model\Orm;

use App\Model\Orm\CollectionById;
use App\Model\Orm\Searchable;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\IMapper;
use Nextras\Orm\Repository\Repository;

/**
 * @method Author getById($id)
 * @method ICollection<Author> findByExactOrder(array $ids)
 *
 * @extends Repository<Author>
 */
final class AuthorRepository extends Repository implements CollectionById, Searchable
{

	public static function getEntityClassNames(): array
	{
		return [Author::class];
	}
	/**
	 * @return ICollection<Author>
	 */
	public function findByIdOrder(array $ids): ICollection
	{
		return $this->findByExactOrder($ids);
	}
	/**
	 * @return ICollection<Author>
	 */
	public function searchByName(string $string, array $excludedIds = []): ICollection
	{
		return $this->getMapper()->searchByName($string, $excludedIds);
	}

	public function getMapper(): AuthorMapper
	{
		$mapper = parent::getMapper();
		assert($mapper instanceof AuthorMapper);
		return $mapper;
	}
}
