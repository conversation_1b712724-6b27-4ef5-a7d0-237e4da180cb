cf:
	templates:
		author:
			settings:
				type: group
				label: "Nastavení"
				items:
					avatar:
						type: image # has size
						label: "Avatar (min.560x560)"
		authorLocalization:
			settings:
				type: group
				label: "Nastavení"
				items:
					position:
						type: text
						label: "Pozice / firma"
					bio:
						type: textarea
						label: "Biografie"

cc:
	templates:
	# 	author: []
	# 	authorLocalization: []

application:
	mapping:
		Author: App\PostType\Author\*Module\Presenters\*Presenter

parameters:
	postTypeRoutes:
		Author: author

services:


	## AUTHOR
	- App\PostType\Author\Model\AuthorLocalizationFacade
