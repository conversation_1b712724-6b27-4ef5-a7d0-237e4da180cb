<?php declare(strict_types = 1);

namespace App\PostType\Author\Api\V1\List\Response;

use Apitte\Core\Mapping\Response\BasicEntity;
use App\PostType\Author\Model\Orm\AuthorLocalization;

class AuthorLocalizationListItem extends BasicEntity
{

	public readonly int $id;

	public readonly string $name;

	public function __construct(AuthorLocalization $authorLocalization)
	{
		$this->id = $authorLocalization->id;
		$this->name = $authorLocalization->name;
	}

}
