{varType App\PostType\Calendar\Model\Orm\CalendarLocalization $object}

{block content}
	<div class="u-mb-last-0 u-mb-md u-mb-xl@md">
		{include $templates.'/part/box/intro.latte', annotation: $object->description}

		<div class="holder holder--lg">
			<div class="u-maw-7-12 u-mx-auto">
				{php $img = isset($object->cf->settings??->mainImage) ? $object->cf->settings->mainImage->getEntity() ?? false : false}
				<img n:if="$img" src="{$img->getSize('lg')->src}" alt="{$img->getAlt($mutation)}" fetchpriority="high">
				{include $templates.'/part/core/table-meta.latte'}
				{include $templates.'/part/core/share.latte'}
				{include $templates.'/part/crossroad/tags.latte', items: $object->calendarTags, parent: $pages->events}
			</div>
		</div>

		{control customContentRenderer}
	</div>
{/block}


{* {control attachedCalendars}
{include $templates.'/part/crossroad/tags.latte', items: $object->calendarTags} *}
{* <p>
	{_'article_reading_time'}:
	{$object->readingTime} {_($object->readingTime|plural: "minute_1", "minute_2", "minute_3")}
</p> *}

{* <h2>
		{_"title_categories"}
	</h2>
	<ul n:ifcontent class="u-mb-lg">
		<li n:foreach="$object->categories as $category">
			<a n:href="$category">{$category->name}</a>
		</li>
	</ul> *}
