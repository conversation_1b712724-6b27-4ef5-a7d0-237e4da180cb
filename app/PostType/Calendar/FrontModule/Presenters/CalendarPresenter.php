<?php declare(strict_types = 1);

namespace App\PostType\Calendar\FrontModule\Presenters;

use App\FrontModule\Components\CustomContentRenderer\HasCustomContentRenderer;
use App\FrontModule\Presenters\BasePresenter;
use App\FrontModule\Presenters\HasFilterTrait;
use App\Model\BucketFilter\BucketFilterBuilder;
use App\Model\BucketFilter\BucketFilterBuilderFactory;
use App\Model\BucketFilter\BucketFilterFactory;
use App\Model\BucketFilter\SetupCreator\Calendar\BasicElasticItemListFactory;
use App\Model\BucketFilter\SetupCreator\Calendar\BoxListFactory;
use App\Model\BucketFilter\SetupCreator\Calendar\ElasticItemListFactory;
use App\Model\BucketFilter\SortCreator;
use App\Model\Link\LinkSeo;
use App\PostType\Calendar\Model\Orm\CalendarLocalization;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Page\Model\Orm\CommonTree;

/**
 * @method CalendarLocalization getObject()
 */
final class CalendarPresenter extends BasePresenter
{

	use HasCustomContentRenderer;
	use HasFilterTrait;

	private array $filterParams;

	private CalendarLocalization $calendarLocalization;

	private BucketFilterBuilder $bucketFilterBuilder;

	public function __construct(
		private readonly BucketFilterFactory $bucketFilterFactory,
		private readonly SortCreator $sortCreator,
		private readonly BasicElasticItemListFactory $basicElasticItemListFactory,
		private readonly ElasticItemListFactory $elasticItemListFactory,
		private readonly BoxListFactory $boxListFactory,
		private readonly LinkSeo $linkSeo,
		private readonly BucketFilterBuilderFactory $bucketFilterBuilderFactory,
		//		private readonly string $appDir,
		//		private AttachedCalendarsFactory $attachedCalendarsFactory,
		//		private CalendarLocalizationModel $calendarLocalizationModel,
		//		private CalendarLocalizationStructuredDataFactory $calendarLocalizationStructuredDataFactory,
		//		private CalendarTagModel $calendarTagModel,
	)
	{
		parent::__construct();
	}

	public function startup(): void
	{
		parent::startup();
		$this->setupCleanFilterParam();

		$this->filterParams = $this->cleanFilterParam;
	}


	public function actionDefault(CommonTree $object): void
	{
		$this->setObject($object);

		$this->bucketFilterBuilder = $this->bucketFilterBuilderFactory->create($this, $object, $this->currentState, $this->priceLevel, '', $this->filterParams);
	}


	public function renderDefault(CommonTree $object, string $order = 'eventFrom'): void
	{
//		$this->addComponent($this->visualPaginatorFactory->create(), 'pager');
//		$this['pager']->object = $object;
//		$this['pager']->special = true;
//
//		$paginator = $this['pager']->getPaginator();
//		$paginator->itemsPerPage = $this->configService->get('calendar', 'paging');
//
//		$possibleCalendarLocalizationIds = $this->orm->calendarLocalizationTree->findBy(['tree->id' => $object->id])->fetchPairs(null, 'calendarLocalization->id');
//		$calendarLocalizations = $this->orm->calendarLocalization->findBy(['id' => $possibleCalendarLocalizationIds])->orderBy('publicFrom');
//
//		$totalCount = $calendarLocalizations->countStored();
//		$paginator->itemCount = $totalCount;
//
//		$this->template->tagsWithCount = $this->calendarTagModel->getTagsWithCount($this->mutation);
//		$this->template->totalCount = $totalCount;
//		$this->template->calendarLocalizations = $calendarLocalizations->limitBy($paginator->itemsPerPage, $paginator->offset);
//
//		if ($this->isAjax()) {
//			if ($this['pager']->getParameter('more')) {
//				$this->redrawControl('articlesInner');
//				$this->redrawControl('articlesPagerBottom');
//				$this->redrawControl('articleList');
//			} else {
//				if (!$this->getSignal()) {
//					$this->redrawControl();
//				}
//			}
//		}

		$searchString = $this->getFilterParam('q', '');

		$dateFrom = $this->getFilterParam('from');
		$dateFrom = $dateFrom && date_create_immutable($dateFrom) ? date_create_immutable($dateFrom) : null;

		$dateTo = $this->getFilterParam('to');
		$dateTo = $dateTo && date_create_immutable($dateTo) ? date_create_immutable($dateTo) : null;

		$this->addComponent($this->visualPaginatorFactory->create(), 'pager');
		$this['pager']->object = $object;
		$this['pager']->special = true;

		$paginator = $this['pager']->getPaginator();
		$paginator->itemsPerPage = $this->configService->get('calendar', 'paging');

		$this->template->cleanFilterParamForCrossroad = $this->cleanFilterParam;
		$this->template->cleanFilterParam = $this->cleanFilterParam;

		$basicElasticItemListGenerator = $this->basicElasticItemListFactory->create($searchString, $dateFrom, $dateTo);
		$elasticItemListGenerator = $this->elasticItemListFactory->create(
			$this->mutation,
			$this->cleanFilterParam,
		);
		$boxListGenerator = $this->boxListFactory->create(
			$this->mutation
		);

		$bucketFilter = $this->bucketFilterFactory->create(
			$basicElasticItemListGenerator,
			$elasticItemListGenerator,
			$boxListGenerator,
			esIndex: $this->orm->esIndex->getCommonLastActive($this->mutation),
			type: BucketFilterBuilder::TYPE_CALENDAR
		);

		$filter = $this->bucketFilterBuilder->getFilter();

		$itemsObject = $bucketFilter->getItems(
			limit: $paginator->itemsPerPage,
			offset: $paginator->offset,
			sort: $this->sortCreator->create($order, $this->currentState, $this->priceLevel),
		);
		$paginator->itemCount = $itemsObject->totalCount;

		$this->template->paginator = $paginator;
		$this->template->appDir = APP_DIR;
		$this->template->filter = $filter;
		$this->template->catalogOrder = $order;
		$this->template->categoriesProductCount = $filter->categories ?? [];
		$this->template->calendarLocalizations = $itemsObject->items;
		$this->template->linkSeo = $this->linkSeo;

		if ($this->isAjax()) {
			if (isset($this->cleanFilterParam)) {
				$this->payload->newUrl = $this->getFilterUrl();
			}

			if (isset($this->params['more'])) {
				$this->redrawControl('itemsInner');
				$this->redrawControl('itemsPagerBottom');
				$this->redrawControl('itemsList');
			} else {
				if (!$this->getSignal()) {
					$this->redrawControl();
				}
			}
		}
	}

	public function actionDetail(CalendarLocalization $object): void
	{
		$this->processExternalRedirect($object);
		$this->setObject($object);
		$this->calendarLocalization = $object;
	}


	public function renderDetail(): void
	{
//		$this->calendarLocalizationModel->increaseViews($this->calendarLocalization);

		$this->template->calendarLocalization = $this->calendarLocalization;
//		$this->template->tagsWithCount = $this->calendarTagModel->getTagsWithCount($this->mutation);
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte');
		$this->template->filterTemplate = APP_DIR . '/FrontModule/Presenters/Catalog/templates/part/filter.latte';
	}

	private function processExternalRedirect(LocalizationEntity $object): void
	{
		if ($this->getParameter('show')) {
			return;
		}

		$externalLink = $this->getObjectExternalLink($object);
		if ($externalLink !== null) {
			$this->redirectUrl($externalLink, 302);
		}
	}

	protected function getObjectExternalLink(object $object): ?string
	{
		$externalLink = $object->cf->settings->external_link ?? null;
		if ($externalLink !== null && !str_starts_with($externalLink, 'http://') && !str_starts_with($externalLink, 'https://')) {
			$externalLink = 'https://' . $externalLink;
		}

		return $externalLink;
	}

//	protected function createComponentAttachedCalendars(): AttachedCalendars
//	{
//		return $this->attachedCalendarsFactory->create($this->calendarLocalization->calendar);
//	}

//	protected function createComponentCalendarLocalizationStructuredData(): CalendarLocalizationStructuredData
//	{
//		return $this->calendarLocalizationStructuredDataFactory->create($this->calendarLocalization, $this->mutation);
//	}

}
