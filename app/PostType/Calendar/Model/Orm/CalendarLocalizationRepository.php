<?php declare(strict_types = 1);

namespace App\PostType\Calendar\Model\Orm;

use App\Model\Orm\CollectionById;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Repository\QueryForIdsByMutation;
use App\Model\Orm\Searchable;
use App\Model\Orm\Traits\HasPublicParameter;
use App\PostType\Page\Model\Orm\CommonTree;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method CalendarLocalization getById($id)
 * @method ICollection<CalendarLocalization> findByIdInPathString(CommonTree $commonTree)
 * @method ICollection<CalendarLocalization> findFiltered(array $ids)
 * @method array findAllIds(?int $limit)
 * @method ICollection<CalendarLocalization> findByExactOrder(array $ids)
 * @extends Repository<CalendarLocalization>
 */
final class CalendarLocalizationRepository extends Repository implements QueryForIdsByMutation, Searchable, CollectionById
{

	use HasPublicParameter;

	public static function getEntityClassNames(): array
	{
		return [CalendarLocalization::class];
	}

	public function findAllIdsInMutation(Mutation $mutation, ?int $limit = null): Result
	{
		$mapper = $this->mapper;
		assert($mapper instanceof CalendarLocalizationMapper);

		return $mapper->findAllIdsInMutation($mutation, $limit);
	}

	/**
	 * @return ICollection<CalendarLocalization>
	 */
	public function searchByName(string $string, array $excludedIds = []): ICollection
	{
		return $this->getMapper()->searchByName($string, $excludedIds);
	}


	public function getMapper(): CalendarLocalizationMapper
	{
		$mapper = parent::getMapper();
		assert($mapper instanceof CalendarLocalizationMapper);
		return $mapper;
	}

	/**
	 * @return ICollection<CalendarLocalization>
	 */
	public function findByIdOrder(array $ids): ICollection
	{
		return $this->findByExactOrder($ids);
	}

		public function getPublicOnlyWhereParams(): array
		{
		return [
			'public' => 1,
		];
		}

}
