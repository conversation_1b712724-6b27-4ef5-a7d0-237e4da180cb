<?php declare(strict_types = 1);

namespace App\PostType\Calendar\Model\Orm;

use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method Calendar getById($id)
 * @method ICollection<Calendar> searchByName(string $q, array $excluded = [])
 * @extends Repository<Calendar>
 */
final class CalendarRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [Calendar::class];
	}

}
