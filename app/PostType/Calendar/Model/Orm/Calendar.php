<?php declare(strict_types = 1);

namespace App\PostType\Calendar\Model\Orm;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\JsonContainer; // phpcs:ignore
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\CalendarTag\Model\Orm\CalendarTag\CalendarTag;
use App\PostType\Core\Model\ParentEntity;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Exception\NoResultException;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property int $id {primary}
 * @property string $internalName {default ''}
 *
 * @property string|null $extId
 *
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 *
 * RELATIONS
 * @property OneHasMany<CalendarLocalization> $localizations {1:M CalendarLocalization::$calendar}
 * @property ManyHasMany<Calendar> $attachedCalendars {m:m Calendar::$parentCalendars, orderBy=[internalName=ASC]}
 * @property ManyHasMany<Calendar> $parentCalendars {m:m Calendar::$attachedCalendars, isMain=true, orderBy=[internalName=ASC]}
 * @property ManyHasMany<CalendarTag> $tags {m:m CalendarTag::$calendars, isMain=true}
 *
 * VIRTUAL
 * @property ArrayHash|null $cf {virtual}
 */
class Calendar extends BaseEntity implements ParentEntity
{

	use HasCustomFields;

	public function getInternalName(): string
	{
		return $this->internalName;
	}

	public function setInternalName(string $internalName): void
	{
		$this->internalName = $internalName;
	}

	/**
	 * @return ICollection<CalendarLocalization>
	 */
	public function getLocalizations(): ICollection
	{
		return $this->localizations->toCollection();
	}

	/**
	 * @throws NoResultException
	 */
	public function getLocalization(Mutation $mutation): CalendarLocalization
	{
		$localization = $this->getLocalizations()->getByChecked(['mutation' => $mutation]);
		assert($localization instanceof CalendarLocalization);
		return $localization;
	}

}
