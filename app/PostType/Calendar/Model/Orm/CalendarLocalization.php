<?php declare(strict_types = 1);

namespace App\PostType\Calendar\Model\Orm;

use App\Model\Orm\HasImages;
use App\Model\Orm\ImageEntity;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\RoutableEntity;
use App\Model\Orm\Traits\HasCustomContent;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\Core\Model\Editable;
use App\PostType\Core\Model\ExpandedPublishable;
use App\PostType\Core\Model\HasPublishable;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Page\Model\Orm\TreeRepository;
use App\PostType\CalendarTag\Model\Orm\CalendarTag\CalendarTagLocalization;
use App\PostType\CalendarTag\Model\Orm\CalendarTag\CalendarTagLocalizationRepository;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\Validatable;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Relationships\OneHasMany;
use stdClass;
use App\Model\Orm\JsonContainer; // phpcs:ignore

/**
 * @property int $id {primary}
 * @property string $name  {default ''}
 * @property int $public {enum self::PUBLIC_*} {default self::PUBLIC_NOT_PUBLISHED}
 * @property bool $isTop {default false}
 * @property DateTimeImmutable|null $publicFrom {default now}
 * @property DateTimeImmutable|null $publicTo {default '+100 year'}
 * @property DateTimeImmutable|null $editedTime
 * @property int|null $edited
 *
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 * @property ArrayHash $customContentJson {container JsonContainer}
 * @property int $viewsNumber {default 0}
 *
 * RELATIONS
 * @property OneHasMany<CalendarLocalizationTree> $calendarLocalizationTrees {1:m CalendarLocalizationTree::$calendarLocalization, orderBy=[sort=ASC], cascade=[persist, remove]}
 * @property Mutation $mutation {m:1 Mutation, oneSided=true}
 * @property Calendar $calendar {M:1 Calendar::$localizations}
 *
 * VIRTUAL
 * @property ArrayHash|null $cf {virtual}
 * @property ArrayHash|null $cc {virtual}
 * @property-read string $template {virtual}
 * @property-read  ICollection<CalendarTagLocalization> $calendarTags {virtual}
 * @property-read  ICollection<CalendarLocalization> $attachedCalendars {virtual}
 * @property-read  ICollection<Tree> $categories {virtual}
 * @property-read  string $annotation {virtual}
 * @property int $readingTime {virtual}
 */
class CalendarLocalization extends RoutableEntity implements LocalizationEntity, ExpandedPublishable, Validatable, HasImages, Editable
{

	use HasCustomFields;
	use HasCustomContent;
	use HasPublishable;

	private TreeRepository $treeRepository;

	private CalendarTagLocalizationRepository $calendarTagLocalizationRepository;

	public function injectRepository(
		TreeRepository $treeRepository,
		CalendarTagLocalizationRepository $calendarTagLocalizationRepository
	): void
	{
		$this->treeRepository = $treeRepository;
		$this->calendarTagLocalizationRepository = $calendarTagLocalizationRepository;
	}

	protected function getterTemplate(): string
	{
		return ':Calendar:Front:Calendar:detail';
	}

	protected function getterPath(): array
	{
//		$categoryIds = $this->calendarLocalizationTrees->toCollection()->fetchPairs(null, 'tree->id');
//		if ($categoryIds === []) {
//			return [];
//		}
		$path = [];
//		$mainCategory = $this->treeRepository->findFilteredPages($categoryIds)->findBy(['rootId' => $this->getMutation()->id])->fetch();
		$mainCategory = $this->treeRepository->findBy(['uid' => 'events'])->findBy(['rootId' => $this->getMutation()->id])->fetch();

		if ($mainCategory !== null) {
			$mainPath = $mainCategory->path;
			if ($mainPath !== null) {
				$path = $mainPath;
				$path[] = $mainCategory->id;
			}
		}

		return $path;
	}

	/**
	 * @return ICollection<CalendarTagLocalization>
	 */
	protected function getterCalendarTags(): ICollection
	{
		$tagIds = $this->calendar->tags->toCollection()->fetchPairs(null, 'id');
		return $this->calendarTagLocalizationRepository
			->findBy([
				'calendarTag->id' => $tagIds,
				'mutation' => $this->mutation,
			]);
	}

	/**
	 * @return ICollection<CalendarLocalization>
	 */
	protected function getterAttachedCalendars(): ICollection
	{
		$repository = $this->getRepository();
		assert($repository instanceof CalendarLocalizationRepository);

		$attachedCalendarIds = $this->calendar->attachedCalendars->toCollection()->fetchPairs(null, 'id');
		return $repository
			->findBy([
				'id' => $attachedCalendarIds,
				'mutation' => $this->mutation,
			]);
	}


	/**
	 * @return ICollection<Tree>
	 */
	protected function getterCategories(): ICollection
	{
		$categoryIds = $this->calendarLocalizationTrees->toCollection()->fetchPairs(null, 'tree->id');
		if ($categoryIds === []) {
			/** @var EmptyCollection<Tree> $emptyCollection */
			$emptyCollection = new EmptyCollection();
			return $emptyCollection;
		}

		return $this->treeRepository->findFilteredPages($categoryIds)->findBy(['rootId' => $this->getMutation()->id]);
	}

	protected function getterAnnotation(): string
	{
		$annotation = '';
		if (isset($this->cf->annotationDetail) && $this->cf->annotationDetail) {
			$annotation = $this->cf->annotationDetail;
		} elseif (isset($this->cf->annotation) && $this->cf->annotation) {
			$annotation = $this->cf->annotation;
		}

		return $annotation;
	}

	protected function getterReadingTime(): int
	{
		$words_per_minute = 220;
		$words_per_second = $words_per_minute / 60;
		$content = '';

		if ($this->cc !== null) {
			foreach ($this->cc as $key => $c) {
				if (isset($c[0]->content) && str_starts_with($key, 'content_')) {
					$content .= ' ' . $c[0]->content;
				}
			}
		}

		// $word_count = count(explode(" ", strip_tags($content)));
		$word_count = str_word_count(strip_tags($content));

		// How many seconds (total)?
		$seconds = ceil($word_count / $words_per_second);
		$minutes = ceil($seconds / 60);

		return (int) $minutes;
	}

	public function getEsContent(): string
	{
		$content = '';
		foreach ($this->cc as $item) {
			foreach ($item as $parts) {
				if ($parts instanceof stdClass) {
					foreach ((array) $parts as $part) {
						if (is_string($part)) {
							$content .= ' ' . strip_tags($part);
						}
					}
				}
			}
		}

		return $content;
	}

	public function getId(): int
	{
		return $this->id;
	}

	public function getMutation(): Mutation
	{
		return $this->mutation;
	}


	public function setMutation(Mutation $mutation): void
	{
		$this->mutation = $mutation;
	}


	public function getParent(): Calendar
	{
		return $this->calendar;
	}


	public function setParent(ParentEntity $parentEntity): void
	{
		assert($parentEntity instanceof Calendar);
		$this->calendar = $parentEntity;
	}

	public function getPublicFrom(): DateTimeImmutable|null
	{
		return $this->publicFrom;
	}

	public function getPublicTo(): DateTimeImmutable|null
	{
		return $this->publicTo;
	}

	public function setPublicFrom(?DateTimeImmutable $publicFrom): void
	{
		$this->publicFrom = $publicFrom;
	}

	public function setPublicTo(?DateTimeImmutable $publicTo): void
	{
		$this->publicTo = $publicTo;
	}

	public function getName(): string
	{
		return $this->name;
	}

	public function setName(string $name): void
	{
		$this->name = $name;
	}

	public function getFirstImage(): ImageEntity|null
	{
		return isset($this->cf->base->mainImage) ? $this->cf->base->mainImage->getEntity() : null;
	}


	public function setEditorId(int $id): void
	{
		$this->edited = $id;
	}

	public function setEditedTime(DateTimeImmutable $editedTime): void
	{
		$this->editedTime = $editedTime;
	}

	public function getIsPublic(): bool
	{
			return $this->public !== 0;
	}

	public function setIsPublic(bool $isPublic): void
	{
		$this->public = (int) $isPublic;
	}

}
