<?php declare(strict_types = 1);

namespace App\PostType\Calendar\Model\Orm;

use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends <PERSON>balMapper<Calendar>
 */
class CalendarMapper extends D<PERSON><PERSON><PERSON>per
{

	use HasCamelCase;

	/**
	 * @return literal-string
	 */
	public function getTableName(): string
	{
	return 'calendar';
	}

	/**
	 * @return ICollection<Calendar>
	 */
	public function searchByName(string $q, array $excluded = []): ICollection
	{
		$builder = $this->builder()
			->andWhere('internalName LIKE %_like_', $q);

		if ($excluded !== []) {
			$builder->andWhere('id not in %i[]', $excluded);
		}

		return $this->toCollection($builder);
	}

}
