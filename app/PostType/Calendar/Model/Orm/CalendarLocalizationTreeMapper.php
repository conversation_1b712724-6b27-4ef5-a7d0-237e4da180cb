<?php declare(strict_types = 1);

namespace App\PostType\Calendar\Model\Orm;

use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Orm\Mapper\Dbal\Conventions\Conventions;
use Nextras\Orm\Mapper\Dbal\Conventions\IConventions;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<CalendarLocalizationTree>
 */
class CalendarLocalizationTreeMapper extends DbalMapper
{

	use HasCamelCase;

	/**
	 * @return literal-string
	 */
	public function getTableName(): string
	{
	return 'calendar_localization_tree';
	}

	protected function createConventions(): IConventions
	{
		$conventions = parent::createConventions();
		assert($conventions instanceof Conventions);

		return $conventions;
	}

}
