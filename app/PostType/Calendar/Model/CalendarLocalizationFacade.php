<?php declare(strict_types = 1);

namespace App\PostType\Calendar\Model;

use App\PostType\Calendar\Model\Orm\Calendar;
use App\PostType\Calendar\Model\Orm\CalendarLocalization;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\PostType\Core\Model\EntityLocalizationFacade;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;

final class CalendarLocalizationFacade implements EntityLocalizationFacade
{

	public function __construct(
		private Orm $orm,
	)
	{
	}


	public function create(Mutation $mutation, ParentEntity|null $localizableEntity): CalendarLocalization
	{
		$calendarLocalization = new CalendarLocalization();
		$this->orm->calendarLocalization->attach($calendarLocalization);
		$calendarLocalization->mutation = $mutation;

		if ($localizableEntity === null) {
			$localizableEntity = new Calendar();
			$calendarLocalization->calendar = $localizableEntity;
		} else {
			assert($localizableEntity instanceof Calendar);
			$calendarLocalization->calendar = $localizableEntity;
		}

		$this->orm->persistAndFlush($calendarLocalization);

		return $calendarLocalization;
	}


	public function remove(LocalizationEntity $localizableEntity): void
	{
		assert($localizableEntity instanceof CalendarLocalization);

		$parent = $localizableEntity->getParent();
		assert($parent instanceof Calendar);
		$this->orm->calendarLocalization->remove($localizableEntity);

		if ($parent->getLocalizations()->count() === 0) {
			$this->orm->calendar->remove($parent);
		}

		$this->orm->flush();
	}

}
