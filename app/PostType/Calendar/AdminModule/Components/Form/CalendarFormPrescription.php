<?php declare(strict_types=1);

namespace App\PostType\Calendar\AdminModule\Components\Form;

use App\Model\CustomField\SuggestUrls;
use App\PostType\Calendar\AdminModule\Components\Form\FormData\CalendarFormData;
use App\PostType\Calendar\Model\Orm\CalendarLocalization;
use App\PostType\Core\AdminModule\Components\Form\Builder;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\CustomFormExtender;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\RelationTemplatePart;
use App\PostType\Core\AdminModule\Components\Form\Definition\FormDefinition;
use App\PostType\Core\AdminModule\Components\Form\Definition\RelationInfoFactory;
use App\PostType\Core\AdminModule\Components\Form\Handler;
use Nette\Application\UI\Form;
use Nette\Http\Request;

class CalendarFormPrescription
{

	public function __construct(
		private readonly RelationInfoFactory $relationInfoFactory,
		private readonly Request $request,
		private readonly Builder $coreBuilder,
		private readonly Handler $coreHandler,
		private readonly SuggestUrls $urls,
		private readonly string $coreFormPath,
	)
	{
	}

	public function get(CalendarLocalization $calendarLocalization): FormDefinition
	{
		$extenders = [];
		$extenders[] = $this->addTags($calendarLocalization);
//		$extenders[] = $this->addAuthors($calendarLocalization);
//		$extenders[] = $this->addCategories($calendarLocalization);
//		$extenders[] = $this->addIsTop($calendarLocalization);

		$form = new Form();
		$form->setMappedType(CalendarFormData::class);

		return new FormDefinition(
			form: $form,
			extenders: $extenders,
		);
	}

	public function addTags(CalendarLocalization $calendarLocalization): CustomFormExtender
	{
		$tagsRelationsInfo = $this->relationInfoFactory->create(
			sourceEntity: $calendarLocalization->getParent(),
			propertyName: 'tags',
			suggestUrl: $this->urls['searchCalendarTag'],
		);

		return new CustomFormExtender(
			addHandler: function (Form $form) use ($tagsRelationsInfo) {
				$this->coreBuilder->addHasManyRelation($form, $tagsRelationsInfo, $this->request->getPost());
			},
			successHandler: function (Form $form, CalendarFormData $data) use ($tagsRelationsInfo) {
				$this->coreHandler->handleHasManyRelation($data->{$tagsRelationsInfo->propertyName}, $tagsRelationsInfo);
			},
			templateParts: [
				new RelationTemplatePart(
					relationInfo: $tagsRelationsInfo,
					templateFile: $this->coreFormPath . '/parts/relation.latte',
					type: RelationTemplatePart::TYPE_RELATION
				),
				new RelationTemplatePart(
					relationInfo: $tagsRelationsInfo,
					templateFile: $this->coreFormPath . '/parts/newItemTemplate.latte',
					type: RelationTemplatePart::TYPE_RELATION_PRESCRIPTION
				),
			]
		);
	}



//	public function addIsTop(CalendarLocalization $calendarLocalization): CustomFormExtender
//	{
//		return new CustomFormExtender(
//			function (Form $form) use ($calendarLocalization) {
//				$form->addCheckbox('isTop', 'Top')->setDefaultValue($calendarLocalization->isTop);
//			},
//			function (Form $form, CalendarFormData $data) use ($calendarLocalization) {
//				$calendarLocalization->isTop = $data->isTop;
//			},
//			[
//				new CommonTemplatePart(__DIR__ . '/settings.latte',
//					CommonTemplatePart::TYPE_SIDE,
//					['langCodeIsTop' => $calendarLocalization->mutation->langCode],
//				)
//			]
//		);
//	}

//	private function addAuthors(CalendarLocalization $calendarLocalization): CustomFormExtender
//	{
//		$authorsRelationsInfo = $this->relationInfoFactory->create(
//			sourceEntity: $calendarLocalization->getParent(),
//			propertyName: 'authors',
//			suggestUrl: $this->urls['searchAuthors'],
//		);
//
//		return new CustomFormExtender(
//			addHandler: function (Form $form) use ($authorsRelationsInfo) {
//				$this->coreBuilder->addHasManyRelation($form, $authorsRelationsInfo, $this->request->getPost());
//			},
//			successHandler: function (Form $form, CalendarFormData $data) use ($authorsRelationsInfo) {
//				$this->coreHandler->handleHasManyRelation($data->{$authorsRelationsInfo->propertyName}, $authorsRelationsInfo);
//			},
//			templateParts: [
//				new RelationTemplatePart(
//					relationInfo: $authorsRelationsInfo,
//					templateFile: $this->coreFormPath . '/parts/relation.latte',
//					type: RelationTemplatePart::TYPE_RELATION),
//				new RelationTemplatePart(
//					relationInfo: $authorsRelationsInfo,
//					templateFile: $this->coreFormPath . '/parts/newItemTemplate.latte',
//					type: RelationTemplatePart::TYPE_RELATION_PRESCRIPTION),
//			]
//		);
//	}

//	private function addCategories(CalendarLocalization $calendarLocalization): CustomFormExtender
//	{
//		$url = $this->urls['searchMutationPage'];
//		$url->params['mutationId'] = $calendarLocalization->mutation->id;
//		$url->params['templates'] = [':Calendar:Front:Calendar:default'];
//
//		$categoriesRelationsInfo = $this->relationInfoFactory->create(
//			sourceEntity: $calendarLocalization,
//			propertyName: 'calendarLocalizationTrees',
//			suggestUrl: $url,
//			inputSuggestPropertyName: 'name',
//			toggleName: 'categories',
//			dragAndDrop: true,
//			builderCollection: $calendarLocalization->categories,
//
//		);
//
//		return new CustomFormExtender(
//			addHandler: function (Form $form) use ($categoriesRelationsInfo) {
//				$this->coreBuilder->addHasManyRelation($form, $categoriesRelationsInfo, $this->request->getPost());
//			},
//			successHandler: function (Form $form, CalendarFormData $data) use ($categoriesRelationsInfo, $calendarLocalization) {
//				$ids = Handler::readOnlyValidIds($data->{$categoriesRelationsInfo->propertyName});
//				$this->calendarLocalizationModel->setCategoriesByIds($calendarLocalization, $ids);
//			},
//			templateParts: [
//				new RelationTemplatePart(
//					relationInfo: $categoriesRelationsInfo,
//					templateFile: $this->coreFormPath . '/parts/relation.latte',
//					type: RelationTemplatePart::TYPE_RELATION),
//				new RelationTemplatePart(
//					relationInfo: $categoriesRelationsInfo,
//					templateFile:$this->coreFormPath . '/parts/newItemTemplate.latte',
//					type: RelationTemplatePart::TYPE_RELATION_PRESCRIPTION),
//			]
//		);
//	}

}
