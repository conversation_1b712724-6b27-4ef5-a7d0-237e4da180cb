<?php

namespace App\PostType\Calendar\AdminModule\Components\DataGrid;

use App\PostType\Core\AdminModule\Components\DataGrid\Definition\DataGridDefinition;

class CalendarDataGridPrescription
{

	public function __construct(
		//		private readonly Translator $translator,
	)
	{
	}

	public function get(): DataGridDefinition
	{
		return new DataGridDefinition(
			extenders: [
			//				new CustomDataGridExtender(
			//					improveFunction: $this->addIsTop(...)
			//				),
			]
		);
	}

//	private function addIsTop(DataGrid $dataGrid): void
//	{
//		$dataGrid->addColumnText('isTop', 'isTop')
//		->setRenderer(function (CalendarLocalization $calendarLocalization) {
//			return ($calendarLocalization->isTop) ?
//				$this->translator->translate('top') :
//				$this->translator->translate('common');
//		})
//		->setFilterSelect([
//			true => $this->translator->translate('top'),
//			false => $this->translator->translate('common'),
//		])->setPrompt($this->translator->translate('all'));
//	}

}
