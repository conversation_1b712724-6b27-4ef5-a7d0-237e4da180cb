parameters:
	config:
		calendar:
			paging: 12 #temp low number - to testing

	postTypeRoutes:
		Calendar: calendar

cf:
	templates:
		calendarLocalization:
			base:
				extends: @cf.base
			settings:
				type: group
				label: "Parametry"
				items:
					mainImage:
						type: image
						label: "<PERSON><PERSON><PERSON><PERSON> obrá<PERSON>"
					address:
						type: text
						label: "<PERSON>res<PERSON>"
					from:
						type: dateTime
						label: "Začátek události"
					to:
						type: dateTime
						label: "Konec události"
					link:
						type: text
						label: "Externí URL (bez přesměrování)"
					external_link:
						type: text
						label: "Externí URL"
					map_link:
						type: text
						label: "Odkaz na mapu"


cc:
	templates:
		# ":Calendar:Front:Calendar:detail": []

application:
	mapping:
		Calendar: App\PostType\Calendar\*Module\Presenters\*Presenter

services:
	- App\PostType\Calendar\Model\Orm\CalendarLocalizationModel
	- App\PostType\Calendar\AdminModule\Components\Form\CalendarFormPrescription(coreFormPath: %appDir%/PostType/Core/AdminModule/Components/Form)
	- App\PostType\Calendar\Model\CalendarLocalizationFacade
	- App\PostType\Calendar\AdminModule\Components\DataGrid\CalendarDataGridPrescription
#	- App\PostType\Calendar\FrontModule\Components\CalendarLocalizationStructuredData\CalendarLocalizationStructuredDataFactory
#	-
#		implement: App\PostType\Calendar\FrontModule\Components\Attached\AttachedCalendarsFactory
#		inject: true
