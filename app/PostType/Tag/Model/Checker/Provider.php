<?php

namespace App\PostType\Tag\Model\Checker;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\ProductLocalization\ProductLocalizationRepository;

class Provider
{

	public function __construct(
		private readonly ProductLocalizationRepository $productLocalizationRepository,
	)
	{
	}

	public function getItems(Mutation $mutation): \Generator
	{
		foreach ($this->productLocalizationRepository->findAllIdsInMutation($mutation) as $item) {
			yield $item->id;
		}
	}

}
