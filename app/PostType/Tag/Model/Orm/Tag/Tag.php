<?php declare(strict_types = 1);

namespace App\PostType\Tag\Model\Orm\Tag;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Tag\Model\Orm\TagLocalization\TagLocalization;
use App\Model\Orm\BackedEnumWrapper;// phpcs:ignore
use App\PostType\Tag\Model\TagType;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Exception\NoResultException;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;
use App\Model\Orm\JsonContainer;// phpcs:ignore

/**
 * @property int                          $id                      {primary}
 * @property string|null $extId {default null}
 * @property string                       $internalName            {default ''}
 * @property TagType                      $type                    {wrapper BackedEnumWrapper}
 * @property string|null                  $customType              {default null}
 * @property string                       $color                   {default '#ffffff'}
 * @property ArrayHash                    $customFieldsJson        {container JsonContainer}
 *
 * RELATIONS
 * @property OneHasMany<TagLocalization> $localizations           {1:M TagLocalization::$tag}
 * @property ManyHasMany<Product>        $products                {m:m Product::$tags, isMain=true}
 *
 * VIRTUAL
 * @property ArrayHash|null               $cf                      {virtual}
 */
final class Tag extends BaseEntity implements ParentEntity
{

	use HasCustomFields;

	public function getInternalName(): string
	{
		return $this->internalName;
	}

	public function setInternalName(string $internalName): void
	{
		$this->internalName = $internalName;
	}

	/**
	 * @return ICollection<TagLocalization>
	 */
	public function getLocalizations(): ICollection
	{
		return $this->localizations->toCollection();
	}

	/**
	 * @throws NoResultException
	 */
	public function getLocalization(Mutation $mutation): TagLocalization
	{
		$localization = $this->getLocalizations()->getByChecked(['mutation' => $mutation]);
		assert($localization instanceof TagLocalization);
		return $localization;
	}

}
