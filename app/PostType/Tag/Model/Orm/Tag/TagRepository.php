<?php declare(strict_types = 1);

namespace App\PostType\Tag\Model\Orm\Tag;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Product\Product;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Tag\Model\TagType;
use DateTimeImmutable;
use Nette\Utils\DateTime;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method ICollection<Tag> findByIds(array $cond)
 * @method array findProductsInProductTreeOrderedByScore(Tree $tree, int $limit, int $offset)
 * @method array findProductsByRequalificationPossibility(int $limit, int $offset, Product $product = null)
 * @method array findProductsToPreorderWithPublishDate(int $limit = 0, int $offset = 0)
 * @method ICollection<Tag> findProductsWithPresent(?int $limit = null, ?int $offset = null)
 * @method void removeAssignees(TagType $tagType, array $products)
 * @method void removeAssignee(TagType $tagType, Product $product)
 * @method void updateTagTimestamp(TagType $tagType, Product $product)
 * @method array getProductByDate(TagType $tagType, DateTime $date, int $offset, int $batchSize)
 * @method array getAllProcessedProducts(TagType $tagType, DateTime $date, ?int $offset, ?int $batchSize)
 * @method Result getProductRelations(Product $product)
 * @method void replaceRelationRow(Product $product, Tag $tag, ?DateTimeImmutable $from, ?DateTimeImmutable $to)
 * @method ICollection<Tag> findActiveTags(Product $product, ?Mutation $mutation = null, int $limit = 3)
 * @extends Repository<Tag>
 */
final class TagRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [Tag::class];
	}

	public function getByExtId(int|string $id): ?Tag
	{
		return $this->getBy(['extId' => (string) $id]);
	}

}
