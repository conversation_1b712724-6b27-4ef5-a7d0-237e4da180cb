<?php declare(strict_types = 1);

namespace App\PostType\Tag\Model\Orm\TagLocalization;

use App\Model\Orm\CollectionById;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Repository\QueryForIdsByMutation;
use App\Model\Orm\Searchable;
use App\Model\Orm\Traits\HasPublicParameter;
use DateTimeImmutable;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @extends Repository<TagLocalization>
 * @method Result getProductRelations(Product $product, Mutation $mutation)
 * @method void replaceRelationRow(Product $product, TagLocalization $tag, ?DateTimeImmutable $from, ?DateTimeImmutable $to)
 * @method void removeRelationRow(Product $product, TagLocalization $tag)
 * @method void removeAssignee(TagLocalization $tagLocalization, Product $product)
 */
final class TagLocalizationRepository extends Repository implements QueryForIdsByMutation, CollectionById, Searchable
{

	use HasPublicParameter;

	public static function getEntityClassNames(): array
	{
		return [TagLocalization::class];
	}

	public function getPublicOnlyWhereParams(): array
	{
		return [
			'public' => 1,
		];
	}

	public function getMapper(): TagLocalizationMapper
	{
		$mapper = parent::getMapper();
		assert($mapper instanceof TagLocalizationMapper);
		return $mapper;
	}

	/**
	 * @return ICollection<TagLocalization>
	 */
	public function findByIdOrder(array $ids): ICollection
	{
		return $this->getMapper()->findByIdOrder($ids);
	}

	public function findAllIdsInMutation(Mutation $mutation, ?int $limit = null): Result
	{
		return $this->getMapper()->findAllIdsInMutation($mutation, $limit);
	}

	/**
	 * @return ICollection<TagLocalization>
	 */
	public function searchByName(string $string, array $excludedIds = []): ICollection
	{
		return $this->getMapper()->searchByName($string, $excludedIds);
	}

	public function getByExtId(string $id): ?TagLocalization
	{
		return $this->getBy(['extId' => $id]);
	}

}
