<?php declare(strict_types = 1);

namespace App\PostType\Tag\Model\Orm\TagLocalization;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Traits\HasCamelCase;
use DateTimeImmutable;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<TagLocalization>
 */
final class TagLocalizationMapper extends DbalMapper
{

	use HasCamelCase;

	/**
	 * @return literal-string
	 */
public function getTableName(): string
{
	return 'tag_localization';
}

	public function findAllIdsInMutation(Mutation $mutation, ?int $limit = null): Result
	{
		$builder = $this->builder()->select('tl.id')
			->from($this->getTableName(), 'tl')
			->andWhere('tl.mutationId = %i', $mutation->getPersistedId())
			->limitBy($limit);

		return $this->connection->queryByQueryBuilder($builder);
	}

	/**
	 * @return ICollection<TagLocalization>
	 */
	public function findByIdOrder(array $ids): ICollection
	{
		$builder = $this->builder()
			->andWhere('id in %i[]', $ids)
			->orderBy('%raw', 'FIELD(id, ' . implode(',', $ids) . ')');

		return $this->toCollection($builder);
	}

	public function getProductRelations(Product $product, Mutation $mutation): Result
	{
		return $this->connection->query(
			'SELECT tlxp.*, t.internalName, tl.name FROM tag_localization_x_product as tlxp
         			LEFT JOIN tag_localization as tl on (tlxp.tagLocalizationId = tl.id)
         			LEFT JOIN tag as t on (t.id = tl.tagId)
         			where productId = %i and tl.mutationId = %i order by tl.position',
			$product->id,
			$mutation->id
		);
	}

	/**
	 * @return ICollection<TagLocalization>
	 */
	public function searchByName(string $q, array $excluded = []): ICollection
	{
		$builder = $this->builder()
			->select('tl.*')->from($this->getTableName(), 'tl')
			->joinLeft('[tag] AS [t]', '[t.id] = [tl.tagId]')
			->andWhere('tl.name LIKE %_like_ or t.internalName LIKE %_like_', $q, $q);

		if ($excluded !== []) {
			$builder->andWhere('tl.id not in %i[]', $excluded);
		}

		return $this->toCollection($builder);
	}

	public function replaceRelationRow(Product $product, TagLocalization $tagLocalization, ?\DateTimeImmutable $from, ?\DateTimeImmutable $to): void
	{
		$data = [
			'tagLocalizationId' => $tagLocalization->id,
			'productId' => $product->id,
			'from' => $from,
			'to' => $to,
			'edited' => new DateTimeImmutable(),
		];
		$this->connection->query('INSERT INTO [tag_localization_x_product] %values ON DUPLICATE KEY UPDATE %set', $data, $data);
	}

	public function removeRelationRow(Product $product, TagLocalization $tagLocalization): void
	{
		$this->connection->query('DELETE FROM [tag_localization_x_product] WHERE productId = %i and tagLocalizationId = %i', $product->id, $tagLocalization->id);
	}

}
