<?php declare(strict_types = 1);

namespace App\PostType\Tag\Model\Orm\TagLocalization;

use App\Model\Orm\HasImages;
use App\Model\Orm\ImageEntity;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Product\Product;
use App\Model\Orm\RoutableEntity;
use App\Model\Orm\Synchronizable;
use App\Model\Orm\Traits\HasCache;
use App\Model\Orm\Traits\HasConfigService;
use App\Model\Orm\Traits\HasCustomContent;
use App\Model\Orm\Traits\HasCustomFields;
use App\Model\Orm\Traits\HasTemplateCache;
use App\PostType\Core\Model\Editable;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Core\Model\Publishable;
use App\PostType\Tag\Model\Orm\Tag\Tag;
use App\Model\Orm\JsonContainer;// phpcs:ignore
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Relationships\ManyHasMany;

/**
 * @property int                    $id                                          {primary}
 * @property string|null $extId {default null}
 * @property string                 $name                                        {default ''}
 * @property bool                   $public                                      {default false}
 * @property bool                   $isInFilter                                  {default false}
 * @property bool $isInImage                                  {default false}
 * @property DateTimeImmutable|null $editedTime
 * @property DateTimeImmutable|null $createdTime                                 {default now}
 * @property int|null               $edited
 * @property int|null               $position
 *
 * @property ArrayHash              $customFieldsJson                            {container JsonContainer}
 * @property ArrayHash              $customContentJson                           {container JsonContainer}
 *
 * RELATIONS
 * @property Mutation               $mutation                                    {m:1 Mutation, oneSided=true}
 * @property Tag                    $tag                                         {M:1 Tag::$localizations}
 * @property ManyHasMany<Product>   $products                					 {m:m Product::$tagLocalizations, isMain=true}
 *
 * VIRTUAL
 * @property ArrayHash|null         $cf                                          {virtual}
 * @property ArrayHash|null         $cc                                          {virtual}
 * @property-read string            $template                                    {virtual}
 */
final class TagLocalization extends RoutableEntity implements LocalizationEntity, Publishable, HasImages, Editable, Synchronizable
{

	use HasConfigService;
	use HasCache, HasCustomContent, HasTemplateCache, HasCustomFields {
		HasCache::onAfterPersist as hasCacheOnAfterPersist;
		HasCustomFields::onAfterPersist as hasCustomFieldsOnAfterPersist;
		HasTemplateCache::onAfterPersist as hasTemplateCacheOnAfterPersist;
		HasCustomContent::onAfterPersist as hasCustomContentOnAfterPersist;
	}

	public function getId(): int
	{
		return $this->id;
	}

	protected function getterTemplate(): string
	{
		return ':Tag:Front:Tag:detail';
	}

	protected function getterPath(): array
	{
		if (!isset($this->cache['path'])) {

			$this->cache['path'] = $this->createPath();
		}

		return $this->cache['path'];
	}

	private function createPath(): array
	{
		$path = [];

		if ($rootId = $this->mutation->getRealRootId()) {
			$path[] = $rootId;
		}

		$path[] = $this->mutation->pages->eshop->getPersistedId();

		if ($treeTheme = $this->mutation->pages->tag) {
			$path[] = $treeTheme->getPersistedId();
		}

		return $path;
	}

	public function getMutation(): Mutation
	{
		return $this->mutation;
	}

	public function setMutation(Mutation $mutation): void
	{
		$this->mutation = $mutation;
	}

	public function getParent(): Tag
	{
		return $this->tag;
	}

	public function setParent(ParentEntity $parentEntity): void
	{
		assert($parentEntity instanceof Tag);
		$this->tag = $parentEntity;
	}

	public function setIsPublic(bool $isPublic): void
	{
		$this->public = $isPublic;
	}

	public function getName(): string
	{
		return $this->name;
	}

	public function setName(string $name): void
	{
		$this->name = $name;
	}

	public function getPosition(): ?int
	{
		return $this->position;
	}

	public function setPosition(?int $position): void
	{
		$this->position = $position;
	}

	public function getFirstImage(): ImageEntity|null
	{
		return isset($this->getParent()->cf->base->mainImage) ? $this->getParent()->cf->base->mainImage->getEntity() : null;
	}

	public function setEditorId(int $id): void
	{
		$this->edited = $id;
	}

	public function setEditedTime(DateTimeImmutable $editedTime): void
	{
		$this->editedTime = $editedTime;
	}

	public function getIsPublic(): bool
	{
		return $this->public;
	}

	public function onAfterPersist(): void
	{
		$this->hasCacheOnAfterPersist();
		$this->hasCustomFieldsOnAfterPersist();
		$this->hasTemplateCacheOnAfterPersist();
		$this->hasCustomContentOnAfterPersist();
	}

	public function getExternalId(): ?int
	{
		if ($this->extId === null) {
			return null;
		}
		return (int) $this->extId;
	}

	public function setExternalId(?string $externalId): void
	{
		$this->extId = $externalId;
	}

}
