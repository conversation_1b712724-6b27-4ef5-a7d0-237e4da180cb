{varType App\PostType\Tag\Model\Orm\TagLocalization\TagLocalization $tagLocalization}
{default $content = $object->cf->tagContent->text ?? null}

{block content}
	<div class="b-layout b-layout--catalog">
		<div class="row-main">
			<div class="b-layout__inner">
				<div class="b-layout__bc">
					{snippetArea breadcrumbArea}
						{control breadcrumb}
					{/snippetArea}
				</div>
				<div class="b-layout__title">
					{snippet catalogHeader}
						{include $templates.'/part/box/annot-entity.latte', class=>'u-mb-xxs u-mb-sm@lg', titlePrefix: 'title_tag_prefix', productCount: $presenter['catalogProducts']['pager']->getPaginator()->getItemCount()}
						<hr class="u-mb-xs u-d-n u-d-b@lg">
					{/snippet}
				</div>
				<div class="b-layout__categories">
					{control mainCategories}
				</div>
				<div class="b-layout__nav u-mb-xxs u-mb-0@lg" data-controller="toggle-class">
					{embed $templates.'/part/core/toggle.latte', class=>'toggle--filter', icon=>'filter', arrow=>true}
						{block text}
							{_"btn_filter_products"}
						{/block}
					{/embed}

					<div class="b-layout__menu">
						{snippet filterArea}
							{include $templates .'/../Presenters/Catalog/templates/part/filter.latte', class=>false}
						{/snippet}
					</div>
				</div>

				<div class="b-layout__selected">
					{snippet filterSelected}
						{include $templates .'/../Presenters/Catalog/templates/part/selectedFilters.latte', class=>'u-mb-xxs u-mb-xs@lg'}
					{/snippet}
				</div>

				<div class="b-layout__sort">
					{snippet filterSetup}
						{include $templates .'/../Presenters/Catalog/templates/part/sort.latte'}
					{/snippet}
				</div>

				<div class="b-layout__bestsellers" n:snippet="bestsellers">
					{control bestsellers}
				</div>

				<div class="b-layout__main u-mb-last-0">
					{snippet products}
						{control catalogProducts}
					{/snippet}


					{if $content !== null}
						<hr class="u-mb-xs u-mb-md@md">
						{include $templates . '/part/box/content.latte', class=>'b-content--catalog', content=>$content}
					{/if}

					{if $marketingConsent}
						{snippet recomendedList}
							{* <hr class="u-mb-xs u-mb-xl@md" n:if="$presenter['productListRecommended']->getProductCount() > 0">
							{php $presenter['productListRecommended']->setTemplateParameters(['class' => 'section--recommended'])}
							{control productListRecommended} *}
						{/snippet}
					{/if}
				</div>
			</div>
		</div>
	</div>
{/block}
