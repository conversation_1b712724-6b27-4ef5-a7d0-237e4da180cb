<?php declare(strict_types = 1);

namespace App\PostType\Tag\FrontModule\Presenters;

use App\FrontModule\Components\Bestsellers\BestsellersFactory;
use App\FrontModule\Components\CatalogProducts\CatalogProducts;
use App\FrontModule\Components\CatalogProducts\CatalogProductsCallbackBuilder;
use App\FrontModule\Components\CatalogProducts\CatalogProductsFactory;
use App\FrontModule\Components\MainCategories\MainCategories;
use App\FrontModule\Components\MainCategories\MainCategoriesFactory;
use App\FrontModule\Presenters\BasePresenter;
use App\FrontModule\Presenters\HasPagerLimits;
use App\FrontModule\Presenters\Pageable;
use App\Model\BucketFilter\BucketFilterBuilder;
use App\Model\BucketFilter\BucketFilterBuilderFactory;
use App\Model\ElasticSearch\Product\Repository;
use App\Model\Link\LinkSeo;
use App\PostType\Tag\Model\Orm\TagLocalization\TagLocalization;
use Nette\Application\Attributes\Persistent;
use Nette\DI\Attributes\Inject;
use stdClass;

class TagPresenter extends BasePresenter implements Pageable
{

	use HasPagerLimits;

	public const LIMIT_TO_TOP_CATEGORIES_SHOW = 60;
	public const LIMIT_FOR_SYSTEM_TAGS = 200;

	private array $filterParams;

	private TagLocalization $tagLocalization;

	/** @var array */
	private array $cleanFilterParam;

	#[Inject]
	public BestsellersFactory $bestsellersFactory;

	#[Inject]
	public CatalogProductsFactory $catalogProductsFactory;

	#[Inject]
	public CatalogProductsCallbackBuilder $catalogProductsCallbackBuilder;

	#[Persistent]
	public int $page = 1;

	#[Persistent]
	public ?string $category = null;

	private stdClass $filter;

	private BucketFilterBuilder $bucketFilterBuilder;

	public function __construct(
		private readonly BucketFilterBuilderFactory $bucketFilterBuilderFactory,
		private readonly MainCategoriesFactory $mainCategoriesFactory,
		private readonly Repository $esProductRepository,
		private readonly LinkSeo $linkSeo,
	)
	{
	}

	protected function startup(): void
	{
		parent::startup();

		$filterParams = $this->getRequest()->getParameter('filter');
		if ($filterParams === null) {
			$this->filterParams = [];
		} else {
			$this->filterParams = $filterParams;
		}
		$this->cleanFilterParam = $this->filterParams;
	}

	public function actionDetail(TagLocalization $object, string $order = 'top'): void
	{
		$this->setObject($object);
		$this->tagLocalization = $object;

		$this->bucketFilterBuilder = $this->bucketFilterBuilderFactory->create($this, $object, $this->currentState, $this->priceLevel, $order, $this->filterParams, itemsToSkip: ['isNew']);
		$this->bucketFilterBuilder->setCategory($this->category);

		if ($this->tagLocalization->tag->type->isSystemTag()) {
			$productIds = $this->esProductRepository->findTopTagsIds($this->tagLocalization, self::LIMIT_FOR_SYSTEM_TAGS);
			$this->bucketFilterBuilder->setProductIds($productIds);
		}
	}
	public function renderDetail(string $order = 'top'): void
	{
		$this->template->cleanFilterParamForCrossroad = $this->cleanFilterParam;
		$this->template->cleanFilterParam = $this->cleanFilterParam;

		$this->filter = $this->bucketFilterBuilder->getFilter();
		$this->seoToolsFilter = $this->filter;
		$this->seoToolsSort = $this->bucketFilterBuilder->getSort();

		$this->template->filter = $this->filter;
		$this->template->catalogOrder = $order;
		$this->template->tagLocalization = $this->tagLocalization;
		$this->template->categories = [];
		$this->template->linkSeo = $this->linkSeo;
		$this->template->linkSeoPage = $this->mutation->pages->eshop;

		$this->bucketFilterBuilder->redrawSnippets($this->cleanFilterParam);
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->getTemplate()->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte');
	}


	public function createComponentMainCategories(): MainCategories
	{
		$mainCategories = $this->mainCategoriesFactory->create($this['robots']);
		if (isset($this->filter->topCategories)
			&& $this['catalogProducts']->getTotalCount() > self::LIMIT_TO_TOP_CATEGORIES_SHOW) {
			$mainCategories->setCategoriesId($this->filter->topCategories);
		}

		$mainCategories->setSelectedCategory($this->bucketFilterBuilder->getCategory());
		return $mainCategories;
	}

	protected function createComponentCatalogProducts(): CatalogProducts
	{
		$paramsToTemplate = [
			'productTitle' => 'h2',
			'ajaxPage' => true,
			'cleanFilterParam' => $this->cleanFilterParam,
			'class' => 'u-mb-0',
		];

		$findCatalogProductsDataCallback = $this->catalogProductsCallbackBuilder->build($this->bucketFilterBuilder, $this['bestsellers']->getBestsellerIds());

		return $this->catalogProductsFactory->create(
			$this->object,
			$this->setup,
			$findCatalogProductsDataCallback,
			$this->getPageParameter(),
			$paramsToTemplate,
			'catalog',
			'tag',
		);
	}

}
