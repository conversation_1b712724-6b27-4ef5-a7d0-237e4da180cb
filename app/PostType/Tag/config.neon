application:
	mapping:
		Tag: App\PostType\Tag\*Module\Presenters\*Presenter

parameters:

	config:
		tagColors:
			'green': '<PERSON>pra<PERSON> zdarma' #1aad55
			'blue': 'Novinka' #3e5efc
			'rainbow': 'Sleva' # přechod červená - fialová
			'red': 'Poslední šance' #d0204c
			'cz': 'Logo ČR' # žlutý přechod + ikona ČR
			'yellow-gradient': '<PERSON><PERSON><PERSON>' #f8c471
			'primary': 'Black Friday' #010E47
			'drak': 'Drak Friday' # custom obrázek pro Drak Friday

	postTypeRoutes:
		Tag: tag

cf:
	fields:
		tagTypeCount:
			type: text
			subType: number
			label: ''

		# tagBanner:
		# 	type: group
		# 	label: 'Banner'
		# 	items:
		# 		banner:
		# 			type: image # TODO size
		tagOrder:
			type: group
			label: 'Nastavení způsobu výchozího řazení'
			items:
				order:
					type: select
					options: [
							{ label: "Nejnovější", value: "newest" },
							{ label: "Nejstarš<PERSON>", value: "oldest" },
							{ label: "Nejdražš<PERSON>", value: "expensive" },
							{ label: "Nejlevnější", value: "cheapes" },
							{ label: "Nejprodávanější", value: "bestseller" },
							{ label: "Podle názvu", value: "name" },
					]
		tagProductsAllowedCategories:
			type: list
			label: Nastavení povolených kategorií
			items:
				productCategories:
					type: suggest
					subType: tree
					label: 'Povolená kategorie'
					url: @cf.suggestUrls.searchMutationPage
		categoryTagCounts:
			type: group
			label: 'Nastavení výchozích počtů štítků'
			items:
				tagNewCount:
					extends: @cf.tagTypeCount
					label: 'Počet štítků typu "Nový"'
				tagTopProductCount:
					extends: @cf.tagTypeCount
					label: 'Počet štítků typu "TOP produkt"'
		tagAnnotation:
			type: group
			label: 'Anotace'
			items:
				text:
					type: textarea
		tagContent:
			type: group
			label: 'Obsah'
			items:
				text:
					type: tinymce

	templates:
		#tagLocalization: [@cf.tagAnnotation, @cf.tagContent, @cf.parameterForFilter, @cf.tagOrder, @cf.tagProductsAllowedCategories] # @cf.tagBanner

services:
	- App\PostType\Tag\Model\TagLocalizationFacade
	- App\PostType\Tag\Model\Orm\TagLocalization\TagLocalizationModel
	- App\PostType\Tag\AdminModule\Components\TagDataGrid\TagDataGridPrescription
	- App\PostType\Tag\AdminModule\Components\TagDetailForm\TagDetailFormPrescription
	- App\PostType\Tag\AdminModule\Components\TagDetailForm\TagLocalizationFormData
	- App\PostType\Tag\Model\Orm\Tag\TagModel



	#TagChecker
	- App\PostType\Tag\Model\Checker\Provider
	- App\PostType\Tag\Model\Checker\Checker
