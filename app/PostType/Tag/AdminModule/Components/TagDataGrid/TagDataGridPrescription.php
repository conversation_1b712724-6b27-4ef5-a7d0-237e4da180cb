<?php declare(strict_types = 1);

namespace App\PostType\Tag\AdminModule\Components\TagDataGrid;

use App\PostType\Core\AdminModule\Components\DataGrid\Definition\DataGridDefinition;
use App\PostType\Core\AdminModule\Components\DataGrid\Definition\Extenders\CustomDataGridExtender;
use Ublaboo\DataGrid\DataGrid;
use App\Model\ConfigService;
use App\Model\Translator;

readonly class TagDataGridPrescription
{

	public function __construct(
		private readonly ConfigService $configService,
		private readonly Translator $translator,
	)
	{
	}
	public function get(): DataGridDefinition
	{
		$datagrid = new DataGrid();

		return new DataGridDefinition(
			dataGrid: $datagrid,
			extenders: [
				new CustomDataGridExtender(
					improveFunction:$this->updateColumns(...),
				),
			],
		);
	}

	public function updateColumns(DataGrid $dataGrid): void
	{
		$colorsEnum = $this->configService->get('tagColors');

		$dataGrid
			->addColumnNumber('position', 'position')
			->setSortable();
		$dataGrid
			->addColumnText('color', 'color', 'tag.color')
			->setRenderer(function ($item) use ($colorsEnum) {
				return $colorsEnum[$item->tag->color] ?? $item->tag->color;
			});
		$dataGrid
			->addColumnText('isInFilter', 'isInFilter', 'isInFilter')
			->setSortable()
			->setRenderer(function ($item) {
				return $item->isInFilter ? 'ano' : 'ne';
			});
		$dataGrid
			->addColumnText('type', 'type', 'tag.type')
			->setSortable()
			->setRenderer(function ($item) {
				return $this->translator->translate($item->tag->type->name);
			});
		$dataGrid->removeColumn('internalName');
	}

}
