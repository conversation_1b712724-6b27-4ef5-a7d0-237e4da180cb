<?php declare(strict_types = 1);

namespace App\PostType\Tag\AdminModule\Components\TagDetailForm;

use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\CustomFormExtender;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\CommonTemplatePart;
use App\PostType\Core\AdminModule\Components\Form\Definition\FormDefinition;
use App\PostType\Tag\Model\Orm\TagLocalization\TagLocalization;
use Nette\Application\UI\Form;
use App\Model\ConfigService;
use App\PostType\Tag\Model\TagType;

class TagDetailFormPrescription
{

	public function __construct(
		private readonly ConfigService $configService,
	)
	{
	}


	public function getPrescription(TagLocalization $tagLocalization): FormDefinition
	{
		$form = new Form();
		$form->setMappedType(TagLocalizationFormData::class);
		$extenders = [];
		$extenders[] = $this->addColor($tagLocalization);
		$extenders[] = $this->addType($tagLocalization);
		$extenders[] = $this->addPosition($tagLocalization);

		return new FormDefinition(
			form: $form,
			extenders: $extenders,
			templateParameters: [
				'showDeleteButton' => !$tagLocalization->tag->type->isSystemTag(),
			]
		);
	}


	private function addColor(TagLocalization $tagLocalization): CustomFormExtender
	{
		return new CustomFormExtender(
			addHandler: function (Form $form) use ($tagLocalization) {
				$colorValues = $this->configService->get('tagColors');
				$form->addSelect('color', 'tag_color', $colorValues);
				if (isset($colorValues[$tagLocalization->tag->color])) {
					$form['color']->setDefaultValue($tagLocalization->tag->color);
				}
			},
			successHandler: function (Form $form, TagLocalizationFormData $formData) use ($tagLocalization) {
				$tagLocalization->tag->color = $formData->color;
			},
			templateParts: [
				new CommonTemplatePart(
					templateFile: __DIR__ . '/templates/color.latte',
					type: CommonTemplatePart::TYPE_SIDE
				),
			]
		);
	}

	private function addType(TagLocalization $tagLocalization): CustomFormExtender
	{
		return new CustomFormExtender(
			addHandler: function (Form $form) use ($tagLocalization) {
				$form->addSelect('type', 'tag_type', TagType::toArray())
					->setDefaultValue($tagLocalization->tag->type);
			},
			successHandler: function (Form $form, TagLocalizationFormData $formData) use ($tagLocalization) {
				$tagLocalization->tag->type = $formData->type;
			},
			templateParts: [
				new CommonTemplatePart(
					templateFile: __DIR__ . '/templates/type.latte',
					type: CommonTemplatePart::TYPE_SIDE
				),
			]
		);
	}

	private function addPosition(TagLocalization $tagLocalization): CustomFormExtender
	{
		return new CustomFormExtender(
			addHandler: function (Form $form) use ($tagLocalization) {
				$form->addInteger('position', 'tag_position')
					->setRequired()
					->setDefaultValue($tagLocalization->position);
			},
			successHandler: function (Form $form, TagLocalizationFormData $formData) use ($tagLocalization) {
				$tagLocalization->position = $formData->position;
			},
			templateParts: [
				new CommonTemplatePart(
					templateFile: __DIR__ . '/templates/position.latte',
					type: CommonTemplatePart::TYPE_SIDE,
				),
			]
		);
	}

}
