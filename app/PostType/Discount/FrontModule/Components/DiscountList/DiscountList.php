<?php declare(strict_types = 1);

namespace App\PostType\Discount\FrontModule\Components\DiscountList;

use App\Model\Orm\Mutation\Mutation;
use App\Model\TranslatorDB;
use App\PostType\Discount\Model\Orm\DiscountLocalization\DiscountLocalizationRepository;
use Nette\Application\UI\Control;
use Nette\Utils\DateTime;
use Nextras\Orm\Collection\ICollection;

final class DiscountList extends Control
{

	public function __construct(private readonly Mutation $mutation, private readonly DiscountLocalizationRepository $discountLocalizationRepository, private readonly TranslatorDB $translator)
	{
	}

	public function render(): void
	{
		$dateToday = DateTime::from(date('Y-m-d H:i:s'));

		$this->getTemplate()->discountHrefs = $this->mutation->pages->discount->cf->discountHref;

		$this->getTemplate()->discounts = $this->discountLocalizationRepository->findBy([
			ICollection::OR,
			['public' => true, 'publicTo>=' => $dateToday, 'publicFrom<=' => $dateToday],
			['public' => true, 'publicTo' => null, 'publicFrom<=' => $dateToday],
			['public' => true, 'publicTo>=' => $dateToday, 'publicFrom' => null],
			['public' => true, 'publicTo' => null, 'publicFrom' => null],
		])->orderBy('discount->order', ICollection::ASC_NULLS_LAST);

		$this->getTemplate()->setFile(__DIR__ . '/templates/discountList.latte');
		$this->getTemplate()->mutation = $this->mutation;
		$this->getTemplate()->setTranslator($this->translator);
		$this->getTemplate()->render();
	}

}
