<?php declare(strict_types = 1);

namespace App\PostType\Discount\FrontModule\Presenters;

use App\Components\VisualPaginator\PagerLimits;
use App\FrontModule\Components\Bestsellers\Bestsellers;
use App\FrontModule\Components\Bestsellers\BestsellersFactory;
use App\FrontModule\Components\CatalogProducts\CatalogProducts;
use App\FrontModule\Components\CatalogProducts\CatalogProductsCallbackBuilder;
use App\FrontModule\Components\CatalogProducts\CatalogProductsFactory;
use App\FrontModule\Components\MainCategories\MainCategories;
use App\FrontModule\Components\MainCategories\MainCategoriesFactory;
use App\FrontModule\Components\ProductList\ProductList;
use App\FrontModule\Presenters\BasePresenter;
use App\FrontModule\Presenters\Pageable;
use App\Model\BucketFilter\BucketFilterBuilder;
use App\Model\BucketFilter\BucketFilterBuilderFactory;
use App\Model\Link\LinkSeo;
use App\PostType\Discount\Model\Orm\DiscountLocalization\HasDiscountLocalizationRepository;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Discount\FrontModule\Components\DiscountList\DiscountList;
use App\PostType\Discount\FrontModule\Components\DiscountList\DiscountListFactory;
use App\PostType\Discount\Model\Orm\DiscountLocalization\DiscountLocalization;
use DateTimeImmutable;
use Nette\Application\Attributes\Persistent;
use Nette\Application\BadRequestException;
use Nette\Application\UI\Presenter;
use Nette\DI\Attributes\Inject;
use Nette\Http\IResponse;
use Nette\Utils\DateTime;
use Nette\Utils\Paginator;
use stdClass;

class DiscountPresenter extends BasePresenter implements Pageable
{

	use HasDiscountLocalizationRepository;

	#[Inject]
	public DiscountListFactory $discountListFactory;

	#[Inject]
	public MainCategoriesFactory $mainCategoriesFactory;

	#[Inject]
	public BestsellersFactory $bestsellersFactory;

	#[Inject]
	public CatalogProductsFactory $catalogProductsFactory;

	#[Inject]
	public CatalogProductsCallbackBuilder $catalogProductsCallbackBuilder;

	#[Persistent]
	public int $page = 1;

	private DiscountLocalization $discountLocalization;

	private array $filterParams;

	private mixed $cleanFilterParam;

	#[Persistent]
	public ?string $category = null;

	private BucketFilterBuilder $bucketFilterBuilder;

	private stdClass $filter;

	public function __construct(
		private readonly BucketFilterBuilderFactory $bucketFilterBuilderFactory,
		private readonly LinkSeo $linkSeo,
	)
	{
	}

	protected function startup(): void
	{
		parent::startup();

		$this->template->seolink = false;
		$filterParams = $this->getRequest()->getParameter('filter');
		if ($filterParams === null) {
			$this->filterParams = [];
		} else {
			$this->filterParams = $filterParams;
		}

		$this->cleanFilterParam = $this->filterParams;
	}

	public function actionDefault(CommonTree $object): void
	{
		$this->setObject($object);
	}

	public function renderDefault(CommonTree $object): void
	{
	}

	public function actionDetail(DiscountLocalization $object, array $filter, string $order = 'top'): void
	{
		if ($object->publicFrom?->getTimestamp() > DateTime::from('now')->getTimestamp()) {
			throw new BadRequestException($this->translator->translate('action_not_started'), IResponse::S404_NotFound);
		}

		$this->getTemplate()->actionIsOver = $object->publicTo && $object->publicTo->getTimestamp() < DateTime::from('now')->getTimestamp();
		$this->discountLocalization = $object;
		$this->setObject($object);

		$this->bucketFilterBuilder = $this->bucketFilterBuilderFactory->create($this, $object, $this->currentState, $this->priceLevel, $order, $this->filterParams);
		$this->bucketFilterBuilder->setCategory($this->category);
	}

	public function renderDetail(DiscountLocalization $object, string $order = 'top'): void
	{
		$this->template->cleanFilterParamForCrossroad = $this->cleanFilterParam;
		$this->template->cleanFilterParam = $this->cleanFilterParam;

		$this->filter = $this->bucketFilterBuilder->getFilter();
		$this->seoToolsFilter = $this->filter;
		$this->seoToolsSort = $this->bucketFilterBuilder->getSort();

		$this->template->filter = $this->filter;
		$this->template->catalogOrder = $order;
		$this->template->discountLocalization = $this->discountLocalization;
		$this->template->categories = [];
		$this->template->linkSeo = $this->linkSeo;
		$this->template->linkSeoPage = $this->mutation->pages->discount;

		$this->bucketFilterBuilder->redrawSnippets($this->cleanFilterParam);
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->getTemplate()->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte');
	}

	public function createComponentDiscountList(): DiscountList
	{
		return $this->discountListFactory->create($this->mutation);
	}

	public function createComponentMainCategories(): MainCategories
	{
		$mainCategories = $this->mainCategoriesFactory->create($this['robots']);
		$mainCategories->setCategoriesId($this->filter->mainCategories);
		$mainCategories->setSelectedCategory($this->bucketFilterBuilder->getCategory());
		return $mainCategories;
	}

	public function createComponentProductListRecommended(): ProductList
	{
		$productIds = $orderedIds = $cartIds = [];
		if ($this->marketingConsent->isPersonalizationGranted()) {
			$orderedIds = $this->userModel->findOrderedProductIds($this->user);
			$cartIds = $this->shoppingCart->getProductsIds();

			$productIds = $this->userModel->findRelatedOrderedProducts(
				productIds: array_merge($orderedIds, $this->lastVisitedProduct->getProductIds()),
				orderFrom: (new DateTimeImmutable())->modify('-1 year')
			);

		}
		return $this->productListFactory->create($this->currentState, $this->priceLevel, ProductList::TYPE_INTERESTED, $this->userEntity)
			->setIncludeProductIds(array_keys($productIds))
			->setOrderedProductIds($orderedIds)
			->setCartProductIds($cartIds)
			->setAppendBestselling()
			->setLimit(21);
	}



	protected function createComponentBestsellers(): Bestsellers
	{
		$bestsellerCount = (int) ($this->object->mutation->pages->discount->customFieldsJson->postTypeOtherSettings[0]->topProducts ?? 4);
		return $this->bestsellersFactory->create($this->setup, $this->bucketFilterBuilder, $bestsellerCount);
	}

	protected function createComponentCatalogProducts(): CatalogProducts
	{
		$paramsToTemplate = [
			'productTitle' => 'h2',
			'ajaxPage' => true,
			'cleanFilterParam' => $this->cleanFilterParam,
		];

		$findCatalogProductsDataCallback = $this->catalogProductsCallbackBuilder->build($this->bucketFilterBuilder, $this['bestsellers']->getBestsellerIds());

		return $this->catalogProductsFactory->create(
			$this->object,
			$this->setup,
			$findCatalogProductsDataCallback,
			$this->getPageParameter(),
			$paramsToTemplate,
			'catalog',
			'discount',
		);
	}

	public function getPagerLimits(): ?PagerLimits
	{
		assert($this instanceof Presenter);
		if ($this->action === 'default') {
			return null;
		}

		$paginator = $this['catalogProducts']['pager']->getPaginator();
		assert($paginator instanceof Paginator);

		return new PagerLimits($paginator->page, $paginator->pageCount);
	}

}
