{layout $templates.'/@layout-new.latte'}
{varType App\PostType\Discount\Model\Orm\DiscountLocalization\DiscountLocalization $discountLocalization}
{var $dataGridShown = true}
{block #content}
<div class="main__main">
	<div class="main__header" n:snippet="header">
		{include $templates.'/part/box/header.latte',
			props: [
				title: $sectionTitle,
				hrefClose: $presenter->link('edit', [id=>$discountLocalization->id]),
				isPageTitle: true,
			]
		}
	</div>
	<div class="main__content scroll"  n:snippet="mainContent">

		<div style="position: relative; z-index: 1">
			{control productSearch}
		</div>

		{control productsList}
	</div>
	<div class="main__content-side scroll">
	</div>
</div>
