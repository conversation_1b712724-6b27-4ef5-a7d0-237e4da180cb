<?php declare(strict_types = 1);

namespace App\PostType\Discount\AdminModule\Components\ProductsList;

use App\Model\Orm\Product\ProductRepository;
use App\PostType\Discount\Model\Orm\Discount\DiscountRepository;
use App\PostType\Discount\Model\Orm\DiscountLocalization\DiscountLocalization;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use App\Model\Translator;
use Ublaboo\DataGrid\DataGrid;

/**
 * @property-read DefaultTemplate $template
 */
final class ProductsList extends UI\Control
{

	public function __construct(
		private readonly DiscountLocalization $discountLocalization,
		private readonly Translator $translator,
		private readonly DiscountRepository $discountRepository,
		private readonly ProductRepository $productRepository,
		private readonly \App\Model\ElasticSearch\Product\Facade $esProductFacade,
	)
	{
	}

	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->render(__DIR__ . '/templates/products.latte');
	}

	public function createComponentGrid(): DataGrid
	{
		$grid = new DataGrid();

		$grid->setTranslator($this->translator);
		$grid->setDataSource($this->discountLocalization->getParent()->products->toCollection());

		$grid->addColumnText('internalName', 'internalName')
			->setSortable()
			->setFilterText();

		$grid->addGroupButtonAction('delete_button')
			->setClass('btn btn-xs btn-danger ajax group-delete-button')
			->setAttribute('data-datagrid-confirm', $this->translator->translate('delete_confirms'))
			->onClick[] = $this->deleteSelected(...);

		return $grid;
	}

	public function deleteSelected(array $items): void
	{
		foreach ($items as $item) {
			$this->discountLocalization->getParent()->products->remove($item);
			$this->discountRepository->persistAndFlush($this->discountLocalization->getParent());

			if ($product = $this->productRepository->getById($item)) {
				$this->esProductFacade->updateAllMutations($product);
			}
		}
	}

}
