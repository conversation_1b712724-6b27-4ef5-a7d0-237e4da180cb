<?php declare(strict_types = 1);

namespace App\PostType\Discount\AdminModule\Components\DiscountDataGrid;

use App\Model\Translator;
use App\PostType\Core\AdminModule\Components\DataGrid\Definition\DataGridDefinition;
use App\PostType\Core\AdminModule\Components\DataGrid\Definition\Extenders\CustomDataGridExtender;
use App\PostType\Discount\Model\Orm\DiscountLocalization\DiscountLocalization;
use Nextras\Orm\Collection\ICollection;
use Ublaboo\DataGrid\DataGrid;

readonly class DiscountDataGridPrescription
{

	public function __construct(private readonly Translator $translator)
	{
	}

	public function get(): DataGridDefinition
	{
		$datagrid = new DataGrid();
		$datagrid->setOuterFilterRendering();

		return new DataGridDefinition(
			dataGrid: $datagrid,
			extenders: [
				new CustomDataGridExtender(
					improveFunction: $this->addOrder(...)
				),
				new CustomDataGridExtender(
					improveFunction: $this->addFilter(...)
				),
				new CustomDataGridExtender(
					improveFunction:$this->removeColumns(...),
				),
			],
		);
	}

	public function removeColumns(DataGrid $dataGrid): void
	{
		$dataGrid->removeColumn('internalName');
	}

	private function addFilter(DataGrid $dataGrid): void
	{
		$dataGrid->addColumnNumber('public', 'public')
			->setReplacement([true => $this->translator->translate('yes'), false => $this->translator->translate('no')])
			->setSortable()
			->setFilterSelect([null => 'all', true => 'yes', false => 'no'])
			->setTranslateOptions();

		$dataGrid->addFilterDateRange('publicFrom', 'publicFrom', 'publicTo', 'publicTo');

		$dataGrid->addColumnDateTime('publicTo', 'publicTo')
			->setFormat('j.n.Y H:i')
			->setSortable();
	}

	private function addOrder(DataGrid $dataGrid): void
	{
		$dataGrid->addColumnText('discount->order', 'order')
			->setRenderer(function (DiscountLocalization $discountLocalization) {
				return $discountLocalization->discount->order;
			})
			->setSortableCallback(function (ICollection $collection, array $sort) {
				$collection->getQueryBuilder()// @phpstan-ignore-line
					->orderBy('[discount.order] IS NULL ASC, [discount.order] ' . $sort['discount->order']);
			})
			->setSortable();
	}

}
