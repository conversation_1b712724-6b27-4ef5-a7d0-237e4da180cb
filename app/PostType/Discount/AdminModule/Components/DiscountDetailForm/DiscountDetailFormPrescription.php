<?php declare(strict_types = 1);

namespace App\PostType\Discount\AdminModule\Components\DiscountDetailForm;

use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\CustomFormExtender;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\CommonTemplatePart;
use App\PostType\Core\AdminModule\Components\Form\Definition\FormDefinition;
use App\PostType\Discount\Model\Orm\DiscountLocalization\DiscountLocalization;
use App\PostType\Discount\Model\Orm\DiscountLocalization\DiscountLocalizationRepository;
use Nette\Application\UI\Form;

class DiscountDetailFormPrescription
{

	private DiscountLocalization $discountLocalization;

	public function __construct(
		private readonly DiscountLocalizationRepository $discountLocalizationRepository,
	)
	{
	}

	public function getPrescription(DiscountLocalization $discountLocalization): FormDefinition
	{
		$this->discountLocalization = $discountLocalization;

		$extenders = [];
		$extenders[] = $this->addSetting($discountLocalization);
		$extenders[] = $this->addProducts();

		return new FormDefinition(
			form: $this->createForm(),
			extenders: $extenders,
		);
	}

	private function createForm(): Form
	{
		$form = new Form();
		$form->setMappedType(DiscountLocalizationFormData::class);

		$form->onSuccess[] = $this->evaluateExpirationChange(...);

		return $form;
	}

	private function addProducts(): CustomFormExtender
	{
		return new CustomFormExtender(
			addHandler: function () {
			},
			successHandler: function () {
			},
			templateParts: [
				new CommonTemplatePart(
					templateFile: __DIR__ . '/templates/products.latte',
					type: CommonTemplatePart::TYPE_SIDE
				),
			]
		);
	}

	private function addSetting(DiscountLocalization $discountLocalization): CustomFormExtender
	{
		return new CustomFormExtender(
			addHandler: function (Form $form) use ($discountLocalization) {
				$form->addInteger('order', 'order')
					->setDefaultValue($discountLocalization->discount->order);
			},
			successHandler: function (Form $form, DiscountLocalizationFormData $formData) use ($discountLocalization) {
				$discountLocalization->discount->order = $formData->order;
			},
			templateParts: [
				new CommonTemplatePart(
					templateFile: __DIR__ . '/templates/settings.latte',
					type: CommonTemplatePart::TYPE_MAIN,
				),
			],
		);
	}

	private function evaluateExpirationChange(Form $form, DiscountLocalizationFormData $discountLocalizationFormData): void
	{
		if ($this->discountLocalization->publicTo?->getTimestamp() === $discountLocalizationFormData->validity->publicToDateTime?->getTimestamp()) {
			return;
		}

		$this->discountLocalization->expirationNotifySend = false;
		$this->discountLocalization->expiredNotifySend = false;

		$this->discountLocalizationRepository->persist($this->discountLocalization);
	}

}
