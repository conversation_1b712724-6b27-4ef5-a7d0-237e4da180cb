{var $anchorName = 'description'}
{var $icon = $templates . '/part/icons/align-left.svg'}
{var $title = 'Nastaveni'}

{var $props = [
title: $title,
id: $anchorName,
icon: $icon,
variant: 'main',
open: true,
classes: ['u-mb-xxs'],
]}

{embed $templates . '/part/box/toggle.latte', props => $props, templates => $templates}
	{block content}
		{include $templates . '/part/core/inp.latte',
			props: [
				input: $form['order'],
				variant: 'main',
				open: true,
				type: 'int',
				classesLabel: ['title'],
			]
		}
{*		{include $templates . '/part/core/inp.latte',*}
{*			props: [*}
{*				input: $form['products'],*}
{*				variant: 'main',*}
{*				open: true,*}
{*				type: 'select',*}
{*				multiple: true,*}
{*				classesLabel: ['title'],*}
{*			]*}
{*		}*}
	{/block}
{/embed}
