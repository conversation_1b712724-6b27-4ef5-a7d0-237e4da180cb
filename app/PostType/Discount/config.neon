application:
	mapping:
		Discount: App\PostType\Discount\*Module\Presenters\*Presenter

parameters:
	postTypeRoutes:
		Discount: discount


cf:
	definitions:
		discountHrefItem:
			type: group
			label: "Položka"
			items:
				link:
					extends: @cf.definitions.linkChoose
				icon:
					type: image # has size
					label: "SVG ikona / o<PERSON><PERSON><PERSON><PERSON><PERSON> (min. 50x50)"

	fields:
		discountHref:
			type: list
			label: "Nastavení horních odkazů"
			items:
				item:
					extends: @cf.definitions.discountHrefItem
	templates:
		uid-discount: [@cf.discountHref]
		discountLocalization:
			gallery:
				type: group
				items:
					mainImage:
						type: image # has size
						multiple: false
						label: "Fotografie na výpisu (min. 1400x700)"

services:
	- App\PostType\Discount\Model\DiscountLocalizationFacade
	- App\PostType\Discount\FrontModule\Components\DiscountList\DiscountListFactory
	- App\PostType\Discount\AdminModule\Components\DiscountDataGrid\DiscountDataGridPrescription
	- App\PostType\Discount\AdminModule\Components\DiscountDetailForm\DiscountDetailFormPrescription
	- App\PostType\Discount\Model\Orm\DiscountLocalization\DiscountLocalizationModel
	- App\PostType\Discount\AdminModule\Components\ProductsList\ProductsListFactory
