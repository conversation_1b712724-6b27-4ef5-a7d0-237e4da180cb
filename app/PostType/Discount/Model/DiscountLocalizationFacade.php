<?php declare(strict_types = 1);

namespace App\PostType\Discount\Model;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\PostType\Core\Model\EntityLocalizationFacade;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Discount\Model\Orm\Discount\HasDiscountRepository;
use App\PostType\Discount\Model\Orm\Discount\Discount;
use App\PostType\Discount\Model\Orm\DiscountLocalization\DiscountLocalization;

class DiscountLocalizationFacade implements EntityLocalizationFacade
{

	private ParameterValue $parameterValue;

	use HasDiscountRepository;

	public function __construct(private readonly Orm $orm)
	{
	}

	public function create(Mutation $mutation, ?ParentEntity $localizableEntity): LocalizationEntity
	{
		$localization = new DiscountLocalization();

		$this->orm->discountLocalization->attach($localization);

		$localization->mutation = $mutation;

		if ($localizableEntity === null) {
			$localizableEntity = new Discount();
			$localization->discount = $localizableEntity;
		} else {
			assert($localizableEntity instanceof Discount);
			$localization->discount = $localizableEntity;
		}

		$this->orm->persistAndFlush($localization);

		return $localization;
	}

	public function remove(LocalizationEntity $localizableEntity): void
	{
		assert($localizableEntity instanceof DiscountLocalization);

		$parent = $localizableEntity->getParent();
		$this->orm->discountLocalization->remove($localizableEntity);

		if ($parent->localizations->count() === 0) {
			$this->orm->discount->remove($parent);
		}

		$this->orm->flush();
	}

	public function getParameterValue(): ParameterValue
	{
		return $this->parameterValue;
	}

	public function setParameterValue(ParameterValue $parameterValue): DiscountLocalizationFacade
	{
		$this->parameterValue = $parameterValue;
		return $this;
	}

}
