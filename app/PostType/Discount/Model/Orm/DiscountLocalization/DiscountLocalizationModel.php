<?php declare(strict_types = 1);

namespace App\PostType\Discount\Model\Orm\DiscountLocalization;

use App\Model\Email\Common;
use App\Model\Email\CommonFactory;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\PostType\Discount\Model\Orm\Discount\Discount;
use App\Utils\DateTime;
use Nette\Application\LinkGenerator;
use Nette\Utils\Random;
use Nette\Utils\Strings;

class DiscountLocalizationModel
{

	private Common $commonEmail;

	public function __construct(
		private readonly Orm $orm,
		private readonly DiscountLocalizationRepository $discountLocalizationRepository,
		private readonly CommonFactory $commonFactory,
		private readonly LinkGenerator $linkGenerator,
	)
	{
		$this->commonEmail = $this->commonFactory->create();
	}

	public function generateDummyData(Mutation $mutation, int $limit = 50): void
	{
		for ($count = 0; $count < $limit; $count++) {

			$randomName = Strings::firstUpper(Random::generate(8, 'a-z'));
			$localization = new DiscountLocalization();

			$localization->mutation = $mutation;
			$localization->name = $randomName;
			$localization->public = (bool) rand(0, 1);
			$this->orm->discountLocalization->attach($localization);

			$localizableEntity = new Discount();
			$this->orm->discount->attach($localizableEntity);
			$parameterValues = $this->orm->parameterValue->findBy(['parameter->uid' => 'discount']);

			$localizableEntityParameterValues = [];
			if ($parametersCount = $parameterValues->count() - 1) {

				$randomParameterCount = rand(1, $parametersCount);
				for ($parameterCount = 0; $parameterCount <= $randomParameterCount; ++$parameterCount) {
					$randomIndex = rand(1, $parametersCount);

					if (in_array($randomIndex, $localizableEntityParameterValues)) {
						continue;
					}

					$localizableEntityParameterValues[] = $parameterValues->fetchAll()[$randomIndex]->getPersistedId();
				}
			}

			$localizableEntity->parameterValueDiscount = $localizableEntityParameterValues[rand(0, count($localizableEntityParameterValues) - 1)];

			$localizableEntity->internalName = Strings::firstUpper(Random::generate(8, 'a-z'));
			$localization->discount = $localizableEntity;

			$this->orm->persistAndFlush($localization);
		}
	}

	public function remindExpiration(Mutation $mutation, int $interval = 0): void
	{
		$dateReminderMaximum = DateTime::from(date('Y-m-d'))
			->modify('+ ' . $interval . ' days')
			->modify('+ 23 hours')
			->modify('+ 59 minutes');

		$dateReminderMinimum = DateTime::from(date('Y-m-d'));

		$notifyProperty = 'expirationNotifySend';

		if ($interval === 0) {
			$notifyProperty = 'expiredNotifySend';
		}

		$discountsToNotify = $this->discountLocalizationRepository->findBy([$notifyProperty => 0])->findBy(['publicTo>=' => $dateReminderMinimum, 'publicTo<=' => $dateReminderMaximum, 'public' => true]);

		if ($discountsToNotify->count() === 0) {
			return;
		}

		/**
		 * @var DiscountLocalization $discount
		 */
		foreach ($discountsToNotify as $discount) {
			$this->commonEmail->send(
				'',
				$mutation->getRealAdminEmail(),
				'discountExpiration',
				[
					'publicTo' => $discount->publicTo->format('j.n.Y'),
					'discountDetailLink' => $this->linkGenerator->withReferenceUrl($mutation->getUrl())->link('Discount:Admin:Discount:edit', ['id' => $discount->id]),
					'discountName' => $discount->name,
				],
				'discountExpirationNotify'
			);

			$discount->$notifyProperty = true;
			$this->discountLocalizationRepository->persist($discount);
		}

		$this->discountLocalizationRepository->flush();
	}

}
