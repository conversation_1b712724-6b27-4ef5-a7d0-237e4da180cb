<?php declare(strict_types = 1);

namespace App\PostType\Discount\Model\Orm\DiscountLocalization;

use App\Model\Orm\Mutation\Mutation;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Repository\Repository;

/**
 * @method Result findAllIdsInMutation(Mutation $mutation, ?int $limit = null)
 * @extends Repository<DiscountLocalization>
 */
class DiscountLocalizationRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [DiscountLocalization::class];
	}

}
