<?php declare(strict_types = 1);

namespace App\PostType\Discount\Model\Orm\DiscountLocalization;

use App\Model\Orm\HasImages;
use App\Model\Orm\ImageEntity;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\RoutableEntity;
use App\Model\Orm\Traits\HasConfigService;
use App\Model\Orm\Traits\HasCustomContent;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\Core\Model\Editable;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Core\Model\Publishable;
use App\PostType\Core\Model\Validatable;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Page\Model\Orm\TreeRepository;
use App\PostType\Discount\Model\Orm\Discount\Discount;
use App\Model\Orm\JsonContainer;// phpcs:ignore
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;

/**
 * @property int                    $id                                                {primary}
 * @property string                 $name                                              {default ''}
 * @property bool                   $public                                            {default false}
 * @property DateTimeImmutable|null $publicFrom                                        {default now}
 * @property DateTimeImmutable|null $publicTo                                          {default '+100 year'}
 * @property DateTimeImmutable|null $editedTime
 * @property int|null               $edited
 * @property bool                   $expirationNotifySend                              {default false}
 * @property bool                   $expiredNotifySend                                 {default false}
 *
 * @property ArrayHash              $customFieldsJson                                  {container JsonContainer}
 * @property ArrayHash              $customContentJson                                 {container JsonContainer}
 *
 * RELATIONS
 * @property Mutation               $mutation                                          {m:1 Mutation, oneSided=true}
 * @property Discount               $discount                                          {M:1 Discount::$localizations}
 *
 * VIRTUAL
 * @property ArrayHash|null         $cf                                                {virtual}
 * @property ArrayHash|null         $cc                                                {virtual}
 * @property-read string            $template                                          {virtual}
 */
class DiscountLocalization extends RoutableEntity implements LocalizationEntity, Publishable, Validatable, HasImages, Editable
{

	use HasCustomFields;
	use HasCustomContent;
	use HasConfigService;

	private TreeRepository $treeRepository;

	public function injectRepository(TreeRepository $treeRepository): void
	{
		$this->treeRepository = $treeRepository;
	}

	protected function getterTemplate(): string
	{
		return ':Discount:Front:Discount:detail';
	}

	protected function getterPath(): array
	{
		if (!isset($this->cache['path'])) {

			$this->cache['path'] = $this->createPath();
		}

		return $this->cache['path'];
	}

	private function createPath(): array
	{
		$path = [];

		if ($rootId = $this->mutation->getRealRootId()) {
			$path[] = $rootId;
		}

		if ($treeDiscount = $this->treeRepository->getByUid(Tree::UID_DISCOUNT, $this->mutation)) {
			$path[] = $treeDiscount->getPersistedId();
		}

		return $path;
	}

	public function getId(): int
	{
		return $this->id;
	}

	public function getMutation(): Mutation
	{
		return $this->mutation;
	}

	public function setMutation(Mutation $mutation): void
	{
		$this->mutation = $mutation;
	}

	public function getParent(): Discount
	{
		return $this->discount;
	}

	public function setParent(ParentEntity $parentEntity): void
	{
		assert($parentEntity instanceof Discount);
		$this->discount = $parentEntity;
	}

	public function getIsPublic(): bool
	{
		return $this->public;
	}

	public function setIsPublic(bool $isPublic): void
	{
		$this->public = $isPublic;
	}

	public function getPublicFrom(): DateTimeImmutable|null
	{
		return $this->publicFrom;
	}

	public function getPublicTo(): DateTimeImmutable|null
	{
		return $this->publicTo;
	}

	public function setPublicFrom(?DateTimeImmutable $publicFrom): void
	{
		$this->publicFrom = $publicFrom;
	}

	public function setPublicTo(?DateTimeImmutable $publicTo): void
	{
		$this->publicTo = $publicTo;
	}

	public function getName(): string
	{
		return $this->name;
	}

	public function setName(string $name): void
	{
		$this->name = $name;
	}

	public function getFirstImage(): ImageEntity|null
	{
		return isset($this->getParent()->cf->base->mainImage) ? $this->getParent()->cf->base->mainImage->getEntity() : null;
	}

	public function setEditorId(int $id): void
	{
		$this->edited = $id;
	}

	public function setEditedTime(DateTimeImmutable $editedTime): void
	{
		$this->editedTime = $editedTime;
	}

}
