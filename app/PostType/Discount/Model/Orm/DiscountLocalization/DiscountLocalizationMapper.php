<?php declare(strict_types = 1);

namespace App\PostType\Discount\Model\Orm\DiscountLocalization;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<DiscountLocalization>
 */
class DiscountLocalizationMapper extends DbalMapper
{

	use HasCamelCase;

	/**
	 * @return literal-string
	 */
public function getTableName(): string
{
	return 'discount_localization';
}

	public function findAllIdsInMutation(Mutation $mutation, ?int $limit = null): Result
	{
		$builder = $this->builder()->select('dl.id')
			->from($this->getTableName(), 'dl')
			->andWhere('dl.mutationId = %i', $mutation->getPersistedId())
			->limitBy($limit);

		return $this->connection->queryByQueryBuilder($builder);
	}

}
