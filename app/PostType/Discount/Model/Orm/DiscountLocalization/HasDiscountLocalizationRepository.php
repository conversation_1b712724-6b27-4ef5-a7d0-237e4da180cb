<?php declare(strict_types = 1);

namespace App\PostType\Discount\Model\Orm\DiscountLocalization;

trait HasDiscountLocalizationRepository
{

	private DiscountLocalizationRepository $discountLocalizationRepository;

	public function injectDiscountRepository(DiscountLocalizationRepository $discountLocalizationRepository): void
	{
		$this->discountLocalizationRepository = $discountLocalizationRepository;
	}

}
