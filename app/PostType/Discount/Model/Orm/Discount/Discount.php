<?php declare(strict_types = 1);

namespace App\PostType\Discount\Model\Orm\Discount;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Discount\Model\Orm\DiscountLocalization\DiscountLocalization;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Exception\NoResultException;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;
use App\Model\Orm\JsonContainer;// phpcs:ignore

/**
 * @property int                               $id                        {primary}
 * @property string                            $internalName              {default ''}
 * @property int|null                          $order                     {default null}
 * @property ArrayHash                         $customFieldsJson          {container JsonContainer}
 *
 * RELATIONS
 * @property OneHasMany<DiscountLocalization> $localizations             {1:M DiscountLocalization::$discount}
 * @property ManyHasMany<Product>             $products                  {M:M Product::$discounts, isMain=true}
 * @property ParameterValue|null               $parameterValueDiscount    {1:1 ParameterValue::$discount, isMain=true}
 *
 * VIRTUAL
 * @property ArrayHash|null                    $cf                        {virtual}
 */
class Discount extends BaseEntity implements ParentEntity
{

	use HasCustomFields;

	public function getInternalName(): string
	{
		return $this->internalName;
	}

	public function setInternalName(string $internalName): void
	{
		$this->internalName = $internalName;
	}
	/**
	 * @return ICollection<DiscountLocalization>
	 */
	public function getLocalizations(): ICollection
	{
		return $this->localizations->toCollection();
	}

	/**
	 * @throws NoResultException
	 */
	public function getLocalization(Mutation $mutation): DiscountLocalization
	{
		$localization = $this->getLocalizations()->getByChecked(['mutation' => $mutation]);
		assert($localization instanceof DiscountLocalization);
		return $localization;
	}

}
