<?php declare(strict_types = 1);

namespace App\PostType\Discount\Model\Orm\Discount;

use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method Discount|null getById(int $id)
 * @method Discount|null getBy(array $conds)
 * @method ICollection<Discount> findBy(array $conds)
 * @method ICollection<Discount> findByIds(array $conds)
 * @extends Repository<Discount>
 */
class DiscountRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [Discount::class];
	}

}
