<?php declare(strict_types = 1);

namespace App\PostType\Discount\Model\Orm\Discount;

use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Orm\Mapper\Dbal\DbalMapper;
use Nextras\Orm\Mapper\Dbal\Conventions\IConventions;

/**
 * @extends <PERSON>balMapper<Discount>
 */
class DiscountMapper extends DbalMapper
{

	use HasCamelCase;

	/**
	 * @return literal-string
	 */
public function getTableName(): string
{
	return 'discount';
}

	protected function createConventions(): IConventions
	{
		$conventions = parent::createConventions();
		$conventions->manyHasManyStorageNamePattern = '%s_%s';    // @phpstan-ignore-line
		return $conventions;
	}

}
