<?php

declare(strict_types=1);

namespace App\PostType\SeoLink\AdminModule\Components\Form;

use App\AdminModule\Components\PostType\Localizations\Localizations;
use App\AdminModule\Components\PostType\Localizations\LocalizationsFactory;
use App\Model\ConfigService;
use App\Model\CustomField\SuggestUrls;
use App\Model\ElasticSearch\All\Facade;
use App\Model\Link\LinkFactory;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\User\User;
use App\Model\Translator;
use App\Model\TranslatorDB;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellForm;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellFormFactory;
use App\PostType\SeoLink\AdminModule\Components\Form\FormData\BaseFormData;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalization;
use App\PostType\SeoLink\Model\SeoLinkLocalizationFacade;
use ArrayIterator;
use Nette\Application\UI\Control;
use Nette\Http\IRequest;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use const RS_TEMPLATE_DIR;

final class Form extends Control
{

	/** @var ICollection<Mutation> */
	private ICollection $mutations;

	/** @var array */
	private mixed $postData;

	public function __construct(
		private readonly SeoLinkLocalizationFacade $seoLinkLocalizationFacade,
		private readonly SeoLinkLocalization $seoLinkLocalization,
		private readonly User $userEntity,
		private readonly Builder $formBuilder,
		private readonly Handler $formHandler,
		private readonly SuggestUrls $urls,
		private readonly Translator $translator,
		private readonly TranslatorDB $translatorDB,
		private readonly LinkFactory $linkFactory,
		private readonly Orm $orm,
		private readonly ConfigService $configService,
		private readonly Facade $allElasticFacade,
		private readonly LocalizationsFactory $languageFactory,
	)
	{
		$this->onAnchor[] = $this->init(...);
	}

	private function init(): void
	{
		$this->mutations = $this->orm->mutation->findAll();

		$method = $this->presenter->request->getMethod();
		if ($method === IRequest::Post) {
			$this->postData = (array) $this->presenter->request->getPost();
		} else {
			$this->postData = [];
		}
	}

	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$template->add('templates', RS_TEMPLATE_DIR);
		$template->add('corePartsDirectory', \App\PostType\Core\AdminModule\Components\Form\Form::TEMPLATE_PARTS_DIR);

		$template->parent = $this->seoLinkLocalization->getParent();
		$template->headerItems = new ArrayIterator();
		if ($this->seoLinkLocalization->alias !== null) {
			$template->linksToFront = $this->linkFactory->linkTranslateToNette($this->seoLinkLocalization, ['show' => 1, 'mutation' => $this->seoLinkLocalization->mutation]);
		} else {
			$template->linksToFront = null;
		}

		$template->entityLocalization = $this->seoLinkLocalization;
		$template->mutation = $this->seoLinkLocalization->mutation;
		$template->orm = $this->orm;
		$template->translatorDB = $this->translatorDB;
		$template->fileUploadLink = $this->presenter->link(':Admin:File:upload');

		$template->otherMutations = $this->seoLinkLocalization->seoLink->localizations->toCollection()->findBy(['mutation!=' => $this->seoLinkLocalization->mutation]);
		$activeMutationLangCodes = [];
		foreach ($this->seoLinkLocalization->seoLink->localizations as $localization) {
			$activeMutationLangCodes[] = $localization->mutation->langCode;
		}

		$template->missingMutations = $this->mutations->findBy(['langCode!=' => $activeMutationLangCodes]);

		$template->config = $this->configService->getParams();
		$template->userEntity = $this->userEntity;

		$template->urls = $this->urls;
		$template->render(__DIR__ . '/form.latte');
	}

	protected function createComponentForm(): \Nette\Application\UI\Form
	{
		$form = new \Nette\Application\UI\Form();
		$form->setMappedType(BaseFormData::class);

		$this->formBuilder->build($form, $this->seoLinkLocalization, $this->postData);

		$form->setTranslator($this->translator);
		$form->onSuccess[] = $this->formSucceeded(...);
		$form->onError[] = $this->formError(...);

		return $form;
	}

	private function formSucceeded(\Nette\Application\UI\Form $form, BaseFormData $data): void
	{
		$this->formHandler->handle($this->seoLinkLocalization, $this->userEntity, $data, ArrayHash::from($this->postData));
		$this->allElasticFacade->updateNow($this->seoLinkLocalization);

		$this->presenter->redirect('edit', ['id' => $this->seoLinkLocalization->id]);
	}

	private function formError(): void
	{
		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	public function handleDelete(): void
	{
		$this->allElasticFacade->deleteNow($this->seoLinkLocalization);
		$this->seoLinkLocalizationFacade->remove($this->seoLinkLocalization);
		$this->presenter->redirect('default');
	}


	protected function createComponentLanguage(): Localizations
	{
		return $this->languageFactory->create(
			localizationEntity: $this->seoLinkLocalization,
		);
	}
}
