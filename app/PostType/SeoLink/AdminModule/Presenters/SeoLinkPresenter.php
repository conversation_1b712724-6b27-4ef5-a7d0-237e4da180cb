<?php

declare(strict_types=1);

namespace App\PostType\SeoLink\AdminModule\Presenters;

use App\AdminModule\Presenters\BasePresenter;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGrid;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGridFactory;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellForm;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellFormFactory;
use App\PostType\SeoLink\AdminModule\Components\Form\Form;
use App\PostType\SeoLink\AdminModule\Components\Form\FormFactory;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalization;
use App\PostType\SeoLink\Model\SeoLinkLocalizationFacade;
use function assert;

final class SeoLinkPresenter extends BasePresenter
{

	public const ORM_REPOSITORY_NAME = 'seoLink';

	private SeoLinkLocalization $seoLinkLocalization;

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
		private readonly FormFactory $formFactory,
		private readonly ShellFormFactory $shellFormFactory,
		private readonly SeoLinkLocalizationFacade $seoLinkFacade,
	)
	{
		parent::__construct();
	}

	public function renderDefault(): void
	{
	}

	public function actionEdit(int $id): void
	{
		$seoLinkLocalization = $this->orm->seoLinkLocalization->getById($id);
		if ($seoLinkLocalization === null) {
			$this->redirect('default');
		}

		assert($seoLinkLocalization instanceof SeoLinkLocalization);
		$this->seoLinkLocalization = $seoLinkLocalization;
	}

	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create(self::ORM_REPOSITORY_NAME, $this->orm->seoLinkLocalization->findAll());
	}

	protected function createComponentForm(): Form
	{
		assert($this->userEntity !== null);
		return $this->formFactory->create($this->seoLinkLocalization, $this->userEntity);
	}

	protected function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create(
			entity: null,
			facade: $this->seoLinkFacade,
		);
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->action . '.latte');
	}

}
