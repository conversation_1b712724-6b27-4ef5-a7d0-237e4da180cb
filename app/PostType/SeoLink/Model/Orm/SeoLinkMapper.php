<?php

declare(strict_types=1);

namespace App\PostType\SeoLink\Model\Orm;

use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<SeoLink>
 */
final class Seo<PERSON>inkMapper extends DbalMapper
{

	use HasCamelCase;

	/**
* @return literal-string
*/
public function getTableName(): string
{
	return 'seolink';
}

	/**
	 * @return ICollection<SeoLink>
	 */
	public function searchByName(string $q, int $mutationId, array $excluded = []): ICollection
	{
		$builder = $this->builder()
			->andWhere('name LIKE %_like_', $q);

		if ($mutationId) {
			$builder->andWhere('mutationId = %i', $mutationId);
		}

		if ($excluded) {
			$builder->andWhere('id not in %i[]', $excluded);
		}

		return $this->toCollection($builder);
	}
	/**
	 * @return ICollection<SeoLink>
	 */
	public function findByFilter(object $filter): ICollection
	{
		$builder = $this->builder()->select('s.*')->from($this->getTableName(), 's');

		if (isset($filter->mutation) && $filter->mutation) {
			$builder->andWhere('mutationId = %i', $filter->mutation);
		}

		if (isset($filter->fulltext) && $filter->fulltext) {
			$builder->andWhere('( s.name LIKE %_like_ OR s.h1 LIKE %_like_ )', $filter->fulltext, $filter->fulltext);
		}

		if (isset($filter->isActive) && $filter->isActive) {
			$builder->andWhere('isActive = %b', $filter->isActive);
		}

		if (isset($filter->isDefault) && $filter->isDefault) {
			$builder->andWhere('isDefault = %b', $filter->isDefault);
		}

		$builder->groupBy('s.id');

		return $this->toCollection($builder);
	}

}
