<?php declare(strict_types=1);

namespace App\PostType\SeoLink\Model\Orm;

use App\Model\Orm\JsonArrayHashContainer; // phpcs:ignore
use App\Model\Orm\JsonContainer; // phpcs:ignore
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\RoutableEntity;
use App\Model\Orm\Traits\HasCustomContent;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use Nette\Utils\ArrayHash;

/**
 * @property int $id {primary}
 * @property string $name {default ''}
 * @property ArrayHash $customFieldsJson {container JsonArrayHashContainer}
 * @property ArrayHash $customContentJson {container JsonContainer}
 *
 * RELATIONSseolm
 * @property SeoLink $seoLink {m:1 SeoLink::$localizations}
 * @property Mutation $mutation {m:1 Mutation, oneSided=true}
 *
 * VIRTUAL
 * @property ArrayHash $cf {virtual}
 * @property ArrayHash $cc {virtual}
 * @property-read string $template {virtual}
 */
class SeoLinkLocalization extends RoutableEntity implements LocalizationEntity
{

	use HasCustomFields;
	use HasCustomContent;

	public function getId(): int
	{
		return $this->id;
	}

	public function getMutation(): Mutation
	{
		return $this->mutation;
	}


	public function setMutation(Mutation $mutation): void
	{
		$this->mutation = $mutation;
	}


	public function getParent(): SeoLink
	{
		return $this->seoLink;
	}


	public function setParent(ParentEntity $parentEntity): void
	{
		assert($parentEntity instanceof SeoLink);
		$this->seoLink = $parentEntity;
	}


	public function getName(): string
	{
		return $this->name;
	}

	public function setName(string $name): void
	{
		$this->name = $name;
	}

	protected function getterTemplate(): string
	{
		return ':SeoLink:Front:SeoLink:default';
	}

	protected function getterPath(): array
	{
		return [];
	}


}
