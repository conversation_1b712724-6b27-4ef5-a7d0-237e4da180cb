<?php

declare(strict_types=1);

namespace App\PostType\SeoLink\Model\Orm;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Repository\QueryForIdsByMutation;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Repository\Repository;

/**
 * @method array findAllIds(?int $limit)
 *
 * @extends Repository<SeoLinkLocalization>
 */
final class SeoLinkLocalizationRepository extends Repository implements QueryForIdsByMutation
{

	public static function getEntityClassNames(): array
	{
		return [SeoLinkLocalization::class];
	}


	public function findAllIdsInMutation(Mutation $mutation, ?int $limit = null): Result
	{
		$mapper = $this->mapper;
		assert($mapper instanceof SeoLinkLocalizationMapper);

		return $mapper->findAllIdsInMutation($mutation, $limit);
	}

}
