<?php

declare(strict_types=1);

namespace App\PostType\SeoLink\Model\Orm;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Traits\HasCamelCase;
use App\PostType\Author\Model\Orm\AuthorLocalizationMapper;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<SeoLinkLocalization>
 */
final class SeoLinkLocalizationMapper extends DbalMapper
{

	use HasCamelCase;

	/**
* @return literal-string
*/
public function getTableName(): string
{
	return 'seolink_localization';
}

	public function findAllIds(?int $limit): Result
	{
		$builder = $this->builder()->select('sll.id')
			->from($this->getTableName(), 'sll')
			->limitBy($limit);
		return $this->connection->queryByQueryBuilder($builder);
	}


	public function findAllIdsInMutation(Mutation $mutation, ?int $limit = null): Result
	{
		$builder = $this->builder()->select('sll.id')
			->from($this->getTableName(), 'sll')
			->andWhere('sll.mutationId = %i', $mutation->id)
			->limitBy($limit);
		return $this->connection->queryByQueryBuilder($builder);
	}
}
