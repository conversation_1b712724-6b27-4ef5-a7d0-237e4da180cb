<?php declare(strict_types = 1);

namespace App\PostType\SeoLink\Model;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\PostType\SeoLink\Model\Orm\SeoLink;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalization;
use function array_merge;
use function array_values;
use function assert;
use function implode;
use function is_array;
use function sort;

final class SeoLinkModel
{

	public function __construct(
		private readonly Orm $orm,
	)
	{
	}


	public function getByFilter(?Mutation $mutation, array $filter, array $params): SeoLinkLocalization|null
	{
		if ( ! isset($filter['dials'])) {
			return null;
		}

		$allValues = [];
		foreach ($filter['dials'] as $value) {
			$allValues[] = is_array($value) ? array_values($value) : [$value];
		}

		$allValues = array_merge(...$allValues);
		sort($allValues);

		$allValuesIds = implode(',', $allValues);
		$seoLink = $this->orm->seoLink->getBy([
			'parameterValuesIds' => $allValuesIds,
		]);

		if ($seoLink === null) {
			return null;
		}

		assert($seoLink instanceof SeoLink);
		$localization = $seoLink->getLocalizations()->getBy(['mutation' => $mutation]);
		if ($localization === null) {
			return null;
		}

		assert($localization instanceof SeoLinkLocalization);
		return $localization;
	}

}
