<?php

declare(strict_types=1);

namespace App\PostType\SeoLink\Model;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\PostType\Core\Model\EntityLocalizationFacade;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\SeoLink\Model\Orm\SeoLink;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalization;
use function assert;

final class SeoLinkLocalizationFacade implements EntityLocalizationFacade
{

	public function __construct(
		private readonly Orm $orm,
	)
	{
	}

	public function create(Mutation $mutation, ParentEntity|null $localizableEntity): SeoLinkLocalization
	{
		$localization = new SeoLinkLocalization();
		$this->orm->seoLinkLocalization->attach($localization);
		$localization->mutation = $mutation;

		if ($localizableEntity === null) {
			$localizableEntity = new SeoLink();
			$localization->seoLink = $localizableEntity;
		} else {
			assert($localizableEntity instanceof SeoLink);
			$localization->seoLink = $localizableEntity;
		}

		$this->orm->persistAndFlush($localization);

		return $localization;
	}

	public function remove(LocalizationEntity $localizableEntity): void
	{
		assert($localizableEntity instanceof SeoLinkLocalization);

		$parent = $localizableEntity->getParent();
		$this->orm->seoLinkLocalization->remove($localizableEntity);

		if ($parent->getLocalizations()->count() === 0) {
			$this->orm->seoLink->remove($parent);
		}

		$this->orm->flush();
	}

}
