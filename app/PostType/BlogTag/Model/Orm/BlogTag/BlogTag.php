<?php declare(strict_types = 1);

namespace App\PostType\BlogTag\Model\Orm\BlogTag;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\JsonContainer; // phpcs:ignore
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\Blog\Model\Orm\Blog;
use App\PostType\Core\Model\ParentEntity;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Exception\NoResultException;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property int $id {primary}
 * @property string $internalName {default ''}
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 *
 * RELATIONS
 * @property OneHasMany<BlogTagLocalization> $localizations {1:M BlogTagLocalization::$blogTag}
 * @property ManyHasMany<Blog> $blogs {m:m Blog::$tags}
 *
 * VIRTUAL
 * @property ArrayHash|null $cf {virtual}
 */
class BlogTag extends BaseEntity implements ParentEntity
{

	use HasCustomFields;

	public function getInternalName(): string
	{
		return $this->internalName;
	}

	public function setInternalName(string $internalName): void
	{
		$this->internalName = $internalName;
	}

	/**
	 * @return ICollection<BlogTagLocalization>
	 */
	public function getLocalizations(): ICollection
	{
		return $this->localizations->toCollection();
	}

	/**
	 * @throws NoResultException
	 */
	public function getLocalization(Mutation $mutation): BlogTagLocalization
	{
		$localization = $this->getLocalizations()->getByChecked(['mutation' => $mutation]);
		assert($localization instanceof BlogTagLocalization);
		return $localization;
	}

}
