<?php declare(strict_types = 1);

namespace App\PostType\BlogTag\Model\Orm\BlogTag;

use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<BlogTag>
 */
class BlogTagMapper extends DbalMapper
{

	use HasCamelCase;

	/**
* @return literal-string
*/
public function getTableName(): string
{
	return 'blog_tag';
}
	/**
	 * @return ICollection<BlogTag>
	 */
	public function searchByName(string $q, array $excluded = []): ICollection
	{
		$builder = $this->builder()
			->andWhere('internalName LIKE %_like_', $q);

		if (count($excluded) > 0) {
			$builder->andWhere('id not in %i[]', $excluded);
		}

		return $this->toCollection($builder);
	}

	/**
	 * @return ICollection<BlogTag>
	 */
	public function findByExactOrder(array $ids): ICollection
	{
		$builder = $this->builder()
			->andWhere('id in %i[]', $ids)
			->orderBy('%raw', 'FIELD(id, ' . implode(',', $ids) . ')');

		return $this->toCollection($builder);
	}

}
