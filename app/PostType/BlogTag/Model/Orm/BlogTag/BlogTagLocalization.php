<?php declare(strict_types = 1);

namespace App\PostType\BlogTag\Model\Orm\BlogTag;

use App\Model\Orm\JsonContainer; // phpcs:ignore
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\RoutableEntity;
use App\Model\Orm\Traits\HasCustomContent;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\Blog\Model\Orm\Blog;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\Blog\Model\Orm\BlogLocalizationRepository;
use App\PostType\Core\Model\Editable;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Core\Model\Publishable;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;

/**
 * @property int $id {primary}
 * @property string $name  {default ''}
 * @property bool $public {default false}
 * @property DateTimeImmutable|null $editedTime
 * @property int|null $edited
 * @property int $sort {default 0}
 *
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 * @property ArrayHash $customContentJson {container JsonContainer}
 *
 * RELATIONS
 * @property BlogTag $blogTag {M:1 BlogTag::$localizations}
 * @property Mutation $mutation {m:1 Mutation, oneSided=true}
 *
 *
 * VIRTUAL
 * @property ArrayHash|null $cf {virtual}
 * @property ArrayHash|null $cc {virtual}
 * @property-read ICollection< BlogLocalization> $blogsPublic {virtual}
 * @property-read string $template {virtual}
 */
class BlogTagLocalization extends RoutableEntity implements LocalizationEntity, Publishable, Editable
{

	use HasCustomFields;
	use HasCustomContent;

	private BlogLocalizationRepository $blogLocalizationRepository;

	public function injectBlogRepository(BlogLocalizationRepository $blogLocalizationRepository): void
	{
		$this->blogLocalizationRepository = $blogLocalizationRepository;
	}


	protected function getterTemplate(): string
	{
		return ':BlogTag:Front:BlogTag:detail';
	}


	protected function getterPath(): array
	{
		return [];
	}


	/**
	 * @return ICollection<BlogLocalization>
	 */
	protected function getterBlogsPublic(): ICollection
	{
		$blogIds = $this->blogTag->blogs->toCollection()->fetchPairs(null, 'id');
		return $this->blogLocalizationRepository
			->findBy([
					'blog' => $blogIds,
					'mutation' => $this->mutation,
				])
			->findBy($this->blogLocalizationRepository->getPublicOnlyWhereParams());
	}

	public function getId(): int
	{
		return $this->id;
	}

	public function getMutation(): Mutation
	{
		return $this->mutation;
	}


	public function setMutation(Mutation $mutation): void
	{
		$this->mutation = $mutation;
	}


	public function getParent(): BlogTag
	{
		return $this->blogTag;
	}


	public function setParent(ParentEntity $parentEntity): void
	{
		assert($parentEntity instanceof BlogTag);
		$this->blogTag = $parentEntity;
	}

	public function getIsPublic(): bool
	{
		return $this->public;
	}

	public function setIsPublic(bool $isPublic): void
	{
		$this->public = $isPublic;
	}

	public function getName(): string
	{
		return $this->name;
	}

	public function setName(string $name): void
	{
		$this->name = $name;
	}

	public function setEditorId(int $id): void
	{
		$this->edited = $id;
	}

	public function setEditedTime(DateTimeImmutable $editedTime): void
	{
		$this->editedTime = $editedTime;
	}
}
