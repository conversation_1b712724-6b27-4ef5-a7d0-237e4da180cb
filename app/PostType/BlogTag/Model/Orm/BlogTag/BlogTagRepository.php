<?php declare(strict_types = 1);

namespace App\PostType\BlogTag\Model\Orm\BlogTag;

use App\Model\Orm\CollectionById;
use App\Model\Orm\Searchable;
use App\PostType\Author\Model\Orm\AuthorMapper;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method BlogTag getById($id)
 * @method ICollection<BlogTag> findByExactOrder(array $ids)
 *
 * @extends Repository<BlogTag>
 */
final class BlogTagRepository extends Repository implements CollectionById, Searchable
{

	public static function getEntityClassNames(): array
	{
		return [BlogTag::class];
	}
	/**
	 * @return ICollection<BlogTag>
	 */
	public function findByIdOrder(array $ids): ICollection
	{
		return $this->findByExactOrder($ids);
	}

	/**
	 * @return ICollection<BlogTag>
	 */
	public function searchByName(string $string, array $excludedIds = []): ICollection
	{
		return $this->getMapper()->searchByName($string, $excludedIds);
	}

	public function getMapper(): BlogTagMapper
	{
		$mapper = parent::getMapper();
		assert($mapper instanceof BlogTagMapper);
		return $mapper;
	}

}
