<?php declare(strict_types = 1);

namespace App\PostType\BlogTag\Model;

use App\Model\Orm\Mutation\Mutation;
use App\PostType\Blog\Model\Orm\BlogLocalizationRepository;
use App\PostType\Blog\Model\Orm\BlogRepository;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalization;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalizationRepository;
use stdClass;

class BlogTagModel
{

	public function __construct(
		private BlogTagLocalizationRepository $blogTagLocalizationRepository,
		private BlogLocalizationRepository $blogLocalizationRepository,
		private BlogRepository $blogRepository,
	)
	{
	}

	public function getTagsWithCount(Mutation $mutation): array
	{
		$blogTagLocalizations = $this->blogTagLocalizationRepository->findBy([
			'mutation' => $mutation
		]);
		$tagsWithCount = [];

		foreach ($blogTagLocalizations as $blogTagLocalization) {
			assert($blogTagLocalization instanceof BlogTagLocalization);
			$tagData = new stdClass();
			$tagData->tag = $blogTagLocalization;

			$blogIds = $this->blogRepository->findBy(['tags->id' => $blogTagLocalization->blogTag->id])->fetchPairs(null, 'id');

			$blogTagLocalizations = $this->blogLocalizationRepository->findBy([
				'mutation' => $blogTagLocalization->getMutation(),
				'blog' => $blogIds,
			]);

			$tagData->count = $blogTagLocalizations->count();

			$tagsWithCount[] = $tagData;
		}

		usort($tagsWithCount, fn($a, $b) => $a->count <=> $b->count);
		return $tagsWithCount;
	}

}
