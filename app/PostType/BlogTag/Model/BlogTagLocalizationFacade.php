<?php declare(strict_types = 1);

namespace App\PostType\BlogTag\Model;


use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTag;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalization;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\PostType\Core\Model\EntityLocalizationFacade;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;

class BlogTagLocalizationFacade implements EntityLocalizationFacade
{

	public function __construct(
		private Orm $orm,
	)
	{
	}


	public function create(Mutation $mutation, ParentEntity|null $localizableEntity): LocalizationEntity
	{
		$localization = new BlogTagLocalization();
		$this->orm->blogTagLocalization->attach($localization);
		$localization->mutation = $mutation;

		if ($localizableEntity === null) {
			$localizableEntity = new BlogTag();
			$localization->blogTag = $localizableEntity;
		} else {
			assert($localizableEntity instanceof BlogTag);
			$localization->blogTag = $localizableEntity;
		}

		$this->orm->persistAndFlush($localization);

		return $localization;
	}


	public function remove(LocalizationEntity $localizableEntity): void
	{
		assert($localizableEntity instanceof BlogTagLocalization);

		$parent = $localizableEntity->getParent();
		$this->orm->blogTagLocalization->remove($localizableEntity);

		if ($parent->localizations->count() === 0) {
			$this->orm->blogTag->remove($parent);
		}

		$this->orm->flush();
	}

}
