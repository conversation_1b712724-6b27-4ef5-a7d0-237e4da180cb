<?php declare(strict_types = 1);

namespace App\PostType\BlogTag\FrontModule\Presenters;

use App\FrontModule\Presenters\BasePresenter;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTag;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalization;

/**
 * @method BlogTag getObject()
 */
final class BlogTagPresenter extends BasePresenter
{

	private BlogTagLocalization $blogTagLocalization;

	public function startup(): void
	{
		parent::startup();
	}


	public function actionDetail(BlogTagLocalization $object): void
	{
		$this->setObject($object);
		$this->blogTagLocalization = $object;
	}


	public function renderDetail(): void
	{
		$this->addComponent($this->visualPaginatorFactory->create(), 'pager');
		$this['pager']->object = $this->blogTagLocalization;
		$this['pager']->special = true;

		$paginator = $this['pager']->getPaginator();
		$paginator->itemsPerPage = 1;
		$allPublicBlogs = $this->blogTagLocalization->blogsPublic;
		$paginator->itemCount = $allPublicBlogs->count();

		$this->template->blogTag = $this->blogTagLocalization;
		$this->template->blogs = $allPublicBlogs->limitBy($paginator->itemsPerPage, $paginator->offset);

		if ($this->isAjax()) {
			$this->redrawControl('articles');
		}
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte');
	}

}
