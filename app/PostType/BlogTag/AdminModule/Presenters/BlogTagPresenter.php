<?php declare(strict_types = 1);

namespace App\PostType\BlogTag\AdminModule\Presenters;

use App\AdminModule\Presenters\BasePresenter;
use App\Model\Orm\User\User;
use App\PostType\BlogTag\AdminModule\Components\BlogTagDataGrid\BlogTagDataGridPrescription;
use App\PostType\BlogTag\AdminModule\Components\BlogTagDetailForm\BlogTagDetailFormPrescription;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalization;
use App\PostType\BlogTag\Model\BlogTagLocalizationFacade;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGrid;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGridFactory;
use App\PostType\Core\AdminModule\Components\Form\Form;
use App\PostType\Core\AdminModule\Components\Form\FormFactory;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellForm;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellFormFactory;

final class BlogTagPresenter extends BasePresenter
{

	private const string ORM_REPOSITORY_NAME = 'blogTag';

	private BlogTagLocalization $blogTagLocalization;

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
		private readonly FormFactory $blogTagFormFactory,
		private readonly ShellFormFactory $shellFormFactory,
		private readonly BlogTagLocalizationFacade $blogTagLocalizationFacade,
		private readonly BlogTagDetailFormPrescription $blogTagDetailFormPrescription,
		private readonly BlogTagDataGridPrescription $blogTagDatagridPrescription,
	)
	{
		parent::__construct();
	}

	public function renderDefault(): void
	{
	}


	public function actionEdit(int $id): void
	{
		$blogTagLocalization = $this->orm->blogTagLocalization->getById($id);
		if ($blogTagLocalization === null) {
			$this->redirect('default');
		}

		$this->blogTagLocalization = $blogTagLocalization;
	}


	public function renderEdit(int $id): void
	{
	}


	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create(self::ORM_REPOSITORY_NAME, $this->orm->blogTagLocalization->findAll(), dataGridDefinition: $this->blogTagDatagridPrescription->get());
	}


	protected function createComponentForm(): Form
	{
		/** @var User $userEntity */
		$userEntity = $this->userEntity;
		return $this->blogTagFormFactory->create($this->blogTagLocalizationFacade, $this->blogTagLocalization, $userEntity, $this->blogTagDetailFormPrescription->getPrescription($this->blogTagLocalization));
	}

	public function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create(entity:null, facade: $this->blogTagLocalizationFacade);
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte');
	}

}
