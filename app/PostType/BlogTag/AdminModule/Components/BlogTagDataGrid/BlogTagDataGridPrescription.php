<?php declare(strict_types = 1);

namespace App\PostType\BlogTag\AdminModule\Components\BlogTagDataGrid;

use App\PostType\Core\AdminModule\Components\DataGrid\Definition\DataGridDefinition;
use App\PostType\Core\AdminModule\Components\DataGrid\Definition\Extenders\CustomDataGridExtender;
use Ublaboo\DataGrid\DataGrid;

readonly class BlogTagDataGridPrescription
{
	public function get(): DataGridDefinition
	{
		$datagrid = new DataGrid();

		return new DataGridDefinition(
			dataGrid: $datagrid,
			extenders: [
				new CustomDataGridExtender(
					improveFunction:$this->updateColumns(...),
				),
			],
		);
	}

	public function updateColumns(DataGrid $dataGrid): void
	{

		$dataGrid
			->addColumnNumber('sort', 'sort')
			->setSortable();

		$dataGrid->removeColumn('internalName');
	}

}
