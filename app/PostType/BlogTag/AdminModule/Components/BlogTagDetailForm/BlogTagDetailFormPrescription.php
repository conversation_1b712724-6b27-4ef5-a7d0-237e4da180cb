<?php declare(strict_types = 1);

namespace App\PostType\BlogTag\AdminModule\Components\BlogTagDetailForm;

use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalization;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\CustomFormExtender;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\CommonTemplatePart;
use App\PostType\Core\AdminModule\Components\Form\Definition\FormDefinition;
use Nette\Application\UI\Form;

class BlogTagDetailFormPrescription
{
	public function getPrescription(BlogTagLocalization $blogTagLocalization): FormDefinition
	{
		$form = new Form();
		$form->setMappedType(BlogTagLocalizationFormData::class);
		$extenders = [];
		$extenders[] = $this->addSort($blogTagLocalization);

		return new FormDefinition(
			form: $form,
			extenders: $extenders,
			templateParameters: []
		);
	}

	private function addSort(BlogTagLocalization $blogTagLocalization): CustomFormExtender
	{
		return new CustomFormExtender(
			addHandler: function (Form $form) use ($blogTagLocalization) {
				$form->addInteger('sort', 'tag_sort')
					->setRequired()
					->setDefaultValue($blogTagLocalization->sort);
			},
			successHandler: function (Form $form, BlogTagLocalizationFormData $formData) use ($blogTagLocalization) {
				$blogTagLocalization->sort = $formData->sort;
			},
			templateParts: [
				new CommonTemplatePart(
					templateFile: __DIR__ . '/templates/sort.latte',
					type: CommonTemplatePart::TYPE_SIDE,
				),
			]
		);
	}

}
