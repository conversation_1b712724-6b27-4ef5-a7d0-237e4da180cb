<?php declare(strict_types = 1);

namespace App\PostType\BlogTag\Api\V1\List\Response;

use Apitte\Core\Mapping\Response\BasicEntity;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalization;

class BlogTagLocalizationListItem extends BasicEntity
{

	public readonly int $id;

	public readonly string $name;

	public function __construct(BlogTagLocalization $blogTagLocalization)
	{
		$this->id = $blogTagLocalization->id;
		$this->name = $blogTagLocalization->name;
	}

}
