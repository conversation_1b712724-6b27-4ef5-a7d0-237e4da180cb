<?php declare(strict_types = 1);

namespace App\PostType\Brand\FrontModule\Presenters;

use App\FrontModule\Components\CatalogProducts\CatalogProducts;
use App\FrontModule\Components\CatalogProducts\CatalogProductsCallbackBuilder;
use App\FrontModule\Components\CatalogProducts\CatalogProductsFactory;
use App\FrontModule\Components\CustomContentRenderer\HasCustomContentRenderer;
use App\FrontModule\Presenters\BasePresenter;
use App\FrontModule\Presenters\HasPagerLimits;
use App\FrontModule\Presenters\Pageable;
use App\Model\BucketFilter\BucketFilterBuilder;
use App\Model\BucketFilter\BucketFilterBuilderFactory;
use App\Model\BucketFilter\SortCreator;
use App\Model\Link\LinkSeo;
use App\PostType\Brand\Model\Orm\BrandLocalization;
use App\PostType\Page\Model\Orm\CommonTree;
use Nette\Application\Attributes\Persistent;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;

/**
 * @method BrandLocalization getObject()
 * @property BrandLocalization $object
 * @property-read DefaultTemplate $template
 */
final class BrandPresenter extends BasePresenter implements Pageable
{

	use HasCustomContentRenderer;
	use HasPagerLimits;

	private array $filterParams;

	private mixed $cleanFilterParam;

	#[Persistent]
	public int $page = 1;

	private BucketFilterBuilder $bucketFilterBuilder;

	private string $order;

	public function __construct(
		private readonly LinkSeo $linkSeo,
		private readonly BucketFilterBuilderFactory $bucketFilterBuilderFactory,
		private readonly CatalogProductsCallbackBuilder $catalogProductsCallbackBuilder,
		private readonly CatalogProductsFactory $catalogProductsFactory,
	)
	{
		parent::__construct();
	}

	protected function startup(): void
	{
		parent::startup();

		$this->template->seolink = false;

		$filterParams = $this->getRequest()->getParameter('filter');
		if ($filterParams === null) {
			$this->filterParams = [];
		} else {
			$this->filterParams = $filterParams;
		}

		$this->cleanFilterParam = $this->filterParams;
	}


	public function actionDefault(CommonTree $object, array $filter, string $order = SortCreator::TOP): void
	{
		$this->setObject($object);
		$this->bucketFilterBuilder = $this->bucketFilterBuilderFactory->create($this, $object, $this->currentState, $this->priceLevel, $order, $this->filterParams);
	}

	public function renderDefault(CommonTree $object): void
	{
		$this->template->brand = $this->orm->brandLocalization->findBy([
			'mutation' => $this->mutation,
		]);
	}


	public function actionDetail(BrandLocalization $object, array $filter = [], string $order = SortCreator::TOP): void
	{
		$this->order = $order;
		$this->setObject($object);
		$filter['brand'] = $object->brand->id;
		$this->filterParams = $filter;

		// Create bucket filter builder for brand products
		$this->bucketFilterBuilder = $this->bucketFilterBuilderFactory->create(
			$this,
			$this->mutation->pages->eshop, // Use eshop as base object for filtering
			$this->currentState,
			$this->priceLevel,
			$this->order,
			$this->filterParams
		);
	}


	public function renderDetail(BrandLocalization $object, string $order = SortCreator::TOP): void
	{
		$this->template->cleanFilterParamForCrossroad = $this->cleanFilterParam;
		$this->template->cleanFilterParam = $this->cleanFilterParam;

		$filter = $this->bucketFilterBuilder->getFilter();
		$this->seoToolsFilter = $filter;
		$this->seoToolsSort = $this->bucketFilterBuilder->getSort();

		$this->template->filter = $filter;
		$this->template->catalogOrder = $order;
		$this->template->sortingOptions = [SortCreator::TOP, SortCreator::CHEAPEST, SortCreator::EXPENSIVE];

		$this->template->brand = $object;
		$this->template->linkSeo = $this->linkSeo;

		$this->template->randomBrands = $this->orm->brandLocalization->findRandom()
			->findBy($this->orm->brandLocalization->getPublicOnlyWhereParams())
			->findBy([
				'id!=' => $object->id,
				'mutation' => $object->mutation,
			])
			->limitBy(4);

		$this->bucketFilterBuilder->redrawSnippets($this->cleanFilterParam);
	}


	protected function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->action . '.latte');

		$this->template->seolink = false;
		$this->template->isOpenInCookie = function ($name) {
			return $this->getHttpRequest()->getCookie('isOpen' . $name);
		};
	}

	protected function createComponentCatalogProducts(): CatalogProducts
	{
		$paramsToTemplate = [
			'productTitle' => 'h2',
			'ajaxPage' => true,
			'cleanFilterParam' => $this->cleanFilterParam,
		];

		$findCatalogProductsDataCallback = $this->catalogProductsCallbackBuilder->build($this->bucketFilterBuilder);

		return $this->catalogProductsFactory->create(
			$this->object,
			$this->setup,
			$findCatalogProductsDataCallback,
			$this->getPageParameter(),
			$paramsToTemplate,
			'brand',
			'brand',
		);
	}

}
