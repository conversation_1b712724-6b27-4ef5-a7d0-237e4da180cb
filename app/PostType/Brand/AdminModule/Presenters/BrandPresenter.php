<?php declare(strict_types = 1);

namespace App\PostType\Brand\AdminModule\Presenters;

use App\AdminModule\Presenters\BasePresenter;
use App\Model\Orm\User\User;
use App\PostType\Brand\AdminModule\Components\Form\BrandLocalizationFormPrescription;
use App\PostType\Brand\Model\BrandLocalizationFacade;
use App\PostType\Brand\Model\Orm\BrandLocalization;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGrid;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGridFactory;
use App\PostType\Core\AdminModule\Components\Form\Form;
use App\PostType\Core\AdminModule\Components\Form\FormFactory;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellForm;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellFormFactory;

final class BrandPresenter extends BasePresenter
{

	private const ORM_REPOSITORY_NAME = 'brand';

	private BrandLocalization $brandLocalization;

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
		private readonly FormFactory $brandFormFactory,
		private readonly ShellFormFactory $shellFormFactory,
		private readonly BrandLocalizationFacade $brandLocalizationFacade,
		private readonly BrandLocalizationFormPrescription $brandLocalizationFormPrescription,
	)
	{
		parent::__construct();
	}

	public function renderDefault(): void
	{
	}


	public function actionEdit(int $id): void
	{
		$brandLocalization = $this->orm->brandLocalization->getById($id);
		if ($brandLocalization === null) {
			$this->redirect('default');
		}

		$this->brandLocalization = $brandLocalization;
	}


	public function renderEdit(int $id): void
	{
	}


	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create(self::ORM_REPOSITORY_NAME, $this->orm->brandLocalization->findAll());
	}


	protected function createComponentForm(): Form
	{
		/** @var User $userEntity */
		$userEntity = $this->userEntity;
		return $this->brandFormFactory->create($this->brandLocalizationFacade, $this->brandLocalization, $userEntity, $this->brandLocalizationFormPrescription->getPrescription($this->brandLocalization));
	}

	public function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create(entity:null, facade: $this->brandLocalizationFacade);
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte');
	}

}
