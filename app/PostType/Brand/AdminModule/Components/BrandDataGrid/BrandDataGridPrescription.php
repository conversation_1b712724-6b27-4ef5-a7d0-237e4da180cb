<?php declare(strict_types=1);

namespace App\PostType\Brand\AdminModule\Components\BrandDataGrid;

use App\PostType\Core\AdminModule\Components\DataGrid\Definition\DataGridDefinition;
use App\PostType\Core\AdminModule\Components\DataGrid\Definition\Extenders\CustomDataGridExtender;
use App\PostType\Gift\Model\Orm\Gift\GiftLocalization;
use Ublaboo\DataGrid\DataGrid;

readonly class BrandDataGridPrescription
{

	public function getPrescription(): DataGridDefinition
	{
		$grid = new DataGrid();
		$grid->addColumnText('price_currency', 'currency')->setRenderer(fn(GiftLocalization $giftLocalization) => $giftLocalization->getParent()->price->currency ?? '-');

		$sortColumns = new CustomDataGridExtender(
			function (DataGrid $grid) {
				$grid->setColumnsOrder(['name', 'internalName', 'mutation', 'price_currency']);
			}
		);

		return new DataGridDefinition(
			dataGrid: $grid,
			beforeRenderExtenders: [$sortColumns],
		);
	}

}
