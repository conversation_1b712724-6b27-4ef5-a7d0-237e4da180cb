<?php declare(strict_types=1);

namespace App\PostType\Brand\AdminModule\Components\Form;

use App\Model\Orm\Parameter\ParameterRepository;
use App\Model\Orm\ParameterValue\ParameterValueRepository;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\CustomFormExtender;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\CommonTemplatePart;
use App\PostType\Core\AdminModule\Components\Form\Definition\FormDefinition;
use App\PostType\Brand\AdminModule\Components\Form\FormData\BrandLocalizationFormData;
use App\PostType\Brand\Model\Orm\BrandLocalization;
use Nette\Application\UI\Form;

readonly class BrandLocalizationFormPrescription
{

	public function __construct(
		private ParameterRepository $parameterRepository,
		private ParameterValueRepository $parameterValueRepository,
	)
	{
	}

	public function getPrescription(BrandLocalization $brandLocalization): FormDefinition
	{
		$form = new Form();
		$form->setMappedType(BrandLocalizationFormData::class);

		$extenders = [];
		$extenders[] = $this->addBrands($brandLocalization);

		return new FormDefinition(
			form: $form,
			extenders: $extenders,
			templateParameters: [
				'title' => $brandLocalization->getParent()->internalName,
			]
		);
	}

	public function addBrands(BrandLocalization $brandLocalization): CustomFormExtender
	{
		return new CustomFormExtender(
			addHandler: function (Form $form) use ($brandLocalization) {
				$parameter = $this->parameterRepository->getBy(['uid' => 'brand']);
				$parameterValues = isset($parameter->options) ? $parameter->options->toCollection()->fetchPairs('id', 'internalValue') : [];
				$form->addSelect('brand', 'Značka', $parameterValues)
					->setDefaultValue($brandLocalization->brandParameterValue ? $brandLocalization->brandParameterValue->id : null)
					->setPrompt('Vyberte značku');
			},
			successHandler: function (Form $form, BrandLocalizationFormData $data) use ($brandLocalization) {
				$brandLocalization->brandParameterValue = $this->parameterValueRepository->getById($data->brand);
			},
			templateParts: [
				new CommonTemplatePart(
					__DIR__ . '/parts/brand.latte',
					CommonTemplatePart::TYPE_MAIN,
					['langCodeBrand' => $brandLocalization->mutation->langCode],
				),
			]
		);
	}

}
