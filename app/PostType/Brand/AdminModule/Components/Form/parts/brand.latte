{embed $templates.'/part/box/toggle.latte', props=>[
	title: '<PERSON><PERSON><PERSON><PERSON>',
	id: 'brand',
	icon: $templates.'/part/icons/sliders-h.svg',
	variant: 'main',
	classes: ['u-mb-xxs'],
	open: true
], templates=>$templates}
	{block content}
		{embed $templates.'/part/box/std.latte', props=>[
		title=> '<PERSON><PERSON><PERSON><PERSON> (Adamint)',
		]}
			{block content}
				<div class="u-mb-sm">
				{include $templates.'/part/core/inp.latte',
					props: [
						type: select,
						input: $form['brand'],
						showLabel: false,
						classes: ['u-mb-sm'],
					]
				}
				</div>
			{/block}
		{/embed}
	{/block}
{/embed}
