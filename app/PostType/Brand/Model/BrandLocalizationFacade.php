<?php declare(strict_types = 1);

namespace App\PostType\Brand\Model;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\PostType\Core\Model\EntityLocalizationFacade;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Brand\Model\Orm\Brand;
use App\PostType\Brand\Model\Orm\BrandLocalization;

class BrandLocalizationFacade implements EntityLocalizationFacade
{

	public function __construct(
		private Orm $orm,
	)
	{
	}


	public function create(Mutation $mutation, ParentEntity|null $localizableEntity): LocalizationEntity
	{
		$localization = new BrandLocalization();
		$this->orm->brandLocalization->attach($localization);
		$localization->mutation = $mutation;

		if ($localizableEntity === null) {
			$localizableEntity = new Brand();
			$localization->brand = $localizableEntity;
		} else {
			assert($localizableEntity instanceof Brand);
			$localization->brand = $localizableEntity;
		}

		$this->orm->persistAndFlush($localization);

		return $localization;
	}


	public function remove(LocalizationEntity $localizableEntity): void
	{
		assert($localizableEntity instanceof BrandLocalization);

		$parent = $localizableEntity->getParent();
		$this->orm->brandLocalization->remove($localizableEntity);

		if ($parent->localizations->count() === 0) {
			$this->orm->brand->remove($parent);
		}

		$this->orm->flush();
	}

}
