<?php declare(strict_types = 1);

namespace App\PostType\Brand\Model\Orm;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\ParameterValue\ParameterValue;
use Nette\Utils\Random;
use Nette\Utils\Strings;

readonly class BrandLocalizationModel
{

	public function __construct(
		private Orm $orm,
	)
	{
	}

	public function generateDummyData(Mutation $mutation, int $limit = 50): void
	{
		for ($count = 0; $count < $limit; $count++) {
			$randomName = Strings::firstUpper(Random::generate(8, 'a-z'));
			$localization = new BrandLocalization();

			$localization->mutation = $mutation;
			$localization->name = $randomName;
			$localization->public = (bool) rand(0, 1);
			$this->orm->brandLocalization->attach($localization);

			$localizableEntity = new Brand();
			$this->orm->brand->attach($localizableEntity);

			$localizableEntity->internalName = Strings::firstUpper(Random::generate(8, 'a-z'));
			$localization->brand = $localizableEntity;

			// Create and associate a ParameterValue if needed
			if (rand(0, 1)) {
				$parameterValue = $this->orm->parameterValue->getBy(['parameter->uid' => 'brand']);
				if ($parameterValue instanceof ParameterValue) {
					$localization->brandParameterValue = $parameterValue;
				}
			}

			$this->orm->persistAndFlush($localization);
		}
	}

}
