<?php declare(strict_types = 1);

namespace App\PostType\Brand\Model\Orm;

use App\Model\Orm\CollectionById;
use App\Model\Orm\Searchable;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method Brand getById($id)
 * @method ICollection<Brand> findByExactOrder(array $ids)
 * @extends Repository<Brand>
 */
final class BrandRepository extends Repository implements CollectionById, Searchable
{

	public static function getEntityClassNames(): array
	{
		return [Brand::class];
	}
	/**
	 * @return ICollection<Brand>
	 */
	public function findByIdOrder(array $ids): ICollection
	{
		return $this->findByExactOrder($ids);
	}
	/**
	 * @return ICollection<Brand>
	 */
	public function searchByName(string $string, array $excludedIds = []): ICollection
	{
		return $this->getMapper()->searchByName($string, $excludedIds);
	}

	public function getMapper(): BrandMapper
	{
		$mapper = parent::getMapper();
		assert($mapper instanceof BrandMapper);
		return $mapper;
	}

}
