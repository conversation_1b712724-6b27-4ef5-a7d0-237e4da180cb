<?php declare(strict_types = 1);

namespace App\PostType\Brand\Model\Orm;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Repository\QueryForIdsByMutation;
use App\Model\Orm\Traits\HasPublicParameter;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method BrandLocalization|null getById($id)
 * @method ICollection<BrandLocalization> searchByName(string $q, array $excluded)
 * @method ICollection<BrandLocalization> findRandom()
 * @method array findAllIds(?int $limit)
 * @extends Repository<BrandLocalization>
 */
final class BrandLocalizationRepository extends Repository implements QueryForIdsByMutation
{

	use HasPublicParameter;

	public static function getEntityClassNames(): array
	{
		return [BrandLocalization::class];
	}


	public function getPublicOnlyWhereParams(): array
	{
		return [
			'public' => 1,
		];
	}


	public function findAllIdsInMutation(Mutation $mutation, ?int $limit = null): Result
	{
		$mapper = $this->mapper;
		assert($mapper instanceof BrandLocalizationMapper);

		return $mapper->findAllIdsInMutation($mutation, $limit);
	}

	public function getByExtId(int $extId): BrandLocalization|null
	{
		return $this->getBy(['extId' => $extId]);
	}

}
