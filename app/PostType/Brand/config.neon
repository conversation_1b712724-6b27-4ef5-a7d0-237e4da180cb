cf:
	templates:
		brand:
			settings:
				type: group
				label: "Nastavení"
				items:
					logo:
						type: image
						label: "<PERSON><PERSON> (min. 320x320)"

		brandLocalization:
			settings:
				type: group
				label: "Nastavení"
				items:
					shortDescription:
						type: textarea
						label: "Krátký popis"
					longDescription:
						type: tinymce
						label: "Dlouhý popis"

cc:
	templates:

application:
	mapping:
		Brand: App\PostType\Brand\*Module\Presenters\*Presenter

parameters:
	postTypeRoutes:
		Brand: brand

services:

	## BRAND
	- App\PostType\Brand\Model\BrandLocalizationFacade
	- App\PostType\Brand\AdminModule\Components\BrandDataGrid\BrandDataGridPrescription
	- App\PostType\Brand\AdminModule\Components\Form\BrandLocalizationFormPrescription
	- App\PostType\Brand\AdminModule\Components\ShellForm\ShellFormPrescription
	- App\PostType\Brand\Model\Orm\BrandLocalizationModel
