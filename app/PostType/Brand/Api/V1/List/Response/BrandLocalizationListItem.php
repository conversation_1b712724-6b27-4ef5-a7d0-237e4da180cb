<?php declare(strict_types = 1);

namespace App\PostType\Brand\Api\V1\List\Response;

use Apitte\Core\Mapping\Response\BasicEntity;
use App\PostType\Brand\Model\Orm\BrandLocalization;

class BrandLocalizationListItem extends BasicEntity
{

	public readonly int $id;

	public readonly string $name;

	public function __construct(BrandLocalization $brandLocalization)
	{
		$this->id = $brandLocalization->id;
		$this->name = $brandLocalization->name;
	}

}
