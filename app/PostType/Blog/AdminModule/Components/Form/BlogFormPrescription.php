<?php declare(strict_types=1);

namespace App\PostType\Blog\AdminModule\Components\Form;

use App\Model\CustomField\SuggestUrls;
use App\PostType\Blog\AdminModule\Components\Form\FormData\BlogFormData;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\Blog\Model\Orm\BlogLocalizationModel;
use App\PostType\Core\AdminModule\Components\Form\Builder;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\CustomFormExtender;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\CommonTemplatePart;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\RelationTemplatePart;
use App\PostType\Core\AdminModule\Components\Form\Definition\FormDefinition;
use App\PostType\Core\AdminModule\Components\Form\Definition\RelationInfoFactory;
use App\PostType\Core\AdminModule\Components\Form\Handler;
use Nette\Application\UI\Form;
use Nette\Http\Request;

class BlogFormPrescription
{
	public function __construct(
		private readonly BlogLocalizationModel $blogLocalizationModel,
		private readonly RelationInfoFactory $relationInfoFactory,
		private readonly Request $request,
		private readonly Builder $coreBuilder,
		private readonly Handler $coreHandler,
		private readonly SuggestUrls $urls,
		private readonly string $coreFormPath,

	)
	{}

	public function get(BlogLocalization $blogLocalization): FormDefinition
	{
		$extenders = [];
		$extenders[] = $this->addTags($blogLocalization);
		$extenders[] = $this->addAuthors($blogLocalization);
		$extenders[] = $this->addCategories($blogLocalization);

		$extenders[] = $this->addIsTop($blogLocalization);

		$form = new Form();
		$form->setMappedType(BlogFormData::class);

		return new FormDefinition(
			form: $form,
			extenders: $extenders,
		);
	}

	public function addTags(BlogLocalization $blogLocalization): CustomFormExtender
	{
		$tagsRelationsInfo = $this->relationInfoFactory->create(
			sourceEntity: $blogLocalization->getParent(),
			propertyName: 'tags',
			suggestUrl: $this->urls['searchBlogTag'],
		);

		return new CustomFormExtender(
			addHandler: function (Form $form) use ($tagsRelationsInfo) {
				$this->coreBuilder->addHasManyRelation($form, $tagsRelationsInfo, $this->request->getPost());
			},
			successHandler: function (Form $form, BlogFormData $data) use ($tagsRelationsInfo) {
				$this->coreHandler->handleHasManyRelation($data->{$tagsRelationsInfo->propertyName}, $tagsRelationsInfo);
			},
			templateParts: [
				new RelationTemplatePart(
					relationInfo: $tagsRelationsInfo,
					templateFile: $this->coreFormPath . '/parts/relation.latte',
					type: RelationTemplatePart::TYPE_RELATION),
				new RelationTemplatePart(
					relationInfo: $tagsRelationsInfo,
					templateFile: $this->coreFormPath . '/parts/newItemTemplate.latte',
					type: RelationTemplatePart::TYPE_RELATION_PRESCRIPTION),
			]
		);

	}

	public function addIsTop(BlogLocalization $blogLocalization): CustomFormExtender
	{
		return new CustomFormExtender(
			function (Form $form) use ($blogLocalization) {
				$form->addCheckbox('isTop', 'Top')->setDefaultValue($blogLocalization->isTop);
			},
			function (Form $form, BlogFormData $data) use ($blogLocalization) {
				$blogLocalization->isTop = $data->isTop;
			},
			[
				new CommonTemplatePart(__DIR__ . '/settings.latte',
					CommonTemplatePart::TYPE_SIDE,
					['langCodeIsTop' => $blogLocalization->mutation->langCode],
				)
			]
		);
	}

	private function addAuthors(BlogLocalization $blogLocalization): CustomFormExtender
	{
		$authorsRelationsInfo = $this->relationInfoFactory->create(
			sourceEntity: $blogLocalization->getParent(),
			propertyName: 'authors',
			suggestUrl: $this->urls['searchAuthors'],
		);

		return new CustomFormExtender(
			addHandler: function (Form $form) use ($authorsRelationsInfo) {
				$this->coreBuilder->addHasManyRelation($form, $authorsRelationsInfo, $this->request->getPost());
			},
			successHandler: function (Form $form, BlogFormData $data) use ($authorsRelationsInfo) {
				$this->coreHandler->handleHasManyRelation($data->{$authorsRelationsInfo->propertyName}, $authorsRelationsInfo);
			},
			templateParts: [
				new RelationTemplatePart(
					relationInfo: $authorsRelationsInfo,
					templateFile: $this->coreFormPath . '/parts/relation.latte',
					type: RelationTemplatePart::TYPE_RELATION),
				new RelationTemplatePart(
					relationInfo: $authorsRelationsInfo,
					templateFile: $this->coreFormPath . '/parts/newItemTemplate.latte',
					type: RelationTemplatePart::TYPE_RELATION_PRESCRIPTION),
			]
		);
	}

	private function addCategories(BlogLocalization $blogLocalization): CustomFormExtender
	{
		$url = $this->urls['searchMutationPage'];
		$url->params['mutationId'] = $blogLocalization->mutation->id;
		$url->params['templates'] = [':Blog:Front:Blog:default'];

		$categoriesRelationsInfo = $this->relationInfoFactory->create(
			sourceEntity: $blogLocalization,
			propertyName: 'blogLocalizationTrees',
			suggestUrl: $url,
			inputSuggestPropertyName: 'name',
			toggleName: 'categories',
			dragAndDrop: true,
			builderCollection: $blogLocalization->categories,

		);

		return new CustomFormExtender(
			addHandler: function (Form $form) use ($categoriesRelationsInfo) {
				$this->coreBuilder->addHasManyRelation($form, $categoriesRelationsInfo, $this->request->getPost());
			},
			successHandler: function (Form $form, BlogFormData $data) use ($categoriesRelationsInfo, $blogLocalization) {
				$ids = Handler::readOnlyValidIds($data->{$categoriesRelationsInfo->propertyName});
				$this->blogLocalizationModel->setCategoriesByIds($blogLocalization, $ids);
			},
			templateParts: [
				new RelationTemplatePart(
					relationInfo: $categoriesRelationsInfo,
					templateFile: $this->coreFormPath . '/parts/relation.latte',
					type: RelationTemplatePart::TYPE_RELATION),
				new RelationTemplatePart(
					relationInfo: $categoriesRelationsInfo,
					templateFile:$this->coreFormPath . '/parts/newItemTemplate.latte',
					type: RelationTemplatePart::TYPE_RELATION_PRESCRIPTION),
			]
		);
	}
}
