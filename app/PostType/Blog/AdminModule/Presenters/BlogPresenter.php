<?php declare(strict_types = 1);

namespace App\PostType\Blog\AdminModule\Presenters;

use App\AdminModule\Presenters\BasePresenter;
use App\Model\Orm\User\User;
use App\PostType\Blog\AdminModule\Components\DataGrid\BlogDataGridPrescription;
use App\PostType\Blog\AdminModule\Components\Form\BlogFormPrescription;
use App\PostType\Blog\AdminModule\Components\Form\FormData\BaseFormData;
use App\PostType\Blog\Model\BlogLocalizationFacade;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGrid;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGridFactory;
use App\PostType\Core\AdminModule\Components\Form\Definition\RelationInfo;
use App\PostType\Core\AdminModule\Components\Form\Form;
use App\PostType\Core\AdminModule\Components\Form\FormFactory;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellForm;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellFormFactory;
use Nette\Utils\ArrayHash;

final class BlogPresenter extends BasePresenter
{

	public const ORM_REPOSITORY_NAME = 'blog';

	private BlogLocalization $blogLocalization;

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
		private readonly FormFactory $formFactory,
		private readonly ShellFormFactory $shellFormFactory,
		private readonly BlogLocalizationFacade $blogLocalizationFacade,
		private readonly BlogFormPrescription $blogFormPrescription,
		private readonly BlogDataGridPrescription $blogDataGridPrescription,
	)
	{
		parent::__construct();
	}

	public function renderDefault(): void
	{
	}


	public function actionEdit(int $id): void
	{
		$blog = $this->orm->blogLocalization->getById($id);
		if ($blog === null) {
			$this->redirect('default');
		}

		$this->blogLocalization = $blog;
	}


	public function renderEdit(int $id): void
	{
	}


	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create(
			baseEntityName: self::ORM_REPOSITORY_NAME,
			collection: $this->orm->blogLocalization->findAll(),
			dataGridDefinition: $this->blogDataGridPrescription->get(),
		);
	}



	public function createComponentForm(): Form
	{
		/** @var User $userEntity */
		$userEntity = $this->userEntity;
		return $this->formFactory->create($this->blogLocalizationFacade, $this->blogLocalization, $userEntity, $this->blogFormPrescription->get($this->blogLocalization));
	}

	public function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create(entity: null, facade: $this->blogLocalizationFacade);
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->action . '.latte');
	}

}
