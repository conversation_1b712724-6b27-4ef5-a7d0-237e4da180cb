{varType App\PostType\Blog\Model\Orm\BlogLocalization $blogLocalization}

{if isset($blogLocalization->firstImage)}
	{php $img = $blogLocalization->firstImage->getSize('md')}
	{php $stDataImage = $mutation->getBaseUrl().$img->src}
{else}
	{php $stDataImage = NULL}
{/if}


<script type="application/ld+json">
	{
		"@context": "http://schema.org/",
		"@type": "NewsArticle",
		"mainEntityOfPage": {$mutation->getBaseUrlWithPrefix()."/".$blogLocalization->alias},
		"headline": {$blogLocalization->name},
		"datePublished": {$blogLocalization->publicFrom->format('Y-m-d')},
		"dateModified": {if $blogLocalization->editedTime}{$blogLocalization->editedTime->format('Y-m-d')}{else}{$blogLocalization->createdTimeOrder->format('Y-m-d')}{/if},
		"description": {$blogLocalization->annotation},
		{if $stDataImage}
			"image": {
				"@type": "ImageObject",
				"height": {$stDataImageW},
				"width": {$stDataImageH},
				"url": {$stDataImage}
			},
		{/if}
		{if $author !== null}
			"author": {
				"@type": "Person",
				"name": {$author->name}
			},
		{/if}
		"publisher": {
			"@type": "Organization",
			"logo": {
				"@type": "ImageObject",
				"url": {$mutation->getBaseUrl()."/static/img/logo.png"}
			},
			"name": {$publisher}
		},
		"articleBody": {$content}
	}
	</script>
