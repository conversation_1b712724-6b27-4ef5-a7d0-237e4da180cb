<?php declare(strict_types = 1);

namespace App\PostType\Blog\FrontModule\Components\BlogLocalizationStructuredData;

use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\Model\Orm\Mutation\Mutation;
use Nette\Application\UI\Control;
use App\Model\TranslatorDB;

class BlogLocalizationStructuredData extends Control
{

	public function __construct(
		private BlogLocalization $blogLocalization,
		private Mutation $mutation,
		private TranslatorDB $translator
	)
	{
	}


	public function render(): void
	{
		$this->template->setTranslator($this->translator);

		$this->template->blogLocalization = $this->blogLocalization;
		$this->template->mutation = $this->mutation;
		$this->template->templates = FE_TEMPLATE_DIR;

		$this->template->content = (isset($this->blogLocalization->cf->base->annotation)) ? $this->blogLocalization->cf->base->annotation : '';
		$this->template->publisher = 'superadmin';

		$this->template->author = $this->blogLocalization->authors->fetch();


		$this->template->render(__DIR__ . '/blogLocalizationStructuredData.latte');
	}

}
