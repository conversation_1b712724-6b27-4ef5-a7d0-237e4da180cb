<?php

declare(strict_types=1);

namespace App\PostType\Blog\FrontModule\Components\BlogList;

use App\Model\Orm\Routable;
use App\Model\Setup;
use App\Model\StaticPage\StaticPage;
use Closure;

interface BlogListFactory
{

	/**
	 * @phpstan-param Closure(int $limit, int $offset): BlogListData $findBlogListDataCallback
	 */
	public function create(
		Routable|StaticPage $routable,
		Closure $findBlogListDataCallback,
		int $pageNumber,
		array $paramsToTemplate,
	): BlogList;

}
