<?php declare(strict_types = 1);

namespace App\PostType\Blog\FrontModule\Components\BlogList;

use App\Components\VisualPaginator\VisualPaginator;
use App\Components\VisualPaginator\VisualPaginatorFactory;
use App\Model\ConfigService;
use App\Model\Orm\Routable;
use App\Model\StaticPage\StaticPage;
use App\Model\TranslatorDB;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use Closure;
use Nette\Application\UI\Control;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nextras\Orm\Collection\ICollection;

/**
 * @property-read DefaultTemplate $template
 */
final class BlogList extends Control
{
	private int $totalCount;

	private int $itemPerPage;

	public array $onAfterRender = [];

		/** @var ICollection<BlogLocalization>  */
	private ICollection $blogLocalizations;

	/**
	 * @phpstan-param Closure(int $limit, int $offset): BlogListData $findBlogListDataCallback
	 */
	public function __construct(
		private readonly Routable|StaticPage $routable,
		private readonly Closure $findBlogListDataCallback,
		private readonly int $pageNumber,
		private readonly array $paramsToTemplate,
		private readonly TranslatorDB $translator,
		private readonly ConfigService $configService,
		private readonly VisualPaginatorFactory $visualPaginatorFactory,
	)
	{
		$this->onAnchor[] = $this->init(...);
	}

	public function setItemPerPage(int $itemPerPage): void
	{
		$this->itemPerPage = $itemPerPage;
	}

	private function init(): void
	{
		$itemsPerPage = $this->itemPerPage ?? $this->configService->get('blog', 'paging');
		$this['pager']->object  = $this->routable;
		$this['pager']->special = true;
		$this['pager']->setTranslator($this->translator);
		$paginator = $this['pager']->getPaginator();
		$paginator->setPage($this->pageNumber);
		$paginator->itemsPerPage = $itemsPerPage;

		$blogListData = ($this->findBlogListDataCallback)($paginator->itemsPerPage, $paginator->offset);
		assert($blogListData instanceof BlogListData);

		$this->blogLocalizations = $blogListData->blogLocalizations;
		$this->totalCount = $blogListData->totalCount;
		$paginator->itemCount = max($this->totalCount, 0);

		foreach ($this->onAfterRender as $afterRender) {
			$afterRender($this->blogLocalizations);
		}
	}

	public function render(): void
	{
		$template = $this->template;
		$template->showMoreBtn = true;
		$this->template->templates = FE_TEMPLATE_DIR;

		foreach ($this->paramsToTemplate as $key => $value) {
			$this->getTemplate()->$key = $value;
		}

		$template->setTranslator($this->translator);
		$template->FE_TEMPLATE_DIR = FE_TEMPLATE_DIR;
		$template->blogLocalizations = $this->blogLocalizations;
		$this->template->render(__DIR__ . '/templates/blogList.latte');
	}

	public function createComponentPager(): VisualPaginator
	{
		return $this->visualPaginatorFactory->create();
	}

	public function getTotalCount(): int
	{
		return $this->totalCount;
	}

}
