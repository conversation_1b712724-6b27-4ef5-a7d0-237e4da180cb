<?php declare(strict_types = 1);

namespace App\PostType\Blog\FrontModule\Presenters;

use App\FrontModule\Components\CustomContentRenderer\HasCustomContentRenderer;
use App\FrontModule\Presenters\BasePresenter;
use App\Model\BucketFilter\BucketFilterBuilder;
use App\Model\BucketFilter\BucketFilterBuilderFactory;
use App\PostType\Blog\FrontModule\Components\Attached\AttachedBlogs;
use App\PostType\Blog\FrontModule\Components\Attached\AttachedBlogsFactory;
use App\PostType\Blog\FrontModule\Components\BlogList\BlogListCallbackBuilder;
use App\PostType\Blog\FrontModule\Components\BlogList\BlogList;
use App\PostType\Blog\FrontModule\Components\BlogList\BlogListFactory;
use App\PostType\Blog\Model\Orm\Blog;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\Blog\Model\Orm\BlogLocalizationModel;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Blog\FrontModule\Components\BlogLocalizationStructuredData\BlogLocalizationStructuredData;
use App\PostType\Blog\FrontModule\Components\BlogLocalizationStructuredData\BlogLocalizationStructuredDataFactory;
use App\PostType\BlogTag\Model\BlogTagModel;

/**
 * @method Blog getObject()
 */
final class BlogPresenter extends BasePresenter
{

	use HasCustomContentRenderer;

	private BucketFilterBuilder $bucketFilterBuilder;

	private array $filterParams;

	private mixed $cleanFilterParam;

	private BlogLocalization $blogLocalization;

	public function __construct(
		private readonly AttachedBlogsFactory $attachedBlogsFactory,
		private readonly BlogListFactory $blogListFactory,
		private readonly BlogLocalizationModel $blogLocalizationModel,
		private readonly BucketFilterBuilderFactory $bucketFilterBuilderFactory,
		private readonly BlogLocalizationStructuredDataFactory $blogLocalizationStructuredDataFactory,
		private readonly BlogTagModel $blogTagModel,
		private readonly BlogListCallbackBuilder $blockListCallbackBuilder,

	)
	{
		parent::__construct();
	}

	public function startup(): void
	{
		parent::startup();

		$filterParams = $this->getRequest()->getParameter('filter');
		if ($filterParams === null) {
			$this->filterParams = [];
		} else {
			$this->filterParams = $filterParams;
		}

		$this->cleanFilterParam = $this->filterParams;
	}


	public function actionDefault(CommonTree $object): void
	{
		$this->setObject($object);

		$this->bucketFilterBuilder = $this->bucketFilterBuilderFactory->create($this, $object, $this->currentState, $this->priceLevel, '', $this->filterParams);
	}


	public function renderDefault(CommonTree $object): void
	{
		$this->template->cleanFilterParam = $this->cleanFilterParam;
		$filter = $this->bucketFilterBuilder->getFilter();
		$this->seoToolsFilter = $filter;
		$this->seoToolsSort = $this->bucketFilterBuilder->getSort();

		$this->template->filter = $filter;

		$this->bucketFilterBuilder->redrawSnippets($this->cleanFilterParam);

		/*$this->addComponent($this->visualPaginatorFactory->create(), 'pager');
		$this['pager']->object = $object;
		$this['pager']->special = true;

		$paginator = $this['pager']->getPaginator();
		$paginator->itemsPerPage = $this->configService->get('blog', 'paging');

		$possibleBlogLocalizationIds = $this->orm->blogLocalizationTree->findBy(['tree->id' => $object->id])->fetchPairs(null, 'blogLocalization->id');
		$blogLocalizations = $this->orm->blogLocalization->findBy(['id' => $possibleBlogLocalizationIds])->orderBy('publicFrom');

		$totalCount = $blogLocalizations->countStored();
		$paginator->itemCount = $totalCount;

		$this->template->tagsWithCount = $this->blogTagModel->getTagsWithCount($this->mutation);
		$this->template->totalCount = $totalCount;
		$this->template->blogLocalizations = $blogLocalizations->limitBy($paginator->itemsPerPage, $paginator->offset);

		if ($this->isAjax()) {
			if ($this['pager']->getParameter('more')) {
				$this->redrawControl('articlesInner');
				$this->redrawControl('articlesPagerBottom');
				$this->redrawControl('articleList');
			} else {
				if (!$this->getSignal()) {
					$this->redrawControl();
				}
			}
		}*/
	}

	public function actionDetail(BlogLocalization $object): void
	{
		$this->setObject($object);
		$this->blogLocalization = $object;
	}


	public function renderDetail(): void
	{
		$this->blogLocalizationModel->increaseViews($this->blogLocalization);

		$this->template->blogLocalization = $this->blogLocalization;
		$this->template->tagsWithCount = $this->blogTagModel->getTagsWithCount($this->mutation);
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte');
	}

	public function createComponentBlogList(): BlogList
	{
		$paramsToTemplate = [
			'productTitle' => 'h2',
			'ajaxPage' => true,
			'cleanFilterParam' => $this->cleanFilterParam,
			'class' => 'u-mb-0',
		];

		$findBlogListDataCallback = $this->blockListCallbackBuilder->build($this->bucketFilterBuilder);

		return $this->blogListFactory->create(
			$this->object,
			$findBlogListDataCallback,
			$this->getPageParameter(),
			$paramsToTemplate,
		);
	}



	protected function createComponentAttachedBlogs(): AttachedBlogs
	{
		return $this->attachedBlogsFactory->create($this->blogLocalization->blog);
	}

	protected function createComponentBlogLocalizationStructuredData(): BlogLocalizationStructuredData
	{
		return $this->blogLocalizationStructuredDataFactory->create($this->blogLocalization, $this->mutation);
	}

}
