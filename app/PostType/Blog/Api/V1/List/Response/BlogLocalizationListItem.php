<?php declare(strict_types = 1);

namespace App\PostType\Blog\Api\V1\List\Response;

use Apitte\Core\Mapping\Response\BasicEntity;
use App\Api\Entity\Response\CustomContent;
use App\Api\Entity\Response\CustomFields;
use App\PostType\Blog\Model\Orm\BlogLocalization;

class BlogLocalizationListItem extends BasicEntity
{

	public readonly int $id;

	public readonly string $name;

	public readonly string $nameAnchor;

	public readonly string $nameTitle;

	public readonly string $alias;

	public readonly ?CustomFields $cf;

	public readonly ?CustomFields $parentCf;

	public readonly ?CustomContent $cc;

	public function __construct(BlogLocalization $blogLocalization)
	{
		$this->id = $blogLocalization->id;
		$this->alias = $blogLocalization->alias->alias;
		$this->name = $blogLocalization->name;
		$this->nameAnchor = $blogLocalization->nameAnchor;
		$this->nameTitle = $blogLocalization->nameTitle;
		$this->cf = CustomFields::from($blogLocalization->cf);
		$this->parentCf = CustomFields::from($blogLocalization->blog->cf);
		$this->cc = CustomContent::from($blogLocalization->cc);
	}

}
