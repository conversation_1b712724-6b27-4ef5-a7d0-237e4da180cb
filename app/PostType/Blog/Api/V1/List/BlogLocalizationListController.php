<?php declare(strict_types = 1);

namespace App\PostType\Blog\Api\V1\List;

use Apitte\Core\Annotation\Controller\Method;
use Apitte\Core\Annotation\Controller\Path;
use Apitte\Core\Annotation\Controller\RequestParameter;
use Apitte\Core\Annotation\Controller\Response;
use Apitte\Core\Annotation\Controller\Tag;
use Apitte\Core\Http\ApiRequest;
use Apitte\Core\Http\ApiResponse;
use Apitte\Core\Schema\EndpointParameter;
use App\Api\Decorator\MutationFinder;
use App\Api\V1\Controllers\BaseV1Controller;
use App\PostType\Blog\Api\V1\List\Response\BlogLocalizationList;
use App\PostType\Blog\Model\Orm\BlogLocalizationRepository;
use Nextras\Orm\Collection\ICollection;

#[Path('/')]
#[Tag('noAuthentication')]
#[Tag('Blog')]
final class BlogLocalizationListController extends BaseV1Controller
{

	public function __construct(
		private readonly BlogLocalizationRepository $blogLocalizationRepository,
	)
	{
	}
	public const DEFAULT_LIMIT = 20;
	public const MAX_LIMIT = 20;

	#[Path('/mutation/{mutationId}/blog')]
	#[Method('GET')]
	#[RequestParameter(name: 'limit', type: 'int', in: EndpointParameter::IN_QUERY, required: false, description: 'Resource paging')]
	#[RequestParameter(name: 'page', type: 'int', in: EndpointParameter::IN_QUERY, required: false, description: 'Resource paging')]
	#[RequestParameter(name: 'sort', type: 'string', in: EndpointParameter::IN_QUERY, required: false, description: 'List order')]
	#[RequestParameter(name: MutationFinder::MUTATION_ID_PARAMETER_NAME, type: 'int', in: EndpointParameter::IN_PATH, required: true, description: 'Mutation Id')]
	#[Response(description: 'Success', code: '200', entity: BlogLocalizationList::class)]
	public function get(ApiRequest $request, ApiResponse $response): ApiResponse
	{
		$limit = min(
			$request->getParameter('limit', self::DEFAULT_LIMIT),
			self::MAX_LIMIT
		);
		$page = $request->getParameter('page', 0);
		$responseEntity = new BlogLocalizationList(
			$this->blogLocalizationRepository->findBy([
				'mutation' => $request->getParameter(MutationFinder::MUTATION_PARAMETER_NAME),
			])->limitBy($limit, $page * $limit)->orderBy('publicFrom', ICollection::DESC)
		);

		return $this->jsonResponse($responseEntity->toResponse(), $response);
	}

}
