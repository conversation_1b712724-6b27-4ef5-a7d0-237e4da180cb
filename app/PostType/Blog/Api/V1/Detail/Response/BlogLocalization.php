<?php declare(strict_types = 1);

namespace App\PostType\Blog\Api\V1\Detail\Response;

use Apitte\Core\Mapping\Response\BasicEntity;
use App\Api\Entity\Response\CustomContent;
use App\Api\Entity\Response\CustomFields;
use App\Api\Entity\Response\IdentifiedListItem;
use App\PostType\Author\Api\V1\List\Response\AuthorLocalizationListItem;
use App\PostType\BlogTag\Api\V1\List\Response\BlogTagLocalizationListItem;

class BlogLocalization extends BasicEntity
{

	public readonly int $id;

	public readonly string $name;

	public readonly string $nameAnchor;

	public readonly string $nameTitle;

	public readonly string $alias;

	public readonly ?CustomFields $cf;

	public readonly ?CustomFields $parentCf;

	public readonly ?CustomContent $cc;

	/** @var BlogTagLocalizationListItem[] */
	public readonly array $tags;

	/** @var AuthorLocalizationListItem[] */
	public readonly array $authors;

	/** @var IdentifiedListItem[] */
	public readonly array $categories;

	public function __construct(\App\PostType\Blog\Model\Orm\BlogLocalization $blogLocalization)
	{
		$this->id = $blogLocalization->id;
		$this->alias = $blogLocalization->alias->alias;
		$this->name = $blogLocalization->name;
		$this->nameAnchor = $blogLocalization->nameAnchor;
		$this->nameTitle = $blogLocalization->nameTitle;
		$this->cf = CustomFields::from($blogLocalization->cf);
		$this->parentCf = CustomFields::from($blogLocalization->blog->cf);
		$this->cc = CustomContent::from($blogLocalization->cc);

		$tags = [];
		foreach ($blogLocalization->blogTags as $blogTagLocalization) {
			$tags[] = new BlogTagLocalizationListItem($blogTagLocalization);
		}

		$this->tags = $tags;

		$authors = [];
		foreach ($blogLocalization->authors as $authorLocalization) {
			$authors[] = new AuthorLocalizationListItem($authorLocalization);
		}

		$this->authors = $authors;

		$categories = [];
		foreach ($blogLocalization->categories as $category) {
			$categories[] = new IdentifiedListItem($category);
		}

		$this->categories = $categories;
	}

}
