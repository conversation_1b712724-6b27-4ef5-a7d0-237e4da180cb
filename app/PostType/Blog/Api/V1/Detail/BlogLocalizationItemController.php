<?php declare(strict_types = 1);

namespace App\PostType\Blog\Api\V1\Detail;

use Apitte\Core\Annotation\Controller\Method;
use Apitte\Core\Annotation\Controller\Path;
use Apitte\Core\Annotation\Controller\RequestParameter;
use Apitte\Core\Annotation\Controller\Response;
use Apitte\Core\Annotation\Controller\Tag;
use Apitte\Core\Exception\Api\ClientErrorException;
use Apitte\Core\Http\ApiRequest;
use Apitte\Core\Http\ApiResponse;
use Apitte\Core\Schema\EndpointParameter;
use App\Api\Decorator\MutationFinder;
use App\Api\V1\Controllers\BaseV1Controller;
use App\PostType\Blog\Api\V1\Detail\Response\BlogLocalization;
use App\PostType\Blog\Model\Orm\BlogLocalizationRepository;

#[Path('/')]
#[Tag('noAuthentication')]
#[Tag('Blog')]
final class BlogLocalizationItemController extends BaseV1Controller
{

	public function __construct(
		private readonly BlogLocalizationRepository $blogLocalizationRepository,
	)
	{
	}

	#[Path('/mutation/{mutationId}/blog/{id}')]
	#[Method('GET')]
	#[RequestParameter(name: 'id', type: 'int', in: EndpointParameter::IN_PATH, required: true, description: 'Blog post Id')]
	#[RequestParameter(name: MutationFinder::MUTATION_ID_PARAMETER_NAME, type: 'int', in: EndpointParameter::IN_PATH, required: true, description: 'Mutation Id')]
	#[Response(description: 'Success', code: '200', entity: BlogLocalization::class)]
	#[Response(description: 'Not found', code: '404')]
	public function get(ApiRequest $request, ApiResponse $response): ApiResponse
	{
		$blogLocalization = $this->blogLocalizationRepository->getBy([
			'id' => $request->getParameter('id'),
			'mutation' => $request->getParameter(MutationFinder::MUTATION_PARAMETER_NAME),
		]);

		if ($blogLocalization !== null) {
			$responseEntity = new BlogLocalization($blogLocalization);
			return $this->jsonResponse($responseEntity->toResponse(), $response);
		} else {
			throw new ClientErrorException('Not found', ApiResponse::S404_NOT_FOUND);
		}
	}

}
