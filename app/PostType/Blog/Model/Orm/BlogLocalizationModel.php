<?php declare(strict_types = 1);

namespace App\PostType\Blog\Model\Orm;

use Jaybi<PERSON>\CrawlerDetect\CrawlerDetect;

class BlogLocalizationModel
{

	public function __construct(
		private readonly BlogLocalizationRepository $blogLocalizationRepository,
		private readonly BlogLocalizationTreeRepository $blogLocalizationTreeRepository,
	)
	{}


	public function increaseViews(BlogLocalization $blogLocalization): bool
	{
		$crawlerDetect = new CrawlerDetect();
		if ($crawlerDetect->isCrawler()) {
			return false;
		}

		$blogLocalization->viewsNumber++;
		$this->blogLocalizationRepository->persistAndFlush($blogLocalization);
		return true;
	}

	/**
	 * @param array<int, int> $treeIds
	 */
	public function setCategoriesByIds(BlogLocalization $blogLocalization, array $treeIds): void
	{
		$currentBlogLocalizationTrees = $this->blogLocalizationTreeRepository->findBy(['blogLocalization' => $blogLocalization])->fetchPairs('tree->id');
		$sort = 0;
		foreach ($treeIds as $treeId) {

			if (isset($currentBlogLocalizationTrees[$treeId])) {
				// update sort
				$blogLocalizationTree = $currentBlogLocalizationTrees[$treeId];
				assert($blogLocalizationTree instanceof BlogLocalizationTree);
				$blogLocalizationTree->sort = $sort;
				// unset array
				unset($currentBlogLocalizationTrees[$treeId]);
			} else {
				// create new
				$blogLocalizationTree = new BlogLocalizationTree();
				$blogLocalizationTree->blogLocalization = $blogLocalization;
				$blogLocalizationTree->tree = $treeId;
				$blogLocalizationTree->sort = $sort;
			}
			$sort++;
		}

		// remove old
		if ($currentBlogLocalizationTrees !== []) {
			foreach ($currentBlogLocalizationTrees as $currentBlogLocalizationTree) {
				$this->blogLocalizationTreeRepository->remove($currentBlogLocalizationTree);
			}
		}
	}

}
