<?php declare(strict_types = 1);

namespace App\PostType\Blog\Model\Orm;

use App\PostType\Page\Model\Orm\Tree;
use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property int $sort
 *
 * RELATIONS
 * @property BlogLocalization $blogLocalization {m:1 BlogLocalization::$blogLocalizationTrees}
 * @property Tree $tree {m:1 Tree::$blogLocalizationTrees}
 */
class BlogLocalizationTree extends Entity
{

}
