<?php declare(strict_types = 1);

namespace App\PostType\Blog\Model\Orm;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\JsonContainer; // phpcs:ignore
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\Author\Model\Orm\Author;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTag;
use App\PostType\Core\Model\ParentEntity;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Exception\NoResultException;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;
use App\Model\Orm\ImageEntity;

/**
 * @property int $id {primary}
 * @property string $internalName {default ''}
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 *
 * RELATIONS
 * @property OneHasMany<BlogLocalization> $localizations {1:M BlogLocalization::$blog}
 * @property ManyHasMany<Author> $authors {m:m Author::$blogs, isMain=true, orderBy=[internalName=ASC]}
 * @property ManyHasMany<Blog> $attachedBlogs {m:m Blog::$parentBlogs, orderBy=[internalName=ASC]}
 * @property ManyHasMany<Blog> $parentBlogs {m:m Blog::$attachedBlogs, isMain=true, orderBy=[internalName=ASC]}
 * @property ManyHasMany<BlogTag> $tags {m:m BlogTag::$blogs, isMain=true}
 *
 * VIRTUAL
 * @property ArrayHash|null $cf {virtual}
 */
class Blog extends BaseEntity implements ParentEntity
{

	use HasCustomFields;

	public function getInternalName(): string
	{
		return $this->internalName;
	}

	public function setInternalName(string $internalName): void
	{
		$this->internalName = $internalName;
	}

	/**
	 * @return ICollection<BlogLocalization>
	 */
	public function getLocalizations(): ICollection
	{
		return $this->localizations->toCollection();
	}

	/**
	 * @throws NoResultException
	 */
	public function getLocalization(Mutation $mutation): BlogLocalization
	{
		$localization = $this->getLocalizations()->getByChecked(['mutation' => $mutation]);
		assert($localization instanceof BlogLocalization);
		return $localization;
	}

	public function getFirstImage(): ImageEntity|null
	{
		return isset($this->cf->settings->mainImage) ? $this->cf->settings->mainImage->getEntity() : null;
	}

}
