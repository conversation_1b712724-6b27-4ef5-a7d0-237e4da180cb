<?php declare(strict_types = 1);

namespace App\PostType\Blog\Model\Orm;

use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Orm\Mapper\Dbal\Conventions\Conventions;
use Nextras\Orm\Mapper\Dbal\Conventions\IConventions;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<BlogLocalizationTree>
 */
class BlogLocalizationTreeMapper extends DbalMapper
{

	use HasCamelCase;

	/**
* @return literal-string
*/
public function getTableName(): string
{
	return 'blog_localization_tree';
}

	protected function createConventions(): IConventions
	{
		$conventions = parent::createConventions();
		assert($conventions instanceof Conventions);

		return $conventions;
	}

}
