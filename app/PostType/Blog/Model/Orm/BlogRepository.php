<?php declare(strict_types = 1);

namespace App\PostType\Blog\Model\Orm;

use App\Model\Orm\CollectionById;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method Blog getById($id)
 * @method ICollection<Blog> searchByName(string $q, array $excluded = [])
 * @method ICollection<Blog> findByExactOrder(array $ids)
 *
 * @extends Repository<Blog>
 */
final class BlogRepository extends Repository
{
	public static function getEntityClassNames(): array
	{
		return [Blog::class];
	}
}
