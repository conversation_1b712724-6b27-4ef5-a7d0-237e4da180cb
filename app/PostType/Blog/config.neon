parameters:
	config:
		blog:
			paging: 3 #temp low number - to testing

	postTypeRoutes:
		Blog: blog

cf:
	templates:
		blog:
			settings:
				type: group
				label: "Nastavení"
				items:
					mainImage:
						type: image # has size
						label: "<PERSON><PERSON><PERSON><PERSON><PERSON>k pro detail a roz<PERSON><PERSON>ník (min.1400x1400)"
		blogLocalization:
			settings:
				type: group
				label: "Nastavení"
				items:
					annotation:
						type: textarea
						label: "Anotace"

cc:
	templates:

application:
	mapping:
		Blog: App\PostType\Blog\*Module\Presenters\*Presenter

services:
	- App\PostType\Blog\Model\Orm\BlogLocalizationModel
	- App\PostType\Blog\AdminModule\Components\Form\BlogFormPrescription(coreFormPath: %appDir%/PostType/Core/AdminModule/Components/Form)
	- App\PostType\Blog\Model\BlogLocalizationFacade
	- App\PostType\Blog\AdminModule\Components\DataGrid\BlogDataGridPrescription
	- App\PostType\Blog\FrontModule\Components\BlogLocalizationStructuredData\BlogLocalizationStructuredDataFactory
	- App\PostType\Blog\FrontModule\Components\BlogList\BlogListFactory
	- App\PostType\Blog\FrontModule\Components\BlogList\BlogListCallbackBuilder

	-
		implement: App\PostType\Blog\FrontModule\Components\Attached\AttachedBlogsFactory
		inject: true
