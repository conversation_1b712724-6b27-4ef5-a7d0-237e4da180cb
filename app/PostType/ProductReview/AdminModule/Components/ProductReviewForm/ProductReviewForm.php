<?php declare(strict_types = 1);

namespace App\PostType\ProductReview\AdminModule\Components\ProductReviewForm;

use App\Model\Link\LinkFactory;
use App\Model\Orm\ProductReview\ProductReview;
use App\Model\Orm\ProductReview\ProductReviewModel;
use App\Model\Translator;
use App\PostType\Core\AdminModule\Components\Form\Form as CoreForm;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Throwable;

final class ProductReviewForm extends Control
{

	public function __construct(private readonly ProductReview $productReview, private readonly ProductReviewModel $productReviewModel, private readonly Translator $translator, private readonly LinkFactory $linkFactory)
	{
	}

	public function render(): void
	{
		$productLocalization = $this->productReview->product->getLocalization($this->productReview->product->getMutation());
		$mutation = $this->productReview->product->getMutation();
		$template = $this->getTemplate();
		$template->setTranslator($this->translator);
		$template->add('corePartsDirectory', CoreForm::TEMPLATE_PARTS_DIR);                   	// @phpstan-ignore-line
		$template->add('templates', RS_TEMPLATE_DIR);                                         	// @phpstan-ignore-line
		$template->add('showDeleteButton', false);                                            	// @phpstan-ignore-line
		$template->add('entity', $this->productReview);                                         	    // @phpstan-ignore-line
		$template->add('fileUploadLink', $this->presenter->link(':Admin:File:upload'));   	// @phpstan-ignore-line
		$template->add('mutation', $mutation);                    									// @phpstan-ignore-line
		$template->add('entityLocalization', $productLocalization);                    				// @phpstan-ignore-line
		$template->add('linksToFront', $this->linkFactory->linkTranslateToNette($productLocalization, ['productDetailReview' => true]));// @phpstan-ignore-line
		$this->getTemplate()->setFile(__DIR__ . '/templates/productReviewForm.latte');
		$this->getTemplate()->render();
	}

	public function createComponentProductReviewForm(): Form
	{
		$form = new Form();

		$form->setTranslator($this->translator);
		$form->setMappedType(ProductReviewFormData::class);

		$form->addText('name', 'name');
		$form->addTextArea('text', 'text');

		$form->addContainer('setup')
			->addHidden('cf');

		$form->addEmail('email', 'email');

		$form->addInteger('stars', 'stars')
			->addRule(Form::Min, $this->translator->translate('min_star_count'), 0)
			->addRule(Form::Max, $this->translator->translate('max_star_count'), 5)
			->addRule(Form::MaxLength, $this->translator->translate('max_star_length'), 1);

		 $form->addSelect('status', 'status', ProductReview::getConstsByPrefix('STATUS_', 'product_review_status_'));
		$form->addSelect('source', 'source', ProductReview::getConstsByPrefix('SOURCE_', 'product_review_source_'));

		$form->addSubmit('send', 'send');
		$form->addCheckbox('public', 'public');

		$form->setDefaults($this->productReview->toArray());

		$form->onSuccess[] = $this->formSuccess(...);
		$form->onError[] = $this->formError(...);

		return $form;
	}

	private function formError(Form $form): void
	{
		$this->flashMessage('Error', 'error');

		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	private function formSuccess(Form $form, ProductReviewFormData $productReviewFormData): void
	{
		try {
			$this->productReviewModel->editReview($this->productReview, $productReviewFormData);
		} catch (Throwable $exception) {
			$this->flashMessage($exception->getMessage(), 'error');
			$this->redirect('this');
		}

		$this->flashMessage($this->translator->translate('product_review_succesfully_updated'));
	}

}
