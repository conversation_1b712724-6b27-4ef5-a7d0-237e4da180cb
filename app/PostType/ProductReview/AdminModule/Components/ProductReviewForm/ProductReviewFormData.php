<?php declare(strict_types = 1);

namespace App\PostType\ProductReview\AdminModule\Components\ProductReviewForm;

use App\AdminModule\Presenters\Mutation\Components\Form\FormData\SetupFormData;

final class ProductReviewFormData
{

	public string|null $name;

	public string|null $email;

	public string|null $text;

	public int $stars;

	public string $source;

	public string $status;

	public bool $public;

	public SetupFormData $setup;

}
