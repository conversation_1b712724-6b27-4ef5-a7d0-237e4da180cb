<?php declare(strict_types = 1);

namespace App\PostType\ProductReview\AdminModule\Components\ProductReviewList;

use App\Model\Orm\ProductReview\ProductReview;
use App\Model\Orm\ProductReview\ProductReviewRepository;
use App\Model\Translator;
use Nette\Application\UI\Control;
use Nextras\Orm\Collection\ICollection;
use Ublaboo\DataGrid\DataGrid;

final class ProductReviewList extends Control
{

	public function __construct(private readonly ProductReviewRepository $productReviewRepository, private readonly Translator $translator)
	{
	}

	public function render(): void
	{
		$this->getTemplate()->setTranslator($this->translator);
		$this->getTemplate()->setFile(__DIR__ . '/templates/productReviewList.latte');
		$this->getTemplate()->render();
	}

	protected function createComponentProductReviewList(string $name): DataGrid
	{
		$dataGrid = new DataGrid();

		$dataGrid->setTranslator($this->translator);
		$dataGrid->setDataSource($this->productReviewRepository->findAll()->orderBy('date', ICollection::DESC));
		$dataGrid->setOuterFilterRendering();

		$dataGrid->addFilterDateRange('date', 'date_from', 'date', 'date_to');

		$dataGrid->addColumnNumber('id', 'id')
			->setDefaultHide();

		$dataGrid->addColumnText('product', 'product', 'product.internalName')
			->setFilterText();

		$dataGrid->addColumnText('name', 'product_review_label_name');
		$dataGrid->addColumnText('stars', 'product_review_label_stars');

		$dataGrid->addColumnText('status', 'product_review_label_status')
			->setRenderer(function (ProductReview $productReview): string {
				return $this->translator->translate('product_review_status_' . $productReview->status);
			})
			->setFilterSelect(ProductReview::getConstsByPrefix('STATUS_', 'product_review_status_'))
			->setTranslateOptions()
			->setPrompt($this->translator->translate('all'));

		$dataGrid->addColumnText('source', 'product_review_label_source')
			->setRenderer(function (ProductReview $productReview): string {
				return $this->translator->translate('product_review_source_' . $productReview->source);
			})
			->setFilterSelect(ProductReview::getConstsByPrefix('SOURCE_', 'product_review_source_'))
			->setTranslateOptions()
			->setPrompt($this->translator->translate('all'));

		$dataGrid->addColumnNumber('public', 'public')
			->setReplacement([true => $this->translator->translate('yes'), false => $this->translator->translate('no')])
			->setSortable()
			->setFilterSelect([null => 'all', true => 'yes', false => 'no'])
			->setTranslateOptions();

		$dataGrid->addAction('edit', 'Edit', ':edit')->setClass('btn btn-xs btn-primary');

		return $dataGrid;
	}

}
