<?php declare(strict_types = 1);

namespace App\PostType\ProductReview\AdminModule\Presenters;

use App\AdminModule\Presenters\BasePresenter;
use App\Model\Orm\ProductReview\ProductReview;
use App\PostType\ProductReview\AdminModule\Components\ProductReviewForm\ProductReviewForm;
use App\PostType\ProductReview\AdminModule\Components\ProductReviewForm\ProductReviewFormFactory;
use App\PostType\ProductReview\AdminModule\Components\ProductReviewList\ProductReviewList;
use App\PostType\ProductReview\AdminModule\Components\ProductReviewList\ProductReviewListFactory;
use Nette\DI\Attributes\Inject;

class ProductReviewPresenter extends BasePresenter
{

	private ProductReview $productReview;

	#[Inject]
	public ProductReviewListFactory $productReviewListFactory;

	#[Inject]
	public ProductReviewFormFactory $productReviewFormFactory;

	public function __construct()
	{
		parent::__construct();
	}

	public function renderDefault(): void
	{
	}

	public function actionEdit(int $id): void
	{
		/**
		 * @var ProductReview|null $productReview
		 */
		$productReview = $this->orm->productReview->getById($id);
		if ($productReview === null) {
			$this->redirect('default');
		}

		$this->productReview = $productReview;
	}

	public function renderEdit(int $id): void
	{
	}

	public function createComponentProductReviewList(): ProductReviewList
	{
		return $this->productReviewListFactory->create();
	}

	public function createComponentProductReviewForm(): ProductReviewForm
	{
		return $this->productReviewFormFactory->create($this->productReview);
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte');
	}

}
