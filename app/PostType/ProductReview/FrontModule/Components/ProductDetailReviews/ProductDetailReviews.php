<?php declare(strict_types = 1);

namespace App\PostType\ProductReview\FrontModule\Components\ProductDetailReviews;

use App\Components\VisualPaginator\FakePaginator;
use App\Components\VisualPaginator\VisualPaginatorFactory;
use App\Model\ConfigService;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductReview\ProductReview;
use App\Model\Orm\ProductReview\ProductReviewModel;
use App\Model\Orm\User\User;
use App\Model\TranslatorDB;
use App\PostType\ProductReview\FrontModule\Components\Review\Review;
use App\PostType\ProductReview\FrontModule\Components\Review\ReviewFactory;
use Nette\Application\UI;
use Nette\Application\UI\Multiplier;
use Nette\Application\UI\Template;
use Nette\Http\Request;
use Nette\Utils\Json;
use Nextras\Orm\Collection\ICollection;
use Throwable;

/**
 * @property-read Template $template
 */
class ProductDetailReviews extends UI\Control
{

	private array $ratedReviews = [];

	public function __construct(
		private readonly Product $product,
		private readonly Mutation $mutation,
		private readonly ?User $user,
		private readonly VisualPaginatorFactory $visualPaginatorFactory,
		private readonly ReviewFactory $reviewFactory,
		private readonly ConfigService $configService,
		private readonly Orm $orm,
		private readonly TranslatorDB $translator,
		private readonly ProductReviewModel $productReviewModel,
		private readonly Request $request,
	)
	{
		$ratedReviewsCookie = $this->request->getCookie(Review::COOKIE_NAME_LIKE);
		if (!empty($ratedReviewsCookie)) {
			try {
				$this->ratedReviews = Json::decode($ratedReviewsCookie, forceArrays: true);
			} catch (Throwable) {
//			try to set up rated reviews cookie
			}
		}
	}

	public function render(): void
	{
		if (!$this->getComponent('pager', false)) { // ?? ajax request -> Nette\InvalidStateException Component with name 'pager' already exists.
			$this->addComponent($this->visualPaginatorFactory->create(), 'pager');
		}

		/** @var FakePaginator $paginator */
		/** @noinspection PhpPossiblePolymorphicInvocationInspection */
		$paginator = $this['pager']->getPaginator();

		if (!empty($_GET['productDetailReviews-pager-page'])) {
			$paginator->setPage((int) $_GET['productDetailReviews-pager-page']);
		}

		$order = $this->getParameter('order');
		$reviews = $this->product->reviews->toCollection()->findBy([ // only texts
			//'isSimple' => false,
			'status' => ProductReview::STATUS_APPROVED,
		]);

		$reviews = match ($order) {
			'best' => $reviews->resetOrderBy()->orderBy('stars', ICollection::DESC)->orderBy('created', ICollection::DESC),
			'worst' => $reviews->resetOrderBy()->orderBy('stars')->orderBy('created', ICollection::DESC),
			default => $reviews->resetOrderBy()->orderBy('created', ICollection::DESC),
		};

		$totalReviews = $reviews->count();

		$paginator->setItemsOnFirstPage($this->configService->get('review', 'paging', 'productReviewDetailFirst'));
		$paginator->setItemsPerPage($this->configService->get('review', 'paging', 'productReviewDetailOther'));
		$paginator->setItemCount($totalReviews);

		$this->template->reviews = $reviews->limitBy($paginator->itemsPerPage, $paginator->getOffset());

		$this->template->totalReviews = $totalReviews;
		$this->template->product = $this->product;
		$this->template->paginator = $paginator;
		$this->template->mutation = $this->mutation;
		$this->template->pages = $this->mutation->pages;
		$this->template->ratedReviews = $this->ratedReviews;
		$this->template->isEdit = isset($this->user) && $this->productReviewModel->getReviewedProductByUser($this->product, $this->user, true) !== null;
		$this->template->templates = FE_TEMPLATE_DIR;
		$this->template->setTranslator($this->translator);
		$this->template->render(__DIR__ . '/productDetailReviews.latte');// @phpstan-ignore-line
	}

	/**
	 * @return Multiplier<Review>
	 */
	protected function createComponentMultiReview(): Multiplier
	{
		return new Multiplier(function ($id) {
			$productReview = $this->orm->productReview->getById($id);

			if ($productReview === null) {
				$productReview = new ProductReview(); // prazdna entita - jedna se jen o vytvoreni entity kvuli volani signalu v Review -> jiz neexistujici recenze
			}

			return $this->reviewFactory->create($productReview, $this->product, $this->mutation, $this->ratedReviews);
		});
	}

	public function handleMore(): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl('reviewsList');
			$this->redrawControl('reviewsListPagerMore');
		}
	}

	public function handleOrder(string|null $order = null): void
	{
		if ($this->presenter->isAjax()) {
			$this->redrawControl('productReviews');
			$this->redrawControl('reviewsSort');
			$this->redrawControl('reviewsList');
			return;
		}

		$this->presenter->redirect('this');
	}

}
