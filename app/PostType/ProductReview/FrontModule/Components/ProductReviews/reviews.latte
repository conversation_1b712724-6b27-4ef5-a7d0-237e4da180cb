{varType App\Model\Orm\ProductReview\ProductReview[] $reviews}
{default $class = false}

<div n:class="c-reviews, $class" data-controller="reviews">
	<div class="c-reviews__reviews">
		{snippet reviewsList}
			{foreach $reviews as $review}
				{var $isHide = $iterator->counter > $productReviewsPagingFirst}
				<div n:class="c-reviews__review, $isHide ? u-d-n"{if $isHide} data-reviews-target="hidden"{/if}>
					{control 'multiReview-' . $review->id}
				</div>
			{/foreach}
		{/snippet}
	</div>

	{* Tlačítko pro odkrytí *}
	<p n:if="$totalTextReviews > $productReviewsPagingFirst" class="c-reviews__more u-ta-c u-mb-0" data-reviews-target="showMoreBtn">
		<button type="button" class="btn btn--bd" data-action="click->reviews#loadMore">
			<span class="btn__text">
				<span class="btn__inner">
					{_"review_btn_more"}
					{('angle-down')|icon, 'btn__icon'}
				</span>
			</span>
		</button>
	</p>

	{* Tlačítko pro přechod na detail produktu s recenzemi *}
	{* <p class="c-reviews__more u-mb-0 u-d-n" n:if="$totalTextReviews > $minProductDetailReviews" data-reviews-target="showAllBtn">
		<a href="{plink $product, productDetailReview => true}" class="btn btn--shadow btn--arrow btn--sm">
			<span class="btn__text">
				{_'review_btn_show_all'}
			</span>
		</a>
	</p> *}
</div>
