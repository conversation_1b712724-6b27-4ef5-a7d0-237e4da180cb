<?php declare(strict_types = 1);

namespace App\PostType\ProductReview\FrontModule\Components\ProductReviews;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Product\Product;

interface ProductReviewsFactory
{

	public function create(Product $product, Mutation $mutation): ProductReviews;
//	public function create(Product $product, Mutation $mutation, ?User $user): ProductReviews; //TODO order

}
