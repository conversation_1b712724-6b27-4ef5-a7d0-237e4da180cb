<?php declare(strict_types = 1);

namespace App\PostType\ProductReview\FrontModule\Components\ProductReviews;

use App\Model\ConfigService;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductReview\ProductReview;
use App\Model\TranslatorDB;
use App\PostType\ProductReview\FrontModule\Components\Review\Review;
use App\PostType\ProductReview\FrontModule\Components\Review\ReviewFactory;
use Nette\Application\UI;
use Nette\Application\UI\Multiplier;
use Nette\Application\UI\Template;
use Nette\Http\Request;
use Nette\Utils\Json;
use Throwable;

/**
 * @property-read Template $template
 */
class ProductReviews extends UI\Control
{

	private array $ratedReviews = [];

	public function __construct(
		private readonly Product $product,
		private readonly Mutation $mutation,
		//private readonly ?User $user,
		private readonly ReviewFactory $reviewFactory,
		private readonly ConfigService $configService,
		private readonly Orm $orm,
		private readonly TranslatorDB $translator,
		//private readonly ProductReviewModel $productReviewModel,
		private readonly Request $request,
	)
	{
		$ratedReviewsCookie = $this->request->getCookie(Review::COOKIE_NAME_LIKE);
		if (!empty($ratedReviewsCookie)) {
			try {
				$this->ratedReviews = Json::decode($ratedReviewsCookie, forceArrays: true);
			} catch (Throwable) {
//				try to set up rated reviews cookie
			}
		}
	}

	public function render(): void
	{
		$productReviewsPagingFirst = $this->configService->get('review', 'paging', 'productReviewFirst');
		$productReviewsPagingOther = $this->configService->get('review', 'paging', 'productReviewOther');
		$productReviewsPagingTotal = $productReviewsPagingFirst + $productReviewsPagingOther;

		$reviews = $this->product->reviewsPublic; // all
		$totalReviews = $reviews->count();
		$isSomeWithText = false;
		$totalTextReviews = 0;

		if ($totalReviews > 0) {
			$reviewsText = $reviews->findBy([ // only texts
				//'isSimple' => false,
				'status' => ProductReview::STATUS_APPROVED,

			]);

			$totalTextReviews = $reviewsText->count();

			if ($totalTextReviews > 0) {
				$reviews = $reviewsText->limitBy($productReviewsPagingTotal);
				$isSomeWithText = true;
			}
		}

		$this->template->reviews = $reviews;
		$this->template->isSomeWithText = $isSomeWithText;
		$this->template->productReviewsPagingFirst = $productReviewsPagingFirst;
		$this->template->productReviewsPagingFirstOther = $productReviewsPagingOther;
		$this->template->productReviewsPagingTotal = $productReviewsPagingTotal;
		$this->template->totalReviews = $totalReviews;
		$this->template->totalTextReviews = $totalTextReviews;
		$this->template->minProductDetailReviews = $this->configService->get('review', 'paging', 'minProductDetailReviews');

		$this->template->product = $this->product;
		$this->template->mutation = $this->mutation;
		$this->template->pages = $this->mutation->pages;
		$this->template->ratedReviews = $this->ratedReviews;
//		$this->template->isEdit = isset($this->user) && $this->productReviewModel->getReviewedProductByUser($this->product, $this->user, true) !== null;
		$this->template->isEdit = false;
		$this->template->templates = FE_TEMPLATE_DIR;
		$this->template->setTranslator($this->translator);
		$this->template->render(__DIR__ . '/productReviews.latte');// @phpstan-ignore-line
	}

	/**
	 * @return Multiplier<Review>
	 */
	protected function createComponentMultiReview(): Multiplier
	{
		return new Multiplier(function ($id) {
			$productReview = $this->orm->productReview->getById($id);

			if ($productReview === null) {
				$productReview = new ProductReview(); // prazdna entita - jedna se jen o vytvoreni entity kvuli volani signalu v Review -> jiz neexistujici recenze
			}

			return $this->reviewFactory->create($productReview, $this->product, $this->mutation, $this->ratedReviews);
		});
	}

}
