<?php declare(strict_types = 1);

namespace App\PostType\ProductReview\FrontModule\Components\Review;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductReview\ProductReview;
use App\Model\Orm\ProductReview\ProductReviewModel;
use App\Model\TranslatorDB;
use Nette\Application\Attributes\CrossOrigin;
use Nette\Application\UI;
use Nette\Application\UI\Template;
use Nette\Http\Response;
use Nette\Utils\Json;

/**
 * @property-read Template $template
 */
class Review extends UI\Control
{

	public const COOKIE_NAME_LIKE = 'ratedReviews';

	public function __construct(
		private readonly ProductReview $review,
		private readonly Product $product,
		private readonly Mutation $mutation,
		private array $ratedReviews,
		private readonly TranslatorDB $translator,
		private readonly Response $response,
		private readonly ProductReviewModel $productReviewModel,
	)
	{
	}

	public function render(): void
	{
		$this->template->product = $this->product;
		$this->template->review = $this->review;
		$this->template->mutation = $this->mutation;
		$this->template->pages = $this->mutation->pages;
		$this->template->ratedReviews = $this->ratedReviews;
		$this->template->templates = FE_TEMPLATE_DIR;
		$this->template->setTranslator($this->translator);
		$this->template->render(__DIR__ . '/review.latte');// @phpstan-ignore-line
	}

	#[CrossOrigin]
	public function handleLike(): void
	{
		if (!isset($this->ratedReviews[$this->review->id])) {
			$this->productReviewModel->like($this->review);
			$this->setRatedCookie();
		}

		if ($this->presenter->isAjax()) {
			$this->redrawControl('productReviewsLike');
		} else {
			$this->presenter->redirect('this');
		}
	}

	#[CrossOrigin]
	public function handleDislike(): void
	{
		if (!isset($this->ratedReviews[$this->review->id])) {
			$this->productReviewModel->dislike($this->review);
			$this->setRatedCookie();
		}

		if ($this->presenter->isAjax()) {
			$this->redrawControl('productReviewsLike');
		} else {
			$this->presenter->redirect('this');
		}
	}

	private function setRatedCookie(): void
	{
		$this->ratedReviews[$this->review->id] = 1;
		$this->response->setCookie(self::COOKIE_NAME_LIKE, Json::encode($this->ratedReviews), null);
	}

}
