{varType App\Model\Orm\ProductReview\ProductReview $review}

<div class="b-review">
	<p class="b-review__date">
		{if $review->order}
			{$review->order->placedAt|date:'d.'} {_($review->order->placedAt|date:'F')} {$review->order->placedAt|date:'Y'}
			{* {_"review_user_bought"} *}
		{else}
			{$review->created|date:'d.'} {_($review->created|date:'F')} {$review->created|date:'Y'}
			{* {_"review_user_not_bought"} *}
		{/if}
	</p>
	<p class="b-review__rating">
		{include $templates.'/part/core/stars.latte', class=>'b-review__stars', stars=>$review->stars}
		{$review->name}
	</p>
	<p n:if="$review->text ?? false" class="b-review__content u-mb-0">
		{$review->text|texy|noescape}
	</p>
	{* <div class="b-review__content"{if $review->text ?? false} data-controller="clamped toggle-class"{/if}>
		{if $review->text ?? false}
			<div class="b-review__text u-mb-xxs" data-clamped-target="content">
				<p class="u-mb-0">
					{$review->text|texy|noescape}
				</p>
			</div>
			<p class="b-review__more u-mb-xxs u-d-n">
				<button type="button" class="as-link" data-clamped-target="btn" data-action="toggle-class#toggle">
					{_"show_more"}
				</button>
			</p>
		{/if}
	</div> *}
</div>

{var $reviewContent = $review->cf->content ?? false}
<ul n:ifset="$reviewContent->reviewRatingPositive" class="b-review__list b-review__list--positive">
	<li n:foreach="$reviewContent->reviewRatingPositive as $positiveItem">{$positiveItem->review}</li>
	<li n:foreach="$reviewContent->reviewRatingNegative as $negativeItem">{$negativeItem->review}</li>
</ul>
<span n:if="$review->isFromHeureka || $review->user" class="b-review__verified item-icon">
	{('verified')|icon, 'item-icon__icon'}
	<span class="item-icon__text">{_"verified_user"}</span>
</span>

{* <p class="b-review__helpful" n:snippet="productReviewsLike">
	{_"review_helpful"}
	<span class="b-review__links">
		{var $wasRated = isset($ratedReviews[$review->id])}
		<a n:tag-if="!$wasRated" n:href="like!" class="b-review__like item-icon" data-naja data-naja-loader="body" data-naja-history="off" rel="nofollow">
			<span n:tag-if="$wasRated" class="b-review__like item-icon">
				{('thumb-up')|icon:'item-icon__icon'}
				<span class="item-icon__text">
					{_"yes"}&nbsp;({$review->likes})
				</span>
			</span>
		</a>
		<a n:tag-if="!$wasRated" n:href="dislike!" class="b-review__dislike item-icon" data-naja data-naja-loader="body" data-naja-history="off" rel="nofollow">
			<span n:tag-if="$wasRated" class="b-review__dislike item-icon">
				{('thumb-down')|icon:'item-icon__icon'}
				<span class="item-icon__text">
					{_"no"}&nbsp;({$review->dislikes})
				</span>
			</span>
		</a>
	</span>
</p> *}
