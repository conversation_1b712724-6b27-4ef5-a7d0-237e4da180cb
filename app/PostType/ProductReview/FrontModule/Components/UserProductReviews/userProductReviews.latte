{varType App\Model\ProductVariant[]|Nextras\Orm\Collection\ICollection $products}
{varType SuperKoderi\FakePaginator $paginator}
{varType string $view}{* current tab *}
{varType bool $isViewToReview}{* shortcut -> $view === 'toreview' // true = produkty k hodnoceni *}

<div class="tabs tabs--bg">
	<div class="tabs__menu">
		{foreach $tabs as $tab}
			{var $isActive = $view === $tab}
			<a href="{plink $pages->userReview userView => $tab}" n:class="tabs__link, $isActive ? is-selected" data-naja="" data-naja-loader="body">
				{_'user_review_tab_' . $tab}
			</a>
		{/foreach}
	</div>
	<div class="tabs__content is-active">
			{if $totalProducts > 0}
				{snippet productsList}
					<div class="c-user-review">
						<ul class="c-user-review__list">
							{foreach $products as $variant}
								<li class="c-user-review__item">
									<span class="c-user-review__img img img--contain img--3-2">
									{if $variant->firstImage}
										{php $img = $variant->firstImage->getSize('sm')}
										<img src="{$img->src}" alt="" loading="lazy" width="{$img->width}" height="{$img->height}">
									{else}
										<img src="/static/img/illust/noimg.svg" alt="" loading="lazy" width="500" height="500">
									{/if}
									</span>
{*									<a href="{plink $variant}" class="c-user-review__name link-mask">*}
									<a href="#" class="c-user-review__name link-mask">
										{$variant->name}
									</a>
{*									<a href="{plink $pages->productReviewAdd, productId => $variant->product->id, userView => $view}" n:class="c-user-review__link, $isViewToReview ? btn"data-modal-8-12 data-modal='{"medium": "fetch"}' data-snippetid="snippet--productReviewAdd">*}
{*									{if $isViewToReview}*}
{*										<span class="btn__text">*}
{*											<span>*}
{*												{_'user_review_btn_add'}*}
{*											</span>*}
{*										</span>*}
{*									{else}*}
{*										{_'user_review_link_edit'}*}
{*									{/if}*}
{*									</a>*}
								</li>
							{/foreach}
						</ul>
					</div>
				{/snippet}

				{snippet productsPagerBottom}
					{default $ajaxPage = false}
					{control pager, [ajaxPage=>$ajaxPage, najaScroll=>'#snippet--userProductReviews', class=>'paging--2', background=>true]}
				{/snippet}
			{else}
				<p class="u-pt-sm">
					<span class="message message-warning">{if $isViewToReview}{_'user_review_no_products_to_review'}{else}{_'user_review_no_products_reviewed'}{/if}</span>
				</p>
			{/if}
	</div>
</div>
