<?php declare(strict_types = 1);

namespace App\PostType\ProductReview\FrontModule\Components\UserProductReviews;

use App\Components\VisualPaginator\FakePaginator;
use App\Components\VisualPaginator\VisualPaginatorFactory;
use App\Model\ConfigService;
use App\Model\Orm\Mutation\Mutation;
use App\Model\TranslatorDB;
use Nette\Application\UI;
use Nette\Application\UI\Template;
use Nextras\Orm\Collection\EmptyCollection;

/**
 * @property-read Template $template
 */
class UserProductReviews extends UI\Control
{

	private string $view = 'toreview';

	public function __construct(
		//private readonly User $user,
		private readonly Mutation $mutation,
		private readonly VisualPaginatorFactory $visualPaginatorFactory,
		private readonly ConfigService $configService,
		//private readonly Orm $orm,
		private readonly TranslatorDB $translator,
	)
	{
	}

	public function render(): void
	{
		if ($this->presenter->getParameter('userView') === 'reviewed') {
			$this->view = 'reviewed';
		}

		$this->template->tabs = ['toreview', 'reviewed'];
//		$isViewToReview = $this->view === 'toreview';
		$isViewToReview = false;

		if (!$this->getComponent('pager', false)) { // ?? ajax request -> Nette\InvalidStateException Component with name 'pager' already exists.
			$visualPaginator = $this->visualPaginatorFactory->create();
			$visualPaginator->setTranslator($this->translator);
			$this->addComponent($visualPaginator, 'pager');
		}

		/** @var FakePaginator $paginator */
		/** @noinspection PhpPossiblePolymorphicInvocationInspection */
		$paginator = $this['pager']->getPaginator();

		if (!empty($_GET['userProductReviews-pager-page'])) {
			$paginator->setPage((int) $_GET['userProductReviews-pager-page']);
		}

//		if ($isViewToReview) {
//			$variants = $this->orm->productVariant->getVariantIdsFromOrdersByUser($this->user, $this->mutation); //TODO order
//		} else {
//			$variants = $this->orm->productVariant->getVariantIdsReviewedByUser($this->user, $this->mutation);
//		}
		$variants = new EmptyCollection();

		$totalProducts = $variants->count();

		$paginator->setItemsPerPage($this->configService->get('review', 'paging', 'productReviewUser'));
		$paginator->setItemCount($totalProducts);

		$this->getTemplate()->products = $variants->limitBy($paginator->itemsPerPage, $paginator->offset);
		$this->getTemplate()->totalProducts = $totalProducts;
		$this->getTemplate()->paginator = $paginator;
		$this->getTemplate()->mutation = $this->mutation;
		$this->getTemplate()->view = $this->view;
		$this->getTemplate()->isViewToReview = $isViewToReview;
		$this->getTemplate()->pages = $this->mutation->pages;
		$this->getTemplate()->templates = FE_TEMPLATE_DIR;
		$this->getTemplate()->setTranslator($this->translator);
		$this->getTemplate()->render(__DIR__ . '/userProductReviews.latte');// @phpstan-ignore-line

		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

}
