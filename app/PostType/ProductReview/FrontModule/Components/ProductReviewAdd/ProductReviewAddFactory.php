<?php declare(strict_types = 1);

namespace App\PostType\ProductReview\FrontModule\Components\ProductReviewAdd;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductReview\ProductReview;
use App\Model\Orm\User\User;

interface ProductReviewAddFactory
{

	public function create(Product $product, Mutation $mutation, ?User $user = null, ?ProductReview $review = null, ?string $userView = null, Order|null $order = null): ProductReviewAdd;

}
