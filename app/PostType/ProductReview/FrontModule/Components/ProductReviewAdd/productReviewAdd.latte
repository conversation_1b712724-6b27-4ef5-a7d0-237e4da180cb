{varType App\Model\Product $product}
{varType App\Model\ProductReview $review}
{varType ?string $userView}{* null = std z produktu; toreview | reviewed = taby z uz. sekce *}

{snippet form}
	{if count($flashes) > 0 && $flashes[0]->type == 'ok'}
		<div class="f-review">
			<h1 class="h2 u-mb-xs item-icon">
				{('check')|icon, 'item-icon__icon'}
				<span class="item-icon__text">
					{if $review}{_'review_edited_ok_thank_you_title'}{else}{_'review_added_ok_thank_you_title'}{/if}
				</span>
			</h1>
			<p>
				{if $review}{_'review_edited_ok_thank_you_text'}{else}{_'review_added_ok_thank_you_text'}{/if}
			</p>
			<p n:if="$userView === null" class="u-mb-0">
				<a href="{plink $product}" class="btn">
					<span class="btn__text">
						{_"btn_back_to_eshop"}
					</span>
				</a>
			</p>
		</div>
	{else}
		<form
			n:name="form"
			method="post"
			class="f-review block-loader"
			novalidate="novalidate"
			enctype="multipart/form-data"
			data-controller="file-upload"
			data-action="submit->file-upload#submit file-input:addFile->file-upload#addFile file-input:removeFile->file-upload#removeFile"
		>
			<h1 class="h3">
				{if $review}
					{_'review_title_edit'}
				{else}
					{_'review_title_add'}
				{/if}
			</h1>

			{control messageForForm, $flashes, $form, TRUE}
			{include $FE_TEMPLATE_DIR . '/part/box/horizontal.latte', product=>$product}
			{include $FE_TEMPLATE_DIR . '/part/core/inp-stars.latte', name=>'stars', form=>$form}

			{* Pozitiva *}
			<div class="f-review__items f-review__items--positives" data-controller="add-review">
				<ul class="f-review__list" data-add-review-target="list">
					<li class="f-review__item">
						<button type="button" class="f-review__link f-review__link--add as-link" data-action="click->add-review#add">
							{_"review_add_positive"}
						</button>
					</li>

					{php $hasPositives = $review && isset($content->reviewRatingPositive) && is_array($content->reviewRatingPositive)}

					{* Vypsání existujících *}
					{if $hasPositives}
						{foreach $content->reviewRatingPositive as $reviewRatingPositive}
							<li class="f-review__item" data-add-review-target="item">
								<input type="text" name="positives[]" value="{$reviewRatingPositive->review}" class="f-review__inp inp-text" data-action="input->add-review#type paste->add-review#type">
								<button type="button" class="f-review__remove as-link" data-action="click->add-review#remove">
									{_"btn_remove"}
								</button>
							</li>
						{/foreach}
					{/if}

					{* Input pro přidání *}
					<li n:class="f-review__item, f-review__item--add, !$hasPositives ? u-d-n" data-add-review-target="addItem">
						<input type="text" id="positives[]" name="positives[]" class="f-review__inp inp-text" data-action="input->add-review#add paste->add-review#add" data-add-review-target="inp" placeholder="{_'review_form_add_positives'}">
						<label class="u-vhide" for="positives[]">
							{_'review_form_add_positives'}
						</label>
					</li>
				</ul>

				{* Template pro přidání položky *}
				<script type="text/template" class="embla-dot-template" data-add-review-target="template">
					<li class="f-review__item" data-add-review-target="item">
						<button type="button" class="f-review__remove as-link" data-action="click->add-review#remove">
							{_"btn_remove"}
						</button>
					</li>
				</script>
			</div>

			{* Negativa *}
			<div class="f-review__items f-review__items--negatives" data-controller="add-review">
				<ul class="f-review__list" data-add-review-target="list">
					<li class="f-review__item">
						<button type="button" class="f-review__link f-review__link--add as-link" data-action="click->add-review#add">
							{_"review_add_negative"}
						</button>
					</li>

					{php $hasNegatives = $review && isset($content->reviewRatingNegative) && is_array($content->reviewRatingNegative)}

					{* Vypsání existujících *}
					{if $hasNegatives}
						{foreach $content->reviewRatingNegative as $reviewRatingNegative}
							<li class="f-review__item" data-add-review-target="item">
								<input type="text" name="negatives[]" value="{$reviewRatingNegative->review}" class="f-review__inp inp-text" data-action="input->add-review#type paste->add-review#type">
								<button type="button" class="f-review__remove as-link" data-action="click->add-review#remove">
									{_"btn_remove"}
								</button>
							</li>
						{/foreach}
					{/if}

					{* Input pro přidání *}
					<li n:class="f-review__item, f-review__item--add, !$hasNegatives ? u-d-n" data-add-review-target="addItem">
						<input type="text" id="negatives[]" name="negatives[]" class="f-review__inp inp-text" data-action="input->add-review#add paste->add-review#add" data-add-review-target="inp" placeholder="{_'review_form_add_negatives'}">
						<label class="u-vhide" for="negatives[]">
							{_'review_form_add_negatives'}
						</label>
					</li>
				</ul>

				{* Template pro přidání položky *}
				<script type="text/template" class="embla-dot-template" data-add-review-target="template">
					<li class="f-review__item" data-add-review-target="item">
						<button type="button" class="f-review__remove as-link" data-action="click->add-review#remove">
							{_"btn_remove"}
						</button>
					</li>
				</script>
			</div>

			{include $FE_TEMPLATE_DIR . '/../Components/inp.latte', form=>$form, name=>text, cols=>6, rows=>5, placeholderLang=>'review_form_add_text_placeholder'}
			{include $FE_TEMPLATE_DIR . '/../Components/inp.latte', form=>$form, name=>name}
			{include $FE_TEMPLATE_DIR . '/../Components/inp.latte', form=>$form, name=>email}

			<p class="f-review__bottom">
				<span n:if="$pages->review_rules && $pages->personalData">
					{capture $linkPersonal}{plink $pages->personalData}{/capture}
					{capture $linkRules}{plink $pages->review_rules}{/capture}
					{* Souhlasíte se <a href="%linkPersonal">zpracováním osobních údajů</a>. Recenze musí splňovat <a href="%linkRules">naše podmínky</a> a zobrazí se až po jejím schválení. *}
					{_"review_agree"|replace: ["%linkPersonal" => (string)$linkPersonal, "%linkRules" => (string)$linkRules]|noescape}
				</span>

				<button n:name="save" class="f-review__btn btn">
					<span class="btn__text">
						{_"btn_send_review"}
					</span>
				</button>
			</p>

			<div class="block-loader__loader"></div>
		</form>
	{/if}
{/snippet}
