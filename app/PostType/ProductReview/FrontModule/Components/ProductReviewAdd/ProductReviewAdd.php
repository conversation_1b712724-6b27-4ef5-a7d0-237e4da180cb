<?php declare(strict_types = 1);

namespace App\PostType\ProductReview\FrontModule\Components\ProductReviewAdd;

//use App\Model\Order;
use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\Model\Orm\LibraryImage\LibraryImageModel;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductReview\ProductReview;
use App\Model\Orm\ProductReview\ProductReviewModel;
use App\Model\Orm\Traits\HasImageResizer;
use App\Model\Orm\User\User;
use App\Model\TranslatorDB;
use Nette\Application\UI;
use Nette\Application\UI\Form;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Throwable;
use Tracy\Debugger;

class ProductReviewAdd extends UI\Control
{

	use HasImageResizer;

	/**
	 * use hasMessageForFormComponentTrait; //TODO
	 */
	public function __construct(
		private readonly Product $product,
		private readonly Mutation $mutation,
		private readonly ?User $user,
		private readonly ?ProductReview $review,
		private readonly ?string $userView,
		private readonly ProductReviewModel $productReviewModel,
		private readonly LibraryImageModel $libraryImageModel,
		private readonly TranslatorDB $translator,
		private readonly MessageForFormFactory $messageForFormFactory,
		private readonly Order|null $order,
	)
	{
	}

	public function render(): void
	{
		$this->template->imageResizer = $this->libraryImageModel;
		$this->template->FE_TEMPLATE_DIR = FE_TEMPLATE_DIR;
		$this->template->product = $this->product;
		$this->template->review = $this->review;
		$this->template->content = $this->review->cf->content ?? null;

		$this->template->userView = $this->userView;
		$this->template->mutation = $this->mutation;
		$this->template->pages = $this->mutation->pages;

		$this->template->setTranslator($this->translator);
		$this->template->render(__DIR__ . '/productReviewAdd.latte');
	}

	protected function createComponentForm(): Form
	{
		$form = new Form();
		$form->setTranslator($this->translator);

		$form->addRadioList('stars', '', [
			1 => 'review_form_add_star_1',
			2 => 'review_form_add_star_2',
			3 => 'review_form_add_star_3',
			4 => 'review_form_add_star_4',
			5 => 'review_form_add_star_5',
		])->setRequired();

		$form->addTextarea('text', 'review_form_add_text');
		$form->addText('name', 'review_form_add_name');
		$form->addText('email', 'review_form_add_email');
		$form->addMultiUpload('images', '');
		$form->addHidden('public', true);
		$form->addSubmit('save', 'form_btn_save_review');

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onValidate[] = [$this, 'formValidate'];
		$form->onError[] = [$this, 'formError'];

		if (isset($this->review)) {
			$form['stars']->setDefaultValue($this->review->stars);
			$form['name']->setDefaultValue($this->review->name ?? '');
			$form['email']->setDefaultValue($this->review->user->email ?? '');
			$form['text']->setDefaultValue($this->review->text ?? '');
			$form['public']->setDefaultValue($this->review->public);

		} else {
			$name = $this->user->name ?? '';

			if ($name === '') {
				$name = $this->order->user->name ?? '';
			}

			$form['name']->setDefaultValue($name);
			$form['email']->setDefaultValue($this->user?->email);
			$form['stars']->setDefaultValue(5);
		}

		return $form;
	}

	public function formError(Form $form): void
	{
		bd($form->errors);
		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	public function formValidate(Form $form, ArrayHash $values): void
	{
		//only logged users can add reviews
		if ($this->user === null) {
			$this->presenter->flashMessage('user_not_logged_in');
			$this->presenter->redirect($this->mutation->pages->userLogin);

		}
	}

	public function formSucceeded(Form $form, ArrayHash $values): void
	{
		try {
//			if (isset($this->review) && $this->productReviewModel->hasLinkedParent($this->review) !== null) { //TODO heureka
//				throw new LogicException('Duplicate editing ID: ' . $this->review->id);
//			}

			$data = $form->getHttpData();

			assert(is_array($data));

			$pros = $this->processItems($data, 'positives');
			$cons = $this->processItems($data, 'negatives');

			if ($pros === null && $cons === null) {
				$values->isSimple = true;
				$cf = new ArrayHash();
			} else {
				$cf = ProductReviewModel::buildCustomFields($pros, $cons);
			}

			if (isset($this->review)) {
				$values->parent = $this->review;
				$values->created = $this->review->created;
				$values->createdUpdate = new DateTimeImmutable();
				$values->isFromHeureka = $this->review->isFromHeureka;
			}

			$source = match ($this->userView) { //TODO order
				'toreview', 'reviewed' => ProductReview::SOURCE_USER_SECTION,
//				'requestOrder' => ProductReview::SOURCE_REQUEST_ORDER,
				default => ProductReview::SOURCE_PRODUCT_DETAIL,
			};

			$values->source = $source;

			$user = $this->user ?? ($this->order->user ?? null);
			$this->productReviewModel->add($this->product, $this->mutation, $values, $cf, $user, $this->review, $this->order);

			$this->flashMessage('review_saved_ok', 'ok');
			$form->setValues([], true);

		} catch (Throwable $e) {
			Debugger::log($e);
			$this->flashMessage('msg_operation_failed', 'error');
		}

		if ($this->presenter->isAjax()) {
			$this->redrawControl();
			$this->presenter->redrawControl('userProductReviews');
		} else {
			$this->presenter->redirect('this');
		}
	}

	private function processItems(array $data, string $type): ?array
	{
		$result = [];

		if (isset($data[$type])) {
			foreach ($data[$type] as $item) {
				if (!empty($item)) {
					$result[] = $item;
				}
			}
		}

		return count($result) > 0 ? $result : null;
	}

	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

}
