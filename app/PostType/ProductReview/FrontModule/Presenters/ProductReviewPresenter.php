<?php declare(strict_types = 1);

namespace App\PostType\ProductReview\FrontModule\Presenters;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\FrontModule\Components\AddToCart\AddToCart;
use App\FrontModule\Components\AddToCart\AddToCartFactory;
use App\FrontModule\Presenters\BasePresenter;
use App\Infrastructure\Latte\Filters;
use App\Model\Currency\CurrencyHelper;
use App\Model\Orm\Order\OrderRepository;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductModel;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\ProductReview\ProductReview;
use App\Model\Orm\ProductReview\ProductReviewModel;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\ProductReview\FrontModule\Components\ProductDetailReviews\ProductDetailReviews;
use App\PostType\ProductReview\FrontModule\Components\ProductDetailReviews\ProductDetailReviewsFactory;
use App\PostType\ProductReview\FrontModule\Components\ProductReviewAdd\ProductReviewAdd;
use App\PostType\ProductReview\FrontModule\Components\ProductReviewAdd\ProductReviewAddFactory;
use App\PostType\ProductReview\FrontModule\Components\ProductReviews\ProductReviews;
use App\PostType\ProductReview\FrontModule\Components\ProductReviews\ProductReviewsFactory;
use App\PostType\ProductReview\FrontModule\Components\UserProductReviews\UserProductReviewsFactory;
use Nette\DI\Attributes\Inject;

class ProductReviewPresenter extends BasePresenter
{

	#[Inject]
	public UserProductReviewsFactory $userProductReviewsFactory;

	#[Inject]
	public ProductReviewsFactory $productReviewsFactory;

	#[Inject]
	public ProductReviewAddFactory $productReviewAddFactory;

	#[Inject]
	public ProductReviewModel $productReviewModel;

	#[Inject]
	public MessageForFormFactory $messageForFormFactory;

	#[Inject]
	public ProductDetailReviewsFactory $productDetailReviewsFactory;

	#[Inject]
	public OrderRepository $orderRepository;

	#[Inject]
	public ProductModel $productModel;

	#[Inject]
	public AddToCartFactory $addToCartFactory;

	protected Product|null $product;

	protected ProductReview|null $productReview;

	private ProductVariant $variant;

	public function actionReview(ProductLocalization $object): void
	{
		$this->setObject($object);
		$this->product = $object->product;
		$this->variant = $object->product->firstVariant;
	}

	public function actionDefault(CommonTree $object, int|null $productId = null, string $userView = '', int|null $reviewId = null): void
	{
		$this->setObject($object);

		match ($object->uid) {
			'productReviewAdd' => $this->actionProductReviewAdd($productId, $userView, $reviewId),
			default => null
		};
	}

	public function actionProductReviewAdd(int|null $productId = null, string $userView = '', int|null $reviewId = null): void
	{
		$this->product = isset($productId) ? $this->orm->product->getById($productId) : null;

		if (!empty($userView) && !in_array($userView, ['toreview', 'reviewed', 'src'])) {
			$this->error('Unknown view: ' . $userView);
		}

		if (!isset($this->product)) {
			$this->error('Unknown product: ' . $productId);
		}

		$this->template->product = $this->product;
		$this->template->object = $this->object;
		$this->template->userView = $userView;

		if ($reviewId !== null) {
			$this->productReview = $this->orm->productReview->getById($reviewId);
		} else {
			$this->productReview = $this->user->isLoggedIn() && isset($this->userEntity) ? $this->productReviewModel->getReviewedProductByUser($this->product, $this->userEntity) : null;
		}

		$this->template->review = $this->productReview;

		if ($this->isAjax()) {
			$this->redrawControl('productReviewAdd');

			if (isset($this->template->userView)) {
				$this->redrawControl('userProductReviews');
			}
		}
	}

	public function actionDetail(ProductLocalization $object): void
	{
		$this->setObject($object);
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		if ($this->getObject() instanceof ProductLocalization) {
			$this->getTemplate()->variant = $this->variant;
			$this->getTemplate()->product = $this->product;
			$this->getTemplate()->productEntity = $this->product;
			$this->getTemplate()->productDto = $this->productDtoProvider->get($this->product, $this->variant);
			$this->getTemplate()->parametersMain = $this->productModel->filterOutMainParameters($this->getObject()->product);
			$this->getTemplate()->textMore = $this->product->mainCategory->cf->categoryOtherSettings->moreAboutBook ?? null;
			$this->getTemplate()->transitFreeLowestLPrice = $this->orm->deliveryMethod->getTransitFreeFromLevel($this->mutation, $this->currentState, $this->priceLevel, CurrencyHelper::getCurrency());
			$this->getTemplate()->currencySymbol = Filters::formatCurrency(CurrencyHelper::getCurrency());

			$this->getTemplate()->setFile(__DIR__ . '/../templates/orderReview.latte');
			return;
		}

		match ($this->getObject()->uid ?? null) {
			'productReviewAdd' => $this->getTemplate()->setFile(__DIR__ . '/../templates/productReviewAdd.latte'),
			default => $this->getTemplate()->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte')
		};
	}

	protected function createComponentProductReviewAdd(): ProductReviewAdd
	{
		$this->checkUserEntity();
		return $this->productReviewAddFactory->create($this->product, $this->mutation, $this->userEntity, $this->productReview, $this->template->userView, $this->orderRepository->getByUsersProduct($this->product, $this->user));
	}

	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

	protected function createComponentProductReviews(string $name): ProductReviews
	{
		return $this->productReviewsFactory->create($this->getObject()->product ?? null, $this->mutation);
//		return $this->productReviewsFactory->create($this->getObject()->product ?? null, $this->mutation, $this->userEntity); //TODO order
	}

	protected function createComponentProductDetailReviews(): ProductDetailReviews
	{
		return $this->productDetailReviewsFactory->create($this->getObject()->product ?? null, $this->mutation, $this->userEntity);
	}



	private function checkUserEntity(): void
	{
		if ($this->user->isLoggedIn() && !isset($this->userEntity)) {
			$userEntity = $this->orm->user->getById($this->user->id);
			if ($userEntity && $userEntity->id) {
				$this->userEntity = $userEntity;
			} else {
				$this->error('Unknown logged user ID: ' . $this->user->id);
			}
		}
	}

	protected function createComponentAddToCart(): AddToCart
	{
		$productDto = $this->productDtoProvider->get($this->product, $this->variant);

		return $this->addToCartFactory->create(
			product: $this->product,
			productDto: $productDto,
			currentState: $this->currentState,
			priceLevel: $this->priceLevel,
			productVariant: $this->variant,
			type: AddToCart::TYPE_DETAIL,
		);
	}

}
