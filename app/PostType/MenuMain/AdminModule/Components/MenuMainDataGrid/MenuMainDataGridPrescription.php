<?php declare(strict_types = 1);

namespace App\PostType\MenuMain\AdminModule\Components\MenuMainDataGrid;

use App\PostType\Core\AdminModule\Components\DataGrid\Definition\DataGridDefinition;
use App\PostType\Core\AdminModule\Components\DataGrid\Definition\Extenders\CustomDataGridExtender;
use App\PostType\MenuMain\Model\Orm\MenuMainLocalization\MenuMainLocalization;
use Nextras\Orm\Collection\ICollection;
use Ublaboo\DataGrid\DataGrid;

readonly class MenuMainDataGridPrescription
{

	public function get(): DataGridDefinition
	{
		$datagrid = new DataGrid();
		$datagrid->setOuterFilterRendering();

		return new DataGridDefinition(
			dataGrid: $datagrid,
			extenders: [
				new CustomDataGridExtender(
					improveFunction: $this->addOrder(...)
				),
			],
		);
	}

	private function addOrder(DataGrid $dataGrid): void
	{
		$dataGrid->addColumnText('menuMain->order', 'order')
			->setRenderer(function (MenuMainLocalization $menuMainLocalization) {
				return $menuMainLocalization->menuMain->order;
			})
			->setSortableCallback(function (ICollection $collection, array $sort) {
				$collection->getQueryBuilder()// @phpstan-ignore-line
					->orderBy('[menuMain.order] IS NULL ASC, [menuMain.order] ' . $sort['menuMain->order']);
			})
			->setSortable();
	}

}
