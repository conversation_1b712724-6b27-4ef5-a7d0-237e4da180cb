application:
	mapping:
		MenuMain: App\PostType\MenuMain\*Module\Presenters\*Presenter

parameters:
	postTypeRoutes:
		MenuMain: menu-main

cf:
	definitions:
		customLink:
			hasContentToggle: true
			type: group
			label: "Odkaz"
			items:
				toggle:
					type: radio
					inline: true
					isContentToggle: true
					defaultValue: 'systemHref'
					options: [
						{ label: "Systémová stránka", value: "systemHref" },
						{ label: "Vlastní odkaz", value: "customHref" },
					]
				systemHref:
					type: group
					items:
						page:
							type: suggest
							label: "Stránka"
							subType: tree
							url: @cf.suggestUrls.searchMutationPage
				customHref:
					type: group
					items:
						href:
							type: text
							label: "Odkaz"
	templates:
		menuMain:
			link:
				type: group
				label: 'Odkaz'
				extends: @cf.definitions.customLink

			settings:
				type: group
				label: "Nastavení položky"
				items:
					icon:
						type: image
						label: "<PERSON><PERSON><PERSON> (pouze v mobilním menu)"
					color:
						type: select
						label: "Barva textu (pouze v mobilním menu)"
						options: [
							{ label: "Modrá", value: "u-c-primary" },
							{ label: "Červená", value: "u-c-red" },
						]

			submenu:
				type: group
				label: "Submenu"
				items:
					side:
						type: group
						label: "Odkazy v postranním panelu submenu"
						items:
							title:
								type: text
								label: "Nadpis"
							items:
								type: list
								label: "Odkazy"
								items:
									link:
										extends: @cf.definitions.linkChoose
					categories:
						extends: @cf.definitions.categories
						label: "Kategorie"
services:
	- App\PostType\MenuMain\Model\MenuMainLocalizationFacade
	- App\PostType\MenuMain\AdminModule\Components\MenuMainDataGrid\MenuMainDataGridPrescription
	- App\PostType\MenuMain\AdminModule\Components\MenuMainDetailForm\MenuMainDetailFormPrescription
	- App\PostType\MenuMain\Model\Orm\MenuMainLocalization\MenuMainLocalizationModel
