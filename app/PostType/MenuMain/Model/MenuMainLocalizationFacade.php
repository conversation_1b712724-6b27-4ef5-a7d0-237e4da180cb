<?php declare(strict_types = 1);

namespace App\PostType\MenuMain\Model;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\PostType\Core\Model\EntityLocalizationFacade;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\MenuMain\Model\Orm\MenuMain\HasMenuMainRepository;
use App\PostType\MenuMain\Model\Orm\MenuMain\MenuMain;
use App\PostType\MenuMain\Model\Orm\MenuMainLocalization\MenuMainLocalization;

class MenuMainLocalizationFacade implements EntityLocalizationFacade
{

	use HasMenuMainRepository;

	public function __construct(private readonly Orm $orm)
	{
	}

	public function create(Mutation $mutation, ?ParentEntity $localizableEntity): LocalizationEntity
	{
		$localization = new MenuMainLocalization();

		$this->orm->menuMainLocalization->attach($localization);

		$localization->mutation = $mutation;

		if ($localizableEntity === null) {
			$localizableEntity = new MenuMain();
			$localization->menuMain = $localizableEntity;
		} else {
			assert($localizableEntity instanceof MenuMain);
			$localization->menuMain = $localizableEntity;
		}

		$this->orm->persistAndFlush($localization);

		return $localization;
	}

	public function remove(LocalizationEntity $localizableEntity): void
	{
		// TODO: Implement remove() method.
	}

}
