<?php declare(strict_types = 1);

namespace App\PostType\MenuMain\Model\Orm\MenuMain;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\MenuMain\Model\Orm\MenuMainLocalization\MenuMainLocalization;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Exception\NoResultException;
use Nextras\Orm\Relationships\OneHasMany;
use App\Model\Orm\JsonContainer;// phpcs:ignore

/**
 * @property int                               $id                        {primary}
 * @property string                            $internalName              {default ''}
 * @property int|null                          $order                     {default null}
 * @property ArrayHash                         $customFieldsJson          {container JsonContainer}
 *
 * RELATIONS
 * @property OneHasMany<MenuMainLocalization> $localizations             {1:M MenuMainLocalization::$menuMain}
 *
 * VIRTUAL
 * @property ArrayHash|null                    $cf                        {virtual}
 */
class MenuMain extends BaseEntity implements ParentEntity
{

	use HasCustomFields;

	public function getInternalName(): string
	{
		return $this->internalName;
	}

	public function setInternalName(string $internalName): void
	{
		$this->internalName = $internalName;
	}

	/**
	 * @return ICollection<MenuMainLocalization>
	 */
	public function getLocalizations(): ICollection
	{
		return $this->localizations->toCollection();
	}

	/**
	 * @throws NoResultException
	 */
	public function getLocalization(Mutation $mutation): MenuMainLocalization
	{
		$localization = $this->getLocalizations()->getByChecked(['mutation' => $mutation]);
		assert($localization instanceof MenuMainLocalization);
		return $localization;
	}

}
