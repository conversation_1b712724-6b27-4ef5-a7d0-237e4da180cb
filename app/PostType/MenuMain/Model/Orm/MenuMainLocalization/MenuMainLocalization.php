<?php declare(strict_types = 1);

namespace App\PostType\MenuMain\Model\Orm\MenuMainLocalization;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\HasImages;
use App\Model\Orm\ImageEntity;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\RoutableEntity;
use App\Model\Orm\Traits\HasConfigService;
use App\Model\Orm\Traits\HasCustomContent;
use App\Model\Orm\Traits\HasCustomFields;
use App\Model\Orm\Traits\HasTemplateCache;
use App\PostType\Core\Model\Editable;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Core\Model\Publishable;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Page\Model\Orm\TreeRepository;
use App\PostType\MenuMain\Model\Orm\MenuMain\MenuMain;
use App\Model\Orm\JsonContainer;// phpcs:ignore
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;

/**
 * @property int                    $id                                                {primary}
 * @property string                 $name                                              {default ''}
 * @property bool                   $public                                            {default false}
 * @property DateTimeImmutable|null $editedTime
 * @property int|null               $edited
 * @property bool                   $isBig                                             {default false}
 *
 * @property ArrayHash              $customFieldsJson                                  {container JsonContainer}
 * @property ArrayHash              $customContentJson                                 {container JsonContainer}
 *
 * RELATIONS
 * @property Mutation               $mutation                                          {m:1 Mutation, oneSided=true}
 * @property MenuMain               $menuMain                                          {M:1 MenuMain::$localizations}
 *
 * VIRTUAL
 * @property ArrayHash|null         $cf                                                {virtual}
 * @property ArrayHash|null         $cc                                                {virtual}
 * @property-read string            $template                                          {virtual}
 */
class MenuMainLocalization extends BaseEntity implements LocalizationEntity, Publishable, HasImages, Editable
{

	use HasCustomFields, HasTemplateCache {
		HasCustomFields::onAfterPersist as private hasCustomFieldsOnAfterPersist;
		HasTemplateCache::onAfterPersist as private hasTemplateCacheOnAfterPersist;
	}
	use HasCustomContent;
	use HasConfigService;

	private TreeRepository $treeRepository;

	public function injectRepository(TreeRepository $treeRepository): void
	{
		$this->treeRepository = $treeRepository;
	}

	protected function getterTemplate(): string
	{
		return ':MenuMain:Front:MenuMain:detail';
	}

	protected function getterPath(): array
	{
		if (!isset($this->cache['path'])) {

			$this->cache['path'] = $this->createPath();
		}

		return $this->cache['path'];
	}

	private function createPath(): array
	{
		$path = [];

		if ($rootId = $this->mutation->getRealRootId()) {
			$path[] = $rootId;
		}

		if ($treeMenuMain = $this->treeRepository->getByUid(Tree::UID_MENU_MAIN, $this->mutation)) {
			$path[] = $treeMenuMain->getPersistedId();
		}

		return $path;
	}

	public function getId(): int
	{
		return $this->id;
	}

	public function getMutation(): Mutation
	{
		return $this->mutation;
	}

	public function setMutation(Mutation $mutation): void
	{
		$this->mutation = $mutation;
	}

	public function getParent(): MenuMain
	{
		return $this->menuMain;
	}

	public function setParent(ParentEntity $parentEntity): void
	{
		assert($parentEntity instanceof MenuMain);
		$this->menuMain = $parentEntity;
	}

	public function getIsPublic(): bool
	{
		return $this->public;
	}

	public function setIsPublic(bool $isPublic): void
	{
		$this->public = $isPublic;
	}

	public function getName(): string
	{
		return $this->name;
	}

	public function setName(string $name): void
	{
		$this->name = $name;
	}

	public function getFirstImage(): ImageEntity|null
	{
		return isset($this->getParent()->cf->base->mainImage) ? $this->getParent()->cf->base->mainImage->getEntity() : null;
	}

	public function setEditorId(int $id): void
	{
		$this->edited = $id;
	}

	public function setEditedTime(DateTimeImmutable $editedTime): void
	{
		$this->editedTime = $editedTime;
	}

	public function getPage(): ?RoutableEntity
	{
		return $this->menuMain->cf->link->systemHref->page->entity ?? null;
	}

	public function onAfterPersist(): void
	{
		$this->hasCustomFieldsOnAfterPersist();
		$this->hasTemplateCacheOnAfterPersist();
	}

	public function getTemplateCacheTagsCascade(): array
	{
		return [Tree::class, CommonTree::class, CatalogTree::class];
	}

}
