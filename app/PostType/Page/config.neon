parameters:

	postTypeRoutes:
		Page: page

application:
	mapping:
		Page: App\PostType\Page\*Module\Presenters\*Presenter

services:
	- App\PostType\Page\AdminModule\Components\Form\FormFactory
	- App\PostType\Page\AdminModule\Components\Form\Builder
	- App\PostType\Page\AdminModule\Components\Form\Handler
	- App\PostType\Page\Model\Orm\TreeParentModel



cf:
	fields:
		feeds:
			type: group
			label: "Nastavení kategorií feedů"
			items:
				zbozi:
					type: suggest
					label: "Zboží"
					url: @cf.suggestUrls.searchAltCategoryZbozi
					subType: treeAlternative
				heureka:
					type: suggest
					label: "Heureka"
					url: @cf.suggestUrls.searchAltCategoryHeureka
					subType: treeAlternative
				google:
					type: suggest
					label: "Google"
					url: @cf.suggestUrls.searchAltCategoryGoogle
					subType: treeAlternative
		postTypeOtherSettings:
			type: group
			label: "Ostatní nastavení"
			items:
				topProducts:
					type: text
					label: "Počet počítaných TOP produktů"
					defaultValue: 4
		categoryOtherSettings:
			type: group
			label: "Ostatní nastavení"
			items:
				topProducts:
					type: text
					label: "Počet počítaných TOP produktů"
					defaultValue: 4
				moreAboutBook:
					type: text
					label: 'Textace odkazu “Více o knize”'
				whiteBackground:
					type: checkbox
					label: 'Vynucení bílého pozadí hlavní fotky v detailu produktu'
				referalCategory:
					type: suggest
					subType: tree
					placeholder: 'Jméno odkazované kategorie'
					url: @cf.suggestUrls.searchMutationCategory
				#popularChoices:
				#	type: list
				#	label: "Populární výběry"
				#	items:
				#		name:
				#			type: text
				#			label: "Název"
				#		url:
				#			type: text
				#			label: "Cílová URL"

		categoryParametersForProductDetail:
			type: group
			label: Nastavení parametrů
			items:
				visible:
					type: checkbox
					label: "Skrýt parametry na stránce produktu"
				visibleParameters:
					type: list
					label: Parametry na stránce produktu
					items:
						parameter:
							type: suggest
							subType: parameter
							placeholder: Jméno parametru
							url: @cf.suggestUrls.searchParameterForDetail
		parameterForFilter:
			type: group
			label: "Nastavení filtrů"
			items:
				specialFilters:
					type: group
					label: "Speciálni filtry"
					items:
						price:
							type: group
							label: "Cena"
							items:
								sort:
									type: text
									label: 'Pořadí'
								opened:
									type: checkbox
									label: 'Rozbalené ve výchozim stavu'
								hide:
									type: checkbox
									label: 'Schovat'

							order: 1
						showOnly:
							type: group
							label: "Zobrazit pouze"
							items:
								sort:
									type: text
									label: 'Pořadí'
								opened:
									type: checkbox
									label: 'Rozbalené ve výchozim stavu'
								hide:
									type: checkbox
									label: 'Schovat'
							order: 2

				visibleFilters:
					type: text
					label: 'Počet rozbalených filtrů ve výchozím stavu'
				visibleParameters:
					type: list
					label: Parametry
					items:
						indexable:
							type: checkbox
							label: "Indexovatelné"
						visibleCount:
							type: text
							label: "Počet viditelných hodnot"
						parameter:
							type: suggest
							subType: parameter
							placeholder: Jméno parametru
							url: @cf.suggestUrls.searchParameterForFilter
						tooltip:
							type: tinymce
							label: "Tooltip"
						numberAsRange:
							type: checkbox
							label: "Rozsah pro číselné hodnoty"
						link:
							type: suggest
							subType: tree
							label: 'Odkaz pro zobrazení všech'
							url: @cf.suggestUrls.searchMutationPage
						opened:
							type: checkbox
							label: 'Rozbalené ve výchozim stavu'

		sortCategoryDescription:
			type: "group"
			label: "Spodní formátovaný text (popis kategorie)"
			items:
				contentTop:
					label: "Nejoblíbenější"
					type: "tinymce"
				contentNewest:
					label: "Nejnovější"
					type: "tinymce"
				contentOldest:
					label: "Nejstarší"
					type: "tinymce"
				contentCheapest:
					label: "Nejlevnější"
					type: "tinymce"
				contentExpensive:
					label: "Nejdražší"
					type: "tinymce"
				contentName:
					label: "A-Z"
					type: tinymce
				contentDiscount:
					label: 'Dle výše slevy'
					type: tinymce

		product_bnrs:
			type: list
			label: "Bannery v pravém sloupci"
			items:
				weight:
					label: Váha
					type: text
					subType: number
				link:
					hasContentToggle: true
					type: group
					label: "Odkaz"
					items:
						toggle:
							type: radio
							inline: true
							isContentToggle: true
							defaultValue: 'systemHref'
							options: [
								{ label: "Systémová stránka", value: "systemHref" },
								{ label: "Vlastní odkaz", value: "customHref" },
							]
						systemHref:
							type: group
							items:
								page:
									type: suggest
									label: "Stránka"
									subType: tree
									url: @cf.suggestUrls.searchMutationPage
						customHref:
							type: group
							items:
								href:
									type: text
									label: "Odkaz"
				images:
					type: group
					label: "Fotografie"
					items:
						desktop:
							type: image # has size
							label: "Desktop a Tablet (753x125)"
						mobile:
							type: image # has size
							label: "Mobile (volitelné, 455x96)"

		catalog_header:
			extends: @cf.definitions.header
			label: "Nastavení záhlaví"

		settings:
			type: group
			label: "Nastavení ve výpisu kategorií"
			items:
				# image:
				# 	type: image # has size
				# 	label: "Obrázek (nepovinné, přebírá se ze stránky, min. 126x96)"
				highlight:
					type: select
					label: "Zvýraznění"
					defaultValue: false
					options: [
						{ label: "Žlutá", value: "u-bgc-alert-light" },
						{ label: "Šedá", value: 'u-bgc-default' }
						{ label: "Přechod", value: "u-bgc-violet-gradient" },
					]

	suggestUrls:
		searchPageSibling:
			searchParameterName: search
			link: "/superadmin/search2/page-sibling"
			params: []
		searchMutationCategory:
			searchParameterName: search
			link: "/superadmin/search2/page-in-mutation"
			params: ['templates' : ':Front:Catalog:default']
		searchAltCategoryGoogle:
			searchParameterName: search
			link: "/superadmin/search2/alt-category"
			params: [type: App\PostType\Page\Model\Orm\TreeAlternative::TYPE_GOOGLE]
		searchAltCategoryZbozi:
			searchParameterName: search
			link: "/superadmin/search2/alt-category"
			params: [type: App\PostType\Page\Model\Orm\TreeAlternative::TYPE_ZBOZI]
		searchAltCategoryHeureka:
			searchParameterName: search
			link: "/superadmin/search2/alt-category"
			params: [type: App\PostType\Page\Model\Orm\TreeAlternative::TYPE_HEUREKA]
	templates:
		:Front:Catalog:default: [@cf.feeds, @cf.parameterForFilter, @cf.catalog_header, @cf.settings] # @cf.base, @cf.categoryParametersForProductDetail, @cf.sortCategoryDescription, @cf.categoryOtherSettings, @cf.product_bnrs, @cf.categoryTagCounts
		uid-search: [@cf.parameterForFilter]
