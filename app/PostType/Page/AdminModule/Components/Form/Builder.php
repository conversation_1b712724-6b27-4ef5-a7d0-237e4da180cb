<?php declare(strict_types = 1);

namespace App\PostType\Page\AdminModule\Components\Form;

use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Mutation\MutationRepository;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\Tree;
use App\Model\StringHelper;
use App\Model\Translator;
use Nette\Application\UI\Form;

final class Builder
{

	public function __construct(
		private readonly Translator $translator,
		private readonly MutationsHolder $mutationsHolder,
		private readonly MutationRepository $mutationRepository,
	)
	{
	}

	public function build(\App\Model\Security\User $user, Form $form, Tree $tree, array $postData): void
	{
		$form->setTranslator($this->translator);

		$this->addTreeParent($form, $tree, $postData);

		$form->addHidden('id', $tree->id);
		$lastEdited = isset($tree->id) && $tree->editedTime ? $tree->editedTime->getTimestamp() : 0;
		$form->addHidden('lastEdited', $lastEdited);


		$form->addText('name', 'name')->setDefaultValue($tree->name);
		//$form->addText('nameShort', 'name_short')->setDefaultValue($tree->nameShort);
		$form->addText('nameHeading', 'name_heading')->setDefaultValue($tree->nameHeading);
		// Main
		// $form->addTextArea('annotation', 'annotation', 50, 4)
		// 	->setDefaultValue($tree->annotation);
		$form->addTextArea('annotation', 'annotation', 50, 15)
			->setDefaultValue($tree->annotation)
			->addFilter(StringHelper::removeTinyMceEmptyP(...));
		// $form->addTextArea('content', 'content', 50, 15)
		// 	->setRequired(false)
		// 	->setDefaultValue($tree->content)
		// 	->addFilter(StringHelper::removeTinyMceEmptyP(...));


		if ($user->isDeveloper()) {
			if ($tree->parent !== null) {
				$form->addSelect('template', 'template', $tree->possibleTemplates);
				if (isset($tree->possibleTemplates[$tree->template])) {
					$form['template']->setDefaultValue($tree->template);
				}
			}

			$form->addText('uid', 'UID')->setDefaultValue($tree->uid);
		}

		//publish
		$publishContainer = $form->addContainer('publish');
		$publishContainer->addCheckbox('public', 'public')->setDefaultValue($tree->public);


		// localization
		$localizationContainer = $form->addContainer('localization');
		$localizationContainer->addText('name', 'name');
		$localizationContainer->addHidden('cf');
		$localizationContainer->addHidden('cc');
		$localizationContainer->addHidden('ccScheme');

		// SEO
		$routableContainer = $form->addContainer('routable');
		$routableContainer->addText('alias', 'alias')->setDefaultValue($tree->getAlias());
		$routableContainer->addHidden('aliasCopy', $tree->getAlias());
		$routableContainer->addText('nameTitle', 'title')->setDefaultValue($tree->nameTitle);
		$routableContainer->addText('nameAnchor', 'anchor_text')->setDefaultValue($tree->nameAnchor);
		$routableContainer->addText('nameAnchorBreadcrumb', 'anchor_text_breadcrumb')->setDefaultValue($tree->nameAnchorBreadcrumb);
		$routableContainer->addTextArea('description', 'meta_description', 50, 4)->setDefaultValue($tree->description);
		$routableContainer->addTextArea('aliasHistory', 'aliases_history', 50, 4)->setValue($tree->getAliasHistoryString());
		$routableContainer->addText('keywords', 'keywords')->setDefaultValue($tree->getKeywords());



		//validity

		$container = $form->addContainer('validity');
		$container->addText('publicFrom', 'publicFrom');
		$publicFrom = $tree->publicFrom;
		if ($publicFrom !== null) {
			$container['publicFrom']->setDefaultValue($publicFrom->format('Y-m-d\TH:i'));
		}

		$container->addText('publicTo', 'publicTo');
		$publicTo = $tree->publicTo;
		if ($publicTo !== null) {
			$container['publicTo']->setDefaultValue($publicTo->format('Y-m-d\TH:i'));
		}




		// More settings
		$form->addText('createdTimeOrder', 'created_time_order');
		$createdTimeOrder = $tree->createdTimeOrder;
		if ($createdTimeOrder !== null) {
			$form['createdTimeOrder']->setDefaultValue($createdTimeOrder->format('Y-m-d\TH:i'));
		}

		$form->addCheckbox('forceNoIndex', 'forceNoIndex')->setDefaultValue($tree->forceNoIndex);

		if ($tree->hasBadTemplate && $tree->parent !== null) {
			// pokud ma entita spatnou sablonu, ukazu vyber mozne sablony pro vsechny
			// ve vyberu budou pouze validní sablony
			$form->addSelect('firstTemplate', 'template', $tree->templates)->setPrompt('Vyberte šablonu stránky');
		}



		$form->addCheckbox('hideInSearch', 'hide_in_search')->setDefaultValue($tree->hideInSearch);
		$form->addCheckbox('hideInMenu', 'hide_in_menu')->setDefaultValue($tree->hideInMenu);
		// produkty typu voucheru

		$form->addSubmit('send', 'save_button');

	}



	private function addTreeParent(Form $form, Tree $tree, array $postData): void
	{
		if ($tree->mutation !== $this->mutationsHolder->getDefault()) {
			$treeParentContainer = $form->addContainer('treeParent');
			$treeParentContainer->addHidden('id');
			$treeParentContainer->addText('name', 'name');
			if ($tree->treeParent !== null) {

				$mainLocalization = null;
				foreach ($this->mutationRepository->findByTreeNodeSort() as $mutation) {
					if ($mainLocalization = $tree->treeParent->getLocalizations()->getBy(['mutation' => $mutation])) {
						break;
					}
				}
				if ($mainLocalization !== $tree) {
					assert($mainLocalization instanceof Tree);
					$treeParentContainer['name']->setDefaultValue(sprintf('%s (%s)', $mainLocalization->name, $mainLocalization->mutation->langCode));
					$treeParentContainer['id']->setDefaultValue($mainLocalization->treeParent->id);
				}
			}
		}

	}

}
