{if isset($form['treeParent']['name'])}


	{var $url = $urls['searchPageSibling']->toArray()}
	{php $url['params']['ignoreMutationId'] = $entityLocalization->getMutation()->id}

	{var $item = [
		data: [
			controller: 'RemoveItem SuggestInp',
			removeitem-target: 'item',
			suggestinp-target: 'wrapper',
			suggestinp-url-value: Nette\Utils\Json::encode($url)

		],

		inps: [
			[
				placeholder: 'Zadejte název stránky v jiném jazyce',
				input: $form['treeParent']['name'],
				data: [
					suggestinp-target: 'input',
				]
			],
			[
				input: $form['treeParent']['id'],
				data: [
					suggestinp-target: 'idInput',
				],
				classes: 'u-hide',
				type: 'hidden'
			]
		],
		btnsAfter: [
			[
				icon: $templates.'/part/icons/trash.svg',
				tooltip: 'Odstranit',
				variant: 'remove',
				data: [
					action: 'RemoveItem#remove'
				]
			]
		],
	]}

	{include $templates.'/part/box/list-item.latte',
		props => [
			texts: $item['texts'] ?? [],
			inps: $item['inps'] ?? [],
			img: $item['img'] ?? '',
			checkboxes: $item['checkboxes'] ?? [],
			btnsBefore: $item['btnsBefore'] ?? [],
			btnsAfter: $item['btnsAfter'] ?? [],
			tags: $item['tags'] ?? [],
			langs: $item['langs'] ?? [],
			data: $item['data'] ?? null,
			dragdrop: false,
			rowLabel: $translator->translate('main_mutation'),
		]
	}
{/if}
