{var $anchorName = 'catalogSettings'}
{var $icon = $templates . '/part/icons/cogs.svg'}
{var $title = 'Nastavení katalogu'}

{var $props = [
	title: $title,
	id: $anchorName,
	icon: $icon,
	variant: 'main',
	open: true,
	classes: ['u-mb-xxs'],
]}


{embed $templates . '/part/box/toggle.latte', props => $props, templates => $templates}
	{block content}
		{include $templates . '/part/core/inp.latte', props: [
			input: $form['seoTitleFilter'],
			type: 'textarea',
			classesLabel: ['title'],
		]}

		{include $templates . '/part/core/inp.latte', props: [
			input: $form['seoAnnotationFilter'],
			type: 'textarea',
			classesLabel: ['title'],
		]}

		{include $templates . '/part/core/inp.latte', props: [
			input: $form['seoDescriptionFilter'],
			type: 'textarea',
			classesLabel: ['title'],
		]}
	{/block}
{/embed}

