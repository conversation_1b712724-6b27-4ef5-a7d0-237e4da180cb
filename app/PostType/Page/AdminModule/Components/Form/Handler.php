<?php declare(strict_types = 1);

namespace App\PostType\Page\AdminModule\Components\Form;

use App\Model\Orm\User\User;
use App\Model\StringHelper;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\Event\CatalogTreeUpdated;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Page\Model\Orm\TreeModel;
use Nette\Application\UI\Form;
use Nette\Utils\ArrayHash;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;

final class Handler
{

	public function __construct(
		private readonly TreeModel $treeModel,
		private readonly EventDispatcherInterface $eventDispatcher,

	)
	{
	}


	public function handle(User $userEntity, Tree $tree, Form $form, ArrayHash $values): void
	{
		$valuesAll = $form->getHttpData();
		assert(is_array($valuesAll));
		if (isset($values->firstTemplate)) {
			assert(property_exists($values, 'template'));
			$values->template = $values->firstTemplate;
			$valuesAll['template'] = $values->firstTemplate;
			unset($values->firstTemplate);
			unset($valuesAll['firstTemplate']);
		}

		$valuesAll['content'] = StringHelper::removeTinyMceEmptyP($valuesAll['content'] ?? '');

		$tree = $this->treeModel->save($tree, $valuesAll, $values, $userEntity->id);

		$tree->flushCache();
		if ($tree instanceof CatalogTree) {
			$this->eventDispatcher->dispatch(new CatalogTreeUpdated($tree));
		}
	}

}
