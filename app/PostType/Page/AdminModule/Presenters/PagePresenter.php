<?php declare(strict_types = 1);

namespace App\PostType\Page\AdminModule\Presenters;

use App\AdminModule\Components\Tree\TreeFactory;
use App\AdminModule\Presenters\BasePresenter;
use App\Model\Orm\Alias\AliasModel;
use App\Model\Orm\File\FileModel;
use App\Model\TreeStructure\CommonTreeStructure;
use App\Model\TreeStructure\MovingModel;
use App\Model\TreeStructure\TreeStructure;
use App\PostType\Page\AdminModule\Components\Form\Form;
use App\PostType\Page\AdminModule\Components\Form\FormFactory;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Page\Model\Orm\TreeModel;
use App\PostType\Page\Model\Orm\TreeParentModel;
use App\PostType\Page\Model\Orm\TreeRepository;
use Closure;
use Nette\Http\FileUpload;
use Nette\Utils\Json;

final class PagePresenter extends BasePresenter
{

	protected ?Tree $object;

	public function __construct(
		private readonly FileModel $fileModel,
		private readonly TreeModel $treeModel,
		private readonly AliasModel $aliasModel,
		private readonly TreeParentModel $treeParentModel,
		private readonly FormFactory $formFactory,
		private readonly TreeRepository $treeRepository,
		private readonly TreeFactory $treeFactory,
		private readonly MovingModel $treeMovingModel,
	)
	{
		parent::__construct();
	}

	public function actionDefault(?int $id = null): void
	{
		if ($id === null || ($object = $this->orm->tree->getById($id)) === null) {
			$defaultMutation = $this->orm->mutation->getDefault();
			$this->redirect('this', ['id' => $defaultMutation->rootId]);
		}

		$this->object = $object;

		if ($this->object->treeParent === null) {
			$newParentTree = $this->treeParentModel->create();
			$this->treeParentModel->addTree($this->object, $newParentTree);
		}
	}

	public function renderDefault(): void
	{
		$this->template->tree = $this->treeModel->getQuickTree($this->object->id, $this->userEntity->role);
		$this->template->rootLg = $this->object->getRootLang();

		if ($this->isAjax()) {
			$this->redrawControl();
		}
	}


	private function getPageLink(int $id): string
	{
		return $this->link('default', ['id' => $id]);
	}

	private function getPageTreeStructure(): CommonTreeStructure
	{
		return new CommonTreeStructure(
			$this->treeRepository->findBy(['parent' => null])->orderBy('sort')->fetchAll(),
			$this->object->id,
		);
	}


	private function movePageInTree(int $movedNodeId, int $targetNodeId, string $action): void
	{
		$movedPage = $this->orm->tree->getByIdChecked($movedNodeId);
		$targetPage = $this->orm->tree->getByIdChecked($targetNodeId);

		if ($movedPage->parent === null) {
			throw new \LogicException('Cant move root category.');
		}

		if (!in_array($action, ['after', 'before', 'inside', 'last'])) {
			throw new \LogicException('Unknown move action.');
		}

		if (in_array($action, ['after', 'before']) && $targetPage->parent === null) {
			throw new \LogicException('Cant move to root level.');
		}

		$this->treeMovingModel->move($movedPage, $targetPage, $action);
		$this->redirect('default', ['id' => $movedPage->id]);
	}


	public function createPageInTree(int $parentId, string $name): void
	{
		$parent = $this->orm->tree->getByIdChecked($parentId);
		$this->object = $this->treeModel->createNew($parent, $name);

		$this->redirect('default', ['id' => $this->object->id]);
	}


	// needs dummy default values because link is generated dynamically in JS
	public function actionRegenerateAlias(int $id = 0, string $name = '', string $lg = ''): void
	{
		$mutation = $this->orm->mutation->getByCode($lg);

		$tree = $this->orm->tree->getById($id);
		$this->orm->setMutation($mutation);

		if ($tree) {
			echo $this->aliasModel->handleAliasChange($tree, $mutation, $name, false);
		} else {
			echo $this->aliasModel->generateAlias($name, $mutation);
		}
	}

	// needs dummy default values because link is generated dynamically in JS
	public function actionRegenerateAliasMutation(int $id = 0, string $name = '', string $lang = ''): never
	{
		$mutation = $this->orm->mutation->getByCode($lang);

		$tree = $this->orm->tree->getById($id);
		$this->orm->setMutation($mutation);

		if ($tree) {
			echo $this->aliasModel->handleAliasChange($tree, $mutation, $name, false);
		} else {
			echo $this->aliasModel->generateAlias($name, $mutation);
		}

		$this->terminate();
	}


	protected function createComponentEditForm(): Form
	{
		return $this->formFactory->create($this->object, $this->userEntity, $this->user);
	}

	protected function createComponentTree(): \App\AdminModule\Components\Tree\Tree
	{

		return $this->treeFactory->create(
			$this->getPageTreeStructure(...),
			$this->movePageInTree(...),
			$this->createPageInTree(...),
			$this->getPageLink(...),
			false
		);
	}


	/**
	 * AJAX: /superadmin/pages/?id=130&ts=1589545187&do=lastEdited
	 * return: JSON bool
	 */
	public function handleLastEdited(int $id, int $ts): void
	{
		$tree = $this->orm->tree->getById($id);
		$result = false;

		if ($tree) {
			$result = $tree->editedTime->getTimestamp() === $ts;
		}

		$this->sendJson($result);
	}



//	DEBUG UPLOADIFY
	public function handleUpload(?FileUpload $file = null): never
	{
		//\Nette\Diagnostics\Debugger::$consoleMode = true;

		$file ??= new FileUpload($_FILES['file']);

		$fileEntity = $this->fileModel->add($file);
		echo Json::encode([
			'id' => $fileEntity->id,
		]);
		$this->terminate();
	}

}
