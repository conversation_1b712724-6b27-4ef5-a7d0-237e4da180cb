<?php

declare(strict_types=1);

namespace App\PostType\Page\Model\Orm;

use App\Model\CustomField\LazyValue;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\Traits\HasTreeRelation;
use App\Model\Orm\TreeTree\TreeTree;
use Nextras\Orm\Collection\ICollection;

final class CatalogTreeModel
{

	use HasTreeRelation;

	public function __construct(
		private readonly Orm $orm
	)
	{
	}



	public function save(CatalogTree $tree, mixed $data): void
	{
		$this->handleLinkedCategories($tree, $data);

		$this->orm->persistAndFlush($tree);
	}


	private function handleLinkedCategories(CatalogTree $tree, mixed $data): void
	{
		$collection = $tree->linkedCategoriesAll;
		$name = 'linkedCategories';
		$type = TreeTree::TYPE_LINKED_CATEGORY;

		$limitation = 1; // only one relation is possible
		$this->handleTreeRelations($collection, $name, $type, $tree, $data, $limitation);

		$this->orm->persistAndFlush($tree);
		if ($tree->linkedCategoriesAll->count()) {
			$tree->hasLinkedCategories = 1;
		} else {
			$tree->hasLinkedCategories = 0;
		}
	}


	public function getAllCatalogCategories(Product $product, Mutation $mutation): array
	{
		$attachedCategories = $product->attachCategoriesAll->findBy([
			'type' => Tree::TYPE_CATALOG,
			'mutation' => $mutation,
		])->resetOrderBy()->orderBy('level')->orderBy('sort');

		$allCategories = [];
		foreach ($attachedCategories as $attachedCategory) {
			$allCategories[$attachedCategory->id] = $attachedCategory;

			foreach ($attachedCategory->parentLinkedCategoriesAll as $linkedCategory) {

				$allCategories[$linkedCategory->id] = $linkedCategory;

				// add linked parent path nodes
				$reversePath = array_reverse($linkedCategory->path);
				foreach ($reversePath as $linkedParentId) {
					if (!isset($allCategories[$linkedParentId])) {
						$linkedParent = $this->orm->tree->getById($linkedParentId);
						if ($linkedParent) {
							$allCategories[$linkedParentId] = $linkedParent;
							if ($linkedParent->uid === Tree::UID_ESHOP) {
								break;
							}
						}
					}
				}
			}
		}

		// add full path for main category
		if ($mainCategory = $product->mainCategory) {
			foreach ($mainCategory->pathItems as $pathItem) {
				if ($pathItem) {
					$allCategories[$pathItem->id] = $pathItem;
				}
			}
		}

		return $allCategories;
	}

	/**
	 * @return ICollection<CatalogTree>
	 */
	public function findCategoriesForProductLocalization(ProductLocalization $localization): ICollection
	{
		return $localization->product->attachCategoriesAll->findBy(['mutation' => $localization->mutation]);
	}


	public function getMainCategoryForProductLocalization(ProductLocalization $localization): ?CatalogTree
	{
		return $this->findCategoriesForProductLocalization($localization)->fetch();
	}

	/**
	 * @param ICollection<CatalogTree>|array $categories
	 */
	public function getDeepestCategory(ICollection|array $categories): ?CatalogTree
	{
		$deepestCategory = null;

		foreach ($categories as $category) {
			assert($category instanceof CatalogTree);
			if ($deepestCategory === null) {
				$deepestCategory = $category;
			} elseif ($category->level > $deepestCategory->level) {
				$deepestCategory = $category;
			}
		}

		return $deepestCategory;
	}

	public function getFeedCategoryByFeedType(?CatalogTree $category, string $type): string
	{
		if ($category === null) {
			return '';
		}

		$fallback = function (?CatalogTree $parent, string $type) {
			return $this->getFeedCategoryByFeedType($parent, $type);
		};

		if (in_array($type, ['heureka', 'zbozi', 'google'])) {
			if (isset($category->cf->feeds->{$type}) && $category->cf->feeds->{$type} instanceof LazyValue) {
				/** @var LazyValue $lazyValue */
				$lazyValue = $category->cf->feeds->{$type};
				/** @var TreeAlternative|null $treeAlternative */
				$treeAlternative = $lazyValue->getEntity();
				if ($treeAlternative !== null) {
					return trim($treeAlternative->alt_path);
				} else {
					if ($category->parent instanceof CatalogTree) {
						return $fallback($category->parent, $type);
					}
				}
			} else {
				if ($category->parent instanceof CatalogTree) {
					return $fallback($category->parent, $type);
				}
			}
		}

		return '';
	}

}
