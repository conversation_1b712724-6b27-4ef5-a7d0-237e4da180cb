<?php

declare(strict_types = 1);

namespace App\PostType\Page\Model\Orm;

use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<TreeAlternative>
 */
final class TreeAlternativeMapper extends DbalMapper
{

	use HasCamelCase;

	/**
	 * @return literal-string
	 */
public function getTableName(): string
{
	return 'tree_alternative';
}
	/**
	 * @return ICollection<TreeAlternative>
	 */
	public function searchByName(string $q, string $type): ICollection
	{
		$builder = $this->builder()->andWhere('alt_path LIKE %_like_ AND type = %s', $q, $type);

		return $this->toCollection($builder);
	}

}
