<?php declare(strict_types = 1);

namespace App\PostType\Page\Model\Orm;

class TreeParentModel
{

	public function __construct(
		private readonly TreeParentRepository $treeParentRepository,
	)
	{
	}


	public function create(): TreeParent
	{
		$treeParent = new TreeParent();
		$this->treeParentRepository->attach($treeParent);
		return $treeParent;
	}


	public function delete(TreeParent $parent): void
	{
		$this->treeParentRepository->remove($parent);
		$this->treeParentRepository->persistAndFlush($parent);
	}


	public function addTree(Tree $tree, TreeParent $parent): void
	{
		$parent->localizations->add($tree);
		$this->treeParentRepository->persistAndFlush($parent);
	}

	public function removeTree(Tree $tree): void
	{
		if ($tree->treeParent !== null) {
			$parent = $tree->treeParent;
			$parent->localizations->remove($tree);
			$this->tryDeleteEmptyParent($parent);
			$this->treeParentRepository->persistAndFlush($parent);
		}
	}

	private function tryDeleteEmptyParent(TreeParent $parent): void
	{
		if ($parent->localizations->count() === 0) {
			$this->delete($parent);
		}
	}

}
