<?php

declare(strict_types=1);

namespace App\PostType\Page\Model\Orm;

use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property string $type {enum self::TYPE_*}
 * @property int $alt_id
 * @property string $alt_name
 * @property string $alt_path
 * @property DateTimeImmutable|null $syncTime {default null}
 * @property string|null $syncChecksum {default null}
 * @property string|null $extId {default null}
 *
 * RELATIONS
 *
 *
 * VIRTUALS
 */
final class TreeAlternative extends Entity
{

	public const TYPE_GOOGLE = 'google';
	public const TYPE_HEUREKA = 'heureka';
	public const TYPE_ZBOZI = 'zbozi';

}
