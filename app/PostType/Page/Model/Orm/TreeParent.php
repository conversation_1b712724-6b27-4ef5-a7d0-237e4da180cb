<?php declare(strict_types = 1);

namespace App\PostType\Page\Model\Orm;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\Mutation\Mutation;
use App\PostType\Core\Model\ParentEntity;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Exception\NoResultException;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property OneHasMany<Tree> $localizations {1:m Tree::$treeParent}
 */
class TreeParent extends BaseEntity implements ParentEntity
{

	public function getInternalName(): string
	{
		return '';
	}

	public function setInternalName(string $internalName): void
	{
		// TODO: Implement
	}

	/**
	 * @return ICollection<Tree>
	 */
	public function getLocalizations(): ICollection
	{
		return $this->localizations->toCollection();
	}

	/**
	 * @throws NoResultException
	 */
	public function getLocalization(Mutation $mutation): Tree
	{
		$localization = $this->getLocalizations()->getByChecked(['mutation' => $mutation]);
		assert($localization instanceof Tree);
		return $localization;
	}

	public function getCfSchemeJson(): string
	{
		return '{}';
	}

	public function getCfContent(): string
	{
		return '{}';
	}

	public function setCf(mixed $customFields): void
	{
		// TODO: Implement setCf() method.
	}

}
