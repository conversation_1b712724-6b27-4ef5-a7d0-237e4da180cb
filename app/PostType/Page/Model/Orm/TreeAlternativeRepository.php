<?php declare(strict_types = 1);

namespace App\PostType\Page\Model\Orm;

use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method ICollection<TreeAlternative> searchByName(string $q, string $type)
 * @extends Repository<TreeAlternative>
 */
final class TreeAlternativeRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [TreeAlternative::class];
	}

}
