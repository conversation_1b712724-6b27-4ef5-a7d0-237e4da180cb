<?php declare(strict_types = 1);

namespace App\PostType\Page\Model\Orm;

use App\Model\ConfigService;
use App\Model\CustomContent\CustomContent;
use App\Model\CustomField\CustomFields;
use App\Model\ElasticSearch;
use App\Model\ElasticSearch\All\Facade;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Alias\AliasModel;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\OrmCleaner;
use App\Model\Orm\TreeTree\TreeTree;
use Nette\Utils\ArrayHash;
use Nette\Utils\DateTime;
use Nette\Utils\Floats;
use Nextras\Orm\Collection\ICollection;
use stdClass;

final class TreeModel
{

	public function __construct(
		private readonly Orm $orm,
		private readonly MutationsHolder $mutationsHolder,
		private readonly ConfigService $configService,
		private readonly AliasModel $aliasModel,
		private readonly CustomFields $customFields,
		private readonly CustomContent $customContent,
		private readonly OrmCleaner $ormCleaner,
		private readonly Facade $allElasticFacade,
		private readonly ElasticSearch\Common\Facade $commonElasticFacade,
		private readonly CatalogTreeModel $catalogTreeModel,
		private readonly TreeParentModel $treeParentModel,
	)
	{
	}


	public function handleDuplicate(Tree $source, Tree $destination, bool $skipFirst = false): ?Tree
	{
		$this->orm->tree->setPublicOnly(false);
		$this->orm->setMutation($source->mutation);

		return $this->duplicate($source, $destination, null, false, $skipFirst);
	}


	private function duplicate(Tree $source, Tree $destination, ?int $userId = null, bool $copyAllData = false, bool $skipFirst = false): ?Tree
	{
		if ($skipFirst) {
			foreach ($source->crossroadAll as $c) {
				$this->duplicate($c, $destination, $userId, $copyAllData);
			}

			return null;
		}

		$newTree = $this->createNewEntityByType($source->type);
		$newTree->name = $source->name;
		$newTree->nameAnchor = $source->nameAnchor;
		$newTree->nameTitle = $source->nameTitle;
		$newTree->type = $source->type;
		$newTree->publicFrom = $source->publicFrom;
		$newTree->created = $source->created;
		$newTree->annotation = $source->annotation;
		$newTree->content = $source->content;
		$newTree->hideInSearch = $source->hideInSearch;
		$newTree->edited = $source->edited;
		$newTree->editedTime = $source->editedTime;
		$newTree->publicTo = $source->publicTo;
		$newTree->template = $source->template;
		$newTree->level = $destination->level + 1;
		$newTree->last = $source->last;
		$newTree->uid = $source->uid;

		$newTree->path = array_merge($destination->path, [$destination->id]);
		$newTree->public = $source->public;
		$newTree->createdTimeOrder = $source->createdTimeOrder;
		$newTree->rootId = $destination->rootId;
		$newTree->mutation = $destination->mutation;

		$newTree->parent = $destination;
		$this->orm->tree->attach($newTree);
		$newTree->sort = $source->sort;

		$newTree->customFieldsJson = $source->customFieldsJson;

		$this->orm->tree->persistAndFlush($newTree);
		$this->aliasModel->handleAliasChange($newTree, $newTree->mutation);

		foreach ($source->crossroadAll as $c) {
			$this->duplicate($c, $newTree, $userId, $copyAllData);
		}

		return $newTree;
	}


	/**
	 * Add Child to parent - handle parent, level, last, path
	 */
	public function addChild(Tree $parent, Tree $child, bool $save = true, bool $skipSort = false): Tree
	{
		if (!$skipSort) {
			$maxSort = 0;
			foreach ($parent->crossroadAll as $item) {
				if ($item->isPersisted()) {
					$maxSort = max($maxSort, intval($item->sort));
				}
			}

			$child->sort = $maxSort + 1;
		}

		$child->parent = $parent;
		$child->level = intval($parent->level) + 1;
		$child->last = 1;
		$child->rootId = $parent->rootId;
		$child->mutation = $parent->mutation;

		//path
		$path = $parent->path;
		$path[] = $parent->id;
		$child->path = $path;
		$parent->last = 0;

		if ($save) {
			$this->orm->tree->persistAndFlush($child);
		}

		return $child;
	}


	public function save(Tree $tree, array $data, ArrayHash $values, ?int $userId = null): Tree
	{
		if (isset($data['createdTimeOrder']) && $data['createdTimeOrder'] === '') {
			$data['createdTimeOrder'] = null;
		}

		$oldType = $tree->type;
		$templateRules = $this->configService->get('templatesParentsRules');

		// tyto hodnoty jsou bool a DB je chce v integeru
		$boolValues = ['public', 'hideInSearch', 'hideInMenu', 'forceNoIndex'];
		$noSave = ['links', 'treeParent'];

//		$data['publicFrom'] = DateTimeImmutable::createFromFormat('Y-m-d H:i:s', $data['publicFrom']);
//		$data['publicTo'] = DateTimeImmutable::createFromFormat('Y-m-d H:i:s', $data['publicTo']);
		if ($userId) {
			$data['edited'] = $userId;
		}

		$data['editedTime'] = DateTime::from('now')->format('Y-m-d H:i:s');

		foreach ($tree->getMetadata()->getProperties() as $i) {
			$col = (string) $i->name;

			if ($i->isVirtual && !$i->hasSetter) {
				continue;
			}

			if (isset($data[$col])) {
				if (in_array($col, $boolValues)) {
					$data[$col] = (bool) $data[$col];
				}

				if (!in_array($col, $noSave)) {
					if ($col === 'template') {
						// automatic set template according to parent

						if ($tree->parent && $tree->parent->template) {
							if (isset($templateRules[$tree->parent->template]) && is_array($templateRules[$tree->parent->template]) && count($templateRules[$tree->parent->template]) === 1) {
								$tree->$col = $templateRules[$tree->parent->template][0];
								continue;
							}
						}
					}

//					bd($col);
					$tree->$col = $data[$col];
				}
			} else {
//				bd($col);
				if (in_array($col, $boolValues)) {
					$tree->$col = false;
				}
			}
		}

		if (isset($data['localization']['cf'])) {
			$tree->cf = $this->customFields->prepareDataToSave($data['localization']['cf']);
		}

		if (isset($data['localization']['cc']) && isset($data['localization']['ccScheme'])) {
			$tree->customContentJson = $this->customContent->prepareDataToSave($data['localization']['cc'], $data['localization']['ccScheme']);
		}

		if ($tree->parent) {
			$tree->rootId = $tree->parent->rootId;
			$tree->mutation = $tree->parent->mutation;
		} else {
			$tree->rootId = $tree->id;
		}

		$tree->public = $values->publish->public;
		$tree->forceNoIndex = $values->forceNoIndex;
		$tree->hideInSearch = $values->hideInSearch;

		$tree->nameTitle = $values->routable->nameTitle;
		$tree->nameAnchor = $values->routable->nameAnchor;
		$tree->nameAnchorBreadcrumb = $values->routable->nameAnchorBreadcrumb;
		$tree->description = $values->routable->description;
		$tree->keywords = $values->routable->keywords;

		$tree = $this->handleType($tree, $oldType);

		$this->handleValidity($tree, $data);
		$this->handlePages($tree, $data);
		$this->handleTreeParent($tree, $data);
		$this->handleAlias($tree, $data);

		if ($tree instanceof CatalogTree) {
			$this->catalogTreeModel->save($tree, $data);
		}

		$this->orm->persistAndFlush($tree);
		$tree->flushCache();

		$this->commonElasticFacade->updateNow($tree, $tree->mutation);
		$this->allElasticFacade->updateNow($tree);

		return $tree;
	}


	private function handlePages(Tree $tree, array $data): void
	{
		$collection = $tree->pagesAll;
		$name = 'pages';
		$type = TreeTree::TYPE_NORMAL;
		$this->handleTreeRelations($collection, $name, $type, $tree, $data);
	}


	private function handleTreeParent(Tree $tree, array $data): void
	{
		if (isset($data['treeParent']['id'])) {
			$newSibling = $this->orm->tree->getById($data['treeParent']['id']);
			if ($newSibling !== null && $newSibling->treeParent !== $tree->treeParent) {
				$this->treeParentModel->removeTree($tree);
				$this->treeParentModel->addTree($tree, $newSibling->treeParent);
			}
		}
	}


	public function remove(Tree $tree): void
	{
		$this->commonElasticFacade->deleteNow($tree, $tree->mutation);
		$this->allElasticFacade->deleteNow($tree);

		$parent = $tree->treeParent;

		$this->orm->tree->remove($tree);

		if ($parent->localizations->count() === 0) {
			$this->orm->remove($parent);
		}

		$this->orm->flush();
	}


	public function createNew(Tree $parent, string $name): Tree
	{
		$templateRules = $this->configService->get('templatesParentsRules');

		if (isset($templateRules[$parent->template]) && is_array($templateRules[$parent->template]) && count($templateRules[$parent->template]) === 1) {
			$template = $templateRules[$parent->template][0];
		} else {
			$template = $parent->template;
		}

		$child = $this->createNewEntityByType($parent->type);
		$child->name = $name;
		$child->mutation = $parent->mutation;
		$child->type = $parent->type;
		$child->nameTitle = $name;
		$child->nameAnchor = $name;
		$child->public = false;
		$child->template = $template;
		$child->created = 0;
		$this->addChild($parent, $child);

		$newParentTree = $this->treeParentModel->create();
		$this->treeParentModel->addTree($child, $newParentTree);

		$this->orm->tree->persistAndFlush($child);
		return $child;
	}

	public function getQuickTree(?int $id, string $role = 'admin'): array
	{
		if ($role === 'admin') {
			$all = $this->orm->tree->findBy(['uid!=' => 'systemPageId'])->orderBy('sort')->orderBy('id');
		} else {
			$all = $this->orm->tree->findBy([])->orderBy('sort')->orderBy('id');
		}

		$sorted = [];
		foreach ($all as $a) {
			$tmp = new stdClass();
			$tmp->name = $a->name;
			$tmp->id = $a->id;
			$tmp->public = $a->public;
			$sorted[$a->parentId][] = $tmp;
		}

		return $this->buildTree('', $id, $sorted);
	}

	private function buildTree(int|string $level, int $selected, array $sorted): array
	{
		$ret = [];
		if (isset($sorted[$level])) {
			foreach ($sorted[$level] as $k => $r) {
				$ret[$k] = $r;
				$ret[$k]->active = false;
				if ($r->id === $selected) {
					$ret[$k]->active = true;
				}

				$ret[$k]->items = $this->buildTree($r->id, $selected, $sorted);
			}
		}

		return $ret;
	}


	private function handleType(Tree $tree, string $oldType): ?Tree
	{
		$newType = Tree::TYPE_COMMON;
		switch ($tree->template) {
			case ':Front:Catalog:default':
				$newType = Tree::TYPE_CATALOG;
				break;
		}

		if ($oldType !== $newType) {
			// save new type
			$tree->type = $newType;
			$this->orm->tree->persistAndFlush($tree);

			// force refresh entity
			$id = $tree->id;
			$this->ormCleaner->safeClear();

			return $this->orm->tree->findBy([
				'id' => $id,
			])->fetch();
		} else {
			return $tree;
		}
	}

	private function createNewEntityByType(?string $type): CatalogTree|CommonTree
	{
		if ($type === Tree::TYPE_CATALOG) {
			return new CatalogTree();
		} else {
			return new CommonTree();
		}
	}

	public function findMySisterPageInMutations(Tree $sisterPage, Mutation $mutation): ?Tree
	{
		if (($parent = $sisterPage->getParent()) !== null) {
			$sibling = $parent->getLocalizations()->getBy(['mutation' => $mutation]);
			if ($sibling !== null) {
				assert($sibling instanceof Tree);
				return $sibling;
			}
		}
		return null;
	}

	private function handleAlias(Tree $tree, array $data): void
	{
		// first save alias history
		$tree->setAliasHistoryString($data['routable']['aliasHistory']);
		// than solve alais
		$tree->setAlias($data['routable']['alias']);
	}

	/**
	 * @param ICollection<Tree> $collection
	 */
	private function handleTreeRelations(ICollection $collection, string $name, string $type, Tree $tree, mixed $data, ?int $limitation = null): void
	{
		$attachedIds = $collection->fetchPairs('id');

		if (isset($data[$name]) && $data[$name]) {

			$sort = 0;
			foreach ($data[$name] as $key => $id) {
				$attachedProduct = $this->orm->tree->getById($id);
				if ($attachedProduct) {
					if (is_int($key)) {
						unset($attachedIds[$attachedProduct->id]);
					}

					$this->orm->treeTree->replace($tree, $attachedProduct, $type, $sort);
					$sort++;

					if ($limitation !== null && $limitation === $sort) {
						break;
					}
				}
			}
		}

		foreach ($attachedIds as $attachedTree) {
			$treeProductToDelete = $this->orm->treeTree->getBy([
				'type' => $type,
				'mainTree' => $tree,
				'attachedTree' => $attachedTree,
			]);
			$this->orm->treeTree->removeAndFlush($treeProductToDelete);
		}
	}


	private function handleValidity(Tree $tree, array $data): void
	{
		if (isset($data['validity']['publicFrom'])) {
			if ($data['validity']['publicFrom'] === '') {
				$data['validity']['publicFrom'] = null;
			}

			$tree->publicFrom = $data['validity']['publicFrom'];
		}

		if (isset($data['validity']['publicTo'])) {
			if ($data['validity']['publicTo'] === '') {
				$data['validity']['publicTo'] = null;
			}

			$tree->publicTo = $data['validity']['publicTo'];
		}
	}

	public function calculateScore(string $mutationCode, int $batchSize = 50): void
	{
		$treeLastId = 0;
		$treesPath = [];
		$treesScore = [];

		do {
			/**
			 * reload mutaion -> required due cache release
			 */
			$mutation = $this->orm->mutation->getByCode($mutationCode);
			$trees = $this->orm->tree->findBy(['id>' => $treeLastId, 'mutation' => $mutation])->limitBy($batchSize);

			if ($trees->count() === 0) {
				break;
			}

			/**
			 * @var Tree $tree
			 */
			foreach ($trees as $tree) {
				$treeLastId = $tree->id;
				$treesPath[$tree->id] = $tree->path;
				$treesScore[$tree->id] = $tree->productSaleCount;
			}

			/**
			 * flush changes and release used cache = optimize whole process
			 */
			$this->orm->flush();
			$this->ormCleaner->safeClear();

		} while ($trees->count() > 0);

		if (empty($treesPath)) {
			return;
		}

		foreach ($treesPath as $treeRootId => $treePath) {

			$treeScore = $treesScore[$treeRootId] ?? 0.0;

			foreach ($treePath as $treeId) {
				if (!isset($treesScore[$treeId])) {
					continue;
				}

				$treeScore += $treesScore[$treeId];
			}

			$tree = $this->orm->tree->getById($treeRootId);

			if (Floats::areEqual($tree->score, $treeScore)) {
				continue;
			}

			$tree->score = $treeScore;
			$this->orm->persist($tree);
		}

		$this->orm->flush();
	}

}
