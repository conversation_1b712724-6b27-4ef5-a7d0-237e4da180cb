<?php declare(strict_types = 1);

namespace App\PostType\Page\Model\Orm\Event;

use App\Model\ElasticSearch\Product\Convertor\ClassData;
use App\Model\Messenger\Elasticsearch\Product\Message\ReplaceProductMessage;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Orm\Product\ProductRepository;
use App\PostType\Page\Model\Orm\TreeRepository;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;

readonly final class Subscriber implements EventSubscriberInterface
{

	public function __construct(
		private MessageBusInterface $messageBus,
		private ProductRepository $productRepository,
		private EsIndexRepository $esIndexRepository,
		private TreeRepository $treeRepository,
	)
	{
	}

	public function onCatalogTreeUpdated(CatalogTreeUpdated $event): void
	{
		if ($event->catalogTree->isVirtualCategory()) {
			$event->catalogTree->hideInSearch = true;
			$event->catalogTree->forceNoIndex = true;

			$this->treeRepository->persistAndFlush($event->catalogTree);
		}

		$productEsIndex = $this->esIndexRepository->getProductLastActive($event->catalogTree->getMutation());
		if ($productEsIndex !== null) {
			if ($event->catalogTree->hasCourses) {
				foreach ($this->productRepository->findRelationsByMainCategory($event->catalogTree) as $productRow) {
					$this->messageBus->dispatch(new ReplaceProductMessage(
						esIndex: $productEsIndex,
						productId: $productRow->productId,
						convertors: [ClassData::class]
					));
				}
			}
		}
	}
	public static function getSubscribedEvents(): array
	{
		return [
			CatalogTreeUpdated::class => [
				['onCatalogTreeUpdated', 1],
			],
		];
	}

}
