<?php declare(strict_types = 1);

namespace App\PostType\Page\Model\Orm;

use App\Exceptions\LogicException;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Traits\HasCamelCase;
use App\Model\Orm\Traits\HasFindPairFunction;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\Conventions\Conventions;
use Nextras\Orm\Mapper\Dbal\Conventions\IConventions;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<Tree>
 */
final class TreeMapper extends DbalMapper
{

	use HasCamelCase;
	use HasFindPairFunction;

	/**
	 * @return literal-string
	 */
	public function getTableName(): string
	{
		return 'tree';
	}


	public static function getLikePath(int $id): array
	{
		return ['(path LIKE %_like_ OR path LIKE %like_)', '|' . $id . '|', $id . '|'];
	}

	protected function createConventions(): IConventions
	{
		$conventions = parent::createConventions();
		assert($conventions instanceof Conventions); // property is not available on interface

		$conventions->setMapping('linksString', 'links');
		$conventions->setMapping('videosString', 'videos');
		$conventions->setMapping('pathString', 'path');

		$conventions->manyHasManyStorageNamePattern = '%s_%s';

		return $conventions;
	}


	/**
	 * @return ICollection<Tree>
	 */
	public function findLastInPath(mixed $id): ICollection
	{
		$builder = $this->builder()
			->andWhere('last = %i', 1)
			->andWhere('(path LIKE %_like_ OR path LIKE %like_)', '|' . $id . '|', $id . '|');
		return $this->toCollection($builder);
	}

	/**
	 * @return ICollection<Tree>
	 */
	public function findFilteredPages(array $ids): ICollection
	{
		if (empty($ids)) {
			/** @var EmptyCollection<Tree> $emptyCollection */
			$emptyCollection = new EmptyCollection();
			return $emptyCollection;
		}

		$builder = $this->builder()
			->andWhere('id in %i[]', $ids)
			->orderBy('%raw', 'FIELD(id, ' . implode(',', $ids) . ')');

		return $this->toCollection($builder);
	}


	/**
	 * @return ICollection<Tree>
	 */
	public function findInPath(mixed $id): ICollection
	{
		$builder = $this->builder()
			->andWhere('path LIKE %_like_', '|' . $id . '|');

		return $this->toCollection($builder);
	}


	/**
	 * @return ICollection<Tree>
	 */
	public function searchByName(string $q, ?int $parentId = null, ?int $pathId = null, array $excluded = []): ICollection
	{
		$builder = $this->builder()
			->andWhere('name LIKE %_like_', $q);

		if ($parentId) {
			$builder->andWhere('parentId = %i', $parentId);
		}

		if ($pathId) {
			$builder->andWhere('(path LIKE %_like_
				or path LIKE %like_
				or path LIKE %_like )
				', '|' . $pathId . '|', $pathId . '|', '|' . $pathId);
		}

		if ($excluded) {
			$builder->andWhere('id not in %i[]', $excluded);
		}

		return $this->toCollection($builder);
	}

	/**
	 * @return ICollection<Tree>
	 */
	public function findMainTreesInRelations(Tree $attachedTree, string $type): ICollection
	{
		$builder = $this->builder();
		$builder->joinInner('[tree_tree] as tt', '[tree.id] = [tt.mainTreeId] AND [tt.type] = %s', $type);
		$builder->andWhere('tt.attachedTreeId = %i', $attachedTree->id);

		$builder->orderBy('tt.sort');

		return $this->toCollection($builder);
	}

	/**
	 * @return ICollection<Tree>
	 */
	public function findAttachedTreesInRelations(Tree $mainTree, string $type): ICollection
	{
		$builder = $this->builder();
		$builder->joinInner('[tree_tree] as tt', '[tree.id] = [tt.attachedTreeId] AND [tt.type] = %s', $type);
		$builder->andWhere('tt.mainTreeId = %i', $mainTree->id);
		$builder->orderBy('tt.sort');

		return $this->toCollection($builder);
	}

	/**
	 * @return ICollection<Tree>
	 */
	public function findTreesInTreeProductRelations(Product $product, string $type, ?Mutation $mutation = null): ICollection
	{
		$builder = $this->builder();
		$builder->joinInner('[tree_product] as tp', '[tree.id] = [tp.treeId] AND [tp.type] = %s', $type);
		$builder->andWhere('tp.productId = %i', $product->id);
		$builder->orderBy('tp.sort');

		if ($mutation) {
			$builder->andWhere('tree.rootId = %i', $mutation->rootId);
		}

		return $this->toCollection($builder);
	}

	/**
	 * @return ICollection<Tree>
	 */
	public function getByShortcut(Tree $tree, string $shortcut): ICollection
	{
		$builder = $this->builder()->select('t.*')->from($this->getTableName(), 't')
			->joinInner('[tree_parameter] as tp', '[t.id] = [tp.treeId] AND [tp.value] = %s', $shortcut)
			->andWhere('t.path LIKE %_like_', '|' . $tree->id . '|')
			->andWhere('tp.value = %s', $shortcut);

		return $this->toCollection($builder);
	}

	/**
	 * @return ICollection<Tree>
	 */
	public function findByParameterValueIds(array $ids): ICollection
	{
		$builder = $this->builder()->select('t.*')->from($this->getTableName(), 't')
			->joinInner('[tree_parameter] as tp', '[t.id] = [tp.treeId]')
			->andWhere('parameterValueId in %i[]', $ids)
			->groupBy('t.id');
		return $this->toCollection($builder);
	}

	public function findAllIds(?int $limit): Result
	{
		$builder = $this->builder()->select('t.id')
			->from($this->getTableName(), 't')
			->limitBy($limit);
		return $this->connection->queryByQueryBuilder($builder);
	}
	public function findChildIds(array $pathIds): array
	{
		if ($pathIds === []) {
			return [];
		}

		$builder = $this->builder()->select('t.id')
			->from($this->getTableName(), 't')
			->andWhere('parentId in %i[]', $pathIds);

		return $this->connection->queryByQueryBuilder($builder)->fetchPairs(null, 'id');
	}


	public function findAllIdsInMutation(Mutation $mutation, ?int $limit = null): Result
	{
		$builder = $this->builder()->select('t.id')
			->from($this->getTableName(), 't')
			->andWhere('t.rootId = %i', $mutation->getRealRootId())
			->orderBy('level asc')
			->limitBy($limit);
		return $this->connection->queryByQueryBuilder($builder);
	}

	/**
	 * @return ICollection<Tree>
	 */
	public function findByExactOrder(array $ids, string $columnName = 'id'): ICollection
	{
		if (!in_array($columnName, ['id', 'extId'])) {
			throw new LogicException('Invalid column name.');
		}

		if ($ids === []) {
			/** @var ICollection<Tree> $emptyCollection */
			$emptyCollection = new EmptyCollection();
			return $emptyCollection;
		}

		$builder = $this->builder()
			->andWhere('%column in %i[]', $columnName, array_values($ids))
			->orderBy('%raw', 'FIELD(' . $columnName . ', ' . implode(',', array_values($ids)) . ')');

		return $this->toCollection($builder);
	}

}
