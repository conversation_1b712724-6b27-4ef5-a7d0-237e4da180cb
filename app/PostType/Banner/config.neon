parameters:
	config:
		bannerTypes: [
			default: default
			quote: quote
			img-sm: img-sm
			img-lg: img-lg
		]

	postTypeRoutes:
		Banner: banner

cc:

cf:
	fields:
		bannerCommon:
			type: group
			items:
				label:
					type: text
					label: "Štítek"
				title:
					type: textarea
					label: "Nadpis"
				linkChoose:
					type: group
					label: "T<PERSON><PERSON><PERSON><PERSON><PERSON>"
					extends: @cf.definitions.linkChoose
				hideBtn:
					type: checkbox
					label: "Skrýt tlačítko"

	templates:
		bannerLocalization-default:
			- @cf.bannerCommon
		bannerLocalization-img-lg:
			base:
				type: group
				items:
					image:
						label: Obrázek
						type: image
			- @cf.bannerCommon
		bannerLocalization-img-sm:
			base:
				type: group
				items:
					image:
						label: Obr<PERSON>zek
						type: image
			- @cf.bannerCommon
		bannerLocalization-quote:
			base:
				type: group
				items:
					person:
						label: Citovaný
						type: text
			- @cf.bannerCommon

application:
	mapping:
		Banner: App\PostType\Banner\*Module\Presenters\*Presenter

services:
	-
		implement: App\PostType\Banner\AdminModule\Components\ShellForm\ShellFormFactory
		arguments: (types: %config.bannerTypes%)
	- App\PostType\Banner\AdminModule\Components\DataGrid\BannerDataGridPrescription(types: %config.bannerTypes%)
	- App\PostType\Banner\AdminModule\Components\Form\BannerFormPrescription(coreFormPath: %appDir%/PostType/Core/AdminModule/Components/Form)
	- App\PostType\Banner\FrontModule\Components\BannerPosition\BannerPositionFactory
	- App\PostType\Banner\FrontModule\Components\BannerPosition\ShowBannerHolder
	- App\PostType\Banner\Model\BannerLocalizationFacade
	- App\PostType\Banner\Model\Orm\BannerLocalizationModel

