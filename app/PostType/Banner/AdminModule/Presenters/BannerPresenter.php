<?php

namespace App\PostType\Banner\AdminModule\Presenters;

use App\AdminModule\Presenters\BasePresenter;
use App\Model\Orm\User\User;
use App\PostType\Banner\AdminModule\Components\DataGrid\BannerDataGridPrescription;
use App\PostType\Banner\AdminModule\Components\Form\BannerFormPrescription;
use App\PostType\Banner\Model\BannerLocalizationFacade;
use App\PostType\Banner\Model\Orm\BannerLocalization;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGrid;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGridFactory;
use App\PostType\Core\AdminModule\Components\Form\Form;
use App\PostType\Core\AdminModule\Components\Form\FormFactory;
use App\PostType\Banner\AdminModule\Components\ShellForm\ShellForm;
use App\PostType\Banner\AdminModule\Components\ShellForm\ShellFormFactory;

class BannerPresenter extends BasePresenter
{

	public const string ORM_REPOSITORY_NAME = 'banner';

	private BannerLocalization $bannerLocalization;

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
		private readonly BannerDataGridPrescription $bannerDataGridPrescription,
		private readonly ShellFormFactory $shellFormFactory,
		private readonly BannerLocalizationFacade $bannerLocalizationFacade,
		private readonly FormFactory $formFactory,
		private readonly BannerFormPrescription $bannerFormPrescription,
	)
	{
		parent::__construct();
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->action . '.latte');
	}

	public function renderDefault(): void
	{
	}

	public function actionEdit(int $id): void
	{
		$banner = $this->orm->bannerLocalization->getById($id);
		if ($banner === null) {
			$this->redirect('default');
		}

		$this->bannerLocalization = $banner;
	}

	public function renderEdit(int $id): void
	{
	}


	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create(
			baseEntityName: self::ORM_REPOSITORY_NAME,
			collection: $this->orm->bannerLocalization->findAll(),
			dataGridDefinition: $this->bannerDataGridPrescription->get(),
		);
	}


	public function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create(
			entity: null,
			entityLocalizationFacade: $this->bannerLocalizationFacade,
		);
	}

	public function createComponentForm(): Form
	{
		/** @var User $userEntity */
		$userEntity = $this->userEntity;
		return $this->formFactory->create(
			$this->bannerLocalizationFacade,
			$this->bannerLocalization,
			$userEntity,
			$this->bannerFormPrescription->get($this->bannerLocalization)
		);
	}

}
