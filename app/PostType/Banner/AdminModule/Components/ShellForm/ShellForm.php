<?php declare(strict_types = 1);

namespace App\PostType\Banner\AdminModule\Components\ShellForm;

use App\Model\Orm\Mutation\Mutation;
use App\PostType\Banner\Model\Orm\BannerLocalization;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\EntityLocalizationFacade;
use App\PostType\Core\Model\ParentEntity;
use App\Model\Orm\Orm;
use App\PostType\Banner\AdminModule\Components\ShellForm\FormData\BaseFormData;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nextras\Orm\Collection\ICollection;
use App\Model\Translator;

class ShellForm extends Control
{

	/** @var ICollection<Mutation> */
	private ICollection $mutations;

	public function __construct(
		private readonly ?ParentEntity $entity,
		private readonly EntityLocalizationFacade $entityLocalizationFacade,
		private readonly Translator $translator,
		private readonly Orm $orm,
		private readonly array $types = [],
	)
	{
		$this->onAnchor[] = [$this, 'init'];
	}


	public function init(): void
	{
		$mutationIdToSkip = [];
		if ($this->entity !== null) {
			foreach ($this->entity->getLocalizations() as $localization) {
				assert($localization instanceof LocalizationEntity);
				$mutationId = $localization->getMutation()->id;
				$mutationIdToSkip[$mutationId] = $mutationId;
			}
		}

		$this->mutations = $this->orm->mutation->findBy(['id!=' => $mutationIdToSkip]);
	}

	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$template->add('templates', RS_TEMPLATE_DIR);
		$template->add('mutations', $this->mutations);

		$template->render(__DIR__ . '/shellForm.latte');
	}


	protected function createComponentForm(): Form
	{
		$form = new Form();
		$form->setMappedType(BaseFormData::class);
		$form->setTranslator($this->translator);

		$types = [];
		foreach ($this->types as $key => $type) {
			$types[$key] = $this->translator->translate('banner_type_' . $type);
		}

		$form->addSelect('mutation', 'select_mutation', $this->mutations->fetchPairs('id', 'name'));
		$form->addSelect('type', 'select_type', $types);
		$form->addSubmit('send', 'send');

		$form->onSuccess[] = [$this, 'formSucceeded'];
		$form->onError[] = [$this, 'formError'];
		return $form;
	}


	public function formError(Form $form): void
	{
		bd($form->errors);
		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSucceeded(Form $form, BaseFormData $data): void
	{
		$mutation = $this->orm->mutation->getById($data->mutation);

		if ($mutation !== null) {
			$entityLocalization = $this->entityLocalizationFacade->create($mutation, $this->entity);
			assert($entityLocalization instanceof BannerLocalization);
			$entityLocalization->getParent()->type = $data->type;
			$this->orm->bannerLocalization->persistAndFlush($entityLocalization);

			$this->presenter->redirect('edit', ['id' => $entityLocalization->getId()]);
		} else {
			$form->addError('Akce se nezdařila');
			$this->flashMessage('Akce se nezdařila', 'error');
		}
	}

}
