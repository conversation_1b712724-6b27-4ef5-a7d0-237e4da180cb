<?php

namespace App\PostType\Banner\AdminModule\Components\DataGrid;

use App\Model\Translator;
use App\PostType\Banner\Model\Orm\BannerLocalization;
use App\PostType\Core\AdminModule\Components\DataGrid\Definition\DataGridDefinition;
use App\PostType\Core\AdminModule\Components\DataGrid\Definition\Extenders\CustomDataGridExtender;
use Ublaboo\DataGrid\DataGrid;

class BannerDataGridPrescription
{

	public function __construct(
		private array $types,
		private Translator $translator,
	)
	{
	}

	private function addType(DataGrid $dataGrid): void
	{
		$filterItems = array_map(fn(string $item) => $this->translator->translate('banner_type_' . $item), $this->types);
		$dataGrid
			->addColumnText('type', 'type', 'banner.type')
			->setRenderer(
				function (BannerLocalization $bannerLocalization) {
					return $this->translator->translate('banner_type_' . $bannerLocalization->getParent()->type);
				}
			)
			->setFilterSelect($filterItems)->setPrompt('Vše');
	}

	public function get(): DataGridDefinition
	{
		$extenders = [];
		$extenders[] = new CustomDataGridExtender(
			$this->addType(...),
		);

		return new DataGridDefinition(extenders: $extenders);
	}

}
