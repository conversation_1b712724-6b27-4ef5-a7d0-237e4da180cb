<?php

namespace App\PostType\Banner\AdminModule\Components\Form;

use App\Model\CustomField\SuggestUrl;
use App\Model\CustomField\SuggestUrls;
use App\PostType\Banner\AdminModule\Components\Form\FormData\BannerFormData;
use App\PostType\Banner\Model\Orm\BannerLocalization;
use App\PostType\Banner\Model\Orm\BannerLocalizationModel;
use App\PostType\Banner\Model\Orm\BannerPositionRepository;
use App\PostType\Core\AdminModule\Components\Form\Builder;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\CommonTemplatePart;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\RelationTemplatePart;
use App\PostType\Core\AdminModule\Components\Form\Definition\FormDefinition;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\CustomFormExtender;
use App\PostType\Core\AdminModule\Components\Form\Definition\RelationInfoFactory;
use App\PostType\Core\AdminModule\Components\Form\Handler;
use Nette\Application\UI\Form;
use Nette\Http\Request;
use Nextras\Orm\Relationships\HasMany;

readonly class BannerFormPrescription
{

	public function __construct(
		private BannerLocalizationModel $bannerLocalizationModel,
		private RelationInfoFactory $relationInfoFactory,
		private Request $request,
		private Builder $coreBuilder,
		private SuggestUrls $urls,
		private BannerPositionRepository $bannerPositionRepository,
		private string $coreFormPath,
	)
	{
	}

	public function get(BannerLocalization $bannerLocalization): FormDefinition
	{
		$extenders = [];
		$extenders[] = $this->addPositions($bannerLocalization);
//		$extenders[] = $this->addTrees($bannerLocalization);

		$form = new Form();
		$form->setMappedType(BannerFormData::class);

		return new FormDefinition(
			form: $form,
			extenders: $extenders,
		);
	}

	public function addPositions(BannerLocalization $bannerLocalization): CustomFormExtender
	{
		$tagsRelationsInfo = $this->relationInfoFactory->create(
			sourceEntity: $bannerLocalization->getParent(),
			propertyName: 'positions',
			suggestUrl: new SuggestUrl('', ''), // blank
		);

		return new CustomFormExtender(
			addHandler: function (Form $form) use ($bannerLocalization) {
				$positions = $this->bannerPositionRepository->findBy([])->fetchPairs('id', 'name');
				$positions = array_map(
					static fn (string $item): string => 'position_' . $item,
					$positions,
				);
				$form->addMultiSelect('positions', 'positions', $positions)
					->setDefaultValue(
						$bannerLocalization->getParent()
							->positions
							->toCollection()
							->fetchPairs(null, 'id')
					);
			},
			successHandler: function (Form $form, BannerFormData $data) use ($tagsRelationsInfo) {
				$relation = $tagsRelationsInfo->sourceEntity->{$tagsRelationsInfo->propertyName};
				assert($relation instanceof HasMany);
				$relation->set($data->positions);
			},
			templateParts: [
				new CommonTemplatePart(
					__DIR__ . '/positions.latte',
					CommonTemplatePart::TYPE_MAIN,
					[],
				),
			]
		);
	}

	/** @phpstan-ignore-next-line method.unused */
	private function addTrees(BannerLocalization $bannerLocalization): CustomFormExtender
	{
		$url = $this->urls['searchMutationPage'];
		$url->params['mutationId'] = $bannerLocalization->mutation->id;

		$categoriesRelationsInfo = $this->relationInfoFactory->create(
			sourceEntity: $bannerLocalization,
			propertyName: 'bannerLocalizationTrees',
			suggestUrl: $url,
			inputSuggestPropertyName: 'name',
			toggleName: 'trees',
			dragAndDrop: true,
			builderCollection: $bannerLocalization->trees,
		);

		return new CustomFormExtender(
			addHandler: function (Form $form) use ($categoriesRelationsInfo) {
				$this->coreBuilder->addHasManyRelation($form, $categoriesRelationsInfo, $this->request->getPost());
			},
			successHandler: function (Form $form, BannerFormData $data) use ($categoriesRelationsInfo, $bannerLocalization) {
				$ids = Handler::readOnlyValidIds($data->{$categoriesRelationsInfo->propertyName});
				$this->bannerLocalizationModel->setCategoriesByIds($bannerLocalization, $ids);
			},
			templateParts: [
				new RelationTemplatePart(
					relationInfo: $categoriesRelationsInfo,
					templateFile: $this->coreFormPath . '/parts/relation.latte',
					type: RelationTemplatePart::TYPE_RELATION,
				),
				new RelationTemplatePart(
					relationInfo: $categoriesRelationsInfo,
					templateFile:$this->coreFormPath . '/parts/newItemTemplate.latte',
					type: RelationTemplatePart::TYPE_RELATION_PRESCRIPTION,
				),
			]
		);
	}

}
