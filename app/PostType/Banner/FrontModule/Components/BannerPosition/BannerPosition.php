<?php declare(strict_types = 1);

namespace App\PostType\Banner\FrontModule\Components\BannerPosition;

use App\Model\Orm\Mutation\Mutation;
use App\PostType\Banner\Model\Orm\BannerLocalizationModel;
use Nette\Application\UI\Control;
use App\Model\Translator;

class BannerPosition extends Control
{

	public function __construct(
		private readonly Mutation $mutation,
		private readonly string $position,
		private readonly Translator $translator,
		private readonly BannerLocalizationModel $bannerLocalizationModel,
		private readonly ShowBannerHolder $showBannerHolder,
	)
	{
		$this->onAnchor[] = [$this, 'init'];
	}


	public function init(): void
	{
	}

	public function render(): void
	{
		$bannerLocalization = $this->bannerLocalizationModel->getByPositionNotUsed($this->position);

		if ($bannerLocalization !== null) {

			$template = $this->template;
			$template->setTranslator($this->translator);
			$template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
			$template->add('templates', RS_TEMPLATE_DIR);
			$template->add('position', $this->position);
			$template->add('showBannerHolder', $this->showBannerHolder);
			$template->add('bannerLocalization', $bannerLocalization);
			$template->add('mutation', $this->mutation);

			$this->showBannerHolder->addShownBannerLocalization($bannerLocalization);

			$template->render(__DIR__ . '/' . $bannerLocalization->getParent()->type . '.latte');
		}
	}

}
