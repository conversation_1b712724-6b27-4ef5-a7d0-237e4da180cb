<?php

namespace App\PostType\Banner\FrontModule\Components\BannerPosition;

use App\PostType\Banner\Model\Orm\BannerLocalization;

class ShowBannerHolder
{

	private array $shownBannerLocalizations = [];

	public function __construct()
	{
	}

	public function getShownBannerLocalizations(): array
	{
		return $this->shownBannerLocalizations;
	}

	public function addShownBannerLocalization(BannerLocalization $bannerLocalization): void
	{
		$this->shownBannerLocalizations[$bannerLocalization->id] = $bannerLocalization;
	}

}
