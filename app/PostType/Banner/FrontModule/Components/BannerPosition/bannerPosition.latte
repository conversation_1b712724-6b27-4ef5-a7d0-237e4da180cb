{default $class = 'u-mb-sm'}
{default $btn = true}
{default $btnText = (isset($bannerLocalization->cf->bannerCommon->buttonText) ? $bannerLocalization->cf->bannerCommon->buttonText : '')}
{default $btnLink = (isset($bannerLocalization->cf->bannerCommon->buttonLink) ? $bannerLocalization->cf->bannerCommon->buttonLink : '')}
{default $label = (isset($bannerLocalization->cf->bannerCommon->label) ? $bannerLocalization->cf->bannerCommon->label : '')}
{default $title = (isset($bannerLocalization->cf->bannerCommon->title) ? $bannerLocalization->cf->bannerCommon->title : '')}

{var $type = $bannerLocalization->getParent()->type}


<div n:class="b-bnr, $class, $type ? 'b-bnr--' . $type, link-mask">
	<p n:if="$type == 'img-sm' || $type == 'img-lg'" class="b-bnr__img img">
		{if isset($bannerLocalization->cf->base->image)
			&& $bannerLocalization->cf->base->image instanceof App\Model\CustomField\LazyValue
			&& $image = $bannerLocalization->cf->base->image->getEntity()
		}
			<img src="{$image->getSize('lg')->src}" alt="{$image->getAlt($mutation)}" loading="lazy">
		{else}
			<img src="/static/img/illust/noimg.svg" alt="" loading="lazy">
		{/if}
	</p>
	<div class="b-bnr__inner u-mb-last-0">
		<p n:if="$label" class="b-bnr__label label">
			{$label}
		</p>
		<h2 n:if="$title" n:class="b-bnr__title, $type == 'broker' ? h2 : h3">
			{$title|texy|noescape}
		</h2>
		{block bannerContant}{/block}

		{var $linkItem = $bannerLocalization->cf->bannerCommon->linkChoose ?? null}

		<p n:if="$btn" class="b-bnr__btn">
			{if $linkItem && isset($linkItem->internal->page)}
				<a href="{plink $linkItem->internal->page}" n:ifcontent n:class="btn, btn--lg, $type != 'img-lg' ? btn--full, link-mask__link">
					<span n:class="btn__text, $bannerLocalization->cf->bannerCommon->hideBtn ?? false ? 'u-d-n' : false">
						{if isset($linkItem->internal->text)}
							{$linkItem->internal->text}
						{else}
							{$linkItem->internal->page->nameAnchor}
						{/if}
					</span>
				</a>
			{elseif isset($linkItem->external->page)}
				<a href="{$linkItem->external->page}" n:class="btn, btn--lg, $type != 'img-lg' ? btn--full, link-mask__link" target="_blank">
					<span n:class="btn__text, $bannerLocalization->cf->bannerCommon->hideBtn ?? false ? 'u-d-n' : false">
						{$linkItem->external->text ?? false}
					</span>
				</a>
			{/if}
		</p>

	</div>
</div>
