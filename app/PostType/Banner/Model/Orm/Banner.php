<?php

namespace App\PostType\Banner\Model\Orm;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\JsonContainer; // phpcs:ignore
use App\Model\Orm\ImageEntity;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Traits\HasConsts;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property int $id {primary}
 * @property string $internalName {default ''}
 * @property string $type {enum self::TYPE_*} {default self::TYPE_DEFAULT}
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 *
 * RELATIONS
 * @property OneHasMany<BannerLocalization> $localizations {1:m BannerLocalization::$banner}
 * @property ManyHasMany<BannerPosition> $positions {m:m BannerPosition::$banners, isMain=true}
 *
 * VIRTUAL
 * @property ArrayHash|null $cf {virtual}
 */
class Banner extends BaseEntity implements ParentEntity
{

	use HasCustomFields;
	use HasConsts;

	public const string TYPE_DEFAULT = 'default';
	public const string TYPE_IMG_LARGE = 'img-lg';
	public const string TYPE_IMG_SMALL = 'img-sm';
	public const string TYPE_QUOTE = 'quote';

	public function getInternalName(): string
	{
		return $this->internalName;
	}

	public function setInternalName(string $internalName): void
	{
		$this->internalName = $internalName;
	}

	/**
	 * @return ICollection<BannerLocalization>
	 */
	public function getLocalizations(): ICollection
	{
		return $this->localizations->toCollection();
	}

	public function getLocalization(Mutation $mutation): LocalizationEntity
	{
		$localization = $this->getLocalizations()->getByChecked(['mutation' => $mutation]);
		assert($localization instanceof BannerLocalization);

		return $localization;
	}

	public function getFirstImage(): ImageEntity|null
	{
		return isset($this->cf->settings->mainImage) ? $this->cf->settings->mainImage->getEntity() : null;
	}

}
