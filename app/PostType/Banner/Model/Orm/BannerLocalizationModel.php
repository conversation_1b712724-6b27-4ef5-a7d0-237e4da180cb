<?php

namespace App\PostType\Banner\Model\Orm;

use App\PostType\Banner\FrontModule\Components\BannerPosition\ShowBannerHolder;

class BannerLocalizationModel
{

	private array $cache = [];

	public function __construct(
		private readonly BannerLocalizationTreeRepository $bannerLocalizationTreeRepository,
		private readonly BannerLocalizationRepository $bannerLocalizationRepository,
		private readonly ShowBannerHolder $showBannerHolder,
	)
	{
	}

	/**
	 * @param array<int, int> $treeIds
	 */
	public function setCategoriesByIds(BannerLocalization $bannerLocalization, array $treeIds): void
	{
		$currentBannerLocalizationTrees = $this->bannerLocalizationTreeRepository->findBy([
			'bannerLocalization' => $bannerLocalization,
		])->fetchPairs('tree->id');

		$sort = 0;

		foreach ($treeIds as $treeId) {
			if (isset($currentBannerLocalizationTrees[$treeId])) {
				// update sort
				$bannerLocalizationTree = $currentBannerLocalizationTrees[$treeId];
				assert($bannerLocalizationTree instanceof BannerLocalizationTree);
				$bannerLocalizationTree->sort = $sort;
				// unset array
				unset($currentBannerLocalizationTrees[$treeId]);
			} else {
				// create new
				$bannerLocalizationTree = new BannerLocalizationTree();
				$bannerLocalizationTree->bannerLocalization = $bannerLocalization;
				$bannerLocalizationTree->tree = $treeId;
				$bannerLocalizationTree->sort = $sort;
			}
			$sort++;
		}

		// remove old
		if ($currentBannerLocalizationTrees !== []) {
			foreach ($currentBannerLocalizationTrees as $currentBlogLocalizationTree) {
				$this->bannerLocalizationTreeRepository->remove($currentBlogLocalizationTree);
			}
		}
	}

	public function getByPositionNotUsed(string $position, array $ignoredBannerLocalizations = []): ?BannerLocalization
	{
		if (!isset($this->cache[$position])) {
			$conditions = $this->bannerLocalizationRepository->getPublicOnlyWhereParams();
			$conditions['banner->positions->name'] = $position;
			$ignoredIds = array_keys($this->showBannerHolder->getShownBannerLocalizations());

			foreach ($ignoredBannerLocalizations as $ignoredBannerLocalization) {
				assert($ignoredBannerLocalization instanceof BannerLocalization);
				$ignoredIds[] = $ignoredBannerLocalization->id;
			}

			if ($ignoredIds !== []) {
				$conditions['id!='] = $ignoredIds;
			}

			/**
			 * @todo - add conditions for banner localization trees
			 */

			$this->cache[$position] = $this->bannerLocalizationRepository
				->findRandom()
				->findBy($conditions)
				->fetch();
		}
		return $this->cache[$position];
	}

}
