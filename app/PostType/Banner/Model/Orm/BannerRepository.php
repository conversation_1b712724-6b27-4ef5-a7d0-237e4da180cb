<?php

namespace App\PostType\Banner\Model\Orm;

use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method Banner getById($id)
 * @method ICollection<Banner> searchByName(string $q, array $excluded = [])
 * @extends Repository<Banner>
 */
class BannerRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [Banner::class];
	}

}
