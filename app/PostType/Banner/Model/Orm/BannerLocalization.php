<?php

namespace App\PostType\Banner\Model\Orm;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\JsonContainer; // phpcs:ignore
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Traits\HasCustomContent;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\Core\Model\Editable;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Core\Model\Publishable;
use App\PostType\Core\Model\Validatable;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Page\Model\Orm\TreeRepository;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property int $id {primary}
 * @property string $name  {default ''}
 * @property bool $public {default false}
 * @property DateTimeImmutable|null $publicFrom {default now}
 * @property DateTimeImmutable|null $publicTo {default '+100 year'}
 * @property DateTimeImmutable|null $editedTime
 * @property int|null $edited
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 * @property ArrayHash $customContentJson {container JsonContainer}
 *
 * RELATIONS
 * @property Mutation $mutation {m:1 Mutation, oneSided=true}
 * @property Banner $banner {m:1 Banner::$localizations}
 * @property OneHasMany<BannerLocalizationTree> $bannerLocalizationTrees {1:m BannerLocalizationTree::$bannerLocalization, orderBy=[sort=ASC], cascade=[persist, remove]}
 *
 * VIRTUAL
 * @property ArrayHash|null $cf {virtual}
 * @property ArrayHash|null $cc {virtual}
 * @property-read ICollection< Tree> $trees {virtual}
 */
class BannerLocalization extends BaseEntity implements LocalizationEntity, Publishable, Validatable, Editable
{

	use HasCustomFields;
	use HasCustomContent;

	private TreeRepository $treeRepository;

	public function injectRepository(
		TreeRepository $treeRepository,
	): void
	{
		$this->treeRepository = $treeRepository;
	}

	public function getId(): int
	{
		return $this->id;
	}

	public function getName(): string
	{
		return $this->name;
	}

	public function setName(string $name): void
	{
		$this->name = $name;
	}

	public function getIsPublic(): bool
	{
		return $this->public;
	}

	public function setIsPublic(bool $isPublic): void
	{
		$this->public = $isPublic;
	}

	public function getPublicFrom(): ?DateTimeImmutable
	{
		return $this->publicFrom;
	}

	public function getPublicTo(): ?DateTimeImmutable
	{
		return $this->publicTo;
	}

	public function setPublicFrom(?DateTimeImmutable $publicFrom): void
	{
		$this->publicFrom = $publicFrom;
	}

	public function setPublicTo(?DateTimeImmutable $publicTo): void
	{
		$this->publicTo = $publicTo;
	}

	public function setEditorId(int $id): void
	{
		$this->edited = $id;
	}

	public function setEditedTime(DateTimeImmutable $editedTime): void
	{
		$this->editedTime = $editedTime;
	}

	public function getMutation(): Mutation
	{
		return $this->mutation;
	}


	public function setMutation(Mutation $mutation): void
	{
		$this->mutation = $mutation;
	}

	public function getParent(): Banner
	{
		return $this->banner;
	}

	public function setParent(ParentEntity $parentEntity): void
	{
		assert($parentEntity instanceof Banner);
		$this->banner = $parentEntity;
	}

	/**
	 * @return ICollection<Tree>
	 */
	protected function getterTrees(): ICollection
	{
		$treeIds = $this->bannerLocalizationTrees->toCollection()
			->fetchPairs(null, 'tree->id');

		if ($treeIds === []) {
			/** @var EmptyCollection<Tree> $emptyCollection */
			$emptyCollection = new EmptyCollection();

			return $emptyCollection;
		}

		return $this->treeRepository->findFilteredPages($treeIds)->findBy(['mutation' => $this->getMutation()]);
	}

	protected function getterPath(): array
	{
		$categoryIds = $this->bannerLocalizationTrees->toCollection()
			->fetchPairs(null, 'tree->id');
		if ($categoryIds === []) {
			return [];
		}

		$path = [];
		$mainCategory = $this->treeRepository->findFilteredPages($categoryIds)
			->findBy([
				'mutation' => $this->getMutation(),
			])
			->fetch();

		if ($mainCategory !== null) {
			$mainPath = $mainCategory->path;
			if ($mainPath !== null) {
				$path = $mainPath;
				$path[] = $mainCategory->id;
			}
		}

		return $path;
	}

}
