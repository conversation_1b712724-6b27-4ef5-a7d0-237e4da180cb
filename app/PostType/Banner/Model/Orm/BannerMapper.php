<?php

namespace App\PostType\Banner\Model\Orm;

use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<Banner>
 */
class BannerMapper extends DbalMapper
{

	use HasCamelCase;

	public function getTableName(): string
	{
		return 'banner';
	}

	/**
	 * @return ICollection<Banner>
	 */
	public function searchByName(string $q, array $excluded = []): ICollection
	{
		$builder = $this->builder()
			->andWhere('internalName LIKE %_like_', $q);

		if (!empty($excluded)) {
			$builder->andWhere('id not in %i[]', $excluded);
		}

		return $this->toCollection($builder);
	}

}
