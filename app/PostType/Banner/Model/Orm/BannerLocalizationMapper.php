<?php

namespace App\PostType\Banner\Model\Orm;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Traits\HasCamelCase;
use App\PostType\Page\Model\Orm\CommonTree;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<BannerLocalization>
 */
class BannerLocalizationMapper extends DbalMapper
{

	use HasCamelCase;

	/**
	 * @return literal-string
	 */
	public function getTableName(): string
	{
		return 'banner_localization';
	}

	/**
	 * @return ICollection<BannerLocalization>
	 */
	public function searchByName(string $q, array $excluded = []): ICollection
	{
		$builder = $this->builder()
			->andWhere('name LIKE %_like_', $q);

		if ($excluded !== []) {
			$builder->andWhere('id not in %i[]', $excluded);
		}

		return $this->toCollection($builder);
	}

	/**
	 * @param int[] $ids
	 * @return ICollection<BannerLocalization>
	 */
	public function findFiltered(array $ids): ICollection
	{
		$builder = $this->builder()
			->select('cl.*')
			->from($this->getTableName(), 'cl')
			->andWhere('cl.id in %i[]', $ids)
			->orderBy('%raw', 'FIELD(cl.id, ' . implode(',', $ids) . ')');

		return $this->toCollection($builder);
	}

	public function findAllIdsInMutation(Mutation $mutation, ?int $limit): Result
	{
		$builder = $this->builder()
			->select('bl.id')
			->from($this->getTableName(), 'bl')
			->andWhere('bl.mutationId = %i', $mutation->id)
			->limitBy($limit);

		return $this->connection->queryByQueryBuilder($builder);
	}

	/**
	 * @return ICollection<BannerLocalization>
	 */
	public function findByIdInPathString(CommonTree $commonTree): ICollection
	{
		$builder = $this->builder()
			->andWhere('pathString LIKE %_like_', '|' . $commonTree->id . '|');

		return $this->toCollection($builder);
	}

	/**
	 * @return ICollection<BannerLocalization>
	 */
	public function findRandom(): ICollection
	{
		$builder = $this->builder()
			->orderBy('RAND()');

		return $this->toCollection($builder);
	}

}
