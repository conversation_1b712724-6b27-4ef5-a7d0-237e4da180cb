<?php

namespace App\PostType\Banner\Model\Orm;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Traits\HasPublicParameter;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method BannerLocalization|null getById($id)
 * @method ICollection<BannerLocalization> searchByName(string $q, array $excluded = [])
 * @method ICollection<BannerLocalization> findRandom()
 * @extends Repository<BannerLocalization>
 */
class BannerLocalizationRepository extends Repository
{

	use HasPublicParameter;

	public static function getEntityClassNames(): array
	{
		return [BannerLocalization::class];
	}

	public function getPublicOnlyWhereParams(): array
	{
		$now = $this->getNowDateTime();

		return [
			'public' => 1,
			'publicFrom<=' => $now,
			'publicTo>=' => $now,
		];
	}

	public function findAllIdsInMutation(Mutation $mutation, ?int $limit = null): Result
	{
		$mapper = $this->mapper;
		assert($mapper instanceof BannerLocalizationMapper);

		return $mapper->findAllIdsInMutation($mutation, $limit);
	}

}
