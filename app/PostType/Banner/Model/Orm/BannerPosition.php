<?php

namespace App\PostType\Banner\Model\Orm;

use App\Model\Orm\BaseEntity;
use Nextras\Orm\Relationships\ManyHasMany;

/**
 * @property int $id {primary}
 * @property string $name {enum self::NAME_*} {default self::NAME_BLOG}
 *
 * RELATIONS
 * @property ManyHasMany<Banner> $banners {m:m Banner::$positions}
 */
class BannerPosition extends BaseEntity
{

	public const NAME_BLOG = 'blog';
	public const NAME_BLOG_DETAIL = 'blogDetail';
	public const NAME_HERO = 'hero';
	public const NAME_PRODUCT_LIST = 'productList';
	public const NAME_SIDE = 'side';
	public const NAME_SKYSCRAPER = 'skyscraper';

}
