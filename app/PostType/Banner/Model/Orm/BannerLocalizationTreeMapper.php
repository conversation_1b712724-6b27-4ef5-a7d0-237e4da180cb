<?php declare(strict_types = 1);

namespace App\PostType\Banner\Model\Orm;

use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Orm\Mapper\Dbal\Conventions\Conventions;
use Nextras\Orm\Mapper\Dbal\Conventions\IConventions;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<BannerLocalizationTree>
 */
class BannerLocalizationTreeMapper extends DbalMapper
{

	use HasCamelCase;

	/**
	 * @return literal-string
	 */
public function getTableName(): string
{
	return 'banner_localization_tree';
}

	protected function createConventions(): IConventions
	{
		$conventions = parent::createConventions();
		assert($conventions instanceof Conventions);

		return $conventions;
	}

}
