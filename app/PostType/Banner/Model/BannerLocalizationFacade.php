<?php

namespace App\PostType\Banner\Model;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\PostType\Banner\Model\Orm\Banner;
use App\PostType\Banner\Model\Orm\BannerLocalization;
use App\PostType\Core\Model\EntityLocalizationFacade;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;

class BannerLocalizationFacade implements EntityLocalizationFacade
{

	public function __construct(
		private Orm $orm,
	)
	{
	}

	public function create(Mutation $mutation, ?ParentEntity $localizableEntity): LocalizationEntity
	{
		$bannerLocalization = new BannerLocalization();
		$this->orm->bannerLocalization->attach($bannerLocalization);
		$bannerLocalization->mutation = $mutation;

		if ($localizableEntity === null) {
			$localizableEntity = new Banner();
			$bannerLocalization->banner = $localizableEntity;
		} else {
			assert($localizableEntity instanceof Banner);
			$bannerLocalization->banner = $localizableEntity;
		}

		$this->orm->persistAndFlush($bannerLocalization);

		return $bannerLocalization;
	}

	public function remove(LocalizationEntity $localizableEntity): void
	{
		assert($localizableEntity instanceof BannerLocalization);

		$parent = $localizableEntity->getParent();
		assert($parent instanceof Banner);
		$this->orm->bannerLocalization->remove($localizableEntity);

		if ($parent->getLocalizations()->count() === 0) {
			$this->orm->bannerLocalization->remove($localizableEntity);
		}

		$this->orm->flush();
	}

}
