<?php declare(strict_types = 1);

namespace App\PostType\SystemMessage\AdminModule\Components\SystemMessagesList;

use App\Model\Translator;
use App\PostType\Core\AdminModule\Components\DataGrid\Definition\DataGridDefinition;
use App\PostType\Core\AdminModule\Components\DataGrid\Definition\Extenders\CustomDataGridExtender;
use App\PostType\SystemMessage\Model\Orm\SystemMessage;
use Ublaboo\DataGrid\DataGrid;

class SystemMessagesPrescription
{

	public function __construct(private readonly Translator $translator)
	{
	}

	public function get(): DataGridDefinition
	{
		$dataGrid = new DataGrid();

		return new DataGridDefinition(
			dataGrid: $dataGrid,
			extenders: [
				new CustomDataGridExtender(
					improveFunction: $this->reset(...)
				),

				new CustomDataGridExtender(
					improveFunction: $this->addInternalName(...)
				),
				new CustomDataGridExtender(
					improveFunction: $this->addValidity(...)
				),
				new CustomDataGridExtender(
					improveFunction: $this->addPosition(...)
				),
				new CustomDataGridExtender(
					improveFunction: $this->addClosable(...)
				),
				new CustomDataGridExtender(
					improveFunction: $this->addPublish(...)
				),
			]
		);
	}

	private function reset(DataGrid $dataGrid): void
	{
		$dataGrid->removeColumn('name');
		$dataGrid->removeFilter('name');
	}

	private function addInternalName(DataGrid $dataGrid): void
	{
		$dataGrid->addColumnText('internalName', 'internalName', 'internalName')->setSortable()->setFilterText();
	}

	private function addValidity(DataGrid $dataGrid): void
	{
		$dataGrid->addColumnDateTime('publicFrom', 'publicFrom');
		$dataGrid->addColumnDateTime('publicTo', 'publicTo');
	}

	private function addPosition(DataGrid $dataGrid): void
	{
		$positions = SystemMessage::getConstsByPrefix('POSITION_', 'system_message_position_');
		//translate
		foreach ($positions as $key => $value) {
			$positions[$key] = $this->translator->translate($value);
		}
		//add null position
		$positions[null] = $this->translator->translate('all');

		$dataGrid->addColumnText('position', 'position')->setReplacement($positions);
	}

	private function addPublish(DataGrid $dataGrid): void
	{
		$dataGrid->addColumnText('isPublic', 'isPublic')->setReplacement([
			0 => $this->translator->translate('no'),
			1 => $this->translator->translate('yes'),
		]);
	}

	private function addClosable(DataGrid $dataGrid): void
	{
		$dataGrid->addColumnText('closable', 'closable')->setReplacement([
			0 => $this->translator->translate('no'),
			1 => $this->translator->translate('yes'),
		]);
	}

}
