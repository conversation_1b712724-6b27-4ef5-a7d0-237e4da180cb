<?php declare(strict_types = 1);

namespace App\PostType\SystemMessage\AdminModule\Components\SystemMessageForm;

use App\Model\Orm\User\User;
use App\Model\Translator;
use App\PostType\Core\AdminModule\Components\Form\Form as CoreForm;
use App\PostType\SystemMessage\Model\Orm\SystemMessage;
use App\PostType\SystemMessage\Model\Orm\SystemMessageModel;
use App\PostType\SystemMessage\Model\SystemMessageFacade;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nette\Utils\Strings;

class SystemMessageForm extends Control
{

	public function __construct(private readonly SystemMessage $systemMessage, private readonly User $user, private readonly Translator $translator, private readonly SystemMessageModel $systemMessageModel, private readonly SystemMessageFacade $systemMessageFacade)
	{
	}

	public function render(): void
	{
		$template = $this->getTemplate();
		$template->setTranslator($this->translator);
		$template->add('corePartsDirectory', CoreForm::TEMPLATE_PARTS_DIR);      				// @phpstan-ignore-line
		$template->add('templates', RS_TEMPLATE_DIR);                            				// @phpstan-ignore-line
		$template->add('entity', $this->systemMessage);                                				// @phpstan-ignore-line
		$template->add('fileUploadLink', $this->presenter->link(':Admin:File:upload')); 	// @phpstan-ignore-line
		$template->add('mutation', $this->systemMessage->mutation);									// @phpstan-ignore-line

		$template->setFile(__DIR__ . '/systemMessageForm.latte');
		$template->render();
	}

	protected function createComponentSystemMessageForm(): Form
	{
		$form = new Form();
		$form->addCheckbox('isPublic', 'isPublic');
		$form->addCheckbox('isUpperPosition', 'isUpperPosition');
		$form->addText('internalName', 'label_internalName');
		$form->addTextArea('description', 'description');

		$positions = SystemMessage::getConstsByPrefix('POSITION_', 'system_message_position_');
		//translate
		foreach ($positions as $key => $value) {
			$positions[$key] = $this->translator->translate($value);
		}
		$form->addSelect('position', 'position', $positions)
			->setPrompt($this->translator->translate('all'));
		$form->addCheckbox('closable', 'closable');
		$form->addSubmit('send', 'send');
		$form->addContainer('setup')->addHidden('cf');

		$this->addValidity($form);
		$this->addPublish($form);

		$form->setDefaults($this->systemMessage->toArray());

		$form->addHidden('editedBy', $this->user->id)->addRule(\Nette\Forms\Form::Integer);

		$form->onValidate[] = $this->formValidate(...);
		$form->onSuccess[] = $this->formSuccess(...);
		$form->onError[] = $this->formError(...);

		return $form;
	}

	private function formValidate(Form $form, SystemMessageFormData $systemMessageFormData): void
	{
		if (Strings::length($systemMessageFormData->internalName) > 50) {
			$form->addError($this->translator->translate('internal_name_too_long'));
		}
	}

	private function formError(): void
	{
		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	private function formSuccess(Form $form, SystemMessageFormData $systemMessageFormData): void
	{
		$this->systemMessageModel->update($this->systemMessage, $systemMessageFormData);
	}

	public function handleDelete(): void
	{
		$this->systemMessageFacade->remove($this->systemMessage);
		$this->presenter->redirect('default');
	}

	private function addPublish(Form $form): void
	{
		$publishContainer = $form->addContainer('publish');
		$publishContainer->addCheckbox('public', 'public')->setDefaultValue($this->systemMessage->isPublic);
	}

	private function addValidity(Form $form): void
	{
		$container = $form->addContainer('validity');

		$container->addText('publicFrom', 'publicFrom')
			->setDefaultValue($this->systemMessage->publicFrom?->format('Y-m-d\TH:i'));

		$container->addText('publicTo', 'publicTo')
			->setDefaultValue($this->systemMessage->publicTo?->format('Y-m-d\TH:i'));
	}

}
