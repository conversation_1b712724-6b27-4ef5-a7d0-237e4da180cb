{var $anchorName = 'content'}
{var $icon = $templates . '/part/icons/align-left.svg'}
{var $title = 'Obsah'}

{var $props = [
	title: $title,
	id: $anchorName,
	icon: $icon,
	variant: 'main',
	open: true,
	classes: ['u-mb-xxs'],
]}


{embed $templates . '/part/box/toggle.latte', props => $props, templates => $templates}
	{block content}
		{include $templates . '/part/core/inp.latte', props: [
			input: $form['internalName'],
			classLabel: ['title'],
			label: $form['internalName']->name
		]}

		{include $templates.'/part/core/inp.latte' props: [
			label: 'description',
			input: $form['description'],
			classesLabel: ['title'],
			type: 'textarea',
			rows: 20,
			dataInp: [
				controller: 'Tiny',
				tiny-type: 'lite',
				tiny-target: 'item',
			]
		]}

		{include $corePartsDirectory . '/content/custom-fields.latte',
			form => $form,
			cfObject => $entity,
			title => 'Jazykově závislé informace',
			containerName => 'setup',
			itemName => 'cf'
		}
	{/block}
{/embed}
