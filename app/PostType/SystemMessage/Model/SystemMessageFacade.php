<?php declare(strict_types = 1);

namespace App\PostType\SystemMessage\Model;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\PostType\Core\Model\EntityFacade;
use App\PostType\SystemMessage\Model\Orm\SystemMessage;

class SystemMessageFacade implements EntityFacade
{

	public function __construct(private readonly Orm $orm)
	{
	}

	public function create(Mutation $mutation, BaseEntity|null $entity): BaseEntity
	{
		$systemMessage = new SystemMessage();

		$systemMessage->mutation = $mutation;
		$this->orm->persistAndFlush($systemMessage);

		return $systemMessage;
	}

	public function remove(BaseEntity $entity): void
	{
		$this->orm->remove($entity);
		$this->orm->flush();
	}

}
