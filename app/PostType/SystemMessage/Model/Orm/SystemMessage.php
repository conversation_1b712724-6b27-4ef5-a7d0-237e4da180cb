<?php declare(strict_types = 1);

namespace App\PostType\SystemMessage\Model\Orm;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Traits\HasConsts;
use App\Model\Orm\Traits\HasCustomFields;
use App\Model\Orm\User\User;
use Nette\Utils\ArrayHash;
use App\Model\Orm\JsonContainer;		// phpcs:ignore
use Nextras\Dbal\Utils\DateTimeImmutable;

/**
 * @property int                    $id               {primary}
 * @property string                 $internalName     {default ''}
 * @property ArrayHash              $customFieldsJson {container JsonContainer}
 * @property bool                   $isPublic         {default false}
 * @property DateTimeImmutable|null $publicFrom       {default null}
 * @property DateTimeImmutable|null $publicTo         {default null}
 * @property DateTimeImmutable|null $editedTime       {default null}
 *
 * @property string                 $description      {default ''}
 * @property bool                   $isUpperPosition  {default false}
 * @property string|null            $position         {default null}
 * @property bool                   $closable         {default false}
 *
 * Relations
 * @property User|null              $editedBy         {m:1 User::$systemMessages}
 * @property Mutation               $mutation         {m:1 Mutation::$systemMessages}
 *
 * VIRTUAL
 * @property ArrayHash|null         $cf                {virtual}
 */
class SystemMessage extends BaseEntity
{

	use HasConsts;
	use HasCustomFields;

	public const string POSITION_ORDER = 'order';

	public const string POSITION_WEB = 'web';

	public function getId(): int
	{
		return $this->id;
	}

}
