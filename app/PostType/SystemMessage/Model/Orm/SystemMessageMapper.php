<?php declare(strict_types = 1);

namespace App\PostType\SystemMessage\Model\Orm;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Traits\HasCamelCase;
use Nette\Utils\DateTime;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<SystemMessage>
 */
class SystemMessageMapper extends DbalMapper
{

	use HasCamelCase;

	/**
	 * @return literal-string
	 */
public function getTableName(): string
{
	return 'system_message';
}

	/**
	 * @return ICollection<SystemMessage>
	 * @throws \Exception
	 */
	public function findAllPublic(): ICollection
	{
		$dateActual = DateTime::from('now');

		$builder = $this->builder()
			->where('isPublic = %b', true)
			->andWhere('publicFrom<=%dt OR publicFrom is null', $dateActual)
			->andWhere('publicTo>=%dt OR publicTo is null', $dateActual)
			->orderBy('id ASC');

		return $this->toCollection($builder);
	}

	/**
	 * @return ICollection<SystemMessage>
	 * @throws \Exception
	 */
	public function findPublicInMutation(Mutation $mutation, string $position, bool $closable): ICollection
	{
		$dateActual = DateTime::from('now');

		$builder = $this->builder()
			->where('isPublic = %b', true)
			->andWhere('publicFrom<=%dt OR publicFrom is null', $dateActual)
			->andWhere('publicTo>=%dt OR publicTo is null', $dateActual)
			->andWhere('mutationId = %i', $mutation->id)
			->andWhere('closable = %b', $closable)
			->orderBy('id ASC');

		if ($position !== '') {
			$builder->andWhere('position = %s OR position is null', $position);
		}

		return $this->toCollection($builder);
	}

}
