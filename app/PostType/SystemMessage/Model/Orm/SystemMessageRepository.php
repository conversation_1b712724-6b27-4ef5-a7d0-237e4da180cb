<?php declare(strict_types = 1);

namespace App\PostType\SystemMessage\Model\Orm;

use App\Model\Orm\Mutation\Mutation;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method ICollection<SystemMessage> findAllPublic()
 * @method ICollection<SystemMessage> findPublicInMutation(Mutation $mutation, string $position, bool $closable)
 * @extends Repository<SystemMessage>
 */
class SystemMessageRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [SystemMessage::class];
	}

}
