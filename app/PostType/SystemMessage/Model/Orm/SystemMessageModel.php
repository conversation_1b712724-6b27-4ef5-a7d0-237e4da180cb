<?php declare(strict_types = 1);

namespace App\PostType\SystemMessage\Model\Orm;

use App\Model\CustomField\CustomFields;
use App\Model\Orm\Orm;
use App\PostType\SystemMessage\AdminModule\Components\SystemMessageForm\SystemMessageFormData;
use Nette\Utils\ArrayHash;
use Nette\Utils\DateTime;

class SystemMessageModel
{

	public function __construct(private readonly Orm $orm, private readonly CustomFields $customFields)
	{
	}

	public function update(SystemMessage $systemMessage, SystemMessageFormData $systemMessageFormData): SystemMessage
	{
		$systemMessage->internalName = $systemMessageFormData->internalName;
		$systemMessage->description = $systemMessageFormData->description;
		$systemMessage->isPublic = $systemMessageFormData->isPublic;
		$systemMessage->isUpperPosition = $systemMessageFormData->isUpperPosition;
		$systemMessage->closable = $systemMessageFormData->closable;
		$systemMessage->position = $systemMessageFormData->position;
		$systemMessage->publicFrom = $systemMessageFormData->validity->publicFrom ?: null;
		$systemMessage->publicTo = $systemMessageFormData->validity->publicTo ?: null;
		$systemMessage->editedBy = $this->orm->user->getById($systemMessageFormData->editedBy);
		$systemMessage->editedTime = DateTime::from('now')->format('c');

		if (isset($systemMessage->cf)) {
			if (isset($systemMessageFormData->setup->cf) && $systemMessageFormData->setup->cf !== '') {
				$systemMessage->setCf($this->customFields->prepareDataToSave($systemMessageFormData->setup->cf));
			} else {
				$systemMessage->setCf(new ArrayHash());
			}
		}

		$this->orm->persistAndFlush($systemMessage);

		return $systemMessage;
	}

}
