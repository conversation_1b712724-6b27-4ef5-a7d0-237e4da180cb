<?php declare(strict_types=1);

namespace App\PostType\Core\Model;

/**
 * Entity has expanded publication settings represented by constants PUBLIC_*
 */
interface ExpandedPublishable extends Publishable
{
	public const int PUBLIC_NOT_PUBLISHED = 0;
	public const int PUBLIC_PUBLISHED = 1;
	public const int PUBLIC_FOR_REGISTERED = 2;
	public const int PUBLIC_FOR_VERIFIED = 3;
	public const int PUBLIC_FOR_PAYED_MEMBERS = 4;

	/**
	 * Is this entity only to be viewed by registered users?
	 *
	 * @return bool
	 */
	public function isForRegistered(): bool;

	/**
	 * Is this entity only to be viewed by verified users?
	 *
	 * @return bool
	 */
	public function isForVerified(): bool;

	/**
	 * @return bool
	 * @todo phase 2
	 *
	 */
	public function isForPayedMembers(): bool;


}
