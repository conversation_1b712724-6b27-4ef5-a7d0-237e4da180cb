<?php declare(strict_types=1);

namespace App\PostType\Core\Model;

use Nextras\Dbal\Utils\DateTimeImmutable;

/**
 * Implementations for:
 * @see Publishable
 *
 * Trait has also conditional logic for:
 * @see Validatable
 * @see ExpandedPublishable
 */
trait HasPublishable
{
	public function setPublic(bool $public): void
	{
		$this->public = $public ? ExpandedPublishable::PUBLIC_PUBLISHED : ExpandedPublishable::PUBLIC_NOT_PUBLISHED;
	}

	public function getPublic(): bool
	{
		return boolval($this->public);
	}

	public function isPublic(): bool
	{
		$public = boolval($this->public);
		if (!$public) {
			return false;
		}

		if ($this instanceof Validatable) {
			$now = new DateTimeImmutable();

			$publicFrom = $this->getPublicFrom();
			if ($publicFrom !== null && $publicFrom > $now) {
				return false;
			}
			$publicTo = $this->getPublicTo();
			if ($publicTo !== null && $publicTo < $now) {
				return false;
			}
		}

		return true;
	}

	public function isForRegistered(): bool
	{
		return $this instanceof ExpandedPublishable && $this->public >= ExpandedPublishable::PUBLIC_FOR_REGISTERED;
	}

	public function isForVerified(): bool
	{
		return $this instanceof ExpandedPublishable && $this->public >= ExpandedPublishable::PUBLIC_FOR_VERIFIED;
	}

	public function isForPayedMembers(): bool
	{
		return $this instanceof ExpandedPublishable && $this->public >= ExpandedPublishable::PUBLIC_FOR_PAYED_MEMBERS;
	}
}
