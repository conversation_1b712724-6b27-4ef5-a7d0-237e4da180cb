parameters:
#	config:
#		blog:
#			paging: 10
#
#cf:
#	templates:
#		"Blog:detail":
#			annotation: @cf.definitions.annotation
#			annotationDetail:
#				type: textarea
#				label: "Anotace pro detail"
#			mainImage:
#				type: image # TODO size
#				label: "Hlavn<PERSON> obrázek"
#			timeToRead:
#				type: text
#				label: "Délka článku v min."

services:

	- App\PostType\Core\AdminModule\Components\DataGrid\DataGridFactory
	- App\PostType\Core\AdminModule\Components\DbalDataGrid\DbalDataGridFactory
	- App\PostType\Core\AdminModule\Components\ShellForm\ShellFormFactory
	- App\PostType\Core\AdminModule\Components\Form\FormFactory
	- App\PostType\Core\AdminModule\Components\Form\Builder
	- App\PostType\Core\AdminModule\Components\Form\Handler

	- App\PostType\Core\AdminModule\Components\Form\Definition\RelationInfoFactory

#	- App\PostType\Blog\AdminModule\Components\Form\Builder
#	- App\PostType\Blog\AdminModule\Components\Form\Handler
#	- App\Model\BlogLocalizationModel
#	- App\Model\BlogLocalizationFacade
#	- App\PostType\Blog\AdminModule\Components\Form\FormFactory
#
#	-
#		implement: App\PostType\Blog\AdminModule\Components\Form\ShellFormFactory
#		inject: true
#
#	-
#		implement: SuperKoderi\Front\Components\Blog\IAttachedBlogsFactory
#		inject: true
