<?php declare(strict_types = 1);

namespace App\PostType\Core\AdminModule\Components\DbalDataGrid\DataSource;


use Nextras\Dbal\Connection;
use Nextras\Dbal\QueryBuilder\QueryBuilder;
use Nextras\Dbal\Result\Result;
use Nextras\Dbal\Result\Row;
use Ublaboo\DataGrid\DataSource\IDataSource;
use Ublaboo\DataGrid\Filter\FilterSelect;
use Ublaboo\DataGrid\Filter\FilterText;
use Ublaboo\DataGrid\Utils\Sorting;

class DbalDataSource implements IDataSource
{

	protected QueryBuilder $queryBuilder;

	/**
	 * @param literal-string $tableName
	 */
	public function __construct(
		protected Connection $connection,
		protected string $tableName,

	)
	{
		$this->queryBuilder = $this->connection->createQueryBuilder()->from($this->getTableName());
	}

	/**
	 * @return literal-string
	 */
	protected function getTableName(): string
	{
		return $this->tableName;

	}

	public function getCount(): int
	{
		$queryBuilderCount = clone $this->queryBuilder;
		$queryBuilderCount->select('COUNT(id) AS num');
		return $this->connection->queryByQueryBuilder($queryBuilderCount)->fetch()->num ?? 0;
	}

	public function getData(): Result
	{
		return $this->connection->queryByQueryBuilder($this->queryBuilder);
	}

	public function getFirstItem(): ?Row
	{
		$queryBuilderCount = clone $this->queryBuilder;
		$queryBuilderCount->limitBy(1);
		return $this->connection->queryByQueryBuilder($queryBuilderCount)->fetch();
	}

	public function filter(array $filters): void
	{
		foreach ($filters as $column => $filter) {
			if ($filter->getValue() === null) {
				continue;
			}
			if ($filter instanceof FilterText) {
				$this->queryBuilder->andWhere("[%column] LIKE %_like_", $column, $filter->getValue());
			}
			if ($filter instanceof FilterSelect) {
				$this->queryBuilder->andWhere("[%column] = %s", $column, $filter->getValue());
			}
		}
	}

	public function filterOne(array $condition): IDataSource
	{
		// TODO: Implement filterOne() method.
		return $this;
	}

	public function limit(int $offset, int $limit): IDataSource
	{
		$this->queryBuilder->limitBy($limit,$offset);

		return $this;
	}

	public function sort(Sorting $sorting): IDataSource
	{
		$sort = [];
		foreach ($sorting->getSort() as $column => $direction) {
			$sort[] = $column . ' ' . $direction;
		}

		if( $sort !== []) {
			/** @var literal-string $sortString */
			$sortString = implode(', ' ,$sort);
			$this->queryBuilder->orderBy($sortString);
		}
		return $this;
	}
}
