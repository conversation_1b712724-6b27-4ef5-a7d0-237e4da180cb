<?php declare(strict_types = 1);

namespace App\PostType\Core\AdminModule\Components\DbalDataGrid;

use App\Model\Orm\Orm;
use App\Model\Translator;
use App\PostType\Core\AdminModule\Components\DataGrid\Definition\DataGridDefinition;
use App\PostType\Core\AdminModule\Components\DbalDataGrid\DataSource\DbalDataSource;
use Nette\Application\UI\Control;
use Nextras\Dbal\Connection;
use Nextras\Dbal\Result\Row;
use Ublaboo\DataGrid\Exception\DataGridException;

class DbalDataGrid extends Control
{

	#use HasMutationColumn;
	private ?DbalDataSource $dataSource = null;
	private array $mutations;
	private readonly DataGridDefinition $dataGridDefinition;

	/**
	 * @param literal-string $tableName
	 */
	public function __construct(
		private readonly string $tableName,
		?DataGridDefinition $dataGridDefinition,
		private readonly Translator $translator,
		private readonly Connection $connection,
		private readonly Orm $orm,
	)
	{
		$this->dataGridDefinition = $dataGridDefinition ?? new DataGridDefinition();
		$this->onAnchor[] = $this->init(...);
	}

	private function init(): void
	{
		$this->mutations = $this->orm->mutation->findAll()->fetchPairs('id', 'name');
	}

	private function getDataSource(): DbalDataSource
	{
		if ($this->dataSource === null) {
			$this->dataSource = new DbalDataSource($this->connection, $this->tableName);
		}
		return $this->dataSource;
	}

	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);

		$template->render(__DIR__ . '/dbalDataGrid.latte');
	}


	/**
	 * @throws DataGridException
	 */
	public function createComponentGrid(): \Ublaboo\DataGrid\DataGrid
	{
		if ($this->dataGridDefinition->dataGrid !== null) {
			$grid = $this->dataGridDefinition->dataGrid;
		} else {
			$grid = new \Ublaboo\DataGrid\DataGrid();
		}
		$grid->setDataSource($this->getDataSource());

		/** @var Row $firstItem */
		$firstItem = $this->dataSource->getFirstItem();
		if ($firstItem !== null && property_exists($firstItem,'internalName')) {
			$grid->addColumnText('internalName', 'internalName')->setSortable()->setFilterText();
		}

		$grid->addColumnText('name', 'name')->setSortable()->setFilterText();


		foreach ($this->dataGridDefinition->extenders as $extender) {
			($extender->getImproveFunction())($grid);
		}

		if ($firstItem !== null && property_exists($firstItem,'mutationId')) {
			$grid->addColumnText('mutationId', 'label_mutation')
			            ->setRenderer(function (Row $item): string {
				            return $this->mutations[$item->mutationId] ?? '-';
			            })
			            ->setFilterSelect($this->mutations)
			            ->setPrompt($this->translator->translate('all'));
		}

		$grid->addAction('edit', 'Edit', ':edit')->setClass('btn btn-xs btn-primary');

		/*$grid->setRowCallback(function(Entity $item, Html $tr) {
			if ($item instanceof BaseEntity) {
				if (!$item->isPublished()) {
					$tr->addClass("row-disabled");
				}
			}
		});*/

		$grid->setTranslator($this->translator);

		foreach ($this->dataGridDefinition->beforeRenderExtenders as $extender) {
			($extender->getImproveFunction())($grid);
		}

		$grid->setTemplateFile(__DIR__ . '/dbalDataGridTemplate.latte');
		$grid->setStrictSessionFilterValues(false);

		return $grid;
	}

}
