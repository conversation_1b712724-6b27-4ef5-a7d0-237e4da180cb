<?php declare(strict_types = 1);

namespace App\PostType\Core\AdminModule\Components\Form;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\Routable;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\FormExtender;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\CustomFormExtender;
use App\PostType\Core\AdminModule\Components\Form\Definition\FormDefinition;
use App\PostType\Core\AdminModule\Components\Form\Definition\RelationInfo;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Core\Model\Publishable;
use App\PostType\Core\Model\Validatable;
use Nette\Application\UI\Form;
use Nextras\Orm\Entity\IEntity;

final class Builder
{

	public function build(Form $form, LocalizationEntity $entityLocalization, FormDefinition $formDefinition): void
	{
		$this->addLocalization($form, $entityLocalization);
		$this->addParent($form, $entityLocalization->getParent());

		if ($entityLocalization instanceof Routable) {
			$this->addRoutable($form, $entityLocalization);
		}

		if ($entityLocalization instanceof Publishable) {
			$this->addPublish($form, $entityLocalization);
		}

		if ($entityLocalization instanceof Validatable) {
			$this->addValidity($form, $entityLocalization);
		}

		$this->addExtenders($form, $formDefinition->extenders);


		$this->addButtons($form);
	}


	public function addLocalization(Form $form, LocalizationEntity $entityLocalization): void
	{
		$container = $form->addContainer('localization');
		$container->addText('name', 'name')
            ->addRule($form::MaxLength, 'Max length', 250)
			->setDefaultValue($entityLocalization->getName())
			->setRequired();

		$container->addHidden('cf');
		assert($entityLocalization instanceof IEntity);


		if (method_exists($entityLocalization, 'getCcSchemeJson')) {
			$container->addHidden('cc')->setDefaultValue($entityLocalization->getRawValue('customContentJson'));
			$container->addHidden('ccScheme')->setDefaultValue($entityLocalization->getCcSchemeJson());
		}
	}


	public function addRoutable(Form $form, Routable $entityLocalization): void
	{
		$container = $form->addContainer('routable');
		$container->addText('nameTitle', 'nameTitle')->setDefaultValue($entityLocalization->getNameTitle());
		$container->addText('nameAnchor', 'nameAnchor')->setDefaultValue($entityLocalization->getNameAnchor());
		$container->addTextArea('description', 'description')->setDefaultValue($entityLocalization->getDescription());
		$container->addText('keywords', 'keywords')->setDefaultValue($entityLocalization->getKeywords());
		$container->addText('alias', 'alias')->setDefaultValue($entityLocalization->getAlias());
		$container->addTextArea('aliasHistory', 'aliasHistory')->setDefaultValue($entityLocalization->getAliasHistoryString());
	}


	public function addParent(Form $form, ParentEntity $parent): void
	{
		$container = $form->addContainer('parent');
		$container->addText('internalName', 'internalName')
			->setDefaultValue($parent->getInternalName())
            ->addRule($form::MaxLength, 'Max length', 250)
			->setRequired();

		$container->addHidden('cf');

		if (method_exists($parent, 'getCcSchemeJson') && assert($parent instanceof BaseEntity)) {
			$container->addHidden('cc')->setDefaultValue($parent->getRawValue('customContentJson'));
			$container->addHidden('ccScheme')->setDefaultValue($parent->getCcSchemeJson());
		}

	}


	public function addButtons(Form $form): void
	{
		$form->addSubmit('send');
	}

	public function addPublish(Form $form, Publishable $entityLocalization): void
	{
		$container = $form->addContainer('publish');
		$container->addCheckbox('public', 'public')->setDefaultValue($entityLocalization->getIsPublic());
	}

	public function addValidity(Form $form, Validatable $entityLocalization): void
	{
		$container = $form->addContainer('validity');
		$container->addText('publicFrom', 'publicFrom');
		$publicFrom = $entityLocalization->getPublicFrom();
		if ($publicFrom !== null) {
			$container['publicFrom']->setDefaultValue($publicFrom->format('Y-m-d\TH:i'));
		}

		$container->addText('publicTo', 'publicTo');
		$publicTo = $entityLocalization->getPublicTo();
		if ($publicTo !== null) {
			$container['publicTo']->setDefaultValue($publicTo->format('Y-m-d\TH:i'));
		}
	}



	public function addHasOneRelation(Form $form, RelationInfo $relationInfo): void
	{
		$relationName = $relationInfo->propertyName;
		$relationsContainer = $form->addContainer($relationName);

		if ($relationInfo->builderCollection->count() > 0) {
			$entity = $relationInfo->builderCollection->fetchAll()[0];
			assert($entity instanceof BaseEntity);
			$relationsContainer->addHidden('id', $entity->id)->setNullable()
				->addFilter(function (string|int $value) { bd($value); return ($value === '') ? null : (int) $value;});
			$nameInputValue = $entity->{$relationInfo->inputSuggestPropertyName};
			$relationsContainer->addText('name', 'name')
				->setOmitted()
				->setDefaultValue($nameInputValue);
		} else {
			$relationsContainer->addHidden('id', '')->setNullable()
				->addFilter(function (string|int $value) { bd($value); return ($value === '') ? null : (int) $value;});
			$relationsContainer->addText('name', 'name')
				->setOmitted();
		}
	}


	public function addHasManyRelation(Form $form, RelationInfo $relationInfo, array $rawPostData): void
	{
		$relationName = $relationInfo->propertyName;
		$relationsContainer = $form->addContainer($relationName);
		if (isset($rawPostData[$relationName]) && $containerPostData = $rawPostData[$relationName]) {
			foreach ($containerPostData as $itemKey => $state) {
				$entityContainer = $relationsContainer->addContainer($itemKey);
				$entityContainer->addHidden('id');
				$entityContainer->addText('name', 'name')->setOmitted();
			}
		} else {
			foreach ($relationInfo->builderCollection as $itemKey => $entity) {
				assert($entity instanceof BaseEntity);
				$entityContainer = $relationsContainer->addContainer($itemKey);
				$entityContainer->addHidden('id', $entity->id);

				$nameInputValue = $entity->{$relationInfo->inputSuggestPropertyName};
				$entityContainer->addText('name', 'name')
					->setOmitted()
					->setDefaultValue($nameInputValue);
			}
		}

		//add suggest blueprint
		$entityContainer = $relationsContainer->addContainer('newItemMarker');
		$entityContainer->addHidden('id');
		$entityContainer->addText('name', 'name');

	}

	/**
	 * @param FormExtender[] $extenders
	 */
	private function addExtenders(Form $form, array $extenders): void
	{
		foreach ($extenders as $extender) {
			($extender->getAddHandlerFunction())($form);
		}
	}

}
