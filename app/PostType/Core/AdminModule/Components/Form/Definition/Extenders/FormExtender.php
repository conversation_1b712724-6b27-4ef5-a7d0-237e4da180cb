<?php declare(strict_types = 1);

namespace App\PostType\Core\AdminModule\Components\Form\Definition\Extenders;


use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\TemplatePart;
use Closure;
use Nette\Application\UI\Form;

interface FormExtender
{

	/**
	 * @return Closure(Form): void
	 */
	public function getAddHandlerFunction(): Closure;

	/**
	 * @return Closure(Form, mixed $data): void
	 */
	public function getSuccessHandlerFunction(): Closure;

	/**
	 * @return TemplatePart[]
	 */
	public function getTemplateParts(array $types): array;

}
