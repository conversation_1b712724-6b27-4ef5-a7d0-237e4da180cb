<?php declare(strict_types=1);

namespace App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates;

use App\PostType\Core\AdminModule\Components\Form\Definition\RelationInfo;

final class RelationTemplatePart implements TemplatePart
{
	public const TYPE_RELATION = 'relation';
	public const TYPE_RELATION_PRESCRIPTION = 'prescription';

	/**
	 * @param self::TYPE_* $type
	 */
	public function __construct(
		private readonly RelationInfo $relationInfo,
		private readonly string $templateFile,
		private readonly string $type = self::TYPE_RELATION,
		private readonly ?array $parameters = [],
	)
	{}

	public function getType(): string
	{
		return $this->type;
	}

	public function getTemplatePath(): string
	{
		return $this->templateFile;
	}

	public function getParameters(): array
	{
		return array_merge($this->parameters, ['relationInfo' => $this->getRelationInfo()]);
	}

	public function getRelationInfo(): RelationInfo
	{
		return $this->relationInfo;
	}
}
