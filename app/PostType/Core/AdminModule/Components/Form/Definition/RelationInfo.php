<?php declare(strict_types = 1);

namespace App\PostType\Core\AdminModule\Components\Form\Definition;
use App\Model\CustomField\SuggestUrl;
use App\Model\Orm\Orm;
use Nextras\Orm\Collection\ArrayCollection;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\IEntity;
use Nextras\Orm\Relationships\HasOne;
use Nextras\Orm\Relationships\IRelationshipCollection;
use Nextras\Orm\Repository\IRepository;

class RelationInfo
{
	private const DEFAULT_SVG_ICON = 'folder.svg';
	public const DEFAULT_PLACEHOLDER = 'Type for search';
	public const DEFAULT_REMOTE_PROPERTY_NAME = 'internalName';
	public readonly IRepository $targetRepository; // @phpstan-ignore-line
	public readonly string $toggleName;
	public readonly string $iconFilename;

	public ICollection $builderCollection; // @phpstan-ignore-line
	public readonly bool $singleValue;


	public function __construct( // @phpstan-ignore-line
		private readonly Orm $orm,
		public readonly IEntity $sourceEntity,
		public readonly string $propertyName,
		public readonly SuggestUrl $suggestUrl,

		public readonly ?string $inputSuggestPropertyName = self::DEFAULT_REMOTE_PROPERTY_NAME,
		public readonly ?string $inputPlaceHolder = self::DEFAULT_PLACEHOLDER,
		?string $iconFilename = null,
		?string $toggleName = null,
		public readonly ?bool $dragAndDrop = false,
		?ICollection $builderCollection = null,
		?bool $singleValue = false,
	)
	{
		if ($builderCollection === null) {

			if ($this->sourceEntity->{$this->propertyName} instanceof IRelationshipCollection) {
				$this->builderCollection = $this->sourceEntity->{$this->propertyName}->toCollection();
			} else if ($this->sourceEntity->getProperty($this->propertyName) instanceof HasOne) {
				$remoteRepository = $this->orm->getRepository(
					$this->sourceEntity->getMetadata()->getProperty($this->propertyName)->relationship->repository
				);
				if ($this->sourceEntity->{$this->propertyName} === null) {
					$items = [];
				} else {
					$items = [$this->sourceEntity->{$this->propertyName}];
				}
				$this->builderCollection = new ArrayCollection($items, $remoteRepository);
				$singleValue = true;
			}

		} else {
			$this->builderCollection = $builderCollection;
		}

		if ($iconFilename === null) {
			$this->iconFilename = self::DEFAULT_SVG_ICON;
		} else {
			$this->iconFilename = $iconFilename;
		}

		if ($toggleName === null) {
			$this->toggleName = $propertyName;
		} else {
			$this->toggleName = $toggleName;
		}

		$this->singleValue = $singleValue;

		$this->targetRepository = $this->orm->getRepository(
			$this->sourceEntity->getMetadata()->getProperty($propertyName)->relationship->repository
		);
	}

}
