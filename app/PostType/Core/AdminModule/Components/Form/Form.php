<?php declare(strict_types = 1);

namespace App\PostType\Core\AdminModule\Components\Form;

use App\AdminModule\Components\PostType\Localizations\Localizations;
use App\AdminModule\Components\PostType\Localizations\LocalizationsFactory;
use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\Model\CustomField\SuggestUrls;
use App\Model\ElasticSearch\Common\Facade;
use App\Model\Link\LinkFactory;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Routable;
use App\Model\Orm\User\User;
use App\Model\Translator;
use App\Model\TranslatorDB;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\Calendar\Model\Orm\CalendarLocalization;
use App\PostType\Core\AdminModule\Components\Form\Definition\FormDefinition;
use App\PostType\Core\AdminModule\Components\Form\FormData\BaseFormData;
use App\PostType\Core\Model\EntityLocalizationFacade;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Tag\Model\Orm\TagLocalization\TagLocalization;
use Closure;
use Nette\Application\UI\Control;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\IEntity;

class Form extends Control
{

	public const TEMPLATE_PARTS_DIR = __DIR__ . '/parts';

	private const INDEXABLE_IN_COMMON = [
		Tree::class,
		BlogLocalization::class,
		TagLocalization::class,
		CalendarLocalization::class,
	];

	/** @var ICollection<Mutation> */
	private ICollection $mutations;
	private FormDefinition $formDefinition;

	public function __construct(
		private readonly EntityLocalizationFacade $entityLocalizationFacade,
		private readonly LocalizationEntity $entityLocalization,
		private readonly User $userEntity,
		private readonly SuggestUrls $urls,
		private readonly Translator $translator,
		private readonly TranslatorDB $translatorDB,
		private readonly Builder $formBuilder,
		private readonly Handler $formHandler,
		private readonly LinkFactory $linkFactory,
		private readonly Orm $orm,
		private readonly Facade $commonElasticFacade,
		private readonly LocalizationsFactory $languageFactory,
		private readonly \App\Model\ElasticSearch\All\Facade $allElasticFacade,
		private readonly MessageForFormFactory $messageForFormFactory,
		?FormDefinition $formDefinition = null,
	)
	{
		$this->onAnchor[] = Closure::fromCallable([$this, 'init']);
		$this->formDefinition = $formDefinition ?? new FormDefinition();
	}


	private function init(): void
	{
		$this->mutations = $this->orm->mutation->findAll();
	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$template->add('templates', RS_TEMPLATE_DIR);
		$template->add('translator', $this->translator);

		$template->setParameters($this->formDefinition->templateParameters); //@phpstan-ignore-line

		if ($this->entityLocalization instanceof Routable) {
			$template->linksToFront = $this->linkFactory->linkTranslateToNette($this->entityLocalization, ['show' => 1, 'mutation' => $this->entityLocalization->getMutation()]);
		} else {
			$template->linksToFront = null;
		}

		$template->formDefinition = $this->formDefinition;
		$template->parent = $this->entityLocalization->getParent();
		$template->entityLocalization = $this->entityLocalization;
		$template->mutation = $this->entityLocalization->getMutation();
		$template->orm = $this->orm;
		$template->translatorDB = $this->translatorDB;
		$template->fileUploadLink = $this->presenter->link(':Admin:File:upload');

		$template->otherMutations = $this->entityLocalization->getParent()->getLocalizations()->findBy(['mutation!=' => $this->entityLocalization->getMutation()]);
		$activeMutationLangCodes = [];
		foreach ($this->entityLocalization->getParent()->getLocalizations() as $localization) {
			assert($localization instanceof LocalizationEntity);
			$activeMutationLangCodes[] = $localization->getMutation()->langCode;
		}

		$template->missingMutations = $this->mutations->findBy(['langCode!=' => $activeMutationLangCodes]);

		$template->userEntity = $this->userEntity;

		$template->urls = $this->urls;
		$template->render(__DIR__ . '/form.latte');
	}


	protected function createComponentForm(): \Nette\Application\UI\Form
	{
		if ($this->formDefinition->form !== null) {
			$form = $this->formDefinition->form;
		} else {
			$form = new \Nette\Application\UI\Form();
			$form->setMappedType(BaseFormData::class);
		}
		$form->setTranslator($this->translator);

		$this->formBuilder->build($form, $this->entityLocalization, $this->formDefinition);

		$form->onSuccess[] = $this->successBaseSave(...);
		$form->onSuccess[] = $this->successExtensionSave(...);
		$form->onSuccess[] = $this->successFillElastic(...);
		$form->onSuccess[] = $this->successRedirect(...);
		$form->onError[] = $this->formError(...);
		return $form;
	}


	public function formError(\Nette\Application\UI\Form $form): void
	{
		bd($form->errors);
		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	/**
	 * @param BaseFormData $data
	 */
	public function successBaseSave(\Nette\Application\UI\Form $form, $data): void
	{
		$this->formHandler->handle($this->entityLocalization, $this->userEntity, $data);
	}

	/**
	 * @param BaseFormData $data
	 */
	public function successExtensionSave(\Nette\Application\UI\Form $form, $data): void
	{
		foreach ($this->formDefinition->extenders as $extender) {
			($extender->getSuccessHandlerFunction())($form, $data);
		}

		assert($this->entityLocalization instanceof IEntity);
		$this->orm->persistAndFlush($this->entityLocalization);
	}

	/**
	 * @param BaseFormData $data
	 */
	public function successFillElastic(\Nette\Application\UI\Form $form, $data): void
	{
		assert($data instanceof BaseFormData);
		if (in_array(get_class($this->entityLocalization), self::INDEXABLE_IN_COMMON)) {
			$this->commonElasticFacade->updateNow($this->entityLocalization, $this->entityLocalization->getMutation());
		}
		$this->allElasticFacade->updateNow($this->entityLocalization);
	}

	/**
	 * @param BaseFormData $data
	 */
	public function successRedirect(\Nette\Application\UI\Form $form, $data): void
	{
		$this->presenter->redirect('edit', ['id' => $this->entityLocalization->getId()]);
	}

	public function handleDelete(): void
	{
		if (in_array(get_class($this->entityLocalization), self::INDEXABLE_IN_COMMON)) {
			$this->commonElasticFacade->deleteNow($this->entityLocalization, $this->entityLocalization->getMutation());
		}

		$this->allElasticFacade->delete($this->entityLocalization);

		$this->entityLocalizationFacade->remove($this->entityLocalization);
		$this->presenter->redirect('default');
	}


	protected function createComponentLanguage(): Localizations
	{
		return $this->languageFactory->create(
			localizationEntity: $this->entityLocalization,
		);
	}

	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

}
