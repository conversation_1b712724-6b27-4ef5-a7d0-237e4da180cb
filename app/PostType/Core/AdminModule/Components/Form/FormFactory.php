<?php declare(strict_types = 1);

namespace App\PostType\Core\AdminModule\Components\Form;

use App\Model\Orm\User\User;
use App\PostType\Core\AdminModule\Components\Form\Definition\FormDefinition;
use App\PostType\Core\Model\EntityLocalizationFacade;
use App\PostType\Core\Model\LocalizationEntity;

interface FormFactory
{

	public function create(
		EntityLocalizationFacade $entityLocalizationFacade,
		LocalizationEntity $entityLocalization,
		User $userEntity,
		?FormDefinition $formDefinition = null,
	): Form;

}
