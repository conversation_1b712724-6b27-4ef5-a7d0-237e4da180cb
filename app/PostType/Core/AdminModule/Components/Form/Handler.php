<?php declare(strict_types = 1);

namespace App\PostType\Core\AdminModule\Components\Form;

use App\Model\CustomContent\CustomContent;
use App\Model\CustomField\CustomFields;
use App\Model\Orm\Alias\AliasModel;
use App\Model\Orm\BaseEntity;
use App\Model\Orm\Orm;
use App\Model\Orm\Routable;
use App\Model\Orm\User\User;
use App\PostType\Core\AdminModule\Components\Form\Definition\RelationInfo;
use App\PostType\Core\AdminModule\Components\Form\FormData\BaseFormData;
use App\PostType\Core\AdminModule\Components\Form\FormData\LocalizationFormData;
use App\PostType\Core\AdminModule\Components\Form\FormData\ParentFormData;
use App\PostType\Core\AdminModule\Components\Form\FormData\PublishFormData;
use App\PostType\Core\AdminModule\Components\Form\FormData\RoutableFormData;
use App\PostType\Core\AdminModule\Components\Form\FormData\ValidityFormData;
use App\PostType\Core\Model\Editable;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Core\Model\Publishable;
use App\PostType\Core\Model\Validatable;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\IEntity;
use Nextras\Orm\Mapper\IRelationshipMapper;
use Nextras\Orm\Relationships\HasMany;
use Nextras\Orm\Relationships\HasOne;
use Nextras\Orm\Relationships\ManyHasOne;
use Nextras\Orm\Relationships\OneHasMany;
use Nextras\Orm\Relationships\OneHasOne;

final class Handler
{

	public function __construct(
		private readonly AliasModel $aliasModel,
		private readonly CustomFields $customFields,
		private readonly CustomContent $customContent,
		private readonly Orm $orm,
	)
	{
	}


	public function handle(LocalizationEntity $entityLocalization, User $user, BaseFormData $data): void
	{
		$this->handleParent($entityLocalization->getParent(), $data->parent);
		$this->handleLocalization($entityLocalization, $data->localization);
		if ($entityLocalization instanceof Routable) {
			$this->handleRoutable($entityLocalization, $data->localization, $data->routable);
		}

		if ($entityLocalization instanceof Publishable && (isset($data->publish) && $data->publish instanceof PublishFormData)) {
			$this->handlePublish($entityLocalization, $data->publish);
		}

		if ($entityLocalization instanceof Editable) {
			$this->handleEditor($entityLocalization, $user);
		}
		if ($entityLocalization instanceof Validatable) {
			$this->handleValidity($entityLocalization, $data->validity);
		}

		assert($entityLocalization instanceof BaseEntity);
		$this->orm->persistAndFlush($entityLocalization);
	}


	public function handleLocalization(LocalizationEntity $entityLocalization, LocalizationFormData $data): void
	{
		$entityLocalization->setName($data->name);

		if (isset($entityLocalization->cf)) {
			if (isset($data->cf) && $data->cf !== '') {
				$entityLocalization->setCf($this->customFields->prepareDataToSave($data->cf));
			} else {
				$entityLocalization->setCf(new ArrayHash());
			}
		}

		if (isset($entityLocalization->cc)) {
			if (isset($data->cc) && $data->cc !== ''
				&& isset($data->ccScheme) && $data->ccScheme !== '') {
				$entityLocalization->setCc($this->customContent->prepareDataToSave($data->cc, $data->ccScheme));
			} else {
				$entityLocalization->setCc(new ArrayHash());
			}
		}
	}

	public function handleRoutable(Routable $entityLocalization, LocalizationFormData $localization, RoutableFormData $routable): void
	{
		$entityLocalization->setNameTitle($routable->nameTitle);
		$entityLocalization->setNameAnchor($routable->nameAnchor);

		$entityLocalization->setDescription($routable->description);
		$entityLocalization->setKeywords($routable->keywords);
		$entityLocalization->setAliasHistoryString($routable->aliasHistory);
		$entityLocalization->setAlias($routable->alias ?: $this->aliasModel->generateAlias($localization->name, $entityLocalization->getMutation(), $entityLocalization));
	}

	public function handlePublish(Publishable $entityLocalization, PublishFormData $data): void
	{
		$entityLocalization->setIsPublic($data->public);
	}


	public function handleValidity(Validatable $entityLocalization, ValidityFormData $data): void
	{
		$entityLocalization->setPublicFrom($data->publicFromDateTime);
		$entityLocalization->setPublicTo($data->publicToDateTime);
	}

	public function handleEditor(Editable $entityLocalization, User $user): void
	{
		$entityLocalization->setEditorId($user->id);
		$entityLocalization->setEditedTime(new DateTimeImmutable());
	}

	public function handleParent(ParentEntity $localizableEntity, ParentFormData $data): void
	{
		$localizableEntity->setInternalName($data->internalName);
		if (isset($localizableEntity->cf) && isset($data->cf) && $data->cf !== '') {
			$localizableEntity->setCf($this->customFields->prepareDataToSave($data->cf));
		}
	}


	public function handleHasOneRelation(?int $selectedId, RelationInfo $relationInfo): void
	{
		assert(in_array(
			$relationInfo->sourceEntity->getMetadata()->getProperty($relationInfo->propertyName)->wrapper,
			[OneHasOne::class, ManyHasOne::class]
		));
		$relationInfo->sourceEntity->{$relationInfo->propertyName} = $selectedId;
	}

	public function handleHasManyRelation(array $selectedIdsForRelation, RelationInfo $relationInfo): void
	{
		$relation = $relationInfo->sourceEntity->{$relationInfo->propertyName};
		assert($relation instanceof HasMany);
		// remove newItemMarker template & normalize array
		$ids = self::readOnlyValidIds($selectedIdsForRelation);
		$relation->set($ids);
	}

	/**
	 * @return list<int>
	 */
	public static function readOnlyValidIds(array $arrayWithIds): array
	{
		$arrayWithIds = array_filter($arrayWithIds, fn($item) => (isset($item['id']) && $item['id'] !== ''));
		return array_values(array_map(fn($item) => (int) $item['id'], $arrayWithIds));
	}

}
