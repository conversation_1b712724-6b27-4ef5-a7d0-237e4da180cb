<?php declare(strict_types = 1);

namespace App\PostType\Core\AdminModule\Components\Form\FormData;

use Nette\Utils\Json;
use Nextras\Dbal\Utils\DateTimeImmutable;
use stdClass;

class FormDataHelper
{

	public static function convertEmptyValueToValidJsonObject(string|null $string): string
	{
		if ($string === null || $string === '') {
			return Json::encode(new stdClass());
		}

		return $string;
	}

	public static function convertInvalidDateTimeValue(string|null $dateTimeString): DateTimeImmutable|null
	{
		if ($dateTimeString === '' || $dateTimeString === null) {
			return null;
		}

		return new DateTimeImmutable($dateTimeString);
	}

}
