<?php declare(strict_types = 1);

namespace App\PostType\Core\AdminModule\Components\Form\FormData;

use Nextras\Dbal\Utils\DateTimeImmutable;

class ValidityFormData
{

	public DateTimeImmutable|null $publicFromDateTime;

	public DateTimeImmutable|null $publicToDateTime;

	public function __construct(
		?string $publicFrom,
		?string $publicTo,
	)
	{
		$this->publicFromDateTime = FormDataHelper::convertInvalidDateTimeValue($publicFrom);
		$this->publicToDateTime = FormDataHelper::convertInvalidDateTimeValue($publicTo);
	}

}
