<?php declare(strict_types = 1);

namespace App\PostType\Core\AdminModule\Components\Form\FormData;

class LocalizationFormData
{

	public string $cf;

	public string $cc;

	public string $ccScheme;

	public function __construct(
		public string $name,
		string|null $cf,
		string|null $cc,
		string|null $ccScheme,
	)
	{
		$this->cf = FormDataHelper::convertEmptyValueToValidJsonObject($cf);
		$this->cc = FormDataHelper::convertEmptyValueToValidJsonObject($cc);
		$this->ccScheme = FormDataHelper::convertEmptyValueToValidJsonObject($ccScheme);
	}

}
