{default $defaultLangsString = ''}
{default $customFieldsType = 'cc'}

<div class="b-std__content"
	 data-controller="ModularContent CustomFields ToggleAll"
	 data-action="ModularContent:addToScheme->CustomFields#addToScheme CustomFields:updateSchemeValue->ModularContent#updateModules CustomField:updateValue->CustomFields#updateValue CustomFieldImage:updateValue->CustomFields#updateValue CustomFieldFile:updateValue->CustomFields#updateValue CustomFieldList:addListItem->CustomFields#addListItem CustomFieldList:removeListItem->CustomFields#removeListItem CustomFieldList:updateListOrder->CustomFields#updateListOrder Toggle:langToggleSelected@window->CustomFields#langToggleSelected CustomFieldsCopy:copyFromLanguage@window->CustomFields#copyFromLanguage"
	 data-customfields-lang-value="{$langCode}"
	 data-customfields-scheme-value="{$scheme}"
	 data-customfields-values-value='{$values}'
	 data-customfields-uploadurl-value="{$uploadLink}"
	 data-customfields-mutationid-value="{$mutationId}"
	 {if $defaultLangsString !== ''}data-customfields-default-langs-value="{$defaultLangsString}" {/if}
	 data-customfields-type-value="{$customFieldsType}"
	 data-modularcontent-modules-value='{$modules}'
>
	<p><button type="button" class="btn" data-action="ToggleAll#toggle"><span class="btn__text">Sbalit/rozbalit vše</span></button></p>
	<div data-customfields-target="content"></div>
	<input n:name="{$itemName}" data-customfields-target="values">
	<input n:name="{$itemName}Scheme" data-modularcontent-target="modules">
	<div class="m-icons c-custom-fields__menu u-mb-sm">
		<ul class="m-icons__list" data-modularcontent-target="menu"></ul>
		<div class="cc-search inp u-hide" data-modularcontent-target="search">
			<div class="inp-fix">
				<input class="inp-text" type="text" placeholder="Piš pro filtrování" data-action="input->ModularContent#filterCategories" data-modularcontent-target="searchinput">
				<div class="inp-fix__sufix"><a href="#" data-action="ModularContent#toggleSearch"><span class="ico ico--times"></span></a></div>
				<div class="inp-text__holder"></div>
				<div class="b-suggest is-visible" data-modularcontent-target="suggest"></div>
			</div>
		</div>
	</div>
</div>
