{if isset($form['validity'])}
	{var $anchorName = 'validity'}
	{var $icon = $templates.'/part/icons/calendar-alt.svg'}
	{var $title = 'Platnost'}

	{var $props = [
		title: $title,
		id: $anchorName,
		icon: $icon,
		variant: 'main',
		open: true,
		classes: ['u-mb-xxs'],

	]}


	{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
		{block content}
			<div class="grid grid--y-0">
				<div class="grid__cell size--6-12" n:ifset="$form['validity']['publicFrom']">
					{include $templates.'/part/core/inp.latte' props: [
						input: $form['validity']['publicFrom'],
						classesLabel: ['title'],
						type: 'datetime-local'
					]}
				</div>
				<div class="grid__cell size--6-12" n:ifset="$form['validity']['publicTo']">
					{include $templates.'/part/core/inp.latte' props: [
						input: $form['validity']['publicTo'],
						classesLabel: ['title'],
						type: 'datetime-local'
					]}
				</div>
			</div>
		{/block}
	{/embed}
{/if}



