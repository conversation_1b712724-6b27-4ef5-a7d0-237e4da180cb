{default $defaultLangsString = ''}
{default $customFieldsType = 'cf'}

<div class="{$class}"
	 data-controller="CustomFields"
	 data-action="CustomField:updateValue->CustomFields#updateValue CustomFieldImage:updateValue->CustomFields#updateValue CustomFieldFile:updateValue->CustomFields#updateValue CustomFieldList:addListItem->CustomFields#addListItem CustomFieldList:removeListItem->CustomFields#removeListItem CustomFieldList:updateListOrder->CustomFields#updateListOrder Toggle:langToggleSelected@window->CustomFields#langToggleSelected CustomFieldsCopy:copyFromLanguage@window->CustomFields#copyFromLanguage"
	 data-customfields-scheme-value="{$schema}"
	 data-customfields-values-value="{$content}"
	 data-customfields-uploadurl-value="{$uploadLink}"
	 {if $defaultLangsString !== ''}data-customfields-default-langs-value="{$defaultLangsString}" {/if}
	 data-customfields-type-value="{$customFieldsType}"

	{if isset($langCode)} data-customfields-lang-value="{$langCode}" {/if}
	{if isset($mutationId)} data-customfields-mutationid-value="{$mutationId}" {/if}
>
	<div data-customfields-target="content"></div>
	<input n:name="{$inputName}" type="hidden" data-customfields-target="values">
</div>





