{if isset($ccObject->cc) && $ccObject->getCcModules()}
	{var $icon = $templates.'/part/icons/grip-vertical.svg'}

	{var $props = [
		title: $title,
		id: $itemName,
		icon: $icon,
		variant: 'main',
		open: true,
		classes: ['u-mb-xxs'],

	]}


	{formContainer $containerName}
		{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
			{block content}
				<div class="b-std u-mb-sm">
					<h3 class="b-std__title title"></h3>

					{include 'custom-content-element.latte',
						class=>'b-std__content',
						langCode=>$mutation->langCode,
						mutationId=>$mutation->id,
						scheme=>$ccObject->getCcSchemeJson(),
						values=>$ccObject->getCcJson(),
						modules=>$ccObject->getCcModulesJson(),
						uploadLink=>$fileUploadLink,
						itemName=>$itemName

					}
				</div>
			{/block}
		{/embed}
	{/formContainer}
{/if}
