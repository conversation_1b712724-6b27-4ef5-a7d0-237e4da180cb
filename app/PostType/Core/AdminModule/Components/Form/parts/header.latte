{php $imgSrc = ''}
{if isset($entityLocalization->getParent()->firstImage) && $firstImage = $entityLocalization->getParent()->firstImage}
	{php $img = $imageObjectFactory->getByName($firstImage->filename, 's')}
	{php $imgSrc = $img->src}
{/if}





{if !isset($hrefClose)}
	{var $hrefClose = 'default'}
{/if}

{default $title = $entityLocalization->getParent()->internalName ?? $entityLocalization->name}

{include $templates.'/part/box/header.latte',
	props: [
		hrefClose: $hrefClose,
		langCodes: [$entityLocalization->mutation->langCode],
		img: $imgSrc,
		title: $title,
		hasGeneratedMenu: true,
		isPageTitle: true,
	]
}

