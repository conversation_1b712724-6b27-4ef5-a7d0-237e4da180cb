<?php declare(strict_types=1);

namespace App\PostType\Core\AdminModule\Components\ShellForm\Definition\Extenders;

use App\PostType\Core\AdminModule\Components\ShellForm\Definition\Extenders\Templates\TemplatePart;
use Closure;
use Nette\Application\UI\Form;
use Nextras\Orm\Entity\IEntity;

class CustomFormExtender implements FormExtender
{
	/**
	 * @param Closure(Form): void $addHandler
	 * @param Closure(Form, mixed $formData, mixed $item): void $successHandler
	 * @param TemplatePart[] $templateParts
	 */
	public function __construct(
		private readonly Closure $addHandler,
		private readonly Closure $successHandler,
		private readonly array $templateParts = []
	)
	{
	}

	public function getAddHandlerFunction(): \Closure
	{
		return $this->addHandler;
	}

	public function getSuccessHandlerFunction(): \Closure
	{
		return $this->successHandler;
	}

	/**
	 * @return TemplatePart[]
	 */
	public function getTemplateParts(array $types): array
	{
		return array_filter($this->templateParts, function (TemplatePart $templatePart) use ($types) {
			return in_array($templatePart->getType(), $types);
		});
	}
}
