<?php declare(strict_types = 1);

namespace App\PostType\Core\AdminModule\Components\ShellForm;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\Mutation\Mutation;
use App\PostType\Core\AdminModule\Components\ShellForm\Definition\FormDefinition;
use App\PostType\Core\Model\Facade;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use App\Model\Orm\Orm;
use App\PostType\Core\AdminModule\Components\ShellForm\FormData\BaseFormData;
use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nextras\Orm\Collection\ICollection;
use App\Model\Translator;
use Nextras\Orm\Entity\IEntity;

class ShellForm extends Control
{

	/** @var ICollection<Mutation>  */
	private ICollection $mutations;
	private FormDefinition $formDefinition;
	private IEntity $newEntity;

	public function __construct(
		private ?BaseEntity $entity,
		private Facade $facade,
		private Translator $translator,
		private Orm $orm,
		?FormDefinition $formDefinition = null,
	)
	{
		$this->formDefinition = $formDefinition ?? new FormDefinition();
		$this->onAnchor[] = [$this, 'init'];
	}


	public function init(): void
	{
		$mutationIdToSkip = [];
		if ($this->entity instanceof ParentEntity) {
			foreach ($this->entity->getLocalizations() as $localization) {
				assert($localization instanceof LocalizationEntity);
				$mutationIdToSkip[$localization->getMutation()->id] = $localization->getMutation()->id;
			}
		}

		$this->mutations = $this->orm->mutation->findBy(['id!=' => $mutationIdToSkip]);
	}

	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$template->add('templates', RS_TEMPLATE_DIR);
		$template->add('mutations', $this->mutations);

		$template->setParameters($this->formDefinition->templateParameters); //@phpstan-ignore-line
		$template->formDefinition = $this->formDefinition;

		$template->render(__DIR__ . '/shellForm.latte');
	}


	protected function createComponentForm(): Form
	{
		if ($this->formDefinition->form !== null) {
			$form = $this->formDefinition->form;
		} else {
			$form = new Form();
			$form->setMappedType(BaseFormData::class);
		}
		$form->setTranslator($this->translator);

		$form->addSelect('mutation', 'select_mutation', $this->mutations->fetchPairs('id', 'name'));
		$form->addSubmit('send', 'send');

		foreach ($this->formDefinition->extenders as $extender) {
			($extender->getAddHandlerFunction())($form);
		}

		$form->onSuccess[] = $this->formSucceeded(...);
		$form->onSuccess[] = $this->successExtensionSave(...);
		$form->onSuccess[] = $this->successRedirect(...);
		$form->onError[] = [$this, 'formError'];
		return $form;
	}


	public function formError(Form $form): void
	{
		bd($form->errors);
		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	/**
	 * @param BaseFormData $data
	 */
	public function formSucceeded(Form $form, $data): void
	{
		$mutation = $this->orm->mutation->getById($data->mutation);

		if ($mutation !== null) {
			$this->newEntity = $this->facade->create($mutation, $this->entity);
		} else {
			$form->addError('Akce se nezdařila');
			$this->flashMessage('Akce se nezdařila', 'error');
		}
	}

	/**
	 * @param BaseFormData $data
	 */
	public function successExtensionSave(Form $form, $data): void
	{
		if (isset($this->newEntity)) {

			foreach ($this->formDefinition->extenders as $extender) {
				($extender->getSuccessHandlerFunction())($form, $data, $this->newEntity);
			}
		}

		assert($this->newEntity instanceof IEntity);
		$this->orm->persistAndFlush($this->newEntity);
	}

	/**
	 * @param BaseFormData $data
	 */
	public function successRedirect(Form $form, $data): void
	{
		if (isset($this->newEntity) && method_exists($this->newEntity, 'getId')) {
			$this->orm->persistAndFlush($this->newEntity);
			$this->presenter->redirect('edit', ['id' => $this->newEntity->getId()]);
		}

	}

}
