<form n:name="form">
	{embed $templates.'/part/box/std.latte', props=>[
		title=> 'Vytvoření',
	]}
		{block content}
			{include $templates.'/part/core/inp.latte' props: [
				input: $form['mutation'],
				type: 'select',
			]}

			{varType App\PostType\Core\AdminModule\Components\ShellForm\Definition\FormDefinition $formDefinition}
			{foreach $formDefinition->extenders as $extender}
				{foreach $extender->getTemplateParts([
				App\PostType\Core\AdminModule\Components\ShellForm\Definition\Extenders\Templates\CommonTemplatePart::TYPE_MAIN,
				]) as $templatePart}
					{include $templatePart->getTemplatePath(), form => $form, ...$templatePart->getParameters()}
				{/foreach}
			{/foreach}

			<p>
				<button n:name="send" class="btn btn--full btn--success">
					<span class="btn__text item-icon">
						<span class="item-icon__icon icon">
							{include $templates.'/part/icons/plus.svg'}
						</span>
						<span class="item-icon__text">
							Založit
						</span>
					</span>
				</button>
			</p>
		{/block}
	{/embed}
</form>
