<?php declare(strict_types=1);

namespace App\PostType\Core\AdminModule\Components\DataGrid\Definition\Extenders;

use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\FormExtender;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\TemplatePart;
use Closure;
use Nette\Application\UI\Form;
use Ublaboo\DataGrid\DataGrid;

class CustomDataGridExtender implements DataGridExtender
{
	/**
	 * @param Closure(DataGrid): void $improveFunction
	 */
	public function __construct(
		private readonly Closure $improveFunction
	)
	{
	}

	public function getImproveFunction(): \Closure
	{
		return $this->improveFunction;
	}
}
