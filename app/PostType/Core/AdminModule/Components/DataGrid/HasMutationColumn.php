<?php declare(strict_types = 1);

namespace App\PostType\Core\AdminModule\Components\DataGrid;

use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\DbalCollection;
use Ublaboo\DataGrid\DataGrid;
use Ublaboo\DataGrid\Exception\DataGridException;
use Ublaboo\DataGrid\Filter\FilterMultiSelect;
use Ublaboo\DataGrid\Filter\FilterSelect;

trait HasMutationColumn
{

	/**
	 * @throws DataGridException
	 */
	protected function addColumnMutation(DataGrid $grid): FilterSelect
	{
		return $grid->addColumnText('mutation', 'label_mutation')
			->setRenderer(function ($entity): string {
				return $entity->getMetadata()->hasProperty('mutation') ? strval($entity->mutation->name) : '';
			})
			->setFilterSelect($this->orm->mutation->findAll()->fetchPairs('id', 'name'))
			->setPrompt($this->translator->translate('all'));
	}


	/**
	 * Filtr Select
	 *
	 * @throws DataGridException
	 */
	protected function addColumnMutations(DataGrid $grid): FilterSelect
	{
		return $grid->addColumnText('mutations', 'label_mutation')
			->setRenderer(function ($entity): string {
				return $entity->getMetadata()->hasProperty('mutations') ? implode(', ', $entity->mutations->toCollection()->fetchPairs('id', 'name')) : '';
			})
			->setFilterSelect($this->orm->mutation->findAll()->fetchPairs('id', 'name'))
			->setPrompt($this->translator->translate('all'))
			->setCondition(function (DbalCollection $collection, string $value): void {
				$collection->getQueryBuilder()
					->joinLeft('[discount_mutation] as dm', '[discount.id] = [dm.discountId]')
					->andWhere('dm.mutationId = %i', $value);
			});
	}


	/**
	 * Filtr Multi select
	 *
	 * @throws DataGridException
	 */
	protected function addColumnMutationsMulti(DataGrid $grid): FilterMultiSelect
	{
		return $grid->addColumnText('mutations', 'label_mutation')
			->setRenderer(function ($entity): string {
				return $entity->getMetadata()->hasProperty('mutations') ? implode(', ', $entity->mutations->toCollection()->fetchPairs('id', 'name')) : '';
			})
			->setFilterMultiSelect($this->orm->mutation->findAll()->fetchPairs('id', 'name'))
			->setPrompt($this->translator->translate('all'))
			->setCondition(function (DbalCollection $collection, ArrayHash $values): void {
				$collection->getQueryBuilder()
					->joinLeft('[discount_mutation] as dm', '[discount.id] = [dm.discountId]')
					->andWhere('dm.mutationId IN %i[]', (array) $values);
			});
	}

}
