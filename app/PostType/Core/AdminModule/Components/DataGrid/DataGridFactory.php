<?php declare(strict_types = 1);

namespace App\PostType\Core\AdminModule\Components\DataGrid;

use App\PostType\Core\AdminModule\Components\DataGrid\Definition\DataGridDefinition;
use Nextras\Orm\Collection\ICollection;

interface DataGridFactory
{

	public function create( // @phpstan-ignore-line
		string $baseEntityName,
		ICollection $collection,
		?DataGridDefinition $dataGridDefinition = null,
	): DataGrid;

}
