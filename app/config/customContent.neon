extensions:
	cc: App\Model\CustomContent\CustomContentExtension

cc:
	definitions:
		content:
			type: group
			label: "Obsah"
			items:
				content: @cf.definitions.content
				tocName: @cf.definitions.tocName
		gallery:
			type: group
			label: "Fotogalerie"
			items:
				content:
					type: tinymce
					label: "<PERSON>bsah v šedém boxu"
				images:
					type: image # has size
					label: "Fotografie (min. 1400x1400)"
					multiple: true
				tocName: @cf.definitions.tocName

		heureka_rating:
			type: group
			label: "Heureka recenze"
			items:
				title:
					type: text
					label: "Nadpis"
				tocName: @cf.definitions.tocName



		persons:
			type: group
			label: "Osoby"
			items:
				persons:
					type: list
					label: "Osoby"
					items:
						image:
							type: image # has size
							label: "Fotografie (min. 560x560)"
						name:
							type: text
							label: "Jméno a příjmení"
						position:
							type: text
							label: "Pozice / Firma"
				tocName: @cf.definitions.tocName

		certificates:
			type: group
			label: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, členství a spolupr<PERSON>ce"
			items:
				title:
					type: text
					label: "Nadpis"
				items:
					type: list
					items:
						image:
							type: image # TODO
							label: "Logo (min.)"
						text:
							type: text
							label: "<PERSON><PERSON><PERSON> (volitelné)"
						link:
							type: text
							label: "Odkaz (volitelné)"
				tocName: @cf.definitions.tocName

		bonds:
			type: group
			label: "Dluhopisy"
			items:
				title:
					type: text
					label: "Nadpis"
				annot:
					type: textarea
					label: "Anotace"
				usp:
					type: list
					label: "USP"
					items:
						text:
							type: text
				tocName: @cf.definitions.tocName

		audio:
			type: group
			label: "Audioverze obsahu"
			items:
				title:
					type: text
					label: "Nadpis"
				tocName: @cf.definitions.tocName

		crossroad:
			type: group
			label: "Rozcestník"
			items:
				title:
					type: text
					label: "Nadpis"
				custom:
					extends: @cf.definitions.crossroad
				tocName: @cf.definitions.tocName

		articles:
			type: group
			label: "Články"
			items:
				gradient:
					type: checkbox
					label: "Barevný přechod na pozadí"
				carousel:
					type: checkbox
					label: "Zobrazit jako carousel"
					defaultValue: true
				type:
					type: radio
					inline: true
					label: "Typ výpisu"
					defaultValue: 'custom'
					options: [
						{ label: "Vlastní výpis", value: "custom" },
						{ label: "Doporučený", value: "recommended" },
					]
				title:
					type: text
					label: "Nadpis"
				# items:
				# 	type: list
				# 	label: "Články"
				# 	items:
				# 		article:
				# 			type: suggest
				# 			TODO
				btn:
					extends: @cf.definitions.linkChoose
					label: "Tlačítko"
				tocName: @cf.definitions.tocName


		cta_box:
			type: group
			label: "Šedý CTA box"
			items:
				image:
					type: image # has size
					label: "Obrázek (min. 560x420)"
				text:
					type: tinymce
					label: "Text"
				tocName: @cf.definitions.tocName

		usp:
			label: "USP (přebírá defaultní obsah z hlavní stránky)"
			type: group
			items:
				narrow:
					type: checkbox
					label: "Úzké zobrazení s nadpisem nahoře"
					defaultValue: false
				title:
					type: text
					label: "Nadpis"
				items:
					extends: @cf.definitions.usp_list
					label: "Body"
				tocName: @cf.definitions.tocName

		faq:
			label: "FAQ (přebírá defaultní obsah ze stránky FAQ)"
			type: group
			items:
				title:
					type: text
					label: "Nadpis"
				btn:
					type: radio
					label: "Tlačítko pod výpisem"
					options: [
						{ label: "Žádné", value: false },
						{ label: "Odkaz na položení otázky (defaultní)", value: "ask" },
						{ label: "Odkaz na výpis", value: "more" },
					]
					defaultValue: 'ask'
				items:
					type: list
					label: "Otázky"
					items:
						question:
							type: text
							label: "Otázka"
						answer:
							type: textarea
							label: "Odpověď"
				tocName: @cf.definitions.tocName

		projects:
			type: group
			label: "Projekty"
			items:
				title:
					type: text
					label: "Název"
				annot:
					type: textarea
					label: "Anotace"
				# TODO
				# items:
				# 	type: list
				# 	label: "Seznam projektů"
				tocName: @cf.definitions.tocName

		person_cta:
			label: "CTA s osobou"
			type: group
			items:
				person:
					type: image
					label: "Fotografie osoby"
				content:
					type: tinymce
					label: "Obsah"
				tocName: @cf.definitions.tocName
		zigzag:
			type: group
			label: "Cik-cak bloky"
			items:
				reversed:
					type: checkbox
					label: "Obrácený výpis (text + fotka jako prvni)"
				bg:
					type: checkbox
					label: "S modrým pozadím"
				numbered:
					type: checkbox
					label: "Číslovaný výpis"
				items:
					type: list
					items:
						flag:
							type: text
							label: "Text ve štítku (nepovinné)"
						approved:
							type: checkbox
							label: "Zobrazit Alex Approved"
						images:
							type: image # has size TODO
							label: "Obrázek (min. šířka 750)"
							multiple: true
						content:
							extends: @cf.definitions.content
						links:
							type: list
							label: "Odkazy"
							items:
								link:
									extends: @cf.definitions.linkChoose
				tocName: @cf.definitions.tocName


		steps:
			type: group
			label: "Kroky"
			items:
				bd:
					type: checkbox
					label: "S ohraničením"
				title:
					type: text
					label: "Nadpis"
				items:
					type: list
					label: "Položky"
					items:
						text:
							type: text
				tocName: @cf.definitions.tocName

		results:
			type: group
			label: "Výsledky Omega"
			items:
				left:
					type: group
					label: "Levý sloupec"
					items:
						title:
							type: text
							label: "Nadpis"
						text:
							type: textarea
							label: "Text"
						image:
							type: image # has size
							label: "Obrázek (min. 560x560)"
						btn:
							extends: @cf.definitions.linkChoose
				right:
					type: group
					label: "Pravý sloupec"
					items:
						title:
							type: text
							label: "Nadpis"
						persons:
							type: list
							label: "Osoby"
							items:
								image:
									type: image # has size
									label: "Fotografie (min. 560x560)"
								name:
									type: text
									label: "Jméno a příjmení"
								position:
									type: text
									label: "Pozice / Firma"
				tocName: @cf.definitions.tocName

		coverage:
			type: group
			label: "Krytí"
			items:
				title:
					type: text
					label: "Nadpis"
				items:
					type: list
					label: "Položky"
					items:
						text:
							type: text
				tocName: @cf.definitions.tocName

		features:
			type: group
			label: "Výhody"
			items:
				title:
					type: text
					label: "Nadpis"
				items:
					type: list
					label: "Položky"
					items:
						text:
							type: text
				tocName: @cf.definitions.tocName

		services:
			type: group
			label: "Služby"
			items:
				title:
					type: text
					label: "Nadpis"
				items:
					type: list
					label: "Položky"
					items:
						image:
							type: image # has size
							label: "Obrázek (min. 560x560)"
						link:
							extends: @cf.definitions.linkChoose
						annot:
							type: textarea
							label: "Anotace"
				tocName: @cf.definitions.tocName

		showcase:
			type: group
			label: "Ukázky práce"
			items:
				title:
					type: text
					label: "Nadpis"
				items:
					type: list
					label: "Položky"
					hasContentToggle: true
					items:
						toggle:
							type: radio
							inline: true
							isContentToggle: true
							defaultValue: 'systemHref'
							options: [
								{ label: "Video", value: "video" },
								{ label: "Foto", value: "photo" },
								{ label: "Článek", value: "article" },
							]
						video:
							type: group
							label: "Video"
							items:
								link:
									type: text
									label: "Odkaz na Youtube / Vimeo"
								image:
									type: image # TODO
									label: "Obrázek (min. )"
						photo:
							type: group
							label: "Foto"
							items:
								image:
									type: image # TODO
									label: "Obrázek (min. )"
						article:
							type: group
							label: "Článek"
							items:
								page:
									type: suggest
									label: "Stránka"
									subType: tree
									url: @cf.suggestUrls.searchMutationPage

				tocName: @cf.definitions.tocName

		references:
			type: group
			label: "Reference"
			items:
				title:
					type: text
					label: "Nadpis"
					defaultValue: "Co o nás říkají klienti"
				items:
					extends: @cf.definitions.references
					label: "Položky"
				tocName: @cf.definitions.tocName

		references_tabs:
			type: group
			label: "Reference s filtrací"
			items:
				title:
					type: text
					label: "Nadpis"
				tocName: @cf.definitions.tocName

		contact_store:
			type: group
			label: "Místo pro osobní předání"
			items:
				store:
					type: group
					label: "Pobočka"
					items:
						title:
							type: text
							label: "Nadpis"
							defaultValue: "Místo pro osobní předání"
						annot:
							type: text
							label: "Anotace"
				tocName: @cf.definitions.tocName

		mentions:
			type: group
			label: "Napsali a natočili o nás"
			items:
				title:
					type: text
					label: "Nadpis"
				annot:
					type: textarea
					label: "Anotace"
				logos:
					type: image # has size
					label: "Loga (min. 240x135)"
					multiple: true
				# items:
				# 	type: list
				# 	label: "Položky"
				# 	items:
				# 		page:
				# 			type: suggest
				tocName: @cf.definitions.tocName

		video:
			type: group
			label: "Video"
			items:
				link:
					type: text
					label: "Odkaz na Youtube / Vimeo"
				poster:
					type: image # has size
					label: "Zástupný obrázek (min.1540x1540)"
				tocName: @cf.definitions.tocName

		clients:
			type: group
			label: "Klienti"
			items:
				title:
					type: text
					label: "Nadpis"
				items:
					type: list
					label: "Položky"
					items:
						image:
							type: image # has size
							label: "Logo (min.320x180)"
						link:
							type: text
							label: "Odkaz (nepovinné)"
				tocName: @cf.definitions.tocName

		# files:
		# 	type: group
		# 	label: "Soubory ke stažení"
		# 	items:
		# 		files:
		# 			extends: @cf.definitions.files

		products:
			type: group
			label: "Produkty"
			items:
				title:
					type: text
					label: "Nadpis (nepovinné)"
				products:
					type: list
					label: "Produkty"
					items:
						product:
							type: suggest
							label: "Produkt"
							subType: product
							url: @cf.suggestUrls.searchProduct

	components:
		# Foto a video
		gallery:
			icon: "images"
			template: "gallery"
			category: "Foto a video"
			definition: @cc.definitions.gallery
		video:
			icon: "video"
			template: "video"
			category: "Foto a video"
			definition: @cc.definitions.video

		# Ostatní
		person_cta:
			icon: "dragon"
			template: "person_cta"
			category: "Ostatní"
			definition: @cc.definitions.person_cta
		usp:
			icon: "star"
			template: "usp"
			category: "Ostatní"
			definition: @cc.definitions.usp
		faq:
			icon: "question"
			template: "faq"
			category: "Ostatní"
			definition: @cc.definitions.faq
		persons:
			icon: "user-alt"
			template: "persons"
			category: "Ostatní"
			definition: @cc.definitions.persons
		cta_box:
			icon: "mouse-pointer"
			template: "cta_box"
			category: "Ostatní"
			definition: @cc.definitions.cta_box
		references:
			icon: "user-check"
			template: "references"
			category: "Ostatní"
			definition: @cc.definitions.references
		references_tabs:
			icon: "user-check"
			template: "references_tabs"
			category: "Ostatní"
			definition: @cc.definitions.references_tabs
		articles:
			icon: "file-alt"
			template: "articles"
			category: "Ostatní"
			definition: @cc.definitions.articles
		crossroad:
			icon: "list-ul"
			template: "crossroad"
			category: "Ostatní"
			definition: @cc.definitions.crossroad
		clients:
			icon: "images"
			template: "clients"
			category: "Ostatní"
			definition: @cc.definitions.clients
		heureka_rating:
			icon: "images"
			template: "heureka_rating"
			category: "Ostatní"
			definition: @cc.definitions.heureka_rating
		certificates:
			icon: "award"
			template: "certificates"
			category: "Ostatní"
			definition: @cc.definitions.certificates
		contact_store:
			icon: "store-alt"
			template: "contact_store"
			category: "Ostatní"
			definition: @cc.definitions.contact_store

		# Obsah
		content:
			icon: "paragraph"
			template: "content"
			category: "Obsah"
			definition: @cc.definitions.content
			hotkey: true
		zigzag:
			icon: "th-large"
			template: "zigzag"
			category: "Obsah"
			definition: @cc.definitions.zigzag
		steps:
			icon: "list-ol"
			template: "steps"
			category: "Obsah"
			definition: @cc.definitions.steps
		audio:
			icon: "play"
			template: "audio"
			category: "Obsah"
			definition: @cc.definitions.audio
		showcase:
			icon: "images"
			template: "showcase"
			category: "Obsah"
			definition: @cc.definitions.showcase

		# Store
		features:
			icon: "star"
			template: "features"
			category: "Pobočky"
			definition: @cc.definitions.features
		services:
			icon: "store-alt"
			template: "services"
			category: "Pobočky"
			definition: @cc.definitions.services

		# Media
		mentions:
			icon: "pen"
			template: "mentions"
			category: "Media"
			definition: @cc.definitions.mentions

		# Investice
		bonds:
			icon: "money-bill-alt"
			template: "bonds"
			category: "Investice"
			definition: @cc.definitions.bonds
		projects:
			icon: "file"
			template: "projects"
			category: "Investice"
			definition: @cc.definitions.projects
		coverage:
			icon: "check-circle"
			template: "coverage"
			category: "Investice"
			definition: @cc.definitions.coverage
		results:
			icon: "chart-pie"
			template: "results"
			category: "Investice"
			definition: @cc.definitions.results

		products:
			icon: "plane-departure"
			template: "products"
			category: "Ostatní"
			definition: @cc.definitions.products
		# files:
		# 	icon: "file"
		# 	template: "files"
		# 	category: "Ostatní"
		# 	definition: @cc.definitions.files

	templates:
		":Front:Page:default": *
		":Front:Page:references": *
		":Front:Page:sustainable": *
		":Front:Page:insurance": *
		":Front:Page:media": *
		":Front:Page:career": *
		":Front:Page:careerDetail": * # TODO vymenit za postType sablonu
		":Front:Page:investment": *
		":Front:Page:contact": *
		":Front:Page:academy": *
		":Front:Page:ebook": * # TODO vymenit za postType sablonu
		":Front:Page:termDetail": * # TODO vymenit za postType sablonu
		":Front:Page:about": *
		":Front:Page:store": *
		":Front:Page:filmingShooting": *
		":Front:Page:deliveryAndPayment": *
		":Front:Page:buyin": *
		":Front:Page:rental": *
		":Front:Page:service": *
		":Front:Page:rentalStep1": *
		":Front:Page:rentalStep2": *
		":Front:Page:rentalStep3": *
		":Blog:Front:Blog:default": *
		":Blog:Front:Blog:detail": *
		":Banner:Front:Banner:detail": *


services:
	-
		implement: App\FrontModule\Components\CustomContentRenderer\CustomContentRendererFactory
		inject: true
