	search:
		-	in: %appDir%
			extends: App\Event\Provider\AbstractProvider

#	decorator:
#		App\Model\InjectableInterface:
#			inject: true
#			tags: ['injectable']

	services:

		# Security
		authenticator: App\Model\Security\Authenticator
		acl: App\Model\Security\Acl
		user: App\Model\Security\User

#		odkomentovani: vypne cache
		cacheStorage:
			class: Nette\Caching\Storages\DevNullStorage

#		cacheStorage:
#			class: Nette\Caching\Storages\FileStorage('%tempDir%/cache')

		translatorDB:
			class: App\Model\TranslatorDB(
					insertNew: %config.translations.insertNew%,
					markUsage: %config.translations.markUsage%
				)
		- App\Model\TranslatorDBCacheService


		translator:
			class: App\Model\Translator(%config.adminLang%, %config%)

		configService: App\Model\ConfigService(%config%)
		cacheFactory: App\Model\CacheFactory
		- App\Model\CacheStorageService
		- App\Model\Cache\RouterCache
		- App\Model\Cache\PresenterCache

		routerFactory: App\Router\RouterFactory
		router: @routerFactory::createRouter(%config.adminAlias%, %postTypeRoutes%)

		menuService:
			class: App\Model\MenuService

		dbalLog:
			class: App\Model\DbalLog("%appDir%/../nettelog/", "mysql")
			setup:
				- register()

		- App\Model\FulltextSearch\FulltextSearchBucketFilterProvider
		- App\Model\FulltextSearch\UserHistory
		- App\Model\FulltextSearch\UserHistoryCookie

		- App\Model\BucketFilter\FilterSessionStorage

		- App\Model\Consent\MarketingConsent

		# ShoppingCart
		shoppingCart.factory: App\Model\ShoppingCart\ShoppingCartFactory
		shoppingCart.accessor: App\Model\ShoppingCart\ShoppingCartAccessor(consoleMode: %consoleMode%)
		shoppingCart:
			factory: @shoppingCart.accessor::get()
		savedForLater:
			class: App\Model\ShoppingCart\SavedForLaterProvider
			lazy: true
		- App\Model\ShoppingCart\Storage\CookieStorageFactory
		- App\Model\ShoppingCart\Storage\SessionStorageFactory
		- App\Model\ShoppingCart\Handlers\DefaultUserAuthenticationHandler

		- App\Model\Orm\Order\NumberSequence\DefaultOrderNumberFormatter
		- App\Model\Orm\Order\NumberSequence\DefaultOrderNumberSequence(testPrefix: false)
		- App\Model\Orm\Order\NumberSequence\OrderNumberGenerator

		# Sitemap
		- App\Model\Sitemap\SitemapGenerator(%config.WWW_DIR%)
		- App\Model\Sitemap\DataProviderTable
		- App\Model\Sitemap\DataProvider\ProductsProvider
		- App\Model\Sitemap\DataProvider\DamagedProductsProvider
		- App\Model\Sitemap\DataProvider\OutOfStockProductsProvider
		- App\Model\Sitemap\DataProvider\PermanentlyOutOfStockProductsProvider
		- App\Model\Sitemap\DataProvider\ProductImagesProvider
		- App\Model\Sitemap\DataProvider\CatalogProvider
		- App\Model\Sitemap\DataProvider\SitemapRootDataProvider


		- App\Model\Orm\ProductVariant\Availability\ProductAvailabilityService
		#- App\Model\Orm\ProductVariant\Availability\DefaultProductAvailability
		- App\Model\Orm\ProductVariant\Availability\CustomProductAvailability

		- App\Model\TagManager\TagManager
		- App\Model\TagManager\Renderer\RendererRegistry
		- App\Model\TagManager\Renderer\DevNullRenderer
		- App\Model\TagManager\Renderer\DataLayerPushRenderer(debugMode: %debugMode%)

		# OrderPlaced subscribers
		- App\Model\Orm\Order\Event\OrderPlaced\Subscriber\SendEmail
		- App\Model\Orm\Order\Event\OrderPlaced\Subscriber\VoucherDeactivate
		- App\Model\Orm\Order\Event\OrderPlaced\Subscriber\StockReservation
		- App\Model\Orm\Order\Event\OrderPlaced\Subscriber\GiftStockReservation
		- App\Model\Orm\Order\Event\OrderPlaced\Subscriber\LastAddress
		- App\Model\Orm\Order\Event\OrderPlaced\Subscriber\LastPickupPoint
		#- App\Model\Orm\Order\Event\OrderPlaced\Subscriber\SyncErp
		- App\Model\Orm\Order\Event\OrderPlaced\Subscriber\HeurekaOvereno

		# OrderStateChange subscribers
		#- App\Model\Orm\Order\Event\OrderState\Subscriber\SendOrderStateEmail

		# OrderPaymentChange subscribers
		#- App\Model\Orm\Order\Event\PaymentChanged\Subscriber\ErpSync

		# App subscribers
		- App\Model\TagManager\GTM\GTMEventSubscriber
		- App\Model\TagManager\GTM\ProductLocalizationDataProvider
		- App\Model\Messenger\Erp\Events\Subscriber\ImportEventsSubscriber

		# Registration subscribers
		app.event.subscriber.registered.sendEmail: App\Model\Orm\User\Event\Registered\Subscriber\SendEmail
		- App\Model\Orm\User\Event\Registered\Subscriber\NewsletterSubscribe
		- App\Model\Orm\User\Event\Registered\Subscriber\AssignOrders

		# CatalogTree subscribers
		- App\PostType\Page\Model\Orm\Event\Subscriber

		# DeliveryMethods
		- App\Model\Orm\DeliveryMethod\DeliveryMethodRegistry
		- App\Model\Orm\DeliveryMethod\PPL
		- App\Model\Orm\DeliveryMethod\PPLPickup
		- App\Model\Orm\DeliveryMethod\DPD
		- App\Model\Orm\DeliveryMethod\DPDPickup
		- App\Model\Orm\DeliveryMethod\Store
		- App\Model\Orm\DeliveryMethod\CzechPost
		- App\Model\Orm\DeliveryMethod\CzechPostPickup
		- App\Model\Orm\DeliveryMethod\Zasilkovna
		- App\Model\Orm\DeliveryMethod\ZasilkovnaPickup
		- App\Model\Orm\DeliveryMethod\BalikovnaPickup
		- App\Model\Orm\DeliveryMethod\Legacy
		- App\Model\Orm\DeliveryMethod\Electronic

		# PaymentMethods
		- App\Model\Orm\PaymentMethod\PaymentMethodRegistry
		- App\Model\Orm\PaymentMethod\BankTransfer
		- App\Model\Orm\PaymentMethod\Card
		- App\Model\Orm\PaymentMethod\CashOnDelivery
		- App\Model\Orm\PaymentMethod\Store
		- App\Model\Orm\PaymentMethod\BenefitCard
		- App\Model\Orm\PaymentMethod\PluxeeCard
		- App\Model\Orm\PaymentMethod\EdenredCard
		- App\Model\Orm\PaymentMethod\Certificate
		- App\Model\Orm\PaymentMethod\InvoicePayment
		- App\Model\Orm\PaymentMethod\Legacy

		- App\Model\Orm\PaymentMethod\PaymentMethodGroupFactory

		# CardPayment
		- App\Model\Orm\CardPayment\CardPaymentProcessor
		- App\Model\Orm\CardPayment\PaymentGateway\PaymentGatewayRegistry
		#- App\Model\Orm\CardPayment\PaymentGateway\ComgatePaymentGateway
		#- App\Model\Orm\CardPayment\PaymentGateway\BenefitPaymentGateway
		#- App\Model\Orm\CardPayment\PaymentGateway\EdenredPaymentGateway
		#- App\Model\Orm\CardPayment\PaymentGateway\PluxeePaymentGateway
		- App\Model\Orm\CardPayment\PaymentGateway\GopayPaymentGateway

		- App\Model\Orm\Order\Payment\CardPaymentInformationModel

		- App\Model\Orm\Order\OrderModel
		- App\Model\Orm\Order\PickupPoint\PickupPointModel
		- App\Model\Orm\Supply\SupplyModel
		- App\Model\Orm\Stock\StockModel

		- App\AdminModule\Presenters\Library\LibraryPresenter

		- App\Model\FileLockFactory
		- App\Model\PriceInfoFactory

		- \Curl\Curl
		- Nette\Caching\Cache

		- App\PostType\Page\Model\Orm\TreeModel
		- App\Model\Orm\File\FileModel
		- App\Model\Orm\Alias\AliasModel(adminAlias: %config.adminAlias%)
		- App\Model\Orm\AliasHistory\AliasHistoryModel
		- App\Model\Orm\User\UserModel
		- App\Model\Orm\UserHash\UserHashModel
		- App\Model\Orm\LibraryImage\LibraryImageModel
		- App\Model\Orm\LibraryImage\FlagImageService
		- App\Model\Orm\LibraryTree\LibraryTreeModel
		- App\Model\Orm\Product\ProductModel
		- App\Model\Orm\Product\ProductServices
		- App\Model\Orm\ProductLocalization\ProductLocalizationModel
		- App\Model\Orm\ProductVariant\ProductVariantModel
		- App\Model\Orm\ProductVariantLocalization\ProductVariantLocalizationModel
		- App\PostType\Page\Model\Orm\CatalogTreeModel
		- App\Model\Orm\ProductReview\ProductReviewModel
		- App\Model\Orm\Parameter\ParameterModel
		- App\Model\Orm\ParameterValue\ParameterValueModel
		- App\Model\Orm\PriceLevel\PriceLevelModel
		- App\Model\Orm\Rate\RateModel
		- App\Model\Orm\MyLibrary\MyLibraryModel
		- App\Model\ElasticSearch\Product\ProductIdsFinder

		- App\Model\Time\CurrentDateTimeProvider
		- App\Model\Time\TimeOperation
		- App\Model\Time\StoreTiming
		- App\Model\Time\DeliveryTiming

		## DTO
		- App\Model\DTO\Product\ProductDtoFactory
		- App\Model\DTO\Product\ProductDtoProvider

		#Promotions
		- App\Model\Promotion\PromotionApplicator
		- App\Model\Promotion\ApplicatorProvider
		- App\Model\Promotion\Applicator\BonusItemApplicator
		- App\Model\Promotion\Applicator\QuantityApplicator

		- App\Model\Orm\VoucherCode\VoucherCodeModel
		- App\Model\Orm\VoucherLimit\VoucherLimitModel

		- App\Model\Orm\Watchdog\WatchdogModel

		- App\Model\Product\UserProductTopList
		- App\Model\LastVisitedProduct

		- App\Model\PagesFactory

		- App\Model\Orm\EmailTemplate\EmailTemplateModel
		- App\Model\Email\EmailTemplateFactory

		- App\Model\EasyMessages
		- App\Model\Orm\State\StateModel
		- App\Model\Orm\Holiday\HolidayModel
		- App\Model\Orm\String\StringModel
		- App\Model\Orm\ImportCacheTime\ImportCacheTimeModel
		- App\Model\Template\PartCondition
		- App\Model\Price\ProductPriceModel
		- App\Model\Price\ProductVariantPriceModel
		- App\Model\Product\ProductText
		- App\Model\Price\MoneyWrapperHelper

		# Router
		- App\Model\Router\Filter
		- App\Model\Router\FilterLang
		- App\Model\Router\FilterRedirectOrigin
		- App\Model\Router\FilterSeoLink
		- App\Model\Router\FilterAlias(adminAlias: %config.adminAlias%)
		- App\Model\Router\FilterCommonParameters
		- App\Model\Router\FilterFilterParams
		- App\Model\Router\FilterVariantId
		- App\Model\Router\FilterRedirect(adminAlias: %config.adminAlias%)
		- App\Model\Router\FilterSharedLibrary
		- App\Model\Router\FilterReview


		- App\Model\Link\LinkFactory
		- App\Model\Link\LinkSeo
		- App\Model\Link\LinkChecker
		- Nette\Http\UrlScript
		- App\Model\Orm\Redirect\RedirectModel
		- App\Model\ImageResizerWrapper
		- App\Model\Orm\Mutation\MutationModel
		- App\Model\Mutation\MutationsHolder
		- App\Model\Mutation\MutationHolder
		- App\Model\Mutation\MutationDetector
		- App\Model\Mutation\BrowserMutationDetector
		- App\Model\Url\UrlChecker
		- App\Model\Sentry\SentryLogger

		- App\Model\CustomField\LazyValueFactory

		- App\Model\Orm\NewsletterEmail\NewsletterEmailModel
		- App\Model\Orm\OrmCleaner

		- App\Model\Cloner\SiteCloner
		- App\Model\Cloner\MutationCloner
		- App\Model\Cloner\EsIndexCommonCloner
		- App\Model\Cloner\PageCommonCloner
		- App\Model\Cloner\ProductLocalizationCommonCloner
		- App\Model\Cloner\BlogTagLocalizationCommonCloner
		- App\Model\Cloner\AuthorLocalizationCommonCloner
		- App\Model\Cloner\SeoLinkLocalizationCommonCloner
		- App\Model\Cloner\BlogLocalizationCommonCloner
		- App\Model\Cloner\ClonerProvider

		- App\Model\SocialLoginService

		- App\Model\Duplicator\ProductDuplicator
		- App\Model\Dbal\LogService
		- App\Model\Orm\User\User
		- App\Model\Orm\Mutation\Mutation
		- App\Model\Orm\User\User

		#countryDetector
		- App\Model\IpAddress\CookieStorage
		- App\Model\Currency\CookieStorage
		- App\Model\Orm\IpAddress\IpAddressModel
		- App\FrontModule\Components\CurrencyDetector\CurrencyDetectorFactory

		# comparators
		- App\Model\Comparators\ComparatorModeProvider

		- App\Model\Orm\User\UserProvider

		# product score calculation
		- App\Model\Product\Score\Dispatcher\DatabaseDispatcher
		- App\Model\Product\Score\Score
		- App\Model\Product\Score\DirectiveProvider
		- App\Model\Product\Score\Directive\DirectiveAvailable
		- App\Model\Product\Score\Directive\DirectiveProfit
		- App\Model\Product\Score\Directive\DirectivePublishDate
		- App\Model\Product\Score\Directive\DirectiveImage
		- App\Model\Product\Score\Directive\DirectiveDescription
		- App\Model\Product\Score\Directive\DirectiveVideo
		- App\Model\Product\Score\Directive\DirectivePreview
		- App\Model\Product\Score\Directive\DirectiveMarketability
		- App\Model\Product\Score\Directive\DirectiveAction
		- App\Model\Product\Score\Directive\DirectivePresent
		- App\Model\Product\Score\Directive\DirectiveManualBoost
		- App\Model\Product\Score\Directive\DirectiveErpScore
		- App\Model\Product\Score\Directive\DirectiveHeurekaPopularity
		- App\Model\Product\CategoryMainPositionCalculator
		- App\Model\Product\SimilarBuy\Calculator
		- Jaybizzle\CrawlerDetect\CrawlerDetect
		- App\Model\Product\SoldCount\ProductLocalizationSoldCountCalculator
		- App\Model\Product\SoldCount\Dispatcher\DatabaseDispatcher

		# ROBOTS
		- App\Model\Robots\RobotsRows(%robotsTxt.rows%)

#		EMAILY
		- App\Model\Mailer\BaseMailer
		- App\Model\Email\CommonFactory

		- App\Console\ExchangeRate\DownloadCommand
		- App\Infrastructure\Latte\Functions

		- App\Model\Orm\ClassEventSectionMetadata\ClassEventSectionMetadataModel

		#Structured data
		- App\FrontModule\Components\StructuredData\StructuredDataFactory

		nette.latteFactory:
			setup:
			- addFilter(timeAgoInWords, [App\Infrastructure\Latte\Filters, timeAgoInWords])
			- addFilter(plural, [App\Infrastructure\Latte\Filters, plural])
			- addFilter(parseVideoId, [App\Infrastructure\Latte\Filters, parseVideoId])
			- addFilter(niceDate, [App\Infrastructure\Latte\Filters, niceDate])
			- addFilter(money, [App\Infrastructure\Latte\Filters, formatMoney])
			- addFilter(texy, [App\Infrastructure\Latte\Filters, texy])
			- addFilter(skDate, [App\Infrastructure\Latte\Filters, skDate])
			- addFilter(prepareStrJs, [App\Infrastructure\Latte\Filters, prepareStrJs])
			- addFilter(clear, [App\Infrastructure\Latte\Filters, clear])
			- addFilter(copyright, [App\Infrastructure\Latte\Filters, copyright])
			- addFilter(icon, [App\Infrastructure\Latte\Filters, icon])
			- addFilter(lineExploder, [App\Infrastructure\Latte\Filters, lineExploder])
			- addFilter(exploder, [App\Infrastructure\Latte\Filters, exploder])
			- addFilter(stock, [App\Infrastructure\Latte\Filters, stock])
			- addFilter(phoneFormat, [App\Infrastructure\Latte\Filters, phoneFormat])
			- addFilter(formatNumberPrecision, [App\Infrastructure\Latte\Filters, formatNumberPrecision])
			- addFilter(tables, [App\Infrastructure\Latte\Filters, tables])
			- addFilter(lazyLoading, [App\Infrastructure\Latte\Filters, lazyLoading])
			- addFilter(currency, [App\Infrastructure\Latte\Filters, formatCurrency])
			- addFilter(contrastColorType, [App\Infrastructure\Latte\Filters, getContrastColor])
			- addFilter(darken, [App\Infrastructure\Latte\Filters, darkenColor])
			- addFunction(translate, [@App\Infrastructure\Latte\Functions, translate])
			- addFunction('cacheKey', [App\Infrastructure\Latte\Functions, cacheKey])
			- addFilter(obfuscateEmailAddresses, [App\Infrastructure\Latte\Filters, obfuscateEmailAddresses])
			- addFilter(formatSize, [App\Infrastructure\Latte\Filters, formatSize])
			- addFilter(duration, [App\Infrastructure\Latte\Filters, formatDuration])
			- addFilter(totalDuration, [App\Infrastructure\Latte\Filters, formatTotalDuration])
			- addFilter(truncateSafe, [App\Infrastructure\Latte\Filters, truncateSafe])
			- addFilter(externalLink, [App\Infrastructure\Latte\Filters, externalLink])

		- App\Model\Google\GoogleProviderFactory(
			clientId: %google.oauth.clientId%
			clientSecret: %google.oauth.clientSecret%
		)
		- implement: App\FrontModule\Components\FacebookLogin\FacebookLoginFactory
		  arguments: { isEnabled: %config.socialLogin.facebook.isEnabdled% }
		- implement: App\FrontModule\Components\SeznamLogin\SeznamLoginFactory
		  arguments: { isEnabled: %config.socialLogin.seznam.isEnabdled% }

		- App\Model\Form\FormThrottler
		- App\FrontModule\Components\Cart\CartPricesWithVatModeProvider

		# Vocative service for Czech and Slovak names
		- App\Utils\Vocative\VocativeService

	latte:
		extensions:
			- Latte\Essential\TranslatorExtension(@translatorDB)
