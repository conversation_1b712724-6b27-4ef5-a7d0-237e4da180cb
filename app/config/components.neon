	services:
		#FORM/COMPONENT FACTORIES
		- App\Components\VisualPaginator\VisualPaginatorFactory
		- App\Model\Form\FormDataLogger
		- App\Model\Form\CommonFormFactory
		- App\FrontModule\Components\BookReviews\BookReviewsFactory
		- App\FrontModule\Components\FormMessage\FormMessageFactory
		- App\FrontModule\Components\Cart\CartFactory
		- App\FrontModule\Components\CartFreeDelivery\CartFreeDeliveryFactory
		- App\FrontModule\Components\CartDelivery\CartDeliveryFactory
		- App\FrontModule\Components\CartUserDetail\CartUserDetailFactory
		- App\FrontModule\Components\CartGift\CartGiftFactory
		- App\FrontModule\Components\AddToCart\AddToCartFactory
		- App\FrontModule\Components\UserAddressForm\UserAddressFormFactory
		- App\FrontModule\Components\OrderHistory\OrderHistoryFactory
		- App\FrontModule\Components\OrderCreateUser\OrderCreateUserFactory
		- App\FrontModule\Components\OrderBenefitCardPay\OrderBenefitCardPayFactory
		- App\FrontModule\Components\TopTitle\TopTitleFactory
		- App\FrontModule\Components\InfoBox\InfoBoxFactory

		- App\FrontModule\Components\ProductList\ProductListFactory
		- App\FrontModule\Components\ProductList\ProductListInterestedFactory
		# ProductList setup creators
		- App\FrontModule\Components\ProductList\SetupCreator\SetupCreatorRegistry
		- App\FrontModule\Components\ProductList\SetupCreator\News
		- App\FrontModule\Components\ProductList\SetupCreator\Bestsellers
		- App\FrontModule\Components\ProductList\SetupCreator\Upcomming
		- App\FrontModule\Components\ProductList\SetupCreator\Interested
		- App\FrontModule\Components\ProductList\SetupCreator\Bestselling
		- App\FrontModule\Components\ProductList\SetupCreator\LastVisited
		- App\FrontModule\Components\ProductList\SetupCreator\Certificates

		- App\AdminModule\Presenters\Mutation\Components\SynonymsDataGrid\SynonymsDataGridFactory

		- App\FrontModule\Components\Watchdog\WatchdogFactory
		- App\FrontModule\Components\MyWatchdog\MyWatchdogFactory

		- App\FrontModule\Components\PhotoBank\PhotoBankFactory

		-
		    implement: App\AdminModule\Components\SignInForm\SignInFormFactory
		    inject: true
		-
		    implement: App\AdminModule\Components\LostPasswordForm\LostPasswordFormFactory
		    inject: true

		-
		    implement: App\AdminModule\Components\ResetPasswordForm\ResetPasswordFormFactory
		    inject: true

		- App\FrontModule\Components\ContactForm\ContactFormFactory
		- {implement: App\FrontModule\Components\ContactForm\ContactFormControlFactory, inject: true}

		- App\FrontModule\Components\NewsletterForm\NewsletterFormFactory
		- {implement: App\FrontModule\Components\NewsletterForm\NewsletterFormControlFactory, inject: true}

		-
			implement: App\FrontModule\Components\SignInForm\SignInFormFactory
			inject: true
		-
			implement: App\FrontModule\Components\ProfileForm\ProfileFormFactory
			inject: true
		-
			implement: App\FrontModule\Components\ChangePasswordForm\ChangePasswordFormFactory
			inject: true
		-
			implement: App\FrontModule\Components\UserInterestForm\UserInterestFormFactory
			inject: true
		-
			implement: App\FrontModule\Components\RegistrationForm\RegistrationFormFactory
			inject: true
		-
			implement: App\FrontModule\Components\LostPasswordForm\LostPasswordFormFactory
			inject: true
		- App\Components\MessageForForm\MessageForFormFactory
		-
			implement: App\FrontModule\Components\CanonicalUrl\CanonicalUrlFactory
			inject: true
		-
			implement: App\FrontModule\Components\Menu\MenuFactory
			inject: true

		-
			implement: App\FrontModule\Components\Robots\RobotsFactory
			inject: true
		-
			implement: App\FrontModule\Components\SeoTools\SeoToolsFactory
			inject: true
		-
			implement: App\FrontModule\Components\Categories\CategoriesFactory
			inject: true
		-
			implement: App\FrontModule\Components\MainCategories\MainCategoriesFactory
			inject: true
		-
			implement: App\FrontModule\Components\ServiceMenu\ServiceMenuFactory
			inject: true
		-
			implement: App\FrontModule\Components\UserMenu\UserMenuFactory
			inject: true
		-
			implement: App\FrontModule\Components\UserSideMenu\UserSideMenuFactory
			inject: true
		-
			implement: App\FrontModule\Components\UserAnimalList\UserAnimalListFactory
			inject: true
		-
			implement: App\FrontModule\Components\UserAnimalForm\UserAnimalFormFactory
			inject: true

		-
			implement: App\FrontModule\Components\Breadcrumb\BreadcrumbFactory
			inject: true
		-
			implement: App\FrontModule\Components\CurrencyToggle\CurrencyToggleFactory
			inject: true

		- App\FrontModule\Components\ProductParameters\ProductParametersFactory

		-
			implement: App\FrontModule\Components\DeliveryOptions\DeliveryOptionsFactory
			inject: true

		- App\AdminModule\Components\ApiTokenDataGrid\ApiTokenDataGridFactory
		- App\AdminModule\Components\ApiTokenShellForm\ApiTokenShellFormFactory


		- implement: App\FrontModule\Components\GoogleConnect\GoogleConnectFactory
		  arguments: { isEnabled: %google.oauth.isPublic% }

		- implement: App\FrontModule\Components\GoogleLogin\GoogleLoginFactory
		  arguments: { isEnabled: %google.oauth.isPublic% }

		- implement: App\AdminModule\Components\GoogleLogin\GoogleLoginFactory
		  arguments: { isEnabled: ::boolval(%google.oauth.clientId%) }

		- App\FrontModule\Components\MyLibrary\MyLibraryFactory
		- App\FrontModule\Components\AddToMyLibrary\AddToMyLibraryFactory
		- App\FrontModule\Components\BoughtProducts\BoughtProductsFactory

		control.savedForLate:
			class: App\FrontModule\Components\SavedForLater\SavedForLater
			lazy: true
