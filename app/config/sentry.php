<?php

use App\Model\Sentry\Filter\BeforeSendFilter;

return [
	'extensions' => [
		'sentry' => \Contributte\Logging\DI\SentryLoggingExtension::class
	],
	'services' => [
		BeforeSendFilter::class
	],
	'sentry' => [
		'url' => '',
		'options' => [
			'environment' => 'production',
			'attach_stacktrace' => true,
			'send_default_pii' => true,
			'traces_sample_rate' => 0.2,
			'default_integrations' => false,
			'before_send' => ['@App\Model\Sentry\Filter\BeforeSendFilter', 'filter'],
			'integrations' => [
				\Sentry\Integration\RequestIntegration::class,
				\Sentry\Integration\FrameContextifierIntegration::class,
				\Sentry\Integration\EnvironmentIntegration::class,
				\Sentry\Integration\ModulesIntegration::class,
				\Sentry\Integration\FatalErrorListenerIntegration::class,
				\Sentry\Integration\ExceptionListenerIntegration::class,
				\Sentry\Integration\ErrorListenerIntegration::class,
			]
		]
	]
];
