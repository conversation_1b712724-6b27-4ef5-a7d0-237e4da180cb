parameters:
	stageName: 'prod'

	config:
		isDev: false
		env: production

		cookie:
			enableModal: false

		mutations:
			cs:
				domain: superadmin.www6.superkoderi.cz
				urlPrefix: false
				rootId: 1
				hidePageId: 43
				systemPageId: 398
				internalName: česky
				mutationId: 1
				googleAnalyticsCode: # 'UA-222222-1'
				robots: "index, follow"
				sitemap: true
				heurekaReviewXmlUrl: 'https://www.heureka.cz/direct/dotaznik/export-product-review.php?key=26f8728fb895a67c987bac90861bf712'
				heurekaShopReviewXmlUrl: 'https://www.heureka.cz/direct/dotaznik/export-review.php?key=26f8728fb895a67c987bac90861bf712'
				cookie:
					enableModal: false

		domainUrl: "https://superadmin.www6.superkoderi.cz/"
		domainUrlPdf: "https://superadmin.www6.superkoderi.cz/"

		translations:
			insertNew: false
			markUsage: false                                  #kdyz neni zalozeny prekad v DB -> vytovri se novy

		exponea:
			projectToken: ''
			publicKey: ''
			privateKey: ''

includes:
	- header.php



services:
	cacheStorage:
		class: Nette\Caching\Storages\FileStorage('%tempDir%/cache')

	nette.latteFactory:
		setup:
#			- setTempDirectory("../temp/cache/latte")

console.cache:
	generators:
		translate: App\Model\Cache\WarmUpGenerator\FillTranslate
#		productBox: App\Model\Cache\WarmUpGenerator\FillProductBox



