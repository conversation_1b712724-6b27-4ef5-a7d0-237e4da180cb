parameters:
	stageName: 'dev'
	config:
		isDev: true
		env: local

		esBaseName: %config.projectName%_%stageName%
		domainUrl: 'https://superadmin-dev.www6.superkoderi.cz/'
		domainUrlPdf: ''
		debuggerEditor: 'phpstorm://open?file=%file&line=%line'
		translations:
			insertNew: false
			markUsage: false

		dbalLog: false # vypis sql dotazu do souboru nettelog/mysql.log

		adminMail: '<EMAIL>'

		googleAnalyticsCode: "GTM-SUPERADMIN"
		googleAnalyticsName: "auto"

		gopay:
			cs:
				goid: '8710217153' #mojecalibra.cz sandbox
				clientId: '1790281933'
				clientSecret: 'cKuKTkfd'
				gatewayUrl: 'https://gw.sandbox.gopay.com/api/'

		mutations:
			cs:
				domain: superadmin-dev.www6.superkoderi.cz
				urlPrefix: false
				mutationId: 1
				rootId: 1
				hidePageId: 43
				systemPageId: 398
				internalName: česky
				robots: "noindex, nofollow"
				sitemap: true
				heurekaReviewXmlUrl: 'https://www.heureka.cz/direct/dotaznik/export-product-review.php?key=26f8728fb895a67c987bac90861bf712'
				heurekaShopReviewXmlUrl: 'https://www.heureka.cz/direct/dotaznik/export-review.php?key=26f8728fb895a67c987bac90861bf712'
#			sk:
#				domain: superadmin-dev.www6.superkoderi.cz
#				urlPrefix: 'sk'
#				mutationId: 2
#				rootId: 2794
#				robots: "noindex, nofollow"
	migrations:
		withDummyData: false

services:
	nette.latteFactory:
		setup:
#			- setTempDirectory("../temp/cache/latte")
#			- setTempDirectory("") # odkomentované => vypnuti cache u latte

	cacheStorage:
		class: Nette\Caching\Storages\FileStorage('%tempDir%/cache')


session:
	expiration: 14 days
	cookie_secure: false

tracy:
#	maxDepth: 4
	bar:
		- Nette\Bridges\HttpTracy\SessionPanel

console.cache:
	cleaners:
		netteCaching: Contributte\Console\Extra\Cache\Cleaners\NetteCachingStorageCleaner

#application:
#	catchExceptions: true

