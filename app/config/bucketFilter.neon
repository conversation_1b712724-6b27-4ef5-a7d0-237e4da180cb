parameters:
	config:
		bucketFilter:
			flags: [] #['isNew', 'isOld', 'isInDiscount', 'isInStore']
			flagValues: []
			ranges: ['price']
			maxParameterValues: 200

services:

	- App\Model\BucketFilter\BucketFilterFactory
	- App\Model\BucketFilter\QueryAggregation
	- App\Model\BucketFilter\QueryFilter

	#setup creator for catalog
	- App\Model\BucketFilter\SetupCreator\Catalog\BoxListFactory
	- App\Model\BucketFilter\SetupCreator\Catalog\BasicElasticItemListFactory
	- App\Model\BucketFilter\SetupCreator\Catalog\ElasticItemListFactory

	- App\Model\BucketFilter\SetupCreator\Calendar\BoxListFactory
	- App\Model\BucketFilter\SetupCreator\Calendar\BasicElasticItemListFactory
	- App\Model\BucketFilter\SetupCreator\Calendar\ElasticItemListFactory

	- App\Model\BucketFilter\SetupCreator\Blog\BoxListFactory
	- App\Model\BucketFilter\SetupCreator\Blog\BasicElasticItemListFactory
	- App\Model\BucketFilter\SetupCreator\Blog\ElasticItemListFactory

	- App\Model\BucketFilter\SetupCreator\Search\BoxListFactory
	- App\Model\BucketFilter\SetupCreator\Search\BasicElasticItemListFactory
	- App\Model\BucketFilter\SetupCreator\Search\ElasticItemListFactory

	- App\Model\BucketFilter\SetupCreator\Discount\BoxListFactory
	- App\Model\BucketFilter\SetupCreator\Discount\BasicElasticItemListFactory
	- App\Model\BucketFilter\SetupCreator\Discount\ElasticItemListFactory

	- App\Model\BucketFilter\CatalogParameter
	- App\Model\BucketFilter\QueryBaseFilter
	- App\Model\BucketFilter\FilterResultMapper
	- App\Model\BucketFilter\SortCreator

	- App\Model\Orm\ParameterValue\ParameterValueFilterHelper

	- App\Model\BucketFilter\BucketFilterBuilderFactory
	- App\Model\BucketFilter\SetupCreator\FilterFlagsConfig


