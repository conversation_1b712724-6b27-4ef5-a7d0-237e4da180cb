monolog:
	channel:
		scoreCalculator:
			handlers:
				- Monolog\Handler\RotatingFileHandler(%appDir%/../nettelog/ScoreCalculator/score.log, 30, Monolog\Level::Info)
			processors:
				- Monolog\Processor\MemoryPeakUsageProcessor()
				- Monolog\Processor\ProcessIdProcessor

		soldCountCalculator:
			handlers:
				- Monolog\Handler\RotatingFileHandler(%appDir%/../nettelog/SoldCountCalculator/soldCount.log, 30, Monolog\Level::Info)
			processors:
				- Monolog\Processor\MemoryPeakUsageProcessor()
				- Monolog\Processor\ProcessIdProcessor

		xmlGenerator:
			handlers:
				- Monolog\Handler\RotatingFileHandler(%appDir%/../nettelog/XmlGenerator/xml_generator.log, 30, Monolog\Level::Info)
				#- Monolog\Handler\NullHandler
			processors:
				- Monolog\Processor\MemoryPeakUsageProcessor
				- Monolog\Processor\ProcessIdProcessor
