parameters:
	migrations:
		withDummyData: false

extensions:
	migrations: Nextras\Migrations\Bridges\NetteDI\MigrationsExtension

migrations:
	dir: %appDir%/../migrations # migrations base directory
	driver: mysql               # pgsql or mysql
	dbal: nextras               # nextras, nette, doctrine or dibi

	groups:
		core_structure:
			enabled: true
			directory: %appDir%/../migrations/core/structures
			dependencies: [state_structure]
			#generator: null
		core_basic_data:
			enabled: true
			directory: %appDir%/../migrations/core/basic-data
			dependencies: [core_structure, state_basic_data]
			#generator: null
		core_dummy_data:
			enabled: %migrations.withDummyData%
			directory: %appDir%/../migrations/core/dummy-data
			dependencies: [core_basic_data]

		erp_structure:
			enabled: true
			directory: %appDir%/../migrations/erp/structures
			dependencies: [core_structure]

		contact_message_structure:
			enabled: true
			directory: %appDir%/../migrations/contactMessage/structures
			dependencies: [core_structure]

		page_structure:
			enabled: true
			directory: %appDir%/../migrations/page/structures
			dependencies: [core_structure]
			#generator: null
		page_basic_data:
			enabled: true
			directory: %appDir%/../migrations/page/basic-data
			dependencies: [core_structure, page_structure]
			#generator: null
		page_dummy_data:
			enabled: %migrations.withDummyData%
			directory: %appDir%/../migrations/page/dummy-data
			dependencies: [page_basic_data]

		state_structure:
			enabled: true
			directory: %appDir%/../migrations/state/structures
			dependencies: [core_structure]
		state_basic_data:
			enabled: true
			directory: %appDir%/../migrations/state/basic-data
			dependencies: [core_basic_data, state_structure]

		blog_structure:
			enabled: true
			directory: %appDir%/../migrations/blog/structures
			dependencies: [core_structure]
		blog_basic_data:
			enabled: true
			directory: %appDir%/../migrations/blog/basic-data
			dependencies: [page_structure, core_basic_data, blog_structure]
		blog_dummy_data:
			enabled: %migrations.withDummyData%
			directory: %appDir%/../migrations/blog/dummy-data
			dependencies: [blog_basic_data]

		author_structure:
			enabled: true
			directory: %appDir%/../migrations/author/structures
			dependencies: [core_structure, blog_structure]
		author_basic_data:
			enabled: true
			directory: %appDir%/../migrations/author/basic-data
			dependencies: [page_structure, core_basic_data, author_structure, blog_basic_data]
		author_dummy_data:
			enabled: %migrations.withDummyData%
			directory: %appDir%/../migrations/author/dummy-data
			dependencies: [author_basic_data]

		banner_structure:
			enabled: true
			directory: %appDir%/../migrations/banner/structures
			dependencies: [core_structure]
		banner_basic_data:
			enabled: true
			directory: %appDir%/../migrations/banner/basic-data
			dependencis: [banner_structure]

		other_structure:
			enabled: true
			directory: %appDir%/../migrations/other/structures
			dependencies: [core_structure]
		other_basic_data:
			enabled: true
			directory: %appDir%/../migrations/other/basic-data
			dependencies: [other_structure, core_basic_data]

		user_structure:
			enabled: true
			directory: %appDir%/../migrations/user/structures
			dependencies: [core_structure]
		user_basic_data:
			enabled: true
			directory: %appDir%/../migrations/user/basic-data
			dependencies: [core_basic_data, user_structure]
		user_dummy_data:
			enabled: %migrations.withDummyData%
			directory: %appDir%/../migrations/user/dummy-data
			dependencies: [user_basic_data]

		emailTemplate_structure:
			enabled: true
			directory: %appDir%/../migrations/emailTemplate/structures
			dependencies: [core_structure]
		emailTemplate_basic_data:
			enabled: true
			directory: %appDir%/../migrations/emailTemplate/basic-data
			dependencies: [core_basic_data, emailTemplate_structure]

		parameter_structure:
			enabled: true
			directory: %appDir%/../migrations/parameter/structures
			dependencies: [core_structure]
		parameter_basic_data:
			enabled: true
			directory: %appDir%/../migrations/parameter/basic-data
			dependencies: [core_basic_data, parameter_structure]

		product_structure:
			enabled: true
			directory: %appDir%/../migrations/product/structures
			dependencies: [core_structure]

		theme_structure:
			enabled: true
			directory: %appDir%/../migrations/theme/structures
			dependencies: [core_structure]

		product_basic_data:
			enabled: true
			directory: %appDir%/../migrations/product/basic-data
			dependencies: [core_basic_data, product_structure]

		product_dummy_data:
			enabled: %migrations.withDummyData%
			directory: %appDir%/../migrations/product/dummy-data
			dependencies: [product_structure, product_basic_data]

		stock_structure:
			enabled: true
			directory: %appDir%/../migrations/stock/structures
			dependencies: [core_structure]
		stock_basic_data:
			enabled: true
			directory: %appDir%/../migrations/stock/basic-data
			dependencies: [core_basic_data, stock_structure]

		seolink_structure:
			enabled: true
			directory: %appDir%/../migrations/seolink/structures
			dependencies: [core_structure]

		writer_structure:
			enabled: true
			directory: %appDir%/../migrations/writer/structures
			dependencies: [core_structure]

		promotion_structure:
			enabled: true
			directory: %appDir%/../migrations/promotion/structures
			dependencies: [core_structure]

		system_message_structure:
			enabled: true
			directory: %appDir%/../migrations/systemMessage/structures
			dependencies: [core_structure]

		publisher_structure:
			enabled: true
			directory: %appDir%/../migrations/publisher/structures
			dependencies: [core_structure]

		discount_structure:
			enabled: true
			directory: %appDir%/../migrations/discount/structures
			dependencies: [core_structure]

		menu_main_structure:
			enabled: true
			directory: %appDir%/../migrations/menuMain/structures
			dependencies: [core_structure]

		supplier_structure:
			enabled: true
			directory: %appDir%/../migrations/supplier/structures
			dependencies: [core_structure]

		rate_structure:
			enabled: true
			directory: %appDir%/../migrations/rate/structures
			dependencies: [core_structure]

		product_review:
			enabled: true
			directory: %appDir%/../migrations/productReview/structures
			dependencies: [core_structure]

		review_structure:
			enabled: true
			directory: %appDir%/../migrations/review/structures
			dependencies: [core_structure]

		order_structure:
			enabled: true
			directory: %appDir%/../migrations/order/structures
			dependencies: [core_structure]

		order_basic_data:
			enabled: true
			directory: %appDir%/../migrations/order/basic_data
			dependencies: [core_structure, order_structure]

		order_dummy_data:
			enabled: %migrations.withDummyData%
			directory: %appDir%/../migrations/order/dummy-data
			dependencies: [order_basic_data]

		tag_structure:
			enabled: true
			directory: %appDir%/../migrations/tag/structures
			dependencies: [core_structure, core_basic_data]

		tag_dummy_data:
			enabled: %migrations.withDummyData%
			directory: %appDir%/../migrations/tag/dummy-data
			dependencies: [tag_structure]

		ip_address_structure:
			enabled: true
			directory: %appDir%/../migrations/ipAddress/structures
			dependencies: [core_structure, core_basic_data]

		my_library:
			enabled: true
			directory: %appDir%/../migrations/myLibrary/structures
			dependencies: [core_structure, core_basic_data]

		shop_review_structure:
			enabled: true
			directory: %appDir%/../migrations/shopReview/structures
			dependencies: [core_structure]

		brand_structure:
			enabled: true
			directory: %appDir%/../migrations/brand/structures
			dependencies: [core_structure, blog_structure]

		brand_basic_data:
			enabled: true
			directory: %appDir%/../migrations/brand/basic-data
			dependencies: [core_structure, blog_structure, brand_structure]
