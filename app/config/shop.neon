parameters:
	config:
		shop:
			enableLastVisited: true
			lastVisitedProductCount: 50 #

			enableBestSeller: false
			bestSellerCount: 4

			newsletterDefaultVoucherId: 2 #todo revision

			productsPaging: 12

		pickupPoints:
			ppl:
				client_id: 2W133922W
				client_secret: 7Ihj6UvrgIUwwdF1S66uHpRXXuwEASpT
			packeta:
				api_key: 05640ce582a87216

		deliveryPicker:
			locale: cs
			currency: CZK
			#country: CZ
			pluginPath: 'static/js/deliveryPickerPlugin.js'
			pickupPointsResponsePath: 'pickupPoints'
			apiKey: %config.google.apiKey%
			defaultZoom: 7
			defaultCenter: { lat: 49.721102, lng: 15.402829 }

		payments:
			comgate:
				id: 492850
				secret: 'QcL1VaDkoi61UIyOr5hXx1lfZLxTG1sT'
				testMode: true
				logger: true
				expirationTime: '1h' # Délka expirace platby. Povolená hodnota je celé číslo následované písmenem zvolené časové jednotky: 'm' (minuty), 'h' (hodiny) nebo 'd' (dny). Nap<PERSON><PERSON>lad '30m' (30 minut) nebo '10h' (10 hodin) nebo '2d' (2 dny). Jednotky nelze kombinovat. Výsledná délka musí být v rozmezí 30 minut až 7 dní. Pokud není vyplněno, použije se hodnota v nastavení obchodu zvolená v Klientském portálu.
			benefity:
				url: https://online.benefity.cz/payment/
				username: "600130001"
				password: "600130001"
				merchantNumber: "600-130-000"
				benefitGroup: 138
			edenred:
				url: https://www.benefitycafe.cz/placeordergateway.aspx
				returnUrl: %config.domainUrl%api/v1/payment/edenred-result
				notificationEmail: null # ak null pouzije sa e-mail zakaznika
				salt: f5W29c4Q2HiS8j93RU5K5rxpanzTiyjf
				verifyDigest: true # only for dev
			pluxee:
				id: 2000000109
				url: https://brana.ucet.uat.pluxee.cz/  # c. karty: 6203011299093418 | sec. code: QRN172
		watchdog:
			notificationEmail: '<EMAIL>'



monolog:
	channel:
		comgate:
			handlers:
				- Monolog\Handler\RotatingFileHandler(%appDir%/../nettelog/Comgate/default.log, 30, Monolog\Level::Info)
				#- Monolog\Handler\NullHandler
			processors:
				- Monolog\Processor\MemoryPeakUsageProcessor


