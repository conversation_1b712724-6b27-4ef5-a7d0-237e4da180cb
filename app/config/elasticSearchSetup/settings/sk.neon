index:
	number_of_replicas: 0
	max_result_window: 2500000

analysis:
	filter:
		#czech
#		fileSynonym:
#			type: "synonym"
#			synonyms_path: %pathToSynonyms%/cs.txt

		stopWords:
			type: "stop"
			stopwords: ["_slovak_"]
		customEdgeNgram:
			type: edge_ngram
			min_gram: 3
			max_gram: 10
			token_chars: [letter, digit]


#		customCommonGrams:
#			type: "common_grams"
#			common_words: ["ks", "l", "litry", "litrů", "litr"]

#						czechFileSynonym:
#							type: "synonym"
#							synonyms_path: "analysis/synonyms.txt"
#
#		dictionary_CZ:
#			type: 'hunspell'
#			locale: 'cs_CZ'

#						customStemmer:
#							type: "stemmer"
#							name: "Czech"

#
#		mynGram:
#			type: "nGram"
#			min_gram: 2
#			max_gram: 3
#			token_chars: ["letter", "digit"]
#
#		customEdgeNgram:
#			type: "edge_ngram"
#			min_gram": 2
#			max_gram: 6
#
#		customWordDelimiter:
#			type: "word_delimiter"
#			catenate_all: "true"

	analyzer:
		dictionary:
			filter: ["lowercase", "stopWords", "synonym", "unique"]
			type: custom
			tokenizer: standard

		dictionaryWithoutStop:
			filter: ["lowercase","synonym", "unique"]
			type: custom
			tokenizer: standard

		customEdgeNgram:
			filter: [asciifolding, customEdgeNgram, lowercase, unique]
			type: "custom"
			tokenizer: standard

#		csSynonym:
#			filter: ["lowercase","stopWords", "fileSynonym", "unique", "asciifolding"]
#			type: "custom"
#			tokenizer: "standard"

#		customWordDelimiter:
#			filter: ["stop","customWordDelimiter","asciifolding","lowercase","unique"]
#			type: "custom"
#			tokenizer: "standard"
#
#		customCommonGrams:
#			filter: ["asciifolding","lowercase","stop","customCommonGrams","unique"]
#			type: "custom"
#			tokenizer: "standard"
#
#		customEdgeNgram:
#			filter: ["asciifolding","lowercase","stop","customEdgeNgram","unique",]
#			type: "custom"
#			tokenizer: "standard"
#
##						cestina:
##							tokenizer: "camel"
##							filter: [ "lowercase", "stop", "asciifolding"]
##						sub_index:
##							type: custom
##							tokenizer: "camel"
##							filter: [ "lowercase", "asciifolding",  "mynGram"]
##						sub_search:
##							type: custom
##							tokenizer: camel
##							filter: [ "lowercase", "asciifolding"]
##						analyzer_firstletter:
##							tokenizer: keyword
##							filter: lowercase
##					tokenizer:
##						camel:
##							type: "pattern"
##							pattern: "([^\\p{L}\\d]+)|(?<=\\D)(?=\\d)|(?<=\\d)(?=\\D)"
