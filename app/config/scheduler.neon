extensions:
	scheduler: App\Scheduler\SchedulerExtension

scheduler:
	consoleMode: %consoleMode%
	options:
		default:
			#stateful:
			#	adapter: Symfony\Component\Cache\Adapter\RedisAdapter
			#	dsn: @redis.connection.scheduler.client #"redis:?host[%redis.host%:%redis.port%]&dbindex=4"
			lock: @redis.connection.lock.client #"redis:?host[%redis.host%:%redis.port%]&dbindex=%redis.lock.database%"
		import:
			#stateful:
			#	adapter: Symfony\Component\Cache\Adapter\RedisAdapter
			#	dsn: @redis.connection.scheduler.client #"redis:?host[%redis.host%:%redis.port%]&dbindex=4"
			lock: @redis.connection.lock.client
		import_stock:
			#stateful:
			#	adapter: Symfony\Component\Cache\Adapter\RedisAdapter
			#	dsn: @redis.connection.scheduler.client #"redis:?host[%redis.host%:%redis.port%]&dbindex=4"
			lock: @redis.connection.lock.client

monolog:
	channel:
		command:
			handlers:
				- Monolog\Handler\RotatingFileHandler(%appDir%/../nettelog/SymfonyConsole/command.log, 30, Monolog\Level::Info)
			processors:
				- Monolog\Processor\MemoryPeakUsageProcessor

decorator:
	App\Console\BaseCommand:
		setup:
			- setLogger(@monolog.logger.command)

services:
#	- App\Scheduler\Handler\TestHandler
#	- App\Scheduler\ScheduleService
