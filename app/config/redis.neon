extensions:
	redis: Contributte\Redis\DI\RedisExtension

parameters:
	redis:
		host: 127.0.0.1
		port: 6379
		password: ''
		storage:
			database: '0'
			prefix: "%config.projectName%:%stageName%:%config.webVersion%:"
		session:
			database: '1'
			prefix: "%config.projectName%:%stageName%:"
		messages:
			database: '2'
			prefix: "%config.projectName%:%stageName%:"
		lock:
			database: '3'
			prefix: "%config.projectName%:%stageName%:%config.webVersion%:"
		scheduler:
			database: '4'
			prefix: "%config.projectName%:%stageName%:%config.webVersion%:"

redis:
	debug: %debugMode%
	connection:
		default:
			uri: tcp://%redis.host%:%redis.port%
			sessions: false
			storage: true
			options:
				prefix: %redis.storage.prefix%
				parameters:
					database: %redis.storage.database%
					password: %redis.password%
		sessions:
			uri: tcp://%redis.host%:%redis.port%
			sessions: true
			storage: false
			options:
				prefix: %redis.session.prefix%
				parameters:
					database: %redis.session.database%
					password: %redis.password%

		lock:
			uri: tcp://%redis.host%:%redis.port%
			sessions: false
			storage: true
			options:
				prefix: %redis.lock.prefix%
				parameters:
					database: %redis.lock.database%
					password: %redis.password%
		scheduler:
			uri: tcp://%redis.host%:%redis.port%
			sessions: false
			storage: true
			options:
				prefix: %redis.scheduler.prefix%
				parameters:
					database: %redis.scheduler.database%
					password: %redis.password%
