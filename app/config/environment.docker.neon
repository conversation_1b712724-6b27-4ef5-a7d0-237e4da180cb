includes:
	- environment.dev.neon

parameters:
	stageName: local

	admin:
		allowedIpRanges:
			- "**********/16" # default docker bridge range
			- "***********/16" # default orbstack range

	config:
		domainUrl: https://omega.superkoders.test/
		mutations:
			cs:
				domain: omega.superkoders.test
		elastica:
			config:
				host: omega_es
	amqp:
		host: omega_rabbitmq
		vhost: null

	database:
		host: omega_db
		database: omega
		user: omega
		password: omega

	redis:
		host: omega_redis

http:
	proxy:
		- *********/8
		- ***********/16

#session:
#	autoStart: true

mail:
	host: omega_mailcatcher
	port: 1025
