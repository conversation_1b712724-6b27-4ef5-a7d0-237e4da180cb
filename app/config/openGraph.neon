services:
	#OpenGraph
	- App\FrontModule\Components\OpenGraph\OpenGraphFactory
	- App\FrontModule\Components\OpenGraph\Resolver
	- App\FrontModule\Components\OpenGraph\Mappers\MapperDto
	- App\FrontModule\Components\OpenGraph\Mappers\MapperTemplate
	- App\FrontModule\Components\OpenGraph\DTO\DefaultDto
	- App\FrontModule\Components\OpenGraph\DTO\CategoryDto
	- App\FrontModule\Components\OpenGraph\DTO\ProductDto
	- App\FrontModule\Components\OpenGraph\DTO\SharedLibraryDto
	- App\FrontModule\Components\OpenGraph\DTO\ArticleDto
	- App\FrontModule\Components\OpenGraph\DTO\CalendarDto

parameters:
	config:
		openGraph:
			siteName: 'Omega'
			type: 'website'
			imageSize: 'lg'
			imageHeight: 630
			imageWidth: 1200
