parameters:

	esBaseName: %config.projectName%_%stageName%

#	database:
#		host: 127.0.0.1:3307
#		database: 1691_superadmin
#		user: 1691_superadmin
#		password: ''
#		lazy: true

	config:
		#imageFromStage: 'https://superadmin-stage.www6.superkoderi.cz/'
		translations:
			insertNew: false
			markUsage: false
		emailsEchoDie: false

		cookie:
			enableModal: true

		mutations:
			cs:
#				domain: omega.superkoders.test
				domain: app.omega-2025.orb.local
				cookie:
					enableModal: true

includes:
	#- environment.dev.neon
	#- environment.docker.neon

# to enable open file in phpstorm from tracy bar in docker environment
#tracy:
#   editor: 'phpstorm://open?file=%file&line=%line'
#    editorMapping:
#       /var/www/html: /path_to_your_project/omega-2025

#redis:
#    connection:
#        default:
#            options:
#                parameters:
#                    password: 'heslo_u_programatoru'
#        sessions:
#            options:
#                parameters:
#                    password: 'heslo_u_programatoru'

