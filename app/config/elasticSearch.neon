parameters:

	esBaseName: %config.projectName%_%stageName%

	config:
		elasticSearch:
			enabled: true
			pathToSynonyms: %config.APP_DIR%/../documents/es/synonyms/

			index:
				all:
					name: '%esBaseName%_all'
					enabled: true
				common:
					name: '%esBaseName%_common'
					enabled: true
				product:
					name: '%esBaseName%_product'
					enabled: true
		elastica:
			config:
				host: localhost
				port: 9200
monolog:
	channel:
		elastica:
			handlers:
				#- Monolog\Handler\RotatingFileHandler(%appDir%/../nettelog/Elastica/default.log, 30, Monolog\Level::Info)
				- Monolog\Handler\NullHandler
			processors:
				- Monolog\Processor\MemoryPeakUsageProcessor
elastica:
	debug: %debugMode%
	config: %config.elastica.config%

services:
	elastica.client: Contributte\Elastica\Client(%config.elastica.config%, logger: @monolog.logger.elastica) # turn off default logger
	elasticSearch.clientFactory: App\Model\ElasticSearch\ClientFactory::create(%config.elastica.config.host%, %config.elastica.config.port%)

	- App\Model\ElasticSearch\ConfigurationHelper
	- App\Model\Orm\EsIndex\EsIndexModel(%config.elasticSearch.index%)
	- App\Model\Orm\EsIndex\EsIndexFacade

	- \App\Model\ElasticSearch\IndexModel(%esBaseName%)
	- \App\Model\ElasticSearch\Repository()

	- \App\Model\ElasticSearch\All\Repository
	- \App\Model\ElasticSearch\Common\Repository
	- \App\Model\ElasticSearch\Product\Repository

	- App\Model\ElasticSearch\AliasModel(%config.elasticSearch.index%)

	- App\Model\FulltextSearch\SearchFactory
	- App\Model\FulltextSearch\SuggestProductProviderFactory


## new version
	- App\Model\ElasticSearch\Service

	# index of ALL
	- App\Model\ElasticSearch\All\Facade

	- App\Model\ElasticSearch\All\ConvertorProvider
	- App\Model\ElasticSearch\All\Convertor\TreeData
	- App\Model\ElasticSearch\All\Convertor\BlogData
	- App\Model\ElasticSearch\All\Convertor\BannerData
	- App\Model\ElasticSearch\All\Convertor\BlogTagData
	- App\Model\ElasticSearch\All\Convertor\CalendarData
	- App\Model\ElasticSearch\All\Convertor\CalendarTagData
	- App\Model\ElasticSearch\All\Convertor\AuthorData
	- App\Model\ElasticSearch\All\Convertor\ProductData
	- App\Model\ElasticSearch\All\Convertor\SeoLinkData
	- App\Model\ElasticSearch\All\Convertor\MenuMainData
	- App\Model\ElasticSearch\All\Convertor\DiscountData
	- App\Model\ElasticSearch\All\Convertor\TagData
	- App\Model\ElasticSearch\All\Convertor\PromotionData
	- App\Model\ElasticSearch\All\Convertor\GiftData
	- App\Model\ElasticSearch\All\Convertor\BrandData


	# index of COMMON
	- App\Model\ElasticSearch\Common\Facade
	- App\Model\ElasticSearch\Common\ResultReader
	- App\Model\ElasticSearch\Common\ConvertorProvider

	- App\Model\ElasticSearch\Common\Convertor\TreeData
	- App\Model\ElasticSearch\Common\Convertor\BlogData
	- App\Model\ElasticSearch\Common\Convertor\CommonPostTypeData
	- App\Model\ElasticSearch\Common\Convertor\CalendarData
	- App\Model\ElasticSearch\Common\AggregationParser\AlphabetParser


	#index of PRODUCT
	- App\Model\ElasticSearch\Product\ConvertorProvider
	- App\Model\ElasticSearch\Product\Facade
	- App\Model\ElasticSearch\Product\ResultReader
	- App\Model\ElasticSearch\Product\MultiResultReader

	- App\Model\ElasticSearch\Product\Convertor\BaseData
	- App\Model\ElasticSearch\Product\Convertor\CategoryData
	- App\Model\ElasticSearch\Product\Convertor\ParameterData
	- App\Model\ElasticSearch\Product\Convertor\StoreData
	- App\Model\ElasticSearch\Product\Convertor\PriceData
	- App\Model\ElasticSearch\Product\Convertor\DateTimeData
	- App\Model\ElasticSearch\Product\Convertor\DiscountData
	- App\Model\ElasticSearch\Product\Convertor\AvailabilityData
	- App\Model\ElasticSearch\Product\Convertor\ScoreData
	- App\Model\ElasticSearch\Product\Convertor\SalesData
	- App\Model\ElasticSearch\Product\Convertor\SimilarBuyData
	- App\Model\ElasticSearch\Product\Convertor\CustomFeedData
	- App\Model\ElasticSearch\Product\Convertor\CustomFeedDataFlag
	- App\Model\ElasticSearch\Product\Convertor\CustomFeedAvailabilityData
	- App\Model\ElasticSearch\Product\Convertor\FulltextData
	- App\Model\ElasticSearch\Product\Convertor\FlagData
	- App\Model\ElasticSearch\Product\Convertor\ClassData

	- App\Model\ElasticSearch\Product\AggregationParser\MenuMeta\TopProductParser

extensions:
	elastica: Contributte\Elastica\DI\ElasticaExtension

