monolog:
	channel:
		default: # default channel is required
			handlers:
				- Monolog\Handler\RotatingFileHandler(%appDir%/../nettelog/syslog.log, 30, Monolog\Level::Warning)
				- Monolog\Handler\PsrHandler(Tracy\Bridges\Psr\TracyToPsrLoggerAdapter())
			processors:
				- Monolog\Processor\MemoryPeakUsageProcessor()

		formDataSaver:
			handlers:
				- App\Model\Monolog\Handler\DbalHandler
			processors:
				- Monolog\Processor\WebProcessor
				- App\Model\Monolog\Processor\FormDataProcessor(::realpath(%appDir%/../documents/fileLog))

		elasticReplace:
			handlers:
				- Monolog\Handler\RotatingFileHandler(%appDir%/../nettelog/elastic/replace.log, 15)
			processors:
				- Monolog\Processor\MemoryPeakUsageProcessor
	hook:
		fromTracy: false # enabled by default, log through Tracy into Monolog
		toTracy: false # enabled by default, log through Monolog into Tracy
	manager:
		enabled: true # disabled by default
