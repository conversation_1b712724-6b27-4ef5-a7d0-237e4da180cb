<?php

declare(strict_types=1);

namespace App\Model\Google;

use App\Model\Link\LinkFactory;
use App\Model\Orm\Mutation\Mutation;
use App\Model\PagesFactory;
use League\OAuth2\Client\Provider\Google;
use Nette\Application\LinkGenerator;
use function ltrim;

final class GoogleProviderFactory
{

	public function __construct(
		private readonly string|null $clientId,
		private readonly string|null $clientSecret,
		private readonly LinkGenerator $linkGenerator,
		private readonly LinkFactory $linkFactory,
		private readonly PagesFactory $pagesFactory,
	) {}

	public function create(Mutation $mutation): Google
	{
		$pages = $this->pagesFactory->create($mutation);
		[$destination, $params] = $this->linkFactory->linkInPresenter($pages->userLogin, ['do' => 'googleLogin-response']);
		$destination = ltrim($destination, ':');

		return new Google([
			'clientId' => $this->clientId,
			'clientSecret' => $this->clientSecret,
			'redirectUri' => $this->linkGenerator->link($destination, $params),
		]);
	}

}
