<?php

declare(strict_types=1);

namespace App\Model\Router;

use App\Model\Orm\ProductLocalization\ProductLocalization;

final class FilterVariantId
{

	public function in(array $params): array
	{
		if (isset($params['object'])) {

			$object = $params['object'];

			if ($object instanceof ProductLocalization) {

				$defaultVariant = $object->getFirstActiveVariantByMutation($params['mutation']);

				if ($defaultVariant === null) {
					$params['presenter'] = false;
				} else {
					if (!isset($params['v'])) {
						$params['v'] = $defaultVariant->id;
					} else {
						$params['v'] = (int) $params['v'];
						$selectedVariant = $object->getActiveVariantByMutation($params['mutation'], $params['v']);

						if ($selectedVariant === null) {
							// variant doesn't exist -> redirect to primary variant
							$params['v'] = $defaultVariant->id;
						}
					}
				}
			}
		}

		return $params;
	}

	public function out(array $params): array
	{
		if (isset($params['object'])) {
			$object = $params['object'];
			if ($object instanceof ProductLocalization) {
				// if $params v is the same as firstActiveVariant id unset it
				if (isset($params['v'])) {
					$firstActiveVariant = $object->product->firstVariant;
					if ($firstActiveVariant !== null && $params['v'] === $firstActiveVariant->id) {
						unset($params['v']);
					}
				}
			}
		}

		return $params;
	}

}
