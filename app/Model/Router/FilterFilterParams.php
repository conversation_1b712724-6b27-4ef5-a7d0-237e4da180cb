<?php declare(strict_types = 1);

namespace App\Model\Router;

use App\Model\BucketFilter\ElasticItem\LastMinute;
use App\Model\BucketFilter\SetupCreator\FilterFlagsConfig;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Parameter\ParameterRepository;
use App\Model\Orm\ParameterValue\ParameterValueRepository;
use App\Model\Orm\RoutableEntity;
use App\PostType\Page\Model\Orm\CatalogTree;
use Nette\Utils\Strings;

final class FilterFilterParams
{

	private const SEPARATOR = '&';

	private ?array $flagsAliasTable = null;

	public function __construct(
		private readonly ParameterRepository $parameterRepository,
		private readonly ParameterValueRepository $parameterValueRepository,
		private readonly FilterFlagsConfig $elasticItemConfig,
	)
	{
	}

	public function in(array $params): array
	{
		$mutation = (isset($params['mutation']) && $params['mutation'] instanceof Mutation) ? $params['mutation'] : null;

		if (isset($params['object']) && $mutation !== null && $params['object'] instanceof CatalogTree) {
			$this->fillPossibleParams();
			// prevod parametru v aliasu

			if ($matchesAll = Strings::matchAll($params['alias'], '/tag-(' . implode('|', array_keys(FilterFlagsConfig::URL_PLACEHOLDERS)) . ')/')) {
				foreach ($matchesAll as $matches) {
					$params['alias'] = str_replace([$matches[0]], [''], $params['alias']);

					//if (isset(FilterFlagsConfig::URL_PLACEHOLDERS[$matches[1]])) {
						$params[FilterFlagsConfig::URL_PLACEHOLDERS[$matches[1]] . '_1'] = 1;
					//}
				}
			}

			if (preg_match('/(f_(.*)_f)/', $params['alias'], $matches)) {
				$params['alias'] = str_replace('/' . $matches[1], '', $params['alias']);
				$matches[2] = urldecode($matches[2]);

				$tmpParams = explode('_', $matches[2]);

				foreach ($tmpParams as $tmpParam) {
					if (preg_match('/^(.*)\.(.*)$/U', $tmpParam, $selectMatches)) {

						// jedna se o SELECT/MULTISELECT

						$selectValuesTmp = explode(self::SEPARATOR, $selectMatches[2]);
						foreach ($selectValuesTmp as $selectValue) {
							$params[$selectMatches[1] . '_' . $selectValue] = 1;
						}
					} else {
						$params[$tmpParam] = 1;
						// jedna se o FLAG
					}
				}
			}

			$params['alias'] = str_replace([',', '/', self::SEPARATOR], ['', '', ''], $params['alias']);

			foreach ($params as $name => $value) {
				if (in_array($name, ['alias', 'urlPrefix'], true)) {
					continue;
				}

				if (preg_match('/r_([a-zA-Z0-9\-]+)_(min|max)/', $name, $matches)) {
					unset($params[$name]);
					$params['filter']['ranges'][$matches[1]][$matches[2]] = $value;
				} elseif (preg_match('/([a-zA-Z0-9\-]+)_([a-zA-Z0-9\-]+)/', $name, $matches)) {
					// pravdepodobne se jedna o multiselect/select
					$parameterAlias = $matches[1];
					if (($parameterAliasItem = $this->checkParameterAliasTable($name)) !== null) {
						unset($params[$name]);
						if (!isset($params['filter']['dials'][$parameterAlias])) {
							$params['filter']['dials'][$parameterAlias] = [];
						}

						$params['filter']['dials'][$parameterAlias][/*$this->parameterAliasTable[$name]*/$parameterAliasItem] = $parameterAliasItem; //$this->parameterAliasTable[$name];
					} elseif (isset($this->flagsAliasTable[$name])) {
						unset($params[$name]);
						if (!isset($params['filter']['flags'][$parameterAlias])) {
							$params['filter']['flags'][$parameterAlias] = [];
						}

						$params['filter']['flags'][$parameterAlias][$this->flagsAliasTable[$name]] = $this->flagsAliasTable[$name];
					}
				}
			}

			if (isset($params['value-search'])) {
				$searchValues = [];
				foreach ($params['value-search'] as $parameterUid => $value) {
					if (strlen(trim($value)) > 0) {
						$searchValues[$parameterUid] = trim($value);
					}
				}

				$params['filter']['searchValue'] = $searchValues;
				unset($params['value-search']);
			}
		}

		return $params;
	}

	public function out(array $params): array
	{
		$aliasSufix = $aliasFlagSuffix = [];

		if (isset($params['filter']) && isset($params['object']) && $params['object'] instanceof RoutableEntity) {

			$object = $params['object'];
			if (isset($params['filterSubmit'])) {
				// filterSubmit is name of submit action on filter
				// if is present this wil enforce redirect
				unset($params['filterSubmit']);
			}

			if (isset($params['filter']['ranges'])) {
				foreach ($params['filter']['ranges'] as $name => $values) {
					if (isset($values['max']) && $values['max']) {
						$params['r_' . $name . '_max'] = $values['max'];
					}

					if (isset($values['min']) && $values['min']) {
						$params['r_' . $name . '_min'] = $values['min'];
					}
				}
			}

			if (isset($params['filter']['dials'])) {
				foreach ($params['filter']['dials'] as $name => $valuesIds) {
					$parametersOptions = $this->parameterValueRepository->findRowsByIds($valuesIds);
					if (method_exists($object, 'isImportantParameter') && $object->isImportantParameter($name)) {
						$aliases = [];
						foreach ($parametersOptions as $option) {
							$aliases[] = $option->internalAlias;
						}

						if (count($aliases) > 0) {
							$aliasSufix[] = $name . '.' . implode(self::SEPARATOR, $aliases);
						}
					} else {
						foreach ($parametersOptions as $option) {
							$params[$option->parameterUid . '_' . $option->internalAlias] = 1;
						}
					}
				}
			}

			// if flags are indexed filters
			if (isset($params['filter']['flags'])) {
				foreach ($params['filter']['flags'] as $name => $valuesIds) {
					if (in_array($name, FilterFlagsConfig::URL_PLACEHOLDERS)) {
						$aliasFlagSuffix[] = 'tag-' . array_search($name, FilterFlagsConfig::URL_PLACEHOLDERS);// . ',';
					}
				}
			}

			unset($params['filter']['dials']);
			unset($params['filter']['ranges']);
			unset($params['filter']['flags']);
		}

		ksort($params);

		$hasFlag = false;
		if ($aliasFlagSuffix !== []) {
			$params['alias'] .= (!str_contains($params['alias'], '/') ? '/' : '') . implode(self::SEPARATOR, $aliasFlagSuffix);
			$hasFlag = true;
		}

		if ($aliasSufix !== []) {
			$params['alias'] .= (!str_contains($params['alias'], '/') ? '/' : '') . ($hasFlag ? '/' : '') . 'f_' . implode('_', $aliasSufix) . '_f';
		}

		return $params;
	}

	private function fillPossibleParams(): void
	{
		/*if (!$this->parameterAliasTable) {
			$this->parameterAliasTable = [];

			/*$possibleDialParameters = $this->parameterRepository->findBy(['isInFilter' => 1])->orderBy('sort');
			foreach ($possibleDialParameters as $parameter) {
				$options = $this->parameterValueRepository->findRowsByParameter(parameter: $parameter, asPairs: true);
				foreach ($options as $optionId => $optionInternalAlias) {
					//$this->parameterAliasTable[$parameter->uid . '_' . $option->internalAlias] = $option->id;
					$this->parameterAliasTable[$parameter->uid . '_' . $optionInternalAlias] = $optionId;
				}
			}
		}*/

		if ($this->flagsAliasTable === null) {
			$this->flagsAliasTable = [];
			foreach ($this->elasticItemConfig->getProductFlags() as $flag => $callback) {
				$this->flagsAliasTable[$flag . '_1'] = 1;
			}

			$this->flagsAliasTable[LastMinute::CLASS_EVENTS . '_1'] = 1;
		}
	}

	private function checkParameterAliasTable(string $key): ?int
	{
		[$parameterUid, $parameterValueAlias] = explode('_', $key);
		$p = $this->parameterRepository->getBy(['uid' => $parameterUid]);
		$pv = $this->parameterValueRepository->getBy(['parameter' => $p, 'internalAlias' => $parameterValueAlias]);
		return $pv?->getPersistedId();
	}

}
