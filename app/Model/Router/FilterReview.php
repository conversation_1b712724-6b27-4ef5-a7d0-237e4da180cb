<?php declare(strict_types = 1);

namespace App\Model\Router;

final class FilterReview
{

	public function __construct(
		private readonly FilterCommonParameters $filterCommonParameters,
		private readonly FilterAlias $filterAlias,
		private readonly FilterLang $filterLang,
	)
	{
	}

	public function in(array $params): array
	{
		$params = $this->filterCommonParameters->in($params);
		$params = $this->filterLang->in($params);

		if (!isset($params['mutation'])) {
			$params['presenter'] = false;
			return $params;
		}

		$params = $this->filterAlias->in($params);
		if ($params['presenter'] === false) {
			return $params;
		}

		$params['presenter'] = 'ProductReview:Front:ProductReview';
		$params['action'] = 'review';
		return $params;
	}

	public function out(array $params): array
	{
		$params = $this->filterCommonParameters->out($params);
		unset($params['sharedLibraryUid']);
		return $params;
	}

}
