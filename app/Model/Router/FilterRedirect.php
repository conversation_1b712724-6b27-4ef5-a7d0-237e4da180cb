<?php declare(strict_types = 1);

namespace App\Model\Router;

use App\Model\Orm\Redirect\RedirectRepository;
use Nette\Http\Request;

final class FilterRedirect
{

	public function __construct(
		private readonly string $adminAlias,
		private readonly Request $request,
		private readonly RedirectRepository $redirectRepository,
		private readonly FilterRedirectOrigin $filterRedirectOrigin,
		private readonly FilterLang $filterLang,
	)
	{
	}

	public function in(array $params): array
	{
		$params = $this->filterLang->in($params);

		if (isset($params['alias']) && str_starts_with($params['alias'], $this->adminAlias)) {
			$params['presenter'] = false;
			$params['action'] = 'default';
			return $params;
		}
		$redirect = $this->redirectRepository->getBy([
			'oldUrl' => [
				$this->request->getUrl()->relativeUrl,
				$this->request->getUrl()->absoluteUrl,
			],
		]);

		if ($redirect !== null) {
			$params['presenter'] = 'Front:Redirect';
			$params['action'] = 'default';
			$params['redirect'] = $redirect;

			return $params;
		}

		if ($redirectOrigin = $this->filterRedirectOrigin->in($params)) {
			return $redirectOrigin;
		}

		$params['presenter'] = false;

		return $params;
	}

}
