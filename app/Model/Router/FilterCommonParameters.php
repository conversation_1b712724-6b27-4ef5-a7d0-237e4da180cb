<?php

declare(strict_types=1);

namespace App\Model\Router;

final class FilterCommonParameters
{

	public function in(array $params): array
	{
		if (!isset($params['alias'])) {
			$params['alias'] = '';
		}
		return $params;
	}

	public function out(array $params): array
	{
		$paramsToUnset = ['idref', 'action', 'presenter', 'object', 'absolute', 'mutation', 'host'];
		foreach ($paramsToUnset as $paramName) {
			if (isset($params[$paramName])) {
				unset($params[$paramName]);
			}
		}

		if ( ((string) ($params['urlPrefix'] ?? '')) === '') {
			unset($params['urlPrefix']);
		}
		if ( ((string) ($params['alias'] ?? '')) === '') {
			unset($params['alias']);
		}

		if (isset($params['page']) && $params['page'] === 1) {
			unset($params['page']);
		}

		unset($params['sharedLibraryUid']);

		return $params;
	}

}
