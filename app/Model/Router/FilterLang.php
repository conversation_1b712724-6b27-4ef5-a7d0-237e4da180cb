<?php

declare(strict_types=1);

namespace App\Model\Router;

use App\Model\Orm\Mutation\MutationModel;
use App\Model\Orm\Orm;
use Nette\Http\IRequest;
use App\Model\Mutation\MutationHolder;

final class FilterLang
{

	public function __construct(
		private readonly IRequest $httpRequest,
		private readonly Orm $orm,
		private readonly MutationModel $mutationModel,
		private readonly MutationHolder $mutationHolder
	)
	{
	}


	public function in(array $params): array
	{
		$host = $this->httpRequest->getUrl()->getHost();
		$urlPrefix = (isset($params['urlPrefix'])) ? $params['urlPrefix'] : '';
		$hotsWithoutWWW = str_replace('www.', '', $host);
		$mutation = $this->mutationModel->findForRouter($hotsWithoutWWW, $urlPrefix);

		if ($mutation) {
			$params['mutation'] = $mutation;
			$this->orm->setMutation($mutation);
		} else {

			// fallback to prevent e500
			$defaultMutation = $this->orm->mutation->getDefault();

			$params['mutation'] = $defaultMutation;
			$this->orm->setMutation($defaultMutation);
			trigger_error('Mutation not found - try fix local neon config mutation (domain/prefix)?', E_USER_NOTICE);
		}

		return $params;
	}


	public function out(array $params): array
	{
		if (isset($params['mutation'])) {
			$mutation = $params['mutation'];
		} else {
			$mutation = $this->mutationHolder->getMutation();
		}

		if ($mutation->urlPrefix !== false && !isset($params['urlPrefix'])) {
			$params['urlPrefix'] = $mutation->urlPrefix;
		}

		unset($params['host']);
		return $params;
	}

}
