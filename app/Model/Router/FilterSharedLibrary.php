<?php declare(strict_types = 1);

namespace App\Model\Router;

use App\Model\Orm\MyLibrary\MyLibraryRepository;

final class FilterSharedLibrary
{

	public function __construct(
		private readonly FilterCommonParameters $filterCommonParameters,
		private readonly FilterAlias $filterAlias,
		private readonly FilterLang $filterLang,
		private readonly MyLibraryRepository $myLibraryRepository,
	)
	{
	}

	public function in(array $params): array
	{
		$params = $this->filterCommonParameters->in($params);
		$params = $this->filterLang->in($params);

		if (!isset($params['mutation'])) {
			$params['presenter'] = false;
			return $params;
		}

		$params = $this->filterAlias->in($params);
		if ($params['presenter'] === false) {
			return $params;
		}
		$sharedLibrary = $this->myLibraryRepository->getBy([
			'uid' => $params['sharedLibraryUid'],
		]);
		if ($sharedLibrary === null) {
			$params['presenter'] = false;
			return $params;
		}

		return $params;
	}


	public function out(array $params): array
	{
		$params = $this->filterCommonParameters->out($params);

		return $params;
	}

}
