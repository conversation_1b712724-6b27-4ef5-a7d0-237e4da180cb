<?php declare(strict_types = 1);

namespace App\Model\Router;

use App\Model\Orm\Product\ProductRepository;
use App\Model\Orm\ProductRedirect\ProductRedirect;
use App\Model\Orm\ProductRedirect\ProductRedirectRepository;
use App\PostType\Page\Model\Orm\TreeRepository;
use Nette\Utils\Strings;
use Nette\Utils\Validators;

final class FilterRedirectOrigin
{

	public const REGEX_PRODUCT = '/.+\-([0-9]+)\.html/';

	public const REGEX_CATEGORY = '/.+\-([0-9]+)/';

	public function __construct(
		private readonly ProductRepository $productRepository,
		private readonly ProductRedirectRepository $productRedirectRepository,
		private readonly TreeRepository $treeRepository
	)
	{
	}

	final public function in(array $params): array|null
	{
		if (!isset($params['alias'])) {
			return null;
		}

		if ($this->isMatchTag($params)) {
			return null;
		}

		if ($productDetailRedirect = $this->resolveProductDetail($params)) {
			return $productDetailRedirect;
		}

		if ($categoryRedirect = $this->resolveCategory($params)) {
			return $categoryRedirect;
		}

		return null;
	}

	private function resolveProductDetail(array $params): array
	{
		$productExtId = $this->findExtId($params['alias'], self::REGEX_PRODUCT);

		if ($productExtId === null) {
			return [];
		}

		/** @var ProductRedirect|null $redirect */
		$redirect = $this->productRedirectRepository->getBy(['erpId' => $productExtId]);

		if ($redirect !== null) {
			$product = $redirect->product;
		} else {
			if ( ! $product = $this->productRepository->getByExtId($productExtId)) {
				return [];
			}
		}

		$productLocalization = $product->getLocalization($params['mutation']);

		return [
			'mutation' => $params['mutation'],
			'presenter' => 'Front:Product',
			'object' => $productLocalization,
			'alias' => $productLocalization->alias->alias,
		];
	}

	final public function isMatchTag(array $params): bool
	{
		return str_starts_with($params['alias'], 'stitky/');
	}

	final public function resolveCategory(array $params): array
	{
		$cateogryExtId = $this->findExtId($params['alias'], self::REGEX_CATEGORY);

		if ($cateogryExtId === null) {
			return [];
		}

		if (!$categoryLocalization = $this->treeRepository->getBy(['extId' => $cateogryExtId])) {
			return [];
		}

		if (!isset($categoryLocalization->alias->alias)) {
			return [];
		}

		return [
			'mutation' => $params['mutation'],
			'presenter' => 'Front:Catalog',
			'action' => 'detail',
			'object' => $categoryLocalization,
			'alias' => $categoryLocalization->alias->alias,
		];
	}


	final public function findExtId(string $alias, string $regex, int $resultPosition = 1): int|null
	{
		$idParts = Strings::match($alias, $regex);

		if (!$extId = $idParts[$resultPosition] ?? null) {
			return null;
		}

		if (!Validators::isNumeric($extId)) {
			return null;
		}

		return (int) $extId;
	}

}
