<?php declare(strict_types = 1);

namespace App\Model;

/**
 * DO NOT USE in commands, USE LockableTrait instead.
 */
final class FileLock
{

	public const SYNC = 'sync';

	private string $filePath;

	public function __construct(string $directory, string $file = 'lock')
	{
		$this->filePath = $directory . '/' . $file;
	}


	/**
	 * @param int $timeLimit - locked time in seconds
	 */
	public function claim(int $timeLimit = 3600): bool
	{
		if (!$this->isLocked()) {
			$this->release();
			file_put_contents($this->filePath, $timeLimit);
			return true;
		}

		return false;
	}


	public function release(): void
	{
		if (file_exists($this->filePath)) {
			unlink($this->filePath);
		}
	}


	public function isLocked(): bool
	{
		return file_exists($this->filePath) && ((time() - (int) filectime($this->filePath)) < (int) file_get_contents($this->filePath));
	}

}
