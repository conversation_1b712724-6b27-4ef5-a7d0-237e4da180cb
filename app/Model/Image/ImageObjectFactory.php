<?php declare(strict_types = 1);

namespace App\Model\Image;

use App\Infrastructure\Latte\Functions;
use App\Model\CacheFactory;
use App\Model\CacheStorageService;
use App\Model\Image\Setup\ImageSizeConfiguration;
use App\Model\Image\Setup\ImageSizes;
use App\Model\Image\Storage\BasicStorage;
use Nette\Utils\UnknownImageFileException;

final class ImageObjectFactory
{

	public function __construct(
		private readonly BasicStorage $storage,
		private readonly Resizer $resizer,
		private readonly ImageSizes $setup,
		private readonly string $wwwDir,
		private readonly CacheStorageService $cacheStorageService,
		private readonly string $noImageFile = '',
	)
	{
	}



	public function getByName(?string $fileNameWithExtension, string $sizeName, ?int $timestamp = null): ImageObject
	{
		$generator = function() use ($fileNameWithExtension, $sizeName, $timestamp) {
			$fileInfo = pathinfo($fileNameWithExtension);

			$extension = '';
			if (isset($fileInfo['extension'])) {
				$extension = $fileInfo['extension'];
			}

			$fileName = $fileInfo['filename'];

			$extension = strtolower($extension);
			$setup = $this->setup->getSetupByType($sizeName);

			$imageSrc = $this->storage->getImageSrc($setup, $fileName, $extension, $timestamp);
			$imagePath = $this->storage->getPathToImage($setup, $fileName, $extension);

			return new ImageObject(ext: $extension, src: $imageSrc, path: $imagePath, width: $setup->width, height: $setup->height);
		};

		return $this->cacheStorageService->getStoredData('images', Functions::cacheKey($fileNameWithExtension, $sizeName, $timestamp), $generator);
	}

	public function get(string $fileName, string $extension, string $sizeName, ?string $forcedExtension): ImageObject
	{
		$setup = $this->setup->getSetupByType($sizeName);
		return $this->getFromStorage($setup, $fileName, $extension, $forcedExtension);
	}

	public function getOriginal(string $fileNameWithExtension, string $extension): string
	{
		return $this->storage->getOriginalImageSrc($fileNameWithExtension, $extension);
	}

	private function getFromStorage(Setup\ImageSizeConfiguration $setup, string $fileName, string $extension, ?string $forceExtension = null): ImageObject
	{
		if ($this->storage->exist($setup, $fileName, $forceExtension ?? $extension)) {
			$imageSrc = $this->storage->getImageSrc($setup, $fileName, $extension);
			$imagePath = $this->storage->getPathToImage($setup, $fileName, $forceExtension ?? $extension);

			return new ImageObject(ext: $forceExtension ?? $extension, src: $imageSrc, path: $imagePath, width: $setup->width, height: $setup->height);

		} else {
			if ($this->storage->existOriginal($fileName, $extension)) {
				$this->resizer->resample($fileName, $extension, $setup, $forceExtension);
				$imageSrc = $this->storage->getImageSrc($setup, $fileName, $extension);
				$imagePath = $this->storage->getPathToImage($setup, $fileName, $forceExtension ?? $extension);

				return new ImageObject(ext: $forceExtension ?? $extension, src: $imageSrc, path: $imagePath, width: $setup->width, height: $setup->height);
			}
		}

		if ($this->noImageFile === '') {
			throw new MissingImageException(sprintf('Image %s.%s not found.', $fileName, $extension));
		} else {
			throw new UnknownImageFileException(sprintf('Image %s.%s not found.', $fileName, $extension));
		}
	}


	public function getNoImage(string $sizeName): ImageObject
	{
		return  $this->getNoImageBySetup(
			$this->setup->getSetupByType($sizeName)
		);
	}

	private function getNoImageBySetup(ImageSizeConfiguration $setup): ImageObject
	{
		$extension = '';
		$pathInfo = pathinfo($this->noImageFile);
		if (isset($pathInfo['extension'])) {
			$extension = $pathInfo['extension'];
		}
		return new ImageObject(ext: $extension, src: $this->noImageFile, path: $this->wwwDir . $this->noImageFile, width: $setup->width, height: $setup->height);
	}

}
