<?php

namespace App\Model\Image;

class BetterImageTypeDetector
{
	public const OTHER_IMAGE_EXTENSIONS = [
		//'image/avif' => 'avif', //first has priority
		'image/webp' => 'webp',
	];

	public function detect(): ?string
	{
		if (isset($_SERVER['HTTP_ACCEPT'])) {
			foreach (self::OTHER_IMAGE_EXTENSIONS as $mimeType => $otherImageExtension) {
				if(str_contains($_SERVER['HTTP_ACCEPT'], $mimeType)) {
					return strtolower($otherImageExtension);
				}
			}
		}

		return null;
	}
}
