<?php declare(strict_types = 1);

namespace App\Model\Image;

class ImageHelper
{

	public static function getImageTypeByExtension(string $ext): ?int
	{
		$extensions = [
			'jpg' => IMAGETYPE_JPEG,
			'jpeg' => IMAGETYPE_JPEG,
			'png' => IMAGETYPE_PNG,
			'gif' => IMAGETYPE_GIF,
			'webp' => IMAGETYPE_WEBP,
			'bmp' => IMAGETYPE_BMP,
			'avif' => IMAGETYPE_AVIF,
		];

		return $extensions[$ext] ?? null;
	}
}
