<?php declare(strict_types = 1);

namespace App\Model\Image\Setup;

class ImageSizeConfiguration
{

	public function __construct(
		public readonly int $width,
		public readonly int $height,
		public readonly bool $keepRatio,
		public readonly bool $fill,
		public readonly bool $square,
		public readonly int $quality,
		public readonly ?string $watermark,
		public readonly ?string $type = null,
	)
	{
	}


	public static function fromArray(string $type, array $setup): self
	{
		$width = $setup['width'];
		$height = $setup['height'];
		$keepRatio = isset($setup['keepRatio']) && (bool) $setup['keepRatio'];
		$fill = isset($setup['fill']) && (bool) $setup['fill'];
		$square = isset($setup['square']) && (bool) $setup['square'];
		$watermark = (isset($setup['watermark'])) ? (string) $setup['watermark'] : '';
		$quality = (isset($setup['quality'])) ? (int) $setup['quality'] : 100;

		return new self($width, $height, $keepRatio, $fill, $square, $quality, $watermark, $type);
	}

}
