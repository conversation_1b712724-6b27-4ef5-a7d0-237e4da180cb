<?php declare(strict_types = 1);

namespace App\Model\Image;

use Nette\Utils\Image;
use Nette\Utils\ImageColor;

class Rotator
{

	public const NORMAL_ORIENTATION = 1;

	public function normalizeRotation(string $pathToFile, string $extension): void
	{
		$exifHeaders = @exif_read_data($pathToFile);

		if ($exifHeaders !== false
			&& isset($exifHeaders['MimeType'])
			&& isset($exifHeaders['Orientation'])
			&& $exifHeaders['Orientation'] !== self::NORMAL_ORIENTATION) {
			$orientation = $exifHeaders['Orientation'];
			$modify = false;

			$imageForRotation = Image::fromFile($pathToFile);
			if (($rotation = $this->getImageRotation($orientation)) !== null) {
				$imageForRotation->rotate($rotation, ImageColor::rgb(0,0,0));
				$modify = true;
			}

			if (($flipMode = $this->getImageFlip($orientation)) !== null) {
				$imageForRotation->flip($flipMode);
				$modify = true;
			}

			if ($modify) {
				$imageForRotation->save($pathToFile, null, Image::detectTypeFromFile($pathToFile));
			}
		}
	}


	public function getImageFlip(int $orientation): ?int
	{
		return match ($orientation) {
			2 => IMG_FLIP_HORIZONTAL,
			4, 5, 7 => IMG_FLIP_VERTICAL,
			default => null,
		};
	}


	public function getImageRotation(int $orientation): ?int
	{
		return match ($orientation) {
			3, 4  => 180,
			6, 7  => 270,
			5, 8 => 90,
			default => null,
		};
	}


	public function rotateToLeft(ImageObject $imageObject): void
	{
		$imageForRotation = $imageObject->getImage();
		$rotation = -90;
		$imageForRotation->rotate($rotation, ImageColor::rgb(0,0,0));
	}

	public function rotateToRight(ImageObject $imageObject): void
	{
		$imageForRotation = $imageObject->getImage();
		$rotation = 90;
		$imageForRotation->rotate($rotation, ImageColor::rgb(0,0,0));
	}

}
