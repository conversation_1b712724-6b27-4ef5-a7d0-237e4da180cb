<?php declare(strict_types = 1);

namespace App\Model\Image;

use App\Model\Image\Storage\BasicStorage;
use Nette\Utils\Image;

final class ImageObject
{

	private ?string $fileContent = null;

	public function __construct(
		public readonly string $ext,
		public readonly string $src,
		public readonly ?string $path = null,
		public readonly ?int $width = null,
		public readonly ?int $height = null,
		public readonly ?int $ts = null,
	)
	{}


	public function getImage(): Image
	{
		return Image::fromFile($this->path);

	}

	public function getContent(): ?string
	{
		if ($this->fileContent === null) {

			$fileLocation = ($this->path !== null) ? $this->path : $this->src;
			$content = file_get_contents($fileLocation);
			if ($content !== false) {
				$this->fileContent = $content;
			}
		}

		return $this->fileContent;
	}


	public function getContentTypeByExt(): ?string
	{
		$contentType = mime_content_type($this->path);
		if ($contentType !== false) {
			return $contentType;
		}
		return null;
	}

	public function hasRotations(): bool
	{
		return !in_array($this->ext, BasicStorage::FILE_WITHOUT_RESIZE);
	}

}
