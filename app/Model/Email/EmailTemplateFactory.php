<?php declare(strict_types = 1);

namespace App\Model\Email;

use App\Infrastructure\Latte\Filters;
use App\Model\TranslatorDB;
use App\Model\ConfigService;
use App\Model\ImageResizerWrapper;
use Nette\Application\UI\TemplateFactory;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;

final class EmailTemplateFactory
{

	public function __construct(
		private readonly ConfigService $configService,
		private readonly ImageResizerWrapper $imageResizerWrapper,
		private readonly TranslatorDB $translatorDB,
		private readonly TemplateFactory $templateFactory,
	)
	{
	}

	/**
	 * Create template with default macros and translator
	 */
	public function createTemplate(string $templateFile): DefaultTemplate
	{
		$template = $this->templateFactory->createTemplate();
		assert($template instanceof DefaultTemplate);

		$template->setFile($templateFile);
		$template->setTranslator($this->translatorDB);

		$template->addFilter('money', Filters::formatMoney(...));
		$template->addFilter('plural', Filters::plural(...));

		$template->addFunction('getImage', function ($entity, $size) {
			return $this->imageResizerWrapper->getResizedImage($entity, $size);
		});

		$template->addFunction('cfg', function () {
			return call_user_func_array([$this->configService, 'get'], func_get_args());
		});



		return $template;
	}

}
