<?php declare(strict_types = 1);

namespace App\Model\Email;

use App\Exceptions\LogicException;
use App\Model\Image\ImageObjectFactory;
use App\Model\Orm\EmailTemplate\EmailTemplate;
use App\Model\Orm\EmailTemplate\EmailTemplateModel;
use Exception;
use Nette\Utils\Strings;
use App\Model\Mutation\MutationHolder;
use Throwable;

abstract class Base
{

	public function __construct(
		private readonly MutationHolder $mutationHolder,
		private readonly EmailTemplateModel $emailTemplateModel,
		private readonly ImageObjectFactory $imageObjectFactory,
		protected readonly EmailTemplateFactory $templateFactory,
	)
	{
	}

	/**
	 * @throws Throwable
	 * @throws LogicException
	 */
	protected function render(?string $templateTextKey = null, ?string $templateFile = null, array $valuesToLatte = []): string
	{
		$emailTpl = $templateFile ?? 'mail';
		$params = $valuesToLatte;
		$params['text'] = '';

		if (isset($templateTextKey) && Strings::length($templateTextKey) > 0) {
			$templateObject = $this->getTemplate($templateTextKey);
			if (!isset($templateObject)) {
				throw new Exception('Invalid template text key: ' . $templateTextKey);
			}

			$params['text'] = $templateObject->body;
		}
		$params['imageObjectFactory'] = $this->imageObjectFactory;
		$params['mutation'] = $this->mutationHolder->getMutation();

		$template = $this->templateFactory->createTemplate(FE_TEMPLATE_DIR . '/email/' . $emailTpl . '.latte');

		return $template->renderToString(null, $params);
	}

	/**
	 * @throws LogicException
	 */
	public function generateSubject(?string $templateName = null): string
	{
		// automaticka sablona, obsah v RS
		$templateObject = $this->getTemplate($templateName);
		$subject = '';

		if (isset($templateObject)) {
			$subject = $templateObject->subject;
		}

		return $subject ?? '';
	}


	protected function replaceDataArray(string $text, array $data): string
	{
		foreach ($data as $key => $value) {
			if (is_array($value) || is_object($value)) {
				continue;
			}

			$text = str_replace('[DATA-' . $key . ']', (string) $value, $text);
		}

		return $text;
	}

	/**
	 * @throws Exception
	 * @todo: implement
	 */
	public function commonReplaces(string $text): string
	{
		throw new Exception('Not implemented');
		/*if (strpos($text, '[VOUCHER_CODE]') !== false) {
			$voucherId = (int) $this->configService->getParam('shop', 'newsletterDefaultVoucherId');

			if ($voucherId !== 0) {
				$voucherCode = $this->voucherCodeService->addCode($voucherId);
				$text = str_replace('[VOUCHER_CODE]', $voucherCode->code, $text);
			}
		}

		return $text;*/
	}

	/**
	 * @throws LogicException
	 */
	public function getTemplate(?string $templateName = null): ?EmailTemplate
	{
		if ($templateName === null) {
			return null;
		}

		$templateObject = $this->emailTemplateModel->getByKey($templateName, $this->mutationHolder->getMutation());
		if (!isset($templateObject)) {
			throw new LogicException(sprintf('Unknown Email Template "%s" for Mutation ID %d', $templateName, $this->mutationHolder->getMutation()->id));
		}

		return $templateObject;
	}

	/**
	 * @throws LogicException
	 */
	public function getDataAttachment(string $dbTemplate, ?array $attachments = null): array
	{
		$attachments = $attachments ?? [];
		$emailTemplate = $this->getTemplate($dbTemplate);

		if (isset($emailTemplate) && $emailTemplate->files->count() > 0) {
			foreach ($emailTemplate->files as $file) {
				$attachments[] = WWW_DIR . $file->url;
			}
		}

		return $attachments;
	}

}
