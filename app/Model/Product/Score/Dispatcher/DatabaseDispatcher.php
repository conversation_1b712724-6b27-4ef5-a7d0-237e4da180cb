<?php declare(strict_types = 1);

namespace App\Model\Product\Score\Dispatcher;

use App\Exceptions\LimitException;
use App\Model\Orm\Orm;
use App\Model\Product\DispatcherInterface;

final class DatabaseDispatcher implements DispatcherInterface
{

	private int $limit;

	private int $batchSize;

	private string $mutationCode;

	private bool $onlyActiveProducts = true;

	private int $lastRecord = 0;

	private int $count = 0;

	public function __construct(private readonly Orm $orm)
	{
	}

	public function getProducts(): array
	{
		$mutation = null;
		if (isset($this->mutationCode)) {
			$mutation = $this->orm->mutation->getByCode($this->mutationCode);
		}

		$productLocalizations = $this->orm->productLocalization
			->findBy(['id>' => $this->lastRecord])
			->limitBy($this->batchSize);

		if ($mutation !== null) {
			$productLocalizations = $productLocalizations->findBy(['mutation' => $mutation]);
		}

		if ($this->onlyActiveProducts) {
			$productLocalizations->findBy(['public' => $this->onlyActiveProducts]);
		}

		$productLocalizations = $productLocalizations->fetchAll();

		if (empty($productLocalizations)) {
			return [];
		}

		$this->lastRecord = (end($productLocalizations))->id;
		$this->count += count($productLocalizations);

		$this->checkLimit();

		return $productLocalizations;
	}

	private function checkLimit(): void
	{
		if ($this->limit === 0) {
			return;
		}

		if ($this->count <= $this->limit) {
			return;
		}

		throw new LimitException(LimitException::LIMIT_EXCEEDED);
	}

	/**
	 * @param int $limit
	 * @return DatabaseDispatcher
	 */
	public function setLimit(int $limit): DatabaseDispatcher
	{
		$this->limit = $limit;
		return $this;
	}

	/**
	 * @param int $batchSize
	 * @return DatabaseDispatcher
	 */
	public function setBatchSize(int $batchSize): DatabaseDispatcher
	{
		$this->batchSize = $batchSize;
		return $this;
	}

	/**
	 * @param string $mutationCode
	 * @return DatabaseDispatcher
	 */
	public function setMutationCode(string $mutationCode): DatabaseDispatcher
	{
		$this->mutationCode = $mutationCode;
		return $this;
	}

	/**
	 * @param bool $onlyActiveProducts
	 * @return DatabaseDispatcher
	 */
	public function setOnlyActiveProducts(bool $onlyActiveProducts): DatabaseDispatcher
	{
		$this->onlyActiveProducts = $onlyActiveProducts;
		return $this;
	}

}
