<?php declare(strict_types = 1);

namespace App\Model\Product\Score\Directive;

use App\Model\Orm\Product\ProductRepository;

final class DirectiveMarketability extends Directive implements DirectiveInterface
{

	public function __construct(private readonly ProductRepository $productRepository)
	{
	}

	final public function process(): float
	{
		$purchaseCount = $this->productRepository->getPurchaseCount($this->getProductLocalization()->product);

		if ($purchaseCount === 1 || $purchaseCount === 2) {
			return 3.0;
		}

		if ($purchaseCount >= 3 && $purchaseCount <= 5) {
			return 5.0;
		}

		if ($purchaseCount >= 6 && $purchaseCount <= 10) {
			return 8.0;
		}

		if ($purchaseCount >= 11 && $purchaseCount <= 20) {
			return 12.0;
		}

		if ($purchaseCount >= 20) {
			return 17.0;
		}

		return 0.0;
	}

}
