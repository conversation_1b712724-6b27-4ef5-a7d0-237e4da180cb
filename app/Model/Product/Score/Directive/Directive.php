<?php declare(strict_types = 1);

namespace App\Model\Product\Score\Directive;

use App\Exceptions\ScoreException;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\PriceLevel\PriceLevelRepository;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\State\State;
use App\Model\Orm\State\StateRepository;
use Exception;

abstract class Directive implements DirectiveInterface
{

	public function __construct(private readonly PriceLevelRepository $priceLevelRepository, private readonly StateRepository $stateRepository)
	{
	}

	private ProductLocalization $productLocalization;

	public function process(): float
	{
		throw new Exception();
	}

	final protected function getProductLocalization(): ProductLocalization
	{
		return $this->productLocalization;
	}

	final public function setProductLocalization(ProductLocalization $productLocalization): DirectiveInterface
	{
		$this->productLocalization = $productLocalization;

		return $this;
	}

	final protected function getPrice(string $priceLevelType = PriceLevel::TYPE_DEFAULT): float
	{
		$price = $this->getProductLocalization()->product->price($this->getProductLocalization()->mutation, $this->getPriceLevel($priceLevelType), $this->getState());

		return $price->getAmount()->toFloat();
	}

	private function getPriceLevel(string $priceLevelType = PriceLevel::TYPE_DEFAULT): PriceLevel
	{
		if (!$priceLevel = $this->priceLevelRepository->getBy(['type' => $priceLevelType])) {
			throw new ScoreException(ScoreException::PRICE_LEVEL_NOT_FOUND . ' Price level type: ' . $priceLevelType);
		}

		return $priceLevel;
	}

	private function getState(): State
	{
		return $this->stateRepository->getDefault($this->getProductLocalization()->mutation);
	}

}
