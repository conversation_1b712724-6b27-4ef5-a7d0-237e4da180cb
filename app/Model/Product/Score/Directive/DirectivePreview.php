<?php declare(strict_types = 1);

namespace App\Model\Product\Score\Directive;

final class DirectivePreview extends Directive implements DirectiveInterface
{

	final public function process(): float
	{
		if (!isset($this->getProductLocalization()->product->cf->productImagePages)) {
			return 0.0;
		}

		if (!empty($this->getProductLocalization()->product->cf->productImagePages)) {
			return 1.0;
		}

		return 0.0;
	}

}
