<?php declare(strict_types = 1);

namespace App\Model\Product\Score\Directive;

final class DirectiveHeurekaPopularity extends Directive implements DirectiveInterface
{

	final public function process(): float
	{
		$product = $this->getProductLocalization()->product;

		if ($product->erpHeurekaPopularity <= 5) {
			return $product->erpHeurekaPopularity - 1;
		} elseif ($product->erpHeurekaPopularity <= 20) {
			return 6;
		} elseif ($product->erpHeurekaPopularity <= 40) {
			return 8;
		} elseif ($product->erpHeurekaPopularity <= 60) {
			return 10;
		} elseif ($product->erpHeurekaPopularity <= 100) {
			return 12;
		} else {
			return 15;
		}
	}

}
