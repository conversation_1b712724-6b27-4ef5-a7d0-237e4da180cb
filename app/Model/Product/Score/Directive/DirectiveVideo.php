<?php declare(strict_types = 1);

namespace App\Model\Product\Score\Directive;

final class DirectiveVideo extends Directive implements DirectiveInterface
{

	private const VIDEO_URL_PARTS = ['www.youtube'];

	final public function process(): float
	{
		foreach (self::VIDEO_URL_PARTS as $videoPart) {
			if (!str_contains($this->getProductLocalization()->content ?? '', $videoPart)) {
				continue;
			}

			return 1.0;
		}

		return 0.0;
	}

}
