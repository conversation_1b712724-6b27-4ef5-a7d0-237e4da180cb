<?php declare(strict_types = 1);

namespace App\Model\Product\Score\Directive;

use App\Model\Orm\PriceLevel\PriceLevel;

final class DirectiveProfit extends Directive implements DirectiveInterface
{

	final public function process(): float
	{
		if (!$profit = $this->getProfit()) {
			return 0.0;
		}

		if ($profit >= 30) {
			return 12.0;
		}

		if ($profit >= 20) {
			return 9.0;
		}

		if ($profit >= 10) {
			return 6.0;
		}

		if ($profit >= 5) {
			return 2.0;
		}

		return 0.0;
	}

	private function getProfit(): float
	{
		$priceSelling = $this->getPrice();
		$priceBuying = $this->getPrice(PriceLevel::TYPE_PURCHASE);

		if (!$priceSelling) {
			return 0.0;
		}

		return (1 - ($priceBuying / $priceSelling)) * 100;
	}

}
