<?php declare(strict_types = 1);

namespace App\Model\Product\Score\Directive;

use App\Utils\DateTime;

final class DirectiveManualBoost extends Directive implements DirectiveInterface
{

	final public function process(): float
	{
		if (($boostScore = $this->getProductLocalization()->product->boostScore) === null) {
			return 0.0;
		}

		if ($this->getProductLocalization()->product->boostScoreValid === null) {
			return $boostScore;
		}

		if (DateTime::from('now') <= $this->getProductLocalization()->product->boostScoreValid) {
			return $boostScore;
		}

		return 0.0;
	}

}
