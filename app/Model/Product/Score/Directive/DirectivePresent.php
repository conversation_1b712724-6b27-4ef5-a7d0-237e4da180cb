<?php declare(strict_types = 1);

namespace App\Model\Product\Score\Directive;

use Nette\Utils\DateTime;

final class DirectivePresent extends Directive implements DirectiveInterface
{

	final public function process(): float
	{
		$presentDate = $this->getProductLocalization()->product->getGiftDate($this->getProductLocalization()->getMutation());

		if ($presentDate === null) {
			return 0.0;
		}

		if (!$lastPresentDate = $presentDate->diff(DateTime::from('now'))->days) {
			return 0.0;
		}

		if ($lastPresentDate <= 180) {
			return -30.0;
		}

		if ($lastPresentDate <= 360) {
			return -20.0;
		}

		if ($lastPresentDate <= 720) {
			return -5.0;
		}

		return 0.0;
	}

}
