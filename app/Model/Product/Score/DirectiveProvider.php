<?php declare(strict_types = 1);

namespace App\Model\Product\Score;

use App\Model\Product\Score\Directive\DirectiveAction;
use App\Model\Product\Score\Directive\DirectiveAvailable;
use App\Model\Product\Score\Directive\DirectiveDescription;
use App\Model\Product\Score\Directive\DirectiveHeurekaPopularity;
use App\Model\Product\Score\Directive\DirectiveManualBoost;
use App\Model\Product\Score\Directive\DirectiveMarketability;
use App\Model\Product\Score\Directive\DirectivePresent;
use App\Model\Product\Score\Directive\DirectivePreview;
use App\Model\Product\Score\Directive\DirectiveProfit;
use App\Model\Product\Score\Directive\DirectivePublishDate;
use App\Model\Product\Score\Directive\DirectiveVideo;
use App\Model\Product\Score\Directive\DirectiveImage;

final class DirectiveProvider
{

	public function __construct(
		private readonly DirectiveAvailable $directiveAvailable,
		private readonly DirectiveProfit $directiveProfit,
		private readonly DirectivePublishDate $directivePublishDate,
		private readonly DirectiveImage $directiveImage,
		private readonly DirectiveDescription $directiveDescription,
		private readonly DirectiveVideo $directiveVideo,
		private readonly DirectivePreview $directivePreview,
		private readonly DirectiveMarketability $directiveMarketability,
		private readonly DirectiveAction $directiveAction,
		private readonly DirectivePresent $directivePresent,
		private readonly DirectiveManualBoost $directiveManualBoost,
		//private readonly DirectiveErpScore $directiveErpScore,
		private readonly DirectiveHeurekaPopularity $directiveHeurekaPopularity,
	)
	{
	}

	final public function provide(): array
	{
		return [
			$this->directiveAvailable,
			$this->directiveProfit,
			$this->directivePublishDate,
			$this->directiveImage,
			$this->directiveDescription,
			$this->directiveVideo,
			$this->directivePreview,
			$this->directiveMarketability,
			$this->directiveAction,
			$this->directivePresent,
			$this->directiveManualBoost,
			//$this->directiveErpScore,
			$this->directiveHeurekaPopularity,
		];
	}

}
