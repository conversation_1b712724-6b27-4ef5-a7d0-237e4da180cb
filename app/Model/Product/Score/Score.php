<?php declare(strict_types = 1);

namespace App\Model\Product\Score;

use App\Model\ElasticSearch\Product\Facade;
use App\Model\HasLogger;
use App\Model\Orm\Orm;
use App\Model\Orm\OrmCleaner;
use App\Model\Orm\PriceLevel\PriceLevelModel;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Product\DispatcherInterface;
use App\Model\Product\Score\Directive\DirectiveInterface;
use App\Model\TranslatorDB;
use Contributte\Monolog\LoggerManager;
use Psr\Log\LoggerInterface;

final class Score
{

	use HasLogger;

	private ?LoggerInterface $logger = null;

	public function __construct(
		private readonly Orm $orm,
		private readonly DirectiveProvider $directiveProvider,
		private readonly OrmCleaner $ormCleaner,
		private readonly PriceLevelModel $priceLevelModel,
		private readonly TranslatorDB $translatorDB,
		private readonly LoggerManager $loggerManager,
		private readonly Facade $productEsFacade,
	)
	{
		$this->addLogger('scoreCalculator');
	}

	public function calculate(DispatcherInterface $dispatcher): void
	{
		$this->logger?->info(self::class . ' / Starting calculating score');
		while ($productLocalizations = $dispatcher->getProducts()) {
			foreach ($productLocalizations as $productLocalization) {
				assert($productLocalization instanceof ProductLocalization);
				$this->translatorDB->setMutation($productLocalization->getMutation());

				$oldProductScore = $productLocalization->product->score;
				$oldProductLocalizationScore = $productLocalization->score;

				$score = 0;
				/**
				 * @var DirectiveInterface $directive
				 */
				foreach ($this->directiveProvider->provide() as $directive) {
					$score += $directive->setProductLocalization($productLocalization)->process();
				}

				$productLocalization->score = $score;

				$this->orm->persist($productLocalization);

				$this->calculateProductScore($productLocalization);

				if ($oldProductScore !== $productLocalization->product->score || $oldProductLocalizationScore !== $productLocalization->score) {
					$this->productEsFacade->updateAfterProductScoreCalculation($productLocalization->product, $productLocalization->getMutation());
				}
			}

			$this->orm->flush();
			$this->ormCleaner->safeClear();
			$this->priceLevelModel->flushCache();

			$this->logger?->info(self::class . ' / Batch of {count} products completed.', ['count' => count($productLocalizations)]);
		}
		$this->logger?->info(self::class . ' / Ending calculating score.');
	}

	private function calculateProductScore(ProductLocalization $productLocalization): void
	{
		$productScore = 0;

		foreach ($productLocalization->product->productLocalizations as $localization) {
			$productScore += $localization->score;
		}

		if ($productLocalization->product->score === $productScore) {
			return;
		}

		$productLocalization->product->score = $productScore;

		$this->orm->persist($productLocalization->product);
	}

}
