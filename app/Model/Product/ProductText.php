<?php declare(strict_types=1);

namespace App\Model\Product;

use App\Infrastructure\Latte\Filters;
use App\Model\Orm\Product\Product;
use App\Model\PriceInfo;
use App\Model\TranslatorDB;
use Brick\Money\Money;


readonly class ProductText
{
	public function __construct(
		private TranslatorDB $translator,
	)
	{}

	public function getTooltipText(Product $product, PriceInfo $priceInfo): ?string
	{
		$type = $priceInfo->getType();
		$discountMoney = $priceInfo->getDiscountAmount();

		if ($type === null) {
			return null;
		}

		$discount = null;
		if ($priceInfo->getDiscountPercentage() !== null) {
			$discount = $priceInfo->getDiscountPercentage() . '%';
		} elseif ($discountMoney !== null) {
			$discount = Filters::formatMoney($discountMoney);
		}

		$recommendedPrice = $priceInfo->getRecommendedPrice();
		$referencePrice = $priceInfo->getReferencePrice();

		$translate = function (string $key, array $replace = []): string {
			if(!empty($replace)){
				return str_replace(array_keys($replace), array_values($replace), $this->translator->translate($key));
			}
			return $this->translator->translate($key);
		};

		$replace = [
			'%recommended%' => Filters::formatMoney($recommendedPrice),
			'%reference%' => Filters::formatMoney($referencePrice),
			'%discount%' => $discount ?? '',
			'%discountAmount%' => $discountMoney instanceof Money ? Filters::formatMoney($discountMoney) : '',
			'%discountPercentage' => $priceInfo->getDiscountPercentage() ?? $priceInfo->getDiscountPercentageSort() ?? '',
		];

		return $translate('tooltip_product_variant_'.strtolower($type), $replace);
	}
}
