<?php declare(strict_types = 1);

namespace App\Model\Product;

use App\Model\ElasticSearch\Product\Repository;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Product\ProductRepository;
use App\Model\Orm\State\StateRepository;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Page\Model\Orm\TreeRepository;
use Elastica\ResultSet;

final class CategoryMainPositionCalculator
{

	private const LIMIT = 50;

	public function __construct(
		private readonly TreeRepository $treeRepository,
		private readonly Repository $esProductRepository,
		private readonly MutationHolder $mutationHolder,
		private readonly StateRepository $stateRepository,
		private readonly ProductRepository $productRepository,
	)
	{
	}

	final public function calculate(): void
	{
		$productsArray = [];
		$stateCode = $this->stateRepository->getDefault($this->mutationHolder->getMutation())->code;

		foreach ($this->treeRepository->findBy(['public' => true]) as $category) {

			$offset = 0;
			while ($products = $this->getProduct($category, $offset, $stateCode)) {

				if (!$products->getResults()) {
					break;
				}

				foreach ($products->getResults() as $index => $result) {

					if (!isset($result->getSource()['productId'])) {
						continue;
					}

					$productsArray[$result->getSource()['productId']] = $offset + $index;
				}

				$offset += self::LIMIT;
			}

		}

		$this->productRepository->setPositionInMainCategory($productsArray);
	}

	private function getProduct(Tree $category, int $offset, string $stateDefaultCode): ResultSet
	{
		return $this->esProductRepository->findAllByMainCategory(
			$category,
			$this->mutationHolder->getMutation(),
			self::LIMIT,
			$offset,
			['topScore.' . $stateDefaultCode . '.default' => ['order' => 'desc']],
		);
	}

}
