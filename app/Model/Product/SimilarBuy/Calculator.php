<?php declare(strict_types=1);

namespace App\Model\Product\SimilarBuy;

use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductRepository;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;

class Calculator
{
	public function __construct(
		private readonly ProductRepository $productRepository,
	)
	{
	}

	public function calculate(): void
	{
		$products = $this->productRepository
			->findAll()
			->orderBy('similarBuyProductsCalculateAt', ICollection::ASC)
			->limitBy(5000);

		foreach ($products as $product) {
			$this->calculateSimilarForProduct($product);
		}
		$this->productRepository->flush();
	}

	private function calculateSimilarForProduct(Product $product): void
	{
		$product->similarBuyProductsCalculateAt = new DateTimeImmutable();
		$product->similarBuyProducts = (new ArrayHash())->from($this->productRepository->findSimilarBuyIds(
			$product->variants->toCollection()->fetchPairs(null, 'id')
		));
		$this->productRepository->persist($product);
	}
}
