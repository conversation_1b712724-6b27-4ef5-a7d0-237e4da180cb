<?php declare(strict_types = 1);

namespace App\Model\Product\SoldCount;

use App\Model\ElasticSearch\Product\Facade;
use App\Model\HasLogger;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\OrmCleaner;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\ProductLocalization\ProductLocalizationRepository;
use App\Model\Product\DispatcherInterface;
use App\Model\TranslatorDB;
use Contributte\Monolog\LoggerManager;
use Psr\Log\LoggerInterface;

final class ProductLocalizationSoldCountCalculator
{

	use HasLogger;

	private ?LoggerInterface $logger = null;

	public function __construct(
		private readonly Orm $orm,
		private readonly OrmCleaner $ormCleaner,
		private readonly TranslatorDB $translatorDB,
		private readonly LoggerManager $loggerManager,
		private readonly Facade $productEsFacade,
		private readonly ProductLocalizationRepository $productLocalizationRepository,
	)
	{
		$this->addLogger('soldCountCalculator');
	}

	public function calculate(DispatcherInterface $dispatcher, int $daysToPast): void
	{
		$this->logger?->info(self::class . ' / Starting calculating soldCount');

		while ($productLocalizations = $dispatcher->getProducts()) {
			foreach ($productLocalizations as $productLocalization) {
				assert($productLocalization instanceof ProductLocalization);
				$this->translatorDB->setMutation($productLocalization->getMutation());
				$oldProductLocalizationSoldCount = $productLocalization->soldCount;
				$productLocalization->soldCount = $this->productLocalizationRepository->getPurchaseCount($productLocalization, $daysToPast);
				$this->orm->persist($productLocalization);

				$this->calculateProductSoldCount($productLocalization);

				if ($oldProductLocalizationSoldCount !== $productLocalization->soldCount) {
					$this->productEsFacade->updateAfterProductScoreCalculation($productLocalization->product, $productLocalization->getMutation());
				}
			}

			$this->orm->flush();
			$this->ormCleaner->safeClear();

			$this->logger?->info(self::class . ' / Batch of {count} products completed.', ['count' => count($productLocalizations)]);
		}
		$this->logger?->info(self::class . ' / Ending calculating soldCount.');
	}

	private function calculateProductSoldCount(ProductLocalization $productLocalization): void
	{
		$productSoldCount = 0;

		foreach ($productLocalization->product->productLocalizations as $localization) {
			$productSoldCount += $localization->soldCount;
		}

		if ($productLocalization->product->soldCount === $productSoldCount) {
			return;
		}

		$productLocalization->product->soldCount = $productSoldCount;

		$this->orm->persist($productLocalization->product);
	}

}
