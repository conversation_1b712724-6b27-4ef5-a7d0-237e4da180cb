<?php declare(strict_types=1);

namespace App\Model\Product;

use App\Model\Consent\MarketingConsent;
use App\Model\LastVisitedProduct;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductRepository;
use App\Model\Orm\ProductVariant\ProductVariantRepository;
use App\Model\Orm\User\User;
use App\PostType\Page\Model\Orm\Tree;
use Nette\Http\Request;
use Nette\Utils\Json;
use Throwable;

class UserProductTopList
{

	public function __construct(
		private readonly ProductVariantRepository $productVariantRepository,
		private readonly ProductRepository $productRepository,
		private readonly MarketingConsent $marketingConsent,
		private readonly LastVisitedProduct $lastVisitedProduct,
	)
	{
	}

	public function getTopProductIds(?User $userEntity): array
	{
		if (!$this->marketingConsent->isPersonalizationGranted()) {
			return [];
		}

		if ($userEntity !== null && ($productVariantIds = $this->productVariantRepository->findOrderedProductVariantIds($userEntity)) !== []) {
			if (($productIds = $this->productRepository->findBy(['variants->id' => $productVariantIds])->fetchPairs('id', 'id')) !== []) {
				return $productIds;
			}
		}

		return $this->lastVisitedProduct->getProductIds();
	}
}
