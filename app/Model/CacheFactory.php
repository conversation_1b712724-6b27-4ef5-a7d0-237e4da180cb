<?php declare(strict_types = 1);

namespace App\Model;


use Nette\Caching\Cache;
use Nette\Caching\Storage;

final class CacheFactory {

	private array $instances = [];

	public function __construct(
		private readonly Storage $storage
	)
	{
	}

	public function create(string $namespace): Cache
	{
		if (!isset($this->instances[$namespace])) {
			$this->instances[$namespace] = new Cache($this->storage, $namespace);
		}
		return $this->instances[$namespace];
	}
}
