<?php declare(strict_types=1);

namespace App\Model\Sitemap;

use App\Model\FeedGenerator\Individual\Factories\IndividualFactorySetup;
use App\Model\FeedGenerator\Individual\IndividualFeedGenerator;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Sitemap\DataProvider\SitemapRootDataProvider;
use Closure;
use Generator;
use Nette\Utils\FileSystem;

class SitemapGenerator
{

	public const BUCKET_LIMIT = 50000;
	public const string SITEMAP_SUB_FILE_PREFIX = 'sitemap-';

	public function __construct(
		private readonly string $wwwDir,
		private readonly IndividualFactorySetup $individualFactorySetup,
		private readonly DataProviderTable $dataProviderTable,
		private readonly SitemapRootDataProvider $sitemapRootDataProvider,
	)
	{
	}

	public function generate(Mutation $mutation): array
	{
		$xmlGeneratorsList = [];
		foreach ($this->dataProviderTable->getTable() as $fileName => $yieldFunctionProvider) {
			$xmlGeneratorsList += $this->generateXmlByYieldFunction(
				$mutation,
				$yieldFunctionProvider->getYieldFunction($mutation),
				$fileName
			);
		}
		$this->createSitemapRoot($mutation, $xmlGeneratorsList);
		$this->cleanOldFiles(array_keys($xmlGeneratorsList));

		return array_keys($xmlGeneratorsList);
	}

	/**
	 * @phpstan-param Closure(int $limit, int $offset): Generator $getYieldFunction
	 * @return array<\App\Model\FeedGenerator\Individual\Generators\Sitemap\SitemapGenerator>
	 */
	private function generateXmlByYieldFunction(Mutation $mutation, Closure $getYieldFunction, string $fileName): array
	{
		$xmlGeneratorList = [];

		$next = true;
		$counter = 0;
		$offset = 0;
		$individualFeedGenerator = new IndividualFeedGenerator($this->individualFactorySetup->create(), $mutation->langCode);
		while ($next) {
			$fileNameWithCounter = self::SITEMAP_SUB_FILE_PREFIX . $fileName . '-' . $counter . '.xml';
			$fileNameForPart = $this->wwwDir . '/exports/' . $fileNameWithCounter;
			$baseFunction = $getYieldFunction(self::BUCKET_LIMIT, $offset);
			$sitemapPartGenerator = $individualFeedGenerator->generateSitemap($baseFunction, $fileNameForPart);
			$counter++;
			$offset = self::BUCKET_LIMIT + $offset;
			if ($sitemapPartGenerator->getDtosCount() !== self::BUCKET_LIMIT) {
				$next = false;
			}
			if ($sitemapPartGenerator->getDtosCount() !== 0) {
				// clone generator for later
				$xmlGeneratorList[$fileNameWithCounter] = clone $sitemapPartGenerator;
			} else {
				$sitemapPartGenerator->removeTmpFile();
			}
		}
		return $xmlGeneratorList;
	}

	public function createSitemapRoot(Mutation $mutation, array $xmlGeneratorsList): void
	{
		$individualFeedGenerator = new IndividualFeedGenerator($this->individualFactorySetup->create(), $mutation->langCode);
		$fileNameForPart = $this->wwwDir . '/sitemap.xml';
		$sitemapRootGenerator = $individualFeedGenerator->generateRootSitemap(
			$this->sitemapRootDataProvider->getYieldFunction($mutation, array_keys($xmlGeneratorsList))(),
			$fileNameForPart
		);
		foreach ($xmlGeneratorsList as $xmlGenerators) {
			$xmlGenerators->moveTmpOutput();
		}

		$sitemapRootGenerator->moveTmpOutput();
	}

	private function cleanOldFiles(array $createdFileNames): void
	{
		$path = $this->wwwDir . '/exports/';
		$prefix = self::SITEMAP_SUB_FILE_PREFIX;
		$files = glob($path . $prefix . '*');
		if (is_array($files)) {
			$files = array_map('basename', $files);
			foreach ($files as $file) {
				if (!in_array($file, $createdFileNames)) {
					FileSystem::delete($file);
				}
			}
		}
	}

}
