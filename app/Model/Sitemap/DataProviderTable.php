<?php

namespace App\Model\Sitemap;

use App\Model\Sitemap\DataProvider\CatalogProvider;
use App\Model\Sitemap\DataProvider\DamagedProductsProvider;
use App\Model\Sitemap\DataProvider\OutOfStockProductsProvider;
use App\Model\Sitemap\DataProvider\PermanentlyOutOfStockProductsProvider;
use App\Model\Sitemap\DataProvider\ProductImagesProvider;
use App\Model\Sitemap\DataProvider\ProductsProvider;

class DataProviderTable
{

		public function __construct(
			private readonly ProductsProvider $productsProvider,
			private readonly DamagedProductsProvider $damagedProductsProvider,
			private readonly OutOfStockProductsProvider $outOfStockProductsProvider,
			private readonly PermanentlyOutOfStockProductsProvider $permanentlyOutOfStockProductsProvider,
			private readonly ProductImagesProvider $productImagesProvider,
			private readonly CatalogProvider $catalogProvider,
		)
		{
		}

		public function getTable(): array
		{
			return [
				'product' => $this->productsProvider,
				'product-damaged' => $this->damagedProductsProvider,
				'product-out-of-stock' => $this->outOfStockProductsProvider,
				'product-permanently-out-of-stock' => $this->permanentlyOutOfStockProductsProvider,
				'image' => $this->productImagesProvider,
				'catalog' => $this->catalogProvider,
			];
		}

}
