<?php declare(strict_types=1);

namespace App\Model\Sitemap\DataProvider;

use App\Model\ElasticSearch\Product\Repository;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Orm\Mutation\Mutation;
use Elastica\Query\AbstractQuery;
use Generator;

class ElasticProductProvider
{

	public function __construct(
		private readonly EsIndexRepository $esIndexRepository,
		private readonly Repository $esProductRepository,
	)
	{
	}

	protected function getBaseActiveProductYield(Mutation $mutation, AbstractQuery $filter, int $limit, int $offset): Generator
	{
		$index = $this->esIndexRepository->getProductLastActive($mutation);
		if ($index === null) {
			return fn() => yield from [];
		}
		return $this->esProductRepository->getOneBulkFinderClosure($index, $filter, $limit, $offset, onlyIds: false, fieldList: ['url', 'lastModified'])();
	}

}
