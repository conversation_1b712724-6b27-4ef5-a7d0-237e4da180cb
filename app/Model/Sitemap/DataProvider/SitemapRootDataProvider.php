<?php declare(strict_types=1);

namespace App\Model\Sitemap\DataProvider;

use App\Model\Orm\Mutation\Mutation;
use Closure;
use Generator;

class SitemapRootDataProvider
{

	/**
	 * @return Closure(): Generator
	 */
	public function getYieldFunction(Mutation $mutation, array $fileNameList): Closure
	{
		return fn () => $this->getGenerator($fileNameList, $mutation->getBaseUrl());
	}

	private function getGenerator(array $fileNameList, string $baseUrl): Generator
	{
		foreach ($fileNameList as $fileName) {
			yield [
				'url' => $baseUrl . '/exports/' . $fileName,
			];
		}
	}

}
