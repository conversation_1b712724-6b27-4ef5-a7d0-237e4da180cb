<?php declare(strict_types = 1);

namespace App\Model\Sitemap\DataProvider;

use App\Model\Link\LinkFactory;
use App\Model\Orm\Mutation\Mutation;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\TreeRepository;
use Closure;
use Generator;
use Nextras\Dbal\Utils\DateTimeImmutable;

class CatalogProvider implements YieldByMutation
{

	public function __construct(
		private readonly TreeRepository $treeRepository,
		private readonly LinkFactory $linkFactory,
	)
	{
	}

	public function getYieldFunction(Mutation $mutation): Closure
	{
		return fn (int $limit, int $offset) => $this->getCategories($limit, $offset, $mutation);
	}

	private function getCategories(int $limit, int $offset, Mutation $mutation): Generator
	{
		foreach ($this->treeRepository->findBy(['type' => CatalogTree::TYPE_CATALOG])->limitBy($limit, $offset) as $page) {
			yield [
				'url' => $this->linkFactory->linkTranslateToNette($page, ['mutation' => $mutation]),
				'lastModified' => $page->editedTime ?? new DateTimeImmutable(),
			];
		}
	}

//	/** https://gist.github.com/cecilemuller/4688876 @see */
//	private function getCombinations($arrays): array
//	{
//		$results = array(array());
//		foreach ($arrays as $property => $property_values) {
//			$tmp = array();
//			foreach ($results as $result_item) {
//				foreach ($property_values as $property_value) {
//					$tmp[] = array_merge($result_item, array($property => $property_value));
//				}
//			}
//			$results = $tmp;
//		}
//
//
//		foreach ($results as $resultKey=>$result) {
//			foreach ($result as $itemKey=>$item) {
//				if ($item === null) {
//					//remove empty values
//					unset($results[$resultKey][$itemKey]);
//				} else {
//					// bucket filter expect array
//					$results[$resultKey][$itemKey] = [$item];
//				}
//
//			}
//
//		}
//		return $results;
//	}

}
