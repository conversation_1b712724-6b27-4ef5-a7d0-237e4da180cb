<?php declare(strict_types=1);

namespace App\Model\Sitemap\DataProvider;

use App\Model\BucketFilter\ElasticItem\IsPublic;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\ProductVariant\Availability\CustomProductAvailability;
use Closure;
use Elastica\QueryBuilder;
use Generator;

class ProductsProvider extends ElasticProductProvider implements YieldByMutation
{

	/**
	 * @return Closure(int $limit, int $offset): Generator
	 */
	public function getYieldFunction(Mutation $mutation): Closure
	{
		$b = new QueryBuilder();
		$filter = $b->query()->bool()->addMust(
			(new IsPublic())->getCondition()
		)->addMust(
			$b->query()->term(['damageLevel' => 0])
		)->addMust(
			$b->query()->exists('url')
		)->addMust(
			$b->query()->terms('availability', CustomProductAvailability::getOnStockTypes())
		);

		return fn(int $limit, int $offset) => $this->getBaseActiveProductYield($mutation, $filter, $limit, $offset);
	}

}
