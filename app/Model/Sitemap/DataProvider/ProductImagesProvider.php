<?php declare(strict_types = 1);

namespace App\Model\Sitemap\DataProvider;

use App\Model\Image\Storage\BasicStorage;
use App\Model\Orm\LibraryImage\LibraryImageRepository;
use App\Model\Orm\Mutation\Mutation;
use Closure;
use Generator;

class ProductImagesProvider implements YieldByMutation
{

	public function __construct(
		private readonly LibraryImageRepository $libraryImageRepository,
		private readonly BasicStorage $basicStorage,
	)
	{
	}

	public function getYieldFunction(Mutation $mutation): Closure
	{
		return fn (int $limit, int $offset) => $this->getImages($limit, $offset, $mutation->getBaseUrl());
	}

	private function getImages(int $limit, int $offset, string $baseUrl): Generator
	{
		foreach ($this->libraryImageRepository->findImagesForSitemap($limit, $offset) as $productImageRow) {
			$pathInfo = pathinfo($productImageRow->filename);
			$fileName = $pathInfo['filename'];
			$extension = $pathInfo['extension'] ?? '';
			$imagePath = $this->basicStorage->getOriginalImageSrc($fileName, $extension);
			$imagePath = str_replace('/images/', '/images-sm/', $imagePath);
			yield [
				'url' => $baseUrl . $imagePath,
				'lastModified' => $productImageRow->timeOfChange,
			];
		}
	}

}
