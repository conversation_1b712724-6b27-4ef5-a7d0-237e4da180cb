<?php declare(strict_types=1);

namespace App\Model\DateTime;

use DateTimeInterface;
use Nextras\Dbal\Utils\DateTimeImmutable;

class DateTimeHelper
{
	public static function createNextrasDateTime(?DateTimeInterface $object): ?DateTimeImmutable
	{
		if ($object === null) {
			return null;
		}
		$dateTimeImmutable = DateTimeImmutable::createFromInterface($object);
		assert($dateTimeImmutable instanceof DateTimeImmutable);
		return $dateTimeImmutable;
	}
}
