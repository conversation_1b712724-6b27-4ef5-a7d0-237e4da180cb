<?php declare(strict_types = 1);

namespace App\Model;

use Stringable;

final class TranslateData implements Stringable {
	public string $message;
	public array $params = [];

	public function __construct(string $message = '', array $params = []) {
		$this->message = $message;
		$this->params = $params;
	}

	public function __toString(): string
	{
//		trigger_error('Using TranslateData without translation for "'.$this->message.'"', E_USER_NOTICE);
		return $this->message;
	}
}
