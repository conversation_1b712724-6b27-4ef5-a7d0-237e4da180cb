<?php

declare(strict_types=1);

namespace App\Model;

use Brick\Math\BigDecimal;
use Brick\Math\RoundingMode;
use Brick\Money\Money;

final class VatCalculator
{

	public static function priceWithVat(Money $basePrice, BigDecimal $vatRate): Money
	{
		$vatPercentage = $vatRate->toBigRational()->dividedBy(100);
		$vat = $basePrice->multipliedBy($vatPercentage, RoundingMode::HALF_UP);
		return $basePrice->plus($vat);
	}

	public static function priceWithoutVat(Money $price, BigDecimal $vatRate): Money
	{
		$coefficient = $vatRate->toBigRational()->dividedBy($vatRate->plus(100));
		$vat = $price->multipliedBy($coefficient, RoundingMode::HALF_UP);
		return $price->minus($vat);
	}

}
