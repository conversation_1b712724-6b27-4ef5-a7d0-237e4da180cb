<?php declare(strict_types=1);

namespace App\Model\Cache;

use App\Infrastructure\Latte\Functions;
use App\Model\CacheStorageService;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Mutation\MutationRepository;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Page\Model\Orm\TreeRepository;
use Closure;
use Nette\Caching\Cache;

class RouterCache
{

	public function __construct(
		private readonly MutationRepository $mutationRepository,
		private readonly TreeRepository $treeRepository,
		private readonly CacheStorageService $cacheStorageService,
		private readonly MutationsHolder $mutationsHolder,
	)
	{
	}

	public function findUrlPrefixes(): string
	{
		$generator = function () {
			$urlPrefixes = [];
			foreach ($this->mutationRepository->findAll() as $mutation) {
				$urlPrefix = $mutation->getRealUrlPrefix();
				if ($urlPrefix) {
					$urlPrefixes[] = $urlPrefix;
				}
			}

			$options = [
				Cache::Expire => '60 minutes',
			];

			return [
				implode('|', $urlPrefixes),
				$options,
			];
		};

		return $this->cacheStorageService->getStoredDataWithOptions(
			'router',
			Functions::cacheKey('routerPrefixes'),
			$generator
		);
	}


	public function findMyLibraryAliases(): string
	{
		$generator = function () {
			$aliases = [];
			$tags = [];
			foreach ($this->treeRepository->findBy([
				'uid' => Tree::UID_SHARED_LIBRARY,
			]) as $page) {

				assert($page instanceof Tree);
				$tags = array_merge($tags, $page->getTemplateCacheTags());
				if (isset($page->alias->alias)) {
					$aliases[] = $page->alias->alias;
				}

			}

			$options = [
				Cache::Expire => '60 minutes',
				Cache::Tags => array_unique($tags),
			];

			return [
				implode('|', $aliases),
				$options,
			];
		};

		return $this->cacheStorageService->getStoredDataWithOptions(
			'router',
			Functions::cacheKey('libraryAliases'),
			$generator
		);
	}



	/**
	 * @phpstan-param  Closure(string $domainToSearch, string $prefixToSearch): (string) $keyGenerator
	 */
	public function getMutationsMap(Closure $keyGenerator): array
	{
		$generator = function () use ($keyGenerator) {
			$mutations = $this->mutationsHolder->findAll();

			$setup = [];
			foreach ($mutations as $mutation) {
				$setup[$keyGenerator($mutation->getRealDomainWithoutWWW(), $mutation->getRealUrlPrefix())] = $mutation->id;
			}

			$options = [
				Cache::Expire => '60 minutes',
			];

			return [$setup, $options];
		};

		return $this->cacheStorageService->getStoredDataWithOptions(
			'router',
			Functions::cacheKey('mutationsMap'),
			$generator
		);
	}

	public function findAliasesForReview(): string
	{
		$generator = function () {
			$urlReviewPrefix = [];
			$tags = [
				Mutation::class,
			];
			foreach ($this->mutationRepository->findAll() as $mutation) {
				assert($mutation instanceof Mutation);
				$tags[] = Mutation::class . '/' . $mutation->id;
				if ($mutation->urlReviewPrefix !== '' && $mutation->urlReviewPrefix !== null) {
					$urlReviewPrefix[] = $mutation->urlReviewPrefix;
				}
			}

			$options = [
				Cache::Expire => '60 minutes',
				Cache::Tags => array_unique($tags),
			];

			return [
				implode('|', $urlReviewPrefix),
				$options,
			];
		};

		return $this->cacheStorageService->getStoredDataWithOptions(
			'router',
			Functions::cacheKey('aliasesForReview'),
			$generator
		);
	}

}
