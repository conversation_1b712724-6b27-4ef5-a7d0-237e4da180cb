<?php

namespace App\Model\Cache\WarmUpGenerator;

use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\String\StringRepository;
use App\Model\TranslatorDB;
use App\Model\TranslatorDBCacheService;
use Contributte\Console\Extra\Cache\Generators\IGenerator;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class FillTranslate implements IGenerator
{

	public function __construct(
		private readonly MutationsHolder $mutationsHolder,
		private readonly TranslatorDB $translatorDB,
		private readonly StringRepository $stringRepository,
		private readonly TranslatorDBCacheService $translatorDBCacheService,
	)
	{
	}

	public function getDescription(): string
	{
		return 'Fill translate cache';
	}

	public function generate(InputInterface $input, OutputInterface $output): bool
	{
		$mutations = $this->mutationsHolder->findAll(false);
		foreach ($mutations as $mutation) {
			$counter = 0;
			$this->translatorDB->setMutation($mutation);
			foreach ($this->stringRepository->findRawRow($mutation) as $item) {
				$this->translatorDBCacheService->save($mutation, $item->name, $item->value);
				$counter++;
			}
			$output->writeln($mutation->langCode . ' - created items count: ' . $counter);
		}
		return true;
	}

}
