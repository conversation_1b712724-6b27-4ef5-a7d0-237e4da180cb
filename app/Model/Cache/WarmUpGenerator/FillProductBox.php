<?php

namespace App\Model\Cache\WarmUpGenerator;

use App\Model\ElasticSearch\Product\ProductIdsFinder;
use App\Model\Messenger\WarmUp\ProductBox\FillProductBoxMessage;
use App\Model\Mutation\MutationsHolder;
use Contributte\Console\Extra\Cache\Generators\IGenerator;
use Elastica\Query\Range;
use Elastica\QueryBuilder;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Messenger\MessageBusInterface;

class FillProductBox implements IGenerator
{

	public function __construct(
		private readonly MutationsHolder $mutationsHolder,
		private readonly ProductIdsFinder $productIdsFinder,
		private readonly MessageBusInterface $messageBus,
	)
	{
	}

	public function getDescription(): string
	{
		return 'Fill productBox for all public products';
	}

	public function generate(InputInterface $input, OutputInterface $output): bool
	{
		$mutation = $this->mutationsHolder->getDefault();

		$b = new QueryBuilder();
		$boolQuery = $b->query()->bool();
		$boolQuery->addMust(
			$b->query()->bool()->addMust(
				new Range('publicFrom', ['lte' => 'now'])
			)->addMust(
				new Range('publicTo', ['gte' => 'now'])
			)->addMust(
				$b->query()->term(['isPublic' => true])
			)
		);

		$ids = $this->productIdsFinder->getIteratorProductIds($mutation, $boolQuery);

		foreach ($ids() as $id) {
			$this->messageBus->dispatch(
				new FillProductBoxMessage($id, $mutation->id)
			);
		}

		return true;
	}

}
