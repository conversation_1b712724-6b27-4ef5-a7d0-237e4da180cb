<?php declare(strict_types=1);

namespace App\Model\Cache;

use App\Infrastructure\Latte\Functions;
use App\Model\CacheStorageService;
use App\Model\CustomField\LazyValue;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Routable;
use Closure;

class PresenterCache
{

	public function __construct(
		private readonly CacheStorageService $cacheStorageService,
	)
	{
	}

	/**
	 * @phpstan-param  Closure(): (array) $generator
	 */
	public function getLink(Product|LazyValue|Routable|string $destination, array $args, $generator): string
	{
		return $this->cacheStorageService->getStoredDataWithOptions(
			'presenter',
			Functions::cacheKey($destination, ...$args),
			$generator
		);
	}

}
