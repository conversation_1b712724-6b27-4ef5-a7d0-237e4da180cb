<?php declare(strict_types=1);

namespace App\Model;


use Closure;

readonly class CacheStorageService
{

	public function __construct(
		private CacheFactory $cacheFactory,
	)
	{
	}


	/**
	 * @phpstan-param  Closure(): (mixed) $generator
	 */
	public function getStoredData(string $namespace, string $key, callable $generator, array $options = []): mixed
	{
		$storage = $this->cacheFactory->create($namespace);

		$data = $storage->load($key);
		if ($data === null) {
			$data = $generator();
			$storage->save($key, $data, $options);
		}

		return $data;
	}

	/**
	 * @phpstan-param  Closure(): (array) $generatorWithOptions
	 */
	public function getStoredDataWithOptions(string $namespace, string $key, ?callable $generatorWithOptions): mixed
	{
		$storage = $this->cacheFactory->create($namespace);

		$data = $storage->load($key);
		if ($data === null && $generatorWithOptions !== null) {
			[$data, $options] = $generatorWithOptions();
			$storage->save($key, $data, $options);
		}

		return $data;
	}

	/**
	 * @phpstan-param  Closure(): (array) $generatorWithOptions
	 */
	public function saveDataWithOptions(string $namespace, string $key, callable $generatorWithOptions): void
	{
		$storage = $this->cacheFactory->create($namespace);
		[$data, $options] = $generatorWithOptions();
		$storage->save($key, $data, $options);
	}
}
