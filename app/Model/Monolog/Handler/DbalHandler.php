<?php declare(strict_types = 1);

namespace App\Model\Monolog\Handler;

use App\Model\Dbal\LogService;
use Monolog\Formatter\FormatterInterface;
use Monolog\Formatter\JsonFormatter;
use Monolog\Handler\AbstractProcessingHandler;
use Monolog\Level;
use Monolog\LogRecord;

class DbalHandler extends AbstractProcessingHandler
{

	public function __construct(
		private readonly LogService $logService,
		$level = Level::Info,
		bool $bubble = false
	)
	{
		parent::__construct($level, $bubble);
	}

	protected function write(LogRecord $record): void
	{
		$this->logService->create($record);
	}

	protected function getDefaultFormatter(): FormatterInterface
	{
		return new JsonFormatter;
	}

}
