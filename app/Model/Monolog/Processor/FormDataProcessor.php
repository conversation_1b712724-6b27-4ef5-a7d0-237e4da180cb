<?php declare(strict_types=1);

namespace App\Model\Monolog\Processor;

use App\Model\Form\FormDataLogger;
use Monolog\LogRecord;
use Monolog\Processor\ProcessorInterface;
use Nette\Http\FileUpload;


class FormDataProcessor implements ProcessorInterface {

	private const MISSING_DEFINITION_EXCEPTION = 'Add input object translation for %s';
	public function __construct(
		private readonly string $fileLogDirectoryPath,
	)
	{
	}

	public function __invoke(LogRecord $record): LogRecord
	{
		/** @var array $context */
		$context = $record['context'];
		if (isset($context[FormDataLogger::FORM_DATA_KEY])) {
			array_walk_recursive($context[FormDataLogger::FORM_DATA_KEY], function (&$item, $key) {
				if ($item instanceof FileUpload) {
					if ($item->isOk()) {
						$pathToBackupFile = $this->getPathToFile($item);
						if (copy($item->getTemporaryFile(), $pathToBackupFile)) {
							$item = [
								'backup' => $pathToBackupFile,
								'contentType' => $item->getContentType(),
								'size' => $item->getSize(),
							];
						} else {
							$item = null;
						}
					} else {
						$item = null;
					}

				} else if (is_object($item)) {
					throw new \LogicException(sprintf(self::MISSING_DEFINITION_EXCEPTION, get_class($item)));
				}
			});
		}

		if (isset($_SERVER['HTTP_USER_AGENT'])) {
			$record->extra['browser'] = $_SERVER['HTTP_USER_AGENT'];
		}

		return $record;
	}


	private function getPathToFile(FileUpload $fileUpload): string
	{
		return $this->fileLogDirectoryPath . DIRECTORY_SEPARATOR . $this->getUniqFileName($fileUpload);
	}

	private function getUniqFileName(FileUpload $fileUpload): string
	{
		return uniqid() . '-' . $fileUpload->getSanitizedName();
	}
}
