<?php declare(strict_types = 1);

namespace App\Model;

use Nette;

final class Translator implements Nette\Localization\Translator
{

	protected array $list;

	public function __construct(string $lang = 'cz', array $config = [])
	{
		$this->list = $config['translate'][$lang] ?? [];
	}


	/**
	 * Translates the given string.
	 *
	 * @param  mixed  $message
	 * @param  mixed ...$parameters
	 * @return string
	 */
	public function translate($message, ...$parameters): string
	{
		$message = (string) $message;
		return $this->list[$message] ?? $message;
	}

}
