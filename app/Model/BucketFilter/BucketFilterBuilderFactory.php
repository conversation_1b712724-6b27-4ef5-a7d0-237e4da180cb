<?php

declare(strict_types=1);

namespace App\Model\BucketFilter;

use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\RoutableEntity;
use App\Model\Orm\State\State;
use Nette\Application\UI\Presenter;

interface BucketFilterBuilderFactory
{

	public function create(
		Presenter $presenter,
		RoutableEntity $object,
		State $currentState,
		PriceLevel $priceLevel,
		string $sortOrder,
		array $allSelectedParameters = [],
		?array $itemsToSkip = [],
	): BucketFilterBuilder;

}
