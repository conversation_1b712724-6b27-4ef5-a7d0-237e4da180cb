<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\ElasticItem;

use App\Model\BucketFilter\QueryAggregation;
use App\Model\BucketFilter\QueryFilter;
use App\Model\ElasticSearch\Aggregation\BucketScript;
use App\Model\ElasticSearch\Aggregation\BucketSort;
use Closure;
use Elastica\Aggregation\AbstractAggregation;
use Elastica\Query\AbstractQuery;
use Elastica\Query\Term;
use Elastica\Query\Terms;
use Elastica\QueryBuilder;

abstract class DiscreteValues implements ElasticItem, QuestionableElasticItem, AggregableElasticItem
{

	public const NAMESPACE_DIALS = 'dials';
	public const NAMESPACE_FLAGS = 'flags';
	public const NAMESPACE_FLAG_VALUES = 'flagValues';

	public const NAMESPACE_SEARCH = 'searchValue';
	public const MAX_TERMS_LIMIT = 40;

	protected string $elasticPath;

	/**
	 * @phpstan-param Closure(mixed): (int|bool|string)|null $typeInElasticEnforcer
	 */
	public function __construct(
		protected readonly string $elasticKey,
		protected readonly array $selectedValues,
		protected readonly QueryFilter $queryFilter,
		protected ?Closure $typeInElasticEnforcer = null,
		protected readonly string $operatorBetweenValues = QueryAggregation::OPERATOR_OR,
		protected readonly bool $filterEmptySelectedValues = false, // dont ignore empty $selectedValues
		protected ?array $onlyIds = null,
		protected readonly ?bool $nestedValues = false,
	)
	{
		$this->elasticPath = $elasticKey;
	}

	public function setElasticPath(array $path): void
	{
		$this->elasticPath = implode('.', $path);
	}

	public function getCondition(): ?AbstractQuery
	{
		if (!$this->filterEmptySelectedValues && $this->selectedValues === []) {
			return null;
		}

		$b = new QueryBuilder();

		$boolQuery = $b->query()->bool();

		$elasticPath = ($this->nestedValues)? $this->elasticPath . '.id' : $this->elasticPath;

		if ($this->operatorBetweenValues === QueryAggregation::OPERATOR_OR) {
			$terms = new Terms($elasticPath);
			foreach ($this->selectedValues as $selectedValue) {
				//$selectedValue = array_map(fn($value) => $this->typeInElasticEnforcer($value), $selectedValue);
				$terms->addTerm($this->enforcerElasticType($selectedValue));
			}
			$boolQuery->addMust($terms);

		} else {
			foreach ($this->selectedValues as $selectedValue) {
				$boolQuery->addMust((new Term())->setTerm($elasticPath, $this->enforcerElasticType($selectedValue)));
			}
		}

		if ($this->nestedValues) {
			// wrap original query to NESTED
			$boolQuery = $b->query()->nested()->setPath($this->elasticPath)->setQuery($boolQuery);
		}

		return $boolQuery;
	}

	public function getElasticKey(): string
	{
		return $this->elasticKey;
	}


	abstract public function getAggregation(array $filterItems = [], bool $withFiltering = true): AbstractAggregation;

	public function getAggregationName(): string
	{
		return $this->elasticKey . '_agg';
	}


	public function getSelectedValues(): array
	{
		return $this->selectedValues;
	}

	protected function enforcerElasticType(int|float|string|bool $value): int|float|string|bool
	{
		if ($this->typeInElasticEnforcer instanceof Closure) {
			$function = $this->typeInElasticEnforcer;
		} else {
			$function = fn($value) => (int) $value;
		}

		return $function($value);
	}


//	protected function addIdsPriority(AbstractAggregation $aggregation, array $priorityIds): void
//	{
//		$aggregation->addAggregation(
//			new BucketScript(
//				name:'bScriptPriority',
//				bucketsPath: ['currentBucketKey' => '_key'],
//				script: 'if (params.currentBucketKey instanceof Number && params.priorityIds.contains(params.currentBucketKey.intValue())) { return 2; } else { return 1; }',
//				scriptParams: ['priorityIds' => $priorityIds]
//			)
//		)->addAggregation(
//			new BucketSort(
//				name: 'bSort',
//				sort: [
//					'bScriptPriority' => 'desc',
//					'_count' => 'desc',
//				]
//			)
//		);
//	}


	public function getOnlyIds(): array
	{
		return $this->onlyIds ?? [];
	}

}
