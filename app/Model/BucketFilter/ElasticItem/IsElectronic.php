<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\ElasticItem;

use Elastica\Query\AbstractQuery;
use Elastica\Query\Term;
use Elastica\QueryBuilder;

class IsElectronic implements ElasticItem, QuestionableElasticItem
{

	public const ELASTIC_KEY = 'isElectronic';

	public function getCondition(): AbstractQuery
	{
		$b = new QueryBuilder();
		$condition = $b->query()->bool();
		$electronic = new Term();
		$electronic->setTerm(self::ELASTIC_KEY, true);
		$condition->addMust($electronic);

		return $condition;
	}

	public function getElasticKey(): string
	{
		return self::ELASTIC_KEY;
	}

}
