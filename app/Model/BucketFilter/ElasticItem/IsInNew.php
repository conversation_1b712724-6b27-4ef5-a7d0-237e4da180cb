<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\ElasticItem;

use Elastica\Query\AbstractQuery;
use Elastica\Query\Range;
use Elastica\Query\Term;
use Elastica\QueryBuilder;

class IsInNew implements ElasticItem, QuestionableElasticItem
{
	const ELASTIC_KEY = 'isNew';
	public function getCondition(): AbstractQuery
	{
		$b = new QueryBuilder();
		$condition = $b->query()->bool();
		$preorder = new Term();
		$preorder->setTerm('isNew', true);
		$condition->addMust($preorder);


		return $condition;
	}

	public function getElasticKey(): string
	{
		return self::ELASTIC_KEY;
	}

}
