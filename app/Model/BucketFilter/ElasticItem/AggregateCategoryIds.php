<?php declare(strict_types=1);

namespace App\Model\BucketFilter\ElasticItem;

use App\Model\BucketFilter\QueryFilter;
use Elastica\Aggregation\AbstractAggregation;
use Elastica\Aggregation\Filter;
use Elastica\Aggregation\Terms;
use Elastica\Query\AbstractQuery;
use Elastica\QueryBuilder;

class AggregateCategoryIds implements AggregableElasticItem, ElasticItem, QuestionableElasticItem
{
	private const ELASTIC_KEY = 'categories';

	public function __construct(
		private readonly QueryFilter $queryFilter,
	)
	{
	}


	public function getAggregation(array $filterItems = [], bool $withFiltering = true): AbstractAggregation
	{
		$b = new QueryBuilder();

		if ($withFiltering) {
			$aggFilterBuilder = $this->queryFilter->newGet($filterItems);
		} else {
			$aggFilterBuilder = $b->query()->bool();
		}

		$aggr = new Filter('mainCategories_agg');
		$aggr->setFilter($aggFilterBuilder);

		$terms = new Terms('categories');
		$terms->setField('categories');

		$terms->setSize(50);
		$aggr->addAggregation($terms);

		return $aggr;
	}

	public function getElasticKey(): string
	{
		return self::ELASTIC_KEY;
	}

	public function getCondition(): ?AbstractQuery
	{
		return null;
	}
}
