<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\ElasticItem;

use Elastica\Query\AbstractQuery;
use Elastica\QueryBuilder;

final class HasScore implements QuestionableElasticItem
{

	public const GREATER_THAN = 'gt';
	public const GREATER_THAN_OR_EQUAL = 'gte';
	public const LESS_THAN = 'lt';
	public const LEST_THAN_OR_EQUAL = 'lte';

	public function __construct(
		private readonly string $conditionType,
		private readonly float|int $score,
	)
	{
	}

	public function getCondition(): AbstractQuery
	{
		$b = new QueryBuilder();
		return $b->query()
		         ->range('score', [$this->conditionType => $this->score]);
	}



}
