<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\ElasticItem;

use App\Model\Orm\ProductVariant\Availability\CustomProductAvailability;
use Elastica\Query\AbstractQuery;
use Elastica\QueryBuilder;

class IsTypeNotForSale implements ElasticItem, QuestionableElasticItem
{

	private const ELASTIC_KEY = 'availability';

	public function getCondition(): AbstractQuery
	{
		$queryBuilder = new QueryBuilder();
		$boolQuery = $queryBuilder->query()->bool();
		$boolQuery->addMustNot($queryBuilder->query()->term([self::ELASTIC_KEY => CustomProductAvailability::TYPE_NOT_FOR_SALE]));

		return $boolQuery;
	}

	public function getElasticKey(): string
	{
		return self::ELASTIC_KEY;
	}

}
