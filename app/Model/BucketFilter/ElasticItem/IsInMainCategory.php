<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\ElasticItem;

use Elastica\Query\AbstractQuery;
use Elastica\Query\Term;

class IsInMainCategory implements ElasticItem, QuestionableElasticItem
{

	private const ELASTIC_KEY = 'mainCategory';

	public function __construct(
		private readonly int $category
	)
	{
	}


	public function getCondition(): AbstractQuery
	{
		return (new Term())->setTerm(self::ELASTIC_KEY, $this->category);
	}


	public function getElasticKey(): string
	{
		return self::ELASTIC_KEY;
	}


}
