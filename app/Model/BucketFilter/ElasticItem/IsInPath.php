<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\ElasticItem;

use App\PostType\Page\Model\Orm\Tree;
use Elastica\Query\AbstractQuery;
use Elastica\Query\Term;

class IsInPath implements ElasticItem, QuestionableElasticItem
{

	private const ELASTIC_KEY = 'path';

	public function __construct(
		private readonly Tree $pathObject,
	)
	{
	}


	public function getCondition(): AbstractQuery
	{
		return (new Term())->setTerm(self::ELASTIC_KEY, $this->pathObject->id);
	}

	public function getElasticKey(): string
	{
		return self::ELASTIC_KEY;
	}

}
