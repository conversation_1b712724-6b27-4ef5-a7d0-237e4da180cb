<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\ElasticItem;

use App\Model\BucketFilter\QueryFilter;
use Elastica\Aggregation\AbstractAggregation;
use Elastica\Aggregation\Filter;
use Elastica\Aggregation\Max;
use Elastica\Aggregation\Min;
use Elastica\Query\AbstractQuery;
use Elastica\Query\BoolQuery;
use Elastica\QueryBuilder;

class Range implements ElasticItem, QuestionableElasticItem, AggregableElasticItem
{
	public const NAMESPACE_RANGES = 'ranges';

	private string $elasticPath;

	public function __construct(
		private readonly QueryFilter $queryFilter,
		private readonly string $elasticKey,
		private readonly ?float $selectedValueMin,
		private readonly ?float $selectedValueMax,
		private readonly string $minOperator = 'gte',
		private readonly string $maxOperator = 'lte',
		private readonly ?BoolQuery $conditionMust = null,
		private readonly ?BoolQuery $aggregationFilterMust = null,
	)
	{
		$this->elasticPath = $elasticKey . '_range';
	}

	public function setElasticPath(array $path): void
	{
		$this->elasticPath = implode('.', $path);
	}

	public function getCondition(): ?AbstractQuery
	{
		$b = new QueryBuilder();
		$boolQuery = $b->query()->bool();

		if ($this->conditionMust !== null) {
			$boolQuery->addMust($this->conditionMust);
		}
		$rangeCondition = [];
		if ($this->selectedValueMin !== null) {
			$rangeCondition[$this->minOperator] = $this->selectedValueMin;
		}

		if ($this->selectedValueMax !== null) {
			$rangeCondition[$this->maxOperator] = $this->selectedValueMax;
		}

		if ($rangeCondition !== []) {
			$range = new \Elastica\Query\Range($this->elasticPath, $rangeCondition); //TODO NUmber?
			$boolQuery->addMust($range);
			return $boolQuery;
		} else {
			return null;
		}
	}

	public function getElasticKey(): string
	{
		return $this->elasticKey;
	}

	public function getAggregationName(): string
	{
		return $this->elasticKey . '_agg_range';
	}

	public function getAggregation(array $filterItems = [], bool $withFiltering = true): AbstractAggregation
	{
		$aggFilter = $this->queryFilter->newGet($filterItems, null, true);

		if ($this->aggregationFilterMust !== null) {
			$aggFilter->addMust($this->aggregationFilterMust);
		}

		$aggr = new Filter($this->getAggregationName());
		$aggr->setFilter($aggFilter);

		$max = new Max('max');
		$max->setField($this->elasticPath);
		$aggr->addAggregation($max);

		$min = new Min('min');
		$min->setField($this->elasticPath);
		$aggr->addAggregation($min);

		return $aggr;
	}

	public function getOptions(): array
	{
		return [];
	}

	public function getSelectedValues(): array
	{
		return [
			'min' => $this->selectedValueMin,
			'max' => $this->selectedValueMax,
		];
	}
}
