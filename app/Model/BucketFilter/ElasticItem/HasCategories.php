<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\ElasticItem;

use Elastica\Query\AbstractQuery;
use Elastica\QueryBuilder;

final class HasCategories implements QuestionableElasticItem
{

	public function __construct(
		private readonly array $categoryIds,
	)
	{
	}

	public function getCondition(): AbstractQuery
	{
		$b = new QueryBuilder();

		return $b->query()->bool()->setMinimumShouldMatch(1)->addShould(
			$b->query()->terms('categories', $this->categoryIds)
		);
	}

}
