<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\ElasticItem;

use App\Model\BucketFilter\QueryFilter;
use App\PostType\Tag\Model\Orm\TagLocalization\TagLocalization;
use Elastica\Aggregation\AbstractAggregation;
use Elastica\Aggregation\Filter;
use Elastica\Aggregation\Terms;
use Elastica\Query\AbstractQuery;
use Elastica\Query\Term;
use Elastica\QueryBuilder;
use Nette\InvalidArgumentException;

class IsInTag implements ElasticItem, QuestionableElasticItem
{
	private const ELASTIC_KEY = 'tags';

	public function __construct(
		private readonly TagLocalization $tagLocalization,
		private readonly ?int $category = null,
	)
	{
	}

	public function getCondition(): AbstractQuery
	{
		$qb = new QueryBuilder();
		$boolQuery = $qb->query()->bool()->addMust(
			(new Term())->setTerm(self::ELASTIC_KEY, $this->tagLocalization->getParent()->id)
		);

		if($this->category !== null){
			$boolQuery->addMust((new Term())->setTerm('categories', $this->category));
		}

		return $boolQuery;
	}

	public function getElasticKey(): string
	{
		return self::ELASTIC_KEY;
	}
}
