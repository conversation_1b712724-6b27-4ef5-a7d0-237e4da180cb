<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\ElasticItem;

use Elastica\Query\AbstractQuery;
use Elastica\Query\Term;

class IsClass implements ElasticItem, QuestionableElasticItem
{

	private const ELASTIC_KEY = 'productType';

	public function __construct(
	)
	{
	}


	public function getCondition(): AbstractQuery
	{
		return (new Term())->setTerm(self::ELASTIC_KEY, 'CLASS');
	}

	public function getElasticKey(): string
	{
		return self::ELASTIC_KEY;
	}

}
