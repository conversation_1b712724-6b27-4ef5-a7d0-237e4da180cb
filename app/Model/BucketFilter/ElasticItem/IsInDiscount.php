<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\ElasticItem;

use App\Model\BucketFilter\QueryFilter;


use App\PostType\Discount\Model\Orm\DiscountLocalization\DiscountLocalization;
use Elastica\Aggregation\AbstractAggregation;
use Elastica\Aggregation\Filter;
use Elastica\Aggregation\Terms;
use Elastica\Query\AbstractQuery;
use Elastica\Query\Term;
use Elastica\QueryBuilder;

class IsInDiscount implements ElasticItem, QuestionableElasticItem, AggregableElasticItem
{

	private const ELASTIC_KEY = 'discounts';

	public function __construct(
		private readonly DiscountLocalization $object,
		private readonly QueryFilter $queryFilter,
		private readonly ?int $category = null,
	)
	{
	}


	public function getCondition(): AbstractQuery
	{

		$publisherCondition = (new Term())->setTerm(self::ELASTIC_KEY, $this->object->discount->id);

		$qb = new QueryBuilder();
		$boolQuery = $qb->query()->bool()->addMust($publisherCondition);

		if($this->category !== null){
			$boolQuery->addMust((new Term())->setTerm('categories', $this->category));
		}


		return $boolQuery;
	}

	public function getAggregation(array $filterItems = [], bool $withFiltering = true): ?AbstractAggregation
	{
		$b = new QueryBuilder();

		if ($this->category !== null) {
			return null;
		}

		if ($withFiltering) {
			$aggFilterBuilder = $this->queryFilter->newGet($filterItems, 'categories');
		} else {
			$aggFilterBuilder = $b->query()->bool();
		}

		$aggr = new Filter('mainCategories_agg');
		$aggr->setFilter($aggFilterBuilder);

		$terms = new Terms('categories');
		$terms->setField('categories');

		$terms->setSize(50);
		$aggr->addAggregation($terms);

		return $aggr;
	}

	public function getElasticKey(): string
	{
		return self::ELASTIC_KEY;
	}

}
