<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\ElasticItem;

use App\Model\BucketFilter\BucketFilter;
use App\Model\BucketFilter\BucketFilterBuilder;
use App\Model\ElasticSearch\Common\ElasticCommon;
use Elastica\Query\AbstractQuery;
use Elastica\Query\Term;
use Elastica\QueryBuilder;

class IsCalendar implements ElasticItem, QuestionableElasticItem
{
	private const ELASTIC_KEY = 'isCalendar';

	public function getCondition(): AbstractQuery
	{
		$b = new QueryBuilder();
		$condition = $b->query()->bool();
		$isBlog = new Term();
		$isBlog->setTerm('type', BucketFilterBuilder::TYPE_CALENDAR);
		$condition->addMust($isBlog);
		return $condition;
	}

	public function getElasticKey(): string
	{
		return self::ELASTIC_KEY;
	}

}
