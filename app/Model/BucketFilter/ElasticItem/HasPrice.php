<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\ElasticItem;

use App\Model\Currency\CurrencyHelper;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;
use Elastica\Query\AbstractQuery;
use Elastica\Query\Term;
use Elastica\QueryBuilder;

final class HasPrice implements QuestionableElasticItem
{

	public function __construct(
		private readonly PriceLevel $priceLevel,
		private readonly State $state
	)
	{
	}

	public function getCondition(): AbstractQuery
	{
		$b = new QueryBuilder();
		$condition = $b->query()->bool();
		$hasPrice = new Term();
		$hasPrice->setTerm('hasPrice.' . $this->state->code . '.' . $this->priceLevel->type . '.' . CurrencyHelper::getCurrencyCode(), true);
		$condition->addMust($hasPrice);

		return $condition;
	}

}
