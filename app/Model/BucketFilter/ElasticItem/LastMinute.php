<?php

namespace App\Model\BucketFilter\ElasticItem;

use App\Model\BucketFilter\QueryFilter;
use App\Model\Time\CurrentDateTimeProvider;
use Elastica\Aggregation\AbstractAggregation;
use Elastica\Query\AbstractQuery;
use Elastica\Query\BoolQuery;
use Elastica\QueryBuilder;

class LastMinute implements ElasticItem, AggregableElasticItem, QuestionableElasticItem
{

	const string CLASS_EVENTS = 'classEvents';

	public function __construct(
		private readonly QueryFilter $queryFilter,
		private readonly CurrentDateTimeProvider $currentDateTimeProvider,
		private readonly ?array $selectedValues = [],
	)
	{
	}


	public function getElasticKey(): string
    {
        return self::CLASS_EVENTS;
    }

	public function getAggregation(array $filterItems = [], bool $withFiltering = true): AbstractAggregation
	{
		$b = new QueryBuilder();

		if ($withFiltering) {
			$aggFilterBuilder = $this->queryFilter->newGet($filterItems, $this->getElasticKey());
		} else {
			$aggFilterBuilder = $b->query()->bool();
		}


		return $b->aggregation()->nested('itemsWithLastMinute_agg', self::CLASS_EVENTS)->addAggregation(
			$b->aggregation()->filter('lastMinute_agg')->setFilter(
				$this->getBaseQuery()->addMust($aggFilterBuilder)
			)->addAggregation(
				$b->aggregation()->reverse_nested("parentData_agg")
			)
		);
	}

	public function getCondition(): ?AbstractQuery
	{
		if ($this->selectedValues === []) {
			return null;
		}
		$b = new QueryBuilder();
		return $b->query()->nested()->setPath(self::CLASS_EVENTS)->setQuery($this->getBaseQuery());
	}


	private function getBaseQuery(): BoolQuery
	{
		$currentTimeInSeconds = $this->currentDateTimeProvider->getCurrentDateTime()->getTimestamp();

		$b = new QueryBuilder();
		return $b->query()->bool()
			->addShould(['script' => ['script' => "return (doc['classEvents.emptyCapacity'].value > 0 && doc['classEvents.emptyCapacity'].value <= doc['classEvents.emptyCapacityLastMinuteLimit'].value)"]])
			->addShould(['script' => ['script' => "

				def fromLastMinuteLimitMs = " . $currentTimeInSeconds . " + doc['classEvents.fromLastMinuteLimit'].value * 24 * 60 * 60;
				return (doc['classEvents.from'].value.toEpochMilli() / 1000 > " . $currentTimeInSeconds . " && doc['classEvents.from'].value.toEpochMilli() / 1000 <= fromLastMinuteLimitMs)"]])
			->setMinimumShouldMatch(1);
	}

	public function getSelectedValues(): array
	{
		return $this->selectedValues;
	}
}
