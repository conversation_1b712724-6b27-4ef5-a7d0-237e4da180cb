<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\ElasticItem;

use Elastica\Query\AbstractQuery;
use Elastica\Query\Range;
use Elastica\Query\Term;
use Elastica\Query\Terms;
use Elastica\QueryBuilder;

class IsAvailable implements QuestionableElasticItem
{
	public function getCondition(): AbstractQuery
	{
		$b = new QueryBuilder();
		$condition = $b->query()->bool()->setMinimumShouldMatch(1);
		$store = new Term();
		$store->setTerm('isInStore', true);
		$condition->addShould($store);

		$supplier = new Term();
		$supplier->setTerm('isInSupplier', true);
		$condition->addShould($supplier);

		return $condition;
	}



}
