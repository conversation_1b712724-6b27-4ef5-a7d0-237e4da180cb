<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\ElasticItem;

use App\Model\BucketFilter\QueryAggregation;
use App\Model\BucketFilter\QueryFilter;
use App\Model\Orm\Parameter\Parameter;
use Closure;
use Elastica\Aggregation\AbstractAggregation;
use Elastica\Aggregation\Filter;
use Elastica\Aggregation\Max;
use Elastica\Aggregation\ScriptedMetric;
use Elastica\Query\Term;
use Elastica\Query\Terms;
use Elastica\QueryBuilder;
use Elastica\Script\Script;

class MultiValue extends DiscreteValues
{

	/**
	 * @phpstan-param  Closure(): array $getOptionFunction
	 * @phpstan-param  null|Closure(mixed): (int|bool|string) $typeInElasticEnforcer
	 */
	public function __construct(
		string $elasticKey,
		array $selectedValues,
		private readonly Closure $getOptionFunction,
		QueryFilter $queryFilter,
		?Closure $typeInElasticEnforcer = null,
		string $operatorBetweenValues = QueryAggregation::OPERATOR_OR,
		bool $filterEmptySelectedValues = false,
		?array $onlyIds = [],
		?bool $nestedValues = false,
	)
	{
		parent::__construct($elasticKey, $selectedValues, $queryFilter, $typeInElasticEnforcer, $operatorBetweenValues, $filterEmptySelectedValues, $onlyIds, $nestedValues);
	}

	public function getAggregation(array $filterItems = [], bool $withFiltering = true): AbstractAggregation
	{
		$b = new QueryBuilder();

		if ($withFiltering) {
			$aggFilterBuilder = $this->queryFilter->newGet($filterItems, $this->elasticKey);
		} else {
			$aggFilterBuilder = $b->query()->bool();
		}

		$aggr = new Filter($this->getAggregationName());
		$aggr->setFilter($aggFilterBuilder);

		if ($this->nestedValues) {
			$subAggregation = $b->aggregation()->terms('sub_' . $this->elasticKey . '_agg')->setField($this->elasticPath . '.id')
				->setSize(self::MAX_TERMS_LIMIT)->setOrder('ordering', 'asc');
			$subAggregation->addAggregation(
				$b->aggregation()->min('ordering')->setField($this->elasticPath . '.sort')
			);

			if ($this->onlyIds !== null){
				// if filtering by onlyIds don't show random values from aggregation
				$subAggregation->setIncludeAsExactMatch(array_values($this->onlyIds));
			}

			$aggr->addAggregation(
				$b->aggregation()->nested($this->elasticKey . '_agg', $this->elasticPath)->addAggregation(
					$subAggregation
				)
			);

		} else {
			$terms = new \Elastica\Aggregation\Terms($this->elasticKey);
			$terms->setField($this->elasticPath);

			if($this->onlyIds !== null){
				// if filtering by onlyIds don't show random values from aggregation
				$terms->setIncludeAsExactMatch(array_keys($this->onlyIds));
			}

			$terms->setSize(self::MAX_TERMS_LIMIT);
			$aggr->addAggregation($terms);
		}

		return $aggr;
	}

	public function getOptions(): array
	{
		$getOptionFunction = $this->getOptionFunction;
		return $getOptionFunction();
	}
}
