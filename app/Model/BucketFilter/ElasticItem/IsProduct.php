<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\ElasticItem;

use Elastica\Query\AbstractQuery;
use Elastica\Query\Terms;

class IsProduct implements ElasticItem, QuestionableElasticItem
{

	private const ELASTIC_KEY = 'productType';

	public function __construct(
	)
	{
	}


	public function getCondition(): AbstractQuery
	{
		return new Terms(self::ELASTIC_KEY)->setTerms(['product']);
	}

	public function getElasticKey(): string
	{
		return self::ELASTIC_KEY;
	}

}
