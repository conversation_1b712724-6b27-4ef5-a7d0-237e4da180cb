<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\ElasticItem;

use Elastica\Query\AbstractQuery;
use Elastica\Query\Range;
use Elastica\Query\Term;
use Elastica\QueryBuilder;

class IsNotOld implements ElasticItem, QuestionableElasticItem
{

	private const ELASTIC_KEY = 'isOld';

	public function getCondition(): AbstractQuery
	{
		$b = new QueryBuilder();
		$condition = $b->query()->bool();
		$publicOnly = new Term();
		$publicOnly->setTerm(self::ELASTIC_KEY, false);
		$condition->addMust($publicOnly);

		return $condition;
	}

	public function getElasticKey(): string
	{
		return self::ELASTIC_KEY;
	}

}
