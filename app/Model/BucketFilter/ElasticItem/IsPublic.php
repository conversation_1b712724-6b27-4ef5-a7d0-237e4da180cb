<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\ElasticItem;

use Elastica\Query\AbstractQuery;
use Elastica\Query\Range;
use Elastica\Query\Term;
use Elastica\QueryBuilder;

class IsPublic implements ElasticItem, QuestionableElasticItem
{

	private const ELASTIC_KEY = 'path';

	public function getCondition(): AbstractQuery
	{
		$b = new QueryBuilder();
		$condition = $b->query()->bool();
		$publicOnly = new Term();
		$publicOnly->setTerm('isPublic', true);
		$condition->addMust($publicOnly);

		$publicFrom = new Range('publicFrom', [
			'lte' => 'now',
		]);
		$condition->addMust($publicFrom);

		$publicTo = new Range('publicTo', [
			'gte' => 'now',
		]);
		$condition->addMust($publicTo);

		return $condition;
	}

	public function getElasticKey(): string
	{
		return self::ELASTIC_KEY;
	}

}
