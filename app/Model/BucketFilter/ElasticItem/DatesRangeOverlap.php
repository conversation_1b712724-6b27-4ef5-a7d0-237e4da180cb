<?php

namespace App\Model\BucketFilter\ElasticItem;

use App\Model\ElasticSearch\ConvertorHelper;
use Elastica\Query\AbstractQuery;
use DateTimeInterface;
use Elastica\Query\MatchNone;
use Elastica\Query\Range;
use Elastica\QueryBuilder;

/**
 * Query that looks IF given from-to range OVERLAPS with ranges of searched items
 *
 * It can cover overlapping two ranges if both properties $from-$to are filled
 * OR overlapping with just one of the time directions specified, $from-* or *-$to
 *
 * @see self::getCondition
 */
readonly class DatesRangeOverlap implements QuestionableElasticItem
{
	public function __construct(
		private string $fieldFrom,
		private string $fieldTo,
		private ?DateTimeInterface $from = null,
		private ?DateTimeInterface $to = null,
	)
	{

	}

	public function getCondition(): AbstractQuery
	{
		$b = new QueryBuilder();

		$rangeCondition = $b->query()->bool();

		$from = $this->from !== null ? ConvertorHelper::convertTime($this->from) : null;
		$to = $this->to !== null ? ConvertorHelper::convertTime($this->to) : null;

		// --------------------------------------------------------------------------
		if ($from !== null && $to !== null) {
			if ($from <= $to) {
				// Example $from-$to is 2001-2003

				// Either from (1) OR to (2) is BETWEEN $from and $to
				// Example from-to is 1998-2002, 2002-2005...
				foreach ([$this->fieldFrom, $this->fieldTo] as $field) {
					$rangeCondition->addShould(new Range($field, [
						'gte' => $from,
						'lte' => $to,
					]));
				}
				// (3) from is LESS than $from AND to is MORE than $to
				// Example from-to is 1999-2004
				$rangeCondition->addShould($b->query()->bool()->addMust(new Range($this->fieldFrom, [
					'lte' => $from,
				]))->addMust(new Range($this->fieldTo, [
					'gte' => $to,
				])));
			} else {
				// Invalid from-to range
				$rangeCondition->addMust(new MatchNone());
			}
		// --------------------------------------------------------------------------
		} elseif ($from !== null) {
			// Example $from is 2001

			// Anything that has $from lesser or equal than to
			// Example 2000-2003, 2002-2004, ...
			$rangeCondition->addMust(new Range($this->fieldTo, [
				'gte' => $from,
			]));
		// --------------------------------------------------------------------------
		} elseif ($to !== null) {
			// Example $to is 2003

			// Anything that has to $to greater or equal than from
			// Example 1997-2006, 1998-2003, ...

			$rangeCondition->addMust(new Range($this->fieldFrom, [
				'lte' => $to,
			]));
		}

		return $rangeCondition;
	}
}
