<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\ElasticItem;

use Elastica\Query\AbstractQuery;
use Elastica\QueryBuilder;

class Fulltext implements ElasticItem, QuestionableElasticItem
{

	public function __construct(
		private readonly string $fulltext,
		private readonly array $forcedIds = [],
	)
	{
	}

	public function getCondition(): AbstractQuery
	{
		$b = new QueryBuilder();
		if ($this->forcedIds !== []) {
			return $b->query()->terms('id', $this->forcedIds);

		} else {
			$fields = [];
			$fields[] = 'fulltext-name.customEdgeNgram^3';
			$fields[] = 'fulltext-content.customEdgeNgram';
			$fields[] = 'fulltext-categories.customEdgeNgram';
			$fields[] = 'fulltext-tag.customEdgeNgram';


			return

				$b->query()->bool()
					// normal fulltext
					->addShould(
						$b->query()->bool()->addMust(
							$b->query()->multi_match()
								->setType('best_fields') //phrase_prefix phrase, cross_fields best_fields
								->setQuery($this->fulltext)
								->setFuzziness(1)
								->setOperator('OR')
								->setFields($fields)
						)->addMust(
							$b->query()->bool()->addShould(
								$b->query()->range('score', ['lt' => 20])
							)->addShould(
								$b->query()->range('score', ['gte' => 20, 'boost' => 10])
							)->setMinimumShouldMatch(1)
						)
				)->setMinimumShouldMatch(1);


				;
		}
	}

	public function getElasticKey(): string
	{
		return '';
	}

}
