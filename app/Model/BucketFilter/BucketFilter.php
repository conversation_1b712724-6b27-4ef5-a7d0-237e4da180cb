<?php declare(strict_types = 1);

namespace App\Model\BucketFilter;

use App\Infrastructure\Latte\Functions;
use App\Model\BucketFilter\SetupCreator\BasicElasticItemListGenerator;
use App\Model\BucketFilter\SetupCreator\BoxListGenerator;
use App\Model\BucketFilter\SetupCreator\ElasticItemListGenerator;
use App\Model\CacheStorageService;
use App\Model\ElasticSearch\IndexModel;
use App\Model\ElasticSearch\Product\MultiResultReader;
use App\Model\ElasticSearch\Product\ResultReader as ProductResultReader;
use App\Model\ElasticSearch\Common\ResultReader as CommonResultReader;
use App\Model\Orm\EsIndex\EsIndex;
use App\Model\Orm\Orm;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\Routable;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\Calendar\Model\Orm\CalendarLocalization;
use Elastica\Query;
use Elastica\QueryBuilder;
use Elastica\ResultSet;
use Nette\Caching\Cache;
use Nette\Utils\ArrayList;
use Nextras\Orm\Collection\ICollection;
use stdClass;

final class BucketFilter
{
	private array $excludeIds = [];

	public function __construct(
		private readonly BasicElasticItemListGenerator $basicElasticItemListGenerator,
		private readonly ElasticItemListGenerator $elasticItemListGenerator,
		private readonly BoxListGenerator $boxListGenerator,
		private readonly string $type,
		private readonly QueryFilter $queryFilter,
		private readonly QueryBaseFilter $queryBaseFilter,
		private readonly QueryAggregation $queryAggregation,
		private readonly FilterResultMapper $filterResultMapper,
		private readonly ProductResultReader $productResultReader,
		private readonly CommonResultReader $commonResultReader,
		private readonly MultiResultReader $multiResultReader,
		private readonly IndexModel $indexModel,
		private readonly CacheStorageService $cacheStorageService,
		private readonly EsIndex|null $esIndex = null,
		private readonly int|float|null $minScore = null,
	)
	{
	}


	public function getFilter(Routable $object, array $selectedParameters = []): stdClass
	{
		$generator = function () {
			$query = $this->getAggregationQueryWithoutFiltering();
			return $this->tryRunQuery($query);
		};
		$options = [
			Cache::Expire => '0 minutes',
		];
		$emptyCountRes = $this->cacheStorageService->getStoredData('ESresponse', Functions::cacheKey($object), $generator, $options);

		$countRes = null;
		if ($selectedParameters !== []) {
			$query = $this->getAggregationQuery();
			$countRes = $this->tryRunQuery($query);
		}

		return $this->filterResultMapper->convert(
			$this->boxListGenerator->getBoxList(),
			$selectedParameters,
			$this->elasticItemListGenerator->getElasticItemList(),
			$emptyCountRes,
			$countRes
		);
	}


	public function getItems(
		int $limit,
		int $offset,
		Sort $sort,
	): Result
	{
		$query = new Query();
		$query->setFrom($offset);
		$query->setSize($limit);
		$query->setTrackTotalHits();
		if ($this->minScore !== null) {
			$query->setMinScore($this->minScore);
			$query->setTrackScores();
		}

		foreach ($sort->getSentences() as $key => $direction) {
			if (is_string($direction)) {
				$query->addSort([
					$key => $direction,
				]);
			} else {
				$query->addSort($direction);
			}
		}

		$b = new QueryBuilder();
		$boolQuery = $b->query()->bool();
		$boolQuery->addMust($this->queryBaseFilter->get($this->basicElasticItemListGenerator->getBasicElasticItemList()));
		$boolQuery->addMust($this->queryFilter->newGet($this->elasticItemListGenerator->getElasticItemList()));

		if(!empty($this->excludeIds)) {
			$boolQuery->addMustNot((new QueryBuilder())->query()->terms("id", $this->excludeIds));
		}

		$query->setQuery($boolQuery);

		$elasticResult = $this->tryRunQuery($query);
		if ( $elasticResult === null) {
			return Result::empty();
		}

		if ($this->type === BucketFilterBuilder::TYPE_BLOG) {
			/** @var ICollection<BlogLocalization> $items */
			$items = $this->commonResultReader->mapResultToEntityCollection(
				$elasticResult,
				BlogLocalization::class
			);
		} elseif ($this->type === BucketFilterBuilder::TYPE_CALENDAR) {
			/** @var ICollection<CalendarLocalization> $items */
			$items = $this->commonResultReader->mapResultToEntityCollection(
				$elasticResult,
				CalendarLocalization::class
			);
		} elseif ($this->type === BucketFilterBuilder::TYPE_CATALOG) {
			/** @var ArrayList<Product|ProductVariant> $items */
			$items = $this->multiResultReader->mapResultToEntityCollection(
				$elasticResult
			);
		} else {
			/** @var ICollection<Product> $items */
			$items = $this->productResultReader->mapResultToEntityCollection(
				$elasticResult
			);
		}

		return Result::from(
			$items, /** @phpstan-ignore-line */
			$elasticResult->count(),
			$elasticResult->getTotalHits(),
		);
	}


	private function getAggregationQueryWithoutFiltering(): Query
	{
		return $this->getAggregationQuery(false);
	}


	private function getAggregationQuery(bool $withAggregationFilters = true): Query
	{
		// Elastic Item List (filter) aggregations
		$query = $this->getBaseAggregationQuery();
		$aggregations = $this->queryAggregation->get($this->elasticItemListGenerator->getElasticItemList(), $withAggregationFilters);
		foreach ($aggregations as $aggregation) {
			$query->addAggregation($aggregation);
		}

		// Base aggregations
		$aggregations = $this->queryAggregation->get($this->basicElasticItemListGenerator->getBasicElasticItemList(), $withAggregationFilters);
		foreach ($aggregations as $aggregation) {
			$query->addAggregation($aggregation);
		}

		return $query;
	}


	private function tryRunQuery(Query $query): ?ResultSet
	{
		$esResult = null;
		if ($this->esIndex) {
			$esResult = $this->indexModel->getIndex($this->esIndex)->search($query);
		}

		return $esResult;
	}


	private function getBaseAggregationQuery(): Query
	{
		$query = new Query();
		$query->setSize(0);
		if ($this->minScore !== null) {
			$query->setMinScore($this->minScore);
		}
		$query->setQuery($this->queryBaseFilter->get($this->basicElasticItemListGenerator->getBasicElasticItemList()));
		return $query;
	}

	public function setExcludeIds(array $excludeIds = []): BucketFilter
	{
		$this->excludeIds = $excludeIds;
		return $this;
	}

}
