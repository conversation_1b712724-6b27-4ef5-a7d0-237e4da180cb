<?php

declare(strict_types=1);

namespace App\Model\BucketFilter;

use App\Model\BucketFilter\SetupCreator\BasicElasticItemListGenerator;
use App\Model\BucketFilter\SetupCreator\BoxListGenerator;
use App\Model\BucketFilter\SetupCreator\ElasticItemListGenerator;
use App\Model\Orm\EsIndex\EsIndex;
use App\Model\Orm\Mutation\Mutation;

interface BucketFilterFactory
{

	public function create(
		BasicElasticItemListGenerator $basicElasticItemListGenerator,
		ElasticItemListGenerator $elasticItemListGenerator,
		BoxListGenerator $boxListGenerator,
		EsIndex $esIndex,
		string $type,
		int|float|null $minScore = null
	): BucketFilter;

}
