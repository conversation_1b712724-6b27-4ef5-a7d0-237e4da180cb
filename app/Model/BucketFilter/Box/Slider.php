<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\Box;

use App\Model\BucketFilter\ElasticItem\Range;
use Closure;

class Slider
{

	public string $name;

	public string $namespace;

	public string $inputNameMax;

	public string $inputNameMin;

	public string $noUiSliderType;

	public ?float $inputValueMin;

	public ?float $inputValueMax;

	public array $filterToDeSelect;

	public ?float $selectedMin;

	public ?float $selectedMax;

	public ?float $selectedByUserMin;

	public ?float $selectedByUserMax;

	public int $step;

	/**
	 *
	 * @phpstan-param Closure(int|float $value): (int|float|string) $formatter
	 */
	public function __construct(
		public readonly string $title,
		public readonly string $filterPrefix = '',
		public readonly string $filterSuffix = '',
		public readonly string $unit = '',
		private readonly int $steps = 20,
		private readonly bool $forceOpen = false,
		public readonly bool $indexable = false,
		private readonly null|Closure $formatter = null,
	)
	{
		$this->noUiSliderType = 'range'; // fo noUiSlider in js
		$this->namespace = Range::NAMESPACE_RANGES;
	}

	public function populate(string $name, array $aggregationValues, array $selectedValues, array $allSelectedValues): void
	{
		$this->name = $name;
		$this->selectedByUserMin = $selectedValues['min'];
		$this->selectedByUserMax = $selectedValues['max'];
		$this->inputValueMin = $aggregationValues['min'];
		$this->inputValueMax = $aggregationValues['max'];
		$this->selectedMin = $selectedValues['min'] ?? $aggregationValues['min'];
		$this->selectedMax = $selectedValues['max'] ?? $aggregationValues['max'];

		$filterToDeSelect = $allSelectedValues;
		unset($filterToDeSelect[$this->namespace][$name]);
		$this->filterToDeSelect = $filterToDeSelect;


		$this->inputNameMin = 'filter[' . $this->namespace . '][' . $name . '][min]';
		$this->inputNameMax = 'filter[' . $this->namespace . '][' . $name . '][max]';

		if ($name === 'price') {
			$this->step = 1;
		} else {
			$this->step = (int) round(($this->inputValueMax - $this->inputValueMin) / $this->steps);
		}

	}


	public function isOpen(): bool
	{
		return $this->forceOpen || ($this->selectedByUserMin !== $this->inputValueMin || $this->selectedByUserMax !== $this->inputValueMax);
	}

	public function getCheckedItems(): bool
	{
		return ($this->selectedByUserMin !== null && $this->selectedByUserMin !== $this->inputValueMin
			|| $this->selectedByUserMax !== null && $this->selectedByUserMax !== $this->inputValueMax);
	}

	public function format(int|float $value): int|float|string
	{
		if (($formatter = $this->formatter) !== null) {
			return $formatter($value);
		}
		return $value;
	}

}
