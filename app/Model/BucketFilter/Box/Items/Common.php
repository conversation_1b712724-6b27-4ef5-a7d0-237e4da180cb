<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\Box\Items;

class Common
{

	public readonly int $inputValue;

	public readonly string $inputName;

	public readonly array $filterToSelect;

	public readonly array $filterToDeSelect;

	public readonly array $followingFilterParameters;

	public function __construct(
		public readonly string $name,
		public readonly string $filterName,
		public readonly string $filterNameForTitle,
		public readonly string $filterPrefix,
		public readonly string $filterSuffix,
		public readonly string $unit,
		string $mainKey,
		array $allSelectedValues,
		int $valueId,
		public readonly ?int $count,
		public readonly bool $isChecked,
		string $namespace,
	)
	{
		$this->inputName = 'filter[' . $namespace . '][' . $mainKey . '][' . $valueId . ']';
		$this->inputValue = $valueId;

		$filterToSelect = $allSelectedValues;
		$filterToSelect[$namespace][$mainKey][$valueId] = $valueId;
		$this->filterToSelect = $filterToSelect;

		$filterToDeSelect = $this->filterToSelect;
		unset($filterToDeSelect[$namespace][$mainKey][$valueId]);
		if ($filterToDeSelect[$namespace][$mainKey] === []) {
			unset($filterToDeSelect[$namespace][$mainKey]);
		}
		$this->filterToDeSelect = $filterToDeSelect;


		$this->followingFilterParameters = ($isChecked) ? $this->filterToDeSelect : $this->filterToSelect;
	}

}
