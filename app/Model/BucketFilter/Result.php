<?php

declare(strict_types=1);

namespace App\Model\BucketFilter;

use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use Nette\SmartObject;
use Nette\Utils\ArrayList;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;

/**
 * @property-read array|int[] $itemIds
 * @property ICollection<Product>|ArrayList<Product|ProductVariant> $items
 * @property int $count
 * @property int $totalCount
 */
final class Result
{

	use SmartObject;

	/** @var ICollection<Product>|ArrayList<Product|ProductVariant> */
	private ICollection|ArrayList $items;

	private int $count;

	private int $totalCount;


	private function __construct()
	{
	}


	public static function empty(): self
	{
		/** @var ICollection<Product> $emptyCollection */
		$emptyCollection = new EmptyCollection();

		$result = new self();
		$result->items = $emptyCollection;
		$result->count = 0;
		$result->totalCount = 0;
		return $result;
	}


	/**
	 * @param ICollection<Product>|ArrayList<Product|ProductVariant> $items
	 */
	public static function from(
		ICollection|ArrayList $items,
		int $count,
		int $totalCount,
	): self
	{
		$result = new self();
		$result->items = $items;
		$result->count = $count;
		$result->totalCount = $totalCount;
		return $result;
	}


	protected function getItemIds(): array
	{
		$ids = [];

		foreach ($this->items as $item) {
			$ids[] = $item->id;
		}

		return $ids;
	}


	/**
	 * @return ICollection<Product>|ArrayList<Product|ProductVariant>
	 */
	protected function getItems(): ICollection|ArrayList
	{
		return $this->items;
	}


	protected function getCount(): int
	{
		return $this->count;
	}


	protected function getTotalCount(): int
	{
		return $this->totalCount;
	}

}
