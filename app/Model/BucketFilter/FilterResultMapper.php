<?php declare(strict_types = 1);

namespace App\Model\BucketFilter;

use App\Model\BucketFilter\Box\CheckBoxes;
use App\Model\BucketFilter\Box\Slider;
use App\Model\BucketFilter\ElasticItem\BoolValue;
use App\Model\BucketFilter\ElasticItem\DiscreteValues;
use App\Model\BucketFilter\ElasticItem\ElasticItem;
use App\Model\BucketFilter\ElasticItem\LastMinute;
use App\Model\BucketFilter\ElasticItem\MultiValue;
use App\Model\BucketFilter\ElasticItem\Range;
use App\Model\BucketFilter\ElasticItem\SingleValue;
use App\Model\BucketFilter\SetupCreator\FilterFlagsConfig;
use Elastica\ResultSet;
use stdClass;

final class FilterResultMapper
{

	public function convert(array $filterBoxes, array $selectedParameters, array $filterItems, ?ResultSet $emptyCountRes, ?ResultSet $countRes): stdClass
	{
		$ret = new stdClass();
		$ret->boxes = [];
		$ret->boxesByNamespace = [];
		$ret->openedByUser = [];
		$ret->opened = [];
		$ret->categories = [];
		$ret->searchValue = $selectedParameters['searchValue'] ?? [];
		$ret->mainCategories = [];

		$ret->nonRoot = (
			isset($selectedParameters['dials']) && $selectedParameters['dials']
			|| isset($selectedParameters['flags']) && $selectedParameters['flags']
			|| isset($selectedParameters['ranges']) && $selectedParameters['ranges']
		);

		$ret->followingCleanFilterParameters = $selectedParameters;
		unset($ret->followingCleanFilterParameters['dials']);
		unset($ret->followingCleanFilterParameters['flags']);
		unset($ret->followingCleanFilterParameters['ranges']);
		unset($ret->followingCleanFilterParameters['searchValue']);

		if ($emptyCountRes === null) {
			return $ret;
		}

		foreach ($filterItems as $filterItem) {
			if (isset($filterBoxes[$filterItem->getElasticKey()])) {
				$box = $filterBoxes[$filterItem->getElasticKey()];

				$namespace = $filterItem->getElasticKey();
				// @phpstan-ignore-next-line
				if ($box->namespace === DiscreteValues::NAMESPACE_FLAGS && !FilterFlagsConfig::NAMESPACE_FLAGS_SEPARATE) {
					$namespace = DiscreteValues::NAMESPACE_FLAGS;
				}

				if ($filterItem instanceof SingleValue) { // or BoolValue
					$ret->boxes[] = $ret->boxesByNamespace[$namespace][] = $this->getSimpleValueData($box, $selectedParameters, $filterItem, $emptyCountRes, $countRes);
				} elseif ($filterItem instanceof MultiValue) {
					$ret->boxes[] = $ret->boxesByNamespace[$namespace][] = $this->getMultiValueData($box, $selectedParameters, $filterItem, $emptyCountRes, $countRes);
				} elseif ($filterItem instanceof Range) {
					$ret->boxes[] = $ret->boxesByNamespace[$namespace][] = $this->getRangeData($box, $selectedParameters, $filterItem, $emptyCountRes, $countRes);
				} elseif ($filterItem instanceof LastMinute) {
					$lastMinuteCheckBox = $this->getLastMinuteData($box, $selectedParameters, $filterItem, $emptyCountRes, $countRes);
					if ($lastMinuteCheckBox !== null) {
						$ret->boxes[] = $ret->boxesByNamespace[$namespace][] = $lastMinuteCheckBox;
					}
				}

			}
		}

		foreach ($ret->boxes as $box) {
			if ($box->isOpen()) {
				$ret->isOpen[] = $box->name;
			}
		}

		if (isset($emptyCountRes->getAggregations()['mainCategories_agg'])) {
			$agg = $emptyCountRes->getAggregation('mainCategories_agg');
			if (isset($agg['categories'])) {
				foreach ($agg['categories']['buckets'] as $bucket){
					$ret->mainCategories[$bucket['key']] = $bucket['doc_count'];
				}
			}
		}

		if (isset($emptyCountRes->getAggregations()['topCategories_agg'])) {
			$agg = $emptyCountRes->getAggregation('topCategories_agg');
			if (isset($agg['categoriesNestedAgg']['categoriesAgg']['categoriesAgg']['buckets'])) {
				foreach ($agg['categoriesNestedAgg']['categoriesAgg']['categoriesAgg']['buckets'] as $bucket){
					$ret->topCategories[$bucket['key']] = $bucket['doc_count'];
				}
			}
		}


		return $ret;
	}


	private function getSimpleValueData(CheckBoxes $box, array $allSelectedValues, SingleValue $filterItem, ResultSet $emptyCountRes, ?ResultSet $countRes): CheckBoxes
	{
		$bucketValues = $this->mapBucketToValues($filterItem, $emptyCountRes, $countRes);
		$box->populate($filterItem->getElasticKey(), $filterItem->getSelectedValues(), $allSelectedValues, $bucketValues);
		return $box;
	}

	private function getMultiValueData(CheckBoxes $box, array $allSelectedValues, MultiValue $filterItem, ResultSet $emptyCountRes, ?ResultSet $countRes): CheckBoxes
	{
		$bucketValues = $this->mapBucketToValuesForMultiselect($filterItem, $emptyCountRes, $countRes);
		$box->populate($filterItem->getElasticKey(), $filterItem->getSelectedValues(), $allSelectedValues, $bucketValues);
		return $box;
	}

	private function getRangeData(Slider $box, array $allSelectedValues, Range $filterItem, ResultSet $emptyCountRes, ?ResultSet $countRes): Slider
	{
		$bucketValues = $this->mapBucketToValuesForRange($filterItem, $emptyCountRes, $countRes);
		$box->populate($filterItem->getElasticKey(), $bucketValues, $filterItem->getSelectedValues(), $allSelectedValues);
		return $box;
	}



	private function mapBucketToValues(SingleValue $filterItem, ResultSet $emptyCountRes, ?ResultSet $countRes = null): array
	{
		$emptyCounts = $this->parseBucketsToCounts($emptyCountRes, $filterItem);
		$counts = $emptyCounts;

		if ($countRes !== null) {
			$presentCounts = $this->parseBucketsToCounts($countRes, $filterItem);
			foreach ($counts as $key=>$count) {
				if (isset($presentCounts[$key])) {
					$counts[$key] = $presentCounts[$key];
				} else {
					$counts[$key] = 0;
				}
			}
		}

		return $counts;
	}


	private function mapBucketToValuesForMultiselect(MultiValue $filterItem, ResultSet $emptyCountRes, ?ResultSet $countRes): array
	{
		$emptyCounts = $this->parseBucketsToCounts($emptyCountRes, $filterItem);
		$counts = $emptyCounts;

		if ($countRes !== null) {
			$presentCounts = $this->parseBucketsToCounts($countRes, $filterItem);
			foreach ($counts as $key=>$count) {
				if (isset($presentCounts[$key])) {
					$counts[$key] = $presentCounts[$key];
				} else {
					$counts[$key] = 0;
				}
			}
		}

		return $counts;
	}

	private function mapBucketToValuesForRange(Range $filterItem, ResultSet $emptyCountRes, ?ResultSet $countRes): array
	{
		$emptyAgg = $emptyCountRes->getAggregation($filterItem->getAggregationName());
		if ($countRes) {
			$agg = $countRes->getAggregation($filterItem->getAggregationName());
		} else {
			$agg = $emptyAgg;
		}

		$min = null;
		$max = null;
		if (isset($agg['min']['value'])) {
			$min = $agg['min']['value'];
		}

		if (isset($agg['max']['value'])) {
			$max = $agg['max']['value'];
		}

		if ($min !== null) {
			$min = floor($min);
		}

		if ($max !== null) {
			$max = ceil($max);
		}

		return [
			'min' => $min,
			'max' => $max,
		];
	}


	public function parseBucketsToCounts(ResultSet $emptyCountRes, DiscreteValues $filterItem): mixed
	{
		$counts = [];
		$buckets = [];
		if (isset($emptyCountRes->getAggregation($filterItem->getAggregationName())[$filterItem->getAggregationName()]['sub_' . $filterItem->getAggregationName()]['buckets'])) {
			// nested data
			$buckets = $emptyCountRes->getAggregation($filterItem->getAggregationName())[$filterItem->getAggregationName()]['sub_' . $filterItem->getAggregationName()]['buckets'];
		} elseif ($emptyCountRes->getAggregation($filterItem->getAggregationName())[$filterItem->getElasticKey()]['buckets']) {
			$buckets = $emptyCountRes->getAggregation($filterItem->getAggregationName())[$filterItem->getElasticKey()]['buckets'];
		}

		foreach ($buckets as $bucketRow) {
			$counts[$bucketRow['key']] = $bucketRow['doc_count'];
		}

		return $counts;
	}

	private function getLastMinuteData(CheckBoxes $box, array $allSelectedValues, LastMinute $filterItem, ResultSet $emptyCountRes, ?ResultSet $countRes): ?CheckBoxes
	{
		$getCount = function (ResultSet $res) {
			$aggregations = $res->getAggregations();
			if (isset($aggregations['itemsWithLastMinute_agg']['lastMinute_agg']['parentData_agg']['doc_count'])) {
				return $aggregations['itemsWithLastMinute_agg']['lastMinute_agg']['parentData_agg']['doc_count'];
			}
			return 0;
		};
		$countForClearFilter = $getCount($emptyCountRes);
		$counts  = [1 => $countForClearFilter];

		if ($countForClearFilter === 0) {
			return null;
		}

		if ($countRes !== null) {
			$counts  = [1 => $getCount($countRes)];
		}

		$box->populate($filterItem->getElasticKey(), $filterItem->getSelectedValues(), $allSelectedValues, $counts);
		return $box;
	}

}
