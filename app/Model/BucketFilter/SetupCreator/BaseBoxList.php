<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\SetupCreator;

use App\Model\BucketFilter\Box\CheckBoxes;
use App\Model\BucketFilter\Box\Slider;
use App\Model\BucketFilter\CatalogParameter;
use App\Model\BucketFilter\ElasticItem\DiscreteValues;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ParameterValue\ParameterValueFilterHelper;
use App\Model\Orm\Routable;
use App\Model\Orm\RoutableEntity;
use App\Model\Orm\Traits\HasCache;
use App\PostType\Page\Model\Orm\Tree;
use Closure;
use stdClass;

abstract class BaseBoxList implements BoxListGenerator
{
	use HasCache;

	protected array $list = [];
	protected array $itemsToSkip = [];
	protected ?stdClass $cfSetup = null;
	protected Routable $parameterSetupObject;

	public function __construct(
		protected readonly RoutableEntity $parameterObject,
		protected readonly FilterFlagsConfig $filterFlagsConfig,
		protected readonly CatalogParameter $catalogParameter,
		protected readonly ParameterValueFilterHelper $parameterValueFilterHelper,
		protected readonly array $allSelectedParameters,
	)
	{
		$this->parameterSetupObject = $this->parameterObject;
		$this->init();
	}

	protected function init():void
	{

	}

	public function getBoxList(): array
	{
		$this->createDefaultBoxes($this->cfSetup);
		$this->createCustomBoxes();

		return $this->list;
	}

	protected function add(string $key, mixed $item): void
	{
		$this->list[$key] = $item;
	}

	protected function createDefaultBoxes(?stdClass $cfSetup = null):void
	{
		$this->list = [];

		$forceOpenFlags = $forceOpenPrice = false;
		$hideFlags = $hidePrice = false;
		if(isset($cfSetup)){
			$forceOpenFlags = $cfSetup->specialFilters->showOnly->opened ?? false;
			$hideFlags = $cfSetup->specialFilters->showOnly->hide ?? false;
			$forceOpenPrice = $cfSetup->specialFilters->price->opened ?? false;
			$hidePrice = $cfSetup->specialFilters->price->hide ?? false;
		}


		if (!$hideFlags) {
			foreach ($this->filterFlagsConfig->getProductFlags() as $flag => $callback){
				if (in_array($flag, $this->itemsToSkip)) {
					continue;
				}
				$item = new CheckBoxes(
					namespace: DiscreteValues::NAMESPACE_FLAGS,
					getOptionFunction: $this->filterFlagsConfig->getFunctionForValues($flag),
					title: 'title_' . $flag,
					unit: $this->filterFlagsConfig->getUnit($flag),
					description: '',
					visibleValuesCount: 1000,
					forceOpen: $forceOpenFlags,
					indexable: true,
				);

				$this->add($flag, $item);
			}
		}

		if (!$hidePrice) {
			foreach ($this->filterFlagsConfig->getProductSliders() as $slider => $callback){
				if (in_array($slider, $this->itemsToSkip)) {
					continue;
				}

				$item =  new Slider(
					title: 'filter_title_' . $slider,
					unit: $this->filterFlagsConfig->getUnit($slider),
					steps: 20,
					forceOpen: $forceOpenPrice,
					formatter: fn(int|float $value) => number_format($value, 0,',', ' '),
				);

				$this->add($slider, $item);
			}
		}
	}

	abstract protected function createCustomBoxes():void;

	public function getBoxesByCfSetup(): array
	{
		return $this->loadCache($this->createCacheKey('cfSetup'), function () {
			$cfSetup = $this->cfSetup;

			$visibleCounts = [];
			$isForceOpen = [];
			$showMoreLinks = [];
			$numericAsSliderIds = [];
			$indexable = [];
			$limit = [];
			$tooltip = [];
			if ($cfSetup && isset($cfSetup->visibleParameters)) {
				foreach ($cfSetup->visibleParameters as $visibleParameter) {
					if (isset($visibleParameter->parameter) && $visibleParameter->parameter->getEntity()) {
						if (isset($visibleParameter->numberAsRange) && $visibleParameter->numberAsRange) {
							$numericAsSliderIds[] = $visibleParameter->parameter->id;
						}

						$indexable[$visibleParameter->parameter->id] = $visibleParameter->indexable ?? false;
						$visibleCounts[$visibleParameter->parameter->id] = isset($visibleParameter->visibleCount) ? (int) $visibleParameter->visibleCount : null;
						$isForceOpen[$visibleParameter->parameter->id] = $visibleParameter->opened ?? false;
						$showMoreLinks[$visibleParameter->parameter->id] = isset($visibleParameter->link) ? $visibleParameter->link->getEntity() : null;
						$limit[$visibleParameter->parameter->id] = (int) ($visibleParameter->visibleCount ?? 40);
						$tooltip[$visibleParameter->parameter->id] = ($visibleParameter->tooltip ?? '');
					}
				}
			}

			$boxes = [];

			$esParameters = $this->catalogParameter->getPossibleParametersForCatalog($this->parameterSetupObject);
			foreach ($esParameters as $esParameter) {
				$showMoreLink = (isset($showMoreLinks[$esParameter->id]) && $showMoreLinks[$esParameter->id]) ? $showMoreLinks[$esParameter->id] : null;
				if ($esParameter->type === Parameter::TYPE_MULTISELECT) {
					$boxes[$esParameter->uid] = new CheckBoxes(
						namespace: DiscreteValues::NAMESPACE_DIALS,
						getOptionFunction: $this->parameterValueFilterHelper->getFunctionForValues($esParameter, $showMoreLink !== null ? $limit[$esParameter->id] : null), // ak ma parameter odkaz na iny postType, zobrazit len 40 vo filtry
						title: $esParameter->title,
						unit: $esParameter->unit,
						description: $tooltip[$esParameter->id],
						visibleValuesCount: $visibleCounts[$esParameter->id] ?? null,
						forceOpen: $isForceOpen[$esParameter->id] ?? false,
						isMultiselect: true,
						showMoreLink: $showMoreLink,
						search: isset($this->allSelectedParameters[DiscreteValues::NAMESPACE_SEARCH][$esParameter->uid]) ? $this->allSelectedParameters[DiscreteValues::NAMESPACE_SEARCH][$esParameter->uid] : '',
						indexable: $indexable[$esParameter->id] ?? false,
						onlyNonZeroValues: false,
					);
				} elseif ($esParameter->type === Parameter::TYPE_SELECT) {
					$boxes[$esParameter->uid] = new CheckBoxes(
						namespace: DiscreteValues::NAMESPACE_DIALS,
						getOptionFunction: $this->parameterValueFilterHelper->getFunctionForValues($esParameter),
						title: $esParameter->title,
						unit: $esParameter->unit,
						description: $tooltip[$esParameter->id],
						visibleValuesCount: $visibleCounts[$esParameter->id] ?? null,
						forceOpen: $isForceOpen[$esParameter->id] ?? false,
						showMoreLink: $showMoreLink,
						search: isset($this->allSelectedParameters[DiscreteValues::NAMESPACE_SEARCH][$esParameter->uid]) ? $this->allSelectedParameters[DiscreteValues::NAMESPACE_SEARCH][$esParameter->uid] : '',
						indexable: $indexable[$esParameter->id] ?? false,
						onlyNonZeroValues: false,

					);
				} elseif ($esParameter->type === Parameter::TYPE_NUMBER) {
					if (in_array($esParameter->id, $numericAsSliderIds)) {
						// add range for numbers
						$boxes[$esParameter->uid] = new Slider(
							title: $esParameter->title,
							filterPrefix: $esParameter->filterPrefix ?? '',
							filterSuffix: $esParameter->filterPostfix ?? '',
							unit: $esParameter->unit,
							steps: 20,
							forceOpen: $isForceOpen[$esParameter->id] ?? false,
							indexable: $indexable[$esParameter->id] ?? false,
						);
					} else {
						$boxes[$esParameter->uid] = new CheckBoxes(
							namespace: DiscreteValues::NAMESPACE_DIALS,
							getOptionFunction: $this->parameterValueFilterHelper->getFunctionForValues($esParameter),
							title: $esParameter->title,
							unit: $esParameter->unit,
							description: $tooltip[$esParameter->id],
							visibleValuesCount: $visibleCounts[$esParameter->id] ?? null,
							forceOpen: $isForceOpen[$esParameter->id] ?? false,
							showMoreLink: $showMoreLink,
							indexable: $indexable[$esParameter->id] ?? false,
						);
					}
				}
			}
			return $boxes;
		});
	}

	public function setItemsToSkip(?array $itemsToSkip = []): void
	{
		$this->itemsToSkip = $itemsToSkip;
	}

}
