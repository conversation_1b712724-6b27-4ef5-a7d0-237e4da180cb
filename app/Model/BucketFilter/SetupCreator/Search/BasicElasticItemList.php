<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\SetupCreator\Search;

use App\Model\BucketFilter\ElasticItem\Fulltext;
use App\Model\BucketFilter\ElasticItem\IsInPath;
use App\Model\BucketFilter\ElasticItem\IsNotOld;
use App\Model\BucketFilter\ElasticItem\IsPublic;
use App\Model\BucketFilter\SetupCreator\BasicElasticItemListGenerator;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\Model\Pages;

class BasicElasticItemList implements BasicElasticItemListGenerator
{

	private array $list;

	public function __construct(
		private readonly Pages $pages,
		private readonly string $stringToSearch,
		private readonly array $forcedIds,
	)
	{
	}


	public function getBasicElasticItemList(): array
	{
		if (!isset($this->list)) {
			$baseFilterItems = [];
			$baseFilterItems[] = new IsInPath($this->pages->eshop);
			$baseFilterItems[] = new IsPublic();
			$baseFilterItems[] = new Fulltext($this->stringToSearch, $this->forcedIds);
			$baseFilterItems[] = new IsNotOld();

			$this->list = $baseFilterItems;
		}

		return $this->list;
	}

}
