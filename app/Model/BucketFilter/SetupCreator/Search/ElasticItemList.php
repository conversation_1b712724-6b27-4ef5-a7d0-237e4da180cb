<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\SetupCreator\Search;

use App\Model\BucketFilter\SetupCreator\BaseElasticItemList;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Page\Model\Orm\Tree;

/**
 * @property-read CommonTree $parameterObject
 */
class ElasticItemList extends BaseElasticItemList
{
	protected function init(): void
	{
		$this->parameterSetupObject = $this->parameterObject;
	}

	protected function createCustomList(): void
	{
		foreach ($this->getItemsForSearch() as $item){
			$this->add($item);
		}
	}

	private function getItemsForSearch(): array
	{
		return $this->getItemsByCfSetup();
	}

}
