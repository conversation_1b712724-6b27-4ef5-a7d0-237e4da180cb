<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\SetupCreator\Search;

use App\Model\BucketFilter\SetupCreator\BaseBoxList;
use App\PostType\Page\Model\Orm\CommonTree;

/**
 * @property CommonTree $parameterObject
 */
class BoxList extends BaseBoxList
{

	protected function init(): void
	{
		$this->parameterSetupObject = $this->parameterObject;
		$this->cfSetup = $this->catalogParameter->getParametersCfForFilter($this->parameterSetupObject);
	}

	protected function createCustomBoxes(): void
	{
		foreach ($this->getBoxesForPublisher() as $key => $item )
		{
			$this->add($key, $item);
		}
	}

	private function getBoxesForPublisher(): array
	{
		return $this->getBoxesByCfSetup();
	}

}
