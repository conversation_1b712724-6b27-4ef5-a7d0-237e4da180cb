<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\SetupCreator\Blog;

use App\Model\BucketFilter\ElasticItem\IsInPath;
use App\Model\BucketFilter\ElasticItem\IsNotOld;
use App\Model\BucketFilter\ElasticItem\IsPublic;
use App\Model\BucketFilter\SetupCreator\BasicElasticItemListGenerator;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\CommonTree;

class BasicElasticItemList implements BasicElasticItemListGenerator
{

	private array $list;

	public function __construct(
		private readonly CommonTree $pathObject
	)
	{
	}


	public function getBasicElasticItemList(): array
	{
		if (!isset($this->list)) {
			$baseFilterItems = [];
			$baseFilterItems[] = new IsInPath($this->pathObject);
			$baseFilterItems[] = new IsPublic();
			//$baseFilterItems[] = new IsNotOld();

			$this->list = $baseFilterItems;
		}

		return $this->list;
	}

}
