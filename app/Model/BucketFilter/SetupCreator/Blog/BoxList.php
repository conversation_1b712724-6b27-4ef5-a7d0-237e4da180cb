<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\SetupCreator\Blog;


use App\Model\BucketFilter\Box\CheckBoxes;
use App\Model\BucketFilter\CatalogParameter;
use App\Model\BucketFilter\ElasticItem\DiscreteValues;
use App\Model\BucketFilter\ElasticItem\LastMinute;
use App\Model\BucketFilter\SetupCreator\BaseBoxList;
use App\Model\BucketFilter\SetupCreator\FilterFlagsConfig;
use App\Model\ElasticSearch\Product\Convertor\ClassData;
use App\Model\Orm\ParameterValue\ParameterValueFilterHelper;
use App\Model\Orm\RoutableEntity;
use App\Model\Orm\User\UserRepository;
use App\Model\TranslatorDB;
use App\PostType\Page\Model\Orm\Tree;
use stdClass;


/**
 * @property Tree $parameterObject
 */
class BoxList extends BaseBoxList
{

	public function __construct(
		RoutableEntity $parameterObject,
		FilterFlagsConfig $filterFlagsConfig,
		CatalogParameter $catalogParameter,
		ParameterValueFilterHelper $parameterValueFilterHelper,
		array $allSelectedParameters,
	)
	{
		parent::__construct($parameterObject, $filterFlagsConfig, $catalogParameter, $parameterValueFilterHelper, $allSelectedParameters);
	}

	protected function init(): void
	{
		$this->cfSetup = $this->catalogParameter->getParametersCfForFilter($this->parameterSetupObject);
	}

	protected function createCustomBoxes(): void
	{
		foreach ($this->getBoxesForCatalog() as $key => $item ) {
			$this->add($key, $item);
		}
	}

	private function getBoxesForCatalog(): array
	{
		return $this->getBoxesByCfSetup();
	}

}
