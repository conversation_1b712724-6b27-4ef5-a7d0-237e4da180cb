<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\SetupCreator\Blog;

use App\Model\BucketFilter\CatalogParameter;
use App\Model\BucketFilter\ElasticItem\DiscreteValues;
use App\Model\BucketFilter\ElasticItem\LastMinute;
use App\Model\BucketFilter\ElasticItem\MultiValue;
use App\Model\BucketFilter\QueryFilter;
use App\Model\BucketFilter\SetupCreator\BaseElasticItemList;
use App\Model\BucketFilter\SetupCreator\FilterFlagsConfig;
use App\Model\ElasticSearch\Product\Convertor\ClassData;
use App\Model\Orm\ParameterValue\ParameterValueFilterHelper;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Routable;
use App\Model\Orm\State\State;
use App\PostType\Page\Model\Orm\Tree;

/**
 * @property-read Tree $parameterObject
 */
class ElasticItemList extends BaseElasticItemList
{

	protected function createCustomList(): void
	{
		foreach ($this->getItemsForCatalog() as $item){
			$this->add($item);
		}
	}

	private function getItemsForCatalog(): array
	{
		return $this->getItemsByCfSetup();
	}

}
