<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\SetupCreator\Discount;


use App\Model\BucketFilter\ElasticItem\IsInDiscount;
use App\Model\BucketFilter\ElasticItem\IsNotOld;
use App\Model\BucketFilter\ElasticItem\IsPublic;
use App\Model\BucketFilter\QueryFilter;
use App\Model\BucketFilter\SetupCreator\BasicElasticItemListGenerator;
use App\PostType\Discount\Model\Orm\DiscountLocalization\DiscountLocalization;


class BasicElasticItemList implements BasicElasticItemListGenerator
{

	private array $list;

	public function __construct(
		private readonly DiscountLocalization $discountLocalization,
		private readonly QueryFilter $queryFilter,
		private readonly ?int $category = null,
	)
	{
	}


	public function getBasicElasticItemList(): array
	{
		if (!isset($this->list)) {
			$baseFilterItems = [];
			$baseFilterItems[] = new IsInDiscount($this->discountLocalization, $this->queryFilter, $this->category);
			$baseFilterItems[] = new IsPublic();
			$baseFilterItems[] = new IsNotOld();

			$this->list = $baseFilterItems;
		}

		return $this->list;
	}

}
