<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\SetupCreator\Discount;

use App\Model\BucketFilter\SetupCreator\BaseBoxList;
use App\PostType\Discount\Model\Orm\DiscountLocalization\DiscountLocalization;

/**
 * @property DiscountLocalization $parameterObject
 */
class BoxList extends BaseBoxList
{

	protected function init(): void
	{
		$this->parameterSetupObject = $this->parameterObject->mutation->pages->discount;
		$this->cfSetup = $this->catalogParameter->getParametersCfForFilter($this->parameterSetupObject);
	}

	protected function createCustomBoxes(): void
	{
		foreach ($this->getBoxesForDiscount() as $key => $item )
		{
			$this->add($key, $item);
		}
	}

	private function getBoxesForDiscount(): array
	{
		return $this->getBoxesByCfSetup();
	}

}
