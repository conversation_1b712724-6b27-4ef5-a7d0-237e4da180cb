<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\SetupCreator\Discount;

use App\Model\BucketFilter\SetupCreator\BaseElasticItemList;
use App\PostType\Discount\Model\Orm\DiscountLocalization\DiscountLocalization;

/**
 * @property-read DiscountLocalization $parameterObject
 */
class ElasticItemList extends BaseElasticItemList
{
	protected function init(): void
	{
		$this->parameterSetupObject = $this->parameterObject->mutation->pages->discount;
	}

	protected function createCustomList(): void
	{
		foreach ($this->getItemsForDiscount() as $item){
			$this->add($item);
		}
	}

	private function getItemsForDiscount(): array
	{
		return $this->getItemsByCfSetup();
	}

}
