<?php declare(strict_types=1);

namespace App\Model\BucketFilter\SetupCreator\Catalog;

use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;
use App\PostType\Page\Model\Orm\CatalogTree;

interface ElasticItemListFactory
{

	public function create(
		CatalogTree $parameterObject,
		State $currentState,
		PriceLevel $priceLevel,
		array $allSelectedParameters,
	): ElasticItemList;

}
