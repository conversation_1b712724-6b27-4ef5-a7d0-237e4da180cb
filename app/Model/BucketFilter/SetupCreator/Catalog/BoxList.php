<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\SetupCreator\Catalog;


use App\Model\BucketFilter\Box\CheckBoxes;
use App\Model\BucketFilter\CatalogParameter;
use App\Model\BucketFilter\ElasticItem\DiscreteValues;
use App\Model\BucketFilter\ElasticItem\LastMinute;
use App\Model\BucketFilter\SetupCreator\BaseBoxList;
use App\Model\BucketFilter\SetupCreator\FilterFlagsConfig;
use App\Model\ElasticSearch\Product\Convertor\ClassData;
use App\Model\Orm\ParameterValue\ParameterValueFilterHelper;
use App\Model\Orm\RoutableEntity;
use App\Model\Orm\User\UserRepository;
use App\Model\TranslatorDB;
use App\PostType\Page\Model\Orm\Tree;
use stdClass;


/**
 * @property Tree $parameterObject
 */
class BoxList extends BaseBoxList
{

	public function __construct(
		RoutableEntity $parameterObject,
		FilterFlagsConfig $filterFlagsConfig,
		CatalogParameter $catalogParameter,
		ParameterValueFilterHelper $parameterValueFilterHelper,
		array $allSelectedParameters,
		private readonly TranslatorDB $translator,
//		private readonly UserRepository $userRepository,
	)
	{
		parent::__construct($parameterObject, $filterFlagsConfig, $catalogParameter, $parameterValueFilterHelper, $allSelectedParameters);
	}

	protected function init(): void
	{
		$this->cfSetup = $this->catalogParameter->getParametersCfForFilter($this->parameterSetupObject);
	}

	protected function createCustomBoxes(): void
	{
		foreach ($this->getBoxesForCatalog() as $key => $item ) {
			$this->add($key, $item);
		}

//		$this->addLector();
		$this->addLastMinute();
	}

	private function getBoxesForCatalog(): array
	{
		return $this->getBoxesByCfSetup();
	}


//	private function addLector(): void
//	{
//
//		$this->add(
//			ClassData::CLASS_LECTORS,
//			new CheckBoxes(
//				namespace: DiscreteValues::NAMESPACE_DIALS,
//				getOptionFunction:  function () {
//					return $this->userRepository->findLectors()->fetchAll();
//				},
//				title: $this->translator->translate('filter_title_' . ClassData::CLASS_LECTORS),
//				unit: '',
//				description: '',
//				visibleValuesCount: 10,
//				forceOpen: false,
//				isMultiselect: true,
//				onlyNonZeroValues: false,
//				showMoreLink: null,
//				search: '',
//				indexable: false,
//			)
//		);
//	}

	private function addLastMinute(): void
	{
		$hideFlags = $this->parameterObject->cf->parameterForFilter->specialFilters->showOnly->hide ?? false;
		if ($hideFlags) {
			return;
		}
		$this->add(
			LastMinute::CLASS_EVENTS,
			new CheckBoxes(
				namespace: DiscreteValues::NAMESPACE_FLAGS,
				getOptionFunction:  function () {
					return [1 => (object) ['id' => 1, 'name' => $this->translator->translate('Poslední šance')]];
				},
				title: 'filter_title_' . LastMinute::CLASS_EVENTS,
				unit: '',
				description: '',
				visibleValuesCount: 10,
				forceOpen: false,
				isMultiselect: true,
				onlyNonZeroValues: false,
				showMoreLink: null,
				search: '',
				indexable: false,
			)
		);
	}

}
