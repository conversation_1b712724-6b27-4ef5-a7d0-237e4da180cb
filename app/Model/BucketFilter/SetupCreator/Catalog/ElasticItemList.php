<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\SetupCreator\Catalog;

use App\Model\BucketFilter\CatalogParameter;
use App\Model\BucketFilter\ElasticItem\DiscreteValues;
use App\Model\BucketFilter\ElasticItem\LastMinute;
use App\Model\BucketFilter\ElasticItem\MultiValue;
use App\Model\BucketFilter\QueryFilter;
use App\Model\BucketFilter\SetupCreator\BaseElasticItemList;
use App\Model\BucketFilter\SetupCreator\FilterFlagsConfig;
use App\Model\ElasticSearch\Product\Convertor\ClassData;
use App\Model\Orm\ParameterValue\ParameterValueFilterHelper;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Routable;
use App\Model\Orm\State\State;
use App\PostType\Page\Model\Orm\Tree;

/**
 * @property-read Tree $parameterObject
 */
class ElasticItemList extends BaseElasticItemList
{

	protected function createCustomList(): void
	{
		foreach ($this->getItemsForCatalog() as $item){
			$this->add($item);
		}

		$this->add($this->getLectorFilter());
		$this->add($this->getLastMinuteFilter());
	}

	private function getItemsForCatalog(): array
	{
		return $this->getItemsByCfSetup();
	}

	private function getLectorFilter(): MultiValue
	{
		return new MultiValue(
			elasticKey: ClassData::CLASS_LECTORS,
			selectedValues: $this->allSelectedParameters[DiscreteValues::NAMESPACE_DIALS][ClassData::CLASS_LECTORS] ?? [],
			getOptionFunction: fn(): array => [],
			queryFilter: $this->queryFilter,
			typeInElasticEnforcer: fn($value) => (int) $value,
		);
	}

	private function getLastMinuteFilter(): LastMinute
	{
		return new LastMinute(
			queryFilter: $this->queryFilter,
			currentDateTimeProvider: $this->currentDateTimeProvider,
			selectedValues: $this->allSelectedParameters[DiscreteValues::NAMESPACE_FLAGS][LastMinute::CLASS_EVENTS] ?? [],
		);
	}

}
