<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\SetupCreator\Catalog;

use App\Model\BucketFilter\ElasticItem\IsClass;
use App\Model\BucketFilter\ElasticItem\IsInPath;
use App\Model\BucketFilter\ElasticItem\IsNotOld;
use App\Model\BucketFilter\ElasticItem\IsProduct;
use App\Model\BucketFilter\ElasticItem\IsPublic;
use App\Model\BucketFilter\ElasticItem\IsTypeNotForSale;
use App\Model\BucketFilter\SetupCreator\BasicElasticItemListGenerator;
use App\PostType\Page\Model\Orm\CatalogTree;

class BasicElasticItemList implements BasicElasticItemListGenerator
{

	private array $list;

	public function __construct(
		private readonly CatalogTree $pathObject
	)
	{
	}


	public function getBasicElasticItemList(): array
	{
		if (!isset($this->list)) {
			$baseFilterItems = [];
			$baseFilterItems[] = new IsInPath($this->pathObject);
			$baseFilterItems[] = new IsPublic();
			$baseFilterItems[] = new IsNotOld();
			$baseFilterItems[] = new IsTypeNotForSale();

			if ($this->pathObject->hasCourses) {
				$baseFilterItems[] = new IsClass();
			} else {
				// products
				$baseFilterItems[] = new IsProduct();
			}

			$this->list = $baseFilterItems;
		}

		return $this->list;
	}

}
