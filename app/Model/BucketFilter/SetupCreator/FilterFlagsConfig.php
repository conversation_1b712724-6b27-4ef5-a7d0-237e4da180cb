<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\SetupCreator;


use App\Infrastructure\Latte\Filters;
use App\Model\BucketFilter\ElasticItem\ElasticItem;
use App\Model\BucketFilter\ElasticItem\LastMinute;
use App\Model\Currency\CurrencyHelper;
use App\Model\ElasticSearch\Product\Convertor\FlagData;
use App\Model\TranslatorDB;
use Closure;
use Elastica\Query\BoolQuery;
use Elastica\QueryBuilder;
use stdClass;

final class FilterFlagsConfig
{
	public const NAMESPACE_FLAGS_SEPARATE = false;
	public const URL_PLACEHOLDERS = [
		'novinka' => 'isNew',
		'bestseller' => 'isBestseller',
		'top' => 'isTop',
		'predobjednavka' => 'isInPrepare',
		'sleva' => 'isInDiscount',
		'k-odeslani' => 'isInStore',
		'odeslani-za-5-dnu' => 'isInSupplier',
		'poskozeno' => 'isDamaged',
		'last-minute' => LastMinute::CLASS_EVENTS,
	];
	private array $productFlags = [
		FlagData::IS_NEW => null,
//		'isInPrepare' => null,
//		'isTop' => null,
//		'isBestseller' => null,
//		'isInDiscount' => null,
//		'isInStore' => null,
//		'isInSupplier' => null,
//		'isDamaged' => null,
	];
	private array $productSliders = ['price' => null];

	private array $units = [];

	public function __construct(
		private readonly TranslatorDB $translator
	)
	{
	}

	public function getProductFlags(): array
	{
		return $this->productFlags;
	}

	public function getProductSliders(): array
	{
		return $this->productSliders;
	}
	/**
	 *
	 * @phpstan-param  Closure(ElasticItem $item): (void) $callback
	 */
	public function setProductFlagCallback(string $key, Closure $callback): void
	{
		$this->productFlags[$key] = $callback;
	}

	/**
	 *
	 * @phpstan-param  Closure(ElasticItem $item): (void) $callback
	 */
	public function setProductSliderCallback(string $key, Closure $callback): void
	{
		$this->productSliders[$key] = $callback;
	}

	/**
	 * @phpstan-return Closure(): stdClass[]
	 */
	public function getFunctionForValues(string $flag): Closure
	{
		return function () use($flag) {
			$ret = [];
			$retItem = new stdClass(); // only items in store
			$retItem->id = 1;
			$retItem->name = $this->translator->translate('value_'.$flag);
			$retItem->filterName = ($trans = $this->translator->translate( 'filter_value_'.$flag)) !== 'filter_value_'.$flag && $trans !== '' ? $trans : $retItem->name;
			$retItem->filterPrefix = ($trans = $this->translator->translate('filter_value_prefix_'.$flag)) !== 'filter_value_prefix_'.$flag ? $trans : '';
			$retItem->filterSuffix = ($trans = $this->translator->translate('filter_value_postfix_'.$flag)) !== 'filter_value_postfix_'.$flag ? $trans : '';

			$ret[] = $retItem;

			return $ret;
		};
	}

	public function getUnit(string $key): string
	{
		$this->units['price'] = Filters::formatCurrency(CurrencyHelper::getCurrency());

		return $this->units[$key] ?? '';
	}

	public function getConditionMust(string $name): ?BoolQuery
	{
		if ($name === 'price') {
			return $this->getPriceBaseCondition();
		}

		return null;
	}

	public function getAggregationFilterMust(string $name): ?BoolQuery
	{
		if ($name === 'price') {
			return $this->getPriceBaseCondition();
		}
		return null;
	}

	private function getPriceBaseCondition(): BoolQuery
	{
		$b = new QueryBuilder();
		return $b->query()->bool()->addMust(
			$b->query()->term(['showInPriceFilter' => true])
		);
	}

}
