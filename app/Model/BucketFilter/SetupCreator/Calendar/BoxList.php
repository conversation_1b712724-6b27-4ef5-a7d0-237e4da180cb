<?php declare(strict_types=1);

namespace App\Model\BucketFilter\SetupCreator\Calendar;

use App\Model\BucketFilter\Box\CheckBoxes;
use App\Model\BucketFilter\ElasticItem\DiscreteValues;
use App\Model\BucketFilter\SetupCreator\BoxListGenerator;
use App\Model\Orm\Mutation\Mutation;
use App\Model\TranslatorDB;
use App\PostType\CalendarTag\Model\Orm\CalendarTag\CalendarTagLocalizationRepository;
use App\PostType\Faculty\Model\Orm\FacultyLocalizationRepository;

class BoxList implements BoxListGenerator
{

	private array $list;

	public function __construct(
		private readonly Mutation $mutation,
		private readonly TranslatorDB $translator,
		private readonly CalendarTagLocalizationRepository $calendarTagLocalizationRepository,
	)
	{
	}

	public function getBoxList(): array
	{
		if (!isset($this->list)) {
			$this->list = $this->getBoxesForCatalog();
		}

		return $this->list;
	}


	private function getBoxesForCatalog(): array
	{
		$boxes = [];

		$boxes['tags'] = new CheckBoxes(
			namespace: DiscreteValues::NAMESPACE_DIALS,
			getOptionFunction: fn(): array => $this->calendarTagLocalizationRepository->findForSelectList($this->mutation),
			title: $this->translator->translate('form_label_tags'),
			visibleValuesCount: 20,
			isMultiselect: true,
		);

		return $boxes;
	}

}
