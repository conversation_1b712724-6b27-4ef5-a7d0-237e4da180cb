<?php declare(strict_types=1);

namespace App\Model\BucketFilter\SetupCreator\Calendar;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\ProductLocalization\ProductLocalizationRepository;
use Closure;
use stdClass;

class ProductOptionsHelper
{

	public function __construct(
		private readonly Mutation $mutation,
		private readonly ProductLocalizationRepository $productLocalizationRepository,
	) {}

	/**
	 * @phpstan-return Closure(): stdClass[]
	 */
	public function getFunctionForValuesOfProducts(): Closure
	{
		return function () {
			$ret = [];
			$items = $this->productLocalizationRepository->findBy(['mutation' => $this->mutation])->orderBy('name');
			foreach ($items as $item) {
				assert($item instanceof ProductLocalization);
				$retItem = new stdClass();
				$retItem->id = $item->product->id;
				$retItem->name = $item->name;
				$ret[] = $retItem;
			}

			return $ret;
		};
	}
}
