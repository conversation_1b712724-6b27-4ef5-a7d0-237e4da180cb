<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\SetupCreator\Calendar;

use App\Model\BucketFilter\ElasticItem\DatesRangeOverlap;
use App\Model\BucketFilter\ElasticItem\Fulltext;
use App\Model\BucketFilter\ElasticItem\IsPublic;
use App\Model\BucketFilter\ElasticItem\IsCalendar;
use App\Model\BucketFilter\SetupCreator\BasicElasticItemListGenerator;
use DateTimeInterface;

class BasicElasticItemList implements BasicElasticItemListGenerator
{

	private array $list;

	public function __construct(
		readonly private ?string $stringToSearch,
		readonly private ?DateTimeInterface $dateFrom,
		readonly private ?DateTimeInterface $dateTo,
	)
	{
	}

	public function getBasicElasticItemList(): array
	{
		if (!isset($this->list)) {
			$baseFilterItems = [];
			$baseFilterItems[] = new IsPublic();
			$baseFilterItems[] = new IsCalendar();

			if ($this->stringToSearch) {
				$baseFilterItems[] = new Fulltext($this->stringToSearch);
			}

			$baseFilterItems[] = new DatesRangeOverlap(
				fieldFrom: 'eventFrom',
				fieldTo: 'eventTo',
				from: $this->dateFrom,
				to: $this->dateTo
			);

			$this->list = $baseFilterItems;
		}
		return $this->list;
	}

}
