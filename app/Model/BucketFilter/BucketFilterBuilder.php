<?php

declare(strict_types=1);

namespace App\Model\BucketFilter;


use App\Model\BucketFilter\SetupCreator\BaseBoxList;
use App\Model\BucketFilter\SetupCreator\Catalog;
use App\Model\BucketFilter\SetupCreator\Blog;
use App\Model\BucketFilter\SetupCreator\Calendar;
use App\Model\BucketFilter\SetupCreator\Search;
use App\Model\BucketFilter\SetupCreator\Discount;
use App\Model\BucketFilter\SetupCreator\FilterFlagsConfig;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Alias\Alias;
use App\Model\Orm\EsIndex\EsIndex;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\RoutableEntity;
use App\Model\Orm\State\State;
use App\Model\Pages;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\Discount\Model\Orm\DiscountLocalization\DiscountLocalization;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Page\Model\Orm\Tree;
use Nette\Application\BadRequestException;
use Nette\Application\UI\Presenter;
use Nette\DI\Container;
use Nette\InvalidArgumentException;
use stdClass;

final class BucketFilterBuilder
{
	public const string TYPE_CATALOG = 'catalog';
	public const string TYPE_DISCOUNT = 'discount';
	public  const string TYPE_SEARCH = 'search';
	public const string TYPE_BLOG = 'blog';
	public const string TYPE_CALENDAR = 'calendar';

	const array GENERATORS = [
		self::TYPE_CATALOG => [
			Catalog\BasicElasticItemListFactory::class,
			Catalog\ElasticItemListFactory::class,
			Catalog\BoxListFactory::class
		],

		self::TYPE_BLOG => [
			Blog\BasicElasticItemListFactory::class,
			Blog\ElasticItemListFactory::class,
			Blog\BoxListFactory::class
		],

		self::TYPE_CALENDAR => [
			Calendar\BasicElasticItemListFactory::class,
			Calendar\ElasticItemListFactory::class,
			Calendar\BoxListFactory::class
		],

		self::TYPE_DISCOUNT => [
			Discount\BasicElasticItemListFactory::class,
			Discount\ElasticItemListFactory::class,
			Discount\BoxListFactory::class,
		],
		self::TYPE_SEARCH => [
			Search\BasicElasticItemListFactory::class,
			Search\ElasticItemListFactory::class,
			Search\BoxListFactory::class,
		],

	];

	private ?string $type = null;

	private ?RoutableEntity $object = null;
	private ?BucketFilter $bucketFilter = null;
	private Mutation $mutation;
	private array $parameters = [];

	private ?int $category = null;

	public function __construct(
		private readonly FilterFlagsConfig $elasticItemConfig,
		private readonly MutationsHolder $mutationsHolder,
		private readonly BucketFilterFactory $bucketFilterFactory,
		private readonly SortCreator $sortCreator,
		private readonly Container $container,
		private readonly Orm $orm,
		private readonly Presenter $presenter,
		RoutableEntity $object,
		private readonly State $currentState,
		private readonly PriceLevel $priceLevel,

		private readonly string $sortOrder,
		private readonly array $allSelectedParameters = [],
		private readonly ?array $itemsToSkip = [],
	) {
		$this->mutation = $this->mutationsHolder->getDefault();

		$this->setObject($object);

		$this->setParameter($this->elasticItemConfig, 'elasticItemConfig');
		$this->setParameter($this->currentState, 'currentState');
		$this->setParameter($this->priceLevel, 'priceLevel');
		$this->setParameter($this->allSelectedParameters, 'allSelectedParameters');
		$this->setParameter($this->sortOrder, 'order');
		$this->setParameter($this->mutation, 'mutation');
		$this->setParameter($this->getIndexByType(), 'esIndex');
		$this->setParameter($this->type, 'type');
		$this->setCategory(null);
		$this->setParameter(null, 'minScore');
	}

	private function setObject(RoutableEntity $object): void
	{
		$this->object = $object;
		$this->setParameter($object, 'parameterObject');

		if ($this->object instanceof CatalogTree) {
			$this->type = self::TYPE_CATALOG;
		} elseif ($this->object instanceof CommonTree && $this->object->uid === Tree::UID_SEARCH) {
			$this->type = self::TYPE_SEARCH;
		} elseif ($this->object instanceof CommonTree) {
			$this->type = self::TYPE_BLOG;
		} elseif ($this->object instanceof DiscountLocalization) {
			$this->type = self::TYPE_DISCOUNT;
		} else {
			throw new InvalidArgumentException('Object of type "' . $this->object::class . '" is not supported.');
		}
	}

	public function getObject(): RoutableEntity
	{
		return $this->object;
	}

	private function setParameter(mixed $data, string $identifier): void
	{
		$dataType = $identifier;
		if (gettype($data) === 'object') {
			$reflection = new \ReflectionClass($data);
			$dataType   = $reflection->getName();
		}

		$this->parameters[$dataType] = $data;
		$this->onParameterSet($data, $identifier, $dataType);
	}

	private function onParameterSet(mixed $data, string $identifier, string $dataType): void
	{

	}

	public function getFilter(): stdClass
	{
		return $this->getBucketFilter()->getFilter(
			$this->object,
			$this->getParameter('allSelectedParameters'),
		);
	}

	public function getBucketFilter(): BucketFilter
	{
		if ($this->bucketFilter === null) {
			$this->bucketFilter = $this->createBucketFilter();
		}

		return $this->bucketFilter;
	}

	private function createBucketFilter(): BucketFilter
	{
		if ($this->type === null) {
			throw new InvalidArgumentException('Type of BucketFilter is not defined.');
		}

		$bucketFactoryArgs = $bucketFactoryArgsList =  [];
		foreach (self::GENERATORS[$this->type] as $generator) {
			$reflectionClass      = new \ReflectionClass($generator);
			$generatorFactoryArgs = [];
			foreach ($reflectionClass->getMethod('create')->getParameters() as $reflectionParameter) {
				assert($reflectionParameter->getType() instanceof \ReflectionNamedType);
				if ($reflectionParameter->getType()->isBuiltin()) {
					$generatorFactoryArgs[] = $this->getParameter($reflectionParameter->getName());
				} else {
					$generatorFactoryArgs[] = $this->getParameter($reflectionParameter->getType()->getName());
				}
			}
			$bucketFactoryArgsListItem = $this->container->getByType($generator)->create(...$generatorFactoryArgs);
			if ($bucketFactoryArgsListItem instanceof BaseBoxList) {
				$bucketFactoryArgsListItem->setItemsToSkip($this->itemsToSkip);
			}
			$bucketFactoryArgsList[] = $bucketFactoryArgsListItem;

		}

		$bucketFactoryArgsList[] = $this->getIndexByType();
		$bucketFactoryArgsList[] = $this->type;

		$reflectionClass = new \ReflectionClass($this->bucketFilterFactory);
		foreach ($reflectionClass->getMethod('create')->getParameters() as $reflectionParameter){
			if ($reflectionParameter->getType() instanceof \ReflectionNamedType) {
				if ($reflectionParameter->getType()->isBuiltin()) {
					$bucketFactoryArgs[] = $this->getParameter($reflectionParameter->getName());
				} else {
					foreach ($bucketFactoryArgsList as $item) {
						$type = $reflectionParameter->getType()->getName();
						if($item instanceof $type){
							$bucketFactoryArgs[] = $item;
						}
					}
				}
			} elseif ($reflectionParameter->getType() instanceof \ReflectionUnionType) {
				$bucketFactoryArgs[] = $this->getParameter($reflectionParameter->getName());
			}
		}
		return $this->bucketFilterFactory->create(...$bucketFactoryArgs);
	}

	private function getIndexByType(): EsIndex|null {
		if ($this->type === self::TYPE_BLOG) {
			return $this->orm->esIndex->getCommonLastActive($this->mutation);
		} else {
			return $this->orm->esIndex->getProductLastActive($this->mutation);
		}
	}

	private function getParameter(string $type): mixed
	{
		if (array_key_exists($type, $this->parameters)) {
			return $this->parameters[$type];
		}
		throw new InvalidArgumentException('Missing parameter of type "' . $type . '" for bucket filter.');
	}

	public function getBestsellers(int $itemCount = 4): Result
	{
		$sort = $this->sortCreator->create(
			SortCreator::BESTSELLER,
			$this->getParameter(State::class),
			$this->getParameter(PriceLevel::class),
		);

		$bestsellersResult = $this->getItems($itemCount, sort: $sort);;
		if ($bestsellersResult->totalCount > $itemCount) {

			return $bestsellersResult;
		}
		return Result::empty();
	}


	public function getItems(int $limit, ?int $offset = 0, ?Sort $sort = null): Result
	{
		try {
			return $this->getBucketFilter()->getItems(
				$limit,
				$offset,
				$sort ?? $this->getSort()
			);
		} catch (\Throwable) {

		}
		return Result::empty();
	}

	public function getSort(): Sort
	{
		return $this->sortCreator->create(
			$this->getParameter('order'),
			$this->getParameter(State::class),
			$this->getParameter(PriceLevel::class),
			$this->object
		);
	}

	public function redrawSnippets(mixed $cleanFilterParam = null): void
	{
		if ($this->presenter->isAjax()) {
			if (isset($cleanFilterParam)) {
				$this->presenter->getPayload()->newUrl = urldecode(htmlspecialchars_decode($this->presenter->link('//this',
					['filter' => $cleanFilterParam])));
			}

			if ($this->presenter->getParameter('more') !== null) {
				$this->presenter['catalogProducts']->redrawControl('productList');
				$this->presenter['catalogProducts']->redrawControl('productsPagerBottom');
			} else {
				if ( method_exists($this->presenter, 'isSignalRequest') && ! $this->presenter->isSignalRequest()) {
					$this->presenter->redrawControl('filterSelected');
					$this->presenter->redrawControl('filterAreaMainWrapper');
					$this->presenter->redrawControl('filterArea');
					$this->presenter->redrawControl('filterSetup');
					$this->presenter->redrawControl('catalogHeader');
					$this->presenter->redrawControl('bestsellers');
					$this->presenter->redrawControl('products');
					$this->presenter->redrawControl('title');
				}
			}
		}

	}

	public function setCategory(?string $category): void
	{
		$this->category = null;

		if($category !== null) {
			/** @var null|Alias $alias */
			$alias = $this->orm->alias->getBy(['alias' => $category, 'mutation' => $this->mutation]);

			if ($alias === null) {
				throw new BadRequestException();
			}

			$repository = $this->orm->getRepositoryByName($alias->module);
			/** @var CatalogTree $categoryObject */
			$categoryObject = $repository->getById($alias->referenceId);
			if ($categoryObject !== null) {
				$this->category = $categoryObject->id;
			}

		}
		$this->setParameter($this->category, 'category');
	}

	public function getCategory(): ?int
	{
		return $this->category;
	}

	public function setProductIds(array $productIds): void
	{
		$this->setParameter($productIds, 'productIds');
	}

	public function setSearchParameters(Pages $pages, string $stringToSearch, array $forcedIds, int|float|null $minScore): void
	{
		$this->setParameter($pages, 'pages');
		$this->setParameter($stringToSearch, 'stringToSearch');
		$this->setParameter($forcedIds, 'forcedIds');
		$this->setParameter($minScore, 'minScore');
	}


	public function setSort(string $sort): void
	{
		$this->setParameter($sort, 'order');
	}
}
