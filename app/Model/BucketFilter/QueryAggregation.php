<?php declare(strict_types = 1);

namespace App\Model\BucketFilter;

use App\Model\BucketFilter\ElasticItem\AggregableElasticItem;

final class QueryAggregation
{

	public const OPERATOR_AND = 'and';
	public const OPERATOR_OR = 'or';

	public function get(array $filterItems = [], bool $withFiltering = true): array
	{
		$aggrs = [];

		foreach ($filterItems as $filterItem) {
			if($filterItem instanceof AggregableElasticItem) {
				$abstractAggregation = $filterItem->getAggregation($filterItems, $withFiltering);
				if ($abstractAggregation !== null) {
					$aggrs[] = $abstractAggregation;
				}
			}
		}

		return $aggrs;
	}

}
