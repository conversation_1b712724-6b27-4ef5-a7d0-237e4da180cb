<?php declare(strict_types = 1);

namespace App\Model\BucketFilter;

use App\Model\Currency\CurrencyHelper;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;
use App\PostType\Series\Model\Orm\Series\Series;
use Elastica\Query\Range;
use Elastica\QueryBuilder;
use Elastica\Script\Script;

final class Sort
{
	private bool $isPrefixed = true;
	private bool $isSuffixed = false;
	private bool $isIndexed = false;
	private array $sentences = [];
	public function __construct(private readonly string $order)
	{

	}



	public function addByPrice(State $state, PriceLevel $priceLevel, string $dir = 'asc'): void
	{
//		$this->sentences["hasSomePrice"] = 'desc';
		$this->sentences["statePricesWithVat.{$state->code}.{$priceLevel->type}." . CurrencyHelper::getCurrencyCode()] = $dir;
	}

	public function addByName(string $dir = 'asc'): void
	{
		$this->sentences['nameSort'] = $dir;
	}

	public function getSentences(): array
	{
		return $this->sentences;
	}

	public function addByStore(): void
	{
		$this->sentences['isInStore'] = 'desc';
	}


	public function addByBestseller(): void
	{
		$this->sentences['6MonthSale'] = 'desc';
	}

	public function addByScore(): void
	{
		$this->sentences['_score'] = 'desc';
	}

	public function addByComputedScore(): void
	{
		$this->sentences['score'] = 'desc';
	}

	public function addByReview(): void
	{
		$this->sentences['reviewAverage'] = 'desc';
	}

	public function addByNewest(): void
	{
		$this->sentences['isInPrepare'] = 'asc'; // Pre-order goes to end
		$this->addByDatePublished('desc');
		$this->sentences['dateCreated'] = 'desc';
	}

	public function addByHasPriceLevelDefault(): void
	{
		$this->sentences['hasPrice.CZ.default.' . CurrencyHelper::getCurrencyCode()] = 'desc';
	}

	public function addByOldest(): void
	{
		$this->sentences['dateCreated'] = 'asc';
	}

	public function addByDatePublished(string $dir = 'asc'): void
	{
		$this->sentences['datePublished'] = $dir;
	}

	public function addSoldCountTotal(): void
	{
		$this->sentences['soldCount'] = 'desc';
	}

	public function addSoldCountHalfYear(): void
	{
		// TODO:
		$this->sentences['soldCount'] = 'desc';
	}

	public function getOrder(): string
	{
		return $this->order;
	}

	public function setIsPrefixed(bool $isPrefixed = true): void
	{
		$this->isPrefixed = $isPrefixed;
	}

	public function setIsSuffixed(bool $isSuffixed = true): void
	{
		$this->isSuffixed = $isSuffixed;
	}

	public function setIsIndexed(bool $isIndexed = true): void
	{
		$this->isIndexed = $isIndexed;
	}

	public function isPrefixed(): bool
	{
		return $this->isPrefixed;
	}

	public function isSuffixed(): bool
	{
		return $this->isSuffixed;
	}

	public function isIndexed(): bool
	{
		return $this->isIndexed;
	}

	public function addByFulltextScore(): void
	{
		$this->sentences['_score'] = 'desc';
	}

	public function addByStoreCascade(): void
	{
		$this->sentences['stockPriority'] = 'desc';
	}


	public function addByDiscount(): void
	{
		$this->sentences['discountPercentage.CZ.default.' . CurrencyHelper::getCurrencyCode()] = 'desc';
	}

	public function addByClassType(?ParameterValue $online): void
	{
		if ($online === null) {
			return;
		}
		$scriptArray = (new Script("
			if (doc['typKurzu.id'].size() == 0) return 9999;
			int value = doc['typKurzu.id'].value;
			if (value == params.onlineId) return 1;
			return 2;
		", lang: 'painless', params: ['onlineId' => $online->id]))->toArray();

		$scriptArray['order'] = 'asc';
		$scriptArray['type'] = 'number';

		$this->sentences['scriptedEndDate'] = ['_script' => $scriptArray];
	}

	public function addByClassUpcomingDate(): void
	{
		$nestedDate = [
			"classEvents.from" => [
				"mode" => "min",
				"order" => "asc",
				"nested" => [
					"path" =>"classEvents",
					"filter" => [
						"range" => [
							"classEvents.from" => [
								"gte" => "now"
							]
						]
					],
				]
			]
		];

		$this->sentences['classUpcomingDate'] = $nestedDate;
	}

}
