<?php declare(strict_types = 1);

namespace App\Model\BucketFilter;

use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\ParameterValue\ParameterValueRepository;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\RoutableEntity;
use App\Model\Orm\State\State;
use App\PostType\Page\Model\Orm\CatalogTree;

final class SortCreator
{
	public const string TOP = 'top';
	public const string CHEAPEST = 'cheapest';
	public const string EXPENSIVE = 'expensive';
	public const string BESTSELLER = 'bestseller';
	public const string FULLTEXT = 'fulltextScore';

	public function create(string $order, State $state, PriceLevel $priceLevel, ?RoutableEntity $routableEntity = null): Sort
	{
		$sort = new Sort($order);
		// products
		$sort->setIsPrefixed(false);
		if ($order === 'cheapest') {
			$sort->setIsIndexed();
			$sort->addByHasPriceLevelDefault();
			$sort->addByPrice($state, $priceLevel, 'asc');
		} elseif ($order === 'expensive') {
			$sort->setIsIndexed();
			$sort->addByHasPriceLevelDefault();
			$sort->addByPrice($state, $priceLevel, 'desc');
		} elseif ($order === 'top') {
			$sort->setIsIndexed();
			$sort->addByHasPriceLevelDefault();
			$sort->addByComputedScore();
		}

		//$sort->addByStore();
//		if ($order === 'top') {
//			$sort->setIsPrefixed(false);
//			$sort->setIsIndexed();
//			$sort->addByHasPriceLevelDefault();
//
//			$sort->addByComputedScore();
//		//} elseif ($order === 'score') {
//        //    $sort->addByScore();
//		} elseif ($order === self::BESTSELLER) {
//			$sort->setIsPrefixed(false);
//
//			$sort->addByBestseller();
//			$sort->addByComputedScore();
//		} elseif ($order === 'review') {
//			$sort->addByReview();
//		} elseif ($order === 'newest') {
//			$sort->addByHasPriceLevelDefault();
//			$sort->setIsIndexed();
//
//			$sort->addByNewest();
//		} elseif ($order === 'oldest') {
//			$sort->addByHasPriceLevelDefault();
//			$sort->addByOldest();
//		} elseif ($order === 'name') {
//			$sort->setIsPrefixed(false);
//			$sort->setIsSuffixed();
//			$sort->addByHasPriceLevelDefault();
//			$sort->addByName();
//		} elseif ($order === 'fulltextScore') {
//			$sort->addByFulltextScore();
//			$sort->addByStoreCascade();
//		} elseif ($order === 'discount') {
//			$sort->setIsIndexed(false);
//			$sort->setIsPrefixed(false);
//			$sort->setIsSuffixed();
//
//			$sort->addByHasPriceLevelDefault();
//			$sort->addByDiscount();
//			$sort->addByComputedScore();
//		}

		return $sort;
	}

}
