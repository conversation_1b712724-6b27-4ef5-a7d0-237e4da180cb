<?php declare(strict_types=1);

namespace App\Model\BucketFilter;

use App\PostType\Page\Model\Orm\CatalogTree;
use Nette\Http\Session;
use Nette\Http\SessionSection;

class FilterSessionStorage
{

	private ?int $objectId = null;
	private ?string $filter = null;
	private ?string $order = null;

	private bool $sessionLoaded = false;
	private SessionSection $sessionSection;

	private const string SESSION_KEY = 'userFilters';

	public function __construct(
		private readonly Session $session,
	)
	{
		$this->sessionSection = $this->session->getSection(self::class);
	}

	public function adjustFilterParams(array $filterParams): array
	{
		$setFilter = false;
		$this->objectId = $filterParams["object"]->id ?? null;
		$setOrder = false;

		if ($filterParams && $this->objectId !== null && isset($filterParams["object"]) && $filterParams['object'] instanceof CatalogTree) {
			if (isset($filterParams['filter_clear']) || (empty($filterParams['filter']) && isset($filterParams['filter_set']))) {
				unset($filterParams['filter_clear'], $filterParams['filter_set']);
				$setFilter = true;
				$filterParams['filter'] = [];
			} else {
				if (empty($filterParams['filter']) && !isset($filterParams['filter_set'])) {
					$setFilter = true;
					$filterParams['filter'] = $this->getFilter();
				} else if ($filterParams['filter'] !== $this->getFilter()) {
					$setFilter = true;
				}
			}

			if (isset($filterParams['order_set'])) {
				$setOrder = true;
				$filterParams['order'] = $filterParams['order'] ?? null;
			} else {
				if (empty($filterParams['order'])) {
					$setOrder = true;
					$filterParams['order'] = $this->getOrder();;
				} else if ($filterParams['order'] !== $this->getOrder()) {
					$setOrder = true;
				}
			}
			if ($setFilter || $setOrder) {
				$this->set($filterParams['filter'], $filterParams['order']);
			}

		}
		return $filterParams;
	}

	public function getFilter(): ?array
	{
		if ($this->objectId !== null) {
			$this->loadFromSession();
		}
		return $this->filter === null ? null : json_decode($this->filter, true);
	}

	public function getOrder(): ?string
	{
		if ($this->objectId !== null) {
			$this->loadFromSession();
		}
		return $this->order;
	}

	public function set(?array $filter, ?string $order): void
	{
		if ($this->objectId === null) {
			return;
		}

		$encodedFilter = json_encode($filter);
		if ($encodedFilter) {
			$this->filter = $encodedFilter;
		}
		$this->order = $order;
		$this->saveToSession($this->objectId, $this->filter, $order);
	}

	public function clear(): void
	{
		// Clear session data for current object ID
		if ($this->objectId !== null) {
			$this->clearFromSession($this->objectId);
		}
	}

	private function loadFromSession(): void
	{
		if (!$this->sessionLoaded) {
			$filters = isset($this->sessionSection[self::SESSION_KEY]) ? $this->sessionSection[self::SESSION_KEY] : [];

			// If we have a current objectId, try to get its data
			if ($this->objectId !== null && isset($filters[$this->objectId])) {
				$data = $filters[$this->objectId];
				$this->filter = $data['filter'] ?? null;
				$this->order = $data['order'] ?? null;
				$this->sessionLoaded = true;
				return;
			}

			// Reset filter and order if we don't have data for current objectId
			$this->filter = null;
			$this->order = null;
		}
		$this->sessionLoaded = true;
	}

	private function saveToSession(?int $objectId, ?string $filter, ?string $order): void
	{
		if ($objectId === null) {
			return;
		}

		$filters = isset($this->sessionSection[self::SESSION_KEY]) ? $this->sessionSection[self::SESSION_KEY] : [];

		$filters[$objectId] = [
			'filter' => $filter,
			'order' => $order,
		];

		$this->sessionSection[self::SESSION_KEY] = $filters;
		$this->sessionLoaded = true;
	}

	private function clearFromSession(int $objectId): void
	{
		$filters = isset($this->sessionSection[self::SESSION_KEY]) ? $this->sessionSection[self::SESSION_KEY] : [];

		if (isset($filters[$objectId])) {
			unset($filters[$objectId]);
			$this->sessionSection[self::SESSION_KEY] = $filters;
		}
	}
}
