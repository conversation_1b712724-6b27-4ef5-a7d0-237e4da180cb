<?php declare(strict_types = 1);

namespace App\Model;

use App\Model\ConfigService;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Traits\HasStaticCache;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Page\Model\Orm\Tree;
use Tracy\Debugger;

/**
 * @property-read ?CommonTree $lostPassword
 * @property-read ?CommonTree $resetPassword
 * @property-read ?CommonTree $search
 * @property-read ?CommonTree $tag
 * @property-read ?CommonTree $userSection
 * @property-read ?CommonTree $userLogin
 * @property-read ?CatalogTree $eshop
 * @property-read ?CommonTree $title
 * @property-read ?CommonTree $discount
 * @property-read ?CommonTree $precart
 * @property-read ?CommonTree $cart
 * @property-read ?CommonTree $step1
 * @property-read ?CommonTree $step2
 * @property-read ?CommonTree $step3
 * @property-read ?CommonTree $contact
 * @property-read ?CommonTree $userOrderHistory
 * @property-read ?CommonTree $productGallery
 * @property-read ?CommonTree $userAddress
 * @property-read ?CommonTree $loyaltyProgram
 * @property-read ?CommonTree $review_rules
 * @property-read ?CommonTree $personalData
 * @property-read ?CommonTree $registration
 * @property-read ?CommonTree $registrationOk
 * @property-read ?CommonTree $popupDeliveryOptions
 * @property-read ?CommonTree $watchdog
 * @property-read ?CommonTree $sortHelp
 * @property-read ?CommonTree $userAnimal
 * @property-read ?CommonTree $userAnimalEdit
 * @property-read ?Tree $cookie
 * @property-read ?CommonTree $calendar
 * @property-read CommonTree $paymentLandingPage
 */
final class Pages
{
	use HasStaticCache;

	private ?array $uidList = null;

	public function __construct(
		private readonly Orm $orm,
		private readonly Mutation $mutation,
	)
	{
	}


	public function __get(string $uid): ?Tree
	{
		$this->loadList();

		if (isset($this->uidList[$uid])) {
			return $this->uidList[$uid];
		}

		return $this->loadFromCache($uid);
	}

	private function loadList(): void
	{
		if ($this->uidList === null) {
			$uidKeys = function () {
				$docComment = (string) (new \ReflectionClass($this))->getDocComment();
				preg_match_all('/@property-read\s+([^\s]+)\s+\$([^\s]+)/', $docComment, $properties);
				return $properties[2];
			};

			$this->uidList = $this->orm->tree->findBy([
				'uid' => $uidKeys(),
				'mutation' => $this->mutation,
			])->findBy($this->orm->tree->getPublicOnlyWhere())
				->fetchPairs('uid');
		}
	}

	private function loadFromCache(string $uid): ?Tree
	{
		return $this->tryLoadCache($this->createCacheKey('pages', $this->mutation, $uid), function () use ($uid): ?Tree {
			if ($uid === 'title') {
				return $this->orm->tree->getById($this->mutation->getRealRootId());
			} else {
				$cond = [
					'mutation' => $this->mutation,
					'uid' => $uid,
				];
				$cond = array_merge($cond, $this->orm->tree->getPublicOnlyWhere());
				return $this->orm->tree->getBy($cond);
			}
		});
	}

	public function __isset(string $name): bool
	{
		$this->loadList();

		if (!isset($this->uidList[$name])) {
			return $this->loadFromCache($name) !== null;
		}
		return true;
	}

}
