<?php declare(strict_types = 1);

namespace App\Model\Robots;

use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Mutation\Mutation;
use App\PostType\Page\Model\Orm\TreeRepository;

class RobotsRows
{

	public function __construct(
		private readonly array $configRows,
		private readonly TreeRepository $treeRepository,
		private readonly MutationsHolder $mutationsHolder,
	)
	{
	}


	public function get(Mutation $selectedMutation): array
	{
		$robotTxtRows = [];


		// sitemap url
		$robotTxtRows[] = '# Website Sitemap';
		$robotTxtRows[] = 'Sitemap: ' . $selectedMutation->getBaseUrl() . '/sitemap.xml';
		$robotTxtRows[] = '';


		foreach ($this->configRows as $row) {
			$robotTxtRows[] = $row;
		}

		foreach ($this->findMutationToSolve($selectedMutation) as $mutation) {
			assert($mutation instanceof Mutation);
			$robotTxtRows[] = '# ' . $mutation->langCode;

			foreach ($this->treeRepository->findBy([
				'forceNoIndex' => 1,
				'mutation' => $mutation,
			]) as $tree) {
				if (($alias = $tree->alias) !== null) {
					if ($mutation->getRealUrlPrefix() === '') {
						$robotTxtRows[] = 'Disallow: /' . $alias->alias;
					} else {
						$robotTxtRows[] = 'Disallow: /' . $mutation->getRealUrlPrefix() . '/' . $alias->alias;
					}
				}
			}
		}

		return $robotTxtRows;
	}


	private function findMutationToSolve(Mutation $selectedMutation): array
	{
		$mutations = [];
		foreach ($this->mutationsHolder->findAll() as $mutation) {
			if ($mutation->getBaseUrl() === $selectedMutation->getBaseUrl()) {
				$mutations[] = $mutation;
			}
		}

		return $mutations;
	}

}
