<?php

namespace App\Model\IpAddress;


readonly class IpAddressStaticData implements IpAddressInfo
{
	public function __construct(
		private ?string $countryCode,
		private bool $isInEurope,
	)
	{
	}

	public static function create(?string $countryCode, bool $isInEurope): self
	{
		return new self($countryCode, $isInEurope);
	}


	public function getCountryCode(): ?string
	{
		return $this->countryCode;
	}

	public function isInEurope(): bool
	{
		return $this->isInEurope;
	}


}
