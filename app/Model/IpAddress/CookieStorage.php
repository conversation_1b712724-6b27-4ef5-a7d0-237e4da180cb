<?php declare(strict_types=1);

namespace App\Model\IpAddress;

use Nette\Http\Request;
use Nette\Http\Response;
use Throwable;

class CookieStorage
{

	private ?IpAddressStaticData $ipAddressStaticData = null;

	private const COOKIE_EXPIRE = '+1 month';
	public const COOKIE_NAME = 'countryMetadata';

	public function __construct(
		private readonly Request $httpRequest,
		private readonly Response $httpResponse,
	)
	{
	}

	public function get(): ?IpAddressStaticData
	{
		if ($this->ipAddressStaticData === null) {
			$this->ipAddressStaticData = $this->loadCookie();
		}
		return $this->ipAddressStaticData;
	}

	public function set(IpAddressStaticData $ipAddressStaticData): void
	{
		$this->ipAddressStaticData = $ipAddressStaticData;
		$this->save();
	}


	private function loadCookie(): ?IpAddressStaticData
	{
		try {
			$value = $this->httpRequest->getCookie(self::COOKIE_NAME);
			if ($value !== null) {
				$values = unserialize($value);
				return IpAddressStaticData::create($values[0], $values[1]);
			}
		} catch (Throwable) {
			//bad data in json -> ignore
		}
		return null;
	}


	private function save(): void
	{
		$data = serialize([$this->ipAddressStaticData->getCountryCode(), $this->ipAddressStaticData->isInEurope()]);
		$this->httpResponse->setCookie(self::COOKIE_NAME, $data, self::COOKIE_EXPIRE);
	}

}
