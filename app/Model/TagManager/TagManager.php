<?php
declare(strict_types=1);

namespace App\Model\TagManager;

use App\Model\TagManager\Renderer\RendererRegistry;
use Iterator;

/**
 * @implements Iterator<int, mixed>
 */
final class TagManager implements Iterator
{

	public const RENDER_POSITION_TOP = 'top';
	public const RENDER_POSITION_BOTTOM = 'bottom';

	public function __construct(
		private readonly RendererRegistry $rendererRegistry
	)
	{
	}

	private int $position = 0;

	private string $renderPosition = self::RENDER_POSITION_BOTTOM;

	private array $tags = [];

	public function add(RenderableTag $tag, string $renderPosition = self::RENDER_POSITION_BOTTOM): self
	{
		$this->tags[$renderPosition][] = $tag;
		return $this;
	}

	public function current(): mixed
	{
		$tag = $this->tags[$this->renderPosition][$this->position];
		$renderer = $this->rendererRegistry->get($tag->getRendererName());
		//unset($this->tags[$this->position]);
		return $renderer->render($tag);
	}

	public function next(): void
	{
		++$this->position;
	}

	public function key(): int
	{
		return $this->position;
	}

	public function valid(): bool
	{
		return isset($this->tags[$this->renderPosition][$this->position]);
	}

	public function rewind(): void
	{
		$this->position = 0;
	}

	public function setRenderPosition(string $renderPosition): self
	{
		$this->position = 0;
		$this->renderPosition = $renderPosition;
		return $this;
	}

}
