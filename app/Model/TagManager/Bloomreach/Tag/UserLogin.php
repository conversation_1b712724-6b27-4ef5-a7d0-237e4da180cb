<?php

declare(strict_types=1);

namespace App\Model\TagManager\Bloomreach\Tag;

use App\Event\AfterLogin;
use App\Model\TagManager\RenderableTag;
use App\Model\TagManager\Renderer\BloomreachRenderer;

final readonly class UserLogin implements RenderableTag
{

	public function __construct(
		private AfterLogin $loggedInEvent,
	)
	{
	}

	public function getEventName(): string
	{
		return 'user_login';
	}

	public function getData(): array
	{
		return [
			'placement' => $this->loggedInEvent->position,
			'language' => $this->loggedInEvent->mutation->langCode,
			'locale' => $this->loggedInEvent->mutation->isoCode,
			'timestamp' => time(),
		];
	}

	public function getRendererName(): string
	{
		return BloomreachRenderer::class;
	}

	public function getDomElementId(): ?string
	{
		return null;
	}

}
