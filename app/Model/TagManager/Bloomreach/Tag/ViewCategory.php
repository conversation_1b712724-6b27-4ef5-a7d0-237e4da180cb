<?php

declare(strict_types=1);

namespace App\Model\TagManager\Bloomreach\Tag;

use App\Event\CategoryView;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Security\User;
use App\Model\TagManager\RenderableTag;
use App\Model\TagManager\Renderer\BloomreachRenderer;
use App\PostType\Page\Model\Orm\CatalogTree;

final readonly class ViewCategory implements RenderableTag
{

	public function __construct(
		private CategoryView $categoryView,
		private User $user,
	)
	{
	}

	public function getEventName(): string
	{
		return 'view_category';
	}

	public function getData(): array
	{
		/*$productLocalization = $this->productDetailView->productLocalization;
		$product = $productLocalization->product;

		$discount = $product->getPriceInfo($this->productDetailView->mutation, $this->productDetailView->priceLevel, $this->productDetailView->state)->getDiscountAmount();
*/
		$tree = $this->categoryView->tree;

		$department = null;
		$i = 0;
		foreach ($tree->pathItems as $item) {
			if ($item instanceof CatalogTree) {
				if ($i === 1) {
					$department = $item->name;
					break;
				}
				$i++;
			}
		}

		$availability = isset($this->categoryView->filterParams['flags']['isInStore']);
		$price = isset($this->categoryView->filterParams['ranges']['price']);
		$priceFrom = $priceTo = null;
		if ($price) {
			$priceTo = $this->categoryView->filterParams['ranges']['price']['max'] ?? null;
			$priceFrom = $this->categoryView->filterParams['ranges']['price']['min'] ?? null;
		}

		$filter = isset($this->categoryView->filterParams['dials']);
		$filters = [];
		if ($filter) {
			foreach ($this->categoryView->filterParams['dials'] as $key => $dial) {
				foreach ($dial as $dialItem) {
					$filters[] = $key . '_' . $dialItem;
				}
			}
		}

		$data = [
			'category_id' => $tree->id,
			'category_name' => $tree->name,
			'category_department' => $department,
			'category_sorting_method' => $this->categoryView->order,
			'category_sorting_availability' => $availability ? 'On stock' : 'All',
			'category_sorting_price' => $price,
			'category_sorting_price_from' => $priceFrom,
			'category_sorting_price_to' => $priceTo,

			'category_score' => $tree->score,
			'category_filters' => $filters,

			'category_products' => [],
			'visitor_login_state' => $this->user->isLoggedIn() ? 'Logged' : 'Anonymous',
			'language' => $tree->mutation->langCode,
			'locale' => $tree->mutation->isoCode,
			'timestamp' => time(),
		];

		foreach ($this->categoryView->variants as $variant) {
			assert($variant instanceof ProductVariant);
			$data['category_products'][] = $variant->product->id;
		}

		return $data;
	}

	public function getRendererName(): string
	{
		return BloomreachRenderer::class;
	}

	public function getDomElementId(): ?string
	{
		return null;
	}

}
