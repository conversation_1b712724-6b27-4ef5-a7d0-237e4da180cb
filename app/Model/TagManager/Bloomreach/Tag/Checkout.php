<?php

declare(strict_types=1);

namespace App\Model\TagManager\Bloomreach\Tag;

use App\Event\ViewCart;
use App\Model\Orm\Order\Product\ProductItem;
use App\Model\TagManager\RenderableTag;
use App\Model\TagManager\Renderer\BloomreachRenderer;

final readonly class Checkout implements RenderableTag
{

	public function __construct(
		private ViewCart $viewCart
	)
	{
	}

	public function getEventName(): string
	{
		return 'checkout';
	}

	public function getData(): array
	{
		$data = [
			'total_quantity' => $this->viewCart->shoppingCart->getTotalCount(),
			'total_price' => $this->viewCart->shoppingCart->getTotalPriceVat()->getAmount()->toFloat(),
			'total_discount' => $this->viewCart->shoppingCart->getTotalDiscountVat()->getAmount()->toFloat(),
			'total_price_original' => round($this->viewCart->shoppingCart->getTotalPriceVat()->getAmount()->toFloat() + $this->viewCart->shoppingCart->getTotalDiscountVat()->getAmount()->toFloat(), 2),
			'local_currency' => $this->viewCart->shoppingCart->getCurrency()->getCurrencyCode(),
			'language' => $this->viewCart->shoppingCart->getMutation()->langCode,
			'locale' => $this->viewCart->shoppingCart->getMutation()->isoCode,
			'timestamp' => time(),

		];

		if (($voucher = $this->viewCart->shoppingCart->getAppliedVoucherItem()) !== null) {
			$data['coupon'] = $voucher->getCode();
			$data['coupon_value'] = $voucher->totalPriceVat->getAmount()->toFloat();
		}

		/** @var ProductItem $item */
		foreach ($this->viewCart->shoppingCart->getProducts() as $item) {
			$data['product_ids'][] = $item->variant->product->getPersistedId();
			$data['product_list'][] = [
				'product_id' => $item->variant->product->getPersistedId(),
				'quantity' => $item->amount,
			];
		}

		return $data;
	}

	public function getRendererName(): string
	{
		return BloomreachRenderer::class;
	}

	public function getDomElementId(): ?string
	{
		return null;
	}

}
