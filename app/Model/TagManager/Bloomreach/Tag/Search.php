<?php

declare(strict_types=1);

namespace App\Model\TagManager\Bloomreach\Tag;

use App\Event\SearchResuls;
use App\Model\FulltextSearch\Result;
use App\Model\TagManager\RenderableTag;
use App\Model\TagManager\Renderer\BloomreachRenderer;

final readonly class Search implements RenderableTag
{

	public function __construct(
		private SearchResuls $event,
	)
	{
	}

	public function getEventName(): string
	{
		return 'search';
	}

	public function getData(): array
	{
		$result = [];

		foreach ($this->event->fulltextResults as $key => $items) {

			$e = explode('\\', $key);
			$key = end($e);

			$pieces = preg_split('/(?=[A-Z])/', lcfirst($key));
			if ($pieces !== false) {
				$key = $pieces[0];

				$count = 0;
				if ($items instanceof Result) {
					$count = count($items);
				} elseif ($items instanceof \App\Model\BucketFilter\Result) {
					$count = $items->count;
				}

				$items = $items->items;

				if ($count > 0) {
					foreach ($items as $item) {
						$result[$key . '_id'][] = $item->id;
					}
				}
			}
		}

		return [
			'query' => $this->event->searchTerm,
			'result' => $result,
			'status' => $this->event->hasResults ? 'complete' : 'no result',
		];
	}

	public function getRendererName(): string
	{
		return BloomreachRenderer::class;
	}

	public function getDomElementId(): ?string
	{
		return null;
	}

}
