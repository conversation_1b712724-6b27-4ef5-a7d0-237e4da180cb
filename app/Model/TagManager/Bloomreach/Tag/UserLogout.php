<?php

declare(strict_types=1);

namespace App\Model\TagManager\Bloomreach\Tag;

use App\Event\AfterLogout;
use App\Model\TagManager\RenderableTag;
use App\Model\TagManager\Renderer\BloomreachRenderer;

final readonly class UserLogout implements RenderableTag
{

	public function __construct(
		private AfterLogout $loggedOutEvent,
	)
	{
	}

	public function getEventName(): string
	{
		return 'user_logout';
	}

	public function getData(): array
	{
		return [
			'placement' => $this->loggedOutEvent->position,
			'language' => $this->loggedOutEvent->mutation->langCode,
			'locale' => $this->loggedOutEvent->mutation->isoCode,
			'timestamp' => time(),
		];
	}

	public function getRendererName(): string
	{
		return BloomreachRenderer::class;
	}

	public function getDomElementId(): ?string
	{
		return null;
	}

}
