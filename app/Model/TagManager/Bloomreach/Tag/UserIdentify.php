<?php

declare(strict_types=1);

namespace App\Model\TagManager\Bloomreach\Tag;

use App\Event\PageView;
use App\Model\TagManager\RenderableTag;
use App\Model\TagManager\Renderer\BloomreachUserIdentifyRenderer;

final readonly class UserIdentify implements RenderableTag
{

	public function __construct(
		private PageView $event,
	)
	{
	}

	public function getEventName(): string
	{
		return 'identify';
	}

	public function getData(): array
	{
		return [
			'customer_id' => $this->event->user->id,
			'email_id' => $this->event->user->email,
			//'phone' =>  $this->event->user->phone,
		];
	}

	public function getRendererName(): string
	{
		return BloomreachUserIdentifyRenderer::class;
	}

	public function getDomElementId(): ?string
	{
		return null;
	}

}
