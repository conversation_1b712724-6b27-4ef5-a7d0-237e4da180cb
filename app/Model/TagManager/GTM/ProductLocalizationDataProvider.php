<?php

namespace App\Model\TagManager\GTM;

use App\Model\DTO\Product\ProductDtoProvider;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\State\State;
use Brick\Money\Currency;

class ProductLocalizationDataProvider
{

	public function __construct(
		private readonly ProductDtoProvider $productDtoProvider,
	)
	{
	}


	public function getData(ProductLocalization $productLocalization, ProductVariant $variant, Mutation $mutation, State $state, PriceLevel $priceLevel, Currency $currency, int $index = 0, int $quantity = 1, ?string $itemListId = null, ?string $itemListName = null): array
	{
		$productDto = $this->productDtoProvider->get($productLocalization->product, $variant);
		$data = [
			'index' => $index,
			'item_id' => (string) $productLocalization->product->id,
			'item_name' => $productLocalization->getName(),
			'price' => sprintf('%0.2f', $productLocalization->product->price($mutation, $priceLevel, $state)->getAmount()->toFloat()),
			'currency' => $currency->getCurrencyCode(),
			'quantity' => $quantity,
		];

		if ($itemListId !== null) {
			$data['item_list_id'] = $itemListId;
		}

		if ($itemListName !== null) {
			$data['item_list_name'] = $itemListName;
		}

		$data = array_merge($data, $productDto->gtmItemCategories);

		return $data;
	}

}
