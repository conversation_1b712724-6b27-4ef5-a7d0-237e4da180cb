<?php

declare(strict_types=1);

namespace App\Model\TagManager\GTM\Tag;

use App\Event\ProductClicked;
use App\Model\TagManager\RenderableTag;
use App\Model\TagManager\Renderer\DataLayerPushRenderer;

final readonly class GTMSelectItem implements RenderableTag
{

	public function __construct(
		private ProductClicked $productClickedEvent
	)
	{
	}

	public function getRendererName(): string
	{
		return DataLayerPushRenderer::class;
	}

	public function getEventName(): string
	{
		return 'select_item';
	}


	public function getData(): array
	{
		$productGTMData = $this->productClickedEvent->productLocalization->getGTMData(
			variant: $this->productClickedEvent->productVariant,
			currency: $this->productClickedEvent->currency,
			itemListId: $this->productClickedEvent->listId,
			itemListName: $this->productClickedEvent->listName,
		);

		return [
			'event' => $this->getEventName(),
			'ecommerce' => [
				'item_list_id' => $this->productClickedEvent->listId,
				'item_list_name' => $this->productClickedEvent->listName,
				'items' => [
					$productGTMData,
				],
			],
			'_clear' => true,
		];
	}
	public function getDomElementId(): ?string
	{
		return null;
	}

}
