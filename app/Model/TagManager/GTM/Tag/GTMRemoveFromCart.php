<?php

declare(strict_types=1);

namespace App\Model\TagManager\GTM\Tag;

use App\Event\RemoveFromCart;
use App\Model\TagManager\RenderableTag;
use App\Model\TagManager\Renderer\DataLayerPushRenderer;

final readonly class GTMRemoveFromCart implements RenderableTag
{

	public function __construct(
		private RemoveFromCart $removeFromCart
	)
	{
	}

	public function getRendererName(): string
	{
		return DataLayerPushRenderer::class;
	}

	public function getEventName(): string
	{
		return 'remove_from_cart';
	}


	public function getData(): array
	{
		$productGTMData = $this->removeFromCart->productLocalization->getGTMData(
			variant: $this->removeFromCart->variant,
			currency: $this->removeFromCart->currency,
			quantity: $this->removeFromCart->quantity,
		);

		return [
			'event' => $this->getEventName(),
			'ecommerce' => [
				'currency' => $this->removeFromCart->currency->getCurrencyCode(),
				'value' => $productGTMData['price'],
				'items' => [
					$productGTMData,
				],
			],
			'_clear' => true,
		];
	}
	public function getDomElementId(): ?string
	{
		return null;
	}

}
