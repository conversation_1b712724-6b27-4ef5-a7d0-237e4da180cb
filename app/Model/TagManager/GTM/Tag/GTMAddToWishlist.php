<?php

declare(strict_types=1);

namespace App\Model\TagManager\GTM\Tag;

use App\Event\AddToWishlist;
use App\Model\TagManager\RenderableTag;
use App\Model\TagManager\Renderer\DataLayerPushRenderer;

final readonly class GTMAddToWishlist implements RenderableTag
{

	public function __construct(
		private AddToWishlist $addToWishlist
	)
	{
	}

	public function getRendererName(): string
	{
		return DataLayerPushRenderer::class;
	}

	public function getEventName(): string
	{
		return 'add_to_wishlist';
	}


	public function getData(): array
	{
		$productGTMData = $this->addToWishlist->productLocalization->getGTMData(
			variant: $this->addToWishlist->variant,
			currency: $this->addToWishlist->currency,
			itemListId: $this->addToWishlist->listId,
			itemListName: $this->addToWishlist->listName,
		);

		return [
			'event' => $this->getEventName(),
			'ecommerce' => [
				'currency' => $this->addToWishlist->currency->getCurrencyCode(),
				'value' => $productGTMData['price'],
				'items' => [
					$productGTMData,
				],
			],
			'_clear' => true,
		];
	}
	public function getDomElementId(): ?string
	{
		return null;
	}

}
