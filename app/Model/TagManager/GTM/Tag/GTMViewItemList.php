<?php

declare(strict_types=1);

namespace App\Model\TagManager\GTM\Tag;

use App\Event\ProductListView;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\TagManager\RenderableTag;
use App\Model\TagManager\Renderer\DataLayerPushRenderer;

final readonly class GTMViewItemList implements RenderableTag
{

	public function __construct(
		private ProductListView $productDetailView
	)
	{
	}

	public function getRendererName(): string
	{
		return DataLayerPushRenderer::class;
	}

	public function getEventName(): string
	{
		return 'view_item_list';
	}


	public function getData(): array
	{
		$items = [];
		$i = 0;

		foreach ($this->productDetailView->variants as $variant) {
			assert($variant instanceof ProductVariant);
			$items[] = $variant->product->getLocalization($this->productDetailView->mutation)->getGTMData(
				variant: $variant,
				currency: $this->productDetailView->currency,
				index: $i,
				itemListId: $this->productDetailView->listId,
				itemListName: $this->productDetailView->listName,
			);
			$i++;
		}

		$ecommerce = [
			'item_list_name' => $this->productDetailView->listName,
			'item_list_id' => $this->productDetailView->listId,
			'items' => $items,
		];
		return [
			'event' => $this->getEventName(),
			'ecommerce' => $ecommerce,
			'_clear' => true,
		];
	}
	public function getDomElementId(): string
	{
		return $this->productDetailView->listId;
	}

}
