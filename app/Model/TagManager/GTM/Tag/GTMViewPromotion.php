<?php

declare(strict_types=1);

namespace App\Model\TagManager\GTM\Tag;

use App\Event\ViewPromotion;
use App\Model\TagManager\RenderableTag;
use App\Model\TagManager\Renderer\DataLayerPushRenderer;

final readonly class GTMViewPromotion implements RenderableTag
{

	public function __construct(
		private ViewPromotion $viewPromotion
	)
	{
	}

	public function getRendererName(): string
	{
		return DataLayerPushRenderer::class;
	}

	public function getEventName(): string
	{
		return 'view_promotion';
	}


	public function getData(): array
	{
		$ecommerce = [
			'creative_name' => $this->viewPromotion->creative_name,
			'creative_slot' => $this->viewPromotion->creative_slot,
			'promotion_id' => $this->viewPromotion->creative_name,
			'promotion_name' => $this->viewPromotion->creative_name,
		];
		return [
			'event' => $this->getEventName(),
			'ecommerce' => $ecommerce,
			'_clear' => true,
		];
	}
	public function getDomElementId(): string
	{
		return $this->viewPromotion->creative_slot;
	}

}
