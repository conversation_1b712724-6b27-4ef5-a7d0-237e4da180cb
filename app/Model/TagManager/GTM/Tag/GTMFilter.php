<?php

declare(strict_types=1);

namespace App\Model\TagManager\GTM\Tag;

use App\Event\FilterClicked;
use App\Model\TagManager\RenderableTag;
use App\Model\TagManager\Renderer\DataLayerPushRenderer;

final readonly class GTMFilter implements RenderableTag
{

	public function __construct(
		private FilterClicked $filterClicked
	)
	{
	}

	public function getRendererName(): string
	{
		return DataLayerPushRenderer::class;
	}

	public function getEventName(): string
	{
		return 'filter';
	}


	public function getData(): array
	{
		$data = [
			'event' => $this->getEventName(),
			'filter_type' => $this->filterClicked->filterName,
			'filter_value' => $this->filterClicked->filterValue,
			'_clear' => true,
		];
		if ($this->filterClicked->filterAction !== null) {
			$data['filter_action'] = $this->filterClicked->filterAction;
		}

		return $data;
	}
	public function getDomElementId(): ?string
	{
		return null;
	}

}
