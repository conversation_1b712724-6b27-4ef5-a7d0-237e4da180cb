<?php

declare(strict_types=1);

namespace App\Model\TagManager\GTM\Tag;

use App\Event\OrderSubmit;
use App\Model\Orm\Order\Product\ProductItem;
use App\Model\TagManager\RenderableTag;
use App\Model\TagManager\Renderer\DataLayerPushRenderer;

final readonly class GTMPurchase implements RenderableTag
{

	public function __construct(
		private OrderSubmit $orderSubmit
	)
	{
	}

	public function getRendererName(): string
	{
		return DataLayerPushRenderer::class;
	}

	public function getEventName(): string
	{
		return 'purchase';
	}

	public function getData(): array
	{
		$items = [];

		$mutation = $this->orderSubmit->order->mutation;
		$currency = $this->orderSubmit->order->currency;

		$i = 0;
		/** @var ProductItem $productItem */
		foreach ($this->orderSubmit->order->getProducts() as $productItem) {
			$items[] = $productItem->variant->product->getLocalization($mutation)->getGTMData( // TODO: change to OrderProduct
				variant: $productItem->variant,
				currency: $currency,
				index: $i,
				quantity: $productItem->amount,
			);
			$i++;
		}

		$totalPrice = $this->orderSubmit->order->getTotalPrice();
		$totalPriceVat = $this->orderSubmit->order->getTotalPrice(withVat: true);
		$vat = $totalPriceVat->minus($totalPrice);
		$shippingPrice = $this->orderSubmit->order->getDelivery()->totalPrice->plus($this->orderSubmit->order->getPayment()->totalPrice);

		$ecommerce = [
			'transaction_id' => $this->orderSubmit->order->orderNumber,
			'currency' => $this->orderSubmit->order->getCurrency()->getCurrencyCode(),
			'value' => $totalPrice->getAmount()->toFloat(),
			'tax' => $vat->getAmount()->toFloat(),
			'shipping' => $shippingPrice->getAmount()->toFloat(),
			'email' => $this->orderSubmit->order->email,
			'phone' => $this->orderSubmit->order->phone,
			'items' => $items,
		];

		$coupon = $this->orderSubmit->order->getAppliedVoucherItem();
		if ($coupon !== null) {
			$ecommerce['coupon'] = $coupon->voucherCode->code;
		}

		return [
			'event' => $this->getEventName(),
			'ecommerce' => $ecommerce,
			'payment_type' => $this->orderSubmit->order->getPayment()->getName(),
			'_clear' => true,
		];
	}
	public function getDomElementId(): ?string
	{
		return null;
	}

}
