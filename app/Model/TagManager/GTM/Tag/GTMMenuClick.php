<?php

declare(strict_types=1);

namespace App\Model\TagManager\GTM\Tag;

use App\Event\MenuClicked;
use App\Model\TagManager\RenderableTag;
use App\Model\TagManager\Renderer\DataLayerPushRenderer;

final readonly class GTMMenuClick implements RenderableTag
{

	public function __construct(
		private MenuClicked $menuClicked
	)
	{
	}

	public function getRendererName(): string
	{
		return DataLayerPushRenderer::class;
	}

	public function getEventName(): string
	{
		return 'menu_click';
	}


	public function getData(): array
	{
		return [
			'event' => $this->getEventName(),
			'main_menu' => $this->menuClicked->mainMenu,
			'sub_menu' => $this->menuClicked->subMenu,
			'_clear' => true,
		];
	}
	public function getDomElementId(): ?string
	{
		return null;
	}

}
