<?php

declare(strict_types=1);

namespace App\Model\TagManager\GTM\Tag;

use App\Event\SearchResuls;
use App\Model\TagManager\RenderableTag;
use App\Model\TagManager\Renderer\DataLayerPushRenderer;

final readonly class GTMSearchResults implements RenderableTag
{

	public function __construct(
		private SearchResuls $searchResuls
	)
	{
	}

	public function getRendererName(): string
	{
		return DataLayerPushRenderer::class;
	}

	public function getEventName(): string
	{
		return 'search_results';
	}

	public function getData(): array
	{
		return [
			'event' => $this->getEventName(),
			'results' => [
				'products' => $this->searchResuls->productCount,
				'category' => $this->searchResuls->categoryCount,
			],
			'_clear' => true,
		];
	}
	public function getDomElementId(): null
	{
		return null;
	}

}
