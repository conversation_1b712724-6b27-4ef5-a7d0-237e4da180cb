<?php

declare(strict_types=1);

namespace App\Model\TagManager\GTM\Tag;

use App\Event\AddToCart;
use App\Model\TagManager\RenderableTag;
use App\Model\TagManager\Renderer\DataLayerPushRenderer;

final readonly class GTMAddToCart implements RenderableTag
{

	public function __construct(
		private AddToCart $addToCartEvent
	)
	{
	}

	public function getRendererName(): string
	{
		return DataLayerPushRenderer::class;
	}

	public function getEventName(): string
	{
		return 'add_to_cart';
	}


	public function getData(): array
	{
		$productGTMData = $this->addToCartEvent->productLocalization->getGTMData(
			variant: $this->addToCartEvent->variant,
			currency: $this->addToCartEvent->currency,
			quantity: $this->addToCartEvent->quantity,
			itemListId: $this->addToCartEvent->listId,
			itemListName: $this->addToCartEvent->listName,
		);

		return [
			'event' => $this->getEventName(),
			'ecommerce' => [
				'currency' => $this->addToCartEvent->currency->getCurrencyCode(),
				'value' => $productGTMData['price'],
				'items' => [
					$productGTMData,
				],
			],
			'_clear' => true,
		];
	}
	public function getDomElementId(): ?string
	{
		return null;
	}

}
