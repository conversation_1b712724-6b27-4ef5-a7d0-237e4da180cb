<?php

declare(strict_types=1);

namespace App\Model\TagManager\GTM\Tag;

use App\Event\AddPaymentInfo;
use App\Model\Orm\Order\Product\ProductItem;
use App\Model\TagManager\RenderableTag;
use App\Model\TagManager\Renderer\DataLayerPushRenderer;

final readonly class GTMAddPaymentInfo implements RenderableTag
{

	public function __construct(
		private AddPaymentInfo $addPaymentInfo
	)
	{
	}

	public function getRendererName(): string
	{
		return DataLayerPushRenderer::class;
	}

	public function getEventName(): string
	{
		return 'add_payment_info';
	}

	public function getData(): array
	{
		$items = [];

		$mutation = $this->addPaymentInfo->shoppingCart->getMutation();
		$currency = $this->addPaymentInfo->shoppingCart->getCurrency();

		$i = 0;
		/** @var ProductItem $productItem */
		foreach ($this->addPaymentInfo->shoppingCart->getProducts() as $productItem) {
			$items[] = $productItem->variant->product->getLocalization($mutation)->getGTMData(
				variant: $productItem->variant,
				currency: $currency,
				index: $i,
				quantity: $productItem->amount,
			);
			$i++;
		}

		$ecommerce = [
			'currency' => $this->addPaymentInfo->shoppingCart->getCurrency()->getCurrencyCode(),
			'value' => $this->addPaymentInfo->shoppingCart->getTotalPrice()->getAmount()->toFloat(),
			'items' => $items,
		];

		$coupon = $this->addPaymentInfo->shoppingCart->getAppliedVoucherItem();
		if ($coupon !== null) {
			$ecommerce['coupon'] = $coupon->voucherCode->code;
		}

		$payment = $this->addPaymentInfo->shoppingCart->getPayment();
		if ($payment !== null) {
			$ecommerce['payment_type'] = $payment->getName();
		}

		return [
			'event' => $this->getEventName(),
			'ecommerce' => $ecommerce,
			'_clear' => true,
		];
	}

	public function getDomElementId(): ?string
	{
		return null;
	}

}
