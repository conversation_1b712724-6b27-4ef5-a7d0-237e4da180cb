<?php

declare(strict_types=1);

namespace App\Model\TagManager\GTM\Tag;

use App\Event\Registered;
use App\Model\TagManager\RenderableTag;
use App\Model\TagManager\Renderer\DataLayerPushRenderer;

final readonly class GTMRegistrationComplet implements RenderableTag
{

	public function __construct(
		private Registered $registered
	)
	{
	}

	public function getRendererName(): string
	{
		return DataLayerPushRenderer::class;
	}

	public function getEventName(): string
	{
		return 'registration_complet';
	}


	public function getData(): array
	{
		$type = 'DK';
		if ($this->registered->user->googleId !== null) {
			$type = 'Google';
		} elseif ($this->registered->user->facebookId !== null) {
			$type = 'FB';
		} elseif ($this->registered->user->seznamId !== null) {
			$type = 'Seznam';
		}

		return [
			'event' => $this->getEventName(),
			'type' => $type,
			'_clear' => true,
		];
	}
	public function getDomElementId(): ?string
	{
		return null;
	}

}
