<?php

declare(strict_types=1);

namespace App\Model\TagManager\GTM\Tag;

use App\Model\TagManager\RenderableTag;
use App\Model\TagManager\Renderer\DataLayerPushRenderer;

final readonly class GTMOrderSubmit implements RenderableTag
{

	public function getRendererName(): string
	{
		return DataLayerPushRenderer::class;
	}

	public function getEventName(): string
	{
		return 'order_submit';
	}

	public function getData(): array
	{
		return [
			'event' => $this->getEventName(),
			'_clear' => true,
		];
	}
	public function getDomElementId(): ?string
	{
		return null;
	}

}
