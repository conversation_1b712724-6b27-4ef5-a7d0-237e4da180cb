<?php

declare(strict_types=1);

namespace App\Model\TagManager\GTM\Tag;

use App\Event\ProductDetailView;
use App\Model\TagManager\RenderableTag;
use App\Model\TagManager\Renderer\DataLayerPushRenderer;

final readonly class GTMViewItem implements RenderableTag
{

	public function __construct(
		private ProductDetailView $productDetailView
	)
	{
	}

	public function getRendererName(): string
	{
		return DataLayerPushRenderer::class;
	}

	public function getEventName(): string
	{
		return 'view_item';
	}


	public function getData(): array
	{
		$productGTMData = $this->productDetailView->productLocalization->getGTMData(
			variant: $this->productDetailView->productVariant,
			currency: $this->productDetailView->currency,
			itemListId: $this->productDetailView->listId,
			itemListName: $this->productDetailView->listName,
		);
		$ecommerce = [
			'currency' => $this->productDetailView->currency->getCurrencyCode(),
			'value' => $productGTMData['price'],
			'items' => [
				$productGTMData,
			],
		];
		return [
			'event' => $this->getEventName(),
			'ecommerce' => $ecommerce,
			'_clear' => true,
		];
	}
	public function getDomElementId(): ?string
	{
		return null;
	}

}
