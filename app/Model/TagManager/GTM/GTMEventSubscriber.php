<?php

declare(strict_types=1);

namespace App\Model\TagManager\GTM;

use App\Event\AddPaymentInfo;
use App\Event\AddShippingInfo;
use App\Event\AddToCart;
use App\Event\AddToWishlist;
use App\Event\BannerClicked;
use App\Event\BeginCheckout;
use App\Event\FilterClicked;
use App\Event\MenuClicked;
use App\Event\OrderSubmit;
use App\Event\PageView;
use App\Event\ProductClicked;
use App\Event\ProductDetailView;
use App\Event\ProductListView;
use App\Event\Provider\AddToCartClickedProvider;
use App\Event\Provider\BannerClickedProvider;
use App\Event\Provider\FilterClickedProvider;
use App\Event\Provider\FilterRangeClickedProvider;
use App\Event\Provider\MenuClickedProvider;
use App\Event\Provider\ProductClickedProvider;
use App\Event\Registered;
use App\Event\RemoveFromCart;
use App\Event\SearchClick;
use App\Event\SearchResuls;
use App\Event\ViewCart;
use App\Event\ViewPromotion;
use App\Model\Orm\Orm;
use App\Model\Orm\PaymentMethod\BankTransfer;
use App\Model\Orm\PaymentMethod\CashOnDelivery;
use App\Model\TagManager\GTM\Tag\GTMAddPaymentInfo;
use App\Model\TagManager\GTM\Tag\GTMAddShippingInfo;
use App\Model\TagManager\GTM\Tag\GTMAddToCart;
use App\Model\TagManager\GTM\Tag\GTMAddToWishlist;
use App\Model\TagManager\GTM\Tag\GTMBeginCheckout;
use App\Model\TagManager\GTM\Tag\GTMFilter;
use App\Model\TagManager\GTM\Tag\GTMMenuClick;
use App\Model\TagManager\GTM\Tag\GTMOrderSubmit;
use App\Model\TagManager\GTM\Tag\GTMPageView;
use App\Model\TagManager\GTM\Tag\GTMPurchase;
use App\Model\TagManager\GTM\Tag\GTMRegistrationComplet;
use App\Model\TagManager\GTM\Tag\GTMRemoveFromCart;
use App\Model\TagManager\GTM\Tag\GTMSearchResults;
use App\Model\TagManager\GTM\Tag\GTMSelectItem;
use App\Model\TagManager\GTM\Tag\GTMSelectPromotion;
use App\Model\TagManager\GTM\Tag\GTMViewCart;
use App\Model\TagManager\GTM\Tag\GTMViewItem;
use App\Model\TagManager\GTM\Tag\GTMViewItemList;
use App\Model\TagManager\GTM\Tag\GTMViewPromotion;
use App\Model\TagManager\GTM\Tag\GTMWhisperer;
use App\Model\TagManager\TagManager;
use Nette\Http\Request;
use Nette\Http\Response;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

final readonly class GTMEventSubscriber implements EventSubscriberInterface
{

	public function __construct(
		private TagManager $tagManager,
		private Request $httpRequest,
		private Response $httpResponse,
		private Orm $orm,
		private ProductClickedProvider $productClickedProvider,
		private AddToCartClickedProvider $addToCartClickedProvider,
		private BannerClickedProvider $bannerClickedProvider,
		private MenuClickedProvider $menuClickedProvider,
		private FilterClickedProvider $filterClickedProvider,
		private FilterRangeClickedProvider $filterRangeClickedProvider,
	)
	{
	}

	public function onPageView(PageView $pageView): void
	{
		if (!$this->httpRequest->isAjax()) {
			$this->tagManager->add(new GTMPageView($pageView), TagManager::RENDER_POSITION_TOP);
		}
	}

	public function onProductDetailView(ProductDetailView $productDetailView): void
	{
		if (!$this->httpRequest->isAjax()) {
			$this->tagManager->add(new GTMViewItem($productDetailView));
		}
	}

	public function onProductClicked(ProductClicked $productClicked): void
	{
		if (!$this->httpRequest->isAjax()) {
			$this->tagManager->add(new GTMSelectItem($productClicked));
		}
		$this->productClickedProvider->flush();
	}

	public function onAddToCart(AddToCart $addToCart): void
	{
		$this->tagManager->add(new GTMAddToCart($addToCart));
		$this->addToCartClickedProvider->flush();
	}

	public function onRemoveFromCart(RemoveFromCart $removeFromCart): void
	{
		$this->tagManager->add(new GTMRemoveFromCart($removeFromCart));
	}

	public function onViewCart(ViewCart $viewCart): void
	{
		$this->tagManager->add(new GTMViewCart($viewCart));
	}

	public function onBeginCheckout(BeginCheckout $beginCheckout): void
	{
		if (!$this->httpRequest->isAjax()) {
			$this->tagManager->add(new GTMBeginCheckout($beginCheckout));
		}
	}

	public function onAddShippingInfo(AddShippingInfo $addShippingInfo): void
	{
		$this->tagManager->add(new GTMAddShippingInfo($addShippingInfo));
	}

	public function onAddPaymentInfo(AddPaymentInfo $addPaymentInfo): void
	{
		$this->tagManager->add(new GTMAddPaymentInfo($addPaymentInfo));
	}

	public function onOrderSubmit(OrderSubmit $orderSubmit): void
	{
		// send order submited
		if (!($orderSubmit->order->metadata->gtm['order_submit'] ?? false) && !$this->httpRequest->isAjax()) {
			$this->tagManager->add(new GTMOrderSubmit());
			$orderSubmit->order->setMetadata(['gtm' => ['order_submit' => true]]);
		}

		// if order has COD or is paid send GTM event purchase
		$paymentMethodIdentifier = $orderSubmit->order->getPayment()->paymentMethod->paymentMethodUniqueIdentifier;
		if (!($orderSubmit->order->metadata->gtm['purchase'] ?? false) && ( in_array($paymentMethodIdentifier, [CashOnDelivery::ID, BankTransfer::ID]) || $orderSubmit->order->isPaid())) {
			$this->tagManager->add(new GTMPurchase($orderSubmit));
			$orderSubmit->order->setMetadata(['gtm' => ['purchase' => true]]);
		}

		$this->orm->persistAndFlush($orderSubmit->order);
	}

	public function onProductListView(ProductListView $productListView): void
	{
		$this->tagManager->add(new GTMViewItemList($productListView));
	}

	public function onViewPromotion(ViewPromotion $viewPromotion): void
	{
		$this->tagManager->add(new GTMViewPromotion($viewPromotion));
	}

	public function onBannerClicked(BannerClicked $bannerClicked): void
	{
		if (!$this->httpRequest->isAjax()) {
			$this->tagManager->add(new GTMSelectPromotion($bannerClicked));
		}
		$this->bannerClickedProvider->flush();
	}

	public function onRegistered(Registered $registered): void
	{
		if (!$this->httpRequest->isAjax()) {
			$this->tagManager->add(new GTMRegistrationComplet($registered));
		}
		$this->httpResponse->deleteCookie('registrationComplete');
	}

	public function onMenuClicked(MenuClicked $menuClicked): void
	{
		if (!$this->httpRequest->isAjax()) {
			$this->tagManager->add(new GTMMenuClick($menuClicked));
		}
		$this->menuClickedProvider->flush();
	}

	public function onFilterClicked(FilterClicked $filterClicked): void
	{
		$this->tagManager->add(new GTMFilter($filterClicked));
		$this->filterClickedProvider->flush();
		$this->filterRangeClickedProvider->flush();
	}

	public function onSearchView(SearchClick $searchView): void
	{
		$this->tagManager->add(new GTMWhisperer($searchView));
	}

	public function onSearchResults(SearchResuls $searchResuls): void
	{
		$this->tagManager->add(new GTMSearchResults($searchResuls));
	}

	public function onAddToWishlist(AddToWishlist $addToWishlist): void
	{
		if ($this->httpRequest->isAjax()) {
			$this->tagManager->add(new GTMAddToWishlist($addToWishlist));
		}
	}

	public static function getSubscribedEvents(): array
	{
		return [
			PageView::class => [
				['onPageView', 0],
			],
			ProductClicked::class => [
				['onProductClicked', 0],
			],
			ProductDetailView::class => [
				['onProductDetailView', 10],
			],
			AddToCart::class => [
				['onAddToCart', 0],
			],
			RemoveFromCart::class => [
				['onRemoveFromCart', 0],
			],
			ViewCart::class => [
				['onViewCart', 0],
			],
			BeginCheckout::class => [
				['onBeginCheckout', 0],
			],
			AddShippingInfo::class => [
				['onAddShippingInfo', 0],
			],
			AddPaymentInfo::class => [
				['onAddPaymentInfo', 0],
			],
			OrderSubmit::class => [
				['onOrderSubmit', 0],
			],
			ProductListView::class => [
				['onProductListView', 0],
			],
			ViewPromotion::class => [
				['onViewPromotion', 0],
			],
			BannerClicked::class => [
				['onBannerClicked', 0],
			],
			Registered::class => [
				['onRegistered', 0],
			],
			MenuClicked::class => [
				['onMenuClicked', 0],
			],
			FilterClicked::class => [
				['onFilterClicked', 0],
			],
			SearchClick::class => [
				['onSearchView', 0],
			],
			SearchResuls::class => [
				['onSearchResults', 0],
			],
			AddToWishlist::class => [
				['onAddToWishlist', 0],
			],
		];
		// TODO add to library (knihovny)
	}

}
