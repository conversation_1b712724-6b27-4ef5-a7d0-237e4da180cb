<?php

declare(strict_types=1);

namespace App\Model\TagManager;

interface RenderableTag
{

	public function getEventName(): string;
	public function getData(): array;
	public function getRendererName(): string;

	/**
	 * Return string value of "data-gtm-viewport-script-id-value" with data-controller="gtm-viewport" of element, witch has been fired when element is really visible on page
	 * Example of element what going on: <div data-controller="gtm-viewport" data-gtm-viewport-script-id-value="_dom_element_id_">some content</div>
	 *
	 * Return null if tag will fire immediately
	 */
	public function getDomElementId(): ?string;

}
