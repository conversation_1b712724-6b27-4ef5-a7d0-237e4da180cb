<?php

declare(strict_types=1);

namespace App\Model\TagManager\Renderer;

use function array_values;
use function sprintf;

final class RendererRegistry
{

	/** @var array<string, Renderer> */
	private array $renderers = [];

	/**
	 * @param Renderer[] $renderers
	 */
	public function __construct(
		array $renderers,
	)
	{
		foreach ($renderers as $renderer) {
			$this->renderers[$renderer::class] = $renderer;
		}
	}

	/**
	 * @return list<Renderer>
	 */
	public function list(): array
	{
		return array_values($this->renderers);
	}

	public function get(string $className): Renderer
	{
		return $this->renderers[$className] ?? throw new \InvalidArgumentException(sprintf('Renderer "%s" not found.', $className));
	}

}
