{varType App\Model\TagManager\RenderableTag $tag}
{var $data = $tag->getData()}
{var $jsData = \Nette\Utils\Json::encode($data)}
<script type="application/javascript">
	exponea.identify({$jsData|noescape});
	{if $debug}
		console.warn('render bloomreach identify event - ' + {$tag->getEventName()}, {$jsData|noescape});
		{*php bdump($data, 'rendered event - ' . $tag->getEventName())*}
	{/if}
</script>

