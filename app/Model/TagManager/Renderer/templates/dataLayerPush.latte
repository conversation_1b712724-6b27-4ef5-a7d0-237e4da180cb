{varType App\Model\TagManager\RenderableTag $tag}
{var $data = $tag->getData()}
{var $jsData = \Nette\Utils\Json::encode($data)}
<script {if $tag->getDOMElementId() === null}type="application/javascript"{else}type="text/plain" data-element-view-id="{$tag->getDOMElementId()??'-'}"{/if}>
	window.dataLayer = window.dataLayer || [];
	window.dataLayer.push({$jsData|noescape});
	{if $debug}
		console.warn('render event - ' + {$tag->getEventName()}, {$jsData|noescape});
		{*php bdump($data, 'rendered event - ' . $tag->getEventName())*}
	{/if}
</script>
