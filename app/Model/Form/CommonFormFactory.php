<?php declare(strict_types=1);

namespace App\Model\Form;

use App\Model\TranslatorDB;
use Nette\Application\UI\Form as UIForm;
use Nette\Forms\Form;
use Nette\Localization\Translator;

class CommonFormFactory
{
	public function __construct(
		private readonly TranslatorDB $translatorDB,
		private readonly FormDataLogger $formDataLogger,
	)
	{
	}

	/**
	 * @template F of Form
	 * @param class-string<F> $formClass
	 * @return F
	 */
	public function create(
		string $formClass = UIForm::class,
		Translator|null $translator = null,
	): Form
	{
		$form = new $formClass();
		$form->setTranslator($translator ?? $this->translatorDB);
		$form->onSuccess[] = $this->formDataLogger->logData(...);

		return $form;
	}
}
