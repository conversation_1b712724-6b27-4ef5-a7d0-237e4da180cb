<?php

declare(strict_types=1);

namespace App\Model\Form;

use Nette\Forms\Controls\BaseControl;
use Nette\Forms\Form;
use Nette\Http\Helpers;
use Nette\Http\IRequest;
use Nextras\Dbal\Connection;

final class FormThrottler
{
	private const INTERVAL_IN_MINUTES = 5;

	private Form|null $throttledForm = null;

	public function __construct(
		private readonly Connection $connection,
		private readonly IRequest $httpRequest,
		private readonly int $gcProbabilityPercentage = 5,
	) {}

	public function throttleFormSubmissions(
		Form $form,
		string $formType,
		string $discriminatorName,
	): void
	{
		$ipAddress = $this->httpRequest->getRemoteAddress();
		if ($ipAddress === null) {
			return;
		}

		$form->onValidate[] = function (Form $form) use ($formType, $discriminatorName, $ipAddress): void {
			if (random_int(0, 100) < $this->gcProbabilityPercentage) {
				$this->connection->query('DELETE FROM form_failed_submission WHERE attempted_at < DATE_SUB(NOW(), INTERVAL %i MINUTE)', self::INTERVAL_IN_MINUTES);
			}

			/** @var BaseControl $discriminatorControl */
			$discriminatorControl = $form[$discriminatorName];
			$discriminator = $discriminatorControl->getValue();
			if ($this->isThrottled($formType, $discriminator, $ipAddress)) {
				$form->addError('suspicious_activity_try_again_later');
				$this->throttledForm = $form;
			}
		};

		$form->onError[] = function (Form $form) use ($formType, $discriminatorName, $ipAddress): void {
			if ($this->throttledForm === $form) {
				// return;
			}

			// record failed submission
			$this->logSubmissionAttempt($form, $formType, $discriminatorName, $ipAddress);
		};
	}

	public function logSubmissionAttempt(Form $form, string $formType, string $discriminatorName, ?string $ipAddress = null): void
	{
		/** @var BaseControl $discriminatorControl */
		$discriminatorControl = $form[$discriminatorName];
		$discriminator = $discriminatorControl->getValue();

		if ($ipAddress === null) {
			$ipAddress = $this->httpRequest->getRemoteAddress();
			if ($ipAddress === null) {
				return;
			}
		}

		$this->connection->query(
			'INSERT INTO form_failed_submission (form_type, discriminator, ip_address, attempted_at) VALUES (%s, %s, INET6_ATON(%s), NOW())',
			$formType,
			$discriminator,
			$ipAddress,
		);
	}

	private function isThrottled(
		string $formType,
		string $discriminator,
		string $ipAddress,
	): bool
	{
		$now = new \DateTimeImmutable();

		$failedSubmissions = $this->connection->query(<<<SQL
			SELECT discriminator, INET6_NTOA(ip_address) AS ip_address, attempted_at
			FROM form_failed_submission
			WHERE form_type = %s AND attempted_at > DATE_SUB(NOW(), INTERVAL %i MINUTE)
			ORDER BY attempted_at DESC
SQL,
			$formType,
			self::INTERVAL_IN_MINUTES,
		);

		$count = $failedSubmissions->count();
		if ($count === 0) {
			return false;
		}

		$totalScore = 0;
		$lastAttemptAtAll = null;
		$lastMatchingAttempt = null;

		foreach ($failedSubmissions as $failedSubmission) {
			// calculate a score based on...
			$score = 1;

			// ...whether it was a direct match for discriminator
			if ($failedSubmission->discriminator === $discriminator) {
				$lastMatchingAttempt ??= $failedSubmission->attempted_at;
				$score += 2;
			}

			// ...whether the IP is a match
			if (Helpers::ipMatch($failedSubmission->ip_address, $ipAddress)) {
				$lastMatchingAttempt ??= $failedSubmission->attempted_at;
				$score += 2;
			}

			// ...how recent the failed attempt was
			$score *= max(self::INTERVAL_IN_MINUTES - $now->diff($failedSubmission->attempted_at, absolute: true)->i, 0) / self::INTERVAL_IN_MINUTES;

			$totalScore += $score;
			$lastAttemptAtAll ??= $failedSubmission->attempted_at;
		}

		$avgScore = $totalScore / $count;

		// this is a proof of concept and will most likely require some fine-tuning
		$delayInSeconds = match (true) {
			$avgScore > 2 => 4,
			$avgScore > 1 => 2,
			$avgScore > 0.5 => 1,
			default => 0,
		};

		$lastAttempt = $lastMatchingAttempt ?? $lastAttemptAtAll;
		return $lastAttempt !== null && $lastAttempt > $now->modify('-' . $delayInSeconds . ' seconds');
	}
}
