<?php declare(strict_types=1);

namespace App\Model\Comparators;

use App\Utils\DateTime;
use Nette\Http\Request;
use Nette\Http\Response;

final class ComparatorModeProvider
{
	private const string COOKIE_NAME = 'comparatorMode';
	private const string GET_PARAM = 'ppc';

	public bool $isActive = false {
		get => $this->get() || $this->isActive;
		set(bool $value) => $this->isActive = $value;
	}

	public function __construct(
		private readonly Request $httpRequest,
		private readonly Response $httpResponse,
	)
	{
	}

	public function init(): void
	{
		if ($this->httpRequest->getQuery(self::GET_PARAM) !== null) {
			// save cookie
			$this->set();
		}
	}

	private function set(): void
	{
		$this->httpResponse->setCookie(self::COOKIE_NAME, '1', DateTime::from('tomorrow')->modify('-1 second'));
		$this->isActive = true;
	}

	private function get(): bool
	{
		return $this->httpRequest->getCookie(self::COOKIE_NAME) !== null;
	}

}
