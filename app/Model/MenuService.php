<?php

declare(strict_types = 1);

namespace App\Model;

use App\Model\ElasticSearch\Product\Repository;
use App\Model\Orm\BaseEntity;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\ProductLocalization\ProductLocalizationRepository;
use App\Model\Orm\Routable;
use App\Model\StaticPage\StaticPage;
use App\PostType\MenuMain\Model\Orm\MenuMainLocalization\MenuMainLocalization;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\Tree;
use Nette\Caching\Cache;
use Nette\Caching\Storage;
use Nette\Http\IRequest;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\IEntity;
use stdClass;
use App\Model\CustomField\LazyValue;

final class MenuService
{
	private static array $staticCache = [];

	public function __construct(
		private readonly Orm $orm,
	)
	{
	}

	/**
	 * @return ICollection<Tree>
	 */
	private function loadMenuItems(int $parent, array $hide): ICollection
	{
		$key = md5(serialize(func_get_args()));
		if (!isset(self::$staticCache['menuItems'][$key])) {
			$where = [
				'parent' => $parent,
				'public' => 1,
			];
			if ($hide !== []) {
				$where['uid!='] = $hide;
			}

			self::$staticCache['menuItems'][$key] = $this->orm->tree->findBy($where)->orderBy('sort');
		}
		return self::$staticCache['menuItems'][$key];
	}

	/**
	 * @param ICollection<MenuMainLocalization>|null $menuItemEntities
	 */
	public function getMenu(Mutation $mutation, int $parent, Routable|StaticPage|null $object, ICollection|null $menuItemEntities = null, bool $markSelected = true, array $hide = [], IRequest|null $request = null): array
	{
		$menu = $this->createMenu($mutation, $parent, $object, $menuItemEntities, $markSelected, $hide, $request);
		$this->updateActiveForMainMenu($menu);

		return $menu;
	}
	/**
	 * @param ICollection<MenuMainLocalization>|null $menuItemEntities
	 */
	public function createMenu(Mutation $mutation, int $parent, Routable|StaticPage|null $object, ICollection|null $menuItemEntities = null, bool $markSelected = true, array $hide = [], IRequest|null $request = null, ?int $level = 0): array
	{
		$menu = [];

		if (empty($menuItemEntities)) {
			$menuItemEntities = $this->loadMenuItems($parent, $hide);
		}

		if ($object === null) {
			return $menu;
		}



		/** @var BaseEntity $menuItemEntity */
		foreach ($menuItemEntities as $menuItemEntity) {

			$menuItem = null;
			$key = $menuItemEntity::class . '-' . $menuItemEntity->id;

			if ($menuItemEntity instanceof MenuMainLocalization) {

				self::$staticCache['mainMenuObjects'][] = $menuItemEntity->getPage()?->id;
				if (!isset(self::$staticCache[$key])) {
					$tempItem = $this->processMenuItem($mutation, $menuItemEntity, $object, $markSelected, $request);
					if ($tempItem !== null) {
						$tempItem->menuItem = true;
						self::$staticCache[$key] = $tempItem;
					}

				}
				if (isset(self::$staticCache[$key])) {
					$menuItem = self::$staticCache[$key];
				}
			}

			if ($menuItemEntity instanceof CatalogTree) {
				if (!isset(self::$staticCache[$key])) {
					$tempItem  = $this->processCatalogTreeItem($mutation, $menuItemEntity, $object, $markSelected);
					if ($tempItem !== null) {
						self::$staticCache[$key] = $tempItem;
					}
				}

				if (isset(self::$staticCache[$key])) {
					$menuItem = self::$staticCache[$key];
				}
			}

			if ($menuItem !== null) {
				$menu[] = $menuItem;
			}
		}

		return $this->inspectFlagIsActive($menu, $request);
	}

	private function inspectFlagIsActive(array $menu, IRequest|null $request): array
	{
		if ($request === null){
			return $menu;
		}

		foreach ($menu as $menuItem){

			if (!isset($menuItem->page)){
				continue;
			}


			if(!in_array($request->getUrl()->getpath(), self::$staticCache['customHrefDestinations'] ?? [], true)){
				continue;
			}

			$menuItem->active = false;// @phpstan-ignore-line
			$menuItem->selected = false;// @phpstan-ignore-line
		}

		return $menu;
	}

	public function updateActiveForMainMenu(array &$menu): void
	{
		$active = [];
		foreach ($menu as $index => $item){
			if (isset($item->menuItem) && isset($item->active) && $item->active) {
				$active[$index] = true;
			}
		}

		$i = 1;
		foreach ($active as $index => $path){
			if ($i < count($active)) {
				$menu[$index]->active = false;
			}
			$i++;
		}
	}

	private function processCatalogTreeItem(Mutation $mutation, CatalogTree $page, Routable|StaticPage|null $object, bool $markSelected = true): stdClass|null
	{
		if ($object === null) {
			return null;
		}

		$menuItem = new stdClass();

		$menuItem->active = $this->isActive($object, $page);
		$menuItem->selected = $this->isSelected($object, $page, $markSelected);
		$menuItem->hasSubmenu = $this->hasSubmenu($page);
		$menuItem->page = $page;

		if ($menuItem->active || $menuItem->selected) {
			$menuItem->submenu = $this->createMenu($mutation, $page->getId(), $object, null, $markSelected, level: 1);
		}

		return $menuItem;
	}

	private function hasSubmenu(CatalogTree $page): bool
	{
		return $this->orm->tree->findBy([
				'parent' => $page->id,
				'public' => 1,
			])->count() > 0;
	}

	private function processMenuItem(Mutation $mutation, MenuMainLocalization $menuItemEntity, Routable|StaticPage|null $object, bool $markSelected = true, IRequest|null $request = null): stdClass|null
	{
		$menuItem = new stdClass();
		$menuItem->active = false;
		$menuItem->selected = false;
		$menuItem->isBig = $menuItemEntity->isBig ?? false;

		if (!isset($menuItemEntity->menuMain->cf->link)) {
			return null;
		}

		$menuItem->icon = $menuItemEntity->menuMain->cf->settings->icon ?? null;
		$menuItem->color = $menuItemEntity->menuMain->cf->settings->color ?? null;
		$menuItem->categories = $menuItemEntity->menuMain->cf->submenu->categories ?? [];
		foreach ($menuItem->categories as $key => $row) {
			if(isset($row->link->systemHref->page) && $row->link->systemHref->page instanceof LazyValue && $row->link->systemHref->page->getEntity() === null){
				unset($menuItem->categories[$key]);
			}
		}
		$menuItem->side = $menuItemEntity->menuMain->cf->submenu->side ?? false;

		if ($menuItemEntity->menuMain->cf->link->toggle === 'customHref') {
			self::$staticCache['customHrefDestinations'][] = $menuItemEntity->menuMain->cf->link->customHref->href;
			$menuItem->isCustomHref = true;

			$isStaticPage = $request !== null && $request->getUrl()->getPath() === $menuItemEntity->menuMain->cf->link->customHref->href;
			$menuItem->active = $isStaticPage;
			$menuItem->selected = $isStaticPage;
			$menuItem->name = $menuItemEntity->name ?? null;
			$menuItem->link = $menuItemEntity->menuMain->cf->link->customHref->href;

			return $menuItem;
		}

		if (!isset($menuItemEntity->menuMain->cf->link->systemHref)) {
			return null;
		}

		if (($page = $menuItemEntity->getPage()) === null) {
			return null;
		}

		$menuItem->page = $page;
		$menuItem->name = $menuItemEntity->name ?? null;
		$menuItem->active = $this->isActive($object, $page);
		$menuItem->selected = $this->isSelected($object, $page, $markSelected);

		//if ($menuItem->active || $menuItem->selected || true) { // MenuMain must load submenu always
			$menuItem->submenu = $this->createMenu($mutation, $page->getId(), $object, null, $markSelected, level: 1);
		//}

		return $menuItem;
	}

	private function isSelected(Routable|StaticPage|null $object, Routable $page, bool $markSelected): bool
	{
		if (!$markSelected) {
			return false;
		}

		if (!($object instanceof Routable)) {
			return false;
		}

		if (!isset($object->path)) {
			return false;
		}

		if (!in_array($page->getId(), $object->path, true) && $page->getId() !== $object->getId()) {
			return false;
		}

		if ($page->getId() !== $object->getId()) {
			return false;
		}

		return true;
	}

	private function isActive(Routable|StaticPage|null $object, Routable $page): bool
	{
		// Na detailu produktu, nedrzime stav otvorene kategorie, iba MainMenu kategorii
		if ($object instanceof ProductLocalization && !in_array($page->getId(), self::$staticCache['mainMenuObjects'])) {
			return false;
		}

		if (!isset($object->path)) {
			return false;
		}

		if (!in_array($page->getId(), $object->path, true) && $page->getId() !== $object->getId()) {
			return false;
		}

		return true;
	}

	public function getUserMenu(Routable|StaticPage $object, bool $markSelected = true, array $items = []): array
	{
		$menu = [];

		if ($items === []) {
			return $menu;
		}

		foreach ($items as $item) {
			$menuItem = new stdClass();
			$menuItem->page = $item;
			$menuItem->active = false;
			$menuItem->selected = false;

			if (isset($item->link->systemHref->page->entity)) {
				$item = $item->link->systemHref->page->entity;
			}

			if (isset($item->id) && in_array($item->id, (array) $object->getPath(), true)) {
				$menuItem->active = true;
			}

			if (isset($item->id) && $markSelected && $item->id == $object->getId()) {
				$menuItem->selected = true;
			}

			$menu[] = $menuItem;
		}
		return $menu;
	}

}
