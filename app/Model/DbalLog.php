<?php

declare(strict_types=1);

namespace App\Model;

use Nette\Database\Connection;
use Nette;
use Nextras\Dbal\ILogger;
use Nextras\Dbal\Result\Result;
use App\Model\ConfigService;
use Tracy\Debugger;

final class DbalLog implements ILogger
{

	use Nette\SmartObject;

	public function __construct(
		private readonly string $absPath,
		private readonly string $file,
		private readonly \Nextras\Dbal\Connection $db,
		private readonly ConfigService $configService,
		private readonly Nette\Http\Request $httpRequest,
	) {}

	public function register(): void
	{
		if ($this->configService->get('dbalLog')) {
			if ( ! $this->httpRequest->isAjax()) {
				$this->purgeLog();
			}
			$this->db->addLogger($this);
		}
	}


    private function purgeLog(): void
    {
        $file = $this->absPath . $this->file . ".log";
        if (file_exists($file)) {
            unlink($file);
        }
    }

	public function onConnect(): void
	{
	}

	public function onDisconnect(): void
	{
	}

	public function onQuery(string $sqlQuery, float $timeTaken, ?Result $result): void
	{
		if ($this->httpRequest->isAjax()) {
			$timeTaken = '[Ajax] '. $timeTaken;
		}

		Debugger::log($timeTaken . '--' . $sqlQuery, $this->file);
	}

	public function onQueryException(string $sqlQuery, float $timeTaken, ?\Nextras\Dbal\Drivers\Exception\DriverException $exception): void
	{
		Debugger::log('Exception ' . $exception->getMessage());
		Debugger::log($sqlQuery, $this->file);
	}
}
