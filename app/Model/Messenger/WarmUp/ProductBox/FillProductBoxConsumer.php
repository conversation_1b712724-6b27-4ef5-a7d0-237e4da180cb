<?php declare(strict_types=1);

namespace App\Model\Messenger\WarmUp\ProductBox;

use App\Model\DTO\Product\ProductDtoProvider;
use App\Model\Mutation\MutationHolder;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Orm;
use App\Model\Setup;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Throwable;

#[AsMessageHandler]
final readonly class FillProductBoxConsumer
{

	public function __construct(
		private Orm $orm,
		private MutationsHolder $mutationsHolder,
		private MutationHolder $mutationHolder,
		private ProductDtoProvider $productDtoProvider,
	)
	{
	}

	public function __invoke(FillProductBoxMessage $message): void
	{
		$this->orm->reconnect();

		$defaultMutation = $this->mutationsHolder->getDefault();

		$this->mutationHolder->setMutation($defaultMutation);
		$this->orm->setMutation($defaultMutation);
		$this->orm->setPublicOnly(false);

		try {
			$product = $this->orm->product->getById($message->getProductId());

			if ($product === null) {
				return;
			}
			$priceLevel = $this->orm->priceLevel->getDefault();
			$state = $this->orm->state->getDefault($defaultMutation);

			$setup = new Setup(
				$defaultMutation,
				$state,
				$priceLevel,
				null
			);
			$this->productDtoProvider->setSetup($setup);
			foreach ($product->variants as $variant) {
				$this->productDtoProvider->get($product, $variant);
			}
		} catch (Throwable) {
			// do nothing
		}
	}

}
