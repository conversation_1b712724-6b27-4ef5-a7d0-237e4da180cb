<?php
declare(strict_types = 1);

namespace App\Model\Messenger;

use App\Exceptions\LogicException;
use Nette\Http\UrlScript;
use Nette\NotImplementedException;

class MessengerDsn {

	private string $redisStorageKey = 'messages';
	public function __construct(
		private readonly array $redisConfig = [],
		private readonly array $amqpConfig = [],
	)
	{
	}


	public function sync(): string
	{
		return 'sync://';
	}

	/**
	 * Redis DSN example string: redis://%redis.password%@%redis.host%:%redis.port%/%redis.messages.prefix%_import_stock?delete_after_ack=true&auto_setup=true&serializer=1&stream_max_entries=0&lazy=true&dbindex=%redis.messages.database%
	 */
	public function redis(string $listName, array $options = []): string
	{
		$dsnParts = [];
		$dsnParts[] = 'redis://';
		$dsnParts[] = $this->redisConfig['password'] . '@' . $this->redisConfig['host'];
		$dsnParts[] = '/' . $this->redisConfig[$this->redisStorageKey]['prefix'] . '_';
		$dsnParts[] = $listName;

		foreach ($this->defaultRedisOptions() as $key => $defaultOption)
		{
			if (!array_key_exists($key, $options)) {
				$options[$key] = $defaultOption;
			}
		}

		$dsnParts[] = '?' . http_build_query($options);

		$dsn = implode('', $dsnParts);

		return $this->checkDsn($dsn);
	}

	public function amqp(string $listName, array $options = []): string {
		$dsnParts = [];
		$dsnParts[] = 'amqp://';
		$dsnParts[] = rawurlencode($this->amqpConfig['username']) . ':';
		$dsnParts[] = rawurlencode($this->amqpConfig['password']) . '@' . $this->amqpConfig['host'];
		if (isset($this->amqpConfig['port'])) {
			$dsnParts[] = ':' . $this->amqpConfig['port'];
		}

		if (!isset($this->amqpConfig['vhost'])) {
			$dsnParts[] = '/%2f/';
		} else {
			$dsnParts[] = '/' . $this->amqpConfig['vhost'] . '/';
		}

		$dsnParts[] = $listName;

		if ($options !== []) {
			$dsnParts[] = '?' . http_build_query($options);
		}

		$dsn = implode('', $dsnParts);
		return $this->checkDsn($dsn);
	}

	private function checkDsn(string $dsn): string
	{
		if (parse_url($dsn) === false) {
			throw new LogicException('The given DSN is invalid.');
		}
		return $dsn;
	}

	private function defaultRedisOptions(): array
	{
		$options = [];
		$options['delete_after_ack'] = 'true';
		$options['auto_setup'] = 'true';
		$options['serializer'] = 1;
		$options['stream_max_entries'] = 0;
		$options['lazy'] = 'true';
		$options['dbindex'] = $this->redisConfig[$this->redisStorageKey]['database'];

		return $options;
	}

	public function setRedisStorageKey(string $redisStorageKey): MessengerDsn
	{
		$this->redisStorageKey = $redisStorageKey;
		return $this;
	}
}
