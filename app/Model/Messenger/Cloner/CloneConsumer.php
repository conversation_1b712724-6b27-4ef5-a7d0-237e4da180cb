<?php declare(strict_types = 1);

namespace App\Model\Messenger\Cloner;

use App\Model\Cloner\ClonerProvider;
use App\Model\ElasticSearch\All\Facade;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Mutation\MutationRepository;
use App\Model\Orm\Orm;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\PostType\Page\Model\Orm\Tree;
use Nextras\Orm\Entity\IEntity;
use Nextras\Orm\Repository\IRepository;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Tracy\Debugger;


#[AsMessageHandler]
class CloneConsumer
{

	public function __construct(
		private readonly Orm $orm,
		private readonly ClonerProvider $clonerProvider,
		private readonly MutationRepository $mutationRepository,
		private readonly Facade $esAllFacade,
	)
	{
	}

	public function __invoke(CloneMessage $message): void
	{
		try {
			/** @var class-string<IRepository> $className */
			$className = $message->getClass(); // @phpstan-ignore-line
			$repository = $this->orm->getRepository($className); // @phpstan-ignore-line
			$targetMutation = $this->mutationRepository->getByIdChecked($message->getMutationId());

			if (method_exists($repository, 'setPublicOnly')) {
				$repository->setPublicOnly(false);
			}

			$entity = $repository->getByIdChecked($message->getId());
			$newEntity = $this->clone($entity, $targetMutation);

			$repository->persistAndFlush($newEntity);

			if ($newEntity instanceof Tree && $newEntity->uid === Tree::UID_TITLE) {
				// add alias for HomePage
				$newEntity->setAlias('');
			}

			if ($newEntity instanceof ProductLocalization) {
				$this->esAllFacade->update($newEntity->getParent());
			} else {
				$this->esAllFacade->update($newEntity);

			}
			$repository->doClear();
		} catch (\Throwable $e) {
			Debugger::log($e, Debugger::EXCEPTION);
			throw $e;
		}
	}


	private function clone(IEntity $entity, Mutation $targetMutation): IEntity
	{

		$cloner = $this->clonerProvider->getClonerByEntity($entity);
		return $cloner->clone($entity, $targetMutation);
	}

}
