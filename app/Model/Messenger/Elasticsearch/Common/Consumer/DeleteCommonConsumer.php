<?php declare(strict_types = 1);

namespace App\Model\Messenger\Elasticsearch\Common\Consumer;

use App\Model\Orm\Orm;
use App\Model\ElasticSearch\Common\ElasticCommon;
use App\Model\ElasticSearch\Service;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Messenger\Elasticsearch\Common\Message\DeleteCommonMessage;
use App\Model\Messenger\Elasticsearch\ConsumerHelper;
use LogicException;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Throwable;

#[AsMessageHandler]
class DeleteCommonConsumer extends Consumer
{

	public function __construct(
		private EsIndexRepository $esIndexRepository,
		private Service $elasticService,
		private ConsumerHelper $consumerHelper,
		Orm $orm,
	)
	{
		parent::__construct(
			$orm,
		);
	}

	public function __invoke(DeleteCommonMessage $message): void
	{
		$this->orm->reconnect();
		$esIndex = $this->esIndexRepository->getById($message->getEsIndexId());

		if ($esIndex === null) {
			throw new LogicException(sprintf('EsIndex %s not found', $message->getEsIndexId()));
		}

		try {
			$object = $this->getObjectByClass($message->getClass(), $message->getId());
			$elasticProduct = new ElasticCommon($object);
			$this->elasticService->replaceDoc($esIndex, $elasticProduct);
		} catch (Throwable $e) {
			$this->consumerHelper->handleError($esIndex, $e, $message->getId(), $message->getClass());
		}
	}

}
