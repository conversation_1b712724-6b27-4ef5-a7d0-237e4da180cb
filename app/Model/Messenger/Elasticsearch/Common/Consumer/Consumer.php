<?php declare(strict_types = 1);

namespace App\Model\Messenger\Elasticsearch\Common\Consumer;

use App\Model\Orm\Orm;
use Nextras\Orm\Entity\IEntity;

abstract class Consumer
{

	public function __construct(
		protected readonly Orm $orm,
	)
	{
	}

	protected function getObjectByClass(string $class, int $objectId): ?object
	{
		$this->orm->setPublicOnly(false);

		return $this->orm->getRepositoryForEntity($class)->getById($objectId); // @phpstan-ignore-line
	}

}
