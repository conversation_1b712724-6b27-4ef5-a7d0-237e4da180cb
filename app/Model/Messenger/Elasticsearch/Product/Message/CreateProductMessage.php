<?php declare(strict_types = 1);

namespace App\Model\Messenger\Elasticsearch\Product\Message;

use App\Model\Orm\EsIndex\EsIndex;

class CreateProductMessage
{

	private int $esIndexId;

	public function __construct(
		private int $productId,
		EsIndex $esIndex,
		private array $signals = [],
	)
	{
		$this->esIndexId = $esIndex->id;
	}

	public function getEsIndexId(): int
	{
		return $this->esIndexId;
	}

	public function getId(): int
	{
		return $this->productId;
	}

	public function getSignals(): array
	{
		return $this->signals;
	}

}
