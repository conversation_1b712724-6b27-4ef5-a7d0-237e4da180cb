<?php declare(strict_types = 1);

namespace App\Model\Messenger\Elasticsearch\Product\Consumer;

use App\Model\ElasticSearch\Product\ConvertorProvider;
use App\Model\ElasticSearch\Product\ElasticProduct;
use App\Model\ElasticSearch\Service;
use App\Model\Messenger\Elasticsearch\ConsumerHelper;
use App\Model\Messenger\Elasticsearch\Product\Message\QuickReplaceProductMessage;
use App\Model\Messenger\Elasticsearch\Product\Message\ReplaceProductMessage;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\EsIndex\EsIndex;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Orm\Orm;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductRepository;
use App\Model\Orm\ProductLocalization\ProductLocalizationRepository;
use App\Model\TranslatorDB;
use Contributte\Monolog\LoggerManager;
use LogicException;
use Psr\Log\LoggerInterface;
use Throwable;
use Tracy\Debugger;

class BaseReplaceProductConsumer
{
	private LoggerInterface $logger;
	public function __construct(
		private readonly EsIndexRepository $esIndexRepository,
		private readonly ProductRepository $productRepository,
		private readonly ProductLocalizationRepository $productLocalizationRepository,
		private readonly ConvertorProvider $convertorProvider,
		private readonly Service $elasticService,
		private readonly ConsumerHelper $consumerHelper,
		private readonly MutationHolder $mutationHolder,
		private readonly Orm $orm,
		private readonly TranslatorDB $translatorDB,
		private readonly LoggerManager $loggerManager,
	)
	{
		$this->logger = $this->loggerManager->get('elasticReplace');
	}

	protected function consume(QuickReplaceProductMessage|ReplaceProductMessage $message): void
	{
		$this->logger->info('start', ['message' => $message, 'convertorsCount' => count($message->getConvertors())]);
		$this->orm->reconnect();
		$this->productRepository->setPublicOnly(false);
		$this->productLocalizationRepository->setPublicOnly(false);

		$product = $this->productRepository->getById($message->getId());
		$esIndex = $this->esIndexRepository->getById($message->getEsIndexId());

		if ($esIndex === null) {
			throw new LogicException(sprintf('EsIndex %s not found', $message->getEsIndexId()));
		}

		$this->translatorDB->setMutation($esIndex->mutation);

		$esIndexes = [$esIndex];
		$newerEsIndex = $this->esIndexRepository->getBy(['type' => $esIndex->type, 'mutation->id' => $esIndex->mutation->id, 'createdTime>' => $esIndex->createdTime]);
		if ($newerEsIndex !== null) {
			$esIndexes[] = $newerEsIndex;
		}

		/** @var EsIndex $esIndex */
		foreach ($esIndexes as $esIndex) {
			try {
				if ($product === null) {
					throw new LogicException(sprintf('Product %s not found', $message->getId()));
				}

				$this->orm->setMutation($esIndex->mutation);
				$this->mutationHolder->setMutation($esIndex->mutation);

				$convertors = array_map(function ($convertorClass) {
					return $this->convertorProvider->get($convertorClass);
				}, $message->getConvertors());

				foreach ($product->variants as $variant) {
					$elasticProduct = new ElasticProduct($product, $variant, $convertors);
					$this->elasticService->replaceDoc($esIndex, $elasticProduct);
				}

				if (($signals = $message->getSignals()) !== []) {
					$this->consumerHelper->handleSignals($esIndex, $signals);
				}
			} catch (Throwable $e) {
				Debugger::log($e);
				$this->consumerHelper->handleError($esIndex, $e, $message->getId(), Product::class);
			}
		}
		$this->logger->info('end');
	}

}
