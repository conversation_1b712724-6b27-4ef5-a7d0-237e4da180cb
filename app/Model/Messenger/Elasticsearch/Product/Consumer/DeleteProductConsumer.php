<?php declare(strict_types = 1);

namespace App\Model\Messenger\Elasticsearch\Product\Consumer;

use App\Model\ElasticSearch\Product\ElasticProduct;
use App\Model\ElasticSearch\Service;
use App\Model\Messenger\Elasticsearch\ConsumerHelper;
use App\Model\Messenger\Elasticsearch\Product\Message\DeleteProductMessage;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Orm\Orm;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductRepository;
use App\Model\TranslatorDB;
use LogicException;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Throwable;

#[AsMessageHandler]
readonly class DeleteProductConsumer
{

	public function __construct(
		private EsIndexRepository $esIndexRepository,
		private ProductRepository $productRepository,
		private Service $elasticService,
		private ConsumerHelper $consumerHelper,
		private Orm $orm,
		private TranslatorDB $translatorDB,
	)
	{
	}

	public function __invoke(DeleteProductMessage $message): void
	{
		$this->orm->reconnect();
		$this->productRepository->setPublicOnly(false);

		$product = $this->productRepository->getById($message->getId());
		$esIndex = $this->esIndexRepository->getById($message->getEsIndexId());

		if ($esIndex === null) {
			throw new LogicException(sprintf('EsIndex %s not found', $message->getEsIndexId()));
		}

		$this->translatorDB->setMutation($esIndex->mutation);

		try {
			if ($product === null) {
				throw new LogicException(sprintf('Product %s not found', $message->getId()));
			}
			foreach ($product->variants as $variant) {
				$elasticProduct = new ElasticProduct($product, $variant, []);
				$this->elasticService->deleteDoc($esIndex, $elasticProduct);
			}
		} catch (Throwable $e) {
			$this->consumerHelper->handleError($esIndex, $e, $message->getId(), Product::class);
		}
	}

}
