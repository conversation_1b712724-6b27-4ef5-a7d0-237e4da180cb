<?php declare(strict_types = 1);

namespace App\Model\Messenger\Elasticsearch\Product\Consumer;

use App\Model\Messenger\Elasticsearch\Product\Message\ReplaceProductMessage;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;

#[AsMessageHandler]
class ReplaceProductConsumer extends BaseReplaceProductConsumer
{

	public function __invoke(ReplaceProductMessage $message): void
	{
		$this->consume($message);
	}

}
