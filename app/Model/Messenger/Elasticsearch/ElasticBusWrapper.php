<?php declare(strict_types = 1);

namespace App\Model\Messenger\Elasticsearch;

use Symfony\Component\Messenger\Exception\HandlerFailedException;
use Symfony\Component\Messenger\MessageBusInterface;

final class ElasticBusWrapper
{

	public const SIGNAL_FIRST = 'signal_first';
	public const SIGNAL_LAST = 'signal_last';
	public const AUTO_SWITCH = 'signal_auto_switch';

    public function __construct(private MessageBusInterface $bus)
	{
	}

	public function send(object $command): void
	{
		try {
			$this->bus->dispatch($command);
		} catch (HandlerFailedException $e) {
			throw $e->getPrevious();
		}
	}

}
