<?php declare(strict_types = 1);

namespace App\Model\Messenger\Elasticsearch;

use App\Model\Orm\EsIndex\EsIndex;
use App\Model\Orm\EsIndex\EsIndexModel;
use App\Model\Orm\Orm;
use LogicException;
use Throwable;

class ConsumerHelper
{

	public function __construct(
		private EsIndexModel $esIndexModel,
		private Orm $orm,
	)
	{
	}


	public function handleSignals(EsIndex $esIndex, array $signals = []): void
	{
		foreach ($signals as $signal) {
			match ($signal) {
				ElasticBusWrapper::SIGNAL_FIRST => $this->esIndexModel->markAsStarted($esIndex),
				ElasticBusWrapper::SIGNAL_LAST => $this->esIndexModel->markAsFinished($esIndex),
				ElasticBusWrapper::AUTO_SWITCH => $this->esIndexModel->switchIndex($esIndex),
				default => throw new LogicException(sprintf("Unknown message signal '%s'", $signal))
			};
		}
	}

	public function handleError(EsIndex $esIndex, Throwable $e, string|int $id, string $class): void
	{
		$esIndex->errorCount++;

		if (!isset($esIndex->errorDetail->items)) {

			$esIndex->errorDetail->items = [];
		}

		if (count($esIndex->errorDetail->items) < 20) {
			$errorDetail = $esIndex->errorDetail;
			$errorDetail->items[] = [
				'id' => $id,
				'class' => $class,
				'msg' => $e->getMessage(),
			];

			$esIndex->errorDetail = $errorDetail;
		}

		$this->orm->esIndex->persistAndFlush($esIndex);
		throw $e;
	}

}
