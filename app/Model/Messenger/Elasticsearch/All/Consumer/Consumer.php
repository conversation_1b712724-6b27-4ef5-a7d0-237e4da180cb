<?php declare(strict_types = 1);

namespace App\Model\Messenger\Elasticsearch\All\Consumer;

use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductRepository;
use App\PostType\Brand\Model\Orm\BrandLocalization;
use App\PostType\Brand\Model\Orm\BrandLocalizationRepository;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Page\Model\Orm\TreeRepository;
use App\PostType\Author\Model\Orm\AuthorLocalization;
use App\PostType\Author\Model\Orm\AuthorLocalizationRepository;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\Blog\Model\Orm\BlogLocalizationRepository;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalization;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalizationRepository;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalization;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalizationRepository;
use LogicException;

abstract class Consumer
{

	public function __construct(
		private readonly AuthorLocalizationRepository $authorLocalizationRepository,
		private readonly TreeRepository $treeRepository,
		private readonly BlogTagLocalizationRepository $blogTagLocalizationRepository,
		private readonly BlogLocalizationRepository $blogLocalizationRepository,
		private readonly ProductRepository $productRepository,
		private readonly SeoLinkLocalizationRepository $seoLinkLocalizationRepository,
		private readonly BrandLocalizationRepository $brandLocalizationRepository,
	)
	{
	}

	protected function getObjectByClass(string $class, int $objectId): ?object
	{
		$this->authorLocalizationRepository->setPublicOnly(false);
		$this->blogTagLocalizationRepository->setPublicOnly(false);
		$this->blogLocalizationRepository->setPublicOnly(false);
		$this->treeRepository->setPublicOnly(false);
		$this->productRepository->setPublicOnly(false);
		$this->brandLocalizationRepository->setPublicOnly(false);

		return match ($class) {
			AuthorLocalization::class => $this->authorLocalizationRepository->getById($objectId),
			BlogLocalization::class => $this->blogLocalizationRepository->getById($objectId),
			BlogTagLocalization::class => $this->blogTagLocalizationRepository->getById($objectId),
			CatalogTree::class, Tree::class, CommonTree::class => $this->treeRepository->getById($objectId),
			Product::class => $this->productRepository->getById($objectId),
			SeoLinkLocalization::class => $this->seoLinkLocalizationRepository->getById($objectId),
			BrandLocalization::class => $this->brandLocalizationRepository->getById($objectId),
			default => throw new LogicException(sprintf("Missing definition for '%s' class", $class))
		};
	}

}
