<?php
declare(strict_types = 1);

namespace App\Model\Messenger\Erp\Stock;

use App\Console\Erp\CommandType;
use App\Model\Erp;
use App\Model\Messenger\Erp\ErpConsumer;
use App\Model\Orm\ImportCache\ImportCache;
use App\Model\Orm\Stock\Stock;
use App\Model\Orm\Supply\Supply;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final class StockConsumer extends ErpConsumer
{

	protected string $loggerType = CommandType::STOCK_PROCESS;

	private Stock $defaultStock;

	private ?Erp\Entity\Stock $erpStock = null;

	private ?Supply $supply = null;

	protected function setup(): void
	{
		$this->defaultStock = $this->orm->stock->getBy(['alias' => Stock::ALIAS_SHOP]);
	}

	public function __invoke(StockMessage $message): void
	{
		$this->checkMessageAndImport($message);
	}

	protected function doImport(ImportCache $importCache): void
	{
		$this->erpStock = new Erp\Entity\Stock($importCache->data);
		$this->setVariant($importCache, true);

		$this->syncSuply($importCache);
		$this->syncVariant();
	}

	protected function save(ImportCache $importCache): void
	{
		$this->orm->persistAndFlush($this->supply);

		if ($this->hasUpdateElasticIndex()) {
			$this->esProductFacade->updateAfterStockProcess($this->variant->product, $this->defaultMutation);
			$this->esAllFacade->updateAfterStockProcess($this->variant->product);
		}

		$this->orm->persistAndFlush($importCache);
	}

	private function syncSuply(ImportCache $importCache): void
	{
		$this->supply = $this->orm->supply->getBy([
			'variant' => $this->variant,
			'stock' => $this->defaultStock,
		]);

		if ( ! isset($this->supply)) {
			$this->supply = new Supply();
			$this->orm->supply->attach($this->supply);

			$this->supply->stock = $this->defaultStock;
			$this->supply->variant = $this->variant;
		}

		$this->supply->amount = $this->erpStock->stock;
		$this->supply->lastImport = $importCache->importedTime;

		if ($this->supply->amount > 0) {
			$this->supply->lastOnStock = $importCache->importedTime;
		}
	}

	private function syncVariant(): void
	{
		if ($this->variant->isOld) {
			$this->variant->isReSale = true;
			$this->orm->persist($this->variant);
		}
	}

}
