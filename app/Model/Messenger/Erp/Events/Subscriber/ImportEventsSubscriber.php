<?php

declare(strict_types = 1);

namespace App\Model\Messenger\Erp\Events\Subscriber;

use App\Model\Messenger\Erp\Events\ImportFailed;
use App\Model\Messenger\Erp\Events\ImportSuccess;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

readonly final class ImportEventsSubscriber implements EventSubscriberInterface
{

	public function __construct()
	{
	}

	public function onImportSuccess(ImportSuccess $event): void
	{
		//file_put_contents(LOG_DIR . '/import_success.log', date('Y-m-d H:i:s') . " Import success\n", FILE_APPEND);
	}

	public function onImportFailed(ImportFailed $event): void
	{
	}

	public static function getSubscribedEvents(): array
	{
		return [
			ImportSuccess::class => [
				['onImportSuccess', 1],
			],
			ImportFailed::class => [
				['onImportFailed', 1],
			],
		];
	}

}
