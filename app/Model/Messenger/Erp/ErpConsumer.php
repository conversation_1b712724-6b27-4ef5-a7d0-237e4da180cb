<?php declare(strict_types = 1);

namespace App\Model\Messenger\Erp;

use App\Exceptions\LogicException;
use App\Model\ElasticSearch;
use App\Model\Erp\Exception\SkippedException;
use App\Model\Mutation\MutationHolder;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\ImportCache\ImportCache;
use App\Model\Orm\LibraryImage\LibraryImageModel;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\PriceLevel\PriceLevelModel;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Price\ProductPriceModel;
use Contributte\Monolog\LoggerManager;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Psr\Log\LoggerInterface;
use Throwable;
use Tracy\Debugger;
use Tracy\ILogger;

abstract class ErpConsumer
{

	use InitImportCache;

	protected array $signals = [];

	protected array $warnings = [];

	protected string $loggerType;

	protected Mutation $defaultMutation;

	protected LoggerInterface $logger;

	protected ?ProductVariant $variant = null;

	public function __construct(
		protected readonly Orm $orm,
		protected readonly MutationsHolder $mutationsHolder,
		protected readonly MutationHolder $mutationHolder,
		protected readonly PriceLevelModel $priceLevelModel,
		protected readonly ProductPriceModel $productPriceModel,
		protected readonly LibraryImageModel $libraryImageModel,
		protected readonly LoggerManager $loggerManager,
		protected readonly ElasticSearch\Product\Facade $esProductFacade,
		protected readonly ElasticSearch\All\Facade $esAllFacade,
	)
	{
		$this->orm->reconnect();
		$this->logger = $this->loggerManager->get($this->loggerType);

		$this->defaultMutation = $this->mutationsHolder->getDefault();
		$this->mutationHolder->setMutation($this->defaultMutation);

		$this->orm->setMutation($this->defaultMutation);
		$this->orm->setPublicOnly(false);
	}

	abstract protected function setup(): void;

	/**
	 * @throws SkippedException
	 */
	abstract protected function doImport(ImportCache $importCache): void;

	abstract protected function save(ImportCache $importCache): void;

	protected function checkMessageAndImport(ImportMessage $importMessage): void
	{
		$this->signals = $importMessage->getSignals();
		$this->warnings = [];
		$this->setup();

		$importCache = $this->initImportCache($importMessage);

		if ($importCache === null) {
			return;
		}

		try {
			$importCache->importedTime = new DateTimeImmutable();

			$this->doImport($importCache);

			$importCache->status = ImportCache::STATUS_IMPORTED;
			$importCache->message = 'ok';

		} catch (SkippedException $e) {
			$importCache->status = ImportCache::STATUS_SKIPPED;
			$importCache->message = $e->getMessage();

		} catch (Throwable $e) {
			Debugger::log($e, ILogger::EXCEPTION);
			$this->logger->error($e->getMessage(), ['exception' => $e, 'consumer_name' => $_ENV['MESSENGER_CONSUMER_NAME'] ?? null, 'import_cache_id' => $importCache->id, 'import_cache_ext_id' => $importCache->extId]);

			$importCache->status = ImportCache::STATUS_ERROR;
			$importCache->message = $e->getMessage();
		}

		if ($this->hasWarning()) {
			$importCache->status = ImportCache::STATUS_WARNING;
			$importCache->message = implode(' | ', $this->warnings);
		}
		$this->save($importCache);
	}

	protected function initImportCache(ImportMessage $message): ?ImportCache
	{
		$importCache = $this->orm->importCache->getById($message->getImportCacheId());
		if ($importCache === null) {
			Debugger::log(new LogicException('ImportMessage not found.'), Debugger::EXCEPTION);
			return null;
		}

		assert($importCache->type === $message->getImportCacheType());

		return $importCache;
	}

	/** @noinspection PhpUnused */
	protected function hasSignal(ImportMessageSignal $importMessageSignal): bool
	{
		return in_array($importMessageSignal, $this->signals);
	}

	protected function addWarning(string $message): void
	{
		$this->warnings[] = $message;
	}

	protected function hasWarning(): bool
	{
		return count($this->warnings) > 0;
	}

	/** @noinspection PhpUnused */
	protected function doRemove(): void
	{
		throw new LogicException('Remove message is not implemented yet.');
	}

	/**
	 * @throws SkippedException
	 */
	protected function setVariant(ImportCache $importCache, bool $need = false): void
	{
		$this->variant = $this->orm->productVariant->getByExtId($importCache->extId);

		if ($need) {
			if ($this->variant === null) {
				throw new SkippedException('ProductVariant not found');
			}
			assert($this->variant instanceof ProductVariant);
		}
	}

	/**
	 * Only already paired variant with product
	 */
	protected function hasUpdateElasticIndex(): bool
	{
		return $this->variant->product !== null;
	}

}
