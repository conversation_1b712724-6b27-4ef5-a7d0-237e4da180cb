<?php
declare(strict_types = 1);

namespace App\Model\Messenger\Erp\Price;

use App\Console\Erp\CommandType;
use App\Model\Currency\CurrencyHelper;
use App\Model\Erp;
use App\Model\Erp\Exception\SkippedException;
use App\Model\Messenger\Erp\ErpConsumer;
use App\Model\Orm\ImportCache\ImportCache;
use App\Model\Orm\Price;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\ProductVariantPrice\ProductVariantPrice;
use Brick\Math\Exception\MathException;
use Brick\Math\Exception\NumberFormatException;
use Brick\Math\Exception\RoundingNecessaryException;
use Brick\Money\Context\CustomContext;
use Brick\Money\Exception\UnknownCurrencyException;
use Brick\Money\Money;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
final class PriceConsumer extends ErpConsumer
{

	protected string $loggerType = CommandType::PRICE_PROCESS;

	private ?Erp\Entity\Price $erpPrice = null;

	private array $defaultPriceLevels;

	private ?ProductVariantPrice $pricePurchase = null;

	private ?ProductVariantPrice $priceRecommended = null;

	protected function setup(): void
	{
		$this->defaultPriceLevels = $this->priceLevelModel->getAllPriceLevelByType([
			PriceLevel::TYPE_PURCHASE,
			PriceLevel::TYPE_RECOMMENDED,
		]);
	}

	public function __invoke(PriceMessage $message): void
	{
		$this->checkMessageAndImport($message);
	}

	/**
	 * @throws NumberFormatException
	 * @throws RoundingNecessaryException
	 * @throws UnknownCurrencyException
	 * @throws SkippedException
	 */
	protected function doImport(ImportCache $importCache): void
	{
		$this->erpPrice = new Erp\Entity\Price($importCache->data);
		$this->setVariant($importCache, true);

		$this->syncPricePurchase($importCache);
		$this->syncPriceRecommend($importCache);
		$this->syncVariant();
	}

	/**
	 * @throws MathException
	 */
	protected function save(ImportCache $importCache): void
	{
		$this->orm->persist($this->pricePurchase);
		$this->orm->persist($this->priceRecommended);
		$this->orm->productVariantPrice->flush();

		$this->productPriceModel->recalculatePrices($this->variant);

		if ($this->hasUpdateElasticIndex()) {
			$this->esProductFacade->updateAfterPriceProcess($this->variant->product, $this->defaultMutation);
			$this->esAllFacade->updateAfterStockProcess($this->variant->product);
		}

		$this->orm->persistAndFlush($importCache);
	}

	/**
	 * @throws UnknownCurrencyException
	 * @throws RoundingNecessaryException
	 * @throws NumberFormatException
	 */
	private function syncPricePurchase(ImportCache $importCache): void
	{
		$context = new CustomContext(4);

		$this->pricePurchase = $this->orm->productVariantPrice->getBy([
			'mutation' => $this->defaultMutation,
			'variant' => $this->variant,
			'priceLevel' => $this->defaultPriceLevels[PriceLevel::TYPE_PURCHASE],
		]);

		$doUpdatePrice = true;

		if (isset($this->pricePurchase) && $this->pricePurchase->price->asMoney()->isGreaterThan($this->erpPrice->purchasePrice)) {
			$doUpdatePrice = false;
			$this->variant->erpNewPurchasePrice = $this->erpPrice->purchasePrice;
		} else {
			$this->pricePurchase = new ProductVariantPrice();
			$this->orm->productVariantPrice->attach($this->pricePurchase);

			$this->pricePurchase->mutation = $this->defaultMutation;
			$this->pricePurchase->productVariant = $this->variant;
			$this->pricePurchase->priceLevel = $this->defaultPriceLevels[PriceLevel::TYPE_PURCHASE];
		}

		$this->pricePurchase->vat = $this->erpPrice->vat;

		if ($doUpdatePrice) {
			$this->pricePurchase->price = Price::from(Money::of($this->erpPrice->purchasePrice, CurrencyHelper::CURRENCY_CZK, $context));
			$this->pricePurchase->lastImport = $importCache->importedTime;
		}
	}

	/**
	 * @throws UnknownCurrencyException
	 * @throws RoundingNecessaryException
	 * @throws NumberFormatException
	 */
	private function syncPriceRecommend(ImportCache $importCache): void
	{
		$context = new CustomContext(4);

		$this->priceRecommended = $this->orm->productVariantPrice->getBy([
			'mutation' => $this->defaultMutation,
			'variant' => $this->variant,
			'priceLevel' => $this->defaultPriceLevels[PriceLevel::TYPE_RECOMMENDED],
		]);

		if ( ! isset($this->priceRecommended)) {
			$this->priceRecommended = new ProductVariantPrice();
			$this->orm->productVariantPrice->attach($this->priceRecommended);

			$this->priceRecommended->mutation = $this->defaultMutation;
			$this->priceRecommended->productVariant = $this->variant;
			$this->priceRecommended->priceLevel = $this->defaultPriceLevels[PriceLevel::TYPE_RECOMMENDED];
		}

		$this->priceRecommended->price = Price::from(Money::of($this->erpPrice->recommendedPrice, CurrencyHelper::CURRENCY_CZK, $context));
		$this->priceRecommended->vat = $this->erpPrice->vat;
		$this->priceRecommended->lastImport = $importCache->importedTime;
	}

	private function syncVariant(): void
	{
		$this->variant->minExpriration = $this->erpPrice->minExpiration;
	}

}
