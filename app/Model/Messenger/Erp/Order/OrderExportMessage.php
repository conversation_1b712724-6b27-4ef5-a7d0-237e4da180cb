<?php declare(strict_types = 1);

namespace App\Model\Messenger\Erp\Order;

use App\Model\Orm\Order\Sync\OrderSyncType;

readonly class OrderExportMessage
{

	public function __construct(
		private int $orderId,
		private OrderSyncType $syncType,
	)
	{
	}

	public function getOrderId(): int
	{
		return $this->orderId;
	}

	public function getSyncType(): OrderSyncType
	{
		return $this->syncType;
	}

}
