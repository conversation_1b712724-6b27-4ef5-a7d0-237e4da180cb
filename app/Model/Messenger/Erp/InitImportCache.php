<?php declare(strict_types = 1);

namespace App\Model\Messenger\Erp;

use App\Exceptions\LogicException;
use App\Model\Orm\ImportCache\ImportCache;
use App\Model\Orm\Orm;
use Tracy\Debugger;

trait InitImportCache
{

	protected function initImportCache(ImportMessage $message, Orm $orm): ?ImportCache
	{
		$importCache = $orm->importCache->getById($message->getImportCacheId());
		if ($importCache === null) {
			Debugger::log(new LogicException('ImportMessage not found.'), Debugger::EXCEPTION);
			return null;
		}

		assert($importCache->type === $message->getImportCacheType());

		return $importCache;
	}

}
