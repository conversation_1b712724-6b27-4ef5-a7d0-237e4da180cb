<?php declare(strict_types=1);

namespace App\Model\Messenger\Erp;

abstract readonly class ImportMessage
{

	public function __construct(private ?int $importCacheId, private array $signals = [])
	{
	}

	public function getImportCacheId(): ?int
	{
		return $this->importCacheId;
	}

	public function getSignals(): array
	{
		return $this->signals;
	}

	abstract public function getImportCacheType(): string;

}
