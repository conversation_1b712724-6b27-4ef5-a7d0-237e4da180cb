<?php
declare(strict_types=1);

namespace App\Model\Messenger;

use App\Model\Erp\Exception\SkippedException;
use App\Model\Erp\Exception\WarningException;
use App\Model\Messenger\Erp\ImportMessage;
use App\Model\Orm\ImportCache\ImportCache;
use App\Model\Orm\Orm;
use Contributte\Monolog\LoggerManager;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\Event\WorkerMessageFailedEvent;

final readonly class FailedEventSubscriber implements EventSubscriberInterface
{

	public function __construct(
		private LoggerManager $loggerManager,
		private Orm $orm,
	)
	{
	}

	private function getOriginalException(\Throwable $e): \Throwable
	{
		if ($e->getPrevious() !== null) {
			return $this->getOriginalException($e->getPrevious());
		}
		return $e;
	}

	public function __invoke(WorkerMessageFailedEvent $event): void
	{
		$exception = $this->getOriginalException($event->getThrowable());
		if ($event->willRetry()) {
			return;
		}

		$message = $event->getEnvelope()->getMessage();

		$logger = $this->loggerManager->get('messenger_failed');
		$logger->debug('Retry message: ', ['message' => $message::class]);
		$logger->debug('Retry exception: ', ['message' => $event->getThrowable()->getPrevious()?->getMessage() ?? $event->getThrowable()->getMessage()/*, 'exception' => $event->getThrowable()*/]);

		if ($message instanceof ImportMessage) {
			$importCache = $this->orm->importCache->getById($message->getImportCacheId());
			if ($importCache !== null) {
				if ($exception instanceof SkippedException) {
					$importCache->status  = ImportCache::STATUS_SKIPPED;
					$importCache->message = $exception->getMessage();
				} elseif ($exception instanceof WarningException) {
					$importCache->status  = ImportCache::STATUS_WARNING;
					$importCache->message = $exception->getMessage();
				} else {
					$importCache->status = ImportCache::STATUS_ERROR;
					$importCache->message = (string) $exception;
				}
				$this->orm->persistAndFlush($importCache);
			}
		}
	}

	public static function getSubscribedEvents(): array
	{
		return [
			WorkerMessageFailedEvent::class => [
				['__invoke', 0],
			],
		];
	}
}
