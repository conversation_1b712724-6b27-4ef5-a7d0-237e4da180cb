<?php declare(strict_types = 1);

namespace App\Model\Messenger\Default\HeurekaOvereno;

use App\Exceptions\LogicException;
use App\Model\ConfigService;
use App\Model\Mutation\MutationHolder;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\Product\ProductItem;
use App\Model\Orm\Orm;
use Heureka\ShopCertification;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Throwable;

#[AsMessageHandler]
final readonly class HeurekaOverenoConsumer {


	public function __construct(
		private Orm $orm,
		private MutationsHolder $mutationsHolder,
		private MutationHolder $mutationHolder,
		private ConfigService $configService,
	)
	{
	}

	public function __invoke(HeurekaOverenoMessage $message): void
	{
		$this->orm->reconnect();

		$defaultMutation = $this->mutationsHolder->getDefault();

		$this->mutationHolder->setMutation($defaultMutation);
		$this->orm->setMutation($defaultMutation);
		$this->orm->setPublicOnly(false);


		$order = $this->orm->order->getById($message->getOrderId());

		if ($order !== null) {
			if (!$order->heurekaSend) {
				$this->saveRequestResult($order, new LogicException('HeurekaSend is false.'));
				return;
			}
			if (!$this->configService->isEnvProduction()) {
				$this->saveRequestResult($order, new LogicException('Not production mode, skipping.'));
				return;
			}

			if ($order->mutation->heurekaOverenoKey === null || $order->mutation->heurekaOverenoKey === ''){
				$this->saveRequestResult($order, new LogicException('HeurekaOverenoKey is null or empty.'));
				return;
			}
			try {

				$shopCertification = new ShopCertification('', ['service' => ShopCertification::HEUREKA_CZ]);
				$shopCertification->setEmail($order->email);
				$shopCertification->setOrderId($order->orderNumber);

				$productIds = [];
				/** @var ProductItem $product */
				foreach ($order->getProducts() as $product) {
					$productId = $product->variant->product->getId();
					if (isset($productIds[$productId])) {
						continue;
					}
					$shopCertification->addProductItemId((string) $productId);
					$productIds[$productId] = $productId;
				}

				$response = $shopCertification->logOrder();
				$this->saveRequestResult($order, $response);
			} catch (Throwable $e) {
				$this->saveRequestResult($order, $e);
				throw $e;
			}

		}

	}

	private function saveRequestResult(Order $order, ShopCertification\Response|Throwable $response): void
	{
		if ($response instanceof ShopCertification\Response) {
			$order->heurekaResponse = $response->code . ' | ' . $response->message . ' | ' . $response->description;
		} else {
			$order->heurekaResponse = $response->getCode() . ' | ' . $response->getMessage();
		}
		$this->orm->persistAndFlush($order);
	}
}
