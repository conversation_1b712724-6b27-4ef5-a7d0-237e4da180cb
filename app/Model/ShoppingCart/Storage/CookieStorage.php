<?php declare(strict_types = 1);

namespace App\Model\ShoppingCart\Storage;


use App\Model\Orm\Mutation\Mutation;
use Nette\Http\Request;
use Nette\Http\Response;


final class CookieStorage implements Storage
{

	private const COOKIE_NAME = 'shoppingCartId';
	private const COOKIE_EXPIRE = '+3 month';

	private ?Mutation $mutation = null;

	private string $cookieName;

	private ?string $namespace = null;


	public function __construct(
		private readonly Request $httpRequest,
		private readonly Response $httpResponse,
	)
	{
	}

	private function init():void
	{
		$this->cookieName = ($this->namespace ?? '') . self::COOKIE_NAME . '-' . $this->mutation?->langCode;
	}

	public function get(): mixed
	{
		$orderId = $this->httpRequest->getCookie($this->cookieName);
		if ($orderId === null) {
			return null;
		}
		return $orderId;
	}

	public function set(mixed $orderId): void
	{
		$this->httpResponse->setCookie($this->cookieName, (string) $orderId, self::COOKIE_EXPIRE);
	}

	public function remove(): void
	{
		$this->httpResponse->deleteCookie($this->cookieName);
	}

	public function setMutation(Mutation $mutation): void
	{
		$this->mutation = $mutation;
		$this->init();
	}

	public function setNamespace(?string $namespace): self
	{
		$this->namespace = $namespace;
		$this->init();
		return $this;
	}
}
