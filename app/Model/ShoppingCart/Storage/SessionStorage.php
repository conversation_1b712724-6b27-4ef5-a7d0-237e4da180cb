<?php declare(strict_types = 1);

namespace App\Model\ShoppingCart\Storage;


use App\Model\Orm\Mutation\Mutation;
use Nette\Http\Session;
use Nette\Http\SessionSection;

final class SessionStorage implements Storage
{
	private SessionSection $session;

	private Mutation $mutation;

	private ?string $namespace = null;

	public function __construct(private readonly Session $httpSession)
	{

	}

	private function init(): void
	{
		$this->session = $this->httpSession->getSection(($this->namespace ?? '') . self::class . '-' . $this->mutation->langCode);
	}

	public function get(): mixed
	{
		return $this->session->get('orderId');
	}

	public function set(mixed $orderId): void
	{
		$this->session->set('orderId', $orderId);
	}

	public function remove(): void
	{
		$this->session->remove('orderId');
	}

	public function setMutation(Mutation $mutation): void
	{
		$this->mutation = $mutation;
		$this->init();
	}

	public function setNamespace(?string $namespace): self
	{
		$this->namespace = $namespace;
		$this->init();
		return $this;
	}
}
