<?php

declare(strict_types=1);

namespace App\Model\ShoppingCart;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Order\FakeOrder;
use App\Model\Orm\Order\Voucher\VoucherItem;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\RoutableEntity;
use App\Model\StaticPage\StaticPage;
use Nette\Security\User;
use Nette\Utils\ArrayHash;

class FakeShoppingCart extends FakeOrder implements ShoppingCartInterface
{

	public function getMutation(): Mutation
	{
		return new Mutation();
	}

	public function getPriceLevel(): PriceLevel
	{
		return new PriceLevel();
	}

	public function hasOrder(): bool
	{
		return false;
	}

	public function getAppliedVoucherItem(): ?VoucherItem
	{
		return null;
	}

	public function isEmpty(): bool
	{
		return true;
	}

	public function storeVisit(): void
	{

	}

	public function doEmpty(): void
	{

	}

	public function getOrderId(): ?int
	{
		return null;
	}

	public function placeOrder(ArrayHash $orderDetails, ArrayHash $orderDeliveryDetails): array
	{
		return [];
	}

	public function getTotalProducts(): int
	{
		return 0;
	}

	public function showForgottenCart(StaticPage|RoutableEntity $routableEntity): bool
	{
		return false;
	}

	public function getUserSecurity(): ?User
	{
		return null;
	}

	public function flushStorage(): void
	{

	}

	public function hasExtendedDelivery(int $days = 10): bool
	{
		return false;
	}
	public function hasExtendedStock(int $days = 10): bool
	{
		return false;
	}

	public function getTotalClassEvents(): int
	{
		return 0;
	}

	public function setMutation(Mutation $mutation): void
	{

	}
}
