<?php

namespace App\Model\ShoppingCart;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Order\OrderProxy;
use App\Model\Orm\Order\Voucher\VoucherItem;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\RoutableEntity;
use App\Model\Orm\State\State;

use App\Model\StaticPage\StaticPage;
use Nette\Security\User;
use Nette\Utils\ArrayHash;

interface ShoppingCartInterface extends OrderProxy {
	public function getAmounts(): array;
	public function getChangedItems(bool $onlyProducts = false, bool $onlyToRemove = false): array;
	public function setPickupPoint(?int $pickupPointId): void;
	public function setCurrency(string $currencyCode): void;
	public function setMutation(Mutation $mutation): void;
	public function setCountry(State $state): void;

	public function getMutation(): Mutation;
	public function getPriceLevel(): PriceLevel;

	public function hasOrder(): bool;

	public function getAppliedVoucherItem(): ?VoucherItem;



	public function storeVisit(): void;
	public function doEmpty(): void;

	public function getOrderId(): ?int;

	public function placeOrder(ArrayHash $orderDetails, ArrayHash $orderDeliveryDetails): array;

	public function getTotalProducts(): int;

	public function getTotalClassEvents(): int;

	public function showForgottenCart(RoutableEntity|StaticPage $routableEntity): bool;

	public function getUserSecurity(): ?User;

	public function flushStorage(): void;

	public function hasExtendedStock(int $days = 10): bool;

	public function hasExtendedDelivery(int $days = 10): bool;
}
