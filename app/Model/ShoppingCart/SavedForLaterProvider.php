<?php

namespace App\Model\ShoppingCart;

use App\Exceptions\LogicException;
use App\Model\Orm\ClassEvent\ClassEvent;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Order\Class\ClassItem;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderItem;
use App\Model\Orm\Order\OrderState;
use App\Model\Orm\Order\Product\ProductItem;
use App\Model\Orm\Orm;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\User\User as UserEntity;
use App\Model\Orm\User\UserProvider;
use App\Model\ShoppingCart\Storage\CookieStorageFactory;
use Jaybizzle\CrawlerDetect\CrawlerDetect;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;

final class SavedForLaterProvider
{
	private ?Order $order = null;

	private ?Mutation $mutation = null;

	private Storage\Storage $storage;

	public function __construct(
		CookieStorageFactory $storageFactory,
		private readonly UserProvider $userProvider,
		private readonly Orm $orm,
		private readonly ShoppingCartInterface $shoppingCart,
	){
		$this->storage = $storageFactory->create();
		$this->storage->setNamespace('savedForLater-');
	}

	public function add(OrderItem $item): void
	{
		$this->initOrder();
		$amount = $item->amount;

		if ($item instanceof ClassItem) {
			/** @var ClassItem|null $existingItem */
			$existingItem = $this->order->classEvents->toCollection()->getBy(['product' => $item->product, 'classEvent' => $item->classEvent, 'priceLevel' => $item->priceLevel]);

		} elseif ($item instanceof ProductItem) {
			/** @var ProductItem|null $existingItem */
			$existingItem = $this->order->products->toCollection()->getBy(['variant' => $item->variant]);

		} else {
			throw new LogicException('Item type not supported.');
		}

		if ($existingItem === null) {
			$item->order = $this->order;
			$item->setAmount($amount);
			$this->orm->persistAndFlush($item);
		} else {
			$currentAmount = $existingItem->amount;
			$existingItem->setAmount($currentAmount + $amount);

			$this->orm->persistAndFlush($existingItem);
			$this->orm->removeAndFlush($item);
		}

	}

	public function subtract(OrderItem $item, int $quantity): void
	{
		$this->initOrder();
		if ($quantity > 0) {
			$item->setAmount($quantity);
			$this->orm->persistAndFlush($item);
		} else {
			$this->orm->removeAndFlush($item);
		}
	}

	public function remove(OrderItem $item): void
	{
		$this->initOrder();
		$this->orm->removeAndFlush($item);
	}

	public function toCart(OrderItem $item): void
	{
		$this->initOrder();

		if ($item instanceof ClassItem) {
			$this->shoppingCart->addClass($item->product, $item->classEvent,$item->priceLevel,  amount: $item->amount);
		} elseif ($item instanceof ProductItem) {
			$this->shoppingCart->addProduct($item->variant, amount: $item->amount);
		} else {
			throw new LogicException('Item type not supported.');
		}

		$this->orm->removeAndFlush($item);
	}

	public function refresh(): void
	{
		$changedItems = $this->order?->refresh() ?? [];
		foreach ($changedItems as $changedItem) {
			if ($changedItem->removed) {
				$this->orm->removeAndFlush($changedItem->item);
			} elseif ($changedItem->updated) {
				$this->orm->persistAndFlush($changedItem->item);
			}
		}

		if ($changedItems !== []) {
			$this->orm->persistAndFlush($this->order);
		}
	}

	public function getProductItem(ProductVariant $variant): ?ProductItem
	{
		return $this->order?->getProducts()->getBy(['variant' => $variant]);
	}

	public function getClassItem(Product $product, ?ClassEvent $classEvent = null, ?PriceLevel $priceLevel = null): ?ClassItem
	{
		return $this->order?->getClassEvents()->getBy(['product' => $product, 'classEvent' => $classEvent, 'priceLevel' => $priceLevel]);
	}


	private function init(): void
	{
		if ($this->mutation === null) {
			throw new LogicException('Mutation is not set.');
		}
		$this->order = $this->getOrder();
	}
	public function setMutation(Mutation $mutation): void
	{
		$this->mutation = $mutation;
		$this->storage->setMutation($mutation);
		$this->init();
	}

	public function getMutation(): Mutation
	{
		if ($this->order === null) {
			return $this->mutation;
		}
		return $this->order->mutation;
	}

	private function getOrder(): ?Order
	{
		if(($orderCookieHash = $this->storage->get()) !== null) {
			$order = $this->orm->order->getBy(['cookieHash' => $orderCookieHash, 'state' => OrderState::SavedForLater, 'mutation' => $this->getMutation()]);
			if ($order !== null && $order->state === OrderState::SavedForLater) {
				return $order;
			}
		}

		$userEntity = $this->userProvider->userEntity;
		if ($userEntity !== null) {
			$order = $this->orm->order->getBy([
				'state' => OrderState::SavedForLater,
				'user' => $userEntity,
				'mutation' => $this->getMutation(),
			]);

			if ($order !== null) {
				$this->storage->set($order->cookieHash);
				return $order;
			}
		}

		return null;
	}
	private function initOrder(): void
	{
		// Crawler and other bots detection
		$crawlerDetect = new CrawlerDetect();
		if ($crawlerDetect->isCrawler()) {
			return;
		}

		// Otherwise, if order not exists, create it
		if ($this->order === null) {
			$this->order = $this->createOrder();
		}

		// if user is logged in and has shopping cart id
		if ($this->order->user === null && $this->getUserEntity() !== null) {
			$this->order->user = $this->getUserEntity();
			$this->orm->persistAndFlush($this->order);
		}
	}
	private function createOrder(): Order
	{
		$userEntity = $this->userProvider->userEntity;
		$mutation = $this->getMutation();


		$order = new Order(
			$mutation,
			$userEntity?->priceLevel ?? $this->orm->priceLevel->getDefault(),
			$userEntity?->state ?? $this->orm->state->getDefault($mutation),
			$userEntity,
			$mutation->currency,
		);

		$order->state = OrderState::SavedForLater;

		$this->orm->persistAndFlush($order);
		$this->storage->set($order->cookieHash);

		return $order;
	}
	public function getUserEntity(): ?UserEntity
	{
		if ($this->order === null || $this->order->user === null) {
			if ($this->userProvider->userSecurity->isLoggedIn()) {
				return $this->userProvider->userEntity;
			}
		}
		return $this->order?->user;
	}

	/**
	 * @return ICollection<ProductItem>
	 */
	public function getProducts(): ICollection
	{
		/** @var ICollection<ProductItem> $emptyCollection */
		$emptyCollection = new EmptyCollection();
		return $this->order?->getProducts()->orderBy('id', ICollection::DESC) ?? $emptyCollection;
	}

	/**
	 * @return ICollection<ClassItem>
	 */
	public function getClassEvents(): ICollection
	{
		/** @var ICollection<ClassItem> $emptyCollection */
		$emptyCollection = new EmptyCollection();
		return $this->order?->getClassEvents()->orderBy('id', ICollection::DESC) ?? $emptyCollection;
	}

	public function getAmounts(): array
	{
		$amounts = [];
		/** @var ProductItem $productItem */
		foreach ($this->getProducts() as $productItem) {
			$amounts['products-' . $productItem->variant->id] = $productItem->amount;
		}
		/** @var ClassItem $classItem */
		foreach ($this->getClassEvents() as $classItem) {
			$amounts['classEvents-' . $classItem->getIdentifier()] = $classItem->amount;
		}

		return $amounts;
	}
}
