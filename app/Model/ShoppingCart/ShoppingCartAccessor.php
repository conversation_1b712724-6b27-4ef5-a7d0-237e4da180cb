<?php

declare(strict_types=1);

namespace App\Model\ShoppingCart;

final class ShoppingCartAccessor {

	private ?ShoppingCartInterface $shoppingCart = null;

	public function __construct(
		private readonly ShoppingCartFactory $shoppingCartFactory,
		private readonly bool $consoleMode
	)
	{
	}

	public function get(): ShoppingCartInterface
	{
		if ($this->shoppingCart === null) {
			if ($this->consoleMode) {
				$this->shoppingCart = new FakeShoppingCart();
			} else {
				$this->shoppingCart = $this->shoppingCartFactory->create();
			}
		}

		return $this->shoppingCart;
	}
}
