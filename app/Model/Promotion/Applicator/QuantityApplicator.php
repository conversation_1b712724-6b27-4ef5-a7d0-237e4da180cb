<?php declare(strict_types=1);

namespace App\Model\Promotion\Applicator;

use App\PostType\Promotion\Model\Orm\Promotion\PromotionLocalization;
use App\PostType\Promotion\Model\Orm\Promotion\Types\PromotionDiscountType;
use Brick\Money\Money;

class QuantityApplicator implements PromotionApplicator
{

	private Money $savedPrice;

	private array $productItemStack;

	private array $touchedProductItemIds;

	public function getPromotionResult(PromotionLocalization $promotionLocalization, array $productItems): PromotionResult
	{
		$this->touchedProductItemIds = [];
		$this->savedPrice = Money::of(0, $promotionLocalization->mutation->getSelectedCurrency());
		if (isset($promotionLocalization->promotion->typeQuantity->items)) {
			$quantityRows = (array) $promotionLocalization->promotion->typeQuantity->items;

			$this->productItemStack = ProductItemExpander::expandToStack($productItems);
			foreach ($quantityRows as $quantityRow) {
				while ($this->applyQuantityRow($promotionLocalization, $quantityRow)) { // phpcs:ignore
					// try to apply
				}
			}
		}

		return new PromotionResult($promotionLocalization, $this->savedPrice, $this->touchedProductItemIds);
	}

	private function applyQuantityRow(PromotionLocalization $promotionLocalization, \stdClass $quantityRow): bool
	{
		if ($quantityRow->amount > count($this->productItemStack)) {
			return false;
		}

		$foundForPromotion = array_slice($this->productItemStack, 0, $quantityRow->amount);
		$this->productItemStack = array_slice($this->productItemStack, $quantityRow->amount);

		foreach ($foundForPromotion as $item) {

			$productItemId = $item['productItem']->id;
			$this->touchedProductItemIds[$productItemId] = $productItemId;
			$savedPrice = $this->calculateSavedPrice($promotionLocalization, $item['price'], $quantityRow->price);
			$this->savedPrice = $this->savedPrice->plus(
				$savedPrice
			);
		}

		return true;
	}

	private function calculateSavedPrice(PromotionLocalization $promotionLocalization, Money $productItemPrice, float $value): Money
	{
		$currency = $promotionLocalization->mutation->getSelectedCurrency();
		$futurePrice = Money::zero($currency);

		if ($promotionLocalization->promotion->discountType === PromotionDiscountType::FixedPrice) { // TYPE discount price
			$futurePrice = Money::of($value, $currency)->multipliedBy(-1);
		} elseif ($promotionLocalization->promotion->discountType === PromotionDiscountType::Amount) { // TYPE discount amount
			$futurePrice = $productItemPrice->minus($value)->multipliedBy(-1);
		} elseif ($promotionLocalization->promotion->discountType === PromotionDiscountType::Percent) { // TYPE discoun percentage
			$futurePrice = $productItemPrice->multipliedBy((1 - ($value / 100)))->multipliedBy(-1);
		}

		if ($futurePrice->isLessThan(0)) {
			return $productItemPrice->plus($futurePrice);
		}

		return Money::zero($currency);
	}

}
