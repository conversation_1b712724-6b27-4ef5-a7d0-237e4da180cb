<?php declare(strict_types=1);

namespace App\Model\Promotion\Applicator;

use App\PostType\Promotion\Model\Orm\Promotion\PromotionLocalization;
use Brick\Money\Money;

class BonusItemApplicator implements PromotionApplicator
{

	private array $touchedProductItemIds;

	private Money $savedPrice;

	private array $productItemStack;

	public function getPromotionResult(PromotionLocalization $promotionLocalization, array $productItems): PromotionResult
	{
		$this->touchedProductItemIds = [];
		$this->savedPrice = Money::of(0, $promotionLocalization->mutation->getSelectedCurrency());

		if ( ! $this->isPromotionValid($promotionLocalization)) {
			return new PromotionResult($promotionLocalization, $this->savedPrice, $this->touchedProductItemIds);
		}

		$this->productItemStack = ProductItemExpander::expandToStack($productItems);

		$mainProductCount = $promotionLocalization->promotion->typeBonusItem->mainProductCount;
		$subordinateProductCount = $promotionLocalization->promotion->typeBonusItem->subordinateProductCount;
		$discountOnSubordinate = $promotionLocalization->promotion->typeBonusItem->discountOnSubordinate;

		while ($this->applyBonusItem($promotionLocalization, $mainProductCount, $subordinateProductCount, $discountOnSubordinate)) { // phpcs:ignore
			// try to apply
		}

		return new PromotionResult($promotionLocalization, $this->savedPrice, $this->touchedProductItemIds);
	}

	private function isPromotionValid(PromotionLocalization $promotionLocalization): bool
	{
		return isset($promotionLocalization->promotion->typeBonusItem->mainProductCount) && $promotionLocalization->promotion->typeBonusItem->mainProductCount > 0 &&
			isset($promotionLocalization->promotion->typeBonusItem->subordinateProductCount) && $promotionLocalization->promotion->typeBonusItem->subordinateProductCount > 0 &&
			isset($promotionLocalization->promotion->typeBonusItem->discountOnSubordinate) && $promotionLocalization->promotion->typeBonusItem->discountOnSubordinate > 0;
	}

	private function applyBonusItem(PromotionLocalization $promotionLocalization, int $mainProductCount, int $subordinateProductCount, int $discountOnSubordinate): bool
	{
		if ($mainProductCount > count($this->productItemStack)
			|| $discountOnSubordinate === 0) {

			return false;
		}

//		$conditionItems = array_slice($this->productItemStack, 0, $mainProductCount);
		$rest = array_slice($this->productItemStack, $mainProductCount);
		$discountedItems = array_slice($rest, count($rest) - $subordinateProductCount);
		$this->productItemStack = array_slice($rest, 0, count($rest) - count($discountedItems));

		foreach ($discountedItems as $discountedItem) {
			$productItemId = $discountedItem['productItem']->id;
			$this->touchedProductItemIds[$productItemId] = $productItemId;
			/** @var Money $discountPrice */
			$discountPrice = $discountedItem['price'];
			$discountPrice = $discountPrice->multipliedBy(($discountOnSubordinate / 100));
			$this->savedPrice = $this->savedPrice->plus(
				$discountPrice
			);
		}

		return true;
	}

}
