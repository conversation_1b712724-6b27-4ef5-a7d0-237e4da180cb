<?php declare(strict_types=1);

namespace App\Model\Promotion\Applicator;

use App\PostType\Promotion\Model\Orm\Promotion\PromotionLocalization;
use Brick\Money\Money;

class PromotionResult implements \JsonSerializable
{

	public function __construct(
		public readonly PromotionLocalization $promotionLocalization,
		public readonly Money $savedMoney,
		public readonly array $touchedProductItemIds,
	)
	{
	}

	public function getUniqId(): string
	{
		return md5((string) $this->toJson());
	}

	public function jsonSerialize(): array
	{
		return $this->toArray();
	}

	public function toJson(int $options = 0): string|false
	{
		return json_encode($this->jsonSerialize(), $options);
	}

	public function toArray(): array
	{
		return [
			'promotionLocalization' => [
				'id' => $this->promotionLocalization->id,
				'name' => $this->promotionLocalization->name,
			],
			'savedMoney' => [
				'amount' => $this->savedMoney->getAmount()->toFloat(),
				'currency' => $this->savedMoney->getCurrency()->getCurrencyCode(),
			],
			'touchedProductItemIds' => array_values($this->touchedProductItemIds),
		];
	}

}
