<?php declare(strict_types=1);

namespace App\Model\Promotion\Applicator;

use App\Model\Orm\Order\Product\ProductItem;

class ProductItemExpander
{

	public static function expandToStack(array $productItems): array
	{
		$productItemStack = [];
		foreach ($productItems as $productItem) {
			assert($productItem['productItem'] instanceof ProductItem);
			$count = $productItem['productItem']->amount;
			unset($productItem['amount']);
			$productItemStack = array_merge(
				$productItemStack,
				array_fill(0, $count, $productItem)
			);
		}

		return $productItemStack;
	}

}
