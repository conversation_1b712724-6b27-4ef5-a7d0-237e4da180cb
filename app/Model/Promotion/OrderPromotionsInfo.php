<?php declare(strict_types=1);

namespace App\Model\Promotion;

use App\Model\Promotion\Applicator\PromotionResult;

class OrderPromotionsInfo
{

	/** @var array <PromotionResult> */
	private array $promotionResults = [];

	public function __construct()
	{
	}

	public function add(PromotionResult $promotionResult): void
	{
		$this->promotionResults[] = $promotionResult;
	}


	public function getResults(): array
	{
		return $this->promotionResults;
	}

}
