<?php declare(strict_types=1);

namespace App\Model\Promotion;

use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\Product\ProductItem;
use App\PostType\Promotion\Model\PromotionModel;

class PromotionApplicator
{

	public function __construct(
		private readonly PromotionModel $promotionModel,
		private readonly ApplicatorProvider $applicatorProvider,
	)
	{
	}

	public function getOrderPromotionsInfo(Order $order): OrderPromotionsInfo
	{
		$productItemByPromotion = [];
		foreach ($order->getProducts() as $productItem) {
			assert($productItem instanceof ProductItem);
			$promotionLocalization = $this->promotionModel->getPromotionForProduct($productItem->variant->product, $order->mutation);

			if ($promotionLocalization !== null) {
				if (!isset($productItemByPromotion[$promotionLocalization->id])) {
					$productItemByPromotion[$promotionLocalization->id] = [
						'promotionLocalization' => $promotionLocalization,
						'items' => [],
					];
				}
				$productItemByPromotion[$promotionLocalization->id]['items'][] = [
					'price' => $productItem->unitPriceVat,
					'productItem' => $productItem,
				];
			}
		}

		$promotionOrderInfo = new OrderPromotionsInfo();
		foreach ($productItemByPromotion as $productsInfo) {
			$orderedMostExpensiveProductItems = $productsInfo['items'];
			usort($orderedMostExpensiveProductItems, function (array $infoA, array $infoB) {
				return $infoB['price'] <=> $infoA['price'];
			});
			$applicator = $this->applicatorProvider->getApplicator($productsInfo['promotionLocalization']);

			$promotionOrderInfo->add(
				$applicator->getPromotionResult($productsInfo['promotionLocalization'], $orderedMostExpensiveProductItems)
			);

		}
		return $promotionOrderInfo;
	}

}
