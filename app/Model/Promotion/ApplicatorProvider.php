<?php declare(strict_types=1);

namespace App\Model\Promotion;

use App\Model\Promotion\Applicator\BonusItemApplicator;
use App\Model\Promotion\Applicator\QuantityApplicator;
use App\PostType\Promotion\Model\Orm\Promotion\PromotionLocalization;
use App\PostType\Promotion\Model\Orm\Promotion\Types\PromotionType;

class ApplicatorProvider
{

	public function __construct(
		private readonly BonusItemApplicator $bonusItemApplicator,
		private readonly QuantityApplicator $quantityApplicator,
	)
	{
	}

	public function getApplicator(PromotionLocalization $promotionLocalization): \App\Model\Promotion\Applicator\PromotionApplicator
	{
		return match ($promotionLocalization->promotion->type) {
			PromotionType::typeQuantity => $this->quantityApplicator,
			PromotionType::typeBonusItem => $this->bonusItemApplicator,
		};
	}

}
