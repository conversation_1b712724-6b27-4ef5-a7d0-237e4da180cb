<?php

declare(strict_types=1);

namespace App\Model;

use Nette\Http\Session;
use Nette\Http\SessionSection;

final class EasyMessages
{
	public const KEY_CARD = 'card';
	public const KEY_ORDER_BASKET = 'orderBasket';

	public const TYPE_ERROR = 'error';
	public const TYPE_NOTICE = 'notice';
	public const TYPE_OK = 'ok';

	private SessionSection $session;

	public function __construct(Session $session)
	{
		$this->session = $session->getSection('easyMessages');
	}

	public function send(string $key, string $text, string $type = self::TYPE_ERROR, bool $uniqueMsg = true): void
	{
		if (!isset($this->session->$key)) {
			$this->session->$key = new \ArrayIterator();
		}

		if ($uniqueMsg) {
			foreach ($this->session->$key as $msg) {
				if ($msg->type === $type && $msg->text === $text) {
					return;
				}
			}
		}

		$msg = new \stdClass();
		$msg->type = $type;
		$msg->text = $text;
		$this->session->$key->append($msg);
	}


	public function get(string $key): iterable
	{
		if (isset($this->session->$key)) {
			$msgs = $this->session->$key;
			unset($this->session->$key);
			return $msgs;
		}

		return new \ArrayIterator();
	}

}
