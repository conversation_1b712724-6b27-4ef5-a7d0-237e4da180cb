<?php

declare(strict_types=1);

namespace App\Model\CustomContent;

use App\Model\CustomField\CustomFieldDefinition;
use App\Model\CustomField\CustomFieldsExtension;
use Nette\DI\CompilerExtension;
use Nette\DI\Definitions\Reference;
use Nette\Schema\Expect;
use Nette\Schema\Schema;

/**
 * @property-read array{definitions: array<string, array{fields: array<string, string|array<string, mixed>>}>, components: array<string, array<string, mixed>>, templates: array<string, string|string[]>} $config
 */
final class CustomContentExtension extends CompilerExtension
{
	private CustomFieldsExtension $customFieldsExtension;

	public function getConfigSchema(): Schema
	{
		$definitionSchema = CustomFieldsExtension::getDefinitionSchema();

		return Expect::structure([
			'definitions' => Expect::arrayOf(
				$definitionSchema,
				keyType: 'string',
			),

			'components' => Expect::arrayOf(
				Expect::structure([
					'icon' => Expect::string()->required(),
					'template' => Expect::string()->required(),
					'category' => Expect::string(),
					'hotkey' => Expect::bool(),
					'definition' => Expect::anyOf(
						$definitionSchema,
						Expect::string()->pattern('@.+'),
					),
				])->skipDefaults()->otherItems()->castTo('array'),
				keyType: 'string',
			),

			'templates' => Expect::arrayOf(
				Expect::anyOf(
					Expect::string()->pattern('\*'),
					Expect::listOf(
						Expect::string()->pattern('@.+'),
					)->required()->min(1),
				),
				keyType: 'string',
			)->required(),
		])->castTo('array');
	}

	public function loadConfiguration(): void
	{
		$builder = $this->getContainerBuilder();
		$this->requireCustomFieldsExtension();

		foreach ($this->config['definitions'] as $name => $definition) {
			$builder->addDefinition($this->prefix("definitions.$name"))
				->setType(CustomFieldDefinition::class)
				->setFactory($this->customFieldsExtension->processDefinition($definition))
				->setAutowired(false);
		}

		$allComponents = [];
		foreach ($this->config['components'] as $name => $component) {
			$allComponents[] = $builder->addDefinition($this->prefix("components.$name"))
				->setType(CustomComponent::class)
				->setFactory(CustomComponent::class, [\array_merge(
					$component,
					[
						'definition' => $this->customFieldsExtension->processDefinition($component['definition']),
					],
				)])
				->addTag(CustomComponent::class, $name)
				->setAutowired(false);

			$builder->addAlias($this->prefix($name), $this->prefix("components.$name"));
		}

		$builder->addDefinition($this->prefix('customContent'))
			->setType(CustomContent::class)
			->setFactory(CustomContent::class, [
				\array_map(
					static fn(string|array $components) => $components === '*'
						? $allComponents
						: \array_map(
							static fn(string $component) => new Reference(\substr($component, 1)),
							(array) $components,
						),
					$this->config['templates'],
				),
			]);
	}

	private function requireCustomFieldsExtension(): void
	{
		if ( ! isset($this->customFieldsExtension)) {
			$customFieldsExtensions = $this->compiler->getExtensions(CustomFieldsExtension::class);
			if (\count($customFieldsExtensions) === 0) {
				throw new \LogicException(\sprintf(
					'Cannot find extension %s required by %s. Please make sure that both extensions are registered and that they are registered in correct order.',
					CustomFieldsExtension::class,
					self::class,
				));
			}

			$this->customFieldsExtension = \reset($customFieldsExtensions);
		}
	}
}
