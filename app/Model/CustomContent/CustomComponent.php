<?php

declare(strict_types=1);

namespace App\Model\CustomContent;

use App\Model\CustomField\CustomFieldDefinition;

/**
 * @property-read CustomFieldDefinition $definition
 */
final class CustomComponent extends \stdClass implements \JsonSerializable
{
	/**
	 * @param array<string, mixed> $definition
	 */
	public function __construct(array $definition)
	{
		foreach ($definition as $key => $value) {
			$this->$key = $value;
		}
	}

	public function toArray(): array
	{
		$result = (array) $this;
		unset($result['definition']);

		return \array_merge(
			$result,
			['scheme' => \array_merge(
				(array) $this->definition,
				['draggable' => true, 'deletable' => true],
			)]
		);
	}

	public function jsonSerialize(): array
	{
		return $this->toArray();
	}
}
