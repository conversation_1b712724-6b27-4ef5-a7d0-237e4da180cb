<?php declare(strict_types = 1);

namespace App\Model;

use InvalidArgumentException;
use Nette\Utils\Strings;
use phpseclib3\Math\PrimeField\Integer;
use function preg_replace;

final class StringHelper
{

	private function __construct()
	{
	}

	/**
	 * Remove empty p tag at the end of content (content is generated in tinyMce)
	 */
	public static function removeTinyMceEmptyP(string $s): string
	{
		return preg_replace('/(<p>\xC2\xA0<\/p>(\r?\n))*<p>\xC2\xA0<\/p>$/', '', $s);
	}

	public static function removeTinyMceWrapperP(string $string, array $tags, string $replace = ''): string
	{
		foreach ($tags as $tag) {
			$string = preg_replace("/<\\/?" . $tag . "(.|\\s)*?>/", $replace, $string);
		}

		return $string;
	}

	public static function generateRandomString(int $length = 8, string $chars = 'ěščřžýáabcdefghijklmnopqrstuvwxyz'): string
	{
		$count = mb_strlen($chars);

		for ($i = 0, $result = ''; $i < $length; $i++) {
			$index = rand(0, $count - 1);
			$result .= mb_substr($chars, $index, 1);
		}

		return Strings::firstUpper($result);
	}

	public static function stringToNull(mixed $value): ?string
	{
		$value = trim((string)$value);
		return empty($value) ? null : $value;
	}

	public static function intToNull(mixed $value): ?int
	{
		return empty($value) ? null : (int)$value;
	}

	public static function floatToNull(mixed $value): ?float
	{
		return empty($value) ? null : (float)$value;
	}

	public static function arrayToNull(mixed $value): ?array
	{
		return empty($value) ? null : (array)$value;
	}

	public static function formatUnits(int $grams, int $precision = 1): string
	{
		$units = array('g', 'kg', 't');
		$grams = max($grams, 0);
		$pow = floor(($grams ? log($grams) : 0) / log(1000));
		$pow = min($pow, count($units) - 1);
		$grams /= pow(1000, $pow);

		return round($grams, $precision) . ' ' . $units[$pow];
	}

	public static function getImageExtFromMimeType(int $type): string
	{
		return match ($type) {
			IMAGETYPE_JPEG => '.jpg',
			IMAGETYPE_PNG => '.png',
			IMAGETYPE_GIF => '.gif',
			IMAGETYPE_WEBP => '.webp',
			default => throw new InvalidArgumentException('Unsupported image type.'),
		};
	}

}
