<?php

declare(strict_types=1);

namespace App\Model;

use function sprintf;
use function str_ends_with;

final class AdminEmails
{

	/**
	 * @param list<string> $adminEmailDomains
	 * @param list<string> $developerEmailDomains
	 */
	public function __construct(
		private readonly array $adminEmailDomains,
		private readonly array $developerEmailDomains,
	) {}

	public function isAdminEmail(string $email): bool
	{
		foreach ($this->adminEmailDomains as $domain) {
			if (str_ends_with($email, sprintf('%s', $domain))) {
				return true;
			}
		}

		return false;
	}

	public function isDeveloperEmail(string $email): bool
	{
		foreach ($this->developerEmailDomains as $domain) {
			if (str_ends_with($email, sprintf('@%s', $domain))) {
				return true;
			}
		}

		return false;
	}

}
