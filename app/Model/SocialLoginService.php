<?php

namespace App\Model;

use App\Model\Link\LinkFactory;
use App\Model\Mutation\MutationHolder;
use Exception;
use Nette\Application\LinkGenerator;
use Nette\Http\IRequest;
use Nette\Http\IResponse;
use Nette\Http\Session;
use Nette\NotImplementedException;
use VencaX\FacebookLogin;
use VencaX\SocialLogin;
use VencaX\TwitterLogin;
use WebChemistry\OAuth2\Client\Seznam\Provider\Seznam;

class SocialLoginService
{

	private FacebookLogin|null $facebookLogin = null;

	private Seznam|null $seznamLogin = null;

	public function __construct(
		private readonly ConfigService $configService,
		private readonly IResponse $httpResponse,
		private readonly IRequest $httpRequest,
		private readonly Session $session,
		private readonly MutationHolder $mutationHolder,
		private readonly LinkFactory $linkFactory,
		private readonly LinkGenerator $linkGenerator,
	)
	{
	}

	public function getFacebook(): FacebookLogin
	{
		if ($this->facebookLogin === null) {
			$this->initFacebookLogin();
		}

		return $this->facebookLogin;
	}

	public function getTwitter(): TwitterLogin
	{
		throw new NotImplementedException('Twitter login service is not implemented!');
	}

	public function getSeznam(): Seznam
	{
		if ($this->seznamLogin === null) {
			$this->initSeznamLogin();
		}

		return $this->seznamLogin;
	}

	private function initFacebookLogin(): void
	{
		$configSocial = $this->configService->get('socialLogin');

		$configSocial['facebook']['params']['callbackURL'] = $this->createRedirectUri(['do' => 'signInForm-facebookLogin-response']);

		$socialLogin = new SocialLogin(
			[
				'facebook' => $configSocial['facebook']['params'],
			],
			$configSocial['cookieName'],
			$this->httpResponse,
			$this->httpRequest,
			$this->session
		);

		$socialLogin->facebook->setScope($configSocial['facebook']['scope']);

		$this->facebookLogin = $socialLogin->facebook;
	}

	private function initSeznamLogin(): void
	{
		$configSocial = $this->configService->get('socialLogin');

		if (!isset($configSocial['seznam']['params']['clientId'], $configSocial['seznam']['params']['clientSecret'])) {
			throw new Exception('Missing configuration for seznam login!');
		}

		$configSocial['seznam']['params']['redirectUri'] = $this->createRedirectUri(['do' => 'signInForm-seznamLogin-response']);

		$this->seznamLogin = new Seznam($configSocial['seznam']['params']);
	}

	private function createRedirectUri(array $args): string
	{
		[$destination, $params] = $this->linkFactory->linkInPresenter($this->mutationHolder->getMutation()->pages->userLogin, $args);
		$destination = ltrim($destination, ':');

		return $this->linkGenerator->link($destination, $params);
	}

}
