<?php declare(strict_types = 1);

namespace App\Model\StaticPage;

use App\Model\Orm\Mutation\Mutation;
use Nette\SmartObject;

class StaticPage
{

	public string $id;

	public Mutation $mutation;

	public string $name;

	public string $nameTitle;

	public string $nameAnchor;

	public string $description;

	public string $keywords;

	public string $annotation;

	public string $uid;

	public array $path;

	use SmartObject;

	protected function setId(string $id): void
	{
		$this->id = $id;
	}

	public function getId(): string
	{
		return $this->id;
	}


	protected function setMutation(Mutation $mutation): void
	{
		$this->mutation = $mutation;
	}

	public function getMutation(): Mutation
	{
		return $this->mutation;
	}


	protected function setName(string $name): void
	{
		$this->name = $name;
	}

	protected function setNameTitle(string $nameTitle): void
	{
		$this->nameTitle = $nameTitle;
	}

	protected function setNameAnchor(string $nameAnchor): void
	{
		$this->nameAnchor = $nameAnchor;
	}

	public function getName(): string
	{
		return $this->name;
	}

	public function getNameTitle(): string
	{
		return $this->nameTitle;
	}

	public function getNameAnchor(): string
	{
		return $this->nameAnchor;
	}

	protected function setDescription(string $description = ''): void
	{
		$this->description = $description;
	}

	public function getDescription(): string
	{
		return $this->description;
	}

	protected function setAnnotation(string $annotation = ''): void
	{
		$this->annotation = $annotation;
	}

	public function getAnnotation(): string
	{
		return $this->annotation;
	}

	protected function setUid(string $uid): void
	{
		$this->uid = $uid;
	}

	public function getUid(): string
	{
		return $this->uid;
	}

	protected function setPath(array $path): void
	{
		$this->path = $path;
	}

	public function getPath(): array
	{
		return $this->path;
	}

	protected function setKeywords(string $keywords): void
	{
		$this->keywords = $keywords;
	}

	public function getKeywords(): string
	{
		return $this->keywords;
	}

}
