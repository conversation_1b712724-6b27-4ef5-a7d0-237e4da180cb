<?php declare(strict_types = 1);

namespace App\Model\StaticPage;

use App\Model\Orm\Mutation\Mutation;

class Page500 extends StaticPage
{
	public string $template = '';
	public function __construct(
		Mutation $mutation,
		string $name,
		string $annotation = '',
		string $description = '',
	)
	{
		$this->setMutation($mutation);
		$this->setName($name);
		$this->setNameTitle($name);
		$this->setNameAnchor($name);
		$this->setAnnotation($annotation);
		$this->setDescription($description);
		$this->setKeywords('');

		$this->setPath([
			$this->mutation->pages->title->id
		]);

		$this->setId('page_500');
		$this->setUid('500');
	}

}
