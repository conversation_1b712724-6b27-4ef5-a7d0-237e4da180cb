<?php declare(strict_types=1);

namespace App\Model\Orm\EmailTemplate;

use App\Model\Orm\Mutation\Mutation;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method EmailTemplate|null getById($id)
 * @method ICollection<EmailTemplate> searchByName(int $id, int $mutationId, array $excluded)
 * @method ICollection<EmailTemplate> findByFilter(?ArrayHash $filter)
 * @method void cloneAll(Mutation $targetMutation, Mutation $sourceMutation)
 *
 * @extends Repository<EmailTemplate>
 */
final class EmailTemplateRepository extends Repository
{
	static function getEntityClassNames(): array
	{
		return [EmailTemplate::class];
	}

}
