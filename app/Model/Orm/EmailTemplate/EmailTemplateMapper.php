<?php declare(strict_types=1);

namespace App\Model\Orm\EmailTemplate;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\TableHelper;
use App\Model\Orm\Traits\HasCamelCase;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<EmailTemplate>
 */
final class EmailTemplateMapper extends DbalMapper
{
	use HasCamelCase;

	/**
* @return literal-string
*/
public function getTableName(): string
{
	return 'email_template';
}

	/**
	 * @return ICollection<EmailTemplate>
	 */
	public function searchByName(string $q, int $mutationId, array $excluded = []): ICollection
	{
		$builder = $this->builder()
			->andWhere('name LIKE %_like_', $q);

		if ($mutationId) {
			$builder->andWhere('mutationId = %i', $mutationId);
		}
		if ($excluded) {
			$builder->andWhere('id not in %i[]', $excluded);
		}

		return $this->toCollection($builder);
	}

	/**
	 * @return ICollection<EmailTemplate>
	 */
	public function findByFilter(?ArrayHash $filter): ICollection
	{
		$builder = $this->builder()->select('et.*')->from($this->getTableName(), 'et');

		if (!empty($filter->mutation)) {
			$builder->andWhere('mutationId = %i', $filter->mutation);
		}

		if (!empty($filter->fulltext)) {
			$builder->andWhere('( et.name LIKE %_like_ OR et.subject LIKE %_like_ )', $filter->fulltext, $filter->fulltext);
		}

		/*if (isset($filter->isDeveloper) && $filter->isDeveloper) {
			$builder->andWhere('isDeveloper = %b', $filter->isDeveloper);
		}*/

		//$builder->groupBy('et.id');

		return $this->toCollection($builder);
	}


	public function cloneAll(Mutation $targetMutation, Mutation $sourceMutation): void
	{
		$columns = TableHelper::getTableColumns($this->connection, $this->getTableName());
		// clean old items
		$this->connection->query('DELETE FROM email_template WHERE mutationId = %i', $targetMutation->id);

		$this->connection->query('CREATE TEMPORARY TABLE email_template_copy_tmp SELECT * FROM email_template WHERE mutationId = %i', $sourceMutation->id);
		$this->connection->query('UPDATE email_template_copy_tmp SET mutationId = %i', $targetMutation->id);
		$this->connection->query('INSERT INTO `email_template`  (%column[])  SELECT %column[] FROM email_template_copy_tmp', $columns, $columns);
		$this->connection->query('DROP TEMPORARY TABLE IF EXISTS email_template_copy_tmp');
	}

}
