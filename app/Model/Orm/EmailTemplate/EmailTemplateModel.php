<?php declare(strict_types = 1);

namespace App\Model\Orm\EmailTemplate;

use App\Model\Orm\EmailTemplateFile\EmailTemplateFile;
use App\Model\Orm\EmailTemplateFile\EmailTemplateFileRepository;
use App\Model\Orm\File\FileRepository;
use App\Model\Orm\Mutation\Mutation;

final class EmailTemplateModel
{

	public function __construct(
		private readonly EmailTemplateRepository $emailTemplateRepository,
		private readonly EmailTemplateFileRepository $emailTemplateFileRepository,
		private readonly FileRepository $fileRepository,
	)
	{
	}

	public function getByKey(string $key, Mutation $mutation): ?EmailTemplate
	{
		return $this->emailTemplateRepository->getBy([
			'key' => $key,
			'mutation' => $mutation,
		]);
	}

	public function save(?EmailTemplate $emailTemplate, mixed $data): EmailTemplate
	{
		if ($emailTemplate === null) {
			$emailTemplate = new EmailTemplate();
			$this->emailTemplateRepository->attach($emailTemplate);
		}

		$intValues = ['isDeveloper'];
		$boolValues = ['isDeveloper'];
		$noSave = [];

		foreach ($emailTemplate->getMetadata()->getProperties() as $i) {

			$col = (string) $i->name;

			if (in_array($col, $noSave)) {
				continue;
			}

			if (isset($data[$col])) {
				if (in_array($col, $intValues)) {
					$data[$col] = $data[$col] === 'on' ? 1 : (int) $data[$col]; // !!! (int) 'on' = 0    !!!!!!
				}

				$emailTemplate->$col = $data[$col];
			} else {
				if (in_array($col, $boolValues)) {
					$emailTemplate->$col = 0;
				}
			}
		}

		$this->handleFiles($emailTemplate, $data);

		$this->emailTemplateRepository->persistAndFlush($emailTemplate);
		return $emailTemplate;
	}

	private function handleFiles(EmailTemplate $emailTemplate, mixed $data): void
	{
		$actualFiles = [];
		foreach ($emailTemplate->files as $i) {
			$actualFiles[$i->file] = $i->id;
		}

		if (isset($data['fileName'])) {
			$newSort = 0;

			foreach ($data['fileName'] as $fileId => $f) {
				if (isset($actualFiles[$fileId])) { // edit
					$fileEdit = $this->emailTemplateFileRepository->getById($actualFiles[$fileId]);
					$fileEdit->name = $data['fileName'][$fileId];
					$fileEdit->sort = $newSort;
					$fileEdit->size = $data['fileSize'][$fileId];
					unset($actualFiles[$fileId]);

				} else { // add
					$file = $this->fileRepository->getById($fileId);

					if ($file !== null) {
						$newFile = new EmailTemplateFile();
						$newFile->name = $data['fileName'][$fileId];
						$newFile->sort = $newSort;
						$newFile->size = $data['fileSize'][$fileId];
						$newFile->url = $file->url;
						$newFile->file = $fileId;

						$emailTemplate->files->add($newFile);
					}
				}

				$newSort++;
			}
		}

		// remove
		foreach ($actualFiles as $fileId => $connId) {
			$file = $this->emailTemplateFileRepository->getById($connId);
			$this->emailTemplateFileRepository->remove($file);
		}
	}

	public function delete(EmailTemplate $emailTemplate): void
	{
		$this->emailTemplateRepository->removeAndFlush($emailTemplate);
	}

	public function create(Mutation $mutation): EmailTemplate
	{
		$newEmailTemplate = new EmailTemplate();
		$this->emailTemplateRepository->attach($newEmailTemplate);
		$newEmailTemplate->mutation = $mutation;
		$newEmailTemplate->key = '';
		$this->emailTemplateRepository->persistAndFlush($newEmailTemplate);

		return $newEmailTemplate;
	}

}
