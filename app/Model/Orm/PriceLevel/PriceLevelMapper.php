<?php

declare(strict_types=1);

namespace App\Model\Orm\PriceLevel;

use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;
use Nextras\Orm\Mapper\Mapper;

/**
 * @extends DbalMapper<PriceLevel>
 */
final class PriceLevelMapper extends DbalMapper
{
	use HasCamelCase;

	/**
* @return literal-string
*/
public function getTableName(): string
{
	return 'price_level';
}

	/**
	 * @return ICollection<PriceLevel>
	 */
	public function findAllWithDiscountPriceLevel(): ICollection
	{
		$builder = $this->builder();
		$builder->andWhere('[discountPriceId] IS NOT NULL');

		return $this->toCollection($builder);
	}

}
