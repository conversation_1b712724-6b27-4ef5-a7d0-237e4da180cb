<?php

declare(strict_types=1);

namespace App\Model\Orm\PriceLevel;

use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method PriceLevel|null getById($id)
 * @method ICollection<PriceLevel>  findAllWithDiscountPriceLevel()
 *
 * @extends Repository<PriceLevel>
 */
final class PriceLevelRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [PriceLevel::class];
	}

	public function getDefault(): PriceLevel
	{
		return $this->getByIdChecked(PriceLevel::DEFAULT_ID);
	}

}
