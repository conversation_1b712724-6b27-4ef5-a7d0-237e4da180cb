<?php

declare(strict_types = 1);

namespace App\Model\Orm\PriceLevel;

use App\Model\Orm\ProductVariantPriceLog\ProductVariantPriceLog;
use App\Model\Orm\User\User;
use App\Model\Orm\ProductVariantPrice\ProductVariantPrice;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property int $id {primary}
 * @property string $type {enum self::TYPE_*}
 * @property string $name
 * @property float|null $discount {default null}
 * @property float|null $discountSubscription {default null}
 * @property float|null $discountSubscriptionFirst {default null}
 * @property int $sort {default 100}
 *
 *
 * RELATIONS
 * @property OneHasMany<ProductVariantPrice> $prices {1:m ProductVariantPrice::$priceLevel, cascade=[persist, remove]}
 * @property OneHasMany<User> $users {1:m User::$priceLevel, cascade=[persist, remove]}
 * @property OneHasMany<ProductVariantPriceLog> $priceLogs {1:m ProductVariantPriceLog::$priceLevel, cascade=[persist, remove]}
 *
 * VIRTUAL
 * @property-read  bool $isErpSource {virtual}
 * @property-read  bool $isEditDiscount {virtual}
 * @property-read  bool $isEditDiscountSubscriptions {virtual}
 * @property-read  bool $isTypeDefault {virtual}
 * @property-read  bool $isTypeRegistered {virtual}
 * @property-read  bool $isTypeShelter {virtual}
 * @property-read  bool $isTypeRecommended {virtual}
 * @property-read  bool $isTypePurchase {virtual}
 */
class PriceLevel extends Entity
{
	public const int DEFAULT_ID = 1;

	public const string TYPE_DEFAULT = 'default'; // std prodejni cena
	public const string TYPE_REGISTERED = 'registered'; // registrovani
	public const string TYPE_SHELTER = 'shelter'; // utulky
	public const string TYPE_RECOMMENDED = 'recommended'; // katalogova cena (ERP)
	public const string TYPE_PURCHASE = 'purchase'; // nakupni cena (ERP)

	protected function getterIsErpSource(): bool
	{
		return $this->type === self::TYPE_PURCHASE || $this->type === self::TYPE_RECOMMENDED;
	}

	protected function getterIsEditDiscount(): bool
	{
		return $this->type === self::TYPE_REGISTERED || $this->type === self::TYPE_SHELTER;
	}

	protected function getterIsEditDiscountSubscriptions(): bool
	{
		return in_array($this->type, [self::TYPE_DEFAULT, self::TYPE_REGISTERED, self::TYPE_SHELTER]);
	}

	protected function getterIsTypeDefault(): bool
	{
		return $this->type === self::TYPE_DEFAULT;
	}

	protected function getterIsTypeRegistered(): bool
	{
		return $this->type === self::TYPE_REGISTERED;
	}

	protected function getterIsTypeShelter(): bool
	{
		return $this->type === self::TYPE_SHELTER;
	}

	protected function getterIsTypeRecommended(): bool
	{
		return $this->type === self::TYPE_RECOMMENDED;
	}

	protected function getterIsTypePurchase(): bool
	{
		return $this->type === self::TYPE_PURCHASE;
	}

}
