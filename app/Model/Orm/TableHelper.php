<?php declare(strict_types = 1);

namespace App\Model\Orm;

use Nextras\Dbal\IConnection;

class TableHelper
{

	public static function getTableColumns(IConnection $connection, string $tableName): array
	{
		$columnNames = [];
		foreach ($connection->getPlatform()->getColumns($tableName) as $column) {
			if ( ! $column->isPrimary) {
				$columnNames[] = $column->name;
			}
		}

		return $columnNames;
	}

}
