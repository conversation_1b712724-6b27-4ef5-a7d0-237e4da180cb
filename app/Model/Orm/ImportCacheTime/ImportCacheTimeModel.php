<?php declare(strict_types = 1);

namespace App\Model\Orm\ImportCacheTime;



use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;

final readonly class ImportCacheTimeModel {


	public function __construct(
		private ImportCacheTimeRepository $importCacheTimeRepository
	)
	{
	}

	public function storeFromImport(string $importType, ArrayHash $data): void
	{
		$entity = $this->importCacheTimeRepository->getBy(['type' => $importType]);

		if ($entity === null) {
			$entity = new ImportCacheTime();
			$entity->type = $importType;
		} else {
			assert($entity instanceof ImportCacheTime);

			$historyRow = $entity->jsonSerialize();
			$history = $entity->history;
			array_unshift($history, $historyRow);
			if (count($history) > 10) {
				array_pop($history);
			}
			$entity->history = $history;
		}

		$entity->updatedAt = new DateTimeImmutable();
		$entity->generatedAt = $data->generatedAt;
		$entity->numRecords = $data->numRecords;

		$this->importCacheTimeRepository->persistAndFlush($entity);
	}

}
