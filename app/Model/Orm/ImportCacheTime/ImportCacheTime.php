<?php declare(strict_types = 1);

namespace App\Model\Orm\ImportCacheTime;

use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;
use App\Model\Orm\JsonArrayContainer; // phpcs:ignore

/**
 * @property int $id {primary}
 * @property string $type
 * @property DateTimeImmutable $updatedAt {default now}
 * @property DateTimeImmutable $generatedAt
 * @property int $numRecords {default 0}
 * @property array $history {container JsonArrayContainer}
 *
 * RELATIONS
 *
 * VIRTUAL
 */
class ImportCacheTime extends Entity implements \JsonSerializable
{

	public function getGeneratedAt(int $secondsPush = 10): DateTimeImmutable|\DateTimeImmutable
	{
		return $this->generatedAt->modify('-' . $secondsPush . ' seconds');
	}

	public function jsonSerialize(): array
	{
		return [
			'generatedAt' => $this->generatedAt->format('Y-m-d H:i:s'),
			'numRecords' => $this->numRecords,
		];
	}

}
