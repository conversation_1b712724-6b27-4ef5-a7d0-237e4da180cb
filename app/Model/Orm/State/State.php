<?php declare(strict_types = 1);

namespace App\Model\Orm\State;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\ClassEvent\ClassEvent;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Synchronizable;
use App\Model\Orm\User\User;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;
use App\Model\Orm\Traits\HasFormDefaultData;

/**
 * @property int $id {primary}
 * @property string|null $extId {default null}
 * @property string $code iso2
 * @property string $isoCode {virtual}
 * @property string $name
 * @property VatRates $vatRates {embeddable}
 * @property int $public {default 1}
 *
 * RELATIONS
 * @property ManyHasMany<Mutation> $mutations {m:m Mutation::$states}
 * @property OneHasMany<User>|null $users {1:m User::$state}
 * @property OneHasMany<ClassEvent> $classEvents {1:m ClassEvent::$state}
 */
class State extends BaseEntity implements Synchronizable
{

	use HasFormDefaultData;

	public const DEFAULT_ID = 1;
	public const DEFAULT_CODE = 'CZ';

	public const COOKIE_NAME_SELECTED_STATE = 'selectedState';
	public const CODE_CZ = self::DEFAULT_CODE;
	public const CODE_SK = 'SK';

	public function getterIsoCode(): ?string
	{
		$constantName = 'ISO_CODE_' . strtoupper($this->code);
		if (defined(Mutation::class. '::' . $constantName)) {
			return constant(Mutation::class. '::' . $constantName);
		}
		return null;
	}

	public function getExternalId(): ?int
	{
		if ($this->extId === null) {
			return null;
		}
		return (int) $this->extId;
	}

	public function setExternalId(?string $externalId): void
	{
		$this->extId = $externalId;
	}

}
