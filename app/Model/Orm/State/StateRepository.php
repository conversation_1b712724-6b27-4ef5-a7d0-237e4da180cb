<?php declare(strict_types=1);

namespace App\Model\Orm\State;

use App\Model\Orm\CollectionById;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Searchable;
use App\Model\Orm\Traits\HasStaticCache;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;
use App\Model\Orm\Traits\HasPublicParameter;

/**
 * @method State getById($id)
 * @method State getByChecked(array $cond)
 *
 * @extends Repository<State>
 */
final class StateRepository extends Repository implements Searchable, CollectionById
{

	use HasPublicParameter;
	use HasStaticCache;

	public static function getEntityClassNames(): array
	{
		return [State::class];
	}

	public function getPublicOnlyWhereParams(): array
	{
		return [
			'public' => 1,
		];
	}


	public function getDefault(Mutation $mutation): State
	{
		return $this->tryLoadCache('getDefault-M-' . $mutation->id, function () use ($mutation) {
			$defaultState = $this->findBy(['mutations->id' => $mutation->id])->fetch();
			if ($defaultState === null) {
				$defaultState = $this->getByChecked(['code' => State::DEFAULT_CODE]);
			}
			return $defaultState;
		});
	}
	/**
	 * @return ICollection<State>
	 */
	public function searchByName(string $string, array $excludedIds = []): ICollection
	{
		return $this->getMapper()->searchByName($string, $excludedIds);
	}

	public function getMapper(): StateMapper
	{
		$mapper = parent::getMapper();
		assert($mapper instanceof StateMapper);
		return $mapper;
	}
	/**
	 * @return ICollection<State>
	 */
	public function findByIdOrder(array $ids): ICollection
	{
		return $this->$this->getMapper()->findByIdOrder($ids);
	}

	public function getByExtId(int|string $id): ?State
	{
		return $this->getBy(['extId' => (string) $id]);
	}

}
