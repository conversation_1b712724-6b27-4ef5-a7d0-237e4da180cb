<?php

declare(strict_types=1);

namespace App\Model\Orm\State;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Traits\HasCache;
use App\Model\Orm\Traits\HasStaticCache;

final class StateModel
{
	use HasStaticCache;

	public function __construct()
	{}


	/**
	 * Vrati hodnoty vsech sazeb DPH pro vsechny publikovane staty jako strukturovane pole
	 * @return array<int, VatRates>
	 */
	public function getAllVatRatesValues(Mutation $mutation): array
	{
		return $this->tryLoadCache('allVatRatesValues-M-' . $mutation->id, function () use ($mutation) {
			$states = $mutation->states->toCollection()->findBy([
				'public' => 1
			])->fetchAll();

			$vatRatesList = [];
			foreach ($states as $state) {
				$vatRatesList[$state->id] = $state->vatRates;
			}

			return $vatRatesList;
		});
	}

}
