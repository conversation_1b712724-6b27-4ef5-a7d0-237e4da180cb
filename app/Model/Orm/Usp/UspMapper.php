<?php

declare(strict_types=1);

namespace App\Model\Orm\Usp;

use App\Model\Orm\ProductProduct\ProductProduct;
use App\Model\Orm\Traits\HasCamelCase;
use App\PostType\Tag\Model\Orm\TagLocalization\TagLocalization;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<Usp>
 */
class UspMapper extends DbalMapper
{

	use HasCamelCase;

	/**
	 * @return literal-string
	 */
	public function getTableName(): string
	{
		return 'usp';
	}

	/**
	 * @return ICollection<Usp>
	 */
	public function findByIdOrder(array $ids): ICollection
	{
		$builder = $this->builder()
		                ->andWhere('id in %i[]', $ids)
		                ->orderBy('%raw', 'FIELD(id, ' . implode(',', $ids) . ')');

		return $this->toCollection($builder);
	}
}
