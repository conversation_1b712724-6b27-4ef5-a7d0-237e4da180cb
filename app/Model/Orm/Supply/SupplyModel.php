<?php

namespace App\Model\Orm\Supply;

use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\Supply\DTO\SupplyDTO;
use Nextras\Dbal\Result\Row;

class SupplyModel
{

	public function __construct(
		private SupplyRepository $supplyRepository,
	)
	{
	}

	/**
	 * @return array<SupplyDTO>
	 */
	public function findSuppliesDTO(ProductVariant $variant): array
	{
		$result = $this->supplyRepository->findSuppliesByVariant($variant);
		return array_map(fn(Row $supply) => SupplyDTO::createFromRow($supply), $result->fetchAll());
	}

}
