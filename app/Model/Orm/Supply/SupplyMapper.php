<?php

declare(strict_types=1);

namespace App\Model\Orm\Supply;

use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<Supply>
 */
final class SupplyMapper extends DbalMapper
{

	use HasCamelCase;

	/**
* @return literal-string
*/
public function getTableName(): string
{
	return 'stock_supplies';
}

	public function findSuppliesByVariant(ProductVariant $variant): Result
	{
		return $this->connection->query('select * from stock_supplies where variantId = %i', $variant->getId());
	}

}
