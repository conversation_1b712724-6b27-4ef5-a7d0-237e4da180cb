<?php

declare(strict_types=1);

namespace App\Model\Orm\Supply;

use App\Model\Orm\ProductVariant\ProductVariant;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Repository\Repository;

/**
 * @method Supply getById($id)
 * @method Result findSuppliesByVariant(ProductVariant $variant)
 *
 * @extends Repository<Supply>
 */
final class SupplyRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [Supply::class];
	}

}
