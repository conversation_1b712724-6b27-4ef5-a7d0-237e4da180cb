<?php declare(strict_types = 1);

namespace App\Model\Orm\ShopReview;

use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method ShopReview|null getById($id)
 * @method ICollection<ShopReview> findBy()
 * @method ICollection<ShopReview> findByIds()
 * @extends Repository<ShopReview>
 */
final class ShopReviewRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [ShopReview::class];
	}

}
