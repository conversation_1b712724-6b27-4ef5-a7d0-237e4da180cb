<?php declare(strict_types = 1);

namespace App\Model\Orm\Rate;

use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<Rate>
 */
class RateMapper extends DbalMapper
{

	use HasCamelCase;

	/**
* @return literal-string
*/
public function getTableName(): string
{
	return 'rate';
}


	public function findLastTwo(string $currency): array
	{
		$builder = $this->builder()->select('r.*')->from('rate', 'r');
		$builder->andWhere('currency = %s', $currency);
		$builder->orderBy('date DESC');
		$builder->limitBy(2);

		return $this->toCollection($builder)->fetchPairs(null, 'rate');
	}

	public function deleteOlderAs(DateTimeImmutable $date): int
	{
		$this->connection->query('DELETE FROM %table WHERE `date` <= %dt', $this->getTableName(), $date->modify('midnight'));
		return $this->connection->getAffectedRows();
	}
}
