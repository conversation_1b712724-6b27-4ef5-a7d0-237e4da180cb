<?php
declare(strict_types = 1);

namespace App\Model\Orm\Rate;

use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;


// phpcs:ignore

/**
 * @property int $id                      {primary}
 * @property string $currency
 * @property DateTimeImmutable $date    {default now}
 * @property float $rate
 *
 * RELATIONS
 *
 *
 * VIRTUAL

 */
class Rate extends Entity
{
	const DEFAULT_RATES = [
		'EUR' => 25.999,
	];

}
