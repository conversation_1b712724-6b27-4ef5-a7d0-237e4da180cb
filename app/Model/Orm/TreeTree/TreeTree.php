<?php declare(strict_types = 1);

namespace App\Model\Orm\TreeTree;

use App\PostType\Page\Model\Orm\Tree;
use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property int $sort
 * @property string $type {enum self::TYPE_*}
 *
 *
 * RELATIONS
 * @property Tree|null $mainTree {m:1 Tree::$treeTrees}
 * @property Tree|null $attachedTree {m:1 Tree::$attachedTreeTrees}
 *
 *
 * VIRTUAL
 */
class TreeTree extends Entity
{

	public const TYPE_NORMAL = 'normal';
	public const TYPE_LINKED_CATEGORY = 'linkedCategory';
}
