<?php declare(strict_types = 1);

namespace App\Model\Orm\ApiUser;

use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Nette\Utils\Random;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;
use UnexpectedValueException;

/**
 * @property int $id {primary}
 * @property string $username
 * @property string $password
 * @property string $jwtSecret
 * @property bool $active {default true}
 * @property int $tokenValidity {default self::DEF_TOKEN_VALIDITY}
 * @property DateTimeImmutable $createdTime {default now}
 * @property DateTimeImmutable $updatedTime {default now}
 *
 * RELATIONS
 *
 * VIRTUAL
 */
class ApiUser extends Entity
{

	public const DEF_TOKEN_VALIDITY = 24; //hours
	public const JWT_ALG = 'HS256';

	public static function generateJwtSecret(): string
	{
		return Random::generate(32, '0-9A-Za-z\+\-\*\&\(\)\$\^');
	}

	public function onCreate(): void
	{
		parent::onCreate();

		$this->jwtSecret = self::generateJwtSecret();
	}

	public function regenerateJwtSecret(): void
	{
		$this->jwtSecret = self::generateJwtSecret();
	}

	/**
	 * @return array{jwt: string, expires: DateTimeImmutable}
	 */
	public function generateJwt(): array
	{
		$expires = new DateTimeImmutable('+' . $this->tokenValidity . ' hours');
		$payload = [
			'user' => $this->username,
			'iat' => time(),
			'exp' => $expires->format('U'),
		];

		return ['jwt' => JWT::encode($payload, $this->jwtSecret, self::JWT_ALG), 'expires' => $expires];
	}

	public function validateJwt(string $jwt): void
	{
		JWT::$leeway = 60;
		$payload = JWT::decode($jwt, new Key($this->jwtSecret, self::JWT_ALG));
		if ($payload->user !== $this->username) {
			throw new UnexpectedValueException('Invalid username');
		}
	}

}
