<?php declare(strict_types = 1);

namespace App\Model\Orm;

use App\Model\Mutation\MutationHolder;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Orm;

final class OrmCleaner
{

	public function __construct(
		private readonly Orm $orm,
		private readonly MutationHolder $mutationHolder,
		private readonly MutationsHolder $mutationsHolder,
	)
	{
	}

	public function safeClear(): void
	{
		$this->orm->flush();

		$ormMutationId = $this->orm->hasMutation() ? $this->orm->getMutation()->id : null;
		$holderMutationId = $this->mutationHolder->getMutation()->id;

		$this->orm->clear();
		$this->mutationsHolder->flush();

		if (isset($ormMutationId)) {
			$this->orm->setMutation($this->orm->mutation->getById($ormMutationId));
		}

		$this->mutationHolder->setMutation($this->orm->mutation->getById($holderMutationId));
	}

}
