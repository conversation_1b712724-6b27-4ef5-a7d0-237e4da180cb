<?php declare(strict_types = 1);

namespace App\Model\Orm\ClassSection;

use App\Model\Orm\Traits\HasCamelCase;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends <PERSON>balMapper<ClassSection>
 */
class ClassSectionMapper extends Dbal<PERSON>apper
{

	use HasCamelCase;

	/**
	 * @return literal-string
	 */
public function getTableName(): string
{
	return 'class_section';
}
	/**
	 * @return ICollection<ClassSection>
	 */
	public function findByFilter(?ArrayHash $filter): ICollection
	{
		$builder = $this->builder()->select('h.*')->from($this->getTableName(), 'h');

		if (!empty($filter->fulltext)) {
			$builder->andWhere('h.name LIKE %_like_', $filter->fulltext);
		}

		return $this->toCollection($builder);
	}

}
