<?php declare(strict_types = 1);

namespace App\Model\Orm\ClassSection;

use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method ClassSection|null getById($id)
 * @method ICollection<ClassSection> findByFilter(?ArrayHash $filter)
 * @extends Repository<ClassSection>
 */
final class ClassSectionRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [ClassSection::class];
	}
	public function getByExtId(int|string $id): ?ClassSection
	{
		return $this->getBy(['extId' => (string) $id]);
	}

}
