<?php declare(strict_types = 1);

namespace App\Model\Orm\ClassSection;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Product\Product;

final class ClassSectionModel
{

	public function __construct(
		private readonly Orm $orm
	)
	{
	}


	public function create(Mutation $mutation, Product $product, string $name, string $type, int $completionTime): ClassSection
	{
		$newClassSection = new ClassSection();
		$newClassSection->mutation = $mutation;
		$newClassSection->name = $name;
		$newClassSection->product = $product;
		$newClassSection->type = $type;
		$newClassSection->completionTime = $completionTime;
		$this->orm->classSection->attach($newClassSection);
		$this->orm->classSection->persistAndFlush($newClassSection);

		return $newClassSection;
	}

}
