<?php declare(strict_types = 1);

namespace App\Model\Orm\ImportCache;

use App\Model\Orm\Traits\HasSimpleSave;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Repository\Repository;

/**
 * @method ImportCache|null getById($id)
 * @method ImportCache|null getBy(array $conds)
 * @method ImportCache save(?ImportCache $entity, array $data)
 * @method bool setStatus(array $ids, string $status)
 * @method array getCountByTypes(string $status)
 *
 * @extends Repository<ImportCache>
 */
final class ImportCacheRepository extends Repository
{

	use HasSimpleSave;

	/**
	 * @inheritDoc
	 */
	public static function getEntityClassNames(): array
	{
		return [ImportCache::class];
	}

	public function create(string $type, ArrayHash $data, string $extId, DateTimeImmutable $createdTime, ?string $status = null): ImportCache
	{
		$entityData = [
			'type' => $type,
			'data' => $data,
			'extId' => $extId,
			'createdTime' => $createdTime,
		];

		if (isset($status)) {
			$entityData['status'] = $status;
		}

		return $this->save(null, $entityData);
	}

}
