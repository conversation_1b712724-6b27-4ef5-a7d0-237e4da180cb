<?php declare(strict_types = 1);

namespace App\Model\Orm\ImportCache;

use App\Exceptions\LogicException;
use App\Model\Messenger\Erp\Coupon\CouponMessage;
use App\Model\Messenger\Erp\Customer\CustomerMessage;
use App\Model\Messenger\Erp\Order\OrderImportMessage;
use App\Model\Messenger\Erp\Price\PriceMessage;
use App\Model\Messenger\Erp\Product\ProductMessage;
use App\Model\Messenger\Erp\ProductImage\ProductImageMessage;
use App\Model\Messenger\Erp\Stock\StockMessage;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property string $type {enum self::TYPE_*}
 * @property ArrayHash $data {container \App\Model\Orm\JsonArrayHashContainer}
 * @property string $status {enum self::STATUS_*} {default self::STATUS_NEW}
 * @property string|null $extId
 * @property string|null $extChecksum
 * @property DateTimeImmutable $createdTime {default now}
 * @property DateTimeImmutable|null $importedTime {default null}
 * @property string|null $message
 *
 * RELATIONS
 *
 * VIRTUAL
 */
class ImportCache extends Entity
{

	public const string STATUS_NEW = 'new';
	public const string STATUS_QUEUED = 'queued';
	public const string STATUS_READY = 'ready';
	public const string STATUS_IMPORTED = 'imported';
	public const string STATUS_WARNING = 'warning';
	public const string STATUS_ERROR = 'error';
	public const string STATUS_PROCESSING = 'processing';
	public const string STATUS_SKIPPED = 'skipped';

	public const string TYPE_PRODUCT = 'product';
	public const string TYPE_PRODUCT_IMAGES = 'product_images';
	public const string TYPE_STOCK = 'stock';
	public const string TYPE_PRICE = 'price';
	public const string TYPE_REVIEW = 'review';

	public const string TYPE_COUPON = 'coupon';
	public const string TYPE_SUPPLIER = 'supplier';

	public const string TYPE_CATEGORY = 'category';

	public const string TYPE_ORDER = 'order';

	public const string TYPE_HEUREKA_REVIEW = 'heureka_review';
	public const string TYPE_HEUREKA_SHOP_REVIEW = 'heureka_shop_review';

	public const string TYPE_ALTERNATIVE_CATEGORY = 'alternative_category';

	public const string TYPE_CUSTOMER = 'customer';


	public static function toMessageClass(string $type): string
	{
		return match($type) {
			/*self::TYPE_PRODUCT => ProductMessage::class,
			self::TYPE_PRODUCT_IMAGES => ProductImageMessage::class,
			self::TYPE_STOCK => StockMessage::class,
			self::TYPE_PRICE => PriceMessage::class,
			self::TYPE_CUSTOMER => CustomerMessage::class,
			self::TYPE_ORDER => OrderImportMessage::class,
			self::TYPE_COUPON => CouponMessage::class,
*/
			default => throw new LogicException('Unknown import cache type: ' . $type)
		};
	}

}
