<?php declare(strict_types = 1);

namespace App\Model\Orm\ImportCache;

use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Orm\Mapper\Dbal\DbalMapper;
use Throwable;

/**
 * @extends DbalMapper<ImportCache>
 */
class ImportCacheMapper extends Dbal<PERSON><PERSON>per
{

	use hasCamelCase;

	/**
* @return literal-string
*/
public function getTableName(): string
{
	return 'import_cache';
}

	/**
	 * @param array[] $data
	 */
	public function insert(array $data):bool
	{
		if(count($data) <= 0){
			return false;
		}

		$this->connection->query(
			'INSERT INTO %table %values[]',
			$this->getTableName(),
			$data
		);

		return true;
	}

	public function deleteByStatus(array $statuses, ?string $type = null): int
	{
		if ($type === null) {
			$this->connection->query("DELETE FROM %table WHERE `status` IN %s[]", $this->getTableName(), $statuses);
		} else {
			$this->connection->query("DELETE FROM %table WHERE `status` IN %s[] AND `type` = %s", $this->getTableName(), $statuses, $type);
		}
		return $this->connection->getAffectedRows();
	}

	public function setStatus(array $ids, string $status): bool
	{
		if (count($ids) <= 0) {
			return false;
		}

		$this->connection->query(
			'UPDATE %table SET `status` = %s WHERE `id` IN %i[]',
			$this->getTableName(),
			$status,
			$ids
		);

		return true;
	}

	public function getCountByTypes(?string $status = null): array
	{
		if ($status === null) {
			return $this->connection->query('SELECT [type], COUNT(id) AS num, [id] FROM %table GROUP BY [type]', $this->getTableName())->fetchPairs('type');
		}
		return $this->connection->query('SELECT [type], COUNT(id) AS num, [id] FROM %table WHERE [status]=%s GROUP BY [type]', $this->getTableName(), $status)->fetchPairs('type');
	}

}
