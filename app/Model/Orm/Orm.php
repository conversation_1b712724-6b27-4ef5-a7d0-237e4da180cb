<?php declare(strict_types = 1);

namespace App\Model\Orm;

use App\Model\Orm\Alias\AliasRepository;
use App\Model\Orm\AliasHistory\AliasHistoryRepository;
use App\Model\Orm\ApiToken\ApiTokenRepository;
use App\Model\Orm\ApiUser\ApiUserRepository;
use App\Model\Orm\AttachedFile\AttachedFileRepository;
use App\Model\Orm\CardPayment\CardPaymentRepository;
use App\Model\Orm\CardPayment\CardPaymentStatusChangeRepository;
use App\Model\Orm\ClassEvent\ClassEventRepository;
use App\Model\Orm\ClassEventSectionMetadata\ClassEventSectionMetadataRepository;
use App\Model\Orm\ClassSection\ClassSectionRepository;
use App\Model\Orm\DeliveryMethod\DeliveryMethodConfigurationRepository;
use App\Model\Orm\DeliveryMethod\DeliveryMethodCurrencyRepository;
use App\Model\Orm\DeliveryMethod\DeliveryMethodPriceRepository;
use App\Model\Orm\EmailTemplate\EmailTemplateRepository;
use App\Model\Orm\EmailTemplateFile\EmailTemplateFileRepository;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Orm\File\FileRepository;
use App\Model\Orm\Holiday\HolidayRepository;
use App\Model\Orm\ImportCache\ImportCacheRepository;
use App\Model\Orm\ImportCacheTime\ImportCacheTimeRepository;
use App\Model\Orm\IpAddress\IpAddressRepository;
use App\Model\Orm\LibraryImage\LibraryImageRepository;
use App\Model\Orm\LibraryTree\LibraryTreeRepository;
use App\Model\Orm\Log\LogRepository;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Mutation\MutationRepository;
use App\Model\Orm\MyLibrary\MyLibraryRepository;
use App\Model\Orm\MyLibraryProduct\MyLibraryProductRepository;
use App\Model\Orm\NewsletterEmail\NewsletterEmailRepository;
use App\Model\Orm\Order\Class\ClassItemRepository;
use App\Model\Orm\Order\Delivery\DeliveryInformationRepository;
use App\Model\Orm\Order\Delivery\OrderDeliveryRepository;
use App\Model\Orm\Order\Discount\DiscountItemRepository;
use App\Model\Orm\Order\Gift\GiftItemRepository;
use App\Model\Orm\Order\OrderRepository;
use App\Model\Orm\Order\OrderStateHistory\OrderStateChangeRepository;
use App\Model\Orm\Order\Payment\OrderPaymentRepository;
use App\Model\Orm\Order\Payment\PaymentInformationRepository;
use App\Model\Orm\Order\PickupPoint\PickupPointRepository;
use App\Model\Orm\Order\Product\ProductItemRepository;
use App\Model\Orm\Order\Promotion\PromotionItemRepository;
use App\Model\Orm\Order\Sync\OrderSyncHistoryRepository;
use App\Model\Orm\Order\Voucher\VoucherItemRepository;
use App\Model\Orm\Parameter\ParameterRepository;
use App\Model\Orm\ParameterValue\ParameterValueRepository;
use App\Model\Orm\PaymentMethod\PaymentMethodConfigurationRepository;
use App\Model\Orm\PaymentMethod\PaymentMethodCurrencyRepository;
use App\Model\Orm\PaymentMethod\PaymentMethodPriceRepository;
use App\Model\Orm\PriceLevel\PriceLevelRepository;
use App\Model\Orm\Product\ProductRepository;
use App\Model\Orm\ProductFile\ProductFileRepository;
use App\Model\Orm\ProductImage\ProductImageRepository;
use App\Model\Orm\ProductLocalization\ProductLocalizationRepository;
use App\Model\Orm\ProductProduct\ProductProductRepository;
use App\Model\Orm\ProductRedirect\ProductRedirectRepository;
use App\Model\Orm\ProductReview\ProductReviewRepository;
use App\Model\Orm\ProductTree\ProductTreeRepository;
use App\Model\Orm\ProductVariant\ProductVariantRepository;
use App\Model\Orm\ProductVariantLocalization\ProductVariantLocalizationRepository;
use App\Model\Orm\ProductVariantPrice\ProductVariantPriceRepository;
use App\Model\Orm\ProductVariantPriceLog\ProductVariantPriceLogRepository;
use App\Model\Orm\Rate\RateRepository;
use App\Model\Orm\Redirect\RedirectRepository;
use App\Model\Orm\Review\ReviewRepository;
use App\Model\Orm\SearchLog\SearchLogRepository;
use App\Model\Orm\ShopReview\ShopReviewRepository;
use App\Model\Orm\State\StateRepository;
use App\Model\Orm\Stock\StockRepository;
use App\Model\Orm\String\StringRepository;
use App\Model\Orm\Supply\SupplyRepository;
use App\Model\Orm\TreeProduct\TreeProductRepository;
use App\Model\Orm\TreeTree\TreeTreeRepository;
use App\Model\Orm\User\UserRepository;
use App\Model\Orm\UserHash\UserHashRepository;
use App\Model\Orm\UserInterest\UserInterestRepository;
use App\Model\Orm\UserMutation\UserMutationRepository;
use App\Model\Orm\Usp\UspRepository;
use App\Model\Orm\Voucher\VoucherRepository;
use App\Model\Orm\VoucherCode\VoucherCodeRepository;
use App\Model\Orm\VoucherLimit\VoucherLimitRepository;
use App\Model\Orm\Watchdog\WatchdogRepository;
use App\PostType\Author\Model\Orm\AuthorLocalizationRepository;
use App\PostType\Author\Model\Orm\AuthorRepository;
use App\PostType\Banner\Model\Orm\BannerLocalizationRepository;
use App\PostType\Banner\Model\Orm\BannerLocalizationTreeRepository;
use App\PostType\Banner\Model\Orm\BannerPositionRepository;
use App\PostType\Banner\Model\Orm\BannerRepository;
use App\PostType\Blog\Model\Orm\BlogLocalizationRepository;
use App\PostType\Blog\Model\Orm\BlogLocalizationTreeRepository;
use App\PostType\Blog\Model\Orm\BlogRepository;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalizationRepository;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagRepository;
use App\PostType\Calendar\Model\Orm\Calendar;
use App\PostType\Calendar\Model\Orm\CalendarLocalizationRepository;
use App\PostType\Calendar\Model\Orm\CalendarLocalizationTreeRepository;
use App\PostType\Calendar\Model\Orm\CalendarRepository;
use App\PostType\CalendarTag\Model\Orm\CalendarTag\CalendarTagLocalizationRepository;
use App\PostType\CalendarTag\Model\Orm\CalendarTag\CalendarTagRepository;
use App\PostType\ContactMessage\Model\Orm\ContactMessage\ContactMessageRepository;
use App\PostType\Discount\Model\Orm\Discount\DiscountRepository;
use App\PostType\Discount\Model\Orm\DiscountLocalization\DiscountLocalizationRepository;
use App\PostType\Gift\Model\Orm\Gift\GiftLocalizationRepository;
use App\PostType\Gift\Model\Orm\Gift\GiftRepository;
use App\PostType\Brand\Model\Orm\BrandLocalizationRepository;
use App\PostType\Brand\Model\Orm\BrandRepository;
use App\PostType\MenuMain\Model\Orm\MenuMain\MenuMainRepository;
use App\PostType\MenuMain\Model\Orm\MenuMainLocalization\MenuMainLocalizationRepository;
use App\PostType\Page\Model\Orm\TreeAlternativeRepository;
use App\PostType\Page\Model\Orm\TreeParentRepository;
use App\PostType\Page\Model\Orm\TreeRepository;
use App\PostType\Promotion\Model\Orm\Promotion\PromotionLocalizationRepository;
use App\PostType\Promotion\Model\Orm\Promotion\PromotionRepository;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalizationRepository;
use App\PostType\SeoLink\Model\Orm\SeoLinkRepository;
use App\PostType\Supplier\Model\Orm\Supplier\SupplierRepository;
use App\PostType\SystemMessage\Model\Orm\SystemMessageRepository;
use App\PostType\Tag\Model\Orm\Tag\TagRepository;
use App\PostType\Tag\Model\Orm\TagLocalization\TagLocalizationRepository;
use App\PostType\UserAnimal\Model\Orm\UserAnimalRepository;
use App\PostType\UserAnimal\Model\Orm\UserAnimalTypeLocalizationRepository;
use App\PostType\UserAnimal\Model\Orm\UserAnimalTypeRepository;
use LogicException;
use Nette\DI\Container;
use Nextras\Dbal\Connection;
use Nextras\Orm\Model\IRepositoryLoader;
use Nextras\Orm\Model\MetadataStorage;
use Nextras\Orm\Model\Model;
use Nextras\Orm\Repository\Repository;

/**
 * @property-read TreeRepository $tree
 * @property-read TreeParentRepository $treeParent
 * @property-read AliasRepository $alias
 * @property-read AttachedFileRepository $attachedFile
 * @property-read LibraryTreeRepository $libraryTree
 * @property-read LibraryImageRepository $libraryImage
 * @property-read UserRepository $user
 * @property-read UserHashRepository $userHash
 * @property-read AliasHistoryRepository $aliasHistory
 * @property-read RedirectRepository $redirect
 * @property-read FileRepository $file
 * @property-read MutationRepository $mutation
 * @property-read EmailTemplateRepository $emailTemplate
 * @property-read EmailTemplateFileRepository $emailTemplateFile
 * @property-read EsIndexRepository $esIndex
 * @property-read TreeTreeRepository $treeTree
 * @property-read StateRepository $state
 * @property-read BlogRepository $blog
 * @property-read BlogLocalizationRepository $blogLocalization
 * @property-read BlogLocalizationTreeRepository $blogLocalizationTree
 * @property-read BlogTagRepository $blogTag
 * @property-read BlogTagLocalizationRepository $blogTagLocalization
 * @property-read CalendarRepository $calendar
 * @property-read CalendarLocalizationRepository $calendarLocalization
 * @property-read CalendarLocalizationTreeRepository $calendarLocalizationTree
 * @property-read CalendarTagRepository $calendarTag
 * @property-read CalendarTagLocalizationRepository $calendarTagLocalization
 * @property-read AuthorRepository $author
 * @property-read AuthorLocalizationRepository $authorLocalization
 * @property-read UserMutationRepository $userMutation
 * @property-read NewsletterEmailRepository $newsletterEmail
 * @property-read ApiUserRepository $apiUser
 * @property-read StringRepository $string
 * @property-read ParameterRepository $parameter
 * @property-read ParameterValueRepository $parameterValue
 * @property-read PriceLevelRepository $priceLevel
 * @property-read ProductRepository $product
 * @property-read ProductFileRepository $productFile
 * @property-read ProductImageRepository $productImage
 * @property-read ProductLocalizationRepository $productLocalization
 * @property-read ProductProductRepository $productProduct
 * @property-read ProductReviewRepository $productReview
 * @property-read ProductTreeRepository $productTree
 * @property-read ProductVariantRepository             $productVariant
 * @property-read ProductVariantLocalizationRepository $productVariantLocalization
 * @property-read ProductVariantPriceRepository        $productVariantPrice
 * @property-read StockRepository                      $stock
 * @property-read SupplyRepository                     $supply
 * @property-read TreeProductRepository                $treeProduct
 * @property-read SeoLinkRepository                    $seoLink
 * @property-read SeoLinkLocalizationRepository        $seoLinkLocalization
 * @property-read ApiTokenRepository                   $apiToken
 * @property-read LogRepository                        $log
 * @property-read SystemMessageRepository              $systemMessage
 * @property-read ImportCacheRepository $importCache
 * @property-read DiscountLocalizationRepository $discountLocalization
 * @property-read DiscountRepository $discount
 * @property-read SupplierRepository $supplier
 * @property-read RateRepository $rate
 * @property-read MenuMainRepository $menuMain
 * @property-read MenuMainLocalizationRepository $menuMainLocalization
 * @property-read ProductVariantPriceLogRepository $productVariantPriceLog
 * @property-read ReviewRepository $review
 * @property-read DeliveryMethodConfigurationRepository $deliveryMethod
 * @property-read DeliveryMethodPriceRepository $deliveryMethodPrice
 * @property-read DeliveryMethodCurrencyRepository $deliveryMethodCurrency
 * @property-read PaymentMethodConfigurationRepository $paymentMethod
 * @property-read PaymentMethodPriceRepository $paymentMethodPrice
 * @property-read PaymentMethodCurrencyRepository $paymentMethodCurrency
 * @property-read VoucherRepository $voucher
 * @property-read VoucherCodeRepository $voucherCode
 * @property-read OrderRepository $order
 * @property-read OrderStateChangeRepository $orderStateChange
 * @property-read ProductItemRepository $orderProduct
 * @property-read ClassItemRepository $orderClassEvent
 * @property-read VoucherItemRepository $orderVoucher
 * @property-read DiscountItemRepository $orderDiscount
 * @property-read GiftItemRepository $orderGift
 * @property-read PromotionItemRepository $orderPromotion
 * @property-read OrderDeliveryRepository $orderDelivery
 * @property-read DeliveryInformationRepository $orderDeliveryInformation
 * @property-read OrderPaymentRepository $orderPayment
 * @property-read PaymentInformationRepository $orderPaymentInformation
 * @property-read CardPaymentRepository $cardPayment
 * @property-read CardPaymentStatusChangeRepository $cardPaymentStatusChange
 * @property-read HolidayRepository $holiday
 * @property-read TagRepository $tag
 * @property-read TagLocalizationRepository $tagLocalization
 * @property-read PromotionLocalizationRepository $promotionLocalization
 * @property-read PromotionRepository $promotion
 * @property-read GiftLocalizationRepository $giftLocalization
 * @property-read GiftRepository $gift
 * @property-read VoucherLimitRepository $voucherLimit
 * @property-read PickupPointRepository $pickupPoint
 * @property-read IpAddressRepository $ipAddress
 * @property-read SearchLogRepository $searchLog
 * @property-read ProductRedirectRepository $productRedirect
 * @property-read MyLibraryRepository $myLibrary
 * @property-read MyLibraryProductRepository $myLibraryProduct
 * @property-read ImportCacheTimeRepository $importCacheTime
 * @property-read TreeAlternativeRepository $treeAlternative
 * @property-read OrderSyncHistoryRepository $orderSyncHistory
 * @property-read WatchdogRepository $watchdog
 * @property-read ClassSectionRepository $classSection
 * @property-read ClassEventRepository $classEvent
 * @property-read ClassEventSectionMetadataRepository $classEventSectionMetadata
 * @property-read UspRepository $usp
 * @property-read UserAnimalRepository $userAnimal
 * @property-read UserAnimalTypeRepository $userAnimalType
 * @property-read UserAnimalTypeLocalizationRepository $userAnimalTypeLocalization
 * @property-read UserInterestRepository $userInterest
 * @property-read ContactMessageRepository $contactMessageRepository
 * @property-read BannerRepository $banner
 * @property-read BannerLocalizationRepository $bannerLocalization
 * @property-read BannerLocalizationTreeRepository $bannerLocalizationTree
 * @property-read BannerPositionRepository $bannerPosition
 * @property-read ShopReviewRepository $shopReview
 * @property-read BrandRepository $brand
 * @property-read BrandLocalizationRepository $brandLocalization
 */
final class Orm extends Model
{

	private ?Mutation $selectedMutation = null;

	private bool $globalPublicOnly = true;

	public function __construct(
		array $configuration,
		IRepositoryLoader $repositoryLoader,
		MetadataStorage $metadataStorage,
		private Connection $connection,
	) {
		parent::__construct($configuration, $repositoryLoader, $metadataStorage);
	}

	public function setMutation(Mutation $mutation): void
	{
		$this->selectedMutation = $mutation;
	}


	public function getMutation(): Mutation
	{
		if ($this->selectedMutation === null) {
			throw new LogicException('ORM mutation setup missing.');
		}

		return $this->selectedMutation;
	}


	public function hasMutation(): bool
	{
		return $this->selectedMutation !== null;
	}


	public function setPublicOnly(bool $globalPublicOnly = true): void
	{
		$this->globalPublicOnly = $globalPublicOnly;
	}


	public function getPublicOnly(): bool
	{
		return $this->globalPublicOnly;
	}

	public function reconnect(): void
	{
		if ($this->connection->ping() === false) {
			$this->connection->reconnect();
		}
	}

	public function getIdentityMap(Container $container): array
	{
		$identityMap = [];
		foreach ($container->findByType(Repository::class) as $serviceName) {
			if ($container->isCreated($serviceName) !== false) {
				$repositoryIdentityMap = $container->getService($serviceName)->getIdentityMap()->getAll();
				if ($repositoryIdentityMap !== []) {
					$identityMap[str_replace('nextras.orm.repositories.', '', $serviceName)] = $repositoryIdentityMap;
				}
			}
		}

		$array = array_map('count', $identityMap);
		array_multisort($array, SORT_DESC, $identityMap);

		return $identityMap;
	}

}
