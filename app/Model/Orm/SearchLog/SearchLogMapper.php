<?php declare(strict_types=1);

namespace App\Model\Orm\SearchLog;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends <PERSON>balMapper<SearchLog>
 */
final class SearchLogMapper extends D<PERSON><PERSON>apper
{
	use HasCamelCase;
	/**
* @return literal-string
*/
public function getTableName(): string
{
	return 'search_log';
}


	public function findBest(Mutation $mutation): array
	{
		return $this->connection->query("SELECT COUNT(*) AS count, max(createdAt) as date, text
										FROM search_log
										WHERE mutationId = %i ", $mutation->id, " and createdAt > DATE_SUB(now(), INTERVAL 6 MONTH)
										GROUP BY TEXT
										order BY count desc, date desc limit 10;")->fetchPairs(null, 'text');
		;
	}

}
