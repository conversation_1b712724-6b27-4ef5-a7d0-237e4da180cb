<?php declare(strict_types=1);

namespace App\Model\Orm\SearchLog;


use App\Model\Orm\Mutation\Mutation;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property string $text
 * @property bool $hasResults
 * @property string $agent
 * @property int|null $userId
 * @property DateTimeImmutable $createdAt {default now}
 *
 * RELATIONS
 * @property Mutation $mutation {m:1 Mutation, oneSided=true}
 */
class SearchLog extends Entity
{

}
