<?php declare(strict_types = 1);

namespace App\Model\Orm\SearchLog;

use App\Model\Orm\Mutation\Mutation;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method SearchLog|null getById($id)
 * @method SearchLog|null getBy()
 * @method ICollection<SearchLog> findBy()
 * @method ICollection<SearchLog> findByIds()
 * @method array findBest(Mutation $mutation)
 *
 * @extends Repository<SearchLog>
 */
final class SearchLogRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [SearchLog::class];
	}


}
