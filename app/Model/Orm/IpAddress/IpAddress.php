<?php declare(strict_types = 1);

namespace App\Model\Orm\IpAddress;

use App\Model\IpAddress\IpAddressInfo;
use App\Model\Orm\State\State;
use Nextras\Orm\Entity\Entity;

/**
 * @property int              $id         {primary}
 * @property string           $ipAddress
 * @property bool             $isInEurope {default false}
 * @property State $country    {m:1 State, oneSided=true}
 */
final class IpAddress extends Entity implements IpAddressInfo
{

	public function getCountryCode(): string
	{
		return $this->country->code;
	}

	public function isInEurope(): bool
	{
		return $this->isInEurope;
	}

}
