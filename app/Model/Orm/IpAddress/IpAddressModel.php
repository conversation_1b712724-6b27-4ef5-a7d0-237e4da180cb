<?php declare(strict_types = 1);

namespace App\Model\Orm\IpAddress;

use Apitte\Core\Http\ApiResponse;
use App\Model\IpAddress\CookieStorage;
use App\Model\IpAddress\IpAddressInfo;
use App\Model\IpAddress\IpAddressStaticData;
use App\Model\Orm\State\StateRepository;
use Exception;

final class IpAddressModel
{

	private IpAddressInfo|null $ipAddressInfo = null;

	private string|null $ipAddress = null;

	/**
	 * https://www.geoplugin.com/quickstart
	 */
	private const IP_ADDRESS_ENDPOINT_INFO = 'http://www.geoplugin.net/php.gp?ip=';

	public function __construct(
		private readonly IpAddressRepository $ipAddressRepository,
		private readonly StateRepository $stateRepository,
		private readonly CookieStorage $cookieStorage,
	)
	{
	}

	final public function isCountryInEurope(): bool
	{
		return $this->getIpAddressInfo()->isInEurope();
	}

	final public function getCountryCode(): ?string
	{
		return $this->getIpAddressInfo()->getCountryCode();
	}

	final public function init(): void
	{
		$this->getIpAddressInfo();
	}

	final public function getIpAddressInfo(): IpAddressInfo
	{
		if ($this->ipAddressInfo === null) {
			$ipAddressInfo = $this->loadIpAddressInfo();
			if ($ipAddressInfo === null) {
				// use default values
				$ipAddressInfo = IpAddressStaticData::create(null, true);
			}

			$this->saveDetectedData($ipAddressInfo->getCountryCode(), $ipAddressInfo->isInEurope());
			$this->ipAddressInfo = $ipAddressInfo;
		}

		return $this->ipAddressInfo;
	}

	final protected function loadIpAddressInfo(): IpAddressInfo|null
	{
		try {
			$ipInfo = $this->cookieStorage->get();
			if ($ipInfo !== null) {
				return $ipInfo;
			}
		} catch (\Throwable) {
			//just try cookie data
		}

		try {
			$ipInfo = $this->ipAddressRepository->getBy(['ipAddress' => $this->getIpAddress()]);
		} catch (\Throwable) {
			return null;
		}

		if ($ipInfo !== null) {
			return $ipInfo;
		}

		$addressInfo = $this->initIpAddressInfo($this->getIpAddress());

		if ($addressInfo === null) {
			return null;
		}

		if (!isset($addressInfo['geoplugin_countryCode'])) {
			return null;
		}

		return $this->create($this->getIpAddress(), $addressInfo['geoplugin_countryCode'], (bool) ($addressInfo['geoplugin_inEU'] ?? false));
	}

	private function initIpAddressInfo(string $ip): array|null
	{
		$ipInfoSerialized = @file_get_contents(self::IP_ADDRESS_ENDPOINT_INFO . $ip);

		if ($ipInfoSerialized === false) {
			return null;
		}

		$ipInfo = unserialize($ipInfoSerialized);

		if ($ipInfo['geoplugin_status'] === ApiResponse::S404_NOT_FOUND) {
			return null;
		}

		if ($ipInfo === false) {
			return null;
		}

		return $ipInfo;
	}

	public function setIpAddress(string $ipAddress): IpAddressModel
	{
		$this->ipAddress = $ipAddress;
		return $this;
	}

	final protected function getIpAddress(): string
	{
		if ($this->ipAddress === null) {
			throw new Exception('IP address is not set!');
		}

		return $this->ipAddress;
	}


	final public function create(string $ip, string $countryCode, bool $isInEurope = false): IpAddress|null
	{
		if (!$country = $this->stateRepository->getBy(['code' => $countryCode])) {
			return null;
		}

		$ipAddress = new IpAddress();
		$ipAddress->ipAddress = $ip;
		$ipAddress->isInEurope = $isInEurope;
		$ipAddress->country = $country;

		$this->ipAddressRepository->persistAndFlush($ipAddress);

		return $ipAddress;
	}


	public function saveDetectedData(?string $code, bool $isInEurope): void
	{
		$ipAddressStaticData = IpAddressStaticData::create($code, $isInEurope);
		$this->cookieStorage->set($ipAddressStaticData);
	}

}
