<?php declare(strict_types = 1);

namespace App\Model\Orm\MyLibraryProduct;

use App\Model\Orm\MyLibrary\MyLibrary;
use Nextras\Orm\Repository\Repository;


/**
 * @method MyLibraryProduct getById($id)
 * @method array findLibraryProductIds(MyLibrary $myLibrary)
 *
 * @extends Repository<MyLibraryProduct>
 */
final class MyLibraryProductRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [MyLibraryProduct::class];
	}

}
