<?php declare(strict_types = 1);

namespace App\Model\Orm\MyLibraryProduct;

use App\Model\Orm\MyLibrary\MyLibrary;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Traits\HasTemplateCache;
use DateTimeImmutable;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\ManyHasOne;

/**
 * @property int                  $id                 {primary}
 * @property DateTimeImmutable    $dateAdded          {default now}
 * @property ManyHasOne<MyLibrary> $myLibrary          {M:1 MyLibrary::$libraryProducts}
 * @property ManyHasOne<Product>   $product            {M:1 Product, oneSided=true}
 */
final class MyLibraryProduct extends Entity
{
	use HasTemplateCache;

	public function getTemplateCacheTagsCascade(): array
	{
		return [Product::class.'/'. $this->product->id]; // @phpstan-ignore-line
	}

}
