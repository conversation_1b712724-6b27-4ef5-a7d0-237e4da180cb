<?php declare(strict_types = 1);

namespace App\Model\Orm\MyLibraryProduct;

use App\Model\Orm\MyLibrary\MyLibrary;
use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<MyLibraryProduct>
 */
final class MyLibraryProductMapper extends DbalMapper
{

	use HasCamelCase;

	/**
* @return literal-string
*/
public function getTableName(): string
{
	return 'my_library_product';
}

	public function findLibraryProductIds(MyLibrary $myLibrary): array
	{
		$query = $this->builder()->select('productId')
			->from($this->getTableName())
			->andWhere('myLibraryId = %i', $myLibrary->id);;

		return $this->connection->queryByQueryBuilder($query)->fetchPairs(null, 'productId');
	}

}
