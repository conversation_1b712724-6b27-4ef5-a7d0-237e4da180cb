<?php declare(strict_types = 1);

namespace App\Model\Orm;

use Nextras\Orm\Relationships\OneHasMany;

interface ConvertableToTree
{

	public function getName(): string;
	public function getId(): int;
	public function getTreeItems(): array;
	public function getTreeSiblings(): array;
	public function isPublic(): bool;
	public function getParentNode(): ?ConvertableToTree;
	public function setParentNode(?ConvertableToTree $parent): void;
	public function getItemsRelation(): OneHasMany; // @phpstan-ignore-line
	public function setLeafParameter(bool $isLeaf): void;
	public function setSort(int $sort): void;
	public function setLevel(int $level): void;
	public function getLevel(): int;
	public function setPathNodes(array $items): void;
	public function getPathNodes(): array;

}
