<?php declare(strict_types = 1);

namespace App\Model\Orm\Redirect;

use App\Model\Orm\Orm;
use Nette\Utils\Strings;

final class RedirectModel
{

	public function __construct(
		private readonly Orm $orm,
	)
	{
	}


	public function save(string $oldUrl, string $newUrl, int|string $code = 301, ?Redirect $redirect = null): Redirect
	{
		if ($redirect === null) {
			$redirect = new Redirect();
			$this->orm->redirect->attach($redirect);
		}

		$redirect->oldUrl = preg_replace('/^\//', '', $oldUrl);
		$redirect->newUrl = preg_replace('/^\//', '', $newUrl);
		$redirect->code = (int) $code;

		$this->orm->persistAndFlush($redirect);

		return $redirect;
	}

	public function delete(Redirect $redirect): void
	{
		$this->orm->redirect->removeAndFlush($redirect);
	}

}
