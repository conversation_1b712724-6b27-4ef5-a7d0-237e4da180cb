<?php

declare(strict_types=1);

namespace App\Model\Orm\Redirect;

/**
 * @property int $id {primary}
 * @property string $oldUrl
 * @property string $newUrl
 * @property int $code
 *
 * RELATIONS
 *
 * VIRTUAL
 * @property-read  bool $hasStar {virtual}
 */
class Redirect extends \Nextras\Orm\Entity\Entity
{

    use \App\Model\Orm\Traits\HasFormDefaultData;

	protected function getterHasStar(): bool
	{
		return (bool) preg_match('/\*$/', $this->oldUrl);
	}

}
