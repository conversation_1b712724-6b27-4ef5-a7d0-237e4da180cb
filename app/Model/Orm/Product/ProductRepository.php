<?php

declare(strict_types=1);

namespace App\Model\Orm\Product;

use App\Model\Orm\CollectionById;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\Traits\HasPublicParameter;
use App\Model\Orm\Traits\HasSimpleSave;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\Tree;
use Nette\Security\User;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method Product|null getById($id)
 * @method ICollection<Product> findByPageId($id)
 * @method ICollection<Product> findByIds(array $ids)
 * @method ICollection<Product> findBySetpartsById($id)
 * @method ICollection<Product> findSetBySetpartId($id)
 * @method ICollection<Product> addToSet($set, $product, $sort)
 * @method ICollection<Product> removeFromSet($set, $product)
 * @method int getTreeSoldProductsCount(Tree $tree)
 * @method int getPurchaseCount(Product $product, int $daysToPast = 31)
 *
 * @method ICollection<Product> searchByName($q, array $excluded = NULL, Mutation $mutation = null, bool $groupById = false)
 * @method ICollection<Product> searchBy($q, array $excluded = null, bool $groupById = false)
 *
 * @method ICollection<Tree> findMainProductsInRelations(Product $attachedProduct, string $type)
 * @method ICollection<Product> findAttachedProductsInRelations(Product $mainProduct, string $type)
 *
 * @method ICollection<Product> findProductsInTreeProductRelations(Tree $tree, string $type)
 * @method ICollection<Product> addToPages($page, $product, $sort)
 * @method ICollection<Product> removeFromPages($page, $product)
 * @method ICollection<Product> updateToPages($page, $product, $sort)
 *
 * @method Result addParameterValue(Product $product, ParameterValue $parameterValue)
 * @method Result removeParameterValue(Product $product, ParameterValue $parameterValue)
 * @method Result removeParameter(Product $product, Parameter $parameter)
 * @method Result removeMissingParameterValuesIds(Product $product, array $selectedParameterValuesIds)
 *
 * @method Result findRelationsByMainCategory(CatalogTree $catalogTree)
 *
 * @method ICollection<Product> findFilteredProducts($productIds)
 * @method Product save(?Product $entity, array $data)
 *
 * @method void cloneProductParameters(Product $sourceProduct, Product $targetProduct)
 *
 * @method ICollection<Product> findByExactOrder(array $ids)
 * @method array findAllIdsForElasticSearch(?int $limit, Mutation $mutation, ?bool $skipHistorical = false)
 * @method array findByParameterValue(int $parameterValueId)
 * @method array findSimilarBuyIds(array $variantIds)
 * @method array findOrderedProductIds(User $userSecurity)
 * @method array findRelatedOrderedProducts(array $productIds, \DateTimeImmutable $orderFrom)
 * @method array deactivateTagProperty(string $tag, array $productIds = [])
 * @method array setPositionInMainCategory(array $productIdsWithPositions)
 * @method ICollection<Product> getProductWithFreeTransport(int|null $offset = null, int|null $limit = null)
 * @method bool isNewProductByDateCreated(Product $product)
 *
 * @extends Repository<Product>
 */
final class ProductRepository extends Repository  implements CollectionById
{

	use HasPublicParameter;
	use HasSimpleSave;

	public static function getEntityClassNames(): array
	{
		return [Product::class];
	}

	public function getPublicOnlyWhereParams(): array
	{
		/** @var Orm $orm */
		$orm = $this->getModel();
		$ret = [
			'productLocalizations->public' => 1,
			'productLocalizations->mutation' => $orm->getMutation(),
		];

		$now = $this->getNowDateTime();

		$ret['publicFrom<='] = $now;
		$ret['publicTo>='] = $now;

		return $ret;
	}

	/**
	 * @param string|int|null $id
	 */
	public function getByExtId($id): ?Product
	{
		return isset($id) ? $this->getBy(['extId' => (string) $id]) : null;
	}

	public function getByExtCode(string $code): ?Product
	{
		return $this->getBy(['variants->code' => $code]);
	}
	/**
	 * @return ICollection<Product>
	 */
	public function findByIdOrder(array $ids): ICollection
	{
		return $this->findByExactOrder($ids);
	}
	/**
	 * @return ICollection<Product>
	 */
	public function findByEanOrIsbn(mixed $textToSearch): ICollection
	{
		return $this->findBy([
			ICollection::OR,
			'variants->ean' => $textToSearch,
			'variants->isbn' => $textToSearch,
		]);
	}


}
