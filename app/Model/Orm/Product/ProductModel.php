<?php declare(strict_types = 1);

namespace App\Model\Orm\Product;

use App\Exceptions\LogicException;
use App\Model\BucketFilter\CatalogParameter;
use App\Model\ElasticSearch\Product\ConvertorProvider;
use App\Model\ElasticSearch\Product\Facade;
use App\Model\Orm\ClassEvent\ClassEventModel;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Mutation\MutationRepository;
use App\Model\Orm\Orm;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\ParameterValue\ParameterValueRepository;
use App\Model\Orm\ProductImage\ProductImage;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\ProductVariant\ProductVariantModel;
use App\Model\Orm\ProductVariantPrice\ProductVariantPrice;
use App\Model\Orm\User\User;
use ArrayIterator;
use Nextras\Orm\Collection\ICollection;

final class ProductModel
{

	public function __construct(
		private readonly ProductRepository $productRepository,
		private readonly Facade $productElasticFacade,
		private readonly \App\Model\ElasticSearch\All\Facade $allElasticFacade,
		private readonly ProductVariantModel $productVariantModel,
		private readonly MutationRepository $mutationRepository,
		private readonly CatalogParameter $catalogParameter,
		private readonly ConvertorProvider $convertorProvider,
		private readonly ParameterValueRepository $parameterValueRepository,
		private readonly ClassEventModel $classEventModel,
		private readonly Orm $orm,
	)
	{
	}

	public function remove(Product $product): void
	{
		$this->productRepository->removeAndFlush($product);
	}

	public function saveToEs(Product $product): void
	{
		$this->allElasticFacade->updateNow($product);
		$this->productElasticFacade->updateAllMutationsNow($product, $this->convertorProvider->getAllAndDelayed());
	}

	/**
	 * @return bool - should return true, if the product flags were changed, and false otherwise
	 */
	public function handleFlags(Product $product): bool
	{
		$product->flushParam();
		foreach ($product->variants as $variant) {
			$variant->isInDiscount = 0; // @todo jk price akce (int)($variant->priceFinalDPH !=  $variant->priceDPH);
			//$variant->isFreeTransport = $product->isFreeTransport;
		}

		return true;
	}

	public function create(string $template): Product
	{
		$product = new Product();

//		$product->productType = $productType;
		$product->template = $template;
		$this->productRepository->attach($product);
		$this->productRepository->persistAndFlush($product);
		$this->normalizeProduct($product);
		$this->allElasticFacade->updateNow($product);

		return $product;
	}

	public function normalizeProduct(Product $product): void
	{
		$mutations = $this->mutationRepository->findAll();

		if ( ! $product->variants->count()) {
			$variantShell = $this->productVariantModel->createEmpty();
			$product->variants->set([$variantShell]);
			$this->productRepository->persistAndFlush($product);
		}

		foreach ($mutations as $mutation) {

			$productLocalization = $product->productLocalizations->toCollection()->getBy(['mutation' => $mutation]);
			if ($productLocalization === null) {
				$newProductLocalization = new ProductLocalization();
				$newProductLocalization->mutation = $mutation;
				$newProductLocalization->product = $product;
			}

			foreach ($product->variants as $variant) {
				$this->productVariantModel->normalizeVariant($variant, $mutation);
			}
		}

		$this->productRepository->persistAndFlush($product);
	}

	public function filterOutMainParameters(Product $product): array
	{
		$mainCategoryParameters = $this->catalogParameter->getParametersCfForProductDetail($product->mainCategory);

		if ( ! isset($mainCategoryParameters->visibleParameters)) {
			return [];
		}

		$parameterValueList = [];
		foreach ($mainCategoryParameters->visibleParameters as $categoryParameter) {
			$parameterValues = $this->parameterValueRepository->findValues($product, $categoryParameter->uid);
			if ($parameterValues->count() === 0) {
				continue;
			} elseif ($parameterValues->count() === 1) {
				$parameterValueList[] = $parameterValues->fetchAll()[0];
			} else {
				$parameterValueList[] = $parameterValues->fetchAll();
			}
		}

		return $this->prioritizeLanguageParameter($parameterValueList);
	}

	private function prioritizeLanguageParameter(array $parametersValues): array
	{
		$orderedParameterValues = [];

		foreach ($parametersValues as $key => &$parameterValues) {
			$hasCzechLanguage = false;
			$isLanguageParameter = false;

			foreach ($parameterValues instanceof ParameterValue ? [$parameterValues] : $parameterValues as $parameterValue) {
				if ($parameterValue->isLanguageParameter) {
					$isLanguageParameter = true;
					if ($parameterValue->isCzechLanguage) {
						$hasCzechLanguage = true;
					}
				}
			}

			if ($isLanguageParameter && ! $hasCzechLanguage) {
				$orderedParameterValues[] = $parameterValues;
				unset($parametersValues[$key]);
				return array_merge($orderedParameterValues, $parametersValues);
			}
		}

		return $parametersValues;
	}


	public function hasLastMinuteOption(Product $product): bool
	{
		if ( ! $product->isCourse()) {
			return false;
		}

		$daysCount = (int)($product->mainCategory->cf->lastMinute->daysCount ?? 0);
		$capacityCount = (int)($product->mainCategory->cf->lastMinute->capacityCount ?? 0);

		foreach ($product->classEventsPublic as $classEvent) {
			$hasLastMinute = $this->classEventModel->hasLastMinuteOption($classEvent, $daysCount, $capacityCount);
			if ($hasLastMinute) {
				return true;
			}
		}

		return false;
	}

	/**
	 * @param ICollection<ProductVariant> $variants
	 */
	public function createFromFreeErpVariants(Mutation $mutation, ICollection $variants, User $user): Product
	{
		return $this->addVariantsToProduct($mutation, $variants, $user);
	}

	/**
	 * @param ICollection<ProductVariant> $variants
	 */
	public function addFreeErpVariants(Mutation $mutation, ICollection $variants, User $user, Product $product): Product
	{
		return $this->addVariantsToProduct($mutation, $variants, $user, $product);
	}

	/**
	 * @param Mutation $mutation
	 * @param ICollection<ProductVariant> $variants
	 * @param User $user
	 * @param Product|null $product
	 * @return Product
	 */
	private function addVariantsToProduct(Mutation $mutation, ICollection $variants, User $user, ?Product $product = null): Product
	{
		if ($variants->count() === 0) {
			throw new LogicException('add_product_msg_error_variants_missing');
		}

		if ($variants->count() > 10) {
			throw new LogicException('add_product_msg_error_variants_too_much');
		}

		/** @var ProductVariant $firstVariant */
		$firstVariant = $variants->fetch();
		$variantIds = $variants->fetchPairs('id', 'id');

		// Product
		if ($product === null) {

			$product = new Product();
			$this->productRepository->attach($product);

			$product->template = ':Front:Product:detail';
			$product->internalName = trim($firstVariant->erpName);
			$product->edited = $user->id;
			$product->availability = Product::AVAILABILITY_ON_STOCK;

			$productLocalization = new ProductLocalization();
			$productLocalization->mutation = $mutation;
			$productLocalization->product = $product;

			$productLocalization->name = $product->internalName;
			$productLocalization->nameTitle = $product->internalName;
			$productLocalization->nameAnchor = $product->internalName;
//			$productLocalization->annotation = $firstVariant->erpDescription;
//			$productLocalization->description = $firstVariant->erpDescription;
			$productLocalization->content = $firstVariant->erpContent;

			$sort = $sortImg = 0;

		} else {
			$sort = $product->variants->count() + 1;
			$sortImg = $product->images->count() + 1;
		}

		// Product variants
		foreach ($variants as $variant) {
			$variant->sort = $sort;
			$variant->isForRevaluation = true;

			$product->variants->add($variant);
			$sort++;

			if ($variant->tempImageId === null) {
				continue;
			}

			$image = $this->orm->libraryImage->getById($variant->tempImageId);
			if ($image && $image->id) {
				$productImage = new ProductImage();
				$productImage->libraryImage = $image->id;
				$productImage->sort = $sortImg;
				$productImage->product = $product;
				$productImage->variants = strval($variant->id);

				$data = [];
				$data[$mutation->langCode]['name'] = trim($variant->erpName);
				$productImage->data = $data; // @phpstan-ignore-line

				$product->images->add($productImage);
				$sortImg++;
			}
		}

		$product = $this->productRepository->persistAndFlush($product);

		// Product variants prices
		/** @var ICollection<ProductVariantPrice> $prices */
		$prices = $this->orm->productVariantPrice->findBy([
			'mutation' => $mutation,
			'productVariant' => $variantIds,
		]);

		foreach ($prices as $price) {
			$price->productId = $product->id;
			$this->orm->productVariantPrice->persist($price);
		}

		$this->orm->productVariantPrice->flush();

		return $product;
	}

	/**
	 * recalculate product fields before saving
	 *
	 * @param Product $product
	 * @return Product
	 */
	public function recalculateProduct(Product $product): Product
	{
		//Recalculate product free shipping by product price vs. deliveryMethodPrice->freeFrom
//		$product->isFreeTransport = (int)$this->tagModel->checkFreeTransport($product);

		return $product;
	}

}
