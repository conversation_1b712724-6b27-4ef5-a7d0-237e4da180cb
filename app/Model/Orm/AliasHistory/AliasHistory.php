<?php declare(strict_types = 1);

namespace App\Model\Orm\AliasHistory;

use App\Model\Orm\Alias\Alias;
use App\Model\Orm\Mutation\Mutation;
use Nextras\Orm\Entity\Entity;
use Stringable;

/**
 * @property int $id {primary}
 * @property string $alias
 * @property string $module {enum Alias::MODULE_*}
 * @property int $referenceId
 *
 *
 * RELATIONS
 * @property Mutation $mutation {m:1 Mutation::$historyAliases}
 */
final class AliasHistory extends Entity implements Stringable
{

	public function __toString(): string
	{
		return $this->alias;
	}

}
