<?php

declare(strict_types=1);

namespace App\Model\Orm\AliasHistory;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\RoutableEntity;

final class AliasHistoryModel
{
	function __construct(
		private readonly Orm $orm,
	) {}

	public function saveString(string $aliasHistoryString, RoutableEntity $entity, Mutation $mutation): void
	{
		$parts = explode("\n", $aliasHistoryString);
		$parts = array_map('trim', $parts);

		//only unique names
		$newPart = [];
		foreach ($parts as $key=>$part) {
			$newPart[$part] = $part;
		}
		$parts = $newPart;
		$oldAliasHistory = $this->orm->aliasHistory->findBy([
			'mutation' => $mutation,
			'module' => $entity->module,
			'referenceId' => $entity->id,
		]);
		foreach ($oldAliasHistory as $key => $history) {
			if (isset($parts[$history->alias])) {
				// alias se neemenil
				// nic s nim nedelej
				unset($parts[$history->alias]);
			} else {
				// pokud neni nastaveny muzu stary zaznam v DB smazat
				$this->orm->aliasHistory->remove($history);
			}
		}

		if ($parts) {
			foreach ($parts as $alias=>$part) {
				// pokud v aliasu neco je a pro dany jakzyk neexistuje stejny alias
				// tak muzu zalozit novy historicky alias
				$silimarAlias = $this->orm->aliasHistory->getBy(['alias' => $alias, 'mutation' => $mutation]);
				if ($alias && !$silimarAlias) {
					$newAliasHistory = new AliasHistory();
					$newAliasHistory->alias = $alias;
					$newAliasHistory->module = $entity->module;
					$newAliasHistory->mutation = $mutation;
					$newAliasHistory->referenceId = $entity->id;
					$this->orm->aliasHistory->persistAndFlush($newAliasHistory);
				}
			}
		}
	}

	public function removeForEntity(RoutableEntity $entity, Mutation $mutation): void
	{
		$aliases = $this->orm->aliasHistory->findBy([
			'referenceId' => $entity->id,
			'module' => $entity->module,
			'mutation' => $mutation,
		]);
		foreach ($aliases as $alias) {
			$this->orm->aliasHistory->remove($alias);
		}

	}
}
