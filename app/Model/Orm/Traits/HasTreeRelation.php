<?php

declare(strict_types=1);

namespace App\Model\Orm\Traits;

use App\Model\Orm\Orm;
use App\Model\Orm\Product\Product;
use App\PostType\Page\Model\Orm\Tree;
use Nextras\Orm\Collection\ICollection;

/**
 * @property Orm $orm
 */
trait HasTreeRelation
{

	private function handleTreeRelations(ICollection $collection, string $name, string $type, Tree $tree, mixed $data, ?int $limitation = null): void // @phpstan-ignore-line
	{
		$attachedIds = $collection->fetchPairs('id', null);

		if (isset($data[$name]) && $data[$name]) {


			$sort = 0;
			foreach ($data[$name] as $key=>$id) {
				$attachedProduct = $this->orm->tree->getById($id);
				if ($attachedProduct) {
					if (is_int($key)) {
						unset($attachedIds[$attachedProduct->id]);
					}

					$this->orm->treeTree->replace($tree, $attachedProduct, $type, $sort);
					$sort++;


					if (!is_null($limitation) && $limitation == $sort) {
						break;
					}
				}


			}
		}

		foreach ($attachedIds as $attachedTree) {
			$treeProductToDelete = $this->orm->treeTree->getBy([
				'type' => $type,
				'mainTree' => $tree,
				'attachedTree' => $attachedTree
			]);
			$this->orm->treeTree->removeAndFlush($treeProductToDelete);
		}
	}

}
