<?php declare(strict_types = 1);

namespace App\Model\Orm\Traits;

use Nette\Application\UI\Form;
use Nextras\Orm\Relationships\ManyHasMany;

trait HasFormDefaultData
{

	public function getFormData(Form $form): array
	{
		$values = [];
		$formValues = [];
		foreach ($form->getComponents() as $component) {
			$formValues[] = $component->getName();
		}

		foreach ($this->getMetadata()->getProperties() as $name => $property) {
			if (in_array($name, $formValues)) {
				if ($this->hasValue($name)) {
					$value = $this->getValue($name);

					if ($value instanceof ManyHasMany) {
						$values[$name] = $value->toCollection()->fetchPairs(null, 'id');
					} else {
						$values[$name] = $value;
					}
				} else {
					$values[$name] = $this->getRawValue($name);
				}
			}
		}

		$timeFormat = 'Y-m-d H:i:s';

		if (isset($values['publicFrom']) && $values['publicFrom']) {
			$values['publicFrom'] = $values['publicFrom']->format($timeFormat);
		}

		if (isset($values['publicTo']) && $values['publicTo']) {
			$values['publicTo'] = $values['publicTo']->format($timeFormat);
		}

		if (isset($values['createdTimeOrder']) && $values['createdTimeOrder']) {
			$values['createdTimeOrder'] = $values['createdTimeOrder']->format('Y-m-d H:i:s');
		}

		return $values;
	}

}
