<?php

declare(strict_types=1);

namespace App\Model\Orm\Traits;

use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\Product\Product;
use ArrayIterator;
use Nextras\Dbal\Result\Row;
use stdClass;

/**
 * Todo kill me with 🔥
 */
trait HasParameters
{

	// [parameterUid => parameterValues]
	private mixed $parametersUidToParameterValuesCache = null;

	// [parameterID => ParameterValue]
	private mixed $parametersIdToParameterValueCache = null;

	// [parameterUid => parameter]
	private mixed $parameterUidToParameterCache = null;

	// [parameterValueId => parameterValues]
	private mixed $parametersValuesIdToParameterValueCache = null;

	// [parameterId => parameter]
	private mixed $parameterIdToParameterCache = null;

	public function flushParam(): void
	{
		$this->parameterUidToParameterCache = null;
		$this->parametersUidToParameterValuesCache = null;
		$this->parametersValuesIdToParameterValueCache = null;
		$this->parameterIdToParameterCache = null;
		$this->parametersIdToParameterValueCache = null;
	}


	public function fillParameterCache(): void
	{
		if ($this->parameterUidToParameterCache === null) {
			$this->parameterUidToParameterCache = [];
			$this->parametersUidToParameterValuesCache = [];
			$this->parametersValuesIdToParameterValueCache = [];
			$this->parameterIdToParameterCache = [];
			$this->parametersIdToParameterValueCache = [];

			foreach ($this->parametersValues as $parameterValue) {
				if ($parameterValue->parameter->type === Parameter::TYPE_MULTISELECT) {
					if (!isset($this->parametersUidToParameterValuesCache[$parameterValue->parameter->uid]) && $parameterValue->parameter->uid) {
						$this->parametersUidToParameterValuesCache[$parameterValue->parameter->uid] = new ArrayIterator();
					}

					if (!isset($this->parametersIdToParameterValueCache[$parameterValue->parameter->id])) {
						$this->parametersIdToParameterValueCache[$parameterValue->parameter->id] = new ArrayIterator();
					}

					if ($parameterValue->parameter->uid) {
						$this->parametersUidToParameterValuesCache[$parameterValue->parameter->uid]->append($parameterValue);
					}

					$this->parametersIdToParameterValueCache[$parameterValue->parameter->id]->append($parameterValue);

				} else {
					if ($parameterValue->parameter->uid) {
						$this->parametersUidToParameterValuesCache[$parameterValue->parameter->uid] = $parameterValue;
					}

					$this->parametersIdToParameterValueCache[$parameterValue->parameter->id] = $parameterValue;
				}

				$this->parameterIdToParameterCache[$parameterValue->parameter->id] = $parameterValue->parameter;
				$this->parametersValuesIdToParameterValueCache[$parameterValue->id] = $parameterValue;
			}

			$this->parametersUidToParameterValuesCache = new ArrayIterator($this->parametersUidToParameterValuesCache);
			$this->parametersValuesIdToParameterValueCache = new ArrayIterator($this->parametersValuesIdToParameterValueCache);
			$this->parametersIdToParameterValueCache = new ArrayIterator($this->parametersIdToParameterValueCache);

			// sestaveni
			foreach ($this->parameterIdToParameterCache as $parameterId => $parameter) {
				$shell = $this->getParameterShell($parameter);
				if ($parameter->type === Parameter::TYPE_MULTISELECT) {
					$shell->valueObjects = $this->getParameterValueById($parameter->id);
				} else {
					$shell->valueObject = $this->getParameterValueById($parameter->id);
				}

				$this->parameterIdToParameterCache[$parameterId] = $shell;
				if ($parameter->uid) {
					$this->parameterUidToParameterCache[$parameter->uid] = $shell;
				}
			}

			$this->parameterIdToParameterCache = new ArrayIterator($this->parameterIdToParameterCache);
			$this->parameterUidToParameterCache = new ArrayIterator($this->parameterUidToParameterCache);
		}
	}

	private function getParameterShell(Parameter $parameter): stdClass
	{
		$ret = new stdClass();
		$ret->entity = $parameter;

		$ret->id = $parameter->id;
		$ret->name = $parameter->name;
		$ret->uid = $parameter->uid;
		$ret->type = $parameter->type;
		$ret->unit = $parameter->unit;
		$ret->description = $parameter->description;
		$ret->title = $parameter->title;

		return $ret;
	}


	public function getParameterByUid(string $parameterUid): mixed
	{
		$this->fillParameterCache();

		if (isset($this->parameterUidToParameterCache[$parameterUid])) {
			return $this->parameterUidToParameterCache[$parameterUid];
		} else {
			return null;
		}
	}


	/**
	 * @param string $parameterUid
	 * @return ParameterValue|ParameterValue[]|ArrayIterator|null
	 */
	public function getParameterValueByUid(string $parameterUid): mixed
	{
		$this->fillParameterCache();

		if ($parameterUid !== '' && isset($this->parametersUidToParameterValuesCache[$parameterUid])) {
			return $this->parametersUidToParameterValuesCache[$parameterUid];
		} else {
			return null;
		}
	}


	public function getParameterValueByParameterValueId(int $parameterValueId): mixed
	{
		$this->fillParameterCache();
		if (isset($this->parametersValuesIdToParameterValueCache[$parameterValueId])) {
			return $this->parametersValuesIdToParameterValueCache[$parameterValueId];
		} else {
			return new ArrayIterator();
		}
	}


	public function getParameterById(int $parameterId): mixed
	{
		$this->fillParameterCache();
		if (isset($this->parameterIdToParameterCache[$parameterId])) {
			return $this->parameterIdToParameterCache[$parameterId];
		} else {
			return null;
		}
	}


	public function getParameters(): mixed
	{
		$this->fillParameterCache();
		return $this->parameterIdToParameterCache;
	}


	public function getParameterValueById(int $parameterId): mixed
	{
		$this->fillParameterCache();
		if (isset($this->parametersIdToParameterValueCache[$parameterId])) {
			return $this->parametersIdToParameterValueCache[$parameterId];
		} else {
			return null;
		}
	}


	public function getParametersValues(): mixed
	{
		$this->fillParameterCache();
		return $this->parametersIdToParameterValueCache;
	}


	public function hasParameterValue(ParameterValue $parameterValue): mixed
	{
		$this->fillParameterCache();
		return isset($this->parametersValuesIdToParameterValueCache[$parameterValue->id]);
	}


	/**
	 * @return array<Row>
	 */
	public function findParameterValuesRows(string $parameterUid): array
	{
		assert($this instanceof Product);
		return $this->productServices->parameterValueRepository->getRawValues($this, $parameterUid);
	}

}
