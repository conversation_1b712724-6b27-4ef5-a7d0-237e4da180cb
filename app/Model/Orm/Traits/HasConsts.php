<?php

declare(strict_types=1);

namespace App\Model\Orm\Traits;

use Nette\Utils\Strings;
use ReflectionClass;

trait HasConsts
{
	/**
	 * @param string $prefix
	 * @param string $translationPrefix podpora pro preklady, kdyz se z konstant sestavuje pole pro select a chceme hodnotu pouzit jako lang prekladu
	 * @return array
	 */
	public static function getConstsByPrefix(string $prefix, string $translationPrefix = ''): array
	{
		$classObject = new ReflectionClass(__CLASS__);
		$consts = [];

		foreach ($classObject->getConstants() as $key => $value) {
			if (str_starts_with($key, $prefix)) {
				$consts[$value] = $translationPrefix . $value;
			}
		}

		return $consts;
	}
}
