<?php declare(strict_types = 1);

namespace App\Model\Orm\Traits;

use Nextras\Orm\Entity\IEntity;

trait HasSimpleSave
{

	/**
	 * @param string[] $omit
	 */
	public function setEntityData(IEntity $entity, array $data, array $omit = []): void
	{
		$properties = $entity->getMetadata()->getProperties();

		$this->attach($entity); // @phpstan-ignore-line

		foreach ($data as $key => $value) {
			if (isset($properties[$key]) && !$properties[$key]->isVirtual && !in_array($key, $omit, true)) {
				$entity->setValue($key, $value);
			}
		}
	}

	public function save(?IEntity $entity, array $data): IEntity
	{
		if (!isset($entity)) {
			$entityClass = self::getEntityClassNames()[0];
			$entity = new $entityClass();
		}

		$this->setEntityData($entity, $data);
		$this->persist($entity);  // @phpstan-ignore-line

		return $entity;
	}

}
