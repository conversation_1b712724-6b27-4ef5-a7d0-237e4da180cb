<?php declare(strict_types = 1);

namespace App\Model\Orm\Traits;

use Nextras\Dbal\QueryBuilder\QueryBuilder;
use Nextras\Dbal\IConnection;
use Nextras\Dbal\Result\Result;


/**
 * @method QueryBuilder builder()
 * @property IConnection $connection
 * @property string $tableName
 */
trait HasFindPairFunction
{
	public function findPairs(string $value, ?string $key = null, ?array $where = [], ?array $order = []): Result
	{
		if ($key === null) {
			$builder = $this->builder()->select('%column as %column', $value, $value);
		} else {
			$builder = $this->builder()->select('%column as %column, %column as %column', $key, $key, $value, $value);
		}
		$builder = $builder->from($this->getTableName());
		if ($where !== []) {
			$builder = $builder->andWhere('%and', $where);
		}
		if ($order !== []) {
			/** @var literal-string $item */
			foreach ($order as $item) {
				$builder = $builder->addOrderBy($item);
			}
		}

		return $this->connection->queryByQueryBuilder($builder);
	}

}
