<?php declare(strict_types = 1);

namespace App\Model\Orm\Traits;


use App\Event\ProductListView;
use App\Model\Currency\CurrencyHelper;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\TagManager\GTM\Tag\GTMViewItemList;
use Nette\Utils\ArrayList;
use Nette\Utils\Json;
use Nextras\Orm\Collection\ICollection;

trait HasGTMViewport
{

	protected function initGTMViewport(ICollection|ArrayList $products, string $scriptId, string $listId, string $listName, int $index = 1, float $threshold = 0.0): void  // @phpstan-ignore-line
	{
		$this->template->gtmScriptId = $scriptId;
		$this->template->gtmThreshold = $threshold;
		$this->template->listId = $listId;
		$this->template->listName = $listName;

		$event = new ProductListView(
			variants: [],
			mutation: $this->setup->mutation,
			state: $this->setup->state,
			priceLevel: $this->setup->priceLevel,
			currency: CurrencyHelper::getCurrency(),
			listId: $listId,
			listName: $listName
		);

		$gtmEvent = new GTMViewItemList($event);
		$this->template->gtmItemEvent = Json::encode($gtmEvent->getData());

		$index--;
		$i = $index;
		foreach ($products as $product) {
			if ($product instanceof ProductVariant) {

				$this->template->productVariantsGtmItem[$product->product->id] = Json::encode(
					$product->product->getLocalization($this->setup->mutation)->getGTMData(
						variant: $product,
						currency: CurrencyHelper::getCurrency(),
						index: $i,
						itemListId: $this->template->listId,
						itemListName: $this->template->listName,
					)
				);

			} else {
				$this->template->productsGtmItem[$product->id] = Json::encode(
					$product->getLocalization($this->setup->mutation)->getGTMData(
						variant: $product->firstVariant,
						currency: CurrencyHelper::getCurrency(),
						index: $i,
						itemListId: $this->template->listId,
						itemListName: $this->template->listName,
					)
				);
			}
			$i++;
		}
	}
}
