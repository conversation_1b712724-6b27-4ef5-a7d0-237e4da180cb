<?php declare(strict_types=1);

namespace App\Model\Orm\Traits;

/**
 * Trait with static methods for enum array conversion
 *
 */
trait EnumToArray
{

	public static function names(): array
	{
		return array_column(self::cases(), 'name');
	}

	public static function values(): array
	{
		return array_column(self::cases(), 'value');
	}

	public static function toArray(): array
	{
		return array_combine(self::values(), self::names());
	}

}

