<?php

declare(strict_types=1);

namespace App\Model\Orm\Traits;

use App\Model\CacheFactory;
use Nette\Caching\Cache;

trait HasTemplateCache
{
	protected CacheFactory $cacheFactory;
	public function getTemplateCacheExpire(): int|string|null
	{
		return '1 hour';
	}

	public function getTemplateCacheTags(): array
	{
		return [static::class, static::class . '/' . $this->id];
	}

	public function getTemplateCacheTagsCascade():array {
		return [];
	}

	public function flushTemplateCache(): void
	{
		$tags = array_merge([static::class. '/' . $this->id], $this->getTemplateCacheTagsCascade());
		$cache = $this->cacheFactory->create(self::class);
		$cache->clean([
			Cache::Tags => $tags,
		]);
	}

	public function onAfterPersist(): void
	{
		$this->flushTemplateCache();
	}

	public function onAfterRemove(): void
	{
		$this->flushTemplateCache();
	}


	public function injectCacheFactory(CacheFactory $cacheFactory): void
	{
		$this->cacheFactory = $cacheFactory;
	}
}
