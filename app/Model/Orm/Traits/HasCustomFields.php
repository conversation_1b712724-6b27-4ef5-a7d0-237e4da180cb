<?php declare(strict_types = 1);

namespace App\Model\Orm\Traits;

use App\Model\CustomField\CustomField;
use Nette\Utils\Json;
use stdClass;
use App\Model\CustomField\CustomFields;

trait HasCustomFields
{

	use HasCache;

	private CustomFields $customFields;

	public function injectCustomFields(CustomFields $customFields): void
	{
		$this->customFields = $customFields;
	}

	public function getCfScheme(): array
	{
		return $this->customFields->resolveCustomFieldsFor($this);
	}


	public function getCfSchemeJson(): string
	{
		$order = 0;
		$scheme = [];

		$customFields = $this->customFields->resolveCustomFieldsFor($this);
		foreach ($customFields as $name => $customField) {
			$scheme[$name] = array_merge(
				$customField->jsonSerialize(),
				['order' => $order++],
			);
		}

		return Json::encode($scheme);
	}


	public function getCfContent(): string
	{
		$customFields = $this->customFieldsJson;

		return Json::encode($this->customFields->prepareForToShowInRs($this, $customFields));
	}


	protected function getterCf(): stdClass
	{
		assert($this->getMetadata()->hasProperty('customFieldsJson'));
		if (!isset($this->cache['customFields'])) {
			$customFields = $this->customFieldsJson;

			$this->cache['customFields'] = $this->customFields->prepareForToShow($this, $customFields);
		}

		return $this->cache['customFields'];
	}

	public function setCf(mixed $customFields): void
	{
		$this->cf = $customFields;
	}

	protected function setterCf(mixed $customFields): void
	{
		assert($this->getMetadata()->hasProperty('customFieldsJson'));
		$this->customFieldsJson = $customFields;
	}

}
