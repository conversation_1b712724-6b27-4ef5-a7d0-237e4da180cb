<?php declare(strict_types = 1);

namespace App\Model\Orm\Holiday;

use App\Model\Orm\Traits\HasFormDefaultData;
use App\PostType\Core\Model\Publishable;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property string $name {default ''}
 * @property DateTimeImmutable|null $publicFrom {default 'now'}
 * @property DateTimeImmutable|null $publicTo  {default '+1 day'}
 * @property DateTimeImmutable|null $created {default 'now'}
 * @property int $public {default 0}
 *
 * RELATIONS
 *
 * VIRTUAL
 *
 */
class Holiday extends Entity implements Publishable
{
	use HasFormDefaultData;

	public function getIsPublic(): bool
	{
		return !!$this->public;
	}

	public function setIsPublic(bool $isPublic): void
	{
		$this->public = (int) $isPublic;
	}
}
