<?php declare(strict_types = 1);

namespace App\Model\Orm\Holiday;

use App\Model\Orm\Orm;
use Nextras\Orm\Collection\ICollection;

final class HolidayModel
{
	private array $cache = [];

	public function __construct(
		private readonly Orm $orm
	)
	{
	}

	/**
	 * @return ICollection<Holiday>
	 */
	public function getAll(): ICollection
	{
		if (!isset($this->cache['getAll'])) {
			$this->cache['getAll'] = $this->orm->holiday->findBy([])->orderBy('publicTo');
		}

		return $this->cache['getAll'];
	}

	public function create(): Holiday
	{
		$newHoliday = new Holiday();
		$this->orm->holiday->attach($newHoliday);
		$this->orm->holiday->persistAndFlush($newHoliday);

		return $newHoliday;
	}

}

