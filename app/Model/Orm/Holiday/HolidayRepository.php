<?php declare(strict_types = 1);

namespace App\Model\Orm\Holiday;

use App\Model\Orm\Traits\HasPublicParameter;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method Holiday|null getById($id)
 * @method ICollection<Holiday> findByFilter(?ArrayHash $filter)
 *
 * @extends Repository<Holiday>
 */
final class HolidayRepository extends Repository
{

	use HasPublicParameter;

	public static function getEntityClassNames(): array
	{
		return [Holiday::class];
	}



	public function getPublicOnlyWhereParams(): array
	{
		return [
			'public' => 1,
		];
	}

}
