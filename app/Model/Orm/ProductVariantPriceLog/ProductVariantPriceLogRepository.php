<?php declare(strict_types = 1);

namespace App\Model\Orm\ProductVariantPriceLog;

use DateTimeImmutable;
use Nextras\Dbal\Result\Row;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 *
 * @method float|null getMinimumSalePrice(int $mutationId, int $productVariantId, int $priceLevelId, DateTimeImmutable $periodFrom)
 * @method Row[] findAfterSaleDuration(int $mutationId, DateTimeImmutable $periodFrom)
 *
 * @extends Repository<ProductVariantPriceLog>
 */
final class ProductVariantPriceLogRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [ProductVariantPriceLog::class];
	}

}
