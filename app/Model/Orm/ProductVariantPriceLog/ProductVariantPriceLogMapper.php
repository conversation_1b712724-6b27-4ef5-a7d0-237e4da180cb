<?php declare(strict_types = 1);

namespace App\Model\Orm\ProductVariantPriceLog;


use App\Model\Orm\Traits\HasCamelCase;
use DateTimeImmutable;
use Nextras\Orm\Mapper\Dbal\DbalMapper;
use function min;

/**
 * @extends DbalMapper<ProductVariantPriceLog>
 */
class ProductVariantPriceLogMapper extends DbalMapper
{

	use HasCamelCase;

	/**
* @return literal-string
*/
public function getTableName(): string
	{
		return 'product_variant_price_log';
	}

	public function getMinimumSalePrice(int $mutationId, int $productVariantId, int $priceLevelId, DateTimeImmutable $periodFrom): float|null
	{
		$minimumPriceInPeriod = $this->connection->query('
			SELECT MIN(salePrice) as minimumsalePrice FROM `product_variant_price_log`
			WHERE `mutationId` = %i
			  AND `productVariantId` = %i
			  AND `priceLevelId` = %i
			  AND `createdAt` >= %dt
		', $mutationId, $productVariantId, $priceLevelId, $periodFrom)->fetchField(0);

		$lastBeforePeriodPrice  = $this->connection->query('
			SELECT salePrice as minimumsalePrice FROM `product_variant_price_log`
			WHERE `mutationId` = %i
			  AND `productVariantId` = %i
			  AND `priceLevelId` = %i
			  AND `createdAt` < %dt
			ORDER BY `createdAt` DESC
			LIMIT 1
		', $mutationId, $productVariantId, $priceLevelId, $periodFrom)->fetchField(0);

		if ($minimumPriceInPeriod == null) {
			return (float) $lastBeforePeriodPrice;
		} elseif ($lastBeforePeriodPrice == null) {
			return (float) $minimumPriceInPeriod;
		} else {
			return min((float) $minimumPriceInPeriod, (float) $lastBeforePeriodPrice);
		}
	}

	public function findAfterSaleDuration(int $mutationId, DateTimeImmutable $periodFrom): array
	{
		return $this->connection->query("
		SELECT pvpl.*, pv.nameDefault, ug.name FROM product_variant_price_log AS pvpl
		JOIN product_variant AS pv ON pv.id = pvpl.productVariantId
		JOIN user_group AS ug ON (ug.id = pvpl.groupKey AND ug.type = 'default')
		WHERE pvpl.mutationId = %i
		AND pvpl.id IN (
			SELECT MAX(id) FROM product_variant_price_log GROUP BY mutationId, groupKey, productVariantId
		)
		AND pvpl.lastSaleAt < %dt
		", $mutationId, $periodFrom)->fetchAll();
	}

}
