<?php declare(strict_types = 1);

namespace App\Model\Orm\ProductVariantPriceLog;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\ProductVariant\ProductVariant;
use DateTimeImmutable;
use Nette\Utils\Floats;
use Nextras\Orm\Entity\Entity;
use function round;

/**
 * @property int $id {primary}
 *
 * @property float $realOrigPrice {default 0.0}
 * @property float $origPrice {default 0.0}
 * @property float $salePrice {default 0.0
 *
 * @property DateTimeImmutable $createdAt {default now}
 * @property DateTimeImmutable|null $lastSaleAt {default null}
 *
 * RELATIONS
 * @property ProductVariant $productVariant {m:1 ProductVariant::$priceLogs}
 * @property PriceLevel $priceLevel {m:1 PriceLevel::$priceLogs}
 * @property Mutation $mutation {m:1 Mutation::$priceLogs}
 *
 * VIRTUAL
 * @property bool $isInSale {virtual}
 * @property float $discount {virtual}
 */
class ProductVariantPriceLog extends Entity
{

	public function getterIsInSale(): bool
	{
		return Floats::isGreaterThan($this->realOrigPrice, $this->salePrice);
	}

	public function getterDiscount(): float
	{
		return $this->isInSale ? round(100 * ($this->realOrigPrice - $this->salePrice) / $this->realOrigPrice, 2) : 0;
	}

}
