<?php

declare(strict_types=1);

namespace App\Model\Orm\Mutation;

use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Orm\Mapper\Dbal\Conventions\Conventions;
use Nextras\Orm\Mapper\Dbal\Conventions\IConventions;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<Mutation>
 */
final class MutationMapper extends DbalMapper
{
	use HasCamelCase;
	/**
* @return literal-string
*/
public function getTableName(): string
{
	return 'mutation';
}

	// add mapping
	protected function createConventions(): IConventions
	{
		$conventions = parent::createConventions();
		assert($conventions instanceof Conventions);
		$conventions->manyHasManyStorageNamePattern = '%s_%s';
		return $conventions;
	}
}
