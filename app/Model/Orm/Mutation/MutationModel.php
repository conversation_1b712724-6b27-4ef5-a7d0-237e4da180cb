<?php declare(strict_types=1);

namespace App\Model\Orm\Mutation;

use App\Model\Cache\RouterCache;
use App\Model\Mutation\MutationsHolder;

final class MutationModel
{

	public function __construct(
		private readonly MutationRepository $mutationRepository,
		private readonly RouterCache $routerCache,
		private readonly MutationsHolder $mutationsHolder,
	) {}

	public function findForRouter(string $domain, string $prefix = ''): ?Mutation
	{
		$getMutationKey = fn($domainToSearch, $prefixToSearch) => strtolower($domainToSearch) . '--' . strtolower($prefixToSearch);
		$mutationSetups = $this->routerCache->getMutationsMap($getMutationKey);
		if ( ! isset($mutationSetups[$getMutationKey($domain, $prefix)])) {
			return null;
		}

		return $this->mutationsHolder->getMutationById($mutationSetups[$getMutationKey($domain, $prefix)]);
	}


	public function delete(Mutation $mutation): void
	{
		$this->mutationRepository->removeAndFlush($mutation);
	}

}
