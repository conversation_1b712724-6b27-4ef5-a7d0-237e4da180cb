<?php declare(strict_types = 1);

namespace App\Model\Orm\Mutation;

use App\Model\CustomField\LazyValue;
use App\Model\Orm\Alias\Alias;
use App\Model\Orm\AliasHistory\AliasHistory;
use App\Model\Orm\ClassEvent\ClassEvent;
use App\Model\Orm\ClassSection\ClassSection;
use App\Model\Orm\EmailTemplate\EmailTemplate;
use App\Model\Orm\EsIndex\EsIndex;
use App\Model\Orm\LibraryImage\LibraryImage;
use App\Model\Orm\NewsletterEmail\NewsletterEmail;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\ProductVariantLocalization\ProductVariantLocalization;
use App\Model\Orm\ProductVariantPrice\ProductVariantPrice;
use App\Model\Orm\ProductVariantPriceLog\ProductVariantPriceLog;
use App\Model\Pages;
use App\Model\PagesFactory;
use App\Model\Orm\State\State;
use App\Model\Orm\CurrencyContainer; // phpcs:ignore
use App\Model\Orm\JsonContainer; // phpcs:ignore
use App\PostType\Page\Model\Orm\Tree;
use App\Model\Orm\User\User;
use App\Model\Orm\UserMutation\UserMutation;
use App\PostType\SystemMessage\Model\Orm\SystemMessage;
use Brick\Money\Currency;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;
use App\Model\Orm\Traits\HasConfigService;
use App\Model\Orm\Traits\HasConsts;
use App\Model\Orm\Traits\HasCustomFields;
use App\Model\Orm\Traits\HasFormDefaultData;
use App\Model\Image\ImageObject;

/**
 * @property int $id {primary}
 * @property int $public {default 1}
 * @property string $langCode {enum self::CODE_*}
 * @property string|null $isoCode
 * @property string|null $name
 * @property string|null $langMenu {enum self::ISO_CODE_PREFIX_*}
 * @property string $isoCodePrefix
 *
 * @property string|null $adminEmail
 * @property string|null $contactEmail
 * @property string|null $orderEmail
 * @property string|null $fromEmail
 * @property string|null $fromEmailName
 * @property string|null $urlReviewPrefix
 * @property Currency $currency {container CurrencyContainer}
 * @property ArrayHash $synonyms {container JsonContainer}
 * @property string|null $heurekaOverenoKey
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 *
 * RELATIONS
 * @property OneHasMany<EmailTemplate> $emailTemplates  {1:m EmailTemplate::$mutation}
 * @property OneHasMany<EsIndex> $esIndexes  {1:m EsIndex::$mutation}
 * @property OneHasMany<ProductLocalization> $productLocalizations {1:m ProductLocalization::$mutation}
 * @property OneHasMany<ProductVariantLocalization> $variantLocalizations {1:m ProductVariantLocalization::$mutation}
 * @property OneHasMany<Alias> $aliases  {1:m Alias::$mutation}
 * @property OneHasMany<ClassSection> $classSections  {1:m ClassSection::$mutation}
 * @property OneHasMany<ClassEvent> $classEvents  {1:m ClassEvent::$mutation}
 * @property OneHasMany<Tree> $trees {1:m Tree::$mutation}
 * @property OneHasMany<AliasHistory> $historyAliases  {1:m AliasHistory::$mutation}
 *
 * @property ManyHasMany<User>|null $users {m:m User::$mutations}
 *
 * @property OneHasMany<ProductVariantPrice> $productVariantPrices  {1:m ProductVariantPrice::$mutation}
 * @property ManyHasMany<State> $states {m:m State::$mutations, isMain=true}
 * @property OneHasMany<UserMutation> $userMutations {1:m UserMutation::$mutation}
 * @property OneHasMany<NewsletterEmail> $newsletterEmails {1:m NewsletterEmail::$mutation}
 * @property OneHasMany<SystemMessage> $systemMessages {1:m SystemMessage::$mutation}
 * @property OneHasMany<ProductVariantPriceLog> $priceLogs {1:m ProductVariantPriceLog::$mutation, cascade=[persist, remove]}
 *
 *
 * VIRTUAL
 * @property-read Pages $pages {virtual}
 * @property-read ?Tree $rootPage {virtual}
 * @property-read string|null $domain {virtual}
 * @property-read string|null $urlPrefix {virtual}
 * @property-read string|null $region {virtual} https://app.sistrix.com/en/hreflang-generator
 * @property-read int $rootId {virtual}
 * @property-read int|null $hidePageId {virtual}
 * @property-read bool $isDefault {virtual}
 * @property ArrayHash|null $cf {virtual}
 * @property-read array $paymentsArray {virtual}
 * @property-read ArrayHash $cookie {virtual}
 * @property-read bool $isEnabledCookieModal {virtual}
 */
class Mutation extends Entity
{

	use HasConfigService;
	use HasCustomFields;
	use HasFormDefaultData;
	use HasConsts;

	public const CODE_CS = 'cs';
	//public const CODE_EN = 'en';
	public const CODE_SK = 'sk';
	//public const CODE_DE = 'de';
	//public const CODE_AT = 'at';
	//public const CODE_PL = 'pl';
	//public const CODE_ES = 'es';
	//public const CODE_Uk = 'uk';
	//public const CODE_XX = 'xx';

	public const ISO_CODE_PREFIX_CS = 'cs';
	public const ISO_CODE_PREFIX_EN = 'en';

	public const SYNONYMS_DELIMITER = ',';

	public const DEFAULT_CODE = self::CODE_CS; // urceni defaultni mutace

	public const DEFAULT_ISO_CODE = 'cs_CZ';
	public const DEFAULT_RS_CODE = self::CODE_CS;

	private Pages $pagesObject;

	private Currency $selectedCurrency;

	public function injectPages(PagesFactory $pagesFactory): void
	{
		$this->pagesObject = $pagesFactory->create($this);
	}


	public function getRealDomain(): string
	{
		// todo remove

		return $this->domain;
	}


	public function getRealDomainWithoutWWW(): string
	{
		return str_replace('www.', '', $this->domain);
	}


	public function getRealRootId(): int
	{
		$config = $this->configService->get('mutations', $this->langCode, 'rootId');
		if ($config) {
			$realRootId = $config;
		} else {
			$realRootId = $this->rootId;
		}

		return (int) $realRootId;
	}

	public function getRealUrlPrefix(): string
	{
		$config = $this->configService->get('mutations', $this->langCode, 'urlPrefix');
		if ($config) {
			$realUrlPrefix = $config;
		} else {
			$realUrlPrefix = $this->urlPrefix;
		}

		if (is_string($realUrlPrefix)) {
			return str_replace('/', '', $realUrlPrefix);
		} else {
			return '';
		}
	}

	public function getGTMCode(): string
	{
		// TODO zvazit rozsireni: zadavani kodu pres administraci...
		$domain = $this->configService->getParam('mutations', $this->langCode);
		return $domain['gtmCode'] ?? '';
	}

	public function getGACode(): string
	{
		// TODO zvazit rozsireni: zadavani kodu pres administraci...
		$domain = $this->configService->getParam('mutations', $this->langCode);
		return $domain['googleAnalyticsCode'] ?? '';
	}


	public function getRealOrderEmail(): string
	{
		$config = $this->configService->get('mutations', $this->langCode, 'orderEmail');
		if ($config) {
			$realOrderEmail = $config;
		} else {
			$realOrderEmail = $this->orderEmail;
		}

		return $realOrderEmail;
	}


	public function getRealAdminEmail(): string
	{
		$config = $this->configService->get('mutations', $this->langCode, 'adminEmail');
		if ($config) {
			$realAdminEmail = $config;
		} else {
			$realAdminEmail = $this->adminEmail;
		}

		return $realAdminEmail;
	}


	/**
	 * @param string $key .. values = admin|contact|order|breeding
	 * @return bool|mixed
	 */
	public function getEmail(string $key = 'admin')
	{
		$col = $key . 'Email';
		if (!isset($this->$col)) {
			return false;
		}

		$mutation = $this->configService->getParam('mutations', $this->langCode);

		if ($mutation && isset($mutation[$col]) && $mutation[$col]) {
			return $mutation[$col];
		} else {
			return $this->$col;
		}
	}


	public function getRealFromEmail(): string
	{
		$config = $this->configService->get('mutations', $this->langCode, 'fromEmail');
		if ($config) {
			$realEmailFrom = $config;
		} else {
			$realEmailFrom = $this->fromEmail;
		}

		return $realEmailFrom;
	}

	public function getRealFromEmailName(): string
	{
		$config = $this->configService->get('mutations', $this->langCode, 'fromEmailName');
		if ($config) {
			$realFromEmailName = $config;
		} else {
			$realFromEmailName = $this->fromEmailName;
		}

		return $realFromEmailName;
	}


	public function getBaseUrl(): string
	{
		return 'https://' . $this->domain;
	}


	public function getBaseUrlWithPrefix(): string
	{
		if ((bool) $this->urlPrefix) {
			return 'https://' . $this->domain . '/' . $this->urlPrefix;
		} else {
			return 'https://' . $this->domain;
		}
	}


	public function getUrl(): string
	{
		return $this->getBaseUrlWithPrefix();
	}


	protected function getterPages(): Pages
	{
		return $this->pagesObject;
	}

	protected function getterRootPage(): ?Tree
	{
		return $this->trees->toCollection()->getById($this->getRealRootId());
	}

	protected function getterHidePageId(): mixed
	{
		return $this->configService->get('mutations', $this->langCode, 'hidePageId');
	}


	protected function getterRootId(): int
	{
		return (int)$this->configService->get('mutations', $this->langCode, 'rootId');
	}

	protected function getterRegion(): mixed
	{
		return $this->configService->get('mutations', $this->langCode, 'region');
	}


	protected function getterUrlPrefix(): mixed
	{
		return $this->configService->get('mutations', $this->langCode, 'urlPrefix');
	}


	protected function getterDomain(): string
	{
		$domainFromConfig = $this->configService->get('mutations', $this->langCode, 'domain');
		if ($domainFromConfig === null) {
			throw new \LogicException(sprintf('Missing domain for \'%s\' mutation in neon', $this->langCode));
		}

		return (string) $domainFromConfig;
	}


	protected function getterIsDefault(): bool
	{
		return $this->langCode === self::DEFAULT_CODE;
	}

	protected function getterCookie(): ArrayHash
	{
		$cookieSetup = $this->configService->get('mutations', $this->langCode, 'cookie') ?? [];
		return ArrayHash::from($cookieSetup);
	}

	protected function getterIsEnabledCookieModal(): bool
	{
		return $this->configService->get('cookie', 'enableModal') && isset($this->cookie->enableModal) && $this->cookie->enableModal;
	}


	public function setSelectedCurrency(Currency $currency): void
	{
		$this->selectedCurrency = $currency;
	}

	public function getSelectedCurrency(): Currency
	{
		return $this->selectedCurrency ?? $this->currency;
	}

	public function getOgImage(string $size): ?ImageObject
	{
		/** @var ?LazyValue $ogImageLazyValue */
		$ogImageLazyValue = $this->cf->mutationData->ogImage ?? null;
		if ($ogImageLazyValue !== null) {
			/** @var LibraryImage $ogImage */
			$ogImage = $ogImageLazyValue->getEntity();
			return $ogImage->getSize($size);
		}

		return null;
	}

	public function getOgImageSharedLibrary(string $size): ?string
	{
		/** @var ?LazyValue $ogImageLazyValue */
		$ogImageLazyValue = $this->cf->mutationData->ogImageMyLibrary ?? null;
		if ($ogImageLazyValue !== null) {
			/** @var LibraryImage $ogImage */
			$ogImage = $ogImageLazyValue->getEntity();
			return $this->getBaseUrlWithPrefix().$ogImage->getSize($size)->src;
		}

		return null;
	}

	public function getFirstState(): ?State
	{
		return $this->states->toCollection()->limitBy(1)->fetch();
	}

}
