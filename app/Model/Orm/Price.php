<?php declare(strict_types = 1);

namespace App\Model\Orm;

use App\Model\Currency\CurrencyHelper;
use Brick\Math\RoundingMode;
use Brick\Money\Currency;
use Brick\Money\Money;
use Nextras\Orm\Entity\Embeddable\Embeddable;

/**
 * @property-read string $amount
 * @property-read string $currency
 */
final class Price extends Embeddable
{

	private function __construct(
		string $amount,
		string $currency,
	)
	{
		parent::__construct([
			'amount' => $amount,
			'currency' => $currency,
		]);
	}

	public static function from(Money $money): self
	{
		return new self(
			(string) $money->getAmount(),
			$money->getCurrency()->getCurrencyCode(),
		);
	}

	public function asMoney(?int $forcePrecision = null): Money
	{
		$defaultsForCurrency = Currency::of($this->currency);
		$currency = new Currency(
			currencyCode: $this->currency,
			numericCode: $defaultsForCurrency->getNumericCode(),
			name: $defaultsForCurrency->getName(),
			defaultFractionDigits: $forcePrecision ?? CurrencyHelper::CURRENCY_SETTINGS[$this->currency]['precision'] ?? $defaultsForCurrency->getDefaultFractionDigits(),
		);

		return Money::of(
			amount: $this->amount,
			currency: $currency,
			roundingMode: CurrencyHelper::CURRENCY_SETTINGS[$this->currency]['rounding'] ?? RoundingMode::HALF_UP
		);
	}

}
