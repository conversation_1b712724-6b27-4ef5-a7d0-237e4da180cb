<?php

declare(strict_types=1);

namespace App\Model\Orm\VoucherLimit;

use App\Model\Orm\JsonContainer; // phpcs:ignore
use App\Model\Orm\Voucher\Voucher;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Entity\Entity;
/**
 * @property int $id {primary}
 * @property bool $isNegated {default false}
 * @property ArrayHash $limit {container JsonContainer}
 * @property Voucher $voucher {m:1 Voucher::$limits}
 *
 */
class VoucherLimit extends Entity
{

}
