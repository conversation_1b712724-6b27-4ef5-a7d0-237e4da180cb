<?php declare(strict_types = 1);

namespace App\Model\Orm\String;

use App\Model\Orm\Mutation\Mutation;
use Nextras\Dbal\Result\Result;
use Nextras\Dbal\Result\Row;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;
use App\Model\Orm\Traits\HasSimpleSave;

/**
 * @method StringEntity|null getById($id)
 * @method StringEntity|null getBy(array $conds)
 * @method StringEntity save(?StringEntity $entity, array $data)
 * @method null replace(array $data)
 * @method ICollection<StringEntity> findNotParams()
 *
 * @method void cloneAll(Mutation $targetMutation, Mutation $sourceMutation)
 * @method Row|null getRawRow(string $langCode, string $key)
 * @method Result findRawRow(Mutation $mutation)
 *
 * @extends Repository<StringEntity>
 */
final class StringRepository extends Repository
{

	use HasSimpleSave;

	/**
	 * @inheritDoc
	 */
	public static function getEntityClassNames(): array
	{
		return [StringEntity::class];
	}


	public function setTranslation(Mutation $mutation, string $name, string $value): StringEntity
	{
		$entity = $this->getByName($mutation, $name);
		return $this->save($entity, ['name' => $name, 'value' => $value, 'lg' => $mutation->langCode]);
	}

	public function getByName(Mutation $mutation, string $name): ?StringEntity
	{
		return $this->getBy(['lg' => $mutation->langCode, 'name' => $name]);
	}

	public function delete(string $name): void
	{
		foreach ($this->findBy(['name' => $name]) as $item) {
			$this->remove($item);
		}
	}

	public function removeRow(string $langCode, string $name): void
	{
		$stringEntity = $this->getBy([
			'name' => $name,
			'lg' => $langCode,
		]);

		if ($stringEntity !== null) {
			$this->removeAndFlush($stringEntity);
		}
	}

}
