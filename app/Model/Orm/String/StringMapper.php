<?php declare(strict_types = 1);

namespace App\Model\Orm\String;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\TableHelper;
use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Dbal\Result\Result;
use Nextras\Dbal\Result\Row;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<StringEntity>
 */
final class StringMapper extends DbalMapper
{

	use HasCamelCase;

	/**
* @return literal-string
*/
public function getTableName(): string
{
	return 'string';
}

	public function replace(array $data): void
	{
		$this->connection->query('INSERT INTO [string] %values ON DUPLICATE KEY UPDATE %set', $data, $data);
	}

	/**
	 * @return ICollection<StringEntity>
	 */
	public function findNotParams(): ICollection
	{
		$builder = $this->builder()
			->andWhere("name NOT LIKE 'pvalue_%'")
			->andWhere("name NOT LIKE 'pname%'");

		return $this->toCollection($builder);
	}


	public function cloneAll(Mutation $targetMutation, Mutation $sourceMutation): void
	{
		$columns = TableHelper::getTableColumns($this->connection, $this->getTableName());
		// clean old items
		$this->connection->query('DELETE FROM string WHERE lg = %s', $targetMutation->langCode);

		$this->connection->query('CREATE TEMPORARY TABLE string_copy_tmp SELECT %column[] FROM string WHERE lg = %s', $columns, $sourceMutation->langCode);
		$this->connection->query('UPDATE string_copy_tmp SET lg = %s', $targetMutation->langCode);
		$this->connection->query('INSERT INTO `string`  (%column[])  SELECT %column[] FROM string_copy_tmp', $columns, $columns);
		$this->connection->query('DROP TEMPORARY TABLE IF EXISTS string_copy_tmp');
	}

	public function getRawRow(string $langCode, string $key): ?Row
	{
		return $this->connection->query('select id, value from %table where name=%s and lg=%s', $this->getTableName(), $key, $langCode)->fetch();
	}

	public function findRawRow(Mutation $mutation): Result
	{
		return $this->connection->query('select * from %table where lg=%s', $this->getTableName(),  $mutation->langCode);
	}

}
