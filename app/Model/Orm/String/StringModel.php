<?php declare(strict_types = 1);

namespace App\Model\Orm\String;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Mutation\MutationRepository;
use App\Model\TranslatorDBCacheService;
use League\Csv\CannotInsertRecord;
use League\Csv\Exception;
use League\Csv\InvalidArgument;
use League\Csv\Reader;
use League\Csv\Writer;
use Nette\Utils\Random;
use Nette\Utils\Strings;
use RuntimeException;

final class StringModel
{

	public function __construct(
		private readonly StringRepository $stringRepository,
		private readonly MutationRepository $mutationRepository,
		private readonly TranslatorDBCacheService $translatorDBCacheService,
	)
	{
	}

	/**
	 * @param array[] $strings - [['name' => 'title', 'en' => 'Title', ...]]
	 * @param string[] $langs - list of mutations (langCode) to be exported
	 * @return string - tmp csv file name
	 * @throws CannotInsertRecord
	 * @throws InvalidArgument
	 */
	public function export(array $strings, array $langs): string
	{
		$file = TEMP_DIR . '/translations-' . Random::generate() . '.csv';
		if (file_exists($file)) {
			unlink($file);
		}

		$writer = Writer::createFromPath($file, 'w+');
		$writer->setDelimiter(';');
		$writer->insertOne(array_merge(['key'], array_values($langs)));

		foreach ($strings as $row) {
			$values = [$row['name']];
			foreach ($langs as $code) {
				$value = $row[$code] ?? '';

				if (str_starts_with($value, '##')) {
					$value = '';
				}

				$values[] = $value;
			}

			$writer->insertOne($values);
		}
		return $file;
	}

	/**
	 * @throws InvalidArgument
	 * @throws Exception
	 */
	public function import(string $filename, bool $updateOnly): array
	{
		$report = ['total' => ['inserted' => 0, 'updated' => 0, 'skipped' => 0]];
		$reader = Reader::createFromPath($filename);
		$reader->setHeaderOffset(0);

		$mutations = $this->mutationRepository->findAll()->fetchPairs('langCode');

		//check header
		$header = $reader->getHeader();
		if (count($header) === 1 && str_contains($header[0], ';')) {
			$reader->setDelimiter(';');
			$header = $reader->getHeader();
		}

		$columns = [];
		foreach ($header as $col) {
			if (isset($columns[$col])) {
				throw new RuntimeException('Duplicate column name: ' . $col);
			}

			$columns[$col] = 1;

			if ($col !== 'key') {
				$report[$col] = ['inserted' => 0, 'updated' => 0, 'skipped' => 0];

				if (!isset($mutations[$col])) {
					throw new RuntimeException('Invalid column name: ' . $col);
				}
			}
		}

		//import rows
		foreach ($reader->getRecords() as $row) {
			$key = $row['key'];
			foreach ($row as $col => $value) {
				if ($col === 'key') {
					continue;
				}

				$data = ['name' => $key, 'lg' => $col];
				$entity = $this->stringRepository->getBy($data);
				if (!isset($entity) && $updateOnly) {
					$report['total']['skipped']++;
					$report[$col]['skipped']++;
				} else {
					$report['total'][isset($entity) ? 'updated' : 'inserted']++;
					$report[$col][isset($entity) ? 'updated' : 'inserted']++;
					if (($value = trim($value)) === '') {
						$value = '##' . $key;
					}

					$data['value'] = $value;
					$this->stringRepository->save($entity, $data);
				}
			}
		}

		$this->stringRepository->flush();
		return $report;
	}


	public function saveTranslation(Mutation $mutation, string $key, string $newValue, bool $deleteOnEmptyString = false): ?StringEntity
	{
		$translationKey = null;
		if ($deleteOnEmptyString && $newValue === '') {
			$translation = $this->stringRepository->getBy(['name' => $key, 'lg' => $mutation->langCode]);
			if ($translation !== null) {
				$translationKey = $translation->name;
				$this->stringRepository->removeRow(langCode: $mutation->langCode, name: $key);
			}

		} else {
			$edit = [
				'value' => $newValue,
				'name' => $key,
				'lg' => $mutation->langCode,
			];
			$this->stringRepository->replace($edit);
			$translation = $this->stringRepository->getBy(['name' => $key, 'lg' => $mutation->langCode]);
			if ($translation !== null) {
				$translationKey = $translation->name;
			}
		}

		if ($translationKey !== null) {
			$this->translatorDBCacheService->cleanCacheByKey($translationKey);
		}

		return $translation;
	}

}
