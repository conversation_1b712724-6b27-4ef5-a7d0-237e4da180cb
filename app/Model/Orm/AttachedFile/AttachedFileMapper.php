<?php

declare(strict_types=1);

namespace App\Model\Orm\AttachedFile;

use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Orm\Mapper\Dbal\Conventions\Conventions;
use Nextras\Orm\Mapper\Dbal\Conventions\IConventions;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<AttachedFile>
 */
final class AttachedFileMapper extends DbalMapper
{
	use HasCamelCase;
	/**
* @return literal-string
*/
public function getTableName(): string
{
	return 'tree_file';
}

	protected function createConventions(): IConventions
	{
		$conventions = parent::createConventions();
		assert($conventions instanceof Conventions);
		$conventions->setMapping('parent', 'parentId');
		return $conventions;
	}
}
