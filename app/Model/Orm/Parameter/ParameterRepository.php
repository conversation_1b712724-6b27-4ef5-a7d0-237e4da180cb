<?php declare(strict_types = 1);

namespace App\Model\Orm\Parameter;

use App\Model\Orm\CollectionById;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Traits\HasSimpleSave;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method Parameter|null getById($id)
 * @method Parameter|null getBy(array $conds)
 * @method ICollection<Parameter> searchByName(string $search, array $selectedSiblingsIds)
 * @method ICollection<Parameter> findBy(array $conds)
 * @method ICollection<Parameter> findByUidOrdered(array $uids)
 * @method ICollection<Parameter> findByExactOrder(array $ids)
 *
 * @method Result findProductParameterValueIds(Parameter $parameter)
 * @method ICollection<Parameter> findParametersForProduct(Product $product)
 * @method ICollection<Parameter> findDetailParametersForProduct(Product $product)
 * @method Parameter save(?Parameter $entity, array $data)
 * @method void initSort(bool $forceInit = false)
 * @method void moveUp(int $siblingSort, int $sort)
 * @method void moveDown(int $siblingSort, int $sort)
 * @method void delete(Parameter $parameter)
 * @method void unlockParameters()
 *
 * @method ICollection<Parameter> findByProductType(string $productType, bool $isVariantParameter = false)
 *
 * @extends Repository<Parameter>
 */
final class ParameterRepository extends Repository implements CollectionById
{

	use HasSimpleSave;

	/**
	 * @inheritDoc
	 */
	public static function getEntityClassNames(): array
	{
		return [Parameter::class];
	}

	/**
	 * @param string|int|null $id
	 */
	public function getByExtId($id): ?Parameter
	{
		return isset($id) ? $this->getBy(['extId' => (string) $id]) : null;
	}

	public function getByUid(string $uid): ?Parameter
	{
		return $this->getBy(['uid' => $uid]);
	}



	/**
	 * @return ICollection<Parameter>
	 */
	public function findByIdOrder(array $ids): ICollection
	{
		return $this->findByExactOrder($ids);
	}
}
