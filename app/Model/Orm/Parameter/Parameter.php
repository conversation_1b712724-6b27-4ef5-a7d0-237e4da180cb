<?php

declare(strict_types=1);

namespace App\Model\Orm\Parameter;

use App\Model\Orm\JsonContainer; // phpcs:ignore
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\String\StringEntity;
use App\Model\Orm\Traits\HasConsts;
use App\Model\Orm\Traits\HasCustomFields;
use App\Model\Orm\Traits\HasFormDefaultData;
use App\Model\Orm\Traits\HasTranslator;
use App\Model\TranslatorDB;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property int $id {primary}
 * @property string $name
 * @property string|null $uid
 * @property string $type {enum self::TYPE_*}
 * @property int $sort {default 0}
 * @property bool $pendingSort {default false}
 * @property int $variantParameter {default 0}
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 * @property int $isInFilter {default 0}
 * @property int $isInDetail {default 0}
 * @property int $isLockedForES {default 0}
 * @property bool $isProtected {default false}
 * @property string|null $extId
 * @property string|null $typeSort {enum self::SORT_*}
 * @property string|null $productType
 *
 * RELATIONS
 * @property OneHasMany<ParameterValue> $options  {1:m ParameterValue::$parameter, orderBy=[sort=ASC]}
 *
 *
 * VIRTUALS
 * @property-read bool $isChecked {virtual}
 * @property-read bool $isSimple {virtual}
 * @property ArrayHash|null $cf {virtual}
 * @property-read bool $hasTranslatedValues {virtual}
 * @property-read string|null $unit {virtual}
 * @property-read string|null $description {virtual}
 * @property-read string $filterPrefix {virtual}
 * @property-read string $filterPostfix {virtual}
 * @property-read string|null $title {virtual}
 *
 */
class Parameter extends Entity
{

	use HasTranslator;
	use HasConsts;
	use HasFormDefaultData;
	use HasCustomFields {
		onAfterPersist as flushCacheAfterPersist;
	}

	public const TYPE_MULTISELECT = 'multiselect';
	public const TYPE_SELECT = 'select';
	public const TYPE_BOOL = 'bool';
	public const TYPE_NUMBER = 'number';
	public const TYPE_WYSIWYG = 'wysiwyg';
	public const TYPE_TEXTAREA = 'textarea';
	public const TYPE_TEXT = 'text';

	public const TYPES = [
		self::TYPE_TEXT => 'textové pole',
		self::TYPE_NUMBER => 'číslo',
		self::TYPE_BOOL => 'ano / ne',
		self::TYPE_WYSIWYG => 'HTML editor',
		self::TYPE_SELECT => 'číselník (výběr jedné hodnoty)',
		self::TYPE_MULTISELECT => 'číselník (výběr více hodnot)',
	];

	public const UID_COLOR = 'color';

	public const UID_EDITION = 'edice';

	public const UID_PUBLISH_DATE = 'datum-vydani';
	public const UID_WEIGHT = 'vaha';
	public const UID_BRAND = 'brand';
	public const UID_COUNTRY = 'zeme';

	public const UID_HEIGHT = 'vyska';
	public const UID_WIDTH = 'sirka';
	public const UID_DEPTH = 'tloustka';
	public const UID_LANGUAGE = 'jazyk';
	public const UID_PRODUCT_TYPE = 'typ-produktu';
	public const UID_PAGE_COUNT = 'pocet-stran';
	public const UID_BIND = 'vazba';
	public const TRANS_TYPE_NAME = 'name';
	public const TRANS_TYPE_TOOLTIP = 'tooltip';

	public const SORT_ALPHABET = 'alphabet';
	public const SORT_NUMERIC = 'numeric';
	public const SORT_NUMERIC_DESC = 'numeric_desc';

	public const UID_COURSE_TYPE = 'prubehKurzu';
	public const UID_REQUALIFICATION_TYPE = 'typKurzu';

	public const SORTS = [
		self::SORT_NUMERIC => 'matematicky sestupně',
		self::SORT_NUMERIC_DESC => 'matematicky vzestupně',
		self::SORT_ALPHABET => 'abecedně',
	];

	private Orm $orm;

	public function injectOrm(Orm $orm): void
	{
		$this->orm = $orm;
	}

	public function getterIsChecked(): bool
	{
		return isset($_COOKIE['param-cat' . $this->id]);
	}

	/** Return true if paramter and paramtervalues are simple (text, number ...)
	 *
	 * @return bool
	 */
	public function getterIsSimple(): bool
	{
		return in_array($this->type, [
//			self::TYPE_MULTISELECT,
//			self::TYPE_SELECT,
			self::TYPE_BOOL,
			self::TYPE_NUMBER,
			self::TYPE_WYSIWYG,
			self::TYPE_TEXTAREA,
			self::TYPE_TEXT,
		], true);
	}

	public function onAfterPersist(): void
	{
		parent::onAfterPersist();
		if ($this->isModified('sort')) {
			$this->orm->parameterValue->updateParameterSort($this);
		}
		$this->flushCacheAfterPersist();
	}

	protected function getterUnit(): ?string
	{
		if (in_array($this->type, [
			self::TYPE_MULTISELECT,
			self::TYPE_SELECT,
			self::TYPE_NUMBER,
		], true)) {

			$defaultValue = 'pname_unit_' . $this->id;
			$translate = $this->translator->translate($defaultValue, TranslatorDB::DONT_INSERT_NEW);

			return ($translate === $defaultValue) ? '' : $translate;
		}

		return null;
	}

	protected function getterDescription(): string
	{
		$defaultValue = 'pname_tooltip_' . $this->id;
		$translate = $this->translator->translate($defaultValue, TranslatorDB::DONT_INSERT_NEW);

		return ($translate === $defaultValue) ? '' : $translate;
	}


	protected function getterFilterPrefix(): string
	{
		$defaultValue = 'pname_filter_prefix_' . $this->id;
		$translate = $this->translator->translate($defaultValue, TranslatorDB::DONT_INSERT_NEW);

		return ($translate === $defaultValue) ? '' : $translate;
	}

	protected function getterFilterPostfix(): string
	{
		$defaultValue = 'pname_filter_postfix_' . $this->id;
		$translate = $this->translator->translate($defaultValue, TranslatorDB::DONT_INSERT_NEW);

		return ($translate === $defaultValue) ? '' : $translate;
	}

	protected function getterTitle(): string
	{
		$defaultValue = 'pname_' . $this->id;
		$translate = $this->translator->translate($defaultValue, TranslatorDB::DONT_INSERT_NEW);

		return ($translate === $defaultValue) ? $this->name : $translate;
	}

	protected function getterHasTranslatedValues(): bool
	{
		return in_array($this->type, [
			self::TYPE_MULTISELECT,
			self::TYPE_SELECT,
//			self::TYPE_NUMBER,
		], true);
	}


	protected function getTransName(string $type): string
	{
		$prefix = 'pname_' . ($type === self::TRANS_TYPE_NAME ? '' : ($type . '_'));
		return $prefix . $this->id;
	}

	public function hasEditableValues(): bool
	{
		if ($this->options->countStored() > 500) {
			return false;
		}
		return in_array($this->type, [Parameter::TYPE_MULTISELECT, Parameter::TYPE_SELECT, Parameter::TYPE_BOOL]);
	}

	public function isDimensionParameter(): bool
	{
		return in_array($this->uid, [self::UID_HEIGHT, self::UID_WIDTH, self::UID_DEPTH], true);
	}

	public function hasDetailPage(): bool
	{
		return in_array($this->uid, []);
	}
}
