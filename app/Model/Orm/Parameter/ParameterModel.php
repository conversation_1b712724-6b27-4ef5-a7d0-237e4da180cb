<?php declare(strict_types = 1);

namespace App\Model\Orm\Parameter;

use App\AdminModule\Presenters\Parameter\Components\ShellForm\FormData\BaseFormData;
use App\Model\Erp;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\String\StringModel;
use Nette\Utils\ArrayHash;
use Nette\Utils\Random;
use Nette\Utils\Strings;
use RuntimeException;

final class ParameterModel
{

	public function __construct(
		private readonly Orm $orm,
		protected StringModel $stringModel,
	)
	{
	}

	public function createNew(BaseFormData $data): Parameter
	{
		$parameter = new Parameter();
		$this->orm->parameter->attach($parameter);

		$parameter->name = $data->name;
		$parameter->type = $data->parameterType;
		$this->orm->parameter->persistAndFlush($parameter);

		return $parameter;
	}

	public function crateNewFromErp(Erp\Entity\Parameter $extParameter, Mutation $mutation): Parameter
	{
		$parameter = new Parameter();
		$this->orm->parameter->attach($parameter);

		$parameter->name = $extParameter->name;
		$parameter->type = $extParameter->type;
		$parameter->extId = $extParameter->extId;
		$parameter->uid = lcfirst(str_replace(' ', '', ucwords(strtr(Strings::toAscii($extParameter->name), '_-', ' '))));
		$parameter->isProtected = true;

		$parameter = $this->orm->parameter->persistAndFlush($parameter);

		$this->handleParameterTranslations($parameter, $extParameter, $mutation);
		return $parameter;
	}

	private function handleParameterTranslations(Parameter $parameter, Erp\Entity\Parameter $extParameter, Mutation $mutation): void
	{
		$keyMap = [
			'pname' => 'name',
			'pname_unit' => 'unit',
		];

		foreach ($keyMap as $translationName => $propName) {
			if (isset($extParameter->$propName)) {
				$key = $translationName . '_' . $parameter->id;
				$newValue = trim($extParameter->$propName);

				$this->stringModel->saveTranslation(
					mutation: $mutation,
					key: $key,
					newValue: $newValue,
					deleteOnEmptyString: true,
				);
			}
		}
	}

	public function getUid(Parameter $parameter, string $uid, int $k = 0): string
	{
		if ($uid === '') {
			// aut. gener. UID
			$str = Strings::toAscii($parameter->name);
			$uid = lcfirst(str_replace(' ', '', ucwords(strtr($str, '_-', ' '))));
		}

		if ($uid === '') {
			$uid = Random::generate(5);
		}

		if ($k) {
			$uidToTest = $uid . '-' . $k;
		} else {
			$uidToTest = $uid;
		}
		$entityWithUid = $this->orm->parameter->findBy(['uid' => $uidToTest, 'id!=' => $parameter->id]);

		if ($entityWithUid->countStored()) {
			return $this->getUid($parameter, $uid, $k + 1);
		} else {
			return $uidToTest;
		}
	}


	public function remove(Parameter $object): void
	{
		if ($object->isProtected) {
			throw new RuntimeException('Cannot delete a protected parameter.');
		}

		$this->orm->parameter->delete($object);
	}

	public function reindexSort(Parameter $parameter): void
	{
		$parameter->pendingSort = true;
		$this->orm->persistAndFlush($parameter);
		//$this->messageBus->dispatch(new SortMessage($parameter->id, $parameter->typeSort));
	}

	/**
	 * @return array<string, ArrayHash>
	 */
	public function getParameterWithValueByExtIdList(): array
	{
		$list = [];

		/** @var Parameter[] $parameters */
		$parameters = $this->orm->parameter->findBy(['extId!=' => null])->fetchPairs('extId');

		foreach ($parameters as $extId => $parameter) {

			$item = new ArrayHash();
			$item->parameter = $parameter;
			$item->values = $parameter->options->toCollection()->fetchPairs('extId');

			$list[$extId] = $item;
		}

		return $list;
	}

}
