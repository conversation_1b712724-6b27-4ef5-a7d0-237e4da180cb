<?php

declare(strict_types=1);

namespace App\Model\Orm\Watchdog;

use App\Model\Orm\ProductProduct\ProductProduct;
use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<Watchdog>
 */
class WatchdogMapper extends DbalMapper
{

	use HasCamelCase;

	/**
	 * @return literal-string
	 */
	public function getTableName(): string
	{
		return 'watchdog';
	}


	public function findProductIds(): array
	{
		return $this->connection->query('SELECT productId FROM watchdog GROUP BY productId')->fetchPairs('productId', 'productId');
	}

	public function findRelatedProductIds(array $productIdsToCheck): array
	{
		return $this->connection->query('SELECT attachedProductId, mainProductId FROM product_product WHERE `type` = %s AND mainProductId IN %i[]', ProductProduct::TYPE_NORMAL, $productIdsToCheck)
			->fetchPairs('attachedProductId', 'mainProductId');
	}

}
