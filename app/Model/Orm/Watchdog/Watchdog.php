<?php

declare(strict_types=1);

namespace App\Model\Orm\Watchdog;

use App\Model\Orm\JsonArrayContainer; // phpcs:ignore
use App\Model\Orm\Product\Product;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property DateTimeImmutable $createdAt {default now}
 * @property string $email
 * @property Product $product {m:1 Product, oneSided=true}
 * @property array|null $relatedSended {container JsonArrayContainer}
 */
class Watchdog extends Entity
{

}
