<?php
declare(strict_types=1);

namespace App\Model\Orm\Watchdog;

use App\Model\ConfigService;
use App\Model\Email\Common;
use App\Model\Email\CommonFactory;
use App\Model\Link\LinkFactory;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Traits\HasCache;

final class WatchdogModel
{

	use HasCache;

	// phpcs:disable
	private ?Common $mailer = null  {
		get {
			if ( $this->mailer === null ) {
				$this->mailer = $this->commonEmailFactory->create();
			}
			return $this->mailer;
		}
	}
	// phpcs:enable
	public function __construct(
		private readonly LinkFactory $linkFactory,
		private readonly CommonFactory $commonEmailFactory,
		private readonly ConfigService $configService,
		private readonly Orm $orm,
	)
	{
	}

	public function send(Watchdog $watchdog, Mutation $mutation, ?string $from = null, ?Common $mail = null): void
	{
		$mailer = $mail ?? $this->mailer;
		$from = $from ?? $this->configService->get('watchdog', 'notificationEmail');

		$productLocalization = $watchdog->product->getLocalization($mutation);
		$mailer->send($from, $watchdog->email, 'watchdog', [
			'name' => $productLocalization->name,
			'url'  => $this->linkFactory->linkTranslateToNette($productLocalization),
		]);
	}

	public function sendRelated(Watchdog $watchdog, Mutation $mutation, int $productId, ?string $from = null, ?Common $mail = null): void
	{
		$mailer = $mail ?? $this->mailer;
		$from = $from ?? $this->configService->get('watchdog', 'notificationEmail');

		$productLocalization = $this->orm->productLocalization->getBy(['product->id' => $productId, 'mutation' => $mutation]);

		$mailer->send($from, $watchdog->email, 'watchdogRelated', [
			'name' => $productLocalization->name,
			'url'  => $this->linkFactory->linkTranslateToNette($productLocalization),
		]);
	}

}
