<?php declare(strict_types = 1);

namespace App\Model\Orm;

use App\Model\Orm\Alias\Alias;
use App\Model\Orm\AliasHistory\AliasHistory;
use App\Model\Orm\AliasHistory\AliasHistoryModel;
use App\Model\Orm\AliasHistory\AliasHistoryRepository;
use App\Model\Orm\Alias\AliasModel;
use App\Model\Orm\Alias\AliasRepository;
use App\Model\Orm\Mutation\Mutation;
use Nextras\Orm\Collection\ICollection;
use App\Model\Orm\Traits\HasCache;

/**
 * @property string $template {default ''}
 * @property string $name {default ''}
 * @property string $nameAnchor {default ''}
 * @property string $nameTitle {default ''}
 * @property ?string $description {default ''}
 * @property string $keywords {default ''}

 *
 * RELATIONS
 *
 *
 * VIRTUAL
 * @property-read array $path {virtual}
 * @property-read string $module {virtual}
 * @property-read Alias|null $alias {virtual}
 * @property-read AliasHistory[]|null $aliasHistory {virtual}
 * @property bool $forceNoIndex {default false} {virtual}
 * @property bool $hideInSearch {default false} {virtual}
 */
abstract class RoutableEntity extends BaseEntity implements Routable
{

	use HasCache;

	protected AliasModel $aliasModel;

	protected AliasHistoryModel $aliasHistoryModel;

	protected AliasRepository $aliasRepository;

	protected AliasHistoryRepository $aliasHistoryRepository;

	public function injectRoutableDependency(
		AliasRepository $aliasRepository,
		AliasModel $aliasModel,
		AliasHistoryRepository $aliasHistoryRepository,
		AliasHistoryModel $aliasHistoryModel
	): void
	{
		$this->aliasRepository = $aliasRepository;
		$this->aliasModel = $aliasModel;
		$this->aliasHistoryModel = $aliasHistoryModel;
		$this->aliasHistoryRepository = $aliasHistoryRepository;
	}


	protected function getterAlias(): ?Alias
	{
		if (!$this->isPersisted()) {
			return null;
		}

		if (!isset($this->cache['alias'])) {
			/** @var Orm $orm */
			$orm = $this->getRepository()->getModel();

			$aliasRepository = $orm->alias;
			$this->cache['alias'] = $aliasRepository->getBy([
				'referenceId' => $this->id,
				'module' => $this->module,
				'mutation' => $this->getMutation(),
			]);
		}

		return $this->cache['alias'];
	}


	public function setAlias(string $alias): void
	{
		$this->aliasModel->handleAliasChange($this, $this->getMutation(), $alias);
	}


	public function getAlias(): string
	{
		if ($this->alias instanceof Alias) {
			return $this->alias->__toString();
		} else {
			return '';
		}
	}


	public function getAliasEntity(): Alias|null
	{
		return $this->alias;
	}


	/**
	 * @return ICollection<AliasHistory>|null
	 */
	protected function getterAliasHistory(): ?ICollection
	{
		if (!$this->isPersisted()) {
			return null;
		}

		if (!isset($this->cache['aliasHistory'])) {

			/** @var Orm $orm */
			$orm = $this->getRepository()->getModel();

			$this->cache['aliasHistory'] = $orm->aliasHistory->findBy([
				'referenceId' => $this->id,
				'module' => $this->module,
				'mutation' => $this->getMutation(),
			])->orderBy('id', ICollection::DESC);
		}

		return $this->cache['aliasHistory'];
	}


	public function getAliasHistoryString(): string
	{
		$ret = [];
		if ($this->aliasHistory !== null) {
			foreach ($this->aliasHistory as $aliasHistory) {
				$ret[] = $aliasHistory->alias;
			}
		}

		return implode("\n", $ret);
	}


	public function setAliasHistoryString(string $aliasHistoryString): void
	{
		$this->aliasHistoryModel->saveString($aliasHistoryString, $this, $this->getMutation());
	}


	protected function getterModule(): string
	{
		return $this->aliasModel->mapEntityToModule($this);
	}


	abstract public function getMutation(): Mutation;

	abstract protected function getterPath(): array;


	public function onBeforeRemove(): void
	{
		$this->aliasModel->removeForEntity($this, $this->getMutation());
		$this->aliasHistoryModel->removeForEntity($this, $this->getMutation());
	}

	public function getNameTitle(): string
	{
		if (strlen(trim($this->nameTitle)) === 0) {
			return $this->name;
		}
		return $this->nameTitle;
	}

	public function setNameTitle(string $nameTitle): void
	{
		$this->nameTitle = $nameTitle;
	}

	public function getNameAnchor(): string
	{
		return $this->nameAnchor;
	}

	public function setNameAnchor(string $nameAnchor): void
	{
		$this->nameAnchor = $nameAnchor;
	}

	public function getDescription(): ?string
	{
		return $this->description;
	}

	public function setDescription(string $description): void
	{
		$this->description = $description;
	}

	public function getKeywords(): string
	{
		return $this->keywords;
	}

	public function setKeywords(string $keywords): void
	{
		$this->keywords = $keywords;
	}

	public function getPath(): array
	{
		return $this->path;
	}

}
