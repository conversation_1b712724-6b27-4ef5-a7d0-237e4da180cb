<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductVariantPrice;

use App\Model\Orm\Price;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\Traits\HasTemplateCache;
use Nextras\Orm\Entity\Entity;
use Nextras\Dbal\Utils\DateTimeImmutable;

/**
 * @property int $id {primary}
 * @property int|null $productId {default null} zatim neni treba relace
 * @property Price $price {embeddable}
 * @property float $vat {default 0}
 * @property DateTimeImmutable|null $lastImport {default null}
 *
 * @property DateTimeImmutable|null $validFrom {default null}
 * @property DateTimeImmutable|null $validTo {default null}
 * @property float $realOrigPriceDPH {default 0}
 *
 * RELATIONS
 * @property Mutation $mutation {m:1 Mutation::$productVariantPrices}
 * @property ProductVariant $productVariant {m:1 ProductVariant::$prices}
 * @property PriceLevel $priceLevel {m:1 PriceLevel::$prices}
 */
class ProductVariantPrice extends Entity
{
	use HasTemplateCache;
	public const PRICE_ROUND_PRECISION = 5;

	public function getTemplateCacheTagsCascade(): array
	{
		return [ProductVariant::class.'/'.$this->productVariant->id];
	}

}
