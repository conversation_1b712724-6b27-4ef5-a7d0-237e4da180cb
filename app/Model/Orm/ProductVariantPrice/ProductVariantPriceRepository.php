<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductVariantPrice;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\ProductVariant\ProductVariant;
use Nextras\Dbal\Result\Row;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method ProductVariantPrice getById($id)
 * @method ICollection<ProductVariantPrice> findAllInCurrency(string $currency)
 * @method ICollection<ProductVariantPrice> findAllWithDiscountPriceLevel(?string $currency = null)
 * @method Row[] getFutureActivePriceData(ProductVariant $productVariant, Mutation $mutation, PriceLevel $priceLevel, DateTimeImmutable $now)
 *
 * @extends Repository<ProductVariantPrice>
 */
final class ProductVariantPriceRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [ProductVariantPrice::class];
	}

}
