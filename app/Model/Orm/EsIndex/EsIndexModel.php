<?php declare(strict_types = 1);

namespace App\Model\Orm\EsIndex;

use App\Model\ElasticSearch\IndexModel;
use App\Model\Orm\Mutation\Mutation;
use LogicException;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Throwable;

class EsIndexModel
{

	public function __construct(
		private readonly array $indexesSetup,
		private readonly EsIndexRepository $repository,
		private readonly IndexModel $indexModel,
	)
	{
	}

	public function creteNewWithIndex(string $type, Mutation $mutation): EsIndex
	{
		$newEsIndex = $this->createNew($type, $mutation);
		$this->indexModel->createIndex($newEsIndex);

		return $newEsIndex;
	}

	public function creteNewIndexWithOldDataCopy(string $type, Mutation $mutation): ?EsIndex
	{
		$oldIndex = $this->repository->getLastActive($type, $mutation);
		if ($oldIndex === null) {
			return null;
		}
		$newEsIndex = $this->creteNewWithIndex($type, $mutation);
		$newFilledEsIndex = $this->indexModel->copyDataToNewIndex($oldIndex, $newEsIndex);

		if ($newFilledEsIndex === null) {
			return null;
		}

		$this->switchIndex($newFilledEsIndex);

		return $newFilledEsIndex;
	}


	public function createNew(string $type, Mutation $mutation): EsIndex
	{
		$esIndex = new EsIndex();
		$this->repository->attach($esIndex);

		$esIndex->mutation = $mutation;
		$esIndex->type = $type;
		$esIndex->errorCount = 0;
		$esIndex->errorDetail = new ArrayHash();
		$now = new DateTimeImmutable();
		// reset microseconds
		$now = $now->setTime((int) $now->format('H'), (int) $now->format('i'), (int) $now->format('s'), 0);
		$esIndex->createdTime = $now;
		$esIndex->name = $this->getName($type, $mutation);

		$this->repository->persistAndFlush($esIndex);

		return $esIndex;
	}


	public function deleteIndex(EsIndex $esIndex): void
	{
		if ($this->indexModel->deleteByName($esIndex->esName)) {
			$this->repository->removeAndFlush($esIndex);
		}
	}


	private function getName(string $type, Mutation $mutation): string
	{
		if (!isset($this->indexesSetup[$type]['name'])) {
			throw new LogicException(sprintf('Missing type \'%s\' for elastic search alias in elasticSearch.neon', $type));
		}

		return $this->indexesSetup[$type]['name'] . '_' . $mutation->langCode;
	}


	public function markAsStarted(EsIndex $esIndex): void
	{
		$esIndex->startTime = new DateTimeImmutable();
		$esIndex->finishTime = null;
		$esIndex->errorCount = 0;
		$esIndex->errorDetail = new ArrayHash();

		$this->repository->persistAndFlush($esIndex);
	}


	public function markAsFinished(EsIndex $esIndex): void
	{
		$esIndex->finishTime = new DateTimeImmutable();
		$this->repository->persistAndFlush($esIndex);
	}

	private function markAsLastActive(EsIndex $esIndex): void
	{
		$esIndex->active = 1;
		$this->repository->persist($esIndex);

		$oldEsIndexes = $this->repository->findBy([
			'id!=' => $esIndex->id,
			'mutation' => $esIndex->mutation,
			'type' => $esIndex->type,

		]);

		foreach ($oldEsIndexes as $oldSsIndex) {
			$oldSsIndex->active = 0;
			$this->repository->persist($oldSsIndex);
		}

		$this->repository->flush();
	}


	public function markToRecreate(EsIndex $esIndex): void
	{
		$esIndex->recreate = 1;
		$this->repository->persistAndFlush($esIndex);
	}


	public function addErrorStatus(EsIndex $esIndex, Throwable $exception): void
	{
		$esIndex->status .= ' ' . $exception->getMessage();
		$this->repository->persist($esIndex);
	}

	public function switchIndex(EsIndex $esIndex): void
	{
		$this->markAsLastActive($esIndex);
		$this->indexModel->switchMainAliases($esIndex);
	}

}
