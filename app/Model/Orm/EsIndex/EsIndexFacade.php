<?php declare(strict_types = 1);

namespace App\Model\Orm\EsIndex;

use App\Model\ElasticSearch;
use App\Model\ElasticSearch\IndexModel;
use LogicException;

final class EsIndexFacade
{

	public function __construct(
		private readonly IndexModel $indexModel,
		private readonly EsIndexRepository $esIndexRepository,
		private readonly ElasticSearch\All\Facade $allElasticFacade,
		private readonly ElasticSearch\Common\Facade $commonElasticFacade,
		private readonly ElasticSearch\Product\Facade $productElasticFacade,
	)
	{
	}


	public function fill(EsIndex $esIndex, ?int $limit = null, bool $autoSwitch = false): void
	{
		match ($esIndex->type) {
			EsIndex::TYPE_ALL => $this->allElasticFacade->fill($esIndex, $limit, $autoSwitch),
			EsIndex::TYPE_COMMON =>  $this->commonElasticFacade->fill($esIndex, $limit, $autoSwitch),
			EsIndex::TYPE_PRODUCT =>  $this->productElasticFacade->fill($esIndex, $limit, autoSwitch: $autoSwitch),
			default => throw new LogicException('Missing definition for index'),
		};
	}



	public function delete(EsIndex $esIndex): void
	{
		$this->indexModel->getIndex($esIndex)->delete();
		$this->esIndexRepository->removeAndFlush($esIndex);
	}

}
