<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductReview;

use App\Model\CustomField\CustomFields;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Orm;
use App\Model\Orm\Product\Product;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserModel;
use App\Model\StringHelper;
use App\PostType\ProductReview\AdminModule\Components\ProductReviewForm\ProductReviewFormData;
use Nette\Utils\ArrayHash;
use Nette\Utils\Json;
use Nette\Utils\Strings;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\IEntity;

final class ProductReviewModel
{

	public function __construct(
		private readonly Orm $orm,
		private readonly CustomFields $customFields,
		private readonly UserModel $userModel,
	) {}

	public function editReview(ProductReview $productReview, ProductReviewFormData $productReviewFormData): void
	{
		$productReview->text = $productReviewFormData->text;
		$productReview->name = $productReviewFormData->name;
		$productReview->email = $productReviewFormData->email;
		$productReview->stars = $productReviewFormData->stars;
		$productReview->status = $productReviewFormData->status;
		$productReview->source = $productReviewFormData->source;
		$productReview->public = $productReviewFormData->public;

		if (isset($productReview->cf)) {
			if (isset($productReviewFormData->setup->cf) && $productReviewFormData->setup->cf !== '') {
				$productReview->setCf($this->customFields->prepareDataToSave($productReviewFormData->setup->cf));
			} else {
				$productReview->setCf(new ArrayHash());
			}
		}

		$this->orm->persistAndFlush($productReview);
		$this->recalculateCache($productReview->product);
	}

	public function updateReview(ProductReview $productReview, ?int $stars, ?string $text): void
	{
		$this->orm->productReview->persistAndFlush($productReview);
		$productReview->stars = $stars;
		$productReview->text = $text;
		$this->recalculateCache($productReview->product);
	}

	public function addReview(Product $product, ?string $name, ?DateTimeImmutable $date, ?int $stars, ?string $text, ?string $email, int $isWebMaster, ?int $userId, bool $isMain = false): void
	{
		$productReview = new ProductReview();
		$productReview->product = $product;
		$productReview->name = $name;
		$productReview->email = $email;
		$productReview->stars = $stars;
		$productReview->date = $date;
		$productReview->text = $text;
		$productReview->isWebMaster = $isWebMaster;
		$productReview->isMain = (int) $isMain;

		if ($userId) {
			$productReview->userId = $userId;
		}

		$this->orm->productReview->persistAndFlush($productReview);
		$this->recalculateCache($product);

	}


	private function recalculateCache(Product $product): void
	{
		$mapper = $this->orm->productReview->getMapper();
		\assert($mapper instanceof ProductReviewMapper);
		$statistic = $mapper->getStatistic($product)->fetch();

		bd($statistic);

		$product->reviewAverage = $statistic->average ?? 0.0;
		$this->orm->product->persistAndFlush($product);
	}


	public function getReviewedProductByUser(Product $product, User $user, bool $onlyApproved = false): ProductReview|null
	{
		$conds = [
			'user' => $user,
			'product' => $product,
		];

		if ($onlyApproved) {
			$conds['status'] = ProductReview::STATUS_APPROVED;
		}

		return $this->orm->productReview->getBy($conds);
	}

	public static function buildCustomFields(?array $pros, ?array $cons): ArrayHash
	{
		$review = [
			'reviewRatingPositive' => array_map(fn($item) => ['review' => $item], $pros ?: []),
			'reviewRatingNegative' => array_map(fn($item) => ['review' => $item], $cons ?: []),
		];

		// Format as CF needs
		$content = ArrayHash::from($review);

		return ArrayHash::from([
			'content' => [$content],
		], false);
	}


	public function like(ProductReview $productReview): ProductReview
	{
		$productReview->likes++;

		$this->orm->productReview->persistAndFlush($productReview);

		return $productReview;
	}

	public function dislike(ProductReview $productReview): ProductReview
	{
		$productReview->dislikes++;

		$this->orm->productReview->persistAndFlush($productReview);

		return $productReview;
	}

	public function add(Product $product, Mutation $mutation, ArrayHash $data, ArrayHash $cf, ?User $user = null, ProductReview|null $productReview = null, Order|null $order = null): ProductReview|IEntity
	{
		if ($productReview === null){
			$productReview = new ProductReview();
		}

		if (!$user && isset($data['email'])){
			$user = $this->userModel->getByEmail($data['email'], $mutation);
		}
		$productReview->user = $user;
		$productReview->product = $product;
		$productReview->text = $data->text;
		$productReview->public = $data->public;
		$productReview->order = $order;

		$productReview->status = ProductReview::STATUS_UNAPPROVED;
		$productReview->created = $data->created ?? new DateTimeImmutable();
		$productReview->customFieldsJson = $cf;

		$productReview->name = $data->name ?? '';
		$productReview->email = $data->email ?? '';
		$productReview->isSimple = isset($data->isSimple) && $data->isSimple;
		$productReview->isFromHeureka = false;
		$productReview->source = $data->source ?? null;

		if (isset($cf->content)) {
			$productReview->setCf($this->customFields->prepareDataToSave(Json::encode($cf)));
		} else {
			$productReview->setCf(new ArrayHash());
		}

		$stars = 1;
		if (isset($data->stars)) {
			$stars = (int)$data->stars;
			if ($stars > 5) {
				$stars = 5;
			} elseif ($stars < 1) {
				$stars = 1;
			}
		}
		$productReview->stars = $stars;

		$this->orm->productReview->persistAndFlush($productReview);

		$this->recalculateCache($productReview->product);

		return $productReview;
	}
}
