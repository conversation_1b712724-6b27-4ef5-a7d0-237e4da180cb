<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductReview;

use App\Model\Orm\Product\Product;
use App\Model\Security\User;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method ProductReview|null getById($id)
 * @method Result getStatistic($id)
 * @method array getProductForReviewFormOrder($id)
 * @method array getUnreviewProducts(array $productsId, User $user)
 *
 * @extends Repository<ProductReview>
 */
final class ProductReviewRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [ProductReview::class];
	}

	public function getMainReview(Product $product, int $userId): ?ProductReview
	{
		return $this->getBy(['product' => $product->id, 'userId' => $userId]);
	}

}
