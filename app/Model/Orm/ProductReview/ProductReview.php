<?php

declare(strict_types = 1);

namespace App\Model\Orm\ProductReview;

use App\Model\Orm\Order\Order;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Traits\HasConsts;
use App\Model\Orm\Traits\HasCustomFields;
use App\Model\Orm\User\User;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;
use App\Model\Orm\JsonContainer; // phpcs:ignore

// phpcs:ignore

/**
 * @property int                    $id                       {primary}
 * @property Product                $product                  {m:1 Product::$reviews}
 * @property Order|null            	$order                    {m:1 Order::$reviews}
 * @property User|null              $user                     {m:1 User::$reviews}
 * @property int                    $isWebMaster              {default 0}
 * @property string|null            $importCode               {default null}
 * @property int                    $isMain                   {default 0}
 * @property bool                   $isSimple                 {default 0}
 * @property string|null            $name
 * @property string                 $status                   {enum self::STATUS_*}{default self::STATUS_APPROVED}
 * @property string|null            $source                   {enum self::SOURCE_*}
 * @property bool                   $isFromHeureka            {default false}
 * @property bool                   $public            		  {default false}
 * @property string|null            $email
 * @property string|null            $text
 * @property int|null               $stars
 * @property int                    $likes                    {default 0}
 * @property int                    $dislikes                 {default 0}
 * @property DateTimeImmutable|null $date                     {default 'now'}
 * @property DateTimeImmutable      $created                  {default 'now'}
 * @property DateTimeImmutable|null $syncTime                 {default null}
 * @property string|null            $syncChecksum             {default null}
 *
 * VIRTUAL
 * @property ArrayHash|null         $cf                       {virtual}
 * @property ArrayHash              $customFieldsJson         {container JsonContainer}
 * @property int|null               $userId                   {virtual}
 * @property string|null            $dateString               {virtual}
 */
class ProductReview extends Entity
{

	use HasConsts;
	use HasCustomFields;

	public const STATUS_UNAPPROVED = 'unapproved';

	public const STATUS_APPROVED = 'approved';

	public const STATUS_REJECTED = 'rejected';

	public const SOURCE_HEUREKA = 'heu';

	public const SOURCE_PRODUCT_DETAIL = 'prd';
	public const SOURCE_USER_SECTION = 'usr';

	protected function getterDateString(): ?string
	{
		return $this->date?->format("Y-m-d H:i:s");
	}

	protected function getterUserId(): ?int
	{
		return $this->user?->id;
	}

}
