<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductImage;

use App\Model\Orm\Traits\HasSimpleSave;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Repository\Repository;

/**
 * @method ProductImage|null getById($id)
 * @method Result setVariants($id, $variants)
 * @method ProductImage save(ProductImage|null $entity, array $data)
 * @method void removeById(int|array $id)
 *
 * @extends Repository<ProductImage>
 */
final class ProductImageRepository extends Repository
{

	use HasSimpleSave;

	public static function getEntityClassNames(): array
	{
		return [ProductImage::class];
	}

}
