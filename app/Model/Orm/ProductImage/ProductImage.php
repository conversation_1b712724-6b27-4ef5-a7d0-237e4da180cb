<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductImage;

use App\Model\Image\ImageObject;
use App\Model\Orm\ImageEntity;
use App\Model\Orm\JsonContainer; // phpcs:ignore
use App\Model\Orm\LibraryImage\LibraryImage;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Traits\HasCache;
use App\Model\Orm\Traits\HasImageResizer;
use App\Model\Orm\Traits\HasTemplateCache;
use Nette\Utils\ArrayHash;
use Nette\Utils\Strings;

/**
 * @property int $id {primary}
 * @property int|null $sort
 * @property string|null $variants
 * @property ArrayHash $data {container JsonContainer}
 * @property string|null $extId
 *
 * RELATIONS
 * @property Product $product {m:1 Product::$images}
 * @property LibraryImage $libraryImage {m:1 LibraryImage::$productImages}
 *
 *
 * VIRTUALS
 * @property-read string $filename {virtual}
 * @property-read string $name {virtual}
 * @property-read string $url {virtual}
 * @property-read bool $isSvg {virtual}
 */
class ProductImage extends ImageEntity
{

	use HasCache, HasTemplateCache {
		HasCache::onAfterPersist as hasCacheOnAfterPersist;
		HasTemplateCache::onAfterPersist as hasTemplateCacheOnAfterPersist;
	}
	use HasImageResizer;

	public function getterFilename(): string
	{
		return Strings::substring($this->url, (Strings::indexOf($this->url, '/', -1) ?? -1) + 1);
	}


	public function getVariantIds(): array
	{
		return explode('|', $this->variants ?? '');
	}

	public function setVariantIds(array $variantIds = []): void
	{
		$this->variants = implode('|', $variantIds);
	}

	protected function getterIsSvg(): bool
	{
		if (!isset($this->cache['isSvg'])) {
			$fileInfo = pathinfo($this->url);
			$this->cache['isSvg'] = (isset($fileInfo['extension']) && $fileInfo['extension'] === 'svg');
		}

		return $this->cache['isSvg'];
	}


	public function getAlt(Mutation $mutation): string
	{
		$langCode = $mutation->langCode;
		$alt = $this->data->$langCode->name ?? '';

		if (empty($alt)) {
			$alt = $this->product->getLocalization($mutation)->getImageAlt();
		}

		return (string)$alt;
	}


	public function getSize(string $size): ImageObject
	{
		return $this->imageObjectFactory->getByName($this->filename, $size, $this->getTimestamp());
	}

	public function getTimestamp(): ?int
	{
		return $this->libraryImage->getTimestamp();
	}

	protected function getterName(): string
	{
		return $this->libraryImage->name;
	}
	protected function getterUrl(): string
	{
		return $this->libraryImage->url;
	}

	public function getTemplateCacheTagsCascade(): array
	{
		return [Product::class.'/'.$this->product->id];
	}

	public function onAfterPersist(): void
	{
		$this->hasCacheOnAfterPersist();
		$this->hasTemplateCacheOnAfterPersist();
	}
}
