<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductImage;

use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends <PERSON>balMapper<ProductImage>
 */
final class ProductImageMapper extends Dbal<PERSON>apper
{

	use HasCamelCase;

	/**
* @return literal-string
*/
	public function getTableName(): string
	{
		return 'product_image';
	}

	public function setVariants(int $id, array $variants): Result
	{
		$sVariants = implode('|', $variants);
		return $this->connection->query(
			'UPDATE product_image SET variants=%s WHERE id = %i',
			$sVariants,
			$id
		);
	}

	public function removeById(int|array $id): void
	{
		if (!is_array($id)) {
			$id = [$id];
		}

		$this->connection->query('DELETE FROM product_image WHERE id IN %i[]', $id);
	}


}
