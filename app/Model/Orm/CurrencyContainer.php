<?php declare(strict_types = 1);

namespace App\Model\Orm;

use Brick\Money\Currency;
use Nextras\Orm\Entity\ImmutableValuePropertyWrapper;
use function assert;
use function is_string;

final class CurrencyContainer extends ImmutableValuePropertyWrapper
{

	public function convertToRawValue($value): string
	{
		assert($value instanceof Currency);
		return $value->getCurrencyCode();
	}

	public function convertFromRawValue($value): Currency
	{
		assert(is_string($value));
		return Currency::of($value);
	}

}
