<?php
declare(strict_types = 1);

namespace App\Model\Orm\Review;

use App\Model\Orm\LibraryImage\LibraryImage;
use App\Model\Orm\Product\Product;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\ManyHasMany;


/**
 * @property int $id                      {primary}
 * @property int|null $extId                {default null}
 * @property string $name
 * @property string $perex
 * @property string $url
 * @property DateTimeImmutable $date    {default now}
 * @property DateTimeImmutable $updated
 * @property string $author
 *
 * RELATIONS
 * @property null|LibraryImage $libraryImage {m:1 LibraryImage, oneSided=true}
 * @property ManyHasMany<Product> $products {m:m Product::$magazineReviews, isMain=true, cascade=[persist, remove]}
 *
 * VIRTUAL
 * @property Product|null $firstProduct {virtual}
 */
class Review extends Entity
{

	public function getterFirstProduct(): ?Product
	{
		return $this->products->toCollection()->limitBy(1)->fetch();
	}
}
