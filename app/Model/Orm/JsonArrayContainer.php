<?php declare(strict_types = 1);

namespace App\Model\Orm;

use Nette\Utils\Json;
use Nextras\Orm\Entity\ImmutableValuePropertyWrapper;

final class JsonArrayContainer extends ImmutableValuePropertyWrapper
{
	public function setInjectedValue($value): bool
	{
		// array and object normalization
		$this->value = Json::decode(Json::encode($value), forceArrays: true);
		return true;
	}


	public function convertFromRawValue($value = null)
	{
		if (is_null($value) || !$value) {
			return Json::decode('{}', forceArrays: true);
		} else {
			return Json::decode($value, forceArrays: true);
		}
	}


	public function convertToRawValue($value)
	{
		return Json::encode($value);
	}
}
