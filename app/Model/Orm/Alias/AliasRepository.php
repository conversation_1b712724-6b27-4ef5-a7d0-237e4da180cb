<?php declare(strict_types = 1);

namespace App\Model\Orm\Alias;

use App\Model\Orm\CollectionById;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method Alias getById($id)
 * @method ICollection<Alias> findByName($string)
 * @method ICollection<Alias> findByExactOrder(array $ids)
 *
 * @extends Repository<Alias>
 */
final class AliasRepository extends Repository implements CollectionById
{

	public static function getEntityClassNames(): array
	{
		return [Alias::class];
	}

	/**
	 * @return ICollection<Alias>
	 */
	public function findByIdOrder(array $ids): ICollection
	{
		return $this->findByExactOrder($ids);
	}

}
