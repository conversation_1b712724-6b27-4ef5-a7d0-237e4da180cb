<?php declare(strict_types = 1);

namespace App\Model\Orm\Alias;

use App\Model\CacheFactory;
use App\Model\Orm\AliasHistory\AliasHistory;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Routable;
use App\Model\Orm\RoutableEntity;
use App\PostType\Page\Model\Orm\CommonTree;
use Nette\Caching\Cache;
use Nette\Utils\Strings;

final class AliasModel
{

	private readonly Cache $cache;

	public function __construct(
		private readonly Orm $orm,
		private readonly string $adminAlias,
		CacheFactory $cacheFactory,
	)
	{
		$this->cache = $cacheFactory->create(self::class);
	}


	public function handleAliasChange(RoutableEntity $entity, Mutation $mutation, ?string $newAliasString = null, bool $save = true): string
	{
		if (isset($entity->alias) && $entity->alias->alias === $this->sanitizeAlias($newAliasString)) {
			return $newAliasString; // nedoslo k aktualizaci
		}

		if (!$newAliasString) {
			$newAliasString = isset($entity->level) && $entity->level === 0 ? '' : $entity->nameAnchor; // '' = Homepage
		}

		$newAliasString = $this->generateAlias($newAliasString, $mutation, $entity);

		if (!$save) {
			return $newAliasString;
		}

		if ($newAliasString || $entity instanceof CommonTree && $entity->uid === 'title') {

			$oldAlias = $this->orm->alias->getBy([
				'referenceId' => $entity->id,
				'module' => $entity->module,
				'mutation' => $mutation,
			]);

			if ($oldAlias) { // updating old alias
				if ($newAliasString !== $oldAlias->alias) { // move to history $oldAlias->alias
					$historyAlias = $this->orm->aliasHistory->getBy([
						'mutation' => $mutation,
						'alias' => $oldAlias->alias,
					]);

					// jestlize tento historicky alias uz exituje nevkladej ho
					if (!$historyAlias) {
						$newAliasHistory = new AliasHistory();
						$newAliasHistory->referenceId = $entity->id;
						$newAliasHistory->module = $entity->module;
						$newAliasHistory->mutation = $mutation;
						$newAliasHistory->alias = $oldAlias->alias;
						$this->orm->aliasHistory->persistAndFlush($newAliasHistory);
					}

					$oldAlias->alias = $newAliasString;
					$this->orm->alias->persistAndFlush($oldAlias);
				}
			} else { // create new alias
				$newAlias = new Alias();
				$newAlias->referenceId = $entity->id;
				$newAlias->mutation = $mutation;
				$newAlias->alias = $newAliasString;
				$newAlias->module = $entity->module;
				$this->orm->alias->persistAndFlush($newAlias);
			}
		}

		return $newAliasString;
	}


	public function generateAlias(string $name, Mutation $mutation, ?Routable $reference = null): string
	{
		$aliasCandidate = '';
		$sanitizedAlias = $this->sanitizeAlias($name);

		if ($sanitizedAlias !== '') { // kontrola aliasu v tabulce
			$suffix = 0;
			$aliasCandidate = $sanitizedAlias;

			while ( ! $this->isAliasAvailable($aliasCandidate, $mutation, $reference)) {
				$suffix++;
				$aliasCandidate = $sanitizedAlias . '-' . $suffix;
			}

			$this->invalidateCache($mutation, $reference);
		}

		return $aliasCandidate;
	}


	public function removeForEntity(RoutableEntity $entity, Mutation $mutation): void
	{
		$alias = $this->orm->alias->getBy([
			'referenceId' => $entity->id,
			'module' => $entity->module,
			'mutation' => $mutation,
		]);

		if ($alias) {
			$this->orm->alias->remove($alias);
			$this->invalidateCache($mutation, $entity);
		}
	}

	/**
	 * @param RoutableEntity $entity
	 * @return string
	 */
	public function mapEntityToModule(RoutableEntity $entity): string
	{
		$classNameParts = explode('\\', get_class($entity));

		$className = $classNameParts[count($classNameParts) - 1];
		if (in_array($className, ['HpTree', 'CommonTree', 'CatalogTree'])) {
			$className = 'tree';
		}

		return Strings::firstLower($className);
	}


	public function getStringAlias(Mutation $mutation, string $module, int $referenceId): string|null
	{
		return $this->cache->load('string-alias-' . $mutation->langCode . '-' . $module . '-' . $referenceId, function (&$dependencies) use ($mutation, $module, $referenceId) {
			$dependencies[Cache::Expire] = '1 day';
			$dependencies[Cache::Tags] = [$mutation->langCode, $mutation->langCode . '/' . $module . '/' . $referenceId];
			return $this->getAlias($mutation, $module, $referenceId)?->alias ?? null;
		});
	}


	public function getAlias(Mutation $mutation, string $module, int $referenceId): Alias|null
	{
		return $this->orm->alias->getBy([
			'mutation' => $mutation,
			'module' => $module,
			'referenceId' => $referenceId,
		]);
	}


	private function sanitizeAlias(string $name): string
	{
		$alias = Strings::webalize($name);
		$alias = substr($alias, 0, 115); // column lenght 120
		return $alias;
	}


	private function isAliasAvailable(string $alias, Mutation $aliasMutation, ?Routable $reference): bool
	{
		if ($alias === $this->adminAlias) {
			return false;
		}

		foreach ($this->orm->mutation->findAll() as $mutation) {
			if ($mutation->getRealUrlPrefix() === $alias) {
				return false;
			}
		}

		$conds = ['alias' => $alias, 'mutation' => $aliasMutation];

		$currentAlias = $reference?->getAliasEntity();
		if ($currentAlias !== null) { // jedna se o update ignoruj puvodni hodnotu
			$conds['id!='] = $currentAlias->id;
		}

		$existingAlias = $this->orm->alias->getBy($conds);
		return $existingAlias === null;
	}

	private function invalidateCache(Mutation $mutation, ?Routable $routable = null): void
	{
		if ($routable instanceof RoutableEntity) {
			$this->cache->clean([
				Cache::Tags => [
					$mutation->langCode . '/' . $routable->module . '/' . $routable->getId(),
				]
			]);
		} else {
			$this->cache->clean([
				Cache::Tags => [
					$mutation->langCode,
				]
			]);
		}
	}

}
