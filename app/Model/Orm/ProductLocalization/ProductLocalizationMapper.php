<?php declare(strict_types = 1);

namespace App\Model\Orm\ProductLocalization;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Order\OrderState;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Traits\HasCamelCase;
use Nette\Utils\DateTime;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<ProductLocalization>
 */
final class ProductLocalizationMapper extends DbalMapper
{

	use HasCamelCase;

	/**
* @return literal-string
*/
public function getTableName(): string
{
	return 'product_localization';
}


	public function findAllIdsInMutation(Mutation $mutation, ?int $limit = null): Result
	{
		$builder = $this->builder()->select('pv.id')
			->from($this->getTableName(), 'pv')
			->andWhere('pv.mutationId = %i', $mutation->id)
			->limitBy($limit);
		return $this->connection->queryByQueryBuilder($builder);
	}

	public function getPurchaseCount(ProductLocalization $productLocalization, int $daysToPast = 31): int
	{
		$dateInPast = DateTime::from('now')->modify('-' . $daysToPast . 'days');

		$builder = $this->builder()
			->select('pl.id')
			->addSelect('SUM(op.amount) as productPurchaseCount')
			->addSelect('SUM(oc.amount) as classPurchaseCount')
			->from($this->getTableName(), 'pl')
			->joinInner('[product] as [p]', '[pl.productId] = [p.id]')
			->joinLeft('[product_variant] as [pv]', '[pv.productId] = [p.id]')
			->joinLeft('[order_product] as [op]', '[op.variantId] = [pv.id]')
			->joinLeft('[order_class] as [oc]', '[oc.productId] = [p.id]')
			->joinLeft('[order] as [opr]', '[op.orderId] = [opr.id]')
			->joinLeft('[order] as [ocl]', '[oc.orderId] = [ocl.id]')
			->where('pl.id = %i', $productLocalization->getPersistedId())
			->groupBy('pl.id')
			->andWhere('
				([opr.placedAt] is not null AND [opr.state] is not null AND [opr.placedAt] >= %dt AND [opr.state] NOT IN %s[]) OR
				([ocl.placedAt] is not null AND [ocl.state] is not null AND [ocl.placedAt] >= %dt AND [ocl.state] NOT IN %s[])',
				$dateInPast, [OrderState::Draft, OrderState::Declined, OrderState::Canceled], $dateInPast, [OrderState::Draft, OrderState::Declined, OrderState::Canceled]);

		$query = $this->connection->queryByQueryBuilder($builder)->fetch();
		return (int) ($query?->productPurchaseCount ?? 0) + (int) ($query?->classPurchaseCount ?? 0);
	}

}
