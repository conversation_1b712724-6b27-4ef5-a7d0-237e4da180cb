<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductLocalization;

use App\Model\CacheStorageService;
use App\Model\DTO\Product\ProductDto;
use App\Model\DTO\Product\ProductDtoProvider;
use App\Model\Orm\JsonArrayHashContainer; // phpcs:ignore
use App\Model\Orm\JsonContainer; // phpcs:ignore
use App\Model\Orm\JsonArrayContainer; // phpcs:ignore
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductFile\ProductFile;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\ProductVariant\ProductVariantRepository;
use App\Model\Orm\RoutableEntity;
use App\Model\Orm\State\State;
use App\Model\Orm\Traits\HasCustomContent;
use App\Model\Orm\Traits\HasCustomFields;
use Nette\Security\User as UserSecurity;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Page\Model\Orm\TreeRepository;
use App\Model\Orm\TreeProduct\TreeProduct;
use App\PostType\Core\Model\LocalizationEntity;
use Brick\Money\Currency;
use Nette\Caching\Cache;
use Nette\Utils\ArrayHash;
use Nette\Utils\Strings;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property int $id {primary}
 * @property int $public {default 0}
 * @property int|null $requalificationPossibility {default null}
 * @property string|null $name
 * @property string|null $nameTitle
 * @property string|null $nameAnchor
 * @property string|null $description
 * @property string|null $keywords
 * @property float $score {default 0}
 * @property string|null $annotation
 * @property string|null $content
 * @property ArrayHash $setup {container JsonArrayContainer}
 * @property ArrayHash $customFieldsJson {container JsonArrayHashContainer}
 * @property ArrayHash $customContentJson {container JsonArrayHashContainer}
 * @property int|null $soldCount {default 0}
 *
 * RELATIONS
 * @property Product $product {m:1 Product::$productLocalizations}
 * @property Mutation $mutation {m:1 Mutation::$productLocalizations}
 * @property OneHasMany<ProductFile> $files {1:m ProductFile::$productLocalization, orderBy=[sort=ASC], cascade=[persist, remove]}
 *
 *
 * VIRTUAL
 * @property-read string $template {virtual}
 * @property-read ICollection<Tree> $pages {virtual}
 * @property-read ICollection<Tree> $pagesAll {virtual}
 * @property ArrayHash|null $cc {virtual}
 * @property ArrayHash|null $cf {virtual}
 */
class ProductLocalization extends RoutableEntity implements LocalizationEntity
{

	use HasCustomFields;
	use HasCustomContent;

	private TreeRepository $treeRepository;

	private ProductVariantRepository $productVariantRepository;
	private CacheStorageService $cacheStorageService;
	private ProductDtoProvider $productDtoProvider;

	public function injectServices(
		TreeRepository $treeRepository,
		ProductVariantRepository $productVariantRepository,
		CacheStorageService $cacheStorageService,
		ProductDtoProvider $productDtoProvider,
	): void
	{
		$this->treeRepository = $treeRepository;
		$this->productVariantRepository = $productVariantRepository;
		$this->cacheStorageService = $cacheStorageService;
		$this->productDtoProvider = $productDtoProvider;
	}


	public function getId(): int
	{
		return $this->id;
	}


	public function getMutation(): Mutation
	{
		return $this->mutation;
	}


	public function setMutation(Mutation $mutation): void
	{
		$this->mutation = $mutation;
	}


	protected function getterTemplate(): string
	{
		return $this->product->template;
	}


	/**
	 * @return ICollection<Tree>
	 */
	public function getterPages(): ICollection
	{
		return $this->pagesAll;
	}

	/**
	 * @return ICollection<Tree>
	 */
	public function getterPagesAll(): ICollection
	{
		return $this->treeRepository->findTreesInTreeProductRelations($this->product, TreeProduct::TYPE_NORMAL_TO_PRODUCT, $this->mutation);
	}

	protected function getRoutableMutation(): Mutation
	{
		return $this->mutation;
	}


	protected function getterPath(): array
	{
		$path = $this->product->path;
		if ($path !== null) {
			return $path;
		} else {
			return [];
		}
	}


	public function getFirstActiveVariantByMutation(Mutation $mutation): ?ProductVariant
	{
		return $this->productVariantRepository->getBy([
			'product' => $this->product,
//			'this->variantLocalizations->active' => 1,
			'this->variantLocalizations->mutation' => $mutation,
		]);
	}


	public function getActiveVariantByMutation(Mutation $mutation, int $variantId): ?ProductVariant
	{
		return $this->productVariantRepository->getBy([
			'product' => $this->product,
			'id' => $variantId,
			'this->variantLocalizations->active' => 1,
			'this->variantLocalizations->mutation' => $mutation,
		]);
	}

	public function getParent(): Product
	{
		return $this->product;
	}



	public function setParent(ParentEntity $parentEntity): void
	{
		assert($parentEntity instanceof Product);
		$this->product = $parentEntity;
	}


	public function getName(): string
	{
		return $this->name;
	}

	public function setName(string $name): void
	{
		$this->name = $name;
	}

	public function getGTMData(ProductVariant $variant, Currency $currency, int $index = 0, int $quantity = 1, ?string $itemListId = null, ?string $itemListName = null ): array
	{
		$productDto = $this->productDtoProvider->get($this->product, $variant);
		$data = [
			'index' => $index,
			'item_id' => (string) $this->product->id,
			'item_name' => $this->getName(),
			'price' => sprintf('%0.2f',$productDto->price->getAmount()->toFloat()),
			'currency' => $currency->getCurrencyCode(),
			'quantity' => $quantity,
		];

		if ($itemListId !== null) {
			$data['item_list_id'] = $itemListId;
		}

		if ($itemListName !== null) {
			$data['item_list_name'] = $itemListName;
		}

		$generator = function () {
			$data = [];
			if ($this->product->mainCategory !== null) {
				$i = 1;
				foreach (array_merge($this->product->mainCategory->pathItems, [$this->product->mainCategory]) as $category) {
					if ($category instanceof CatalogTree && $category->level > 1) {
						$data['item_category' . ($i > 1 ? $i : '')] = $category->getName();
						$i++;
					}
				}
			}
			return $data;
		};
		$options = [
			Cache::Tags => $this->product->getTemplateCacheTags(),
			Cache::Expire => '24 hour'
		];

		$prePrepared = $this->cacheStorageService->getStoredData('productVariantGTM', 'P-' . $this->id, $generator, $options);
		$data = array_merge($data, $prePrepared);

		// TODO promotion_name, promotion_id, coupon

		return $data;
	}

	public function getBloomreachData(ProductVariant $variant, Mutation $mutation, PriceLevel $priceLevel, State $state, ?UserSecurity $user = null): array
	{

		$productDto = $this->productDtoProvider->get($this->product, $variant);

		$productLocalization = $this;
		$product = $productLocalization->product;

		$discount = $productDto->priceInfoDiscountAmount;

		return [
			'product_id' => $product->id,
			'merchant_id' => null, // TODO: what is this ?
			'title' => $productLocalization->getName(),
			'product_type' => $product->typeName,
			'product_department' => $product->getCategoryByLevel(1)?->name,
			'product_category' => $product->mainCategory?->name,
			'EAN' => $product->firstVariant->ean,
			'inventory' => $productDto->productAvailabilityStateText,
			'visitor_login_state' => $user->isLoggedIn() ? 'Logged' : 'Anonymous',
			'product_language' => $product->getLanguage(),
			'item_on_sale' => $discount !== null,
			'price' => $productDto->priceVat->getAmount()->toFloat(),
			'discount' => $discount?->getAmount()->toFloat() ?? 0,
		];
	}

	public function getTemplateCacheExpire(): int|string|null
	{
		return $this->product->getTemplateCacheExpire();
	}

	public function getTemplateCacheTags(): array
	{
		return $this->product->getTemplateCacheTags();
	}


	public function getNameTitle(): string
	{
		if (strlen(trim($this->nameTitle)) === 0) {
			return $this->generateAutoTitle();
		}
		return $this->nameTitle;
	}

	private function generateAutoTitle(): string
	{
		$bookTitleCallback = function () {
			$part = [];
			$part[] = $this->name;
			if (($pv = $this->product->getParameterValueByUid(Parameter::UID_BIND)) instanceof ParameterValue) {
				$part[] = '(' . $pv->internalValue . ')';
			}

			if ($this->product->mainCategory !== null) {
				$part[] = '- ' . ($this->product->mainCategory->getParentByLevel(2)?->name ?? $this->product->mainCategory->name);
			}

			return implode(' ', $part);
		};

		return $this->name;
	}

	public function getDescription(): ?string
	{
		if (!empty($this->description)) {
			return $this->description;
		}

		$content = strip_tags($this->annotation ?? $this->content ?? '');
		$content = preg_replace('/\s\s+/', ' ', $content);
		$content = html_entity_decode($content, ENT_QUOTES | ENT_HTML5, 'UTF-8');
		$content = trim($content);
		$content = str_replace(['„', '"'], ['',''], $content);
		$content = preg_replace('/\r\n|\r|\n/', ' ', $content);

		if (strlen($content) === 0) {
			return null;
		}

		return Strings::truncate($content, 160);
	}

	public function getImageAlt(): string
	{
		return $this->name;
	}


	public function hasRequalificationPossibility(State $state): bool
	{
		if ($this->requalificationPossibility !== null) {
			if ($this->requalificationPossibility === 100) {
				return true;
			}

			return !$this->product->price($this->mutation, $this->product->getProductServices()->requalificationPriceLevel, $state)->isZero();
		}

		return false;
	}

	public function hasBuyableRequalificationPossibility(State $state): bool
	{
		return $this->requalificationPossibility !== null && $this->requalificationPossibility !== 100 && !$this->product->price($this->mutation, $this->product->getProductServices()->requalificationPriceLevel, $state)->isZero();
	}

	public function hasRequalificationPossibilityValue(int $value, State $state): bool
	{
		return $this->requalificationPossibility === $value && !$this->product->price($this->mutation, $this->product->getProductServices()->requalificationPriceLevel, $state)->isZero();
	}

}
