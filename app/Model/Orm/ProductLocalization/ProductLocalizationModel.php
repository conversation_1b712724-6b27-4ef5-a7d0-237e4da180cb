<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductLocalization;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductTree\ProductTree;
use App\Model\Orm\ProductTree\ProductTreeRepository;

final class ProductLocalizationModel
{

	public function __construct(
		private readonly ProductLocalizationRepository $repository,
		private readonly ProductTreeRepository $productTreeRepository,
	) {}


	public function create(Mutation $mutation, Product $product): void
	{
		$productLocalization = new ProductLocalization();
		$productLocalization->mutation = $mutation;

		$product->productLocalizations->add($productLocalization);
		$this->repository->persistAndFlush($productLocalization);
	}


	public function delete(Mutation $mutation, Product $product): void
	{
		$productLocalization = $this->repository->getBy([
			'product' => $product,
			'mutation' => $mutation,
		]);


		if ($productLocalization) {
			$this->repository->removeAndFlush($productLocalization);
		}
	}

	public function attachTo(ProductLocalization $productLocalization, array $treeIds = []): void
	{
		$product = $productLocalization->product;
		$mutation = $productLocalization->mutation;

		$collectionToDelete = $this->productTreeRepository->findBy([
			'product' => $product,
			'tree->mutation' => $mutation,
			'tree!=' => $treeIds
		]);

		foreach ($collectionToDelete as $item) {
			$this->productTreeRepository->removeAndFlush($item);
		}

		foreach ($treeIds as $key=>$treeId) {
			$entity = $this->productTreeRepository->getBy([
				'product' => $product,
				'tree' => $treeId
			]);

			if (!$entity) {
				$entity = new ProductTree();
				$this->productTreeRepository->attach($entity);
			}

			$entity->product = $product;
			$entity->tree = $treeId;
			$entity->sort = $key;
			$this->productTreeRepository->persistAndFlush($entity);
		}
	}

}
