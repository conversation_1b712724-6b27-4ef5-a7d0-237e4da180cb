<?php declare(strict_types = 1);

namespace App\Model\Orm\UserHash;

use App\Model\Orm\Orm;
use App\Model\Orm\User\User;
use Exception;
use Nextras\Dbal\Utils\DateTimeImmutable;

final class UserHashModel
{

	public function __construct(
		private readonly Orm $orm,
	)
	{
	}

	/**
	 * @throws Exception
	 */
	public function generateHashForUser(User $userEntity, string $type, array $data = [], int $daysToinvalidate = 1, bool $save = true): UserHash
	{
		$userHash = new UserHash();
		$this->orm->userHash->attach($userHash);

		$userHash->validTo = new DateTimeImmutable('+ ' . $daysToinvalidate . ' days');
		$userHash->user = $userEntity;
		$userHash->type = $type;
		$userHash->hash = $this->generateHashString($userEntity, $data, $type);

		if ($save) {
			$this->orm->userHash->persistAndFlush($userHash);
		}

		return $userHash;
	}


	public function generateHashString(User $userEntity, array $data, string $type): string
	{
		return sha1(implode('-', $data) . $userEntity->id . $type . date('Y-m-d-H-i-s'));
	}


	public function getHash(string $hash, string $type): ?UserHash
	{
		return $this->orm->userHash->getBy([
			'hash' => $hash,
			'type' => $type,
		]);
	}


	public function getValidHash(string $hash, string $type): ?UserHash
	{
		$now = new DateTimeImmutable();
		return $this->orm->userHash->getBy([
			'hash' => $hash,
			'type' => $type,
			'validTo>' => $now,
			'valid' => true,
		]);
	}


	public function useHash(UserHash $userHash, bool $save = true): void
	{
		$userHash->valid = false;
		$userHash->usedTime = new DateTimeImmutable();

		if ($save) {
			$this->orm->persistAndFlush($userHash);
		}
	}

}
