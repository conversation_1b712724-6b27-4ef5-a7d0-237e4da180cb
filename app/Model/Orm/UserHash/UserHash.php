<?php

declare(strict_types=1);

namespace App\Model\Orm\UserHash;

use App\Model\Orm\JsonContainer; // phpcs:ignore
use App\Model\Orm\User\User;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property User $user {m:1 User::$hashes}
 * @property string $type {default ''}
 * @property string $hash {default ''}
 * @property DateTimeImmutable $createdTime {default 'now'}
 * @property DateTimeImmutable $validTo {default '+1 day'}
 * @property bool $valid {default true}
 * @property DateTimeImmutable|null $usedTime
 * @property array $data {container JsonContainer}
 */
class UserHash extends Entity
{
	const string TYPE_LOST_PASSWORD = 'lostPassword';
	const string HASH_TYPE_REGISTRATION = 'registration';


	public function isValid(): bool
	{
		$now = new DateTimeImmutable();
		return $this->validTo > $now && $this->valid;
	}
}
