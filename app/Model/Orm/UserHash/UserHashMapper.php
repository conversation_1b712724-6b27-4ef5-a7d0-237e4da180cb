<?php

declare(strict_types=1);

namespace App\Model\Orm\UserHash;

use App\Model\Orm\Traits\HasCamelCase;
use App\Model\Orm\User\User;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends <PERSON>balMapper<UserHash>
 */
final class UserHashMapper extends DbalMapper
{
	use HasCamelCase;
	/**
* @return literal-string
*/
public function getTableName(): string
{
	return 'user_hash';
}


	public function removeByUser(User $user): void
	{
		$this->connection->query('DELETE FROM user_hash WHERE userId = %i', $user->id);
	}

}
