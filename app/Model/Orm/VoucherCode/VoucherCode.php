<?php

declare(strict_types=1);

namespace App\Model\Orm\VoucherCode;


use App\Exceptions\LogicException;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\Product\ProductItem;
use App\Model\Orm\Order\Voucher\VoucherItem;
use App\Model\Orm\Price;
use App\Model\Orm\State\State;
use App\Model\Orm\State\StateModel;
use App\Model\Orm\Traits\HasCache;
use App\Model\Orm\Traits\HasStaticCache;
use App\Model\Orm\Voucher\Voucher;
use App\Model\Orm\VoucherLimit\VoucherLimitModel;
use App\Model\VatCalculator;
use Brick\Math\BigDecimal;
use Brick\Math\RoundingMode;
use Brick\Money\Money;
use Elastica\QueryBuilder;
use Nextras\Orm\Entity\Entity;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property int $id {primary}
 * @property string $code
 * @property int $isUsed {default 0}
 * @property DateTimeImmutable|null $usedAt
 * @property DateTimeImmutable|null $createdTime {default 'now'}
 *
 *
 * RELATIONS
 * @property Voucher $voucher {m:1 Voucher::$codes}
 * @property OneHasMany<VoucherItem> $orderItems {1:m VoucherItem::$voucherCode}
 *
 * VIRTUAL
 * @property-read bool $isInOrder {virtual}
 *
 *
 */
class VoucherCode extends Entity
{
	use HasStaticCache;
	public const ERROR_NO_EXISTS = 'voucher_error_voucher_no_exists'; // chyba pokud zadany voucher neexistuje
	public const ERROR_ALREADY_IN_CART = 'voucher_error_already_in_cart';
	public const ERROR_COMBINATION = 'voucher_error_voucher_combination'; // chyba pokud voucher nlze pouzit v kombinaci s jinym voucherem v kosiku
	private const ERROR_EXPIRED = 'voucher_error_voucher_expired'; // chyba pokud voucher uz casove vyprsel
	private const ERROR_EXPIRED_DATE = 'voucher_error_voucher_expired_date'; // chyba pokud expiroval casovo
	private const ERROR_MIN_PRICE = 'voucher_error_voucher_bad_using_min_order';  // chyba pokud je vyse objednavky niszi nez je potreba
	private const ERROR_USED = 'voucher_error_voucher_used'; // chyba pokud jiz byl voucher jednou pouzit
	private const ERROR_LIMITS = 'voucher_error_voucher_limits'; // chyba pokud voucher nema splnene limitujici podminky
	private const ERROR_MIN_PRICE_LIMITS = 'voucher_error_voucher_bad_using_min_order_limits'; // chyba pokud je vyse limitovanych produktu v kosiku niszi nez je potreba

	private StateModel $stateModel;
	private VoucherLimitModel $voucherLimitModel;

	public function injectServices(
		StateModel $stateModel,
		VoucherLimitModel $voucherLimitModel
	): void
	{
		$this->stateModel = $stateModel;
		$this->voucherLimitModel = $voucherLimitModel;
	}

	public function getterIsInOrder(): bool
	{
		return $this->orderItems->countStored() > 0;
	}
	public function getName(): string
	{
		return $this->voucher->name . ' [' . $this->code . ']';
	}

	private function getLimitedProductIds(Order $order): ?array
	{
		return $this->loadCache($this->createCacheKey('limitedProductIds', $order->id), function () use ($order){
			if($this->voucher->limits->countStored() > 0) {
				$qb = new QueryBuilder();
				$mainQuery = $qb->query()->bool();
				$boolQuery = $this->voucherLimitModel->getElasticCondition($this->voucher->limits->toCollection());
				$mainQuery->addMust($boolQuery);
				$mainQuery->addMust($qb->query()->terms('id', $order->getProductsIds()));

				$productIdsIterator = $this->voucherLimitModel->executeElasticQuery($mainQuery);
				return iterator_to_array($productIdsIterator());
			}
			return null;
		});
	}

	public function isProductInLimit(Order $order, int $productId): bool
	{
		if($this->voucher->limits->countStored() > 0) {
			$productIds = $this->getLimitedProductIds($order);
			return in_array($productId, $productIds);
		}

		return true;
	}

	public function canBeApplied(Order $order): string|true
	{
		if ($this->voucher->isExpired) {
			return self::ERROR_EXPIRED_DATE;
		}

		if (!$this->voucher->reuse && $this->isUsed) {
			return self::ERROR_USED;
		}

		if (!$this->voucher->isActive) {
			return self::ERROR_EXPIRED;
		}

		// check if voucher has limitations
		if (($productIds = $this->getLimitedProductIds($order)) !== null) {
			if ($productIds === []) {
				return self::ERROR_LIMITS;
			}
		}

		$minPriceOrder = $this->voucher->minPriceOrder;
		if ($minPriceOrder !== null && $minPriceOrder->isPositive()) {
			$priceCart = $productIds !== null ? $order->getTotalProductIdsPriceVat($productIds) : $order->getTotalProductPriceVat();
			if ($priceCart->isLessThan($minPriceOrder)) {
				return $productIds !== null ? self::ERROR_MIN_PRICE_LIMITS : self::ERROR_MIN_PRICE;
			}
		}

		if ($this->voucher->combination) {
			$vouchers     = $order->vouchers->toCollection();
			$voucherTypes = [];
			/** @var VoucherItem $voucherItem */
			foreach ($vouchers as $voucherItem) {
				$voucherTypes[$voucherItem->voucherCode->voucher->type] = $voucherItem->voucherCode->voucher->type;
			}

			if ($this->voucher->combinationType !== null && !isset($voucherTypes[$this->voucher->combinationType]) && count($voucherTypes) !== 0) {
				return self::ERROR_COMBINATION;
			}

		}
		return true;
	}

	public function getMaxAmount(Order $order): int
	{
		$isInOrder = $order->vouchers->toCollection()->findBy(['voucherCode' => $this])->countStored();
		$canBeApplied = $this->canBeApplied($order) === true ? 1 : 0;

		return (int) ($canBeApplied && $isInOrder);
	}

	public function vatRate(Mutation $mutation, ?State $state = null): BigDecimal
	{
		$vatRateType = $this->voucher->getVatRate($state);

		$stateId = $state ? $state->id : State::DEFAULT_ID;
		$vatRates = $this->stateModel->getAllVatRatesValues($mutation);
		$rate = $vatRates[$stateId]->get($vatRateType);

		if ($rate === null) {
			throw new LogicException('Unknown vatRate.');// nekonzistentni stav, ktery musime odhalit, nelze pouzit defaultni sazbu, prodavalo by se spatne a neprislo by se na to -> je to tak?
		}

		return $rate;
	}

	public function getDiscount(Order $order): Money
	{
		$productIds = $this->getLimitedProductIds($order);
		$productTotalPrice = $productIds !== null ? $order->getTotalProductIdsPriceVat($productIds) : $order->getTotalProductPriceVat();

		if ($this->voucher->type === Voucher::TYPE_FREE_DELIVERY) {
			if (!$order->hasDelivery()) {
				return Money::zero($productTotalPrice->getCurrency());
			}
			return $order->getDelivery()->totalPriceVat->negated();
		}

		if (in_array($this->voucher->type, [Voucher::TYPE_AMOUNT, Voucher::TYPE_AMOUNT_COMBINATION])) {
			$discount = $this->voucher->discount->asMoney();
			if ($productTotalPrice->isLessThan($discount)) {
				$discount = $productTotalPrice;
			}

			return VatCalculator::priceWithoutVat($discount, $this->vatRate($order->mutation, $order->country))->negated();
		}


		$discountFraction = BigDecimal::of($this->voucher->discountPercent)->dividedBy(100, 2, RoundingMode::HALF_UP);
		$discount = $productTotalPrice->multipliedBy($discountFraction, RoundingMode::HALF_UP);

		return $discount->negated();
	}
}
