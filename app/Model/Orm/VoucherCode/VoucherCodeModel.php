<?php
declare(strict_types=1);

namespace App\Model\Orm\VoucherCode;


use App\Model\Orm\Orm;
use App\Model\Orm\Voucher\Voucher;
use Nette\Utils\Random;
use Nextras\Dbal\Utils\DateTimeImmutable;

class VoucherCodeModel
{

	private const BATCH_SIZE_FOR_CODES_GENERATING = 1000; // kolik kodu se maximalne vygeneruje v ramci jedne davky

	public function __construct(
		private readonly Orm $orm,
	)
	{
	}

	/**
	 * @param VoucherCode[] $vouchers
	 */
	public function deactivateCodes(iterable $vouchers): void
	{
		if ($vouchers === []) {
			return;
		}

		foreach ($vouchers as $voucherCode) {
			if ($voucherCode->voucher->reuse != 1) {
				$voucherCode->isUsed = 1;
				$voucherCode->usedAt = new DateTimeImmutable();

				$this->orm->persistAndFlush($voucherCode);
			}
		}
	}

	public function addCodes(Voucher $voucher, int $count, string $prefix = ''): void
	{
		if ($count > self::BATCH_SIZE_FOR_CODES_GENERATING) {
			// TODO: use Symfony messenger and consumer
		} else {
			$this->generateCodesBatch($voucher, $count, $prefix);
		}
	}


	public function generateCodesBatch(Voucher $voucher, int $count, string $prefix = ''): void
	{
		for ($i = 1; $i <= $count; $i++) {
			$this->addCode($voucher, false, true, $prefix);
		}

		$this->orm->flush();
	}


	public function addCode(Voucher $voucher, bool $return = true, bool $commit = false, string $prefix = ''): ?VoucherCode
	{
		$newCode = new VoucherCode();
		$newCode->voucher = $voucher;
		$newCode->code = $prefix.$this->generateCode($voucher);

		if ($commit) {
			$this->orm->voucherCode->persistAndFlush($newCode);
		} else {
			$this->orm->voucherCode->persist($newCode);
		}

		if ($return) {
			return $newCode;
		}
		return null;
	}


	public function addExactCode(Voucher $voucher, string $code): VoucherCode|false
	{
		$oldCode = $this->orm->voucherCode->getBy([
			'code' => $code,
			'voucher->mutation->id' => $voucher->mutation->id
		]);

		if ($oldCode === null) {
			$newCode = new VoucherCode();
			$newCode->voucher = $voucher;
			$newCode->code = $code;

			return $this->orm->voucherCode->persistAndFlush($newCode);
		} else {
			return false;
		}
	}


	private function generateCode(Voucher $voucher): string
	{
		$mutation = $voucher->mutation;

		do {
			$value = Random::generate(6, 'ABCDEFGHJKLMNPQRSTUXYZ1-9'); // without I, O, V, W and 0
		} while ($this->orm->voucherCode->getBy([
			'code' => $value,
			'voucher->mutation->id' => $mutation->id
		]));
		return $value;
	}
}
