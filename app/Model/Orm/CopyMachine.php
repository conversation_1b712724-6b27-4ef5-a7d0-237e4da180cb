<?php declare(strict_types = 1);

namespace App\Model\Orm;

use Nextras\Orm\Entity\IEntity;
use Nextras\Orm\Relationships\ManyHasOne;
use Nextras\Orm\Relationships\OneHasOne;

class CopyMachine
{

	public static function makeShallowCopy(IEntity $source, IEntity $target): IEntity
	{
		foreach ($source->getMetadata()->getProperties() as $property) {
			if (
				!$property->isVirtual
				&& !$property->isPrimary
				&& $property->wrapper === null
				&& $property->relationship === null
			) {
				$name = $property->name;
				$target->setRawValue($name, $source->getRawValue($name));
			}
		}

		self::setRawValuesForContainers($source, $target);

		return $target;
	}

	public static function setRawValuesForContainers(IEntity $source, IEntity $target): IEntity
	{
		foreach ($source->getMetadata()->getProperties() as $propertyName => $property) {
			if (
				in_array($property->wrapper, [JsonContainer::class, JsonArrayContainer::class, JsonArrayHashContainer::class])
			) {
				$rawDbValue = $source->getProperty($propertyName)->getRawValue();
				$target->getProperty($propertyName)->setRawValue($rawDbValue);
				$target->setAsModified($propertyName);
			}
		}

		return $target;
	}


	public static function makeHasOneRelationCopy(IEntity $source, IEntity $target): IEntity
	{
		foreach ($source->getMetadata()->getProperties() as $propertyName => $property) {
			if ($property->relationship !== null && in_array($property->wrapper, [ManyHasOne::class, OneHasOne::class])) {
				$target->$propertyName = $source->$propertyName;
			}
		}

		return $target;
	}

}
