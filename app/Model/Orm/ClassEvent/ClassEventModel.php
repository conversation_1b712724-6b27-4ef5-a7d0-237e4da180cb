<?php declare(strict_types = 1);

namespace App\Model\Orm\ClassEvent;

use App\Model\ElasticSearch\Product\Convertor\ClassData;
use App\Model\ElasticSearch\Product\Facade;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Product\Product;
use App\Model\Orm\State\State;
use App\Model\Time\CurrentDateTimeProvider;
use Nextras\Dbal\Utils\DateTimeImmutable;

final class ClassEventModel
{

	public function __construct(
		private readonly Orm $orm,
		private readonly Facade $productEsFacade,
		private readonly CurrentDateTimeProvider $currentDateTimeProvider,
	)
	{
	}


	public function create(Mutation $mutation, State $state, Product $product, string $city, string $place, DateTimeImmutable $from, DateTimeImmutable $to, int $limit): ClassEvent
	{
		if ($to < $from) {
			$to = $from;
		}
		$newClassEvent = new ClassEvent();
		$newClassEvent->mutation = $mutation;
		$newClassEvent->product = $product;
		$newClassEvent->state = $state;
		$newClassEvent->city = $city;
		$newClassEvent->place = $place;
		$newClassEvent->from = $from;
		$newClassEvent->to = $to;
		$newClassEvent->capacity = $limit;

		$this->orm->classEvent->attach($newClassEvent);
		$this->orm->classEvent->persistAndFlush($newClassEvent);

		return $newClassEvent;
	}

	public function updateEsForEvent(Product $product, Mutation $mutation): void
	{
		$this->productEsFacade->update($product, $mutation, [ClassData::class]);
	}

	public function hasLastMinuteOption(ClassEvent $classEvent, int $daysCount, int $capacityCount): bool
	{
		$from = $classEvent->from;
		$now = $this->currentDateTimeProvider->getCurrentDateTime();
		$diff = $from->diff($now);
		$seconds = ($diff->days * 24 * 60 * 60) + ($diff->h * 60 * 60) + ($diff->i * 60) + $diff->s;
		if ($seconds < $daysCount * 24 * 60 * 60 && $classEvent->from > $now) {
			return true;
		}
		$availableCapacity = $classEvent->getAvailableCapacity();
		return 0 < $availableCapacity && $availableCapacity <= $capacityCount;
	}

}
