<?php declare(strict_types = 1);

namespace App\Model\Orm\ClassEvent;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Product\Product;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method ClassEvent|null getById($id)
 * @method ICollection<ClassEvent> findByFilter(?ArrayHash $filter)
 * @method ICollection<ClassEvent> findPublicEvents(Product $product, Mutation $mutation, \DateTimeImmutable $from)
 * @extends Repository<ClassEvent>
 */
final class ClassEventRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [ClassEvent::class];
	}

	public function getByExtId(int|string $id): ?ClassEvent
	{
		return $this->getBy(['extId' => (string) $id]);
	}

}
