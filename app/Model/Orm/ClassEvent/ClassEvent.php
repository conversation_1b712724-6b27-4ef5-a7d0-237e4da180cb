<?php declare(strict_types = 1);

namespace App\Model\Orm\ClassEvent;

use App\Model\Orm\ClassEventSectionMetadata\ClassEventSectionMetadata;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Product\Product;
use App\Model\Orm\State\State;
use App\Model\Orm\User\User;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property int $id {primary}
 * @property string|null $extId {default null}
 * @property string $name {default ''}
 * @property DateTimeImmutable $from {default 'now'}
 * @property DateTimeImmutable $to {default 'now'}
 * @property int $capacity {default 0}
 * @property int $capacityUsed {default 0}
 * @property int $public {default 0}
 * @property string $city {default ''}
 * @property string $place {default ''}
 *
 *
 * RELATIONS
 * @property Mutation $mutation {m:1 Mutation::$classEvents}
 * @property Product $product {m:1 Product::$classEvents}
 * @property State|null $state {m:1 State::$classEvents}
 * @property OneHasMany<ClassEventSectionMetadata> $classEventSectionMetadata {1:m ClassEventSectionMetadata::$classEvent}
 * @property ManyHasMany<User> $lectors {m:m User::$classEvents, isMain = true}
 *
 * VIRTUAL
 * @property-read int $daysToGo {virtual}
 * @property-read int $isAvailable {virtual}
 */
class ClassEvent extends Entity
{

	private ClassEventModel $classEventModel;

	public function injectModel(
		ClassEventModel $classEventModel,
	): void
	{
		$this->classEventModel = $classEventModel;
	}

	public function getNiceName(): string
	{
		return $this->city . ' (' . $this->from->format('d.m.Y H:i') . ' - ' . $this->to->format('d.m.Y H:i') . ')';
	}

	public function getAvailableCapacity(): int
	{
		return $this->capacity - $this->capacityUsed;
	}

	public function formatDate(): string
	{
		if ($this->from->format('d.m.Y') === $this->to->format('d.m.Y')) {
			return $this->from->format('j. n. Y');
		}

		return $this->from->format('j. n.') . ' - ' . $this->to->format('j. n. Y');
	}

	public function formatHour(): string
	{
		return $this->from->format('H:i') . ' - ' . $this->to->format('H:i');
	}

	public function getterDaysToGo(): int|null
	{
		$daysToGo = $this->from->diff(\Nette\Utils\DateTime::from('now'))->days;

		if ($daysToGo === false) {
			return null;
		}

		return $daysToGo;
	}

	public function onAfterPersist(): void
	{
		$this->classEventModel->updateEsForEvent($this->product, $this->mutation);
	}

	public function getName(): string
	{
		return implode(' - ', [$this->city, $this->place]);
	}

	public function getterIsAvailable(): bool
	{
		$now = new \DateTimeImmutable();

		if ($this->from <= $now) {
			return false;
		}

		return $this->getAvailableCapacity() > 0;
	}

}
