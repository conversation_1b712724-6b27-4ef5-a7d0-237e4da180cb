<?php declare(strict_types = 1);

namespace App\Model\Orm\ClassEvent;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Traits\HasCamelCase;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<ClassEvent>
 */
class ClassEventMapper extends DbalMapper
{

	use HasCamelCase;

	/**
	 * @return literal-string
	 */
	public function getTableName(): string
	{
		return 'class_event';
	}
	/**
	 * @return ICollection<ClassEvent>
	 */
	public function findByFilter(?ArrayHash $filter): ICollection
	{
		$builder = $this->builder()->select('h.*')->from($this->getTableName(), 'h');

		if (!empty($filter->fulltext)) {
			$builder->andWhere('h.name LIKE %_like_', $filter->fulltext);
		}

		return $this->toCollection($builder);
	}

	/**
	 * @return ICollection<ClassEvent>
	 */
	public function findPublicEvents(Product $product, Mutation $mutation, \DateTimeImmutable $from): ICollection
	{
		$builder = $this->builder()
			->select('ce.*')
			->from($this->getTableName(), 'ce')
			->where('ce.productId = %i', $product->id)
			->andWhere('ce.public = 1')
			->andWhere('ce.mutationId = %i', $mutation->id)
			->andWhere('ce.from >= %dt', $from)
			->andWhere('ce.capacityUsed < ce.capacity')
			->orderBy('ce.from ASC');

		return $this->toCollection($builder);
	}

}
