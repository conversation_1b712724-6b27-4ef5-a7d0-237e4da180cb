<?php declare(strict_types = 1);

namespace App\Model\Orm\ClassEventSectionMetadata;

use App\Model\Orm\ClassEvent\ClassEvent;
use App\Model\Orm\ClassSection\ClassSection;
use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property string $content {default ''}
 * @property string $description {default ''}
 *
 * RELATIONS
 * @property ClassSection $classSection {m:1 ClassSection::$classEventSectionMetadata}
 * @property ClassEvent $classEvent {m:1 ClassEvent::$classEventSectionMetadata}
 *
 *
 * VIRTUAL
 */
class ClassEventSectionMetadata extends Entity
{

}
