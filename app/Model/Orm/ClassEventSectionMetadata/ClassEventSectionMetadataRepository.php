<?php declare(strict_types = 1);

namespace App\Model\Orm\ClassEventSectionMetadata;

use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method ClassEventSectionMetadata|null getById($id)
 * @method ICollection<ClassEventSectionMetadata> findByFilter(?ArrayHash $filter)
 * @extends Repository<ClassEventSectionMetadata>
 */
final class ClassEventSectionMetadataRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [ClassEventSectionMetadata::class];
	}

}
