<?php

declare(strict_types=1);

namespace App\Model\Orm;

use App\Model\TranslateData;
use Nextras\Orm\Entity\ImmutableValuePropertyWrapper;

class TranslateDataWrapper extends  ImmutableValuePropertyWrapper {

	public function convertToRawValue($value)
	{
		if ($value === null) {
			return null;
		}

		assert($value instanceof TranslateData);
		return serialize($value);
	}

	public function convertFromRawValue($value)
	{
		if ($value === null) {
			return null;
		}

		$data = @unserialize($value);

		if ($data === false) {
			return new TranslateData($value);
		}

		assert($data instanceof TranslateData);
		return $data;
	}
}
