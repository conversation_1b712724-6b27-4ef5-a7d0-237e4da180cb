<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductVariantLocalization;

use App\Model\Orm\Traits\HasSimpleSave;
use Nextras\Orm\Repository\Repository;

/**
 * @method ProductVariantLocalization|null getById($id)
 * @method ProductVariantLocalization save(?ProductVariantLocalization $entity, array $data)
 *
 * @extends Repository<ProductVariantLocalization>
 */
final class ProductVariantLocalizationRepository extends Repository
{

	use HasSimpleSave;

	public static function getEntityClassNames(): array
	{
		return [ProductVariantLocalization::class];
	}

}
