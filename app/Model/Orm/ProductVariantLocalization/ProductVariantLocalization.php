<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductVariantLocalization;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\ProductVariant\ProductVariant;
use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property int $active {default 0}
 * @property string|null $name
 * @property string|null $nameShort
 *
 * RELATIONS
 * @property ProductVariant $variant {m:1 ProductVariant::$variantLocalizations}
 * @property Mutation $mutation {m:1 Mutation::$variantLocalizations}
 *
 *
 * VIRTUAL
 */
class ProductVariantLocalization extends Entity
{

}
