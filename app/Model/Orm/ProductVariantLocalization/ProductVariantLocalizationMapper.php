<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductVariantLocalization;

use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<ProductVariantLocalization>
 */
final class ProductVariantLocalizationMapper extends DbalMapper
{

	use HasCamelCase;

	/**
* @return literal-string
*/
public function getTableName(): string
{
	return 'product_variant_localization';
}

}
