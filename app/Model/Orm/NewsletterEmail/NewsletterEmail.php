<?php declare(strict_types = 1);

namespace App\Model\Orm\NewsletterEmail;

use App\Model\Orm\Mutation\Mutation;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property string $email
 * @property DateTimeImmutable $createdTime {default now}
 *
 * RELATIONS
 * @property Mutation $mutation {m:1 Mutation::$newsletterEmails}
 *
 * VIRTUAL
 */
class NewsletterEmail extends Entity
{

}
