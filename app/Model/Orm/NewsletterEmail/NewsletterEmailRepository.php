<?php declare(strict_types = 1);

namespace App\Model\Orm\NewsletterEmail;

use App\Model\Orm\Mutation\Mutation;
use Nextras\Orm\Repository\Repository;
use App\Model\Orm\Traits\HasSimpleSave;

/**
 * @method NewsletterEmail|null getById($id)
 * @method NewsletterEmail|null getBy(array $conds)
 * @method NewsletterEmail save(?NewsletterEmail $entity, array $data)
 *
 * @extends Repository<NewsletterEmail>
 */
final class NewsletterEmailRepository extends Repository
{

	use HasSimpleSave;

	/**
	 * @inheritDoc
	 */
	public static function getEntityClassNames(): array
	{
		return [NewsletterEmail::class];
	}

	public function getEmail(string $email, Mutation $mutation): ?NewsletterEmail
	{
		return $this->getBy(['email' => $email, 'mutation' => $mutation]);
	}

}
