<?php declare(strict_types=1);

namespace App\Model\Orm\LibraryImage;

use App\Model\Orm\ParameterValue\ParameterValueRepository;
use App\Model\Orm\Product\Product;
use Nette\Caching\Cache;
use Nextras\Orm\Collection\ICollection;

class FlagImageService
{

	public function __construct(
		private readonly LibraryImageRepository $libraryImageRepository,
		private readonly ParameterValueRepository $parameterValueRepository,
		private readonly Cache $cache,
	)
	{
	}


	/**
	 * @return array<LibraryImage>
	 */
	public function findFlags(Product $product): array
	{
		$cacheKey = 'flags-' . $product->id;
		$flagIds = $this->cache->load($cacheKey);

		if ($flagIds !== null) {
			return $this->libraryImageRepository->findByIds($flagIds)->fetchAll();
		}

		$flags = $this->parameterValueRepository->getLanguageFlags($product);

		$flagIds = array_map(fn(LibraryImage $flag) => $flag->id, $flags);
		$this->cache->save($cacheKey, $flagIds, [Cache::Expire => '1 day']);

		return $flags;
	}

}
