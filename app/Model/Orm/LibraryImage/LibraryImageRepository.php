<?php declare(strict_types = 1);

namespace App\Model\Orm\LibraryImage;

use App\Model\Orm\CollectionById;
use App\Model\Orm\LibraryTree\LibraryTree;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method LibraryImage|null getById($id)
 * @method ICollection<LibraryImage> findByIds($ids)
 * @method ICollection<LibraryImage> findByFilter(array $filterData, ?LibraryTree $libraryTree)
 * @method ICollection<LibraryImage> findByExactOrder(array $ids)
 * @method void fixSortForDirectory(LibraryTree $libraryTree)
 * @method Result findImagesForSitemap($limit, $offset)
 *
 * @extends Repository<LibraryImage>
 */
final class LibraryImageRepository extends Repository implements CollectionById
{

	public static function getEntityClassNames(): array
	{
		return [LibraryImage::class];
	}
	/**
	 * @return ICollection<LibraryImage>
	 */
	public function findByIdOrder(array $ids): ICollection
	{
		return $this->findByExactOrder($ids);
	}

}
