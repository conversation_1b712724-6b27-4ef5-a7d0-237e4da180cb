<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Promotion;

use App\Model\Orm\JsonArrayHashContainer; // phpcs:ignore
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderItem;
use App\Model\Orm\Price;
use App\Model\Orm\State\VatRate;
use App\Model\Promotion\Applicator\PromotionResult;
use App\PostType\Promotion\Model\Orm\Promotion\PromotionLocalization;
use Brick\Money\Money;
use Nette\Utils\ArrayHash;

/**
 * @property-read int $id {primary}
 * @property Order $order {m:1 Order::$promotions}
 * @property PromotionLocalization|null $promotionLocalization {m:1 PromotionLocalization, oneSided=true}
 * @property string|null $promotionName {default null}
 * @property ArrayHash $promotionInfo {container JsonArrayHashContainer}
 * @property string|null $promotionUniqId {default null}
 */
final class PromotionItem extends OrderItem
{

	private function __construct()
	{
		parent::__construct();
	}

	public static function create(Order $order, PromotionResult $promotionResult): PromotionItem
	{
		$item = new self();
		$item->order = $order;
		$item->promotionLocalization = $promotionResult->promotionLocalization;
		$item->promotionName = $promotionResult->promotionLocalization->getName();
		$item->promotionInfo = ArrayHash::from($promotionResult->toArray());
		$item->promotionUniqId = $promotionResult->getUniqId();

		$item->amount = 1;
		$item->unitPrice = Price::from($item->getCurrentUnitPrice());
		$item->vatRate = $item->getCurrentVatRate();
		$item->vatRateValue = $item->getVatRateValue();

		return $item;
	}

	public function getMaxAvailableAmount(): int
	{
		return $this->promotionInfo->savedMoney->amount > 0 ? 1 : 0;
	}

	public function getCurrentUnitPrice(): Money
	{
		return Money::of($this->promotionInfo->savedMoney->amount, $this->promotionInfo->savedMoney->currency)->multipliedBy(-1);
	}

	protected function getCurrentVatRate(): VatRate
	{
		/*if (($product = $this->giftLocalization->gift->product) !== null) {
			return $product->vatRateType($this->order->country);
		}*/
		return VatRate::None;
	}

	public function getName(): string
	{
		if ($this->promotionName !== null) {
			return $this->promotionName;
		}
		return $this->promotionLocalization->getName();
	}
	public function getDesc(): string
	{
		return '';
	}
	public function setAmount(int $amount): void
	{
		$amount = min($amount, $this->getMaxAvailableAmount());
		$this->amount = $amount;
	}

}
