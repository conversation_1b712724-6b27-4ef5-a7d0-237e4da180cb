<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Discount;

use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderItem;
use App\Model\Orm\State\VatRate;
use Brick\Money\Money;

/**
 * @property-read int $id {primary}
 * @property Order $order {m:1 Order::$discounts}
 * @property string $discountTypeUniqueIdentifier
 */
final class DiscountItem extends OrderItem
{

	private DiscountTypeRegistry $discountTypeRegistry;

	private function __construct()
	{
		parent::__construct();
	}

	public static function create(Order $order, string $discountTypeUniqueIdentifier): DiscountItem
	{
		$item = new self();
		$item->order = $order;
		$item->discountTypeUniqueIdentifier = $discountTypeUniqueIdentifier;

		return $item;
	}

	public function injectDiscountTypeRegistry(DiscountTypeRegistry $registry): void
	{
		$this->discountTypeRegistry = $registry;
	}

	public function getDiscountType(): DiscountType
	{
		return $this->discountTypeRegistry->get($this->discountTypeUniqueIdentifier);
	}

	public function getMaxAvailableAmount(): int
	{
		return $this->getDiscountType()->getMaxAmount($this->order);
	}

	public function getCurrentUnitPrice(): Money
	{
		return $this->getDiscountType()->getDiscount($this->order)->negated();
	}

	protected function getCurrentVatRate(): VatRate
	{
		return $this->getDiscountType()->getVatRate($this->order);
	}

	public function getName(): string
	{
		return '';
	}
	public function getDesc(): string
	{
		return '';
	}

}
