<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Discount;

use function array_values;
use function sprintf;

final class DiscountTypeRegistry
{

	/** @var array<string, DiscountType> */
	private array $discountTypes = [];

	/**
	 * @param DiscountType[] $discountTypes
	 */
	public function __construct(
		array $discountTypes,
	)
	{
		foreach ($discountTypes as $discountType) {
			$this->discountTypes[$discountType->getUniqueIdentifier()] = $discountType;
		}
	}

	/**
	 * @return list<DiscountType>
	 */
	public function list(): array
	{
		return array_values($this->discountTypes);
	}

	public function get(string $uniqueIdentifier): DiscountType
	{
		return $this->discountTypes[$uniqueIdentifier] ?? throw new \InvalidArgumentException(sprintf('Discount type with ID "%s" not found.', $uniqueIdentifier));
	}

}
