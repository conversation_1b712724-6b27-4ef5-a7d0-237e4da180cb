<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\OrderStateHistory;

use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<OrderStateChange>
 */
final class OrderStateChangeMapper extends <PERSON>balMapper
{

	use HasCamelCase;

	/**
	 * @return literal-string
	 */
public function getTableName(): string
{
	return 'order_state_change';
}

}
