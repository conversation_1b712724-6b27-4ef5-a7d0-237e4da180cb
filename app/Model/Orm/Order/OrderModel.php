<?php declare(strict_types = 1);

namespace App\Model\Orm\Order;

use App\Exceptions\LogicException;
use App\Model\ConfigService;
use App\Model\Erp\Connector\OrderConnector;
use App\Model\Link\LinkFactory;
use App\Model\Orm\CardPayment\CardPayment;
use App\Model\Orm\CardPayment\CardPaymentStatus;
use App\Model\Orm\CardPayment\PaymentGateway\PaymentGatewayRegistry;
use App\Model\Orm\DeliveryMethod\DeliveryMethodConfiguration;
use App\Model\Orm\Order\Delivery\PickupDeliveryInformation;
use App\Model\Orm\Order\Event\OrderState\CancelOrder;
use App\Model\Orm\Order\Event\OrderState\DeclineOrder;
use App\Model\Orm\Order\Event\OrderState\DispatchOrder;
use App\Model\Orm\Order\Event\OrderState\PrepareOrder;
use App\Model\Orm\Order\Event\PaymentChanged\PaymentChanged;
use App\Model\Orm\Order\NumberSequence\OrderNumberGenerator;
use App\Model\Orm\Order\Payment\CardPaymentInformation;
use App\Model\Orm\Orm;
use App\Model\Orm\PaymentMethod\PaymentMethodConfiguration;
use Nette\Application\BadRequestException;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;
use Psr\EventDispatcher\EventDispatcherInterface;

final readonly class OrderModel
{

	public function __construct(
		private Orm $orm,
		private EventDispatcherInterface $eventDispatcher,
		protected OrderNumberGenerator $orderNumberGenerator,
		private PaymentGatewayRegistry $gatewayRegistry,
		private ConfigService $configService,
		private LinkFactory $linkFactory,
		private OrderConnector $orderConnector,
	)
	{
	}

	public function place(Order $order, ArrayHash $orderDetails, ArrayHash $orderDeliveryDetails, bool $withFlush = false): array
	{
		// set order details
		foreach ($orderDetails as $key => $value) {
			$order->{$key} = $value;
		}

		// process delivery details
		$deliveryDetails = $order->delivery->deliveryMethod->getDeliveryMethod()->processDeliveryDetails($orderDeliveryDetails);

		// set delivery details
		foreach ($deliveryDetails as $key => $value) {
			if ($order->delivery->information->getMetadata()->hasProperty($key)) {
				$order->delivery->information->{$key} = $value;
			}
		}

		$changes = $order->place($this->orderNumberGenerator);
		if ($changes !== []) {
			return $changes;
		}

		$order->payment?->paymentMethod->getPaymentMethod()->onOrderPlaced($order);
		$order->delivery?->deliveryMethod->getDeliveryMethod()->onOrderPlaced($order);

		// dispatch OrderCreatedEvent [StockReservation, VoucherDeactivate, SendEmail, PaymentProcess etc.]
		// WARNING: not call 'flush' inside events !!!
		// INFO: use try catch block, if Order can be placed if event throws exception
		//TODO $this->eventDispatcher->dispatch(new OrderPlaced($order));

		// if everything is ok, persist
		$this->orm->order->persist($order);

		if ($withFlush) {
			$this->orm->flush();
		}

		return [];
	}

	public function cancel(Order $order, bool $withFlush = false, ?string $cancelReason = null): void
	{
		$previousState = $order->state;
		$order->cancel($cancelReason);
		$this->eventDispatcher->dispatch(new CancelOrder($order, $previousState));
		$this->orm->persist($order);
		if ($withFlush) {
			$this->orm->flush();
		}
	}

	public function decline(Order $order, bool $withFlush = false): void
	{
		$previousState = $order->state;
		$order->decline();
		$this->eventDispatcher->dispatch(new DeclineOrder($order, $previousState));
		$this->orm->persist($order);
		if ($withFlush) {
			$this->orm->flush();
		}
	}

	public function dispatch(Order $order, bool $withFlush = false): void
	{
		$previousState = $order->state;
		$order->dispatch();
		$this->eventDispatcher->dispatch(new DispatchOrder($order, $previousState));
		$this->orm->persist($order);
		if ($withFlush) {
			$this->orm->flush();
		}
	}

	public function prepare(Order $order, bool $withFlush = false): void
	{
		$previousState = $order->state;
		$order->prepare();
		$this->eventDispatcher->dispatch(new PrepareOrder($order, $previousState));
		$this->orm->persist($order);
		if ($withFlush) {
			$this->orm->flush();
		}
	}

	public function setPayment(Order $order, ?PaymentMethodConfiguration $methodConfiguration, bool $withFlush = false): void
	{
		$originalPayment = $order->getPayment();

		$item = null;
		if ($methodConfiguration instanceof PaymentMethodConfiguration) {
			$item = $order->createPayment($methodConfiguration);
			$this->orm->orderPayment->attach($item);
		}

		$order->setPayment($item);
		$this->orm->order->persist($order);

		if ($originalPayment !== null) {
			$this->orm->orderPayment->remove($originalPayment);
		}

		if ($withFlush) {
			$this->orm->flush();
		}
	}

	public function changePayment(Order $order, string $paymentMethodUniqueIdentifier): void
	{
		$methodConfiguration = $this->orm->paymentMethod->getBy(['paymentMethodUniqueIdentifier' => $paymentMethodUniqueIdentifier]);
		if ($methodConfiguration !== null) {
			$this->setPayment($order, $methodConfiguration);
			$order->payment?->paymentMethod->getPaymentMethod()->onOrderPlaced($order);
			$this->orm->persistAndFlush($order);

			$this->eventDispatcher->dispatch(new PaymentChanged($order));
		}
	}

	public function createCardPayment(Order $order, bool $throw = true): ?CardPayment
	{
		$paymentInformation = $order->payment?->information;
		if ( ! $paymentInformation instanceof CardPaymentInformation) {
			if ($throw) {
				throw new BadRequestException('Order not paid by card.');
			}
			return null;
		}

		$existedPaymentsCollection = $paymentInformation->payments->toCollection();
		$existedPayments = $existedPaymentsCollection->findBy(['expireTime>' => new DateTimeImmutable(), 'status!=' => CardPaymentStatus::Canceled])->orderBy('id', ICollection::DESC);
		$existedPaymentsCount = $existedPayments->countStored();

		if ($existedPaymentsCount > 0) {
			$cardPayment = $existedPayments->getBy([]);
		} else {
			$gateway = $order->payment->paymentMethod->getPaymentMethod()->getPaymentGateway();
			$cardPayment = $gateway->createPayment($paymentInformation);
			$paymentInformation->addPendingPayment($cardPayment);
			$this->orm->persistAndFlush($paymentInformation);
		}

		return $cardPayment;
	}

	public function setDelivery(Order $order, ?DeliveryMethodConfiguration $deliveryMethodConfiguration, bool $withFlush = false): void
	{
		$originalDelivery = $order->getDelivery();

		$item = null;
		if ($deliveryMethodConfiguration instanceof DeliveryMethodConfiguration) {
			$item = $order->createDelivery($deliveryMethodConfiguration);
			$this->orm->orderDelivery->attach($item);
		}

		$order->setDelivery($item);
		$this->orm->order->persist($order);

		if ($originalDelivery !== null) {
			$this->orm->orderDelivery->remove($originalDelivery);
		}

		if ($withFlush) {
			$this->orm->flush();
		}
	}

	public function setPickupPoint(Order $order, ?int $pickupPointId, bool $withFlush = false): void
	{
		$delivery = $order->getDelivery();
		assert($delivery->information instanceof PickupDeliveryInformation);
		$delivery->information->pickupPointId = (string) $pickupPointId;
		$this->orm->persist($order);
		if ($withFlush) {
			$this->orm->flush();
		}
	}

	public function remove(Order $order, bool $withFlush = false): void
	{
		if ($order->state !== OrderState::Draft) {
			throw new LogicException('Cannot remove non draft Order.');
		}

		$delivery = $order->getDelivery();
		$payment = $order->getPayment();

		$order->setDelivery(null);
		$order->setPayment(null);

		$this->orm->order->persist($order);
		if ($delivery !== null) {
			$this->orm->orderDelivery->remove($delivery);
		}
		if ($payment !== null) {
			$this->orm->orderPayment->remove($payment);
		}

		$this->orm->order->remove($order);

		if ($withFlush) {
			$this->orm->flush();
		}
	}

}
