<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Product;

use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderItem;
use App\Model\Orm\Order\Promotion\PromotionItem;
use App\Model\Orm\Price;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\ProductVariant\Availability\ProductAvailability;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\State\VatRate;
use App\Model\TranslateData;
use App\Utils\DateTime;
use Brick\Math\BigDecimal;
use Brick\Money\Money;
use Nextras\Dbal\Utils\DateTimeImmutable;
use function min;
use App\Model\Orm\TranslateDataWrapper;  // phpcs:ignore

/**
 * @property-read int $id {primary}
 * @property Order $order {m:1 Order::$products}
 * @property ProductVariant|null $variant {m:1 ProductVariant, oneSided=true}
 * @property TranslateData|null $availabilityShortText {wrapper TranslateDataWrapper}
 * @property TranslateData|null $availabilityText {wrapper TranslateDataWrapper}
 * @property string|null $availabilityType
 * @property DateTimeImmutable|null $availabilityDeliveryDate
 * @property string|null $variantName {default null}
 * @property string|null $variantCode {default null}
 */
final class ProductItem extends OrderItem
{

	private function __construct()
	{
		parent::__construct();
	}

	public static function create(Order $order, ProductVariant $variant, int $amount = 1): ProductItem
	{
		$item = new self();
		$item->order = $order;
		$item->variant = $variant;
		$item->amount = $amount;
		$item->unitPrice = Price::from($item->getCurrentUnitPrice());
		$item->vatRate = $item->getCurrentVatRate();
		$item->vatRateValue = $item->getVatRateValue();
		$item->variantName = $variant->name;
		$item->variantCode = $variant->code;
		return $item;
	}

	public static function createFromErp(Order $order, ?ProductVariant $variant, string $variantName, string $variantCode, Price $unitPrice, VatRate $vatRate, BigDecimal $vatRateValue, int $amount = 1): ProductItem
	{
		$item = new self();
		$item->order = $order;
		$item->variant = $variant;
		$item->amount = $amount;
		$item->unitPrice = $unitPrice;
		$item->vatRate = $vatRate;
		$item->vatRateValue = $vatRateValue;
		$item->variantName = $variantName;
		$item->variantCode = $variantCode;
		return $item;
	}

	public function setAmount(int $amount): void
	{
		$amount = min($amount, $this->getMaxAvailableAmount());
		$this->amount = $amount;
	}

	public function getMaxAvailableAmount(): int
	{
		return $this->variant->productAvailability->getMaxAvailableAmount();
	}

	public function getAvailabilityText(): TranslateData
	{
		$text = $this->variant->productAvailability->getAvailabilityText($this->amount);
		assert($text instanceof TranslateData);
		return $text;
	}

	public function getAvailabilityShortText(): TranslateData
	{
		$text = $this->variant->productAvailability->getAvailabilityShortText();
		assert($text instanceof TranslateData);
		return $text;
	}

	public function getAvailabilityType(): string
	{
		return $this->variant->productAvailability->getType();
	}

	public function getAvailabilityDeliveryDate(): null|DateTime
	{
		return $this->variant->productAvailability->getDeliveryDate(
			mutation: $this->order->mutation,
			state: $this->order->country,
			priceLevel: $this->order->priceLevel,
			currency: $this->order->currency,
			deliveryMethodConfiguration: null,
			quantityRequired: $this->amount
		)?->from;
	}

	public function getAvailability(): ProductAvailability
	{
		return $this->variant->productAvailability;
	}

	public function getCurrentUnitPrice(): Money
	{
		return $this->variant->price(
			$this->order->mutation,
			$this->order->priceLevel,
			$this->order->country,
		);
	}

	protected function getCurrentVatRate(): VatRate
	{
		return $this->variant->product->vatRateType($this->order->country);
	}

	public function getName(): string
	{
		if ($this->variantName !== null) {
			return $this->variantName;
		}
		return $this->variant->name;
	}

	public function getDesc(): string
	{
		return '';
	}

	public function hasPromotion(): PromotionItem|false
	{
		/** @var PromotionItem $promotion */
		foreach ($this->order->getPromotions() as $promotion) {
			if (in_array($this->getPersistedId(), (array) $promotion->promotionInfo->touchedProductItemIds)) {
				return $promotion;
			}
		}
		return false;
	}

	public function getPriceLevel(): PriceLevel
	{
		return /*$this->priceLevel ?? */$this->order->priceLevel;
	}

}
