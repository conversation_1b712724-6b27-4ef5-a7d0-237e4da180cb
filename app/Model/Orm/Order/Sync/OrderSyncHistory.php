<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Sync;

use App\Model\Orm\BackedEnumWrapper; // phpcs:ignore
use DateTimeImmutable;
use Nextras\Orm\Entity\Entity;
use App\Model\Orm\JsonArrayContainer; // phpcs:ignore

/**
 * @property int $id {primary}
 *
 * @property DateTimeImmutable $createdAt {default now}
 * @property string $orderNumber
 * @property OrderSyncType $type {wrapper BackedEnumWrapper}
 * @property array|null $data {container JsonArrayContainer}
 * @property array|null $response {container JsonArrayContainer}
 */
final class OrderSyncHistory extends Entity
{

}
