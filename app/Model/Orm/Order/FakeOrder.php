<?php

declare(strict_types=1);

namespace App\Model\Orm\Order;

use App\Model\DeliveryDate;
use App\Model\Orm\ClassEvent\ClassEvent;
use App\Model\Orm\DeliveryMethod\DeliveryMethodConfiguration;
use App\Model\Orm\Order\Class\ClassItem;
use App\Model\Orm\Order\Delivery\OrderDelivery;
use App\Model\Orm\Order\Gift\GiftItem;
use App\Model\Orm\Order\Payment\OrderPayment;
use App\Model\Orm\Order\Product\ProductItem;
use App\Model\Orm\Order\Promotion\PromotionItem;
use App\Model\Orm\Order\Voucher\VoucherItem;
use App\Model\Orm\PaymentMethod\PaymentMethodConfiguration;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\State\State;
use App\Model\Orm\User\User;
use App\Model\Orm\VoucherCode\VoucherCode;
use App\PostType\Gift\Model\Orm\Gift\GiftLocalization;
use Brick\Math\BigDecimal;
use Brick\Money\Currency;
use Brick\Money\Money;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;

class FakeOrder implements OrderProxy
{

	private function zero(): Money
	{
		return Money::of(0, $this->getCurrency());
	}
	public function getUserEntity(): ?User
	{
		return null;
	}

	public function getCurrency(): Currency
	{
		return Currency::of('CZK');
	}

	public function getTotalProductPrice(bool $withVat = false, ?array $productIds = null): Money
	{
		return $this->zero();
	}

	public function getTotalProductPriceVat(): Money
	{
		return $this->zero();
	}

	public function getTotalProductIdsPriceVat(array $productIds): Money
	{
		return $this->zero();
	}

	public function getTotalPrice(bool $withVat = false, bool $withDelivery = false, bool $includeGiftCertificates = true, bool $withPromotions = true, bool $withFreeDeliveryVoucher = true, ?int $precision = null): Money
	{
		return $this->zero();
	}

	public function getTotalGiftCertificatesPrice(bool $withVat = true): Money
	{
		return $this->zero();
	}

	public function getTotalPriceWithDelivery(?int $precision = null): Money
	{
		return $this->zero();
	}

	public function getTotalPriceVat(
		bool $withDelivery = false,
		bool $includeGiftCertificates = true,
		bool $withFreeDeliveryVoucher = true,
		?int $precision = null
	): Money
	{
		return $this->zero();
	}

	public function getTotalDiscountVat(): Money
	{
		return $this->zero();
	}

	public function getProductItemsDiscountPrice(): Money
	{
		return $this->zero();
	}

	public function getTotalOriginalPriceVat(): Money
	{
		return $this->zero();
	}

	public function getTotalPriceWithDeliveryVat(?int $precision = null): Money
	{
		return $this->zero();
	}

	public function getTotalCount(): int
	{
		return 0;
	}

	public function getTotalWeight(): BigDecimal
	{
		return BigDecimal::of(0);
	}

	public function hasPayment(): bool
	{
		return false;
	}

	public function hasDelivery(): bool
	{
		return false;
	}

	public function hasGiftVouchers(): bool
	{
		return false;
	}

	public function hasGift(): bool
	{
		return false;
	}

	public function hasProductId(int|array $productId): bool
	{
		return false;
	}

	public function containsProductId(int|array $productId): bool
	{
		return false;
	}

	public function hasItemWithForcedFreeDelivery(): bool
	{
		return false;
	}

	public function hasElectronicProduct(bool $strict = false): bool
	{
		return false;
	}

	public function getCountry(): ?State
	{
		return null;
	}

	public function getWorstDeliveryDate(DeliveryMethodConfiguration $deliveryMethodConfiguration): null|false|DeliveryDate
	{
		return null;
	}

	public function getDelivery(): OrderDelivery|null
	{
		return null;
	}

	public function getPayment(): OrderPayment|null
	{
		return null;
	}

	public function getProductsIds(): array
	{
		return [];
	}

	/**
	 * @return ICollection<ProductItem>
	 */
	public function getProducts(): ICollection
	{
		/** @var EmptyCollection<ProductItem> $emptyCollection */
		$emptyCollection = new EmptyCollection();
		return $emptyCollection;
	}

	/**
	 * @return ICollection<VoucherItem>
	 */
	public function getVouchers(): ICollection
	{
		/** @var EmptyCollection<VoucherItem> $emptyCollection */
		$emptyCollection = new EmptyCollection();
		return $emptyCollection;
	}

	/**
	 * @return ICollection<GiftItem>
	 */
	public function getGifts(): ICollection
	{
		/** @var EmptyCollection<GiftItem> $emptyCollection */
		$emptyCollection = new EmptyCollection();
		return $emptyCollection;
	}

	public function getItems(): array
	{
		return [];
	}
	public function getAmounts(): array
	{
		return [];
	}

	public function getChangedItems(bool $onlyProducts = false, bool $onlyToRemove = false): array
	{
		return [];
	}

	public function addProduct(ProductVariant $variant, int $amount = 1): int
	{
		return 0;
	}

	public function addGift(GiftLocalization $giftLocalization): bool
	{
		return false;
	}

	public function flushGifts(): array
	{
		return [];
	}

	public function flushCache(?string $key = null): void
	{
	}

	public function refresh(): array
	{
		return [];
	}

	public function addVoucher(VoucherCode $voucher): string|true
	{
		return true;
	}

	public function subtractProduct(ProductVariant $variant, int $amount = 1): null|int|ProductItem
	{
		return null;
	}

	public function removeProduct(ProductVariant $variant): ?ProductItem
	{
		return null;
	}

	public function removeVoucher(VoucherCode $voucher): ?Voucher\VoucherItem
	{
		return null;
	}

	public function removeGift(GiftLocalization $giftLocalization): ?Gift\GiftItem
	{
		return null;
	}

	public function flushVouchers(array $exclude = [], bool $withGiftCertificates = false): array
	{
		return [];
	}

	public function setCurrency(string $currencyCode): void
	{
	}

	public function getPickupPointId(): string|null
	{
		return null;
	}



	public function setCountry(State $state): void
	{
	}

	public function setPickupPoint(?int $pickupPointId): void
	{
	}

	public function setDelivery(OrderDelivery|DeliveryMethodConfiguration|null $item, bool $ignoreState = false): void
	{
	}

	public function setPayment(OrderPayment|PaymentMethodConfiguration|null $payment): void
	{
	}

	public function useDeliveryAddress(): bool
	{
		return false;
	}

	/**
	 * @return ICollection<PromotionItem>
	 */
	public function getPromotions(): ICollection
	{
		/** @var EmptyCollection<PromotionItem> $emptyCollection */
		$emptyCollection = new EmptyCollection();
		return $emptyCollection;
	}

	public function initAutoItems(?Order $order = null): array
	{
		return [];
	}

	public function hasFreeDeliveryVoucher(): bool
	{
		return false;
	}

	public function hasCertificate(bool $strict = false): bool
	{
		return false;
	}

	public function isRegistrationRequired(): bool
	{
		return false;
	}

	public function addClass(Product $product, ?ClassEvent $classEvent, ?PriceLevel $priceLevel, int $amount = 1): int
	{
		return 0;
	}

	public function subtractClass(Product $product, ?ClassEvent $classEvent, ?PriceLevel $priceLevel, int $amount = 1): null|int|ClassItem
	{
		return null;
	}

	public function removeClass(Product $product, ?ClassEvent $classEvent, ?PriceLevel $priceLevel): ?ClassItem
	{
		return null;
	}

	public function getClassEvents(): ICollection
	{
		/** @var EmptyCollection<ClassItem> $emptyCollection */
		$emptyCollection = new EmptyCollection();
		return $emptyCollection;
	}

	public function isEmpty(): bool
	{
		return true;
	}

	public function getBuyableItems(): array
	{
		return [];
	}

}
