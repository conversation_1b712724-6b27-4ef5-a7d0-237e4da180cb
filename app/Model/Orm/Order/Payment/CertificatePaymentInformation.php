<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Payment;

final class CertificatePaymentInformation extends PaymentInformation
{

	protected function getType(): PaymentType
	{
		return PaymentType::Certificate;
	}

	protected function getInitialState(): PaymentState
	{
		return PaymentState::Completed;
	}

	public function isOnline(): bool
	{
		return true;
	}

	public function redirectUrl(): ?string
	{
		return null;
	}

	public function getState(): PaymentState
	{
		return $this->state;
	}

}
