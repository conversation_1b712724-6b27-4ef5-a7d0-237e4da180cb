<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Payment;

use App\Model\Orm\BackedEnumWrapper; // phpcs:ignore
use Nextras\Orm\Entity\Entity;

/**
 * @property-read int $id {primary}
 * @property-read PaymentType $type {wrapper BackedEnumWrapper}
 * @property-read OrderPayment $payment {1:1 OrderPayment::$information}
 * @property-read PaymentState $state {wrapper BackedEnumWrapper}
 * @property string|null $externalId {default null}
 */
abstract class PaymentInformation extends Entity
{

	final public function __construct(
		OrderPayment $payment,
	)
	{
		parent::__construct();
		$this->setReadOnlyValue('type', $this->getType());
		$this->setReadOnlyValue('payment', $payment);
		$this->setReadOnlyValue('state', $this->getInitialState());
	}

	abstract public function isOnline(): bool;

	abstract public function redirectUrl(): ?string;

	abstract protected function getType(): PaymentType;

	abstract protected function getInitialState(): PaymentState;

	abstract public function getState(): ?PaymentState;

}
