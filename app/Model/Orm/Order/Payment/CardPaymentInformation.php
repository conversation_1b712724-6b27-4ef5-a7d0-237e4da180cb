<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Payment;

use App\Model\Orm\CardPayment\CardPayment;
use App\Model\Orm\CardPayment\CardPaymentStatus;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property OneHasMany<CardPayment> $payments {1:m CardPayment::$cardPaymentInformation, cascade=[persist, remove]}
 */
final class CardPaymentInformation extends PaymentInformation
{

	private CardPaymentInformationModel $cardPaymentInformationModel;

	public function injectServices(CardPaymentInformationModel $cardPaymentInformationModel): void
	{
		$this->cardPaymentInformationModel = $cardPaymentInformationModel;
	}

	protected function getType(): PaymentType
	{
		return PaymentType::Card;
	}

	protected function getInitialState(): PaymentState
	{
		return PaymentState::Pending;
	}

	public function getState(): PaymentState
	{
		return $this->state;
	}

	public function isOnline(): bool
	{
		return true;
	}

	public function redirectUrl(): ?string
	{
		if (($pendingPayment = $this->getPendingPayment()) !== null) {
			return $pendingPayment->externalUrl;
		}

		$newCardPayment = $this->cardPaymentInformationModel->createPendingPayment($this);

		return $newCardPayment?->externalUrl ?? null;
	}

	public function getPendingPayment(): CardPayment|null
	{
		return $this->payments->toCollection()->getBy([
			'status' => CardPaymentStatus::Pending,
			'expireTime<' => new DateTimeImmutable(),
		]);
	}

	public function addPendingPayment(CardPayment $cardPayment): void
	{
		$this->payments->add($cardPayment);
		$this->setReadOnlyValue('state', $cardPayment->status->toPaymentState());
	}

}
