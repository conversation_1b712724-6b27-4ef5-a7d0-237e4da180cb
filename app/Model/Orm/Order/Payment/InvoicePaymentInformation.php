<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Payment;

final class InvoicePaymentInformation extends PaymentInformation
{

	protected function getType(): PaymentType
	{
		return PaymentType::InvoicePayment;
	}

	protected function getInitialState(): PaymentState
	{
		return PaymentState::Pending;
	}

	public function isOnline(): bool
	{
		return false;
	}

	public function redirectUrl(): ?string
	{
		return null;
	}

	public function getState(): PaymentState
	{
		return $this->state;
	}

}
