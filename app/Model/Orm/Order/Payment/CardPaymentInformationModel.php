<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Payment;

use App\Model\Orm\CardPayment\CardPayment;
use App\Model\Orm\Orm;
use <PERSON>\Debugger;
use <PERSON>\ILogger;

readonly final class CardPaymentInformationModel
{

	public function __construct(
		private Orm $orm,
	)
	{
	}

	public function createPendingPayment(CardPaymentInformation $cardPaymentInformation): ?CardPayment
	{
		try {
			$gateway     = $cardPaymentInformation->payment->paymentMethod->getPaymentMethod()->getPaymentGateway();
			$cardPayment = $gateway->createPayment($cardPaymentInformation);
			$cardPaymentInformation->addPendingPayment($cardPayment);
			$this->orm->persistAndFlush($cardPaymentInformation);

			return $cardPayment;
		} catch (\Throwable $e) {
			Debugger::log($e, ILogger::EXCEPTION);
			bdump($e->getMessage());
		}
		return null;
	}

}
