<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Payment;

use Nextras\Orm\Repository\Repository;
use function array_map;

/**
 * @extends Repository<PaymentInformation>
 */
final class PaymentInformationRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		$types = PaymentType::cases();

		return [
			PaymentInformation::class,
			...array_map(
				static fn(PaymentType $type) => $type->getEntityClassName(),
				$types,
			),
		];
	}

	public function getEntityClassName(array $data): string
	{
		$type = PaymentType::from($data['type']);

		if ( ! $type instanceof PaymentType) {
			throw new \LogicException('Cannot resolve PaymentInformation type.');
		}

		return $type->getEntityClassName();
	}

}
