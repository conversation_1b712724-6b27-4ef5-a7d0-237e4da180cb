<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Payment;

enum PaymentType: string
{

	case CashOnDelivery = 'CashOnDelivery';
	case BankTransfer = 'BankTransfer';
	case Card = 'Card';
	case BenefitCard = 'BenefitCard';
	case EdenredCard = 'EdenredCard';
	case PluxeeCard = 'PluxeeCard';
	case Certificate = 'Certificate';
	case InvoicePayment = 'InvoicePayment';
	case LegacyPayment = 'LegacyPayment';

	/**
	 * @return class-string<PaymentInformation>
	 */
	public function getEntityClassName(): string
	{
		return match ($this) {
			self::CashOnDelivery => CashOnDeliveryPaymentInformation::class,
			self::BankTransfer => BankTransferPaymentInformation::class,
			self::Card, self::BenefitCard, self::EdenredCard, self::PluxeeCard => CardPaymentInformation::class,
			self::Certificate => CertificatePaymentInformation::class,
			self::InvoicePayment => InvoicePaymentInformation::class,
			self::LegacyPayment => LegacyPaymentInformation::class,
		};
	}

}
