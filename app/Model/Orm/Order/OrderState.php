<?php

declare(strict_types=1);

namespace App\Model\Orm\Order;

use App\Model\Orm\Traits\EnumToArray;

enum OrderState: string
{

	use EnumToArray; //phpcs:ignore

	case Draft = 'draft';
	case SavedForLater = 'saved_for_later';
	case Placed = 'placed';
	case Canceled = 'canceled';
	case Declined = 'declined';
	case Prepared = 'prepared';
	case Dispatched = 'dispatched';

	case Imported = 'imported';

}
