<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Delivery;

use App\Model\Orm\State\State;

/**
 * @property string|null $name
 * @property string|null $company
 * @property string|null $street
 * @property string|null $city
 * @property string|null $zip
 * @property State|null $country {m:1 State, oneSided=true}
 * @property string|null $phoneNumber
 * @property string|null $trackingCode
 * @property string|null $pickupPointId
 */
final class LegacyDeliveryInformation extends DeliveryInformation
{

	public function getType(): DeliveryType
	{
		return DeliveryType::Legacy;
	}

	public function isValid(): bool
	{
		return true;
	}

}
