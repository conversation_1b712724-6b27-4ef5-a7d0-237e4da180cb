<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Delivery;

use App\Model\Orm\State\State;

/**
 * @property string|null $name
 * @property string|null $company
 * @property string|null $street
 * @property string|null $city
 * @property string|null $zip
 * @property State|null $country {m:1 State, oneSided=true}
 * @property string|null $phoneNumber
 * @property string|null $trackingCode
 */
final class PhysicalDeliveryInformation extends DeliveryInformation
{

	public function getType(): DeliveryType
	{
		return DeliveryType::Physical;
	}

	public function isValid(): bool
	{
		$supportedType = $this->delivery->deliveryMethod->getDeliveryMethod()->getDeliveryType()->getEntityClassName();
		return $this instanceof $supportedType
			&& $this->name !== null
			&& $this->street !== null
			&& $this->city !== null
			&& $this->zip !== null
			&& $this->country !== null
			&& $this->phoneNumber !== null;
	}

}
