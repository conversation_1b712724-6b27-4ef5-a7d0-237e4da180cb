<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Delivery;

use App\Model\Orm\BackedEnumWrapper; // phpcs:ignore
use Nextras\Orm\Entity\Entity;

/**
 * @property-read int $id {primary}
 * @property-read DeliveryType $type {wrapper BackedEnumWrapper}
 * @property OrderDelivery|null $delivery {1:1 OrderDelivery::$information}
 * @property string|null $externalId {default null}
 */
abstract class DeliveryInformation extends Entity
{

	final public function __construct(
		OrderDelivery $delivery,
	)
	{
		parent::__construct();
		$this->setReadOnlyValue('type', $this->getType());
		$this->setReadOnlyValue('delivery', $delivery);
	}

	abstract public function getType(): DeliveryType;

	abstract public function isValid(): bool;

}
