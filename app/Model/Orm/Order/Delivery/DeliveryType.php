<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Delivery;

enum DeliveryType: string
{

	case Physical = 'Physical';
	case Pickup = 'Pickup';
	case Store = 'Store';
	case Legacy = 'Legacy';
	case Online = 'Online';

	/**
	 * @return class-string<DeliveryInformation>
	 */
	public function getEntityClassName(): string
	{
		return match ($this) {
			self::Physical => PhysicalDeliveryInformation::class,
			self::Pickup => PickupDeliveryInformation::class,
			self::Store => StoreDeliveryInformation::class,
			self::Legacy => LegacyDeliveryInformation::class,
			self::Online => OnlineDeliveryInformation::class,
		};
	}

	public function isStore(): bool
	{
		return $this === DeliveryType::Store;
	}

	public function isPickUp(): bool
	{
		return $this === DeliveryType::Pickup;
	}

	public function isPhysical(): bool
	{
		return $this === DeliveryType::Physical;
	}

	public function isOnline(): bool
	{
		return $this === DeliveryType::Online;
	}

}
