<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Delivery;

/**
 * @property string|null $phoneNumber
 */
final class StoreDeliveryInformation extends DeliveryInformation
{

	public function getType(): DeliveryType
	{
		return DeliveryType::Store;
	}

	public function isValid(): bool
	{
		$supportedType = $this->delivery->deliveryMethod->getDeliveryMethod()->getDeliveryType()->getEntityClassName();
		return $this instanceof $supportedType
			   && $this->phoneNumber !== null;
	}

}
