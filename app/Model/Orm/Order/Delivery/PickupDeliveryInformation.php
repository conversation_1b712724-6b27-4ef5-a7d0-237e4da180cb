<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Delivery;

use App\Model\Orm\Order\PickupPoint\PickupPoint;
use App\Model\Orm\Orm;

/**
 * @property string|null $phoneNumber
 * @property string|null $pickupPointId
 * @property string|null $trackingCode
 * @property PickupPoint|null $pickupPoint {virtual}
 */
final class PickupDeliveryInformation extends DeliveryInformation
{

	private Orm $orm;

	private ?PickupPoint $pickupPoint = null;

	public function injectService(Orm $orm): void
	{
		$this->orm = $orm;
	}

	public function getterPickupPoint(): ?PickupPoint
	{
		if ($this->pickupPoint === null) {
			$this->pickupPoint = $this->orm->pickupPoint->getById($this->pickupPointId);
		}
		return $this->pickupPoint;
	}

	public function getType(): DeliveryType
	{
		return DeliveryType::Pickup;
	}

	public function isValid(): bool
	{
		$supportedType = $this->delivery->deliveryMethod->getDeliveryMethod()->getDeliveryType()->getEntityClassName();
		return $this instanceof $supportedType
			&& $this->phoneNumber !== null
			&& $this->pickupPointId !== null;
	}

}
