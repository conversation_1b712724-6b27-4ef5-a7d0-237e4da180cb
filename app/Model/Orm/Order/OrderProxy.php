<?php

declare(strict_types=1);

namespace App\Model\Orm\Order;

use App\Model\DeliveryDate;
use App\Model\Orm\ClassEvent\ClassEvent;
use App\Model\Orm\DeliveryMethod\DeliveryMethodConfiguration;
use App\Model\Orm\Order\Class\ClassItem;
use App\Model\Orm\Order\Delivery\OrderDelivery;
use App\Model\Orm\Order\Gift\GiftItem;
use App\Model\Orm\Order\Payment\OrderPayment;
use App\Model\Orm\Order\Product\ProductItem;
use App\Model\Orm\Order\Promotion\PromotionItem;
use App\Model\Orm\Order\Voucher\VoucherItem;
use App\Model\Orm\PaymentMethod\PaymentMethodConfiguration;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\State\State;
use App\Model\Orm\User\User;
use App\Model\Orm\VoucherCode\VoucherCode;
use App\PostType\Gift\Model\Orm\Gift\GiftLocalization;
use Brick\Math\BigDecimal;
use Brick\Money\Currency;
use Brick\Money\Money;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\IEntity;

interface OrderProxy
{

	public function initAutoItems(?Order $order = null): array;

	public function getUserEntity(): ?User;
	public function getCurrency(): Currency;
	public function getTotalProductPrice(bool $withVat = false, ?array $productIds = null): Money;
	public function getTotalProductPriceVat(): Money;
	public function getTotalProductIdsPriceVat(array $productIds): Money;
	public function getTotalPrice(bool $withVat = false, bool $withDelivery = false, bool $includeGiftCertificates = true, bool $withPromotions = true, bool $withFreeDeliveryVoucher = true, ?int $precision = null): Money;
	public function getTotalGiftCertificatesPrice(bool $withVat = true): Money;
	public function getTotalPriceWithDelivery(?int $precision = null): Money;
	public function getTotalPriceVat(bool $withDelivery = false, bool $includeGiftCertificates = true, bool $withFreeDeliveryVoucher = true, ?int $precision = null): Money;

	public function getTotalDiscountVat(): Money;
	public function getProductItemsDiscountPrice(): Money;
	public function getTotalOriginalPriceVat(): Money;
	public function getTotalPriceWithDeliveryVat(?int $precision = null): Money;
	public function getTotalCount(): int;
	public function getTotalWeight(): BigDecimal;
	public function getCountry(): ?State;
	public function getWorstDeliveryDate(DeliveryMethodConfiguration $deliveryMethodConfiguration): null|false|DeliveryDate;
	public function getDelivery(): OrderDelivery|null;
	public function getPayment(): OrderPayment|null;
	public function getProductsIds(): array;

	/**
	 * @return ICollection<ProductItem>
	 */
	public function getProducts(): ICollection;

	/**
	 * @return ICollection<ClassItem>
	 */
	public function getClassEvents(): ICollection;

	/**
	 * @return ICollection<VoucherItem>
	 */
	public function getVouchers(): ICollection;

	/**
	 * @return ICollection<GiftItem>
	 */
	public function getGifts(): ICollection;

	/** @return OrderItem[]|IEntity[] */
	public function getItems(): array;
	/** @return OrderItem[]|IEntity[] */
	public function getBuyableItems(): array;

	/**
	 * @return ICollection<PromotionItem>
	 */
	public function getPromotions(): ICollection;
	public function getPickupPointId(): string|null;


	public function setDelivery(OrderDelivery|DeliveryMethodConfiguration|null $item, bool $ignoreState = false): void;
	public function setPayment(OrderPayment|PaymentMethodConfiguration|null $payment): void;

	public function useDeliveryAddress(): bool;


	public function addProduct(ProductVariant $variant, int $amount = 1): int;

	public function addClass(Product $product, ?ClassEvent $classEvent, ?PriceLevel $priceLevel, int $amount = 1): int;
	public function addGift(GiftLocalization $giftLocalization): bool;
	public function addVoucher(VoucherCode $voucher): string|true;

	public function subtractProduct(ProductVariant $variant, int $amount = 1): null|int|ProductItem;
	public function subtractClass(Product $product, ?ClassEvent $classEvent, ?PriceLevel $priceLevel, int $amount = 1): null|int|ClassItem;

	public function removeProduct(ProductVariant $variant): ?ProductItem;
	public function removeClass(Product $product, ?ClassEvent $classEvent, ?PriceLevel $priceLevel): ?ClassItem;
	public function removeVoucher(VoucherCode $voucher): ?VoucherItem;
	public function removeGift(GiftLocalization $giftLocalization): ?GiftItem;

	public function hasPayment(): bool;
	public function hasDelivery(): bool;
	public function hasGiftVouchers(): bool;
	public function hasGift(): bool;
	public function hasProductId(int|array $productId): bool;
	public function containsProductId(int|array $productId): bool;
	public function hasFreeDeliveryVoucher(): bool;
	public function hasItemWithForcedFreeDelivery(): bool;
	public function hasElectronicProduct(bool $strict = false): bool;
	public function hasCertificate(bool $strict = false): bool;

	public function isRegistrationRequired(): bool;

	public function flushGifts(): array;
	public function flushCache(?string $key = null): void;
	public function flushVouchers(array $exclude = [], bool $withGiftCertificates = false): array;

	public function refresh(): array;

	public function isEmpty(): bool;

}
