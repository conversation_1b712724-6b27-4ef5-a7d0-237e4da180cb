<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\NumberSequence;

use App\Exceptions\LogicException;
use App\Model\Orm\Order\Order;

final readonly class OrderNumberGenerator
{

	public function __construct(
		private OrderNumberSequence $sequence,
		private OrderNumberFormatter $formatter,
	)
	{
	}

	public function generateOrderNumber(Order $order): string
	{
		$mutation = $order->mutation;
		$date = $order->placedAt;

		if ($order->placedAt === null) {
			throw new LogicException('Cannot generate number for draft order.');
		}

		$numberParts = $this->sequence->getNextNumber($mutation, $date);
		return $this->formatter->format($mutation, $date, $numberParts);
	}

	public function getPartsFromOrderNumber(Order $order): array
	{
		$numberParts = $this->formatter->unformat($order->orderNumber);
		return [
			'prefix' => $numberParts[0],
			'year' => (int) $order->placedAt->format('Y'),
			'month' => $numberParts[2],
			'day' => $numberParts[3],
			'number' => $numberParts[4],
		];
	}

	public function updateSequence(Order $order): void
	{
		$parts = $this->getPartsFromOrderNumber($order);
		$this->sequence->save($order->mutation, $parts['year'], $parts['month'], $parts['day'], $parts['number'], $parts['prefix']);
	}

}
