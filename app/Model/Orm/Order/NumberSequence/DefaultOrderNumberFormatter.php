<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\NumberSequence;

use App\Model\Orm\Mutation\Mutation;
use DateTimeInterface;
use function sprintf;

final class DefaultOrderNumberFormatter implements OrderNumberFormatter
{

	private string $mask = '%d%d%02d%02d%03d';

	public function format(
		Mutation $mutation,
		DateTimeInterface $date,
		array $parts
	): string
	{
		return sprintf($this->mask, ...array_values($parts));
	}

	public function unformat(string $orderNumber): array
	{
		$e = explode('%', $this->mask);
		$result = [];
		$startingChar = 0;
		foreach (array_filter($e, fn($value) => strlen($value) > 0) as $partMask) {
			$chars = intval($partMask) === 0 ? 1 : intval($partMask);
			$value = substr($orderNumber, $startingChar, $chars);
			if (str_ends_with($partMask, 'd')) { // Decimal converts to int
				$value = (int) $value;
			}

			$result[] = $value;

			$startingChar += $chars;
		}
		return $result;
	}

}
