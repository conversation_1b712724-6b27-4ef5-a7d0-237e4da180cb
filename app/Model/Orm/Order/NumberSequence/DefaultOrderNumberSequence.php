<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\NumberSequence;

use App\Model\Orm\Mutation\Mutation;
use DateTimeInterface;
use Nette\Utils\Strings;
use Nextras\Dbal\Connection;

final readonly class DefaultOrderNumberSequence implements OrderNumberSequence
{

	private int $prefix;

	public function __construct(
		private Connection $connection,
		private bool $testPrefix = false,
	)
	{
		if ($this->testPrefix) {
			$this->prefix = 0;
		} else {
			$this->prefix = 2;
		}
	}

	public function getNextNumber(
		Mutation $mutation,
		DateTimeInterface $date,
	): array
	{
		$prefix = $this->prefix;
		$year = (int) $date->format('Y');
		$month = (int) $date->format('m');
		$day = (int) $date->format('d');
		$sufix = 0;
		$number = 1;

		$r = $this->connection->query('SELECT number, prefix FROM order_number_sequence WHERE `year`=%i AND `month`=%i AND `day`=%i ORDER BY prefix DESC', $year, $month, $day)->fetch();

		if ($r !== null) {
			$prefix = $r->prefix;
			$number = $r->number + 1;
			if ($number > 999) {
				$prefix++;
				if ($prefix === 5) {
					$prefix++;
				}
				$number = 1;
			}
		}

		$this->save($mutation, $year, $month, $day, $number, $prefix, $sufix);

		return [
			'prefix' => $prefix,
			'year' => (int) Strings::substring((string) $year, -1),
			'month' => $month,
			'day' => $day,
			'number' => $number,
			'sufix' => $sufix,
		];
	}

	public function save(Mutation $mutation, int $year, int $month, int $day, int $number, int $prefix, int $sufix = 0): void
	{
		// phpcs:disable Squiz.PHP.Heredoc.NotAllowed
		$this->connection->query(
			<<<SQL
			INSERT INTO order_number_sequence SET mutationId = %i, `year`=%i, `month`=%i, `day`=%i, `number`=%i, prefix=%i, sufix=%i ON DUPLICATE KEY UPDATE `number` = %i
			SQL,
			$mutation->id,
			$year,
			$month,
			$day,
			$number,
			$prefix,
			$sufix,
			$number
		);
		// phpcs:enable
	}

}
