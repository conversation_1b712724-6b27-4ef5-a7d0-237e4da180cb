<?php

declare(strict_types=1);

namespace App\Model\Orm\Order;

use App\Model\Orm\Product\Product;
use App\Model\Security\User;
use Nextras\Orm\Repository\Repository;

/**
 * @method Order|null getById($id)
 * @method Order|null getBy(array $conds)
 * @method Order|null getByUsersProduct(Product $product, User $user)
 * @method Order|null getLastPlacedOrder()
 * @extends Repository<Order>
 */
final class OrderRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [Order::class];
	}

}
