<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Class;

use Nextras\Orm\Repository\Repository;
use Nextras\Orm\Collection\ICollection;
use App\Model\Orm\User\User;

/**
 * @method ICollection<ClassItem> getPlannedClasses(?User $user = null, ?String $orderHash = null)
 * @method ICollection<ClassItem> getPastClasses(?User $user = null, ?String $orderHash = null)
 * @method ICollection<ClassItem> getAllClasses(?User $user = null, ?String $orderHash = null)
 * @extends Repository<ClassItem>
 */
final class ClassItemRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [ClassItem::class];
	}

}
