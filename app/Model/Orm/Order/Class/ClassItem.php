<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Class;

use App\Model\Orm\ClassEvent\ClassEvent;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderItem;
use App\Model\Orm\Order\Promotion\PromotionItem;
use App\Model\Orm\Price;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\Availability\ProductAvailability;
use App\Model\Orm\State\VatRate;
use App\Model\TranslateData;
use App\Utils\DateTime;
use Brick\Math\BigDecimal;
use Brick\Money\Money;
use Nextras\Dbal\Utils\DateTimeImmutable;
use function min;
use App\Model\Orm\TranslateDataWrapper;  // phpcs:ignore

/**
 * @property-read int $id {primary}
 * @property Order $order {m:1 Order::$classEvents}
 * @property PriceLevel|null $priceLevel {m:1 PriceLevel, oneSided=true}
 * @property Product|null $product {m:1 Product, oneSided=true}
 * @property ClassEvent|null $classEvent {m:1 ClassEvent, oneSided=true}
 * @property string|null $classEventName {default null}
 * @property string|null $productName {default null}
 * @property DateTimeImmutable|null $classEventDateFrom {default null}
 * @property DateTimeImmutable|null $classEventDateTo {default null}
 * @property string|null $classEventDate {default null}
 * @property string|null $classEventTime {default null}
 * @property int|null $availableCapacity {default null}
 */
final class ClassItem extends OrderItem
{

	private function __construct()
	{
		parent::__construct();
	}

	public static function create(Order $order, Product $product, ?ClassEvent $classEvent, ?PriceLevel $priceLevel, int $amount = 1): ClassItem
	{
		$item = new self();
		$item->order = $order;
		$item->priceLevel = $priceLevel;
		$item->product = $product;
		$item->classEvent = $classEvent;
		$item->classEventName = $classEvent?->getName();
		$item->productName = $product->getLocalization($order->mutation)->getName();
		$item->classEventDateFrom = $classEvent?->from;
		$item->classEventDateTo = $classEvent?->to;
		$item->availableCapacity = $classEvent?->getAvailableCapacity();
		$item->classEventDate = $classEvent?->formatDate();
		$item->classEventTime = $classEvent?->formatHour();

		$item->amount = $amount;
		$item->unitPrice = Price::from($item->getCurrentUnitPrice());
		$item->vatRate = $item->getCurrentVatRate();
		$item->vatRateValue = $item->getVatRateValue();

		return $item;
	}

	public static function createFromErp(Order $order, ?ClassEvent $classEvent, string $variantName, string $variantCode, Price $unitPrice, VatRate $vatRate, BigDecimal $vatRateValue, int $amount = 1): ClassItem
	{
		return new self();
	}

	public function setAmount(int $amount): void
	{
		$amount = min($amount, $this->getMaxAvailableAmount());
		$this->amount = $amount;
	}

	public function getMaxAvailableAmount(): int
	{
		if ($this->classEvent === null) {
			return 999;
		}

		$availableAmount = $this->classEvent->getAvailableCapacity();

		/** @var ClassItem $classItem */
		foreach ($this->order->classEvents->toCollection()->findBy(['product' => $this->product, 'classEvent' => $this->classEvent]) as $classItem) {
			if ($classItem === $this) {
				continue;
			}
			$availableAmount -= $classItem->amount;
		}

		return $availableAmount;
	}

	public function getAvailabilityText(): TranslateData
	{
		$text = $this->getAvailability()->getAvailabilityText($this->amount);
		assert($text instanceof TranslateData);
		return $text;
	}

	public function getAvailabilityShortText(): TranslateData
	{
		$text = $this->getAvailability()->getAvailabilityShortText();
		assert($text instanceof TranslateData);
		return $text;
	}

	public function getAvailabilityType(): string
	{
		return $this->getAvailability()->getType();
	}

	public function getAvailabilityDeliveryDate(): null|DateTime
	{
		return $this->getAvailability()->getDeliveryDate(
			mutation: $this->order->mutation,
			state: $this->order->country,
			priceLevel: $this->order->priceLevel,
			currency: $this->order->currency,
			deliveryMethodConfiguration: null,
			quantityRequired: $this->amount
		)?->from;
	}

	public function getAvailability(): ProductAvailability
	{
		return $this->classEvent->product->productAvailability;
	}

	public function getCurrentUnitPrice(): Money
	{
		return $this->product->price(
			$this->order->mutation,
			$this->getPriceLevel(),
			$this->order->country,
		);
	}

	protected function getCurrentVatRate(): VatRate
	{
		return $this->product->vatRateType($this->order->country);
	}

	public function getName(): string
	{
		if ($this->productName !== null) {
			return $this->productName;
		}
		return $this->classEvent->product->getLocalization($this->order->mutation)->getName();
	}

	public function getDesc(): string
	{
		$return = [];
		if ($this->classEvent !== null) {
			$return[] = $this->classEvent->formatDate();
			$return[] = $this->classEvent->formatHour();
			$return[] = $this->classEvent->getName();
		}

		return implode(' ', $return);
	}

	public function hasPromotion(): PromotionItem|false
	{
		/** @var PromotionItem $promotion */
		foreach ($this->order->getPromotions() as $promotion) {
			if (in_array($this->getPersistedId(), (array) $promotion->promotionInfo->touchedProductItemIds)) {
				return $promotion;
			}
		}
		return false;
	}

	public function getProduct(): ?Product
	{
		return $this->product;
	}

	public function getIdentifier(): string
	{
		return ($this->product?->id ?? 0) . '_' . ($this->classEvent?->id ?? 0) . '_' . ($this->priceLevel?->id ?? 0);
	}

	public function getPriceLevel(): PriceLevel
	{
		return $this->priceLevel ?? $this->order->priceLevel;
	}

	public function hasRequalification(): bool
	{
		// TODO: refresh pricees
		return false;//$this->getPriceLevel()->id === PriceLevel::REQUALIFICATION_ID && $this->product?->getLocalization($this->order->mutation)->hasBuyableRequalificationPossibility($this->order->country);
	}

}
