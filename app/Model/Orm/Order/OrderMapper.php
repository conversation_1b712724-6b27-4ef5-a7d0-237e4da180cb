<?php

declare(strict_types = 1);

namespace App\Model\Orm\Order;

use App\Model\Orm\Product\Product;
use App\Model\Orm\Traits\HasCamelCase;
use App\Model\Security\User;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<Order>
 */
final class OrderMapper extends DbalMapper
{

	use HasCamelCase;

	/**
	 * @return literal-string
	 */
public function getTableName(): string
{
	return 'order';
}

	public function getByUsersProduct(Product $product, User $user): Order|null
	{
		if (!$user->getId()) {
			return null;
		}

		$builder = $this->builder()
			->select('o.id')
			->from('[order]', 'o')
			->joinLeft('order_product as op', 'op.orderId = o.id')
			->joinLeft('product_variant as pv', 'pv.id = op.variantId')
			->where('o.userId = %i', $user->getId())
			->andWhere('pv.productId = %i', $product->id);

		return $this->toEntity($builder);
	}

	public function getLastPlacedOrder(): ?Order
	{
		$builder = $this->builder()
			->where('[order.state] != %s', OrderState::Draft)
			->addOrderBy('[order.placedAt] DESC')
			->limitBy(1);

		return $this->toEntity($builder);
	}

}
