<?php

declare(strict_types=1);

namespace App\Model\Orm\Order;

use App\Exceptions\LogicException;
use App\Infrastructure\Latte\Filters;
use App\Model\Orm\BackedEnumWrapper; // phpcs:ignore
use App\Model\Orm\BigDecimalContainer; // phpcs:ignore
use App\Model\Orm\Order\Class\ClassItem;
use App\Model\Orm\Order\Gift\GiftItem;
use App\Model\Orm\Order\Product\ProductItem;
use App\Model\Orm\Order\Voucher\VoucherItem;
use App\Model\Orm\Price;
use App\Model\Orm\Product\Product;
use App\Model\Orm\State\State;
use App\Model\Orm\State\StateModel;
use App\Model\Orm\State\VatRate;
use App\Model\Orm\Traits\HasStaticCache;
use App\Model\Orm\Voucher\Voucher;
use App\Model\VatCalculator;
use Brick\Math\BigDecimal;
use Brick\Math\RoundingMode;
use Brick\Money\Money;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\Entity;
use function max;
use function min;

/**
 * @property Order $order
 * @property int $amount {default 1}
 * @property Price $unitPrice {embeddable}
 * @property Money $unitPriceVat {virtual}
 * @property Money $totalPrice {virtual}
 * @property Money $totalPriceVat {virtual}
 * @property Money $totalDiscountPrice {virtual}
 * @property Money $totalDiscountPriceVat {virtual}
 * @property bool $isLast {virtual}
 * @property VatRate $vatRate {wrapper BackedEnumWrapper}
 * @property BigDecimal|null $vatRateValue {wrapper BigDecimalContainer}
 */
abstract class OrderItem extends Entity
{

	use HasStaticCache;

	protected const MESSAGE_PRODUCT_SOLD_OUT = 'cart_message_product_sold_out';
	protected const MESSAGE_GIFT_REMOVED = 'cart_message_gift_removed';
	protected const MESSAGE_PRODUCT_SOLD_ENOUGH = 'cart_message_product_sold_enough';
	protected const MESSAGE_PRODUCT_VAT_CHANGED = 'cart_message_product_vat_changed';
	protected const MESSAGE_PRODUCT_PRICE_CHANGED = 'cart_message_product_price_changed';
	protected const MESSAGE_VOUCHER_REMOVED = 'cart_message_voucher_removed';
	protected const MESSAGE_CLASS_REQUALIFICATION_REMOVED = 'cart_message_class_requalification_removed';

	protected StateModel $stateModel;

	public function injectServices(StateModel $stateModel): void
	{
		$this->stateModel = $stateModel;
	}
	public function setAmount(int $amount): void
	{
		$amount = min($this->getMaxAvailableAmount(), max($amount, 1));
		$this->setReadOnlyValue('amount', $amount);
	}

	public function getterTotalPrice(): Money
	{
		return $this->unitPrice->asMoney(2)->multipliedBy($this->amount);
	}

	public function getterTotalPriceVat(): Money
	{
		return Price::from($this->unitPriceVat->multipliedBy($this->amount))->asMoney();
	}

	public function getterIsLast(): bool
	{
		return $this->order->products->toCollection()->orderBy('id', ICollection::DESC)->fetch() === $this;
	}

	public function getterTotalDiscountPrice(): Money
	{
		return $this->calculateTotalDiscountPrice(false); // @phpstan-ignore-line
	}

	public function getterTotalDiscountPriceVat(): Money
	{
		return $this->calculateTotalDiscountPrice(); // @phpstan-ignore-line
	}

	public function getterUnitPriceVat(): Money
	{
		return $this->getUnitPriceVat(2);
	}

	public function getUnitPriceVat(?int $precision = null): Money
	{
		$priceVat = Price::from(VatCalculator::priceWithVat($this->unitPrice->asMoney(4), $this->vatRate($this->order->country)));
		if ($precision !== null) {
			return $priceVat->asMoney($precision);
		}
		return $priceVat->asMoney();
	}

	public function getUnitPrice(?int $precision = null): Money
	{
		return $this->unitPrice->asMoney($precision);
	}

	public function getTotalPrice(?int $precision = null): Money
	{
		return $this->getUnitPrice($precision)->multipliedBy($this->amount);
	}

	public function getTotalPriceVat(?int $precision = null): Money
	{
		return $this->getUnitPriceVat($precision)->multipliedBy($this->amount);
	}

	public function hasDiscount(): bool
	{
		return $this->totalDiscountPriceVat->isLessThan($this->totalPriceVat);
	}

	/**
	 * @deprecated Unused in this version
	 */
	protected function calculateTotalDiscountPrice(bool $withVat = true): Money
	{
		return $this->loadCache($this->createCacheKey('discountPrice', $this->getPersistedId(), $withVat), function () use ($withVat) {
			$totalPriceVat = $withVat ? $this->totalPriceVat : $this->totalPrice;
			if (($voucher = $this->order->getAppliedVoucherItem()) !== null && $this instanceof ProductItem) {
				if (!$voucher->voucherCode->isProductInLimit($this->order, $this->variant->product->id)) {
					return $totalPriceVat;
				}
				if ($voucher->voucherCode->voucher->type === Voucher::TYPE_PERCENT) {
					$discountFraction = BigDecimal::of($voucher->voucherCode->voucher->discountPercent)->dividedBy(100, 2, RoundingMode::HALF_UP);
					$discount = $totalPriceVat->multipliedBy($discountFraction, RoundingMode::HALF_UP);

					if ($this->isLast) {
						$totalDiscountVat = $this->order->getTotalDiscountVat();
						/** @var ProductItem $product */
						foreach ($this->order->products->toCollection()->findBy(['id!=' => $this->id]) as $product) {
							$totalDiscountVat = $totalDiscountVat->minus($product->totalPriceVat->minus($product->totalDiscountPriceVat), RoundingMode::HALF_UP);
						}
						return $totalPriceVat->minus($totalDiscountVat);
					}

					return $totalPriceVat->minus($discount);
				} elseif ($voucher->voucherCode->voucher->type === Voucher::TYPE_AMOUNT) {
					$productCount = $this->order->products->count();
					$discount = VatCalculator::priceWithoutVat($voucher->voucherCode->voucher->discount->asMoney(), $voucher->voucherCode->vatRate($this->order->mutation, $this->order->country));
					$minus = $discount->dividedBy($productCount, RoundingMode::HALF_UP);

					if ($this->isLast) {
						$appliedCount = $productCount - 1;
						$appliedAmount = $minus->multipliedBy($appliedCount);

						$minus = $discount->minus($appliedAmount, RoundingMode::HALF_UP);
					}

					return $totalPriceVat->minus($minus);
				} //elseif ($voucher->voucherCode->voucher->type === Voucher::TYPE_FREE_DELIVERY) {
					// TODO: if used this method
				//}
			}

			return $totalPriceVat;
		});
	}

	/**
	 * A draft order should be refreshed on every request, to update prices and stock availability.
	 * Any item that is no longer available will set its amount to 0, so that UI code can render it as unavailable.
	 */
	public function refresh(bool $withRemove = true): ?RefreshResult
	{
		if ( ! $this->order->isDraft()) {
			throw new \LogicException('Cannot refresh OrderItem after order has been submitted.');
		}

		$vatRateValue = $this->getVatRateValue();
		if (!$this->vatRateValue?->isEqualTo($vatRateValue)) {
			$this->vatRateValue = $vatRateValue;
			$vatRateResult = new RefreshResult($this);
			$vatRateResult->updated = true;
			$vatRateResult->isGrouped = true;
			$vatRateResult->addNullMessage();
		}

		if ($this->vatRate !== ($vatRate = $this->getCurrentVatRate())) {
			$this->setReadOnlyValue('vatRate', $this->getCurrentVatRate());
			if (!isset($vatRateResult)) {
				$vatRateResult = new RefreshResult($this);
			}
			$vatRateResult->updated = true;
			$vatRateResult->isGrouped = true;
			$vatRateResult->addMessage(self::MESSAGE_PRODUCT_VAT_CHANGED, ['%product%' => $this->getName(), '%vatRate%' => $vatRate->value]);
		}

		$refreshResult = null;

		$this->setReadOnlyValue('unitPrice', Price::from($this->getCurrentUnitPrice()));

		$maxAmount = $this->getMaxAvailableAmount();
		if ($this->amount < 1 || $this->amount > $maxAmount) {
			$amount = min($maxAmount, max($this->amount, 1));
			$result = new RefreshResult($this);
			$result->availableAmount = $amount;

			if ($amount > 0) {
				$this->setReadOnlyValue('amount', $amount);
				$result->addMessage(self::MESSAGE_PRODUCT_SOLD_ENOUGH, ['%product%' => $this->getName()]);
				$result->updated = true;
			} else {
				if ($this::class === ProductItem::class) {
					$result->addMessage(self::MESSAGE_PRODUCT_SOLD_OUT, ['%product%' => $this->getName()], 'error');
					$result->isGrouped = true;
					if ($withRemove) {
						$this->order->removeProduct($this->variant);
					}
				} elseif ($this::class === GiftItem::class) {
					$result->addMessage(self::MESSAGE_GIFT_REMOVED, ['%gift%' => $this->getName()], 'error');
					if ($withRemove) {
						$this->order->removeGift($this->giftLocalization);
					}
				} elseif ($this::class === VoucherItem::class) {
					$result->addMessage(self::MESSAGE_VOUCHER_REMOVED, [
						'%voucher%' => $this->getName(),
						'%code%' => $this->voucherCode->code,
						'%reason%' => $this->voucherCode->canBeApplied($this->order),
						'%minOrderPrice%' => Filters::formatMoney(Price::from(Money::of($this->voucherCode->voucher->minPriceOrder ?? 0, $this->voucherCode->voucher->getCurrency()))->asMoney()),
					]);
					$this->order->removeVoucher($this->voucherCode);
				}
				$result->removed = true;
			}

			return $result;
		}

		if ($this instanceof ProductItem && $this->availabilityDeliveryDate?->getTimestamp() !== $this->getAvailabilityDeliveryDate()?->modify('midnight')->getTimestamp()) {
			//if ($refreshResult === null) {
				$refreshResult          = new RefreshResult($this);
				$refreshResult->updated = true;
				$refreshResult->addNullMessage();
			//}

			$this->availabilityType = $this->getAvailabilityType();
			$this->availabilityText = $this->getAvailabilityText();
			$this->availabilityShortText = $this->getAvailabilityShortText();
			$this->availabilityDeliveryDate = new DateTimeImmutable($this->getAvailabilityDeliveryDate()->format('Y-m-d H:i:s'));
		}

		// update persisted column name for product if necessary
		if ($this instanceof ProductItem && $this->variantName !== $this->getName()) {
			if ($refreshResult === null) {
				$refreshResult          = new RefreshResult($this);
				$refreshResult->updated = true;
				$refreshResult->addNullMessage();
			}
			$this->variantName = $this->getName();
		}

		// update persisted column name for gift if necessary
		if ($this instanceof GiftItem && $this->giftName !== $this->getName()) {
			if ($refreshResult === null) {
				$refreshResult          = new RefreshResult($this);
				$refreshResult->updated = true;
				$refreshResult->addNullMessage();
			}
			$this->giftName = $this->getName();
		}

		// update persisted column name for voucher if necessary
		if ($this instanceof VoucherItem && $this->voucherName !== $this->voucherCode->voucher->name) {
			if ($refreshResult === null) {
				$refreshResult          = new RefreshResult($this);
				$refreshResult->updated = true;
				$refreshResult->addNullMessage();
			}
			$this->voucherName = $this->voucherCode->voucher->name;
			$this->voucherCodeString = $this->voucherCode->code;
		}

		return $vatRateResult ?? $refreshResult;
	}

	protected function vatRate(?State $state = null): BigDecimal
	{
		if ($this->vatRateValue !== null) {
			return BigDecimal::of($this->vatRateValue);
		}
		$vatRateType = $this->vatRate;

		$stateId = $state ? $state->id : State::DEFAULT_ID;
		$vatRates = $this->stateModel->getAllVatRatesValues($this->order->mutation);
		$rate = $vatRates[$stateId]->get($vatRateType);

		if ($rate === null) {
			throw new LogicException('Unknown vatRate.');// nekonzistentni stav, ktery musime odhalit, nelze pouzit defaultni sazbu, prodavalo by se spatne a neprislo by se na to -> je to tak?
		}

		return $rate;
	}

	public function getVatRateValue(): BigDecimal
	{
		if ($this->getProduct() && ($this instanceof ProductItem || $this instanceof ClassItem)) {
			return $this->getProduct()->priceVatRate($this->order->mutation, $this->getPriceLevel(), $this->order->country);
		}
		return $this->vatRate($this->order->country);
	}

	abstract public function getMaxAvailableAmount(): int;

	abstract public function getCurrentUnitPrice(): Money;

	abstract protected function getCurrentVatRate(): VatRate;

	abstract public function getName(): string;
	abstract public function getDesc(): string;

	public function getProduct(): ?Product
	{
		return match ($this::class) {
			ProductItem::class => $this->variant->product,
			ClassItem::class => $this->product,
			default => null
		};
	}

}
