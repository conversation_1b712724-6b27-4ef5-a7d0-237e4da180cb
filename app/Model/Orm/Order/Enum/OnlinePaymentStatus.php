<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Enum;

use App\Model\Orm\Traits\EnumToArray;

enum OnlinePaymentStatus: string
{

	use EnumToArray; //phpcs:ignore

	case Created = 'CREATED'; // Platba založena
	case PaymentMethodChosen = 'PAYMENT_METHOD_CHOSEN'; // Platební metoda vybrána
	case Paid = 'PAID'; // Platba zaplacena
	case Authorized = 'AUTHORIZED'; // Platba předautorizována
	case Canceled = 'CANCELED'; // Platba zrušena
	case Timeouted = 'TIMEOUTED'; // Vypršelá platnost platby
	case Refunded = 'REFUNDED'; // refundována
	case PartiallyRefunded = 'PARTIALLY_REFUNDED'; // částečně refundována
	case Error = 'error';
	case PaidNextAttempt = 'PAID_NEXT_ATTEMPT'; // zaplacena na druhý pokus

}
