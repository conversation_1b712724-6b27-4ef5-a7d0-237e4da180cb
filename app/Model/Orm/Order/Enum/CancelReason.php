<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Enum;

use App\Model\Orm\Traits\EnumToArray;

enum CancelReason: string
{

	use EnumToArray; //phpcs:ignore

	case Default = 'Default'; // stornovaná na žiadosť zákazníka
	case ReturnedPackage = 'ReturnedPackage'; // neprevzatá zásielka
	case LostPackage = 'LostPackage'; // stratená zásielka
	case DamagedPackage = 'DamagedPackage'; // poškozené zboží
	case NotInStock = 'NotInStock'; // tovar nie je skladom
	case PaymentTimedOut = 'PaymentTimedOut'; // vypršal čas na online platbu
	case WithdrawContract = 'WithdrawContract'; // odstúpenie od zmluvy
	case ComplaintGoods = 'ComplaintGoods'; // reklamácia

}
