<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Enum;

use App\Model\Orm\Traits\EnumToArray;

enum OrderStatus: string
{

	use EnumToArray; //phpcs:ignore

	case New = 'new'; // nová
	case Waiting = 'waiting'; // čeká na zaplacení
	case Progress = 'progress'; // zpracovává se
	case Shipped = 'shipped'; // expedována
	case Done = 'done'; // vyřízená
	case Cancel = 'cancel'; // stornována
	case ReturnedBack = 'returned'; // vráceno dopravcem

}
