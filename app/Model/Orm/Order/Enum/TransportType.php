<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Enum;

use App\Model\Orm\Traits\EnumToArray;

enum TransportType: string
{

	use EnumToArray; //phpcs:ignore

	case Personal = 'personal';
	case Dpd = 'dpd';
	case DpdSk = 'dpdSk';
	case DpdEurope = 'dpdEurope';
	case Post = 'post';
	case PosteRestante = 'posteRestante';
	case Zasilkovna = 'zasilkovna';
	case PersonalSpecial = 'personalSpecial';
	case PplParcel = 'ppl_parcel';
	case Ppl = 'ppl';

}
