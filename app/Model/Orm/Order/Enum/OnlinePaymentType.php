<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Enum;

use App\Model\Orm\Traits\EnumToArray;

enum OnlinePaymentType: string
{

	use EnumToArray; //phpcs:ignore

	case Card = 'online';
	case ApplePay = 'onlineApplePay';
	case GooglePay = 'onlineGooglePay';
	case BankAccount = 'onlineBankAccount';

	/**
	 * @return array<OnlinePaymentType>
	 */
	public static function getAllTypes(): array
	{
		return [
			self::Card,
			self::ApplePay,
			self::GooglePay,
			self::BankAccount,
		];
	}

	/**
	 * @return array<string, string>
	 */
	public static function getGopayPaymentInstruments(): array
	{
		return [
			self::Card->value => 'PAYMENT_CARD',
			self::ApplePay->value => 'APPLE_PAY',
			self::GooglePay->value => 'GPAY',
			self::BankAccount->value => 'BANK_ACCOUNT',
		];
	}

	public function getGopayPaymentInstrument(): string
	{
		return match ($this) {
			self::Card => 'PAYMENT_CARD',
			self::ApplePay => 'APPLE_PAY',
			self::GooglePay => 'GPAY',
			self::BankAccount => 'BANK_ACCOUNT',
		};
	}

}
