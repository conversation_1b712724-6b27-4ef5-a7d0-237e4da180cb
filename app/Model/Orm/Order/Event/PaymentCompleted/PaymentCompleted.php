<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Event\PaymentCompleted;

use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\Payment\OrderPayment;
use Symfony\Contracts\EventDispatcher\Event;

final class PaymentCompleted extends Event
{

	public function __construct(
		public readonly OrderPayment $payment,
	)
	{
	}

	public function getOrder(): Order
	{
		return $this->payment->order;
	}

}
