<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Event\OrderPlaced\Subscriber;

use App\Model\Messenger\Erp\Order\OrderExportMessage;
use App\Model\Orm\Order\Event\OrderPlaced\OrderPlaced;
use App\Model\Orm\Order\Event\PaymentChanged\PaymentChanged;
use App\Model\Orm\Order\Event\PaymentCompleted\PaymentCompleted;
use App\Model\Orm\Order\Sync\OrderSyncType;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;

final readonly class SyncErp implements EventSubscriberInterface
{

	public function __construct(
		private MessageBusInterface $messageBus
	)
	{
	}

	public function __invoke(OrderPlaced|PaymentCompleted|PaymentChanged $event): void
	{
		$order = $event->getOrder();

		$syncType = match ($event::class) {
			default => OrderSyncType::Unexpected,
			OrderPlaced::class => OrderSyncType::OrderCreate,
			PaymentChanged::class => OrderSyncType::PaymentUpdate,
			PaymentCompleted::class => OrderSyncType::PaymentComplete,
		};

		$this->messageBus->dispatch(new OrderExportMessage($order->getPersistedId(), $syncType));
	}


	public static function getSubscribedEvents(): array
	{
		return [
			OrderPlaced::class => [
				['__invoke', 40],
			],
			PaymentChanged::class => [
				['__invoke', 10],
			],
			PaymentCompleted::class => [
				['__invoke', 10],
			],
		];
	}

}
