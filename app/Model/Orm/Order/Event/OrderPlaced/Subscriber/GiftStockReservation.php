<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Event\OrderPlaced\Subscriber;

use App\Model\ElasticSearch;
use App\Model\Orm\Order\Event\OrderPlaced\OrderPlaced;
use App\Model\Orm\Orm;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

final readonly class GiftStockReservation implements EventSubscriberInterface
{

	public function __construct(
		private Orm $orm,
		private ElasticSearch\Product\Facade $productEsFacade,
		private ElasticSearch\Product\ConvertorProvider $convertorProvider,
	)
	{
	}

	public function __invoke(OrderPlaced $orderCreatedEvent): void
	{
		$order = $orderCreatedEvent->getOrder();

		foreach ($order->gifts as $giftItem) {
			if (($product = $giftItem->giftLocalization->gift->product) === null) {
				continue;
			}

			$productVariant = $product->firstVariant;
			$product = $product;
			$minus = 0;
			foreach ($productVariant->supplies as $variantSupply) {
				$amount = ($giftItem->amount - $minus);

				if ($variantSupply->amount >= $amount) {
					$variantSupply->amount -= $amount;
					$this->orm->supply->persist($variantSupply);
					break;
				} else {
					$minus = $variantSupply->amount;
					$variantSupply->amount = 0;
					$this->orm->supply->persist($variantSupply);
				}
			}

			// flush runtime cache
			$product->flushCache();
			$productVariant->flushCache();

			// save products to elastic
			$this->productEsFacade->updateAllMutationsNow($product, $this->getElasticSearchConvertors());
		}
	}

	private function getElasticSearchConvertors(): array
	{
		$convertors = [];
		$convertors[] = $this->convertorProvider->get(ElasticSearch\Product\Convertor\StoreData::class);
		$convertors[] = $this->convertorProvider->get(ElasticSearch\Product\Convertor\AvailabilityData::class);

		return $convertors;
	}

	public static function getSubscribedEvents(): array
	{
		return [
			OrderPlaced::class => [
				['__invoke', 25],
			],
		];
	}

}
