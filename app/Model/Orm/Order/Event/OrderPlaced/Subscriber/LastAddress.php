<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Event\OrderPlaced\Subscriber;

use App\Model\Orm\Order\Event\OrderPlaced\OrderPlaced;
use App\Model\Orm\Orm;
use Nette\InvalidStateException;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

final readonly class LastAddress implements EventSubscriberInterface
{

	public function __construct(
		private Orm $orm,
	)
	{
	}

	public function __invoke(OrderPlaced $orderCreatedEvent): void
	{
		$order = $orderCreatedEvent->getOrder();
		try {
			$user = $order->user;

			if ($user === null) {
				throw new InvalidStateException('User is not registered.');
			}

			$orderAddress = [
				'invName'    => $order->name,
				'invStreet'  => $order->street,
				'invCity'    => $order->city,
				'invZip'     => $order->zip,
				'invState'   => $order->country->id,
				'invCompany' => $order->companyName,
				'invIc'      => $order->companyIdentifier,
				'invDic'     => $order->vatNumber,
				'invPhone'   => $order->phone,
				'delName'    => $order->delivery->information->name ?? null,
				'delStreet'  => $order->delivery->information->street ?? null,
				'delCity'    => $order->delivery->information->city ?? null,
				'delZip'     => $order->delivery->information->zip ?? null,
				'delState'   => $order->delivery->information->country?->id ?? null,
				'delCompany' => $order->delivery->information->company ?? null,
				'delPhone'   => $order->delivery->information->phoneNumber ?? null,
			];

			$addresses = [];
			foreach ($user->customAddress as $address) {
				$address            = (array) $address;
				$address['invName'] = trim($address['invFirstname'] . ' ' . $address['invLastname']);
				$address['delName'] = null;
				if ($address['delFirstname'] !== null) {
					$address['delName'] = trim($address['delFirstname'] . ' ' . $address['delLastname']);
				}

				$invFirstname = $address['invFirstname'];
				$invLastname = $address['invLastname'];
				$delFirstname = $address['delFirstname'];
				$delLastname = $address['delLastname'];

				unset($address['invFirstname'], $address['invLastname'], $address['delFirstname'], $address['delLastname'], $address['last']);

				$address['last'] = false;

				if (array_diff($orderAddress, $address) === []) {
					$address['last'] = true;
				}

				$address['invFirstname'] = $invFirstname;
				$address['invLastname'] = $invLastname;
				$address['delFirstname'] = $delFirstname;
				$address['delLastname'] = $delLastname;

				unset($address['invName'], $address['delName']);

				$addresses[] = $address;
			}

			$user->customAddress = $addresses;

			$this->orm->user->persist($user);
		} catch (\Throwable) {
			// do nothing
		}
	}

	public static function getSubscribedEvents(): array
	{
		return [
			OrderPlaced::class => [
				['__invoke', 30],
			],
		];
	}

}
