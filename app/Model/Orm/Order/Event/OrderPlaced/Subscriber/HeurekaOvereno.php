<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Event\OrderPlaced\Subscriber;

use App\Model\Messenger\Default\HeurekaOvereno\HeurekaOverenoMessage;
use App\Model\Orm\Order\Event\OrderPlaced\OrderPlaced;
use <PERSON>ymfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;

final readonly class <PERSON>urekaOvereno implements EventSubscriberInterface
{

	public function __construct(
		private MessageBusInterface $messageBus
	)
	{
	}

	public function __invoke(OrderPlaced $event): void
	{
		$order = $event->getOrder();
		if ($order->heurekaSend) {
			$this->messageBus->dispatch(new HeurekaOverenoMessage($order->getPersistedId()));
		}
	}

	public static function getSubscribedEvents(): array
	{
		return [
			OrderPlaced::class => [
				['__invoke', 50],
			],
		];
	}

}
