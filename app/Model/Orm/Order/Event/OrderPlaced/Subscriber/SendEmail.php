<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Event\OrderPlaced\Subscriber;

use App\Model\Email\CommonFactory;
use App\Model\Orm\Order\Event\OrderPlaced\OrderPlaced;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use <PERSON>\Debugger;
use <PERSON>\ILogger;
use App\Model\Link\LinkFactory;

final readonly class SendEmail implements EventSubscriberInterface
{

	public function __construct(
		private CommonFactory $mailFactory,
		private LinkFactory $linkFactory,
	)
	{
	}

	public function __invoke(OrderPlaced $orderCreatedEvent): void
	{
		$order = $orderCreatedEvent->getOrder();

		$mailer = $this->mailFactory->create();

		$data = [
			'NUMBER' => $order->orderNumber,
			'ORDER_URL' => $this->linkFactory->linkTranslateToNette($order->mutation->pages->userOrderHistory, ['orderHash' => $order->hash]),
			'order' => $order,
		];

		try {
			$mailer->send(null, $order->email, 'order', $data);
		} catch (\Throwable $e) {
			Debugger::log($e, ILogger::EXCEPTION);
		}
	}


	public static function getSubscribedEvents(): array
	{
		return [
			OrderPlaced::class => [
				['__invoke', 10],
			],
		];
	}

}
