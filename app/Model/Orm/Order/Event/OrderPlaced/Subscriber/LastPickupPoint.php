<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Event\OrderPlaced\Subscriber;

use App\Model\Orm\Order\Event\OrderPlaced\OrderPlaced;
use App\Model\Orm\Orm;
use Nette\InvalidStateException;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

final readonly class LastPickupPoint implements EventSubscriberInterface
{

	public function __construct(
		private Orm $orm,
	)
	{
	}

	public function __invoke(OrderPlaced $orderCreatedEvent): void
	{
		$order = $orderCreatedEvent->getOrder();
		try {
			$user = $order->user;

			if ($user === null) {
				throw new InvalidStateException('User is not registered.');
			}

			$cf = $user->customFieldsJson;

			$deliveryMethod = $order->getDelivery()->deliveryMethod->deliveryMethodUniqueIdentifier;
			$pickupPointId = $order->getDelivery()->information->pickupPointId ?? null;

			if (!isset($cf->pickupPoints[0])) {
				$cf->pickupPoints[0] = new \stdClass();
			}

			$cf->pickupPoints[0]->{$deliveryMethod} = (string) $pickupPointId;
			$user->setCf($cf);

			$this->orm->persistAndFlush($user);

		} catch (\Throwable $e) {
			// do nothing
		}
	}

	public static function getSubscribedEvents(): array
	{
		return [
			OrderPlaced::class => [
				['__invoke', 35],
			],
		];
	}

}
