<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Event\OrderPlaced\Subscriber;

use App\Model\Orm\Order\Event\OrderPlaced\OrderPlaced;
use App\Model\Orm\Orm;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

final readonly class VoucherDeactivate implements EventSubscriberInterface
{

	public function __construct(
		private Orm $orm
	)
	{
	}


	public function __invoke(OrderPlaced $orderCreatedEvent): void
	{
		$order = $orderCreatedEvent->getOrder();
		foreach ($order->vouchers as $voucherItem) {
			$voucherCode = $voucherItem->voucherCode;

			if (!$voucherCode->voucher->reuse) {
				$voucherCode->isUsed = 1;
				$voucherCode->usedAt = new DateTimeImmutable();

				$this->orm->voucherCode->persist($voucherCode);
			}

		}
	}

	public static function getSubscribedEvents(): array
	{
		return [
			OrderPlaced::class => [
				['__invoke', 15],
			],
		];
	}

}
