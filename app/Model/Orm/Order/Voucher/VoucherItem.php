<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Voucher;

use App\Infrastructure\Latte\Filters;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderItem;
use App\Model\Orm\Price;
use App\Model\Orm\State\VatRate;
use App\Model\Orm\Voucher\Voucher;
use App\Model\Orm\VoucherCode\VoucherCode;
use App\Model\TranslateData;
use Brick\Math\BigDecimal;
use Brick\Money\Money;

/**
 * @property-read int $id {primary}
 * @property Order $order {m:1 Order::$vouchers}
 * @property VoucherCode|null $voucherCode {m:1 VoucherCode::$orderItems}
 * @property string|null $voucherName {default null}
 * @property string|null $voucherCodeString {default null}
 * @property string|null $voucherType {default null}
 */
final class VoucherItem extends OrderItem
{

	private function __construct()
	{
		parent::__construct();
	}

	public function getName(): string
	{
		if ($this->voucherName !== null) {
			$return = $this->voucherName;
			if ($this->voucherCodeString !== null) {
				$return .= ' [' . $this->voucherCodeString . ']';
			}
			return $return;
		}
		return $this->voucherCode->getName();
	}
	public function getDesc(): string
	{
		return '';
	}
	public function getCode(): string
	{
		if ($this->voucherCodeString !== null) {
			return $this->voucherCodeString;
		}
		return $this->voucherCode->code;
	}

	public function getTitle(): string
	{
		if ($this->voucherCode->voucher->type === Voucher::TYPE_AMOUNT_COMBINATION) {
			return 'voucher_certificate';
		}
		return 'voucher_discount';
	}

	public function getVoucherValue(): \Stringable|string
	{
		if ($this->voucherCode->voucher->type === Voucher::TYPE_FREE_DELIVERY) {
			return new TranslateData('voucher_free_delivery');
		}

		if (($discountPercent = $this->voucherCode->voucher->discountPercent) !== null) {
			return sprintf('%d %%', $discountPercent);
		}

		if ($this->voucherCode->voucher->discount->amount !== null) {
			return Filters::formatMoney($this->voucherCode->voucher->discount->asMoney());
		}

		return 'unknown';
	}

	public static function create(Order $order, VoucherCode $voucherCode): VoucherItem
	{
		$item = new self();
		$item->order = $order;
		$item->voucherCode = $voucherCode;
		$item->amount = 1;
		$item->unitPrice = Price::from($item->getCurrentUnitPrice());
		$item->vatRate = $item->getCurrentVatRate();
		$item->vatRateValue = $item->getVatRateValue();
		$item->voucherName = $voucherCode->voucher->name;
		$item->voucherCodeString = $voucherCode->code;
		$item->voucherType = $voucherCode->voucher->type;

		return $item;
	}

	public static function createFromErp(Order $order, ?VoucherCode $voucherCode, string $voucherName, ?string $voucherCodeString, string $voucherType, Price $unitPrice, VatRate $vatRate, BigDecimal $vatRateValue, int $amount = 1): VoucherItem
	{
		$item = new self();
		$item->order = $order;
		$item->voucherCode = $voucherCode;
		$item->voucherName = $voucherName;
		$item->voucherCodeString = $voucherCodeString;
		$item->amount = $amount;
		$item->unitPrice = $unitPrice;
		$item->vatRate = $vatRate;
		$item->vatRateValue = $vatRateValue;
		$item->voucherType = $voucherType;

		return $item;
	}

	public function getMaxAvailableAmount(): int
	{
		return $this->voucherCode->getMaxAmount($this->order);
	}

	public function getCurrentUnitPrice(): Money
	{
		return $this->voucherCode->getDiscount($this->order);
	}

	protected function getCurrentVatRate(): VatRate
	{
		return $this->voucherCode->voucher->getVatRate($this->order->country);
	}

}
