<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\PickupPoint;

use Nextras\Dbal\Connection;

final readonly class PickupPointModel
{

	public function __construct(
		private Connection $connection,
	)
	{
	}


	public function getClosestPlaces(array $postData, ?int $deliveryMethodId = null, ?int $stateId = null, bool $onlyMarkers = false): array
	{
		$queryBuilder = $this->connection->createQueryBuilder();

		$queryBuilder->from('pickup_point');

		if (($deliveryMethodId) !== null) {
			$queryBuilder->andWhere('deliveryMethodId = %i', $deliveryMethodId);
		}

		if (($stateId) !== null) {
			$queryBuilder->andWhere('stateId = %i', $stateId);
		}

		if (isset($postData['searchTerm'])) {
			$queryBuilder->andWhere('name LIKE %_like_ OR address LIKE %_like_', $postData['searchTerm'], $postData['searchTerm']);
		} elseif (isset($postData['lat']) && isset($postData['lng'])) {
			$maxDistance = (int) ($postData['maxDistance'] ?? 20000);

			$lon = floatval($postData['lng']);
			$lat = floatval($postData['lat']);

			if ($lon < -180.0 || $lon > 180.0 || $lat < -90.0 || $lat > 90.0) {
				return [];
			}

			if (isset($postData['bounds'])) {
				$postData['bounds'] = json_decode($postData['bounds'], true);

				if (is_array($postData['bounds']) && count($postData['bounds']) === 2) {
					$northEast = $postData['bounds'][0];
					$southWest = $postData['bounds'][1];
					if (
						isset($northEast['lat'], $northEast['lng'], $southWest['lat'], $southWest['lng']) &&
						is_numeric($northEast['lat']) && is_numeric($northEast['lng']) &&
						is_numeric($southWest['lat']) && is_numeric($southWest['lng'])
					) {
						$queryBuilder->andWhere('lat BETWEEN %f AND %f', floatval($southWest['lat']), floatval($northEast['lat']));
						$queryBuilder->andWhere('lng BETWEEN %f AND %f', floatval($southWest['lng']), floatval($northEast['lng']));
						$queryBuilder->limitBy(100);
					} else {
						return [];
					}
				} else {
					return [];
				}
			} else {
				$queryBuilder->andWhere('ST_Distance_Sphere(point(lat, lng),point(%f, %f)) < %i', $lat, $lon, $maxDistance);
				$queryBuilder->orderBy('ST_Distance_Sphere(point(lat, lng),point(%f, %f))', $lat, $lon);
			}
		} else {
			//$queryBuilder->limitBy(500)->orderBy('name ASC');
			if (!$onlyMarkers) {
				$queryBuilder->orderBy('name ASC')->limitBy(500);
			}

		}

		return $this->connection->queryByQueryBuilder($queryBuilder)->fetchAll();
	}

}
