<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\PickupPoint;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\DeliveryMethod\DeliveryMethodConfiguration;
use App\Model\Orm\JsonArrayContainer;// phpcs:ignore


/**
 * @property-read int $id {primary}
 * @property int $extId
 * @property string $name
 * @property string|null $address
 * @property string|null $lat
 * @property string|null $lng
 * @property array $openingHours {container JsonArrayContainer}
 * @property string|null $image
 *
 * @property DeliveryMethodConfiguration $deliveryMethod {1:1 DeliveryMethodConfiguration, isMain=true, oneSided=true}
 */
final class PickupPoint extends BaseEntity
{

	public function asArray(): array
	{
		return [
			'id' => $this->id,
			'typeId' => $this->deliveryMethod->id,
			'name' => $this->name,
			'address' => $this->address,
			'position' => [
				'lat' => (float) $this->lat,
				'lng' => (float) $this->lng,
			],
			'openingHours' => $this->openingHours,
			'image' => $this->image,
		];
	}

	public function asArrayMarker(): array
	{
		return [
			'id' => $this->id,
			'typeId' => $this->deliveryMethod->id,
			'position' => [
				'lat' => (float) $this->lat,
				'lng' => (float) $this->lng,
			],
		];
	}

}
