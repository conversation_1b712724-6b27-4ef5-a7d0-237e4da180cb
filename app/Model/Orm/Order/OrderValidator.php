<?php

declare(strict_types=1);

namespace App\Model\Orm\Order;

/**
 * Validators are meant to check various project-specific constraints, such as minimum order total price,
 * combinations of delivery and payment, etc. The order should be validated as it is being placed, and the submission
 * must be prevented if any of the validators reports violations.
 */
interface OrderValidator
{

	/**
	 * todo maybe this should return a list of violated constraints?
	 */
	public function isValid(Order $order): bool;

}
