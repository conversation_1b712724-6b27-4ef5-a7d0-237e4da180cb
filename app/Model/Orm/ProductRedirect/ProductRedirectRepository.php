<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductRedirect;

use App\Model\Orm\Product\Product;
use App\Model\Security\User;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @extends Repository<ProductRedirect>
 */
final class ProductRedirectRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [ProductRedirect::class];
	}

}
