<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductRedirect;

use App\Model\Orm\Product\Product;
use App\Model\Orm\Traits\HasCamelCase;
use App\Model\Security\User;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<ProductRedirect>
 */
final class ProductRedirectMapper extends DbalMapper
{

	use HasCamelCase;

	/**
* @return literal-string
*/
public function getTableName(): string
{
	return 'product_redirect';
}

}
