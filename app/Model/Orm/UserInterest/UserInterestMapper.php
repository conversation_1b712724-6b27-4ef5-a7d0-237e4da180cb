<?php

declare(strict_types=1);

namespace App\Model\Orm\UserInterest;

use App\Model\Orm\Traits\HasCamelCase;
use App\Model\Orm\User\User;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<UserInterest>
 */
final class UserInterestMapper extends DbalMapper
{
	use HasCamelCase;
	/**
	* @return literal-string
	*/
	public function getTableName(): string
	{
		return 'user_interest';
	}
}
