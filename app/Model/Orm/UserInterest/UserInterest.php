<?php

declare(strict_types=1);

namespace App\Model\Orm\UserInterest;

use App\Model\Orm\JsonContainer; // phpcs:ignore
use App\Model\Orm\User\User;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property string $uid
 * @property string $name
 * @property bool $public {default false}
 * @property string $interest
 */
class UserInterest extends Entity
{
}
