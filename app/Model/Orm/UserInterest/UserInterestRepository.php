<?php

declare(strict_types=1);

namespace App\Model\Orm\UserInterest;

use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @extends Repository<UserInterest>
 */
final class UserInterestRepository extends Repository
{
	static function getEntityClassNames(): array
	{
		return [UserInterest::class];
	}

	/**
	 * @return ICollection<UserInterest>
	 */
	public function getInterests(): ICollection
	{
		return $this->findBy(['public' => true])->orderBy('name');
	}

}
