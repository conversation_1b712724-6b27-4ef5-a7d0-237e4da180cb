<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductProduct;

use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductRepository;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\ProductLocalization\ProductLocalizationRepository;
use App\PostType\Page\Model\Orm\Tree;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\IMapper;
use Nextras\Orm\Repository\IDependencyProvider;
use Nextras\Orm\Repository\Repository;

/**
 * @method ProductProduct getById($id)
 *
 * @extends Repository<ProductProduct>
 */
final class ProductProductRepository extends Repository
{

	public function __construct(
		IMapper $mapper,
		IDependencyProvider|null $dependencyProvider,
		private readonly ProductRepository $productRepository,
		private readonly ProductLocalizationRepository $productLocalizationRepository,
	)
	{
		parent::__construct($mapper, $dependencyProvider);
	}

	public static function getEntityClassNames(): array
	{
		return [ProductProduct::class];
	}
	/**
	 * @return ICollection<Product>
	 */
	public function findForMainProduct(Product $mainProduct, mixed $type): ICollection
	{
		return $this->productRepository->findBy([
			'attachedProductProducts->type' => $type,
			'attachedProductProducts->mainProduct' => $mainProduct,
		]);
	}
	/**
	 * @return ICollection<ProductLocalization>
	 */
	public function findForTree(Tree $tree, mixed $type): ICollection
	{
		return $this->productLocalizationRepository->findBy([
			'treeProducts->type' => $type,
			'treeProducts->tree' => $tree,
		]);
	}


	public function replace(Product $product, Product $attachedProduct, string $type, int $sort): ProductProduct
	{
		if (!$productProduct = $this->getByReferenceAndType($product, $attachedProduct, $type)) {
			$productProduct = $this->createNew();
		}
		return $this->update($productProduct, $product, $attachedProduct, $sort, $type);
	}


	public function getByReferenceAndType(Product $product, Product $attachedProduct, string $type): ?ProductProduct
	{
		return $this->getBy([
			'type' => $type,
			'mainProduct' => $product,
			'attachedProduct' => $attachedProduct,
		]);
	}


	public function createNew(): ProductProduct
	{
		$newProductProduct = new ProductProduct();
		$this->attach($newProductProduct);

		return $newProductProduct;

	}

	public function update(ProductProduct $productProduct, Product $product, Product $attachedProduct, int $sort, string $type): ProductProduct
	{
		$productProduct->type = $type;
		$productProduct->mainProduct = $product;
		$productProduct->attachedProduct = $attachedProduct;
		$productProduct->sort = $sort;
		return $productProduct;
	}

}
