<?php

declare(strict_types=1);

namespace App\Model\Orm\Voucher;

use App\Model\Orm\BigDecimalContainer; // phpcs:ignore
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Price;
use App\Model\Orm\State\State;
use App\Model\Orm\State\VatRate;
use App\Model\Orm\Traits\HasFormDefaultData;
use App\Model\Orm\VoucherCode\VoucherCode;
use App\Model\Orm\VoucherLimit\VoucherLimit;
use App\PostType\Core\Model\Publishable;
use Brick\Math\BigDecimal;
use Brick\Money\Currency;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property-read int $id {primary}
 * @property int $synced {default 0}
 * @property null|string $syncChecksum {default null}
 * @property string $name {default ''}
 * @property string $internalName {default ''}
 * @property string $type {enum self::TYPE_*} {default self::TYPE_AMOUNT}
 * @property BigDecimal|null $minPriceOrder {wrapper BigDecimalContainer}
 * @property int $reuse {default 0}
 * @property int $combination {default 0}
 * @property string|null $combinationType {enum self::TYPE_*} {default null}
 * @property int $public {default 0}
 * @property Price $discount {embeddable}
 * @property int|null $discountPercent {default null}
 *
 * @property int|null $created
 * @property int|null $edited
 * @property DateTimeImmutable|null $publicTo {default '+100 year'}
 * @property DateTimeImmutable|null $publicFrom {default 'now'}
 * @property DateTimeImmutable|null $createdTime {default 'now'}
 * @property DateTimeImmutable|null $editedTime
 *
 * RELATIONS
 * @property OneHasMany<VoucherCode> $codes {1:m VoucherCode::$voucher, cascade=[persist, remove]}
 * @property Mutation $mutation {m:1 Mutation, oneSided=true}
 * @property OneHasMany<VoucherLimit> $limits {1:m VoucherLimit::$voucher, cascade=[persist, remove]}
 *
 *
 * VIRTUAL
 * @property-read bool $isActive {virtual}
 * @property-read bool $isExpired {virtual}
 * @property-read bool $isUsed {virtual}
 * @property-read bool $isInOrder {virtual}
 */
final class Voucher extends Entity implements Publishable
{
	use HasFormDefaultData;
	public const TYPE_AMOUNT = 'amount';
	public const TYPE_PERCENT = 'percent';
	public const TYPE_AMOUNT_COMBINATION = 'amountCombination';
	public const TYPE_FREE_DELIVERY = 'freeDelivery';

	public function getterIsActive(): bool
	{
		return $this->getIsPublic() && !$this->isExpired;
	}

	public function getterIsExpired(): bool
	{
		$now = new DateTimeImmutable();
		return !($this->publicFrom->getTimestamp() <= $now->getTimestamp() && $this->publicTo->getTimestamp() >= $now->getTimestamp());
	}

	public function getterIsUsed(): bool
	{
		return $this->codes->toCollection()->findBy(['isUsed' => 1])->countStored() > 0;
	}

	public function getterIsInOrder(): bool
	{
		/** @var VoucherCode $voucherCode */
		foreach ($this->codes->toCollection() as $voucherCode) {
			if ($voucherCode->isInOrder) {
				return true;
			}
		}
		return false;
	}

	public function getCurrency(): Currency
	{
		return Currency::of($this->discount->currency);
	}

	public function getVatRate(State $state/*, Order $order*/): VatRate
	{
		return VatRate::None; // todo
	}

	public function getIsPublic(): bool
	{
		//
		return (bool) $this->public;// && $andCondition;
	}

	public function setIsPublic(bool $isPublic): void
	{
		$this->public = (int) $isPublic;
	}
}
