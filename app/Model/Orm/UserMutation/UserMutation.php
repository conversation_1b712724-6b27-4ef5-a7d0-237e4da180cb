<?php declare(strict_types = 1);

namespace App\Model\Orm\UserMutation;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\User\User;
use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property bool $newsletter {default 0}
 *
 * RELATIONS
 * @property User $user {m:1 User::$userMutations}
 * @property Mutation $mutation {m:1 Mutation::$userMutations}
 *
 * VIRTUAL
 */
class UserMutation extends Entity
{

}
