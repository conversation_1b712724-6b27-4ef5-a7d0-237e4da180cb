<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductVariant;


use App\Model\Orm\BaseEntity;
use App\Model\Orm\JsonContainer; // phpcs:ignore
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductFile\ProductFile;
use App\Model\Orm\ProductImage\ProductImage;
use App\Model\Orm\ProductReview\ProductReview;
use App\Model\Orm\ProductTree\ProductTree;
use App\Model\Orm\ProductVariant\Availability\AbstractProductAvailability;
use App\Model\Orm\ProductVariant\Availability\ProductAvailability;
use App\Model\Orm\ProductVariantLocalization\ProductVariantLocalization;
use App\Model\Orm\ProductVariantPrice\ProductVariantPrice;
use App\Model\Orm\ProductVariantPriceLog\ProductVariantPriceLog;
use App\Model\Orm\State\State;
use App\Model\Orm\Stock\Stock;
use App\Model\Orm\Stock\StockModel;
use App\Model\Orm\Supply\Supply;
use App\Model\Orm\Supply\SupplyModel;
use App\Model\Orm\Synchronizable;
use App\Model\Orm\Traits\HasCache;
use App\Model\Orm\Traits\HasTranslator;
use App\Model\Price\ProductVariantPriceModel;
use App\Model\PriceInfo;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\Tree;
use App\Model\VatCalculator;
use ArrayIterator;
use Brick\Math\BigDecimal;
use Brick\Money\Money;
use Nette\Utils\ArrayHash;
use Nette\Utils\Strings;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Relationships\IRelationshipCollection;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property int $id {primary}
 * @property string|null $ean
 * @property string|null $code
 * @property DateTimeImmutable|null $created {default 'now'}
 * @property int|null $createdBy
 * @property DateTimeImmutable|null $edited {default 'now'} last ERP sync
 * @property int|null $editedBy last ERP sync
 * @property int $sort {default 0}
 * @property int $soldCount {default 0}
 * @property int|null $isInDiscount
 * @property bool $isOld {default false}
 * @property bool $isReSale {default false} uvedena znovu do prodeje
 * @property bool $isForRevaluation {default false} urcena k preceneni
 * @property bool $isSubscription {default false} moznost prodeje produktu formou predplatneho
 * @property int|null $extId
 * @property DateTimeImmutable|null $minExpriration
 * @property int|null $weight {default null}
 * @property string|null $weightUnit {default null}
 * @property bool|null $isFreeTransportForced {default false}
 * @property bool|null $isFreeTransport {default false}
 * @property DateTimeImmutable|null $freeTransportForcedFrom
 * @property DateTimeImmutable|null $freeTransportForcedTo
 * @property float|null $margin {default null}
 * @property float|null $marginMin {default null}
 * @property string $usePrice {enum self::USE_PRICE_*} {default self::USE_PRICE_CATALOG}
 *
 * RAW ERP data
 * @property string|null $erpName {default null}
 * @property string|null $erpContent {default null}
 * @property string|null $erpDescription {default null}
 * @property string|null $erpCategoryIds {default null}
 * @property string|null $erpCategoryPath {default null}
 * @property float|null $erpVat {default null}
 * @property float|null $erpNewPurchasePrice {default null} nova nakupni cena, ktera je nizsi, nez aktualni
 *
 * @property int|null $tempImageId ID Image pro pozdejsi pripojení k obalce (import varianty bez produktu)
 * @property int|null $tempBrandParameterValueId ID ParameterValue parametru Znacka pro pozdejsi pripojení k obalce (import varianty bez produktu)
 * @property ArrayHash|null $tempParameters {container JsonContainer}
 *
 *
 * RELATIONS
 * @property Product|null $product {m:1 Product::$variants}
 * @property ParameterValue|null $param1Value {m:1 ParameterValue::$variants1}
 * @property ParameterValue|null $param2Value {m:1 ParameterValue::$variants2}
 * @property OneHasMany<Supply> $supplies {1:m Supply::$variant, cascade=[persist, remove]}
 * @property OneHasMany<ProductVariantLocalization> $variantLocalizations {1:m ProductVariantLocalization::$variant, orderBy=[id=ASC], cascade=[persist, remove]}
 * @property OneHasMany<ProductVariantPrice> $prices {1:m ProductVariantPrice::$productVariant, cascade=[persist, remove]}
 * @property OneHasMany<ProductVariantPriceLog> $priceLogs {1:m ProductVariantPriceLog::$productVariant, cascade=[persist, remove]}
 *
 * VIRTUAL
 * @property-read string|null $cf {virtual}
 * @property-read array|null $path {virtual}
 * @property-read string $stringId {virtual}
 * @property-read string $name {virtual}
 * @property-read string $nameAnchor {virtual}
 * @property-read string $nameTitle {virtual}
 * @property-read string $nameVariant {virtual}
 * @property-read string $nameVariantFormat {virtual}
 * @property-read string|null $content {virtual}
 *
 * @property-read int|null $param1Id {virtual}
 * @property-read int|null $param2Id {virtual}
 * @property-read int|null $param1ValueId {virtual}
 * @property-read int|null $param2ValueId {virtual}
 * @property-read int|null $paramValueIds {virtual}
 * @property-read string|null $template {virtual}
 * @property-read string|null $keywords {virtual}
 * @property-read string|null $description {virtual}
 * @property-read string|null $annotation {virtual}
 * @property-read ProductImage|null $firstImage {virtual}
 * @property-read string|null $uid {virtual}
 *
 * @property-read array $pricesByLevelCurrency {virtual}
 * @property-read array $pricesByLevelName {virtual}
 *
 * @property-read bool $isVariant {virtual}
 * @property-read bool $isShell {virtual}
 *
 * @property-read bool $isInPrepare {virtual}
 * @property-read bool $notSoldSeparately {virtual}
 *
 * @property-read array $suppliesByStock {virtual} sklady strukturovane podle ID
 * @property-read array $suppliesByStockEntity {virtual} sklady (entity) strukturovane podle ID
 * @property-read array $suppliesByStockAlias {virtual} sklady strukturovane podle aliasu skladu
 * @property-read bool $isInStock {virtual} globalne skladem
 * @property-read bool $isInStockDefault {virtual} skladem na centralnim skladu
 * @property-read bool $isInStockSupplier {virtual} skladem u dodavatelu
 * @property-read int $totalSupplyCount {virtual} pocet skladem celkem
 * @property-read int $suplyCountStockDefault {virtual} pocet skladem na centralnim skladu
 * @property-read int $suplyCountStockSupplier {virtual} pocet skladem u dodavatelu
 *
 * @property OneHasMany<ProductReview> $reviews {virtual}
 * @property OneHasMany<ProductFile> $files {virtual}
 * @property OneHasMany<ProductTree> $productTrees {virtual}
 * @property-read ManyHasMany<ParameterValue> $parametersValues {virtual}
 * @property-read Tree[]|null $pages {virtual}
 * @property-read Product[]|null $products {virtual}
 * @property-read ArrayIterator<int, ProductImage> $images {virtual}
 *
 *
 * @property-read string $cacheId {virtual}
 * @property-read ICollection<Product> $accessories {virtual}
 * @property-read CatalogTree|null $mainCategory {virtual}
 *
 * @property-read string|null $editedString {virtual}
 * @property-read ProductAvailability $productAvailability {virtual}
 */
class ProductVariant extends BaseEntity implements Synchronizable
{

	use HasCache;
	use HasTranslator;

	public const SHELL_STRING_ID = '0-0';

	public const string USE_PRICE_CATALOG = 'catalog';
	public const string USE_PRICE_MARGIN = 'margin';

	private SupplyModel $supplyModel;
	private StockModel $stockModel;
	private ProductVariantPriceModel $productVariantPriceModel;

	public function injectServices(
		SupplyModel $supplyModel,
		StockModel $stockModel,
		ProductVariantPriceModel $productVariantPriceModel,
	): void
	{
		$this->supplyModel = $supplyModel;
		$this->stockModel = $stockModel;
		$this->productVariantPriceModel = $productVariantPriceModel;
	}

	public function getterStringId(): string
	{
		if (!isset($this->cache['stringId'])) {
			$stringId = [];
//			bd($this->param1Value);
			if ($this->param1Value) {
				$stringId[] = $this->param1Value->id;
			} else {
				$stringId[] = 0;
			}

			if ($this->param2Value) {
				$stringId[] = $this->param2Value->id;
			} else {
				$stringId[] = 0;
			}

			$this->cache['stringId'] = implode('-', $stringId);
		}

		return $this->cache['stringId'];
	}


	protected function getterUid(): ?string
	{
		return $this->product->uid;
	}

	// ************************************ Name ************************************************

	public function getterNameVariant(): string
	{
		if (!isset($this->cache['nameVariant'])) {
			$parts = [];
			if ($this->param1ValueId !== null) {
				$parts[] = Strings::firstLower($this->translator->translate('pvalue_' . $this->param1ValueId));
			}

			if ($this->ean !== null && $this->ean !== '') {
				$parts[] = $this->ean;
			}

			if ($this->code !== null && $this->code !== '') {
				$parts[] = $this->code;
			}

			$this->cache['nameVariant'] = implode(', ', $parts);
		}

		return $this->cache['nameVariant'];
	}


	public function getterNameVariantFormat(): string
	{
		return $this->nameVariant ? sprintf(' (%s)', $this->nameVariant) : '';
	}


	protected function getterName(): string
	{
		$name = $this->getLocalization($this->product->getMutation())->name;
		return $name === null ? '' : $name;
	}


	protected function getterNameAnchor(): string
	{
		return $this->name;
	}


	protected function getterNameTitle(): string
	{
		return $this->name;
	}

	// ************************************ Param ************************************************

	public function getterParam1Id(): ?int
	{
		return $this->param1ValueId;
	}


	public function getterParam1ValueId(): ?int
	{
		return $this->param1Value ? $this->param1Value->id : null;
	}


	public function getterParam2Id(): ?int
	{
		return $this->param2ValueId;
	}


	public function getterParam2ValueId(): ?int
	{
		return $this->param2Value ? $this->param2Value->id : null;
	}


	protected function getterParamValueIds(): array
	{
		$ret = [];
		if (isset($this->param1Value->id)) {
			$ret[] = $this->param1Value->id;
		}

		if (isset($this->param2Value->id)) {
			$ret[] = $this->param2Value->id;
		}

		return $ret;
	}

	// ************************************************************************************

	protected function getterEditedString(): string
	{
		return $this->edited ? $this->edited->format('Y-m-d H:i:s') : '';
	}


	protected function getterIsShell(): bool
	{
		return $this->stringId === self::SHELL_STRING_ID;
	}


	protected function getterPath(): array
	{
		return $this->product->path;
	}


	protected function getterTemplate(): string
	{
		return $this->product->template;
	}


	protected function getterKeywords(): ?string
	{
		return $this->product->keywords;
	}


	protected function getterDescription(): ?string
	{
		return $this->product->description;
	}


	protected function getterAnnotation(): ?string
	{
		return $this->product->annotation;
	}


	protected function getterMainCategory(): ?CatalogTree
	{
		return $this->product->mainCategory;
	}

	protected function getterFirstImage(): ?ProductImage
	{
		return $this->images->current();
	}


	/**
	 * nalezeni obrazku pro danou variantu
	 * @return ArrayIterator<int, ProductImage>
	 */
	protected function getterImages(): ArrayIterator
	{
		$images = $this->product->images;
		$finalImages = [];
		foreach ($images as $i) {
			$toSearch = empty($i->variants) ? [] : explode('|', $i->variants);
			if (in_array($this->id, $toSearch)) {
				$finalImages[] = $i;
			} elseif ($i->variants === null || !$i->variants) {
				$finalImages[] = $i;
			}
		}

		return new ArrayIterator($finalImages);
	}



	// ************************************ Stock ************************************************


	private function initSuppliesCache(): void
	{
		if (!isset($this->cache['suppliesByStock'])) {
			$supplies = [];
			$suppliesByAlias = [];
			$totalSupplyCount = 0;
			foreach ($this->supplyModel->findSuppliesDTO($this) as $supply) {
				$supplies[$supply->stockId] = $supply;
				$stock = $this->stockModel->getStockBySupplyDTO($supply);
				$suppliesByAlias[$stock->alias] = $supply;
				$totalSupplyCount += $supply->amount;
			}
			$this->cache['suppliesByStockAlias'] = $suppliesByAlias;
			$this->cache['suppliesByStock'] = $supplies;
			$this->cache['totalSupplyCount'] = $totalSupplyCount;

		}
	}


	protected function getterSuppliesByStockEntity(): array
	{
		if (!isset($this->cache['suppliesByStockEntity'])) {
			$supplies = [];
			foreach ($this->supplies as $supply) {
				$supplies[$supply->stock->id] = $supply;
			}
			$this->cache['suppliesByStockEntity'] = $supplies;
		}

		return $this->cache['suppliesByStockEntity'];
	}
	protected function getterSuppliesByStock(): array
	{
		$this->initSuppliesCache();
		return $this->cache['suppliesByStock'];
	}

	protected function getterSuppliesByStockAlias(): array
	{
		$this->initSuppliesCache();
		return $this->cache['suppliesByStockAlias'];
	}


	protected function getterTotalSupplyCount(): int
	{
		$this->initSuppliesCache();
		return $this->cache['totalSupplyCount'];
	}


	protected function getterIsInStock(): bool
	{
		return $this->totalSupplyCount > 0;
	}

	// **** specificke zkratky pro konkretni sklady *******

	protected function getterIsInStockDefault(): bool
	{
		return !empty($this->suppliesByStockAlias[Stock::ALIAS_SHOP]->amount);
	}

	protected function getterSuplyCountStockDefault(): int
	{
		return $this->isInStockDefault ? $this->suppliesByStockAlias[Stock::ALIAS_SHOP]->amount : 0;
	}

	protected function getterIsInStockSupplier(): bool
	{
		return !empty($this->suppliesByStockAlias[Stock::ALIAS_SUPPLIER_STORE]->amount);
	}

	protected function getterSuplyCountStockSupplier(): int
	{
		return $this->isInStockSupplier ? $this->suppliesByStockAlias[Stock::ALIAS_SUPPLIER_STORE]->amount : 0;
	}

	// ************************************ Price ************************************************

	protected function getterPricesByLevelCurrency(): array
	{
		if (!isset($this->cache['pricesByLevelCurrency'])) {
			$prices = [];
			foreach ($this->prices as $price) {
				$prices[$price->mutation->id][$price->priceLevel->id][$price->price->currency] = $price;
			}
			$this->cache['pricesByLevelCurrency'] = $prices;
		}

		return $this->cache['pricesByLevelCurrency'];
	}

	protected function getterPricesByLevelName(): array
	{
		if (!isset($this->cache['pricesByLevelName'])) {
			$prices = [];
			/** @var ProductVariantPrice $price */
			foreach ($this->prices as $price) {
				$prices[$price->mutation->id][$price->price->currency][$price->priceLevel->type] = $price;
			}

			$this->cache['pricesByLevelName'] = $prices;
		}

		return $this->cache['pricesByLevelName'];
	}


	public function getPriceInfo(Mutation $mutation, PriceLevel $priceLevel, State $country): PriceInfo
	{
		return $this->loadCache(
			$this->createCacheKey(__FUNCTION__, $mutation, $priceLevel, $country),
			function () use ($mutation, $priceLevel, $country) {
				return $this->productVariantPriceModel->getPriceInfo($this, $mutation, $priceLevel, $country);
			}
		);
	}

	public function price(Mutation $mutation, PriceLevel $priceLevel, State $country): Money  // $selectedCurrency -> compatibility with other projects, in SA not used
	{
		return $this->getPriceInfo($mutation, $priceLevel, $country)->getSellingPrice();
	}


	public function priceVat(Mutation $mutation, PriceLevel $priceLevel, State $country): Money
	{
		return $this->getPriceInfo($mutation, $priceLevel, $country)->getSellingPriceVat();
	}


	// ***********************************************************************************


	protected function getterContent(): ?string
	{
		return $this->product->content;
	}


	/**
	 * @return IRelationshipCollection<ProductFile>
	 */
	protected function getterFiles(): IRelationshipCollection
	{
		return $this->product->files;
	}


	/**
	 * @return ICollection<Tree>
	 */
	protected function getterPages(): ICollection
	{
		return $this->product->pages;
	}


	/**
	 * @return ICollection<Product>
	 */
	protected function getterProducts(): ICollection
	{
		return $this->product->products;
	}

	protected function getterIsInPrepare(): ?int
	{
		return $this->product->isInPrepare;
	}


	protected function getterNotSoldSeparately(): ?int
	{
		return $this->product->notSoldSeparately;
	}


	public function getParameterValueByUid(string $parameterUid): mixed
	{
		return $this->product->getParameterValueByUid($parameterUid);
	}


	public function getParameters(): array
	{
		return $this->product->getParameters();
	}

	/**
	 * @return ICollection<Product>
	 */
	public function getterAccessories(): ICollection
	{
		return $this->product->accessories;
	}


	public function getterIsVariant(): bool
	{
		return true;
	}


	protected function getterCacheId(): string
	{
		return 'var' . $this->id;
	}

	protected function getterCf(): mixed
	{
		return $this->product->cf;
	}


	public function getLocalization(Mutation $mutation): ?ProductVariantLocalization
	{
		return $this->variantLocalizations->toCollection()->getBy(['mutation' => $mutation]);
	}

	public function getLocalizationChecked(Mutation $mutation): ProductVariantLocalization
	{
		$variantLocalization = $this->getLocalization($mutation);
		if ($variantLocalization === null) {
			throw new \LogicException(sprintf('Missing variant localization for variantId %d in \'%s\' mutation', $this->id, $mutation->id));
		}

		return $variantLocalization;
	}


	public function getFormId(): string
	{
		return ($this->isPersisted()) ? (string) $this->id : 'newItem';
	}

	public function getId(): int
	{
		return $this->id;
	}

	protected function getMutation(): Mutation
	{
		// TODO
		throw new \LogicException('ProductVariant must not be Routable, ProductVariantLocalization should be Routable instead.');
	}

	public function getWeight(): BigDecimal
	{
		if (!isset($this->cache['getWeight'])) {
			$this->cache['getWeight'] =  BigDecimal::of($this?->weight ?? 0);
		}

		return $this->cache['getWeight'];
	}

	public function getExternalId(): int|null
	{
		return $this->extId;
	}

	public function setExternalId(null|string|int $externalId): void
	{
		$this->extId = (int) $externalId;
	}

	public function getterProductAvailability(): AbstractProductAvailability
	{
		if (!isset($this->cache['availability'])) {
			$availability = clone $this->product->productAvailability;
			$this->cache['availability'] = $availability->setProductVariant($this);
		}
		return $this->cache['availability'];
	}

	/**
	 * @return array<ParameterValue>
	 */
	public function getFilledVariantParameterValues(): array
	{
		return array_filter([$this->param1Value, $this->param2Value], fn(?ParameterValue $parameterValue) => $parameterValue !== null);
	}
}
