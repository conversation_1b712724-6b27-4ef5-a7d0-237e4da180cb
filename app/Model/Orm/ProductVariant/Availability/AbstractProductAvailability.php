<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductVariant\Availability;


use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\Traits\HasCache;
use App\Model\Orm\Traits\HasStaticCache;
use Nette\InvalidArgumentException;
use Nette\Utils\Json;
use Nextras\Orm\Entity\IEntity;

/**
 * @property-read ProductVariant $variant
 * @property-read ProductVariant $productVariant
 */
abstract class AbstractProductAvailability implements ProductAvailability
{
	#use HasCache;
	use HasStaticCache;

	private ?ProductVariant $settedVariant = null;
	protected Product $product;

	public function __get(string $name): ?ProductVariant
	{
		if ($name === 'variant' || $name === 'productVariant') {
			if ($this->settedVariant === null) {
				return $this->product->firstVariant;
			}
			return $this->settedVariant;
		}
		throw new InvalidArgumentException('Property not exists.');
	}

	public function setProduct(Product $product): ProductAvailability
	{
		$this->product = $product;
		return $this;
	}

	public function setProductVariant(ProductVariant $productVariant): ProductAvailability
	{
		$this->settedVariant = $productVariant;
		return $this;
	}

	public function isVariantSetted(): bool
	{
		return $this->settedVariant !== null;
	}

}
