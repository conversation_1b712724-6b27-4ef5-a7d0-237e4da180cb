<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductVariant\Availability;


use App\Model\DeliveryDate;
use App\Model\Orm\DeliveryMethod\DeliveryMethodConfiguration;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\State\State;
use Brick\Money\Currency;

interface ProductAvailability
{



	public function setProduct(Product $product): ProductAvailability;
	public function setProductVariant(ProductVariant $productVariant): ProductAvailability;
	public function isShowCartCatalog(Mutation $mutation, PriceLevel $priceLevel, State $state): bool;
	public function isShowCartDetail(Mutation $mutation, PriceLevel $priceLevel, State $state): bool;

	public function getAvailabilityText(int $quantityRequired = 1): string|\Stringable|null;
	public function getAvailabilityShortText(): string|\Stringable|null;
	public function getAvailabilityStateText(): string|\Stringable|null;
	public function getDeliveryText(Mutation $mutation, State $state, PriceLevel $priceLevel, Currency $currency, ?DeliveryMethodConfiguration $deliveryMethodConfiguration = null, int $quantityRequired = 1): string|\Stringable|null;
	public function getExpeditionText(Mutation $mutation, State $state, PriceLevel $priceLevel, Currency $currency, ?DeliveryMethodConfiguration $deliveryMethodConfiguration = null, int $quantityRequired = 1): ?string;
	public function getDeliveryDate(Mutation $mutation, State $state, PriceLevel $priceLevel, Currency $currency, ?DeliveryMethodConfiguration $deliveryMethodConfiguration = null, int $quantityRequired = 1): ?DeliveryDate;
	public function getStoreText(State $state): string|\Stringable|null;

	public function getMaxAvailableAmount():int;

	public function getType(): ?string;

	public function getStockDate(): \DateTimeImmutable;

	public function hasPrice(Mutation $mutation, PriceLevel $priceLevel, State $state): bool;
}
