<?php declare(strict_types = 1);

namespace App\Model\Orm\ProductVariant;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariantLocalization\ProductVariantLocalization;
use Nextras\Dbal\Utils\DateTimeImmutable;

final class ProductVariantModel
{

	public function __construct(
		private readonly Orm $orm,
	)
	{
	}

	public function createEmpty(?int $userId = null): ProductVariant
	{
		$emptyVariant = new ProductVariant();
		$emptyVariant->ean = '0';
		$emptyVariant->code = '';
		$emptyVariant->created = new DateTimeImmutable();
		$emptyVariant->createdBy = $userId;
		$emptyVariant->edited = new DateTimeImmutable();
		$emptyVariant->editedBy = $userId;

		$emptyVariantLocalization = new ProductVariantLocalization();
		$emptyVariantLocalization->mutation = $this->orm->mutation->getDefault();

		$emptyVariant->variantLocalizations->add($emptyVariantLocalization);
		return $emptyVariant;
	}


	public function createWithLocalizations(Product $product): ProductVariant
	{
		$newVariant = new ProductVariant();
		$newVariant->product = $product;

		foreach ($this->orm->mutation->findAll() as $mutation) {
			$localization = new ProductVariantLocalization();
			$localization->variant = $newVariant;
			$localization->mutation = $mutation;
		}

		return $newVariant;
	}


	public function normalizeVariant(mixed $variant, Mutation $mutation): void
	{
		$variantLocalization = $variant->variantLocalizations->toCollection()->getBy(['mutation' => $mutation]);
		if ( ! $variantLocalization) {
			$newProductVariantLocalization = new ProductVariantLocalization();
			$newProductVariantLocalization->mutation = $mutation;
			$newProductVariantLocalization->variant = $variant;
			$this->orm->productVariantLocalization->persistAndFlush($newProductVariantLocalization);
		}
	}

}
