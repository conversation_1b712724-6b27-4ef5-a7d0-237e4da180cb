<?php

declare(strict_types=1);

namespace App\Model\Orm\CardPayment\PaymentGateway;

use App\Model\ConfigService;
use App\Model\Orm\CardPayment\CardPayment;
use App\Model\Orm\CardPayment\CardPaymentStatus;
use App\Model\Orm\Order\Payment\CardPaymentInformation;
use Brick\Money\Money;
use Nette\Http\Url;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;

final class PluxeePaymentGateway implements PaymentGateway
{

	public const ID = 'PluxeeGateway';

	private array $config;

	public function __construct(
		ConfigService $configService,
	)
	{
		$this->config = $configService->get('payments', 'pluxee');
	}

	public function getUniqueIdentifier(): string
	{
		return self::ID;
	}

	public function createPayment(CardPaymentInformation $cardPaymentInformation): CardPayment
	{
		return new CardPayment(
			cardPaymentInformation: $cardPaymentInformation,
			gateway: $this,
			externalId: $cardPaymentInformation->payment->order->orderNumber,
			externalUrl: $this->createRedirectUrl($cardPaymentInformation),
			expireTime: (new DateTimeImmutable())->modify('+1 month'),
		);
	}

	private function createRedirectUrl(CardPaymentInformation $cardPaymentInformation): string
	{
		$query = [];
		$order = $cardPaymentInformation->payment->order;

		$query['EShopOrderId'] = $order->orderNumber;
		$query['BenefitsPrice'] = $this->config['id'] . ':' . $this->formatPrice($order->getTotalPriceVat(withDelivery: true));

		$url = new Url($this->config['url']);
		$url->setQuery($query);

		return (string) $url;
	}

	private function formatPrice(Money $money): string
	{
		return str_replace('.', ',', (string) $money->getAmount()->toFloat());
	}

	public function checkPayment(CardPayment $payment, array $data = []): void
	{
		$order = $payment->cardPaymentInformation->payment->order;

		$benefitPrice = $data['BenefitsPrice'] ?? null;

		if ($benefitPrice !== null) {
			if ($benefitPrice !== $this->config['id'] . ':' . $this->formatPrice($order->getTotalPriceVat(withDelivery: true))) {
				$payment->updateStatus(CardPaymentStatus::Declined);
				return;
			}

			$payment->updateStatus(CardPaymentStatus::Settled);
		} else {
			$payment->updateStatus(CardPaymentStatus::Declined);
		}

		$payment->response = ArrayHash::from($data);
		$payment->checkTime = new DateTimeImmutable();
	}

	public function refundPayment(CardPayment $payment): void
	{
		// TODO: Implement refundPayment() method.
	}

}
