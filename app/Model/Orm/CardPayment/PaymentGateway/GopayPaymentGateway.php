<?php

namespace App\Model\Orm\CardPayment\PaymentGateway;

use App\Model\ConfigService;
use App\Model\Link\LinkFactory;
use App\Model\Orm\CardPayment\CardPayment;
use App\Model\Orm\CardPayment\CardPaymentStatus;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Order\Payment\CardPaymentInformation;
use App\Model\Orm\PaymentMethod\Card;
use GoPay\Definition\Response\PaymentStatus;
use GoPay\Payments;
use Nette\Utils\ArrayHash;
use Tracy\Debugger;
use Tracy\ILogger;
use function GoPay\payments;

final class GopayPaymentGateway implements PaymentGateway
{

	public const ID = 'GopayGateway';

	public const string INSTRUMENT_CARD = 'PAYMENT_CARD';

	public const array PAYMENT_INSTRUMENTS = [
		Card::ID => self::INSTRUMENT_CARD,
	];

	private array $config;

	public function __construct(
		ConfigService $configService,
		private readonly LinkFactory $linkFactory,
	)
	{
		$this->config = $configService->get('gopay');
	}

	public function getUniqueIdentifier(): string
	{
		return self::ID;
	}

	public function createPayment(CardPaymentInformation $cardPaymentInformation): CardPayment
	{
		$order = $cardPaymentInformation->payment->order;
		$totalPrice = $order->getTotalPriceWithDeliveryVat();

		$paymentResult = $order->mutation->pages->paymentLandingPage;

		$callback = [
			'return_url' => $this->linkFactory->linkTranslateToNette($paymentResult, ['active' => 1, 'orderHash' => $order->hash]),
			'notification_url' => $this->linkFactory->linkTranslateToNette($paymentResult, ['active' => 0, 'orderHash' => $order->hash]),
		];

		$payer = [
			'allowed_payment_instruments' => ['PAYMENT_CARD', 'APPLE_PAY', 'GPAY'],
			'contact' => [
				'email' => trim($order->email),
			],
		];

		$target = [
			'type' => 'ACCOUNT',
			'goid' => $this->config[$order->mutation->langCode]['goid'],
		];

		$payer['default_payment_instrument'] = self::PAYMENT_INSTRUMENTS[$order->payment->paymentMethod->paymentMethodUniqueIdentifier];

		$items = [];
		foreach ($order->products as $item) {
			$type = 'ITEM';
			$items[] = [
				'type' => $type,
				'ean' => $item->id,
				'count' => $item->amount,
				'name' => $item->variant->name,
				'amount' => round($item->totalPriceVat->getAmount()->toFloat()) * 100,
				'vat_rate' => '21',
			];
		}

		$payment = [
			'amount' => round($totalPrice->getAmount()->toFloat()) * 100,
			'payer' => $payer,
			'target' => $target,
			'currency' => $totalPrice->getCurrency()->getCurrencyCode(),
			'order_number' => $order->orderNumber,
			'items' => $items,
			'callback' => $callback,
			'lang' => $order->mutation->langCode,
		];

		// @phpstan-ignore-next-line
		set_error_handler(function ($err_severity, $err_msg, $err_file, $err_line) {
			Debugger::log($err_msg, ILogger::ERROR);
		}, E_WARNING);

		$createPayment = $this->getClient($order->mutation->langCode)->createPayment($payment);

		$filename = sprintf('%s/gopay_%s.log', 'orders', date('Y-m-d_H-i-s'));
		Debugger::log($createPayment, $filename);

		return new CardPayment(
			cardPaymentInformation: $cardPaymentInformation,
			gateway: $this,
			externalId: $createPayment->json['id'],
			externalUrl: $createPayment->json['gw_url']
		);
	}

	public function checkPayment(CardPayment $payment, array $data = []): void
	{
		$paymentStatusResponse = $this->getClient($payment->cardPaymentInformation->payment->order->mutation->langCode)->getStatus($payment->externalId);
		switch ($paymentStatusResponse->json['state']) {
			case PaymentStatus::PAID:
				$payment->updateStatus(CardPaymentStatus::Settled);
				break;

			case PaymentStatus::CANCELED:
				$payment->updateStatus(CardPaymentStatus::Canceled);
				break;

			case PaymentStatus::AUTHORIZED:
				$payment->updateStatus(CardPaymentStatus::Authorized);
				break;
		}
		$payment->response = ArrayHash::from($paymentStatusResponse->json);
	}

	public function refundPayment(CardPayment $payment): void
	{
		// TODO: Implement refundPayment() method.
	}

	private function getClient(?string $langCode = Mutation::CODE_CS): Payments
	{
		return payments([
			'goid' => $this->config[$langCode]['goid'],
			'clientId' => $this->config[$langCode]['clientId'],
			'clientSecret' => $this->config[$langCode]['clientSecret'],
			'gatewayUrl' => $this->config[$langCode]['gatewayUrl'],
			'language' => $langCode,
		]);
	}

}
