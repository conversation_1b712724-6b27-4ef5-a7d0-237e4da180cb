<?php

declare(strict_types=1);

namespace App\Model\Orm\CardPayment\PaymentGateway;

use App\Model\Orm\CardPayment\CardPayment;
use App\Model\Orm\Order\Payment\CardPaymentInformation;

interface PaymentGateway
{

	public function getUniqueIdentifier(): string;

	/**
	 * This should dispatch an HTTP request to the payment gateway to create a new transaction. The implementation is expected
	 * to use $cardPaymentInformation->payment->order to assemble the necessary parameters (such as order number, price, items,
	 * possibly $order->user's previously saved card, etc.)
	 *
	 * The gateway API usually returns their transaction ID, along with payment URL, and possibly status (which might be already
	 * settled if using saved card). The PaymentGateway implementation is expected to use these information to create a new CardPayment.
	 */
	public function createPayment(CardPaymentInformation $cardPaymentInformation): CardPayment;

	/**
	 * This should dispatch an HTTP request to fetch the latest information about given $payment. The implementation is expected
	 * to $payment->updateStatus() if necessary (i.e. when the status returned from the gateway API differs from the one we know.)
	 */
	public function checkPayment(CardPayment $payment, array $data = []): void;

	/**
	 * This should dispatch an HTTP request to reverse or refund the $payment.
	 */
	public function refundPayment(CardPayment $payment): void;

}
