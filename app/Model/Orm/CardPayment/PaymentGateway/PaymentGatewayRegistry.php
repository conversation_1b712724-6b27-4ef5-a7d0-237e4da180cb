<?php

declare(strict_types=1);

namespace App\Model\Orm\CardPayment\PaymentGateway;

use App\Model\Orm\Mutation\Mutation;
use function array_values;
use function assert;
use function reset;
use function sprintf;

final class PaymentGatewayRegistry
{

	/** @var array<string, PaymentGateway> */
	private array $paymentGateways = [];

	/**
	 * @param PaymentGateway[] $paymentGateways
	 */
	public function __construct(
		array $paymentGateways,
	)
	{
		foreach ($paymentGateways as $paymentGateway) {
			$this->paymentGateways[$paymentGateway->getUniqueIdentifier()] = $paymentGateway;
		}
	}

	/**
	 * @return list<PaymentGateway>
	 */
	public function list(): array
	{
		return array_values($this->paymentGateways);
	}

	public function get(string $uniqueIdentifier): PaymentGateway
	{
		return $this->paymentGateways[$uniqueIdentifier] ?? throw new \InvalidArgumentException(sprintf('Payment gateway with ID "%s" not found.', $uniqueIdentifier));
	}

	public function getDefault(Mutation $mutation): PaymentGateway
	{
		$gateway = reset($this->paymentGateways);
		assert($gateway !== false);

		return $gateway;
	}

}
