<?php

declare(strict_types=1);

namespace App\Model\Orm\CardPayment\PaymentGateway;

use App\Model\ConfigService;
use App\Model\Orm\CardPayment\CardPayment;
use App\Model\Orm\CardPayment\CardPaymentStatus;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\Payment\CardPaymentInformation;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Nette\InvalidStateException;
use Nette\Utils\ArrayHash;
use Nette\Utils\Json;
use Nette\Utils\JsonException;
use Nextras\Dbal\Utils\DateTimeImmutable;

final class BenefitPaymentGateway implements PaymentGateway
{

	public const ID = 'BenefitGateway';
	private const ERROR_MESSAGES = [
		1  => 'Chybné číslo obchodníka (MERCHANT_NUMBER).',
		2  => 'Objchodník nemůže prodávat tuto skupinu (BENEFIT_GROUP) benefitů',
		3  => '<PERSON><PERSON><PERSON> objedn<PERSON>ky (ORDER_NUMBER) již bylo použito.',
		4  => 'Chybný účet (ACCOUNT) nebo pin (PIN).',
		5  => 'Obchodník nesmí platit přes platební bránu.',
		6  => 'Není vyplněn obchodník (MERCHANT_NIUMBER).',
		7  => 'Není vyplněna skupina benefitu (BENEFIT_GROUP).',
		8  => 'Není vyplněno číslo účtu (ACCOUNT).',
		9  => 'Není vyplněn pin (PIN).',
		10 => 'Není vyplněna cena (AMOUNT).',
		11 => 'Cena (AMOUNT) je menší než nula.',
		12 => 'Není vyplněno číslo objednávky (ORDER_NUMBER).',
		13 => 'Účet (ACCOUNT) nemůže platit přes platební bránu.',
		14 => 'Na účtě není dostatek prostředků.',
	];

	private array $config;

	public function __construct(
		ConfigService $configService,
	)
	{
		$this->config = $configService->get('payments', 'benefity');
	}

	public function getUniqueIdentifier(): string
	{
		return self::ID;
	}

	public function createPayment(CardPaymentInformation $cardPaymentInformation): CardPayment
	{
		return new CardPayment(
			cardPaymentInformation: $cardPaymentInformation,
			gateway: $this,
			externalId: $cardPaymentInformation->payment->order->orderNumber,
			externalUrl: null
		);
	}

	private function isJson(string $data): false|array
	{
		try {
			return Json::decode($data, forceArrays: true);
		} catch (JsonException $e) {
			// do nothing
		}
		return false;
	}
	public function checkPayment(CardPayment $payment, array $data = []): void
	{
		$order = $payment->cardPaymentInformation->payment->order;
		$client = new Client();
		$options = [
			'headers' => [
				'Authorization' => $this->getAuthorizationHeader(),
				'Content-Type' => 'application/x-www-form-urlencoded',
			],
			'form_params' => [
				'MERCHANT_NUMBER' => $this->config['merchantNumber'],
				'ORDER_NUMBER' => $order->orderNumber,
				'CHECK_STATUS' => 1,
			]];
		try {
			$request  = $client->request('POST', $this->config['url'], $options);
			$responseString = $request->getBody()->getContents();
			$response = $this->isJson($responseString);
			if ($response !== false) {
				if ($response['transaction_number'] !== null) {
					$payment->updateStatus(CardPaymentStatus::Settled);
				}
			} else {
				$response = ['responseString' => $responseString];
			}

			$payment->response = ArrayHash::from($response);

		} catch (\Throwable $e) {
			// do nothing
		}
		$payment->checkTime = new DateTimeImmutable();
	}

	public function refundPayment(CardPayment $payment): void
	{
		// TODO: Implement refundPayment() method.
	}

	public function createPaymentRequest(Order $order, string $account, string $pin): void
	{
		$totalPrice = $order->getTotalPriceWithDeliveryVat();

		$client = new Client();
		$options = [
			'headers' => [
				'Authorization' => $this->getAuthorizationHeader(),
			],
			'form_params' => [
				'MERCHANT_NUMBER' => $this->config['merchantNumber'],
				'ORDER_NUMBER' => $order->orderNumber,
				'ACCOUNT' => $account,
				'PIN' => $pin,
				'AMOUNT' => (string) $totalPrice->getAmount()->toFloat(),
				'BENEFIT_GROUP' => $this->config['benefitGroup'],
			],
			//'debug' => true,
		];

		try {
			$request = $client->request('POST', $this->config['url'], $options);
			$response = $request->getBody()->getContents();
			$result = [];
			parse_str($response, $result);
			if (isset($result['ERR']) && $result['ERR'] === '0') {
				// set payment is ok
				return;
			} else {
				throw new InvalidStateException(self::ERROR_MESSAGES[intval($result['ERR'])] ?? 'Request failed');
			}
		} catch (GuzzleException $e) {
			throw new InvalidStateException('Request failed', 0, $e);
		}
	}


	private function getAuthorizationHeader(): string
	{
		return 'Basic ' . base64_encode($this->config['username'] . ':' . $this->config['password']);
	}

}
