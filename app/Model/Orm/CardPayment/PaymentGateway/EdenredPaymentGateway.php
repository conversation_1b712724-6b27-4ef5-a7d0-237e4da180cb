<?php

declare(strict_types=1);

namespace App\Model\Orm\CardPayment\PaymentGateway;

use App\Exceptions\LogicException;
use App\Model\ConfigService;
use App\Model\Orm\CardPayment\CardPayment;
use App\Model\Orm\CardPayment\CardPaymentStatus;
use App\Model\Orm\Order\Payment\CardPaymentInformation;
use App\Model\Orm\Price;
use Brick\Money\Money;
use Nette\Utils\ArrayHash;
use Nette\Utils\Strings;
use Nextras\Dbal\Utils\DateTimeImmutable;

final class EdenredPaymentGateway implements PaymentGateway
{

	public const ID = 'EdenredGateway';

	public const STATUSES = [
		0 => 'payment_edenred_ok',
		1 => 'payment_edenred_points_not_available',
		2 => 'payment_edenred_cancelled_by_user',
		3 => 'payment_edenred_fatal_error',
	];

	private array $config;

	public function __construct(
		ConfigService $configService,
	)
	{
		$this->config = $configService->get('payments', 'edenred');
	}

	public function getUniqueIdentifier(): string
	{
		return self::ID;
	}

	public function createPayment(CardPaymentInformation $cardPaymentInformation): CardPayment
	{
		return new CardPayment(
			cardPaymentInformation: $cardPaymentInformation,
			gateway: $this,
			externalId: $cardPaymentInformation->payment->order->orderNumber,
			externalUrl: $this->createRedirectUrl($cardPaymentInformation),
			expireTime: (new DateTimeImmutable())->modify('+1 month'),
		);
	}

	private function createRedirectUrl(CardPaymentInformation $cardPaymentInformation): string
	{
		$query = [];
		$order = $cardPaymentInformation->payment->order;
		foreach ($order->products as $orderProduct) {
			$query['price'][] = $this->formatPrice($orderProduct->unitPriceVat);
			$query['countofitems'][] = $orderProduct->amount;
			//$query['category'][] = 'VO';
			$query['description'][] = str_replace('-', '_', Strings::webalize($orderProduct->variant->product->name));
		}

		foreach ($query as $key => $values) {
			$query[$key] = implode('|', $values);
		}

		$query['currency'] = Strings::lower($order->currency->getCurrencyCode());
		$query['transport'] = $this->formatPrice($order->getPayment()->totalPriceVat->plus($order->getDelivery()->totalPriceVat));
		$query['package'] = '0';
		$query['ordernumber'] = $order->orderNumber;
		$query['orderpricetotal'] = $this->formatPrice($order->getTotalPriceWithDeliveryVat());
		$query['source'] = $this->config['returnUrl'] ?? null;
		$query['notificationemail'] = $this->config['notificationEmail'] ?? $order->email;

		foreach ($query as $key => $value) {
			$query[$key] = $this->encodeString((string) $value);
		}

		$digest = $this->buildQueryString($query) . $this->config['salt'];

		$query['digest'] = sha1($digest);

		$url = $this->config['url'];
		$url .= '?' . $this->buildQueryString($query);

		return (string) $url;
	}

	private function encodeString(string $string): string
	{
		return strtr($string, ['|' => '%7c', ',' => '%2c', '/' => '%2f', ':' => '%3a', '[' => '%5b', ']' => '%5d', '?' => '%3f', '=' => '%3d', '@' => '%40']);
	}

	private function buildQueryString(array $query): string
	{
		$queryStringArray = [];
		foreach ($query as $key => $value) {
			$queryStringArray[] = $key . '=' . $value;
		}
		return implode('&', $queryStringArray);
	}

	private function formatPrice(Money $money): string
	{
		return str_replace('.', ',', (string) $money->getAmount()->toFloat());
	}

	public function checkPayment(CardPayment $payment, array $data = []): void
	{
		if ($data === []) {
			return;
		}

		$digest = $data['digest'] ?? null;
		unset($data['digest']);

		if ($digest === null) {
			throw new LogicException('Digest not provided.');
		}

		if ($this->config['verifyDigest'] ?? true) {
			$digest = Strings::lower($digest);
			$toDigest = http_build_query($data) . $this->config['salt'];
			if ($digest !== sha1($toDigest)) {
				throw new LogicException('Digest mismatch.');
			}
		}

		if ((int) $data['statuscode'] === 0) {
			$order = $payment->cardPaymentInformation->payment->order;
			$payedTotal = Price::from(Money::of((float) str_replace(',', '.', $data['orderpricetotal']), $order->getCurrency()))->asMoney();

			if ($order->getTotalPriceWithDeliveryVat()->isEqualTo($payedTotal)) {
				$payment->updateStatus(CardPaymentStatus::Settled);
			} else {
				$payment->updateStatus(CardPaymentStatus::Partial);
			}

		} elseif ((int) $data['statuscode'] === 2) {
			$payment->updateStatus(CardPaymentStatus::Canceled);
		}

		$payment->response = ArrayHash::from($data);
		$payment->checkTime = new DateTimeImmutable();
	}

	public function refundPayment(CardPayment $payment): void
	{
		// TODO: Implement refundPayment() method.
	}

	public static function getStatus(int $statusCode): string
	{
		return match ($statusCode) {
			0 => 'PAID',
			2 => 'CANCELED',
			default => 'PENDING'
		};
	}

}
