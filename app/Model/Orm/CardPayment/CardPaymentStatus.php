<?php

declare(strict_types=1);

namespace App\Model\Orm\CardPayment;

use App\Model\Orm\Order\Payment\PaymentState;

enum CardPaymentStatus: string
{

	case Pending = 'Pending';
	case Authorized = 'Authorized';
	case Settled = 'Settled';
	case Refunded = 'Refunded';
	case Declined = 'Declined';
	case Canceled = 'Canceled';

	case Partial = 'Partial';

	public function toPaymentState(): PaymentState
	{
		return match ($this) {
			self::Pending, self::Partial => PaymentState::Pending,
			self::Authorized, self::Settled => PaymentState::Completed,
			self::Declined, self::Canceled => PaymentState::Failed,
			self::Refunded => PaymentState::Refunded,
		};
	}

}
