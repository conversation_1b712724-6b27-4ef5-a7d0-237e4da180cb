<?php

declare(strict_types=1);

namespace App\Model\Orm\CardPayment;

use App\Model\Orm\BackedEnumWrapper; // phpcs:ignore
use App\Model\Orm\JsonArrayContainer; // phpcs:ignore
use App\Model\Orm\CardPayment\PaymentGateway\PaymentGateway;
use App\Model\Orm\Order\Payment\CardPaymentInformation;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property-read int $id {primary}
 * @property-read CardPaymentInformation $cardPaymentInformation {m:1 CardPaymentInformation::$payments, cascade=[persist]}
 * @property-read string $paymentGatewayUniqueIdentifier
 * @property-read string $externalId
 * @property-read string|null $externalUrl
 * @property-read DateTimeImmutable|null $createTime {default now}
 * @property-read DateTimeImmutable|null $expireTime {default null}
 * @property DateTimeImmutable|null $checkTime {default null}
 * @property ArrayHash|null $response {container JsonArrayContainer}
 * @property-read CardPaymentStatus $status {wrapper BackedEnumWrapper}
 * @property OneHasMany<CardPaymentStatusChange> $statusChanges {1:m CardPaymentStatusChange::$payment, cascade=[persist, remove]}
 */
final class CardPayment extends Entity
{

	public function __construct(
		CardPaymentInformation $cardPaymentInformation,
		PaymentGateway $gateway,
		string $externalId,
		string|null $externalUrl,
		DateTimeImmutable|null $expireTime = null,
		CardPaymentStatus $initialStatus = CardPaymentStatus::Pending,
	)
	{
		parent::__construct();
		$this->setReadOnlyValue('cardPaymentInformation', $cardPaymentInformation);
		$this->setReadOnlyValue('paymentGatewayUniqueIdentifier', $gateway->getUniqueIdentifier());
		$this->setReadOnlyValue('externalId', $externalId);
		$this->setReadOnlyValue('externalUrl', $externalUrl);
		$this->setReadOnlyValue('status', $initialStatus);
		$this->setReadOnlyValue('expireTime', $expireTime);
		$this->statusChanges->add(CardPaymentStatusChange::of($this, null, $initialStatus));
	}

	public function updateStatus(CardPaymentStatus $status): void
	{
		$this->statusChanges->add(CardPaymentStatusChange::of($this, $this->status, $status));
		$this->setReadOnlyValue('status', $status);
	}

}
