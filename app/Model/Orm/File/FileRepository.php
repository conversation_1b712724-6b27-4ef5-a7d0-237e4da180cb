<?php declare(strict_types = 1);

namespace App\Model\Orm\File;

use App\Model\Orm\CollectionById;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method File|null getById($id)
 * @method ICollection<File> findByExactOrder(array $ids)
 *
 * @extends Repository<File>
 */
final class FileRepository extends Repository implements CollectionById
{

	static function getEntityClassNames(): array
	{
		return [File::class];
	}

	/**
	 * @return ICollection<File>
	 */
	public function findByIdOrder(array $ids): ICollection
	{
		return $this->findByExactOrder($ids);
	}

}
