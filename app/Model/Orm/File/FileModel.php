<?php

declare(strict_types=1);

namespace App\Model\Orm\File;

final class FileModel
{
	public function __construct(
		private readonly FileRepository $fileRepository,
	) {}


	public function add(\Nette\Http\FileUpload $file): File
	{
		$newFile = new File();
		$this->fileRepository->attach($newFile);
		$newFile->name = $file->getUntrustedName();

		$this->fileRepository->persistAndFlush($newFile);

		$filename = $newFile->id.'-'.mb_strtolower($file->getSanitizedName());
		$file = $file->move(WWW_DIR.FILES_DIR.$filename);
		$size = $file->getSize();

		$newFile->filename = $filename;
		$newFile->size = $size;
		$this->fileRepository->persistAndFlush($newFile);

		return $newFile;
	}
}
