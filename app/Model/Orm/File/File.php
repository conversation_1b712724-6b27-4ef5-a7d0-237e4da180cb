<?php

declare(strict_types=1);

namespace App\Model\Orm\File;

use App\Model\Orm\ProductFile\ProductFile;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property int $id {primary}
 * @property string|null $name
 * @property string|null $filename
 * @property int|null $size
 *
 *
 * RELATIONS
 * @property OneHasMany<ProductFile> $productFiles {1:m ProductFile::$file}
 *
 * VIRTUAL
 * @property-read string|null $url {virtual}
 * @property-read string|null $ext {virtual}
 * @property-read string|null $sort {virtual}
 * @property-read string|null $type {virtual}
 * @property-read string|null $sizeMB {virtual}
 */
class File extends \Nextras\Orm\Entity\Entity
{
	protected function getterUrl(): string
	{
		return FILES_DIR.$this->filename;
	}


	protected function getterExt(): string
	{
		return substr($this->url, strrpos($this->url, '.')+1);
	}


	protected function getterSort(): ?int
	{
		if ($this->isPersisted()) {
			return $this->id;
		}

		return null;
	}


	protected function getterType(): ?string
	{
		if ($this->isPersisted() && file_exists(WWW_DIR . $this->url)) {

			$contentType = mime_content_type(WWW_DIR . $this->url);
			\assert($contentType !== false);

			$parts = explode('/', $contentType);
			$parts = array_reverse($parts);
			return $parts[0];
		}

		return null;
	}


	protected function getterSizeMB(): string
	{
		return round($this->size / 1000000, 2) . ' MB';
	}


}
