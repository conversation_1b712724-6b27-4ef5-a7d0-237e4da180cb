<?php

declare(strict_types=1);

namespace App\Model\Orm\LibraryTree;

use Nextras\Orm;

use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Orm\Mapper\Dbal\Conventions\Conventions;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<LibraryTree>
 */
final class LibraryTreeMapper extends DbalMapper
{
	use HasCamelCase;
	/**
* @return literal-string
*/
public function getTableName(): string
{
	return 'library_tree';
}


	protected function createConventions(): Orm\Mapper\Dbal\Conventions\IConventions
	{
		$conventions = parent::createConventions();
		assert($conventions instanceof Conventions);
		$conventions->setMapping('pathString', 'path');
		return $conventions;
	}
}
