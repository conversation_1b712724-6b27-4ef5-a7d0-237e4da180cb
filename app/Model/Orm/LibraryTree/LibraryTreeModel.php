<?php

declare(strict_types=1);

namespace App\Model\Orm\LibraryTree;

use App\Model\Orm\Orm;
use App\Model\Orm\User\User;
use Nextras\Dbal\Utils\DateTimeImmutable;

final class LibraryTreeModel
{

	public function __construct(
		private readonly Orm $orm,
	)
	{
	}

	public function save(LibraryTree $libraryTree, mixed $data): void
	{
		$libraryTree->name = $data['name'];

		foreach ($libraryTree->images as $image) {
			if (isset($data['imageName'][$image->id])) {
				$image->name = $data['imageName'][$image->id];
				$image->sort = $data['imageSort'][$image->id];
			}
		}
		$this->orm->libraryTree->persistAndFlush($libraryTree);
	}

	/**
	 * @param LibraryTree $parent
	 * @param string $name
	 * @param User $user
	 * @return LibraryTree
	 */
	public function createNew(LibraryTree $parent, string $name, User $user): LibraryTree
	{
		$child = new LibraryTree();
		$child->name = $name;
		$child->nameTitle = $name;
		$child->nameAnchor = $name;
		$child->publicFrom = $child->createdTime = new DateTimeImmutable();
		$child->publicTo = '2100-01-01';
		$child->created = $child->edited = $user->id;

		return $this->addChild($parent, $child);
	}

	/**
	 * Add Child to parent - handle parent, level, last, path
	 */
	public function addChild(LibraryTree $parent, LibraryTree $child, bool $save = true, bool $skipSort = false): LibraryTree
	{
		$child->parent = $parent;
		$child->level = $parent->level + 1;
		$child->last = 1;

		//path
		$path = $parent->path;
		$path[] = $parent->id;
		$child->path = $path;

		if (!$skipSort) {
			$lastSort = 0;
			foreach ($parent->crossroad as $item) {
				$lastSort = $item->sort;
			}
			$child->sort = $lastSort + 1;
		}

		$parent->last = 0;

		if ($save) {
			$this->orm->libraryTree->persistAndFlush($child);
		}
		return $child;
	}

	public function move(LibraryTree $movedTree, LibraryTree $targetTree, string $action): void
	{
		// toto musis prepocitat
		$oldMovedParent = $movedTree->parent;
		switch ($action) {
			case "after":
			case "before":
				$tmp = [];
				$i = 0;

				if ($targetTree->parent) {

					foreach ($targetTree->parent->crossroad as $child) {
						if ($child->id == $movedTree->id) {
							continue;
						}

						if ($action == 'before' && $child->id == $targetTree->id) {
							$i++;
							$movedTree->sort = $i;
							$tmp[] = $movedTree;
						}

						$i++;
						$child->sort = $i;
						$tmp[] = $child;

						if ($action == 'after' && $child->id == $targetTree->id) {
							$i++;
							$movedTree->sort = $i;
							$tmp[] = $movedTree;
						}
					}
					$movedTree->parent = $targetTree->parent;
					$this->orm->persistAndFlush($targetTree->parent);
				}
				break;

			case "inside":
			case "last":

				$i = 0;
				foreach ($targetTree->crossroad as $item) {
					$i++;
					$item->sort = $i;
				}
				$i++;
				$movedTree->sort = $i;
				$targetTree->crossroad->add($movedTree);
				$this->orm->persistAndFlush($targetTree);
				break;
		}

		if ($oldMovedParent && $oldMovedParent->crossroad->count() == 0) {
			$oldMovedParent->last = 1;
			$this->orm->persistAndFlush($oldMovedParent);
		}

		if ($targetTree->parent) {
			$this->recalculateTreePath($targetTree->parent);
		} else {
			$this->recalculateTreePath($targetTree);
		}

		$this->orm->persistAndFlush($targetTree);
	}

	public function recalculateTreePath(LibraryTree $tree): void
	{
		if ($tree->crossroad->count()) {
			$tree->last = 0;
		} else {
			$tree->last = 1;
		}

		if ($tree->parent) {
			$tree->level = $tree->parent->level + 1;
			$tmpPath = $tree->parent->path;
			$tmpPath[] = $tree->parent->id;
			$tree->path = $tmpPath;
		} else {
			$tree->level = 0;
			$tree->path = [];
		}
		foreach ($tree->crossroad as $item) {
			$this->recalculateTreePath($item);
		}
	}
}
