<?php declare(strict_types = 1);

namespace App\Model\Orm\LibraryTree;

use App\Model\Orm\LibraryImage\LibraryImage;
use App\Model\Orm\ConvertableToTree;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\ManyHasOne;
use Nextras\Orm\Relationships\OneHasMany;
use function assert;

/**
 * @property int $id {primary}
 * @property string|null $pathString
 * @property int|null $last
 * @property int|null $level
 * @property int|null $sort
 * @property int|null $created
 * @property int|null $edited
 * @property string|null $name
 * @property string|null $nameTitle
 * @property string|null $nameAnchor
 * @property DateTimeImmutable $createdTime {default "now"}
 * @property DateTimeImmutable $editedTime {default "now"}
 * @property DateTimeImmutable|null $publicFrom
 * @property DateTimeImmutable|null $publicTo
 * @property string|null $uid
 *
 * VIRTUAL
 * @property array|null $path {virtual}
 * @property array $pathItems {virtual}
 *
 * RELATIONS
 * @property LibraryTree|null $parent {m:1 LibraryTree::$crossroad}
 * @property OneHasMany<LibraryTree> $crossroad {1:m LibraryTree::$parent, orderBy=[sort=ASC], cascade=[persist, remove]}
 * @property OneHasMany<LibraryImage> $images {1:m LibraryImage::$library, orderBy=[sort=ASC], cascade=[persist, remove]}
 */
class LibraryTree extends Entity implements ConvertableToTree
{

	public const ROOT_ID = 1; // výchozí složka
	public const TREE_REVIEW_ID = 1548;
	public const int USER_ANIMAL_ID = 6;


	protected function getterPath(): ?array
	{
		if (!isset($this->id)) {
			return null;
		}

		$path = preg_split('~\|~', $this->pathString ?? '', -1, PREG_SPLIT_NO_EMPTY);
		assert($path !== false);

		$path = array_map('intval', $path);
		return $path;
	}


	protected function setterPath(?array $path = []): void
	{
		if ($path) {
			$this->pathString = implode('|', $path) . '|';
		} else {
			$this->pathString = null;
		}
	}

	public function getName(): string
	{
		return $this->name;
	}

	public function getId(): int
	{
		return $this->id;
	}


	protected function getterPathItems(): array
	{
		$ret = [];
		foreach ($this->path as $pathId) {
			$ret[] = $this->getRepository()->getById($pathId);
		}

		return $ret;
	}


	public function getTreeItems(): array
	{
		return $this->crossroad->toCollection()->fetchAll();
	}

	public function getTreeSiblings(): array
	{
		$parent = $this->parent;
		if ($parent !== null) {
			$siblings = $parent->crossroad->toCollection()->fetchAll();
		} else {
			$siblings = $this->getRepository()->findBy(['parent' => null])->orderBy('sort')->fetchAll();
		}

		return $siblings;
	}

	public function isPublic(): bool
	{
		return true;
	}

	/**
	 * @return OneHasMany<LibraryTree>
	 */
	public function getItemsRelation(): OneHasMany
	{
		return $this->crossroad;
	}

	public function getParentNode(): ?ConvertableToTree
	{
		return $this->parent;
	}

	public function setParentNode(?ConvertableToTree $parent): void
	{
		assert($parent instanceof LibraryTree || $parent === null);
		$this->parent = $parent;
	}

	public function setLeafParameter(bool $isLeaf): void
	{
		$this->level = (int)$isLeaf;
	}

	public function setSort(int $sort): void
	{
		$this->sort = $sort;
	}

	public function setLevel(int $level): void
	{
		$this->level = $level;
	}

	public function getLevel(): int
	{
		return $this->level;
	}

	public function setPathNodes(array $items): void
	{
		$this->path = array_map(function (ConvertableToTree $item) {
			return $item->getId();
		}, $items);
	}

	public function getPathNodes(): array
	{
		return $this->pathItems;
	}
}
