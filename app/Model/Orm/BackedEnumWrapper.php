<?php

declare(strict_types=1);

namespace App\Model\Orm;

use BackedEnum;
use InvalidArgumentException;
use Nextras\Orm\Entity\ImmutableValuePropertyWrapper;
use function array_key_first;
use function assert;
use function is_int;
use function is_string;
use function is_subclass_of;

/**
 * TODO remove me when this is released in nextras/orm
 */
final class BackedEnumWrapper extends ImmutableValuePropertyWrapper
{
	public function convertToRawValue($value)
	{
		if ($value === null) {
			return null;
		}

		$type = array_key_first($this->propertyMetadata->types);

		if ( ! $value instanceof BackedEnum) {
			throw new InvalidArgumentException('Value must be of type BackedEnum');
		}

		if ( ! $value instanceof $type) {
			throw new InvalidArgumentException('Value must be of type ' . $type . '.');
		}

		return $value->value;
	}

	public function convertFromRawValue($value)
	{
		if ($value === null) {
			return null;
		}

		$type = array_key_first($this->propertyMetadata->types);

		if (is_int($value) || is_string($value)) {
			if (is_subclass_of($type, BackedEnum::class)) {
				return $type::from($value);
			}
		}

		if ($value instanceof BackedEnum && $value instanceof $type) {
			return $value;
		}

		throw new InvalidArgumentException('Invalid value for enum.');
	}
}
