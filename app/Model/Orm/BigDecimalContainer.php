<?php

declare(strict_types=1);

namespace App\Model\Orm;

use Brick\Math\BigDecimal;
use Nextras\Orm\Entity\ImmutableValuePropertyWrapper;
use function assert;
use function is_string;

final class BigDecimalContainer extends ImmutableValuePropertyWrapper
{

	public function convertToRawValue($value): string|null
	{
		if ($value === null) {
			return null;
		}

		assert($value instanceof BigDecimal);
		return (string) $value;
	}

	public function convertFromRawValue($value): BigDecimal|null
	{
		if ($value === null) {
			return null;
		}

		assert(is_string($value) || is_float($value));
		return BigDecimal::of($value);
	}

}
