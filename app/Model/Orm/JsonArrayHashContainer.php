<?php declare(strict_types = 1);

namespace App\Model\Orm;

use Nette\Utils\ArrayHash;
use Nette\Utils\Json;
use Nette\Utils\JsonException;
use Nette\Utils\Strings;
use Nextras\Orm\Entity\ImmutableValuePropertyWrapper;

final class JsonArrayHashContainer extends ImmutableValuePropertyWrapper
{

	public function setInjectedValue($value): bool
	{
		// array and object normalization
		$this->value = $value instanceof ArrayHash ? $value : ArrayHash::from(Json::decode(Json::encode($value), forceArrays: true));
		return true;
	}

	/**
	 * @param string|null $value
	 * @throws JsonException
	 */
	public function convertFromRawValue($value = null): ArrayHash
	{
		if (!isset($value) || Strings::length($value) === 0) {
			return new ArrayHash();
		} else {
			return ArrayHash::from(Json::decode($value, forceArrays: true));
		}
	}

	/**
	 * @throws JsonException
	 */
	public function convertToRawValue($value): string
	{
		return Json::encode($value);
	}

}
