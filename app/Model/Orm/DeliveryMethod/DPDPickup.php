<?php

declare(strict_types=1);

namespace App\Model\Orm\DeliveryMethod;

use App\Model\Orm\Order\Delivery\DeliveryType;
use App\Model\Orm\Order\Order;
use Nette\Utils\ArrayHash;

final class DPDPickup extends BaseDeliveryMethod
{

	public const string ID = 'DPDPickup';

	public function getUniqueIdentifier(): string
	{
		return self::ID;
	}

	public function getDeliveryType(): DeliveryType
	{
		return DeliveryType::Pickup;
	}

	public function supportsTracking(): bool
	{
		return false;
	}

	public function getTrackingUrl(string $trackingCode): string
	{
		return '';
	}

	public function onOrderPlaced(Order $order): void
	{
		// TODO: Implement onOrderConfirmed() method.
	}

	public function onOrderCanceled(Order $order): void
	{
		// TODO: Implement onOrderCanceled() method.
	}

	public function processDeliveryDetails(ArrayHash $details): ArrayHash
	{
		return $details;
	}
	public function supportsPickup(): bool
	{
		return true;
	}

}
