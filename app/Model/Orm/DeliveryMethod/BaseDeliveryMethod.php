<?php

declare(strict_types=1);

namespace App\Model\Orm\DeliveryMethod;

use App\Model\ConfigService;
use App\Model\CustomField\LazyValue;
use App\Model\Orm\LibraryImage\LibraryImage;
use App\Model\Orm\Order\Delivery\DeliveryType;
use App\Model\Orm\Order\OrderProxy;
use App\Model\Orm\Order\PickupPoint\PickupPoint;
use App\Model\Orm\Orm;
use App\Model\Orm\State\State;
use App\Model\Security\User;
use App\Model\ShoppingCart\ShoppingCartInterface;
use Brick\Money\Money;
use stdClass;

abstract class BaseDeliveryMethod implements DeliveryMethod
{

	public function __construct(
		private readonly ConfigService $configService,
		private readonly User $user,
		private readonly Orm $orm,
		private readonly ShoppingCartInterface $shoppingCart,
	)
	{
	}

	public function useDeliveryAddress(): bool
	{
		return $this->getDeliveryType() === DeliveryType::Physical;
	}

	public function supportsPickup(): bool
	{
		return false;
	}

	public function getPickupMarkersUrl(?DeliveryMethodConfiguration $methodConfiguration = null, ?State $state = null): ?string
	{
		if (!$this->supportsPickup()) {
			return null;
		}

		$apiUrlQuery = [];
		if ($methodConfiguration !== null) {
			$apiUrlQuery['deliveryMethodId'] = $methodConfiguration->id;
		}

		if ($state !== null) {
			$apiUrlQuery['stateId'] = $state->id;
		}

		return '/api/v1/pickup-point-markers' . (($apiUrlQuery !== []) ? '?' . http_build_query($apiUrlQuery) : '');
	}

	public function getPickupConfig(DeliveryMethodConfiguration $methodConfiguration, ?Money $price = null, ?PickupPoint $pickupPoint = null, ?State $state = null): stdClass|array
	{
		if (!$this->supportsPickup()) {
			return new stdClass();
		}

		$apiUrlQuery = [];
		if (!empty($methodConfiguration->id)) {
			$apiUrlQuery['deliveryMethodId'] = $methodConfiguration->id;
		}

		if ($state !== null) {
			$apiUrlQuery['stateId'] = $state->id;
		}

		$config = $this->configService->get('deliveryPicker');
		$config['searchApiUrl'] = '/api/v1/pickup-point' . ((!empty($apiUrlQuery)) ? '?' . http_build_query($apiUrlQuery) : '');
		$config['markersApiUrl'] = $this->getPickupMarkersUrl($methodConfiguration, $state);
		$config['pickupPointTypes'][] = $this->getPickupPointType($methodConfiguration, $price);
		$config['lastUsedPoints'] = [];

		if ($pickupPoint !== null && $methodConfiguration->id === $pickupPoint->deliveryMethod->id) {
			$config['initialSelectedPoint'] = $pickupPoint->asArray();
			$config['lastUsedPoints'][] = $config['initialSelectedPoint'];
		}

		if ($this->user->isLoggedIn()) {
			if (($user = $this->orm->user->getById($this->user->getId())) !== null && ($pickupPointId = $user->cf->pickupPoints->{$methodConfiguration->deliveryMethodUniqueIdentifier} ?? null) !== null) {
				$lastPickupPoint = $this->orm->pickupPoint->getById($pickupPointId);
				if ($lastPickupPoint !== null) {
					$config['initialSelectedPoint'] = $lastPickupPoint->asArray();
					$config['lastUsedPoints'] = [];
					$config['lastUsedPoints'][]     = $config['initialSelectedPoint'];
				}
			}
		}

		$config['country'] = $this->shoppingCart->getCountry()->code;

		return $config;
	}

	public function getPickupPointType(DeliveryMethodConfiguration $methodConfiguration, ?Money $price = null): array
	{
		$icon = '/static/img/illust/noimg.svg';
		/** @var LazyValue $marker */
		$marker = $methodConfiguration->cf->deliveryPayment?->marker ?? null;

		if ($marker instanceof LazyValue && ($markerImage = $marker->getEntity())) {
			assert($markerImage instanceof LibraryImage);
			$icon = $markerImage->getSize('xs')->src;
		}

		return [
			'id' => $methodConfiguration->id,
			'name' => $methodConfiguration->name,
			'icon' => $icon,
			'price' => $price?->getAmount()->toFloat() ?? 0,
		];
	}

	public function isAvailableForOrder(OrderProxy $order): bool
	{
		return !$order->hasElectronicProduct(strict: true);
	}

}
