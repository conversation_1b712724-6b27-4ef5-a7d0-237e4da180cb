<?php

declare(strict_types=1);

namespace App\Model\Orm\DeliveryMethod;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;
use App\Model\Orm\Traits\HasCamelCase;
use App\Model\Orm\Traits\HasStaticCache;
use App\Model\Orm\User\User;
use Brick\Money\Currency;
use Brick\Money\Money;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<DeliveryMethodConfiguration>
 */
final class DeliveryMethodConfigurationMapper extends DbalMapper
{

	use HasCamelCase;
	use HasStaticCache;

	/**
	 * @return literal-string
	 */
public function getTableName(): string
{
	return 'delivery_method';
}

	public function getFreeDelivery(Mutation $mutation, State $state, PriceLevel $priceLevel, Currency $currency): ?DeliveryMethodConfiguration
	{
		return $this->loadCache($this->createCacheKey('freeDelivery-', $mutation, $state, $priceLevel, $currency), function () use ($mutation, $state, $priceLevel, $currency) {
			$builder = $this->builder();
			$builder->select('[dm.*]');
			$builder->from('[' . $this->getTableName() . ']', 'dm');
			$builder->joinLeft('[delivery_method_x_state] AS [dmxs]', '[dmxs.deliveryMethodId] = [dm.id]');
			$builder->joinLeft('[delivery_method_price] AS [dmp]', '[dmp.deliveryMethodId] = [dm.id]');
			$builder->where('[dm.calculateFree] = 1 AND [dm.mutationId] = %i AND [dm.public] = 1 AND [dmxs.stateId] = %i AND [dmp.priceLevelId] = %i AND [dmp.stateId] = %i AND [dmp.freeFrom] IS NOT NULL AND [dmp.price_currency] = %s AND (SELECT COUNT(dmc.id) FROM delivery_method_currency AS dmc WHERE dmc.deliveryMethodId = [dm.id] AND dmc.currency = %s) > 0', $mutation->id, $state->id, $priceLevel->id, $state->id, $currency->getCurrencyCode(), $currency->getCurrencyCode());
			$builder->orderBy('[dmp.freeFrom] ASC');

			return $this->toEntity($builder);
		});
	}

	public function getFreeDeliveryAmount(Mutation $mutation, State $state, PriceLevel $priceLevel, Currency $currency): ?Money
	{
		$freeDelivery = $this->getFreeDelivery($mutation, $state, $priceLevel, $currency);
		if ($freeDelivery === null) {
			return null;
		}

		$price = $freeDelivery->getPrice($priceLevel, $state, $currency);
		bdump(Money::of($price->freeFrom, $price->price->currency));
		return Money::of($price->freeFrom, $price->price->currency);
	}

	/**
	 * @return ICollection<DeliveryMethodConfiguration>
	 */
	public function getAvailable(Mutation $mutation, State $state, PriceLevel $priceLevel, Currency $currency): ICollection
	{
		return $this->loadCache($this->createCacheKey('available-', $mutation, $state, $priceLevel, $currency), function () use ($mutation, $state, $priceLevel, $currency) {
			$builder = $this->builder();
			$builder->select('[dm.*]');
			$builder->from('[' . $this->getTableName() . ']', 'dm');
			$builder->joinLeft('[delivery_method_x_state] AS [dmxs]', '[dmxs.deliveryMethodId] = [dm.id]');
			$builder->joinLeft('[delivery_method_price] AS [dmp]', '[dmp.deliveryMethodId] = [dm.id]');
			//$builder->joinLeft('[delivery_method_currency] AS [dmc]', '[dmp.deliveryMethodId] = [dmc.id]');
			$builder->where('[dm.mutationId] = %i AND [dm.public] = 1 AND [dmxs.stateId] = %i AND [dmp.priceLevelId] = %i AND [dmp.stateId] = %i AND [dmp.price_currency] = %s AND (SELECT COUNT(dmc.id) FROM delivery_method_currency AS dmc WHERE dmc.deliveryMethodId = [dm.id] AND dmc.currency = %s) > 0', $mutation->id, $state->id, $priceLevel->id, $state->id, $currency->getCurrencyCode(), $currency->getCurrencyCode());
			$builder->addOrderBy('[dm.sort] ASC');

			return $this->toCollection($builder);
		});
	}

	final public function getPrefferedDelivery(User $user): DeliveryMethodConfiguration|null
	{
		return $this->loadCache($this->createCacheKey('preffered-', $user), function () use ($user) {
			return $this->toEntity($this->builder()
				->select('[dm.*]')
				->addSelect('count(od.deliveryMethodId) as deliveryOrder')
				->from('[' . $this->getTableName() . ']', 'dm')
				->joinLeft('[order_delivery] as [od]', '[od.deliveryMethodId] = [dm.id]')
				->joinLeft('[order] as [o]', '[o.deliveryId] = [od.id]')
				->where('o.userId = %i', $user->id)
				->groupBy('[dm.id]')
				->orderBy('[deliveryOrder] DESC'));
		});
	}

	public function hasFreeTransportsForProduct(Mutation $mutation, PriceLevel $priceLevel, State $state, Money $price, float $weight): bool
	{
		$results = $this->connection->query(
			'SELECT dm.id FROM %table as dm
          			LEFT JOIN delivery_method_price as dmp on (dm.id = dmp.deliveryMethodId)

	          		where
	          		    dm.public = 1 and mutationId = %i and dm.calculateFree = 1
	          		    and dmp.priceLevelId = %i and dmp.stateId = %i and freeFrom <= %f and price_currency = %s and (maxWeight <= %f or maxWeight is null)',
			$this->getTableName(),
			$mutation->id,
			$priceLevel->id,
			$state->id,
			$price->getAmount()->toFloat(),
			$mutation->currency->getCurrencyCode(),
			$weight
		);

		return $results->count() > 0;
	}

}
