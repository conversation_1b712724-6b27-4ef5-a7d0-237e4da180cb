<?php

declare(strict_types=1);

namespace App\Model\Orm\DeliveryMethod;

use App\Model\Orm\Price;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;
use Brick\Math\BigDecimal;
use Brick\Money\Money;
use Nextras\Orm\Entity\Entity;
use App\Model\Orm\BigDecimalContainer; // phpcs:ignore
/**
 * @property-read int $id {primary}
 * @property DeliveryMethodConfiguration $deliveryMethod {m:1 DeliveryMethodConfiguration::$prices}
 *
 * @property PriceLevel $priceLevel {m:1 PriceLevel, oneSided=true}
 * @property State $state {m:1 State, oneSided=true}
 *
 * @property Price $price {embeddable}
 * @property BigDecimal|null $freeFrom {wrapper BigDecimalContainer} {default null}
 * @property int|null $maxWeight
 * @property int|null $maxCodPrice
 * @property string|null $externalId {default null}
 */
final class DeliveryMethodPrice extends Entity
{

	public function getPrice(): Money
	{
		return $this->price->asMoney();
	}

	public function getFreeFrom(): ?Money
	{
		if ($this->freeFrom === null) {
			return null;
		}

		return Price::from(Money::of($this->freeFrom, $this->price->currency))->asMoney();
	}

}
