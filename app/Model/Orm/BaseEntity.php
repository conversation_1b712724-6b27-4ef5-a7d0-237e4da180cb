<?php declare(strict_types = 1);

namespace App\Model\Orm;

use App\PostType\Core\Model\Publishable;
use App\PostType\Core\Model\Validatable;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;

/**
 * @method int getId()
 * @property int $id {primary}
 *
 * RELATIONS
 *
 * VIRTUAL
 */
abstract class BaseEntity extends Entity
{
	public function isPublished(): bool
	{
		$isValid = true;
		$isPublic = true;

		if ($this instanceof Publishable) {
			$isPublic = $this->getIsPublic();
		}

		if ($this instanceof Validatable) {
			$now = new DateTimeImmutable();
			$isValid = $this->getPublicTo() > $now && $this->getPublicFrom() < $now;
		}

		return $isValid && $isPublic;
	}
}
