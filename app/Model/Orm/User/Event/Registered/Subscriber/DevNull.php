<?php

declare(strict_types=1);

namespace App\Model\Orm\User\Event\Registered\Subscriber;


use App\Event\Registered;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class DevNull implements EventSubscriberInterface {

	public function __invoke(Registered $event): void
	{
		bdump('event fired', self::class);
	}
	public static function getSubscribedEvents(): array
	{
		return [
			Registered::class => [
				['__invoke', 5],
			],
		];
	}
}
