<?php

declare(strict_types=1);

namespace App\Model\Orm\User\Event\Registered\Subscriber;


use App\Event\Registered;
use App\Model\Email\CommonFactory;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use <PERSON>\Debugger;
use <PERSON>\ILogger;

final readonly class SendEmail implements EventSubscriberInterface {

	public function __construct(
		private CommonFactory $commonEmailFactory,
	)
	{
	}

	public function __invoke(Registered $event): void
	{
		try {
			$user = $event->user;
			$data = [
				'email' => $user->email,
				//'isNewsletter' => $user->isNewsletter,
			];
			$this->commonEmailFactory->create()->send('', $user->email, 'login', $data);
		} catch (\Throwable $e) {
			bdump($e);
			Debugger::log($e, ILogger::EXCEPTION);
		}
	}

	public static function getSubscribedEvents(): array
	{
		return [
			Registered::class => [
				['__invoke', 10],
			],
		];
	}
}
