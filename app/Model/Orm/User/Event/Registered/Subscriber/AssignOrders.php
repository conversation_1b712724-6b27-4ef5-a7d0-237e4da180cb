<?php

declare(strict_types=1);

namespace App\Model\Orm\User\Event\Registered\Subscriber;


use App\Event\Registered;
use App\Model\Orm\Orm;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use <PERSON>\Debugger;
use <PERSON>\ILogger;

final readonly class AssignOrders implements EventSubscriberInterface {

	public function __construct(
		private Orm $orm
	)
	{
	}

	public function __invoke(Registered $event): void
	{
		try {
			$this->orm->user->assignOrders($event->user);
		} catch (\Throwable $exception) {
			bdump($exception);
			Debugger::log($exception, ILogger::EXCEPTION);
		}
	}

	public static function getSubscribedEvents(): array
	{
		return [
			Registered::class => [
				['__invoke', 30],
			],
		];
	}
}
