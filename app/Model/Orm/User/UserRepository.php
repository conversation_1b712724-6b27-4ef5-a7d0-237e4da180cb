<?php declare(strict_types = 1);

namespace App\Model\Orm\User;

use App\Model\Orm\Mutation\Mutation;
use Nette\Http\SessionSection;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method User|null getById($id)
 * @method ICollection<User> findByFilter(SessionSection $filter, \App\Model\Security\User $user, string $order)
 * @method void assignOrders(User $user)
 *
 * @extends Repository<User>
 */
final class UserRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [User::class];
	}

	public function getByEmail(string $email, Mutation $mutation): ?User
	{
		return $this->getBy([
			'email' => $email,
			'mutations->id' => $mutation->id,
		]);
	}

	public function getByOrderId(int $orderid): User|null/** @phpstan-ignore-line */
	{
		return null; //TODO implement after order is ready;
	}

	public function getByFacebookId(string $facebookId): User|null
	{
		/**
		 * @var User|null $user
		 */
		$user = $this->getBy(['facebookId' => $facebookId]);

		return $user;
	}

	public function getBySeznamId(string $seznamId): User|null
	{
		/**
		 * @var User|null $user
		 */
		$user = $this->getBy(['seznamId' => $seznamId]);

		return $user;
	}

	/**
	 * @return ICollection<User>
	 */
	public function findLectors(): ICollection
	{
		return $this->findBy([
			'role' => User::ROLE_LECTOR,
		]);
	}

	public function getByExtId(int|string $id): ?User
	{
		return $this->getBy(['extId' => (string) $id]);
	}

}
