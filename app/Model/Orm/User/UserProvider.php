<?php
declare(strict_types = 1);

namespace App\Model\Orm\User;

final class UserProvider {

	public ?User $userEntity = null;
	public function __construct(
		public \Nette\Security\User $userSecurity,
		private readonly UserRepository $userRepository,
	) {
		if ($this->userSecurity->id && $this->userSecurity->isLoggedIn()) {
			$userEntity = $this->userRepository->getById($this->userSecurity->getId());
			if ($userEntity && $userEntity->id) {
				$this->userEntity = $userEntity;
			}
		}
	}
}
