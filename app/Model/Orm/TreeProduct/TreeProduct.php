<?php

declare(strict_types=1);

namespace App\Model\Orm\TreeProduct;

use App\Model\Orm\Product\Product;
use App\PostType\Page\Model\Orm\Tree;
use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property int $sort
 * @property string $type {enum self::TYPE_*}
 *
 *
 * RELATIONS
 * @property Tree $tree {m:1 Tree::$treeProducts}
 * @property Product $product {m:1 Product::$treeProducts}
 *
 *
 * VIRTUAL
 */
class TreeProduct extends Entity
{

	public const TYPE_NORMAL_TO_PRODUCT = 'attachedToProduct';
	public const TYPE_NORMAL_TO_TREE = 'attachedToTree';
	public const TYPE_WORKSHOP = 'workshop';
	public const TYPE_ARTICLE = 'article';

}
