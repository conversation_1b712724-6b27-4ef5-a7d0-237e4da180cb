<?php

declare(strict_types=1);

namespace App\Model\Orm\PaymentMethod;

use function array_values;
use function sprintf;

final class PaymentMethodRegistry
{

	/** @var array<string, PaymentMethod> */
	private array $paymentMethods = [];

	/**
	 * @param PaymentMethod[] $paymentMethods
	 */
	public function __construct(
		array $paymentMethods,
	)
	{
		foreach ($paymentMethods as $paymentMethod) {
			$this->paymentMethods[$paymentMethod->getUniqueIdentifier()] = $paymentMethod;
		}
	}

	/**
	 * @return list<PaymentMethod>
	 */
	public function list(): array
	{
		return array_values($this->paymentMethods);
	}

	public function get(string $uniqueIdentifier): PaymentMethod
	{
		return $this->paymentMethods[$uniqueIdentifier] ?? throw new \InvalidArgumentException(sprintf('Payment method with ID "%s" not found.', $uniqueIdentifier));
	}

}
