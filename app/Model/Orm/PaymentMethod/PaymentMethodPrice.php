<?php

declare(strict_types=1);

namespace App\Model\Orm\PaymentMethod;

use App\Model\Orm\Price;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;
use Brick\Money\Money;
use Nextras\Orm\Entity\Entity;
use App\Model\Orm\BigDecimalContainer; // phpcs:ignore

/**
 * @property-read int $id {primary}
 * @property PaymentMethodConfiguration $paymentMethod {m:1 PaymentMethodConfiguration::$prices}
 *
 * @property PriceLevel $priceLevel {m:1 PriceLevel, oneSided=true}
 * @property State $state {m:1 State, oneSided=true}
 *
 * @property Price $price {embeddable}
 * @property string|null $externalId {default null}
 */
final class PaymentMethodPrice extends Entity
{

	public function getPrice(): Money
	{
		return $this->price->asMoney();
	}

}
