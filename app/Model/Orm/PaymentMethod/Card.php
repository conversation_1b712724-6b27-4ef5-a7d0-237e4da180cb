<?php

declare(strict_types=1);

namespace App\Model\Orm\PaymentMethod;

use App\Model\Orm\CardPayment\CardPaymentStatus;
use App\Model\Orm\CardPayment\PaymentGateway\GopayPaymentGateway;
use App\Model\Orm\CardPayment\PaymentGateway\PaymentGateway;
use App\Model\Orm\CardPayment\PaymentGateway\PaymentGatewayRegistry;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderProxy;
use App\Model\Orm\Order\Payment\CardPaymentInformation;
use App\Model\Orm\Order\Payment\PaymentType;
use function in_array;

final readonly class Card implements PaymentMethod
{

	public const ID = 'Card';

	public function __construct(
		private PaymentGatewayRegistry $gatewayRegistry,
	)
	{
	}

	public function getUniqueIdentifier(): string
	{
		return self::ID;
	}

	public function getPaymentType(): PaymentType
	{
		return PaymentType::Card;
	}

	public function isAvailableForOrder(OrderProxy $order): bool
	{
		return true;
	}

	public function onOrderPlaced(Order $order): void
	{
		$paymentInformation = $order->payment?->information;
		assert($paymentInformation instanceof CardPaymentInformation);

		$gateway = $this->getPaymentGateway();
		$cardPayment = $gateway->createPayment($paymentInformation);
		$paymentInformation->addPendingPayment($cardPayment);
	}

	public function onOrderCanceled(Order $order): void
	{
		$paymentInformation = $order->payment?->information;
		assert($paymentInformation instanceof CardPaymentInformation);

		foreach ($paymentInformation->payments as $payment) {
			if (in_array($payment->status, [CardPaymentStatus::Authorized, CardPaymentStatus::Settled], true)) {
				$gateway = $this->gatewayRegistry->get($payment->paymentGatewayUniqueIdentifier);
				$gateway->refundPayment($payment);
			}
		}
	}
	public function getGroup(): ?PaymentMethodGroup
	{
		return null;
	}

	public function getPaymentGateway(): PaymentGateway
	{
		return $this->gatewayRegistry->get(GopayPaymentGateway::ID);
	}

}
