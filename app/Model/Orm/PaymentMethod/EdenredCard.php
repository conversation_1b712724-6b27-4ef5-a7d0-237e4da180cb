<?php

declare(strict_types=1);

namespace App\Model\Orm\PaymentMethod;

use App\Model\Orm\CardPayment\CardPaymentStatus;
use App\Model\Orm\CardPayment\PaymentGateway\EdenredPaymentGateway;
use App\Model\Orm\CardPayment\PaymentGateway\PaymentGateway;
use App\Model\Orm\CardPayment\PaymentGateway\PaymentGatewayRegistry;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderProxy;
use App\Model\Orm\Order\Payment\CardPaymentInformation;
use App\Model\Orm\Order\Payment\PaymentType;

final readonly class EdenredCard implements PaymentMethod
{

	public const ID = 'EdenredCard';
	public const GROUP = 'BenefitCard';

	public function __construct(
		private PaymentGatewayRegistry $gatewayRegistry,
		private PaymentMethodGroupFactory $paymentMethodGroupFactory,
	)
	{
	}
	public function getUniqueIdentifier(): string
	{
		return self::ID;
	}

	public function getPaymentType(): PaymentType
	{
		return PaymentType::EdenredCard;
	}

	public function isAvailableForOrder(OrderProxy $order): bool
	{
		return true;
	}

	public function onOrderPlaced(Order $order): void
	{
		$paymentInformation = $order->payment?->information;
		assert($paymentInformation instanceof CardPaymentInformation);

		/** @var EdenredPaymentGateway $gateway */
		$gateway = $this->getPaymentGateway();
		$cardPayment = $gateway->createPayment($paymentInformation);
		$paymentInformation->addPendingPayment($cardPayment);
	}

	public function onOrderCanceled(Order $order): void
	{
		$paymentInformation = $order->payment?->information;
		assert($paymentInformation instanceof CardPaymentInformation);

		foreach ($paymentInformation->payments as $payment) {
			if (in_array($payment->status, [CardPaymentStatus::Authorized, CardPaymentStatus::Settled], true)) {
				$gateway = $this->gatewayRegistry->get($payment->paymentGatewayUniqueIdentifier);
				$gateway->refundPayment($payment);
			}
		}
	}

	public function getGroup(): PaymentMethodGroup
	{
		return $this->paymentMethodGroupFactory->create(self::GROUP);
	}

	public function getPaymentGateway(): PaymentGateway
	{
		return $this->gatewayRegistry->get(EdenredPaymentGateway::ID);
	}

}
