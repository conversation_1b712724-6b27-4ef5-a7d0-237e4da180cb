<?php

declare(strict_types=1);

namespace App\Model\Orm\PaymentMethod;

use App\Exceptions\LogicException;
use App\Model\Orm\BaseEntity;
use App\Model\Orm\JsonContainer; // phpcs:ignore
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderProxy;
use App\Model\Orm\Order\Payment\OrderPayment;
use App\Model\Orm\Order\Payment\PaymentType;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;
use App\Model\Orm\State\StateModel;
use App\Model\Orm\State\VatRate;
use App\Model\Orm\Traits\HasCustomFields;
use App\Model\ShoppingCart\ShoppingCartInterface;
use App\Model\VatCalculator;
use App\PostType\Core\Model\Publishable;
use Brick\Math\BigDecimal;
use Brick\Money\Currency;
use Brick\Money\Money;
use Latte\Loaders\StringLoader;
use Latte\Sandbox\SecurityPolicy;
use Nette\Bridges\ApplicationLatte\LatteFactory;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property-read int $id {primary}
 * @property ?string $externalId {default null}
 *
 * @property string $name {default ''}
 * @property string $desc {default ''}
 * @property ?string $pageText {default null}
 * @property string|null $tooltip {default null}
 * @property bool $public {default false}
 *
 * @property int $sort {default 0}
 * @property bool $isRecommended {default false}
 * @property bool $pageShow {default false}
 *
 * @property string $paymentMethodUniqueIdentifier
 * @property Mutation $mutation {m:1 Mutation, oneSided=true}
 * @property-read ManyHasMany<State> $countries {m:m State, isMain=true, oneSided=true}
 *
 * @property-read OneHasMany<PaymentMethodPrice> $prices {1:m PaymentMethodPrice::$paymentMethod}
 * @property-read OneHasMany<OrderPayment> $orderItems {1:m OrderPayment::$paymentMethod}
 * @property-read OneHasMany<PaymentMethodCurrency> $currencies {1:m PaymentMethodCurrency::$paymentMethod}
 * @property array $vats {container JsonContainer}
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 * @property ArrayHash|null $cf {virtual}
 */
final class PaymentMethodConfiguration extends BaseEntity implements Publishable
{

	use HasCustomFields;

	public string $uid = 'paymentMethod';

	private PaymentMethodRegistry $paymentMethodRegistry;

	private StateModel $stateModel;

	private LatteFactory $latteFactory;

	public function injectServices(
		PaymentMethodRegistry $registry,
		StateModel $stateModel,
		LatteFactory $latteFactory,
	): void
	{
		$this->paymentMethodRegistry = $registry;
		$this->stateModel = $stateModel;
		$this->latteFactory = $latteFactory;
	}

	public function getPaymentMethod(): PaymentMethod
	{
		return $this->paymentMethodRegistry->get($this->paymentMethodUniqueIdentifier);
	}

	public function price(PriceLevel $priceLevel, State $country, OrderProxy $order): ?Money
	{
		$price = $this->getPrice($priceLevel, $country, $order->getCurrency());

		if ($price !== null) {
			// TODO: uprava ceny, ak platby maju nejake podminky
			return VatCalculator::priceWithoutVat($price->price->asMoney(), $this->vatRate($country));
		}

		return null;
	}

	public function priceVat(PriceLevel $priceLevel, State $country, OrderProxy $order): ?Money
	{
		$price = $this->price($priceLevel, $country, $order);
		if ($price === null) {
			return null;
		}

		return VatCalculator::priceWithVat($price, $this->vatRate($country));
	}

	public function getPrice(PriceLevel $priceLevel, State $country, Currency $currency): ?PaymentMethodPrice
	{
		return $this->prices->toCollection()->getBy([
			'priceLevel' => $priceLevel,
			'state' => $country,
			'price->currency' => $currency->getCurrencyCode(),
		]);
	}

	public function getVatRate(?State $country = null): VatRate
	{
		$countryId = $country?->id ?? State::DEFAULT_ID;
		return isset($this->vats->$countryId) ? VatRate::from($this->vats->$countryId) : VatRate::Standard;
	}

	public function vatRate(?State $state = null): BigDecimal
	{
		$vatRateType = $this->getVatRate($state);

		$stateId = $state ? $state->id : State::DEFAULT_ID;
		$vatRates = $this->stateModel->getAllVatRatesValues($this->mutation);
		$rate = $vatRates[$stateId]->get($vatRateType);

		if ($rate === null) {
			throw new LogicException('Unknown vatRate.');// nekonzistentni stav, ktery musime odhalit, nelze pouzit defaultni sazbu, prodavalo by se spatne a neprislo by se na to -> je to tak?
		}

		return $rate;
	}

	public function getIsPublic(): bool
	{
		return $this->public;
	}

	public function setIsPublic(bool $isPublic): void
	{
		$this->public = $isPublic;
	}

	public function getThankYouPageText(Order $order): ?string
	{
		$type = $order->delivery->deliveryMethod->getDeliveryMethod()->getDeliveryType()->value;
		$text = $this->cf->paymentText->{$type}->text ?? null;
		if ($text === null) {
			return null;
		}
		return $this->latteRender($text, ['order' => $order]);
	}

	public function getThankYouPageNote(Order $order): ?string
	{
		$type = $order->delivery->deliveryMethod->getDeliveryMethod()->getDeliveryType()->value;
		$text = $this->cf->paymentText->{$type}->note ?? null;
		if ($text === null) {
			return null;
		}
		return $this->latteRender($text, ['order' => $order]);
	}

	private function latteRender(string $text, array $params = []): string
	{
		$text = str_replace('&gt;', '>', $text);

		$latte = $this->latteFactory->create();
		$latte->setLoader(new StringLoader(['text' => $text]));
		$latte->setPolicy(SecurityPolicy::createSafePolicy());

		return $latte->renderToString('text', $params);
	}


	public function isAllowed(PriceLevel $priceLevel, State $country, ShoppingCartInterface $shoppingCart): bool
	{
		return $this->loadCache($this->createCacheKey('isAllowed', $priceLevel, $country, $shoppingCart), function () use ($priceLevel, $country, $shoppingCart) {
			if ($this->getPaymentMethod()->getPaymentType() === PaymentType::CashOnDelivery && ($maxCodPrice = $shoppingCart->getDelivery()?->deliveryMethod->getMaxCodPrice($priceLevel, $country, $shoppingCart)) !== null) {
				return $shoppingCart->getTotalPriceVat()->isLessThan($maxCodPrice);
			}

			return true;
		});
	}

}
