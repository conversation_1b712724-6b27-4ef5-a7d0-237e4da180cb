<?php

declare(strict_types=1);

namespace App\Model\Orm\PaymentMethod;

use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderProxy;
use App\Model\Orm\Order\Payment\PaymentType;

final class Legacy implements PaymentMethod
{

	public const ID = 'Legacy';

	public function getUniqueIdentifier(): string
	{
		return self::ID;
	}

	public function getPaymentType(): PaymentType
	{
		return PaymentType::LegacyPayment;
	}

	public function isAvailableForOrder(OrderProxy $order): bool
	{
		return true;
	}

	public function onOrderPlaced(Order $order): void
	{
	}

	public function onOrderCanceled(Order $order): void
	{
	}

	public function getGroup(): ?PaymentMethodGroup
	{
		return null;
	}

	public function getPaymentGateway(): null
	{
		return null;
	}

}
