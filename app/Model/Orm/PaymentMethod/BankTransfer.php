<?php

declare(strict_types=1);

namespace App\Model\Orm\PaymentMethod;

use App\Exceptions\LogicException;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderProxy;
use App\Model\Orm\Order\Payment\BankTransferPaymentInformation;
use App\Model\Orm\Order\Payment\PaymentType;
use DateInterval;
use App\Utils\QrCodePayment\QrCodePayment;
use function assert;
use function preg_replace;

final class BankTransfer implements PaymentMethod
{

	public const ID = 'BankTransfer';

	public function getUniqueIdentifier(): string
	{
		return self::ID;
	}

	public function getPaymentType(): PaymentType
	{
		return PaymentType::BankTransfer;
	}

	public function isAvailableForOrder(OrderProxy $order): bool
	{
		return true;
	}

	public function onOrderPlaced(Order $order): void
	{
		$paymentInformation = $order->payment?->information;
		assert($paymentInformation instanceof BankTransferPaymentInformation);

		$paymentInformation->variableSymbol = $this->formatVariableSymbol($order);
		$paymentInformation->dueDate = $order->placedAt?->add(new DateInterval('P14D'));
	}

	public function onOrderCanceled(Order $order): void
	{
	}

	private function formatVariableSymbol(Order $order): string
	{
		if ($order->orderNumber === null) {
			throw new LogicException('Cannot create variable symbol on non placed order.');
		}

		return preg_replace('/\D+/', '', $order->orderNumber);
	}

	public function getQrCode(Order $order): ?string
	{
		$paymentAccount = $order->getPaymentAccount();
		$currency = $order->getCurrency()->getCurrencyCode();
		$mutation = $order->mutation;
		$totalAmountFloat = $order->getTotalPriceVat(withDelivery: true)->getAmount()->toFloat();
		$variableSymbol = $order->getVariableSymbol();

		if (($iban = $paymentAccount->iban) === null) {
			throw new LogicException('Unknown IBAN');
		}

		if (($swift = $paymentAccount->swift) === null && $mutation->langCode === Mutation::CODE_SK) {
			throw new LogicException('Unknown SWIFT');
		}

		$qrPayment = match ($mutation->langCode) {
			Mutation::CODE_CS => QrCodePayment::createCzPayment($iban),
			Mutation::CODE_SK => QrCodePayment::createSkPayment($iban, $swift),
			default => null
		};

		if ($qrPayment === null) {
			return null;
		}

		$qrPayment
			->setVariableSymbol($variableSymbol)
			->setAmount($totalAmountFloat)
			->setCurrency($currency);

		//okamzita platba
		if (method_exists($qrPayment, 'setInstantPayment')) {
			$qrPayment->setInstantPayment(true);
		}

		return $qrPayment->getQrCode()->getDataUri();
	}
	public function getGroup(): ?PaymentMethodGroup
	{
		return null;
	}

	public function getPaymentGateway(): null
	{
		return null;
	}

}
