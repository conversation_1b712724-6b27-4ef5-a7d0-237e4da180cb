<?php

declare(strict_types=1);

namespace App\Model\Orm\PaymentMethod;

use App\Model\Orm\DeliveryMethod\DeliveryMethodConfiguration;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;
use App\Model\Orm\Traits\HasCamelCase;
use App\Model\Orm\Traits\HasStaticCache;
use Brick\Money\Currency;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<PaymentMethodConfiguration>
 */
final class PaymentMethodConfigurationMapper extends DbalMapper
{

	use HasCamelCase;
	use HasStaticCache;

	/**
	 * @return literal-string
	 */
public function getTableName(): string
{
	return 'payment_method';
}

	/**
	 * @return ICollection<PaymentMethodConfiguration>
	 */
	public function getAvailable(Mutation $mutation, State $state, PriceLevel $priceLevel, Currency $currency): ICollection
	{
		return $this->loadCache($this->createCacheKey('payments-available-', $mutation, $state, $priceLevel, $currency), function () use ($mutation, $state, $priceLevel, $currency) {
			$builder = $this->builder();
			$builder->select('[pm.*]');
			$builder->from('[' . $this->getTableName() . ']', 'pm');
			$builder->joinLeft('[payment_method_price] AS [pmp]', '[pmp.paymentMethodId] = [pm.id]');
			$builder->where('[pm.mutationId] = %i AND [pm.public] = 1 AND [pmp.priceLevelId] = %i AND [pmp.stateId] = %i AND [pmp.price_currency] = %s AND (SELECT COUNT(pmc.id) FROM payment_method_currency AS pmc WHERE pmc.paymentMethodId = [pm.id] AND pmc.currency = %s) > 0', $mutation->id, $priceLevel->id, $state->id, $currency->getCurrencyCode(), $currency->getCurrencyCode());
			$builder->addOrderBy('[pm.sort] ASC');

			return $this->toCollection($builder);
		});
	}

	/**
	 * @return ICollection<PaymentMethodConfiguration>
	 */
	public function getAvailableByDelivery(DeliveryMethodConfiguration $deliveryMethodConfiguration, Mutation $mutation, State $state, PriceLevel $priceLevel, Currency $currency): ICollection
	{
		return $this->loadCache($this->createCacheKey('payments-available-by-delivery-', ...func_get_args()), function () use ($mutation, $state, $priceLevel, $deliveryMethodConfiguration, $currency) {
			$allowedPayments = $deliveryMethodConfiguration->allowedPaymentMethods->toCollection()->fetchPairs(null, 'id');

			if ($allowedPayments === []) {
				return new EmptyCollection();
			}

			$builder = $this->builder();
			$builder->select('[pm.*]');
			$builder->from('[' . $this->getTableName() . ']', 'pm');
			$builder->joinLeft('[payment_method_price] AS [pmp]', '[pmp.paymentMethodId] = [pm.id]');
			$builder->where('[pm.id] IN %i[] AND [pm.mutationId] = %i AND [pm.public] = 1 AND [pmp.priceLevelId] = %i AND [pmp.stateId] = %i AND [pmp.price_currency] = %s AND (SELECT COUNT(pmc.id) FROM payment_method_currency AS pmc WHERE pmc.paymentMethodId = [pm.id] AND pmc.currency = %s) > 0', $allowedPayments, $mutation->id, $priceLevel->id, $state->id, $currency->getCurrencyCode(), $currency->getCurrencyCode());
			$builder->addOrderBy('[pm.sort] ASC');

			return $this->toCollection($builder);
		});
	}

}
