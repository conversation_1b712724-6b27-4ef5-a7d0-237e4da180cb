<?php

declare(strict_types=1);

namespace App\Model\Orm\Stock;

use App\Model\Orm\Supply\Supply;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property int $id {primary}
 * @property string|null $name
 * @property string|null $alias {enum self::ALIAS_*} {default self::ALIAS_SHOP}
 * @property string|null $address
 * @property int|null $order
 * @property string|null $deliveryHour
 * @property string|null $extId
 *
 *
 * RELATIONS
 * @property OneHasMany<Supply> $supplies {1:M Supply::$stock}
 *
 *
 * VIRTUAL
 */
class Stock extends Entity
{

	public const ALIAS_SHOP = 'shop';
	public const ALIAS_SUPPLIER_STORE = 'supplier_store';

}
