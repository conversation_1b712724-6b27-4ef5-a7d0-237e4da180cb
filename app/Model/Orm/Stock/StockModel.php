<?php declare(strict_types=1);

namespace App\Model\Orm\Stock;

use App\Model\Orm\Supply\DTO\SupplyDTO;
use App\Model\Orm\Traits\HasStaticCache;

final class StockModel
{

	use HasStaticCache;

	public function __construct(
		private readonly StockRepository $stockRepository
	)
	{
	}

	public function getStockBySupplyDTO(SupplyDTO $supply): Stock
	{
		$generator = function () use ($supply) {
			return $this->stockRepository->getById($supply->stockId);
		};

		return $this->tryLoadCache(
			$this->createCacheKey('stock', $supply->stockId),
			$generator
		);
	}
}
