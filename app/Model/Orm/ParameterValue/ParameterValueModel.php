<?php declare(strict_types = 1);

namespace App\Model\Orm\ParameterValue;

use App\Model\CustomField\CustomFields;
use App\Model\Erp;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\Parameter\ParameterRepository;
use App\Model\Orm\Product\Product;
use App\Model\Orm\String\StringModel;
use App\PostType\Page\Model\Orm\Tree;
use Nette\Utils\ArrayHash;
use Nette\Utils\Strings;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\IEntity;
use stdClass;
use function assert;

final class ParameterValueModel
{

	public function __construct(
		private readonly ParameterValueRepository $parameterValueRepository,
		private readonly ParameterRepository $parameterRepository,
		private readonly Orm $orm,
		private readonly CustomFields $customFields,
		private readonly StringModel $stringModel,
	)
	{
	}


	public function removeValue(ParameterValue $parameterValue, IEntity $holder, bool $all = false): void
	{
		if ($holder instanceof Tree) {
			if ($all) {
				$this->orm->tree->removeParameter($holder, $parameterValue->parameter);
			} else {
				$this->orm->tree->removeParameterValue($holder, $parameterValue);
			}
		} elseif ($holder instanceof Product) {
			if ($all) {
				$this->orm->product->removeParameter($holder, $parameterValue->parameter);
			} else {
				$this->orm->product->removeParameterValue($holder, $parameterValue);
			}
		}

		if ($parameterValue->parameter->isSimple) {

			// is simple --> remove value
			$this->orm->parameterValue->remove($parameterValue);
		}
	}


	public function addValue(ParameterValue $parameterValue, IEntity $holder): void
	{
		if ($holder instanceof Tree) {
			$this->orm->tree->addParameterValue($holder, $parameterValue);
		} elseif ($holder instanceof Product) {
			$this->orm->product->addParameterValue($holder, $parameterValue);
		}
	}


	public function handleParameterValuesAttachment(IEntity $holder, Parameter $parameter, ?int $parameterValueId = null, mixed $newValue = null): void
	{
		if ($newValue || ($parameter->type === Parameter::TYPE_BOOL && $newValue === '0')) {
			if (!$parameterValueId) {
				$parameterValue = new ParameterValue();
				$parameterValue->internalValue = $newValue;
				$parameterValue->internalAlias = Strings::webalize($parameterValue->internalValue);
				$parameterValue->parameter = $parameter;
				$parameterValue->parameterSort = $parameter->sort;
				$this->orm->parameterValue->persistAndFlush($parameterValue);

				$this->addValue($parameterValue, $holder);
			} else {

				$parameterValue = $this->orm->parameterValue->getById($parameterValueId);
				if ($parameterValue->internalValue !== $newValue) {
					// update simple value
					$parameterValue->internalValue = $newValue;
					$parameterValue->internalAlias = Strings::webalize($parameterValue->internalValue);
					$this->orm->parameterValue->persistAndFlush($parameterValue);
					$this->addValue($parameterValue, $holder);
				}
			}
		} else {
			// delete simple value
			$parameterValue = $this->orm->parameterValue->getById($parameterValueId);
			if ($parameterValue) {
				$this->removeValue($parameterValue, $holder);
			}
		}
	}

	public function getVariantParameterValues(): array
	{
		$ret = [];

		foreach ($this->orm->parameter->findBy(['variantParameter' => 1]) as $parameter) {
			foreach ($parameter->options as $option) {
				$ret[$parameter->uid][$option->id] = $option;
			}
		}

		return $ret;
	}

	public function handleParameterValuesSelectAttachment(IEntity $holder, Parameter $parameter, mixed $parameterValuesData): void
	{
		assert(method_exists($holder, 'flushParam'));
		assert(method_exists($holder, 'getParameterValueById'));
		$holder->flushParam();
		$parameterValue = $this->orm->parameterValue->getById($parameterValuesData);
		$presentValue = $holder->getParameterValueById($parameter->id);

		if ($presentValue !== $parameterValue) {
			// budu pro select pridavat novou hodnotu
			// muzu smazat vsechny nynejsi
			if ($presentValue) {
				$this->removeValue($presentValue, $holder, true);
			}

			$this->addValue($parameterValue, $holder);
		}
	}

	public function handleParameterValuesMultiSelectAttachment(IEntity $holder, Parameter $parameter, mixed $parameterValuesData): void
	{
		assert(method_exists($holder, 'flushParam'));
		assert(method_exists($holder, 'getParameterValueById'));

		$holder->flushParam();
		$parameterValues = $this->orm->parameterValue->findByIds($parameterValuesData);
		$presentValues = $holder->getParameterValueById($parameter->id);

		$presentValuesTmp = [];

		if ($presentValues) {
			foreach ($presentValues as $presentValue) {
				$presentValuesTmp[$presentValue->id] = $presentValue;
			}
		}

		foreach ($parameterValues as $parameterValue) {
			assert($parameterValue instanceof ParameterValue);
			if (isset($presentValuesTmp[$parameterValue->id])) {
				// zasilana hodnota je v present hodnotach -- neni potreba nic delat
				unset($presentValuesTmp[$parameterValue->id]);
			} else {
				$this->addValue($parameterValue, $holder);
			}

			$this->addValue($parameterValue, $holder);
		}

		foreach ($presentValuesTmp as $presentValue) {
			// odstranit stare hodnoty ktere nejsou nove zasílány
			$this->removeValue($presentValue, $holder);
		}
	}

	public function saveValueDetail(ParameterValue $parameterValue, array $valuesAll): void
	{
		if (isset($valuesAll['setup']['cf'])) {
			$parameterValue->cf = $this->customFields->prepareDataToSave($valuesAll['setup']['cf']);
		} else {
			$parameterValue->cf = new ArrayHash();
		}

		$this->orm->parameterValue->persistAndFlush($parameterValue);
	}

	public function createNewValue(Parameter $parameter, string $cleanValue): ParameterValue
	{
		$newParameterValue = new ParameterValue();
		$newParameterValue->internalValue = $cleanValue;
		$newParameterValue->internalAlias = Strings::webalize($cleanValue);
		$parameter->options->add($newParameterValue);
		$this->parameterValueRepository->persistAndFlush($newParameterValue);
		return $newParameterValue;
	}

	public function crateNewValueFromErp(Parameter $parameter, Erp\Entity\Parameter $extParameter, Mutation $mutation): ParameterValue
	{
		$lastSort = $this->orm->parameterValue->findBy(['parameter' => $parameter])->orderBy('sort', ICollection::DESC)->limitBy(1)->fetch();
		$lastSort = isset($lastSort) ? $lastSort->sort + 1 : 1;

		$parameterValue = new ParameterValue();
		$this->orm->parameterValue->attach($parameterValue);

		$parameterValue->internalValue = $extParameter->valueName;
		$parameterValue->internalAlias = Strings::webalize($extParameter->valueName);
		$parameterValue->extId = $extParameter->valueExtId;
		$parameterValue->sort = $lastSort;
		$parameter->options->add($parameterValue);

		$parameterValue = $this->orm->parameterValue->persistAndFlush($parameterValue);

		$this->handleParameterValueTranslations($parameterValue, $mutation);

		return $parameterValue;
	}

	private function handleParameterValueTranslations(ParameterValue $parameterValue, Mutation $mutation): void
	{
		$keyMap = [
			'pvalue' => 'internalValue',
			'pvalue_alias' => 'internalAlias',
		];

		foreach ($keyMap as $translationName => $propName) {
			if (isset($parameterValue->$propName)) {
				$key = $translationName . '_' . $parameterValue->id;
				$newValue = trim($parameterValue->$propName);

				$this->stringModel->saveTranslation(
					mutation: $mutation,
					key: $key,
					newValue: $newValue,
					deleteOnEmptyString: true,
				);
			}
		}
	}

	public function attachParameterValue(Product $product, string $parameterUid, string $cleanValue, ?true &$isCreated = null): ParameterValue
	{
		$parameter = $this->parameterRepository->getBy(['uid' => $parameterUid]);
		if ($parameter === null) {
			throw new \LogicException(sprintf("Missing parameter UID: %s", $parameterUid));
		}

		$parameterValue = $parameter->options->toCollection()->getBy(['internalAlias' => Strings::webalize($cleanValue)]);
		if ($parameterValue === null) {
			$parameterValue = $this->createNewValue($parameter, $cleanValue);
			$isCreated = true;
		}

		$this->addValue($parameterValue, $product);
		return $parameterValue;
	}


	public function setParametersForProduct(array $data, Product $product): void
	{
		$selectedParameterValuesIds = [];
		if (isset($data['parameterValue'])) {
			foreach ($data['parameterValue'] as $parameterId => $parameterData) {
				$parameter = $this->orm->parameter->getById($parameterId);

				if ($parameter->isSimple) {
					$simpleParameterValue = $parameterData;
					if ($simpleParameterValue !== '') {
						$parameterValue = $parameter->options->toCollection()->getBy(['internalValue' => $simpleParameterValue]);
						if (!$parameterValue) {
							$parameterValue = new ParameterValue();
							$parameterValue->parameter = $parameter;
							$parameterValue->internalValue = $simpleParameterValue;
							$this->orm->parameterValue->persistAndFlush($parameterValue);
							$parameterValue->internalAlias = (string)$parameterValue->id;
							$this->orm->parameterValue->persistAndFlush($parameterValue);
						}

						$selectedParameterValuesIds[] = $parameterValue->id;
						$res = $this->orm->product->addParameterValue($product, $parameterValue);
					}
				} else {
					// select and multiselect
					if (is_array($parameterData)) {
						foreach ($parameterData as $multiselectValueId) {
							$parameterValue = $parameter->options->toCollection()->getById($multiselectValueId);
							if ($parameterValue) {
								$selectedParameterValuesIds[] = $parameterValue->id;
								$this->orm->product->addParameterValue($product, $parameterValue);
							}
						}
					} else {
						$selectValueId = $parameterData;
						$parameterValue = $parameter->options->toCollection()->getById($selectValueId);
						if ($parameterValue) {
							$selectedParameterValuesIds[] = $parameterValue->id;
							$this->orm->product->addParameterValue($product, $parameterValue);
						}
					}
				}
			}
		}

		if (isset($data['parameterValueLoaded'])) {
			foreach ($data['parameterValueLoaded'] as $parameterId => $loaded) {
				$loaded = (bool)((int)$loaded);
				if (!$loaded) {
					$parameter = $product->getParameterById($parameterId);
					if ($parameter instanceof stdClass) {
						if ($parameter->type === Parameter::TYPE_MULTISELECT) {
							foreach ($parameter->valueObjects as $paramValue) {
								$selectedParameterValuesIds[] = $paramValue->id;
							}
						} elseif ($parameter->type === Parameter::TYPE_SELECT) {
							$selectedParameterValuesIds[] = $parameter->valueObject->id;
						}
					}
				}
			}
		}

		$this->orm->product->removeMissingParameterValuesIds($product, $selectedParameterValuesIds);
	}

}
