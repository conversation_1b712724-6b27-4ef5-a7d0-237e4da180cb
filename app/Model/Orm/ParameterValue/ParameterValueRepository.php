<?php

declare(strict_types=1);

namespace App\Model\Orm\ParameterValue;

use App\Model\Orm\CollectionById;
use App\Model\Orm\FindPairs;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Searchable;
use App\Model\Orm\Traits\HasSimpleSave;
use App\PostType\Page\Model\Orm\TreeMapper;
use Nextras\Dbal\Result\Result;
use Nextras\Dbal\Result\Row;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method ParameterValue|null getById($id)
 * @method ParameterValue getByIdChecked($id)
 * @method ParameterValue updateParameterSort($parameter)
 * @method ParameterValue|null getProductParameterValueByUid(Product $product, $uid)
 * @method ICollection<ParameterValue> searchByInternalValue(string $search)
 * @method ParameterValue save(?ParameterValue $entity, array $data)
 * @method array findIdsForProductAllParameters(Product $product, array $parameterIds)
 * @method ICollection<ParameterValue> findValuesByIdsOrderNumeric(Parameter $parameter, array $valueIds)
 * @method Result findRowsByParameterAndIds(Parameter $parameter, array $valueIds)
 * @method Result findRowsByIds(array $valueIds)
 * @method Result findRowsByParameter(Parameter $parameter, ?array $valueIds = null, ?int $limit = null, array $ignoredIds = [], ?array $secondValueIds = [])
 * @method array findIdsByFulltext(Parameter $parameter, string $searchTerm)
 * @method void initSortByParameter(Parameter $parameter, ?string $typeSort = null)
 * @method void moveUp(Parameter $parameter, int $siblingSort, int $sort)
 * @method void moveDown(Parameter $parameter, int $siblingSort, int $sort)
 * @method Row[] getRawValues(Product $product, string $uid)
 * @method ICollection<ParameterValue> findValues(Product $product, string $uid)
 * @method ICollection<ParameterValue> findByExactOrder(array $ids)
 *
 * @extends Repository<ParameterValue>
 */
final class ParameterValueRepository extends Repository implements CollectionById, FindPairs, Searchable
{

	use HasSimpleSave;

	/**
	 * @inheritDoc
	 */
	public static function getEntityClassNames(): array
	{
		return [ParameterValue::class];
	}

	public function getRawValuesIds(Product $product, string $uid): array
	{
		return array_map(fn(Row $row) => $row->id, $this->getRawValues($product, $uid));
	}


	/**
	 * @param string|int|null $id
	 */
	public function getByExtId($id): ?ParameterValue
	{
		return isset($id) ? $this->getBy(['extId' => (string) $id]) : null;
	}
	/**
	 * @return ICollection<ParameterValue>
	 */
	public function findByIdOrder(array $ids): ICollection
	{
		return $this->findByExactOrder($ids);
	}

	public function findPairsForParameter(Parameter $parameter, string $value, ?string $key = null, ?array $where = []): Result
	{
		$mapper = $this->mapper;
		assert($mapper instanceof ParameterValueMapper);
		$where[] =  ['parameterId = %i', $parameter->id];
		return $mapper->findPairs($value, $key, $where);
	}



	public function findPairs(string $value, ?string $key = null, ?array $where = []): Result
	{
		$mapper = $this->mapper;
		assert($mapper instanceof ParameterValueMapper);
		return $mapper->findPairs($value, $key, $where);
	}

	public function getLanguageFlags(Product $product): array
	{
		$mapper = $this->mapper;
		assert($mapper instanceof ParameterValueMapper);
		return $mapper->getLanguageFlags($product);
	}

	/**
	 * @return ICollection<ParameterValue>
	 */
	public function searchByName(string $string, array $excludedIds): ICollection
	{
		$mapper = $this->mapper;
		assert($mapper instanceof ParameterValueMapper);
		return $mapper->searchByName($string, $excludedIds);
	}
}
