<?php declare(strict_types = 1);

namespace App\Model\Orm\ParameterValue;

use App\Model\ConfigService;
use App\Model\Orm\Parameter\Parameter;
use App\Model\TranslatorDB;
use Closure;
use stdClass;

class ParameterValueFilterHelper
{
	private int $maxParameterValues;

	public function __construct(
		private readonly TranslatorDB $translator,
		private readonly ParameterValueRepository $parameterValueRepository,
		ConfigService $configService
	)
	{
		$this->maxParameterValues = (int)($configService->get('bucketFilter', 'maxParameterValues') ?? 500);
	}


	public function getName(Parameter $parameter, int $valueId, string $internalValue): string
	{
		if ($parameter->hasTranslatedValues === true) {
			$defaultValue = 'pvalue_filter_' . $valueId;
			$translation = $this->translator->translate($defaultValue);

			if ($translation !== $defaultValue && $translation !== '') {
				return $translation;
			}

			$defaultValue = 'pvalue_' . $valueId;
			$translation = $this->translator->translate($defaultValue);

			if ($translation !== $defaultValue && $translation !== '') {
				return $translation;
			}
		}

		return $internalValue;
	}

	/**
	 * @phpstan-return Closure(): stdClass[]
	 */
	public function getFunctionForValues(Parameter $esParameter, ?int $limit = null): Closure
	{
		return function (?array $valueIds = null) use ($esParameter, $limit) {
			return $this->getValues(
				parameter: $esParameter,
				valueIds: $valueIds,
				limit: $limit
			);
		};
	}

	/**
	 *
	 * @return stdClass[]
	 */
	public function getValues(Parameter $parameter, ?array $valueIds = null, ?int $limit = null): array
	{
		$parameterValuesLimit = $limit ?? $this->maxParameterValues;

		$ret = [];
		$items = $this->parameterValueRepository->findRowsByParameter($parameter, $valueIds, $parameterValuesLimit);

		foreach ($items as $item) {
			$retItem = new stdClass();
			$retItem->id = $item->id;
			$retItem->name = ($trans = $this->translator->translate('pvalue_' . $item->id, TranslatorDB::DONT_INSERT_NEW)) !== 'pvalue_' . $item->id && $trans !== '' ? $trans : $item->internalValue;
			$retItem->unit = $parameter->unit;
			if ($parameter->hasTranslatedValues) {
				$retItem->filterName = ($trans = $this->translator->translate('pvalue_filter_' . $item->id, TranslatorDB::DONT_INSERT_NEW)) !== 'pvalue_filter_' . $item->id && $trans !== '' ? $trans : $item->internalValue;
				$retItem->filterNameForTitle = ($trans = $this->translator->translate('pvalue_filter_title_' . $item->id, TranslatorDB::DONT_INSERT_NEW)) !== 'pvalue_filter_title_' . $item->id && $trans !== '' ? $trans : '';
				$retItem->filterPrefix = ($trans = $this->translator->translate('pname_filter_prefix_' . $parameter->id, TranslatorDB::DONT_INSERT_NEW)) !== 'pname_filter_prefix_' . $parameter->id ? $trans : '';
				$retItem->filterSuffix = ($trans = $this->translator->translate('pname_filter_postfix_' . $parameter->id, TranslatorDB::DONT_INSERT_NEW)) !== 'pname_filter_postfix_' . $parameter->id ? $trans : '';
			} else {
				$retItem->filterPrefix = $parameter->filterPrefix;
				$retItem->filterSuffix = $parameter->filterPostfix;
			}

			$ret[] = $retItem;
		}

		return $ret;
	}

	public function getIdsBySearch(Parameter $parameter, string $searchTerm = ''): array
	{
		return $this->parameterValueRepository->findIdsByFulltext($parameter, $searchTerm);
	}

}
