<?php declare(strict_types = 1);

namespace App\Model\Orm\ParameterValue;

use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Traits\HasCamelCase;
use App\Model\Orm\Traits\HasFindPairFunction;
use Nette\Caching\Cache;
use Nette\Utils\Json;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

/**
 * @extends DbalMapper<ParameterValue>
 */
final class ParameterValueMapper extends DbalMapper
{

	use HasCamelCase;
	use HasFindPairFunction;

	/**
* @return literal-string
*/
public function getTableName(): string
{
	return 'parameter_value';
}

	public function updateParameterSort(Parameter $parameter): Result
	{
		return $this->connection->query('UPDATE parameter_value SET parameterSort = %i WHERE parameterId = %i', $parameter->sort, $parameter->id);
	}
	/**
	 * @return ICollection<ParameterValue>
	 */
	public function findByAliasAndParameterAlias(string $parameterValueAlias, string $parameterValue): ICollection
	{
		$builder = $this->builder()->select('pv.*')->from($this->getTableName(), 'pv')
			->joinInner('[parameter] as p', '[pv.parameterId] = [p.id]')
			->andWhere('pv.alias = %s', $parameterValueAlias)
			->andWhere('p.uid = %s', $parameterValue);

		return $this->toCollection($builder);
	}
	/**
	 * @return ICollection<ParameterValue>
	 */
	public function searchByInternalValue(string $search): ICollection
	{
		$builder = $this->builder()
			->andWhere('internalValue  LIKE %_like_', $search);

		return $this->toCollection($builder);
	}

	public function findIdsForProductAllParameters(Product $product, array $parameterIds): array
	{
		$result = $this->connection->query(
			'SELECT pp.parameterValueId, pp.parameterId, pv.sort, pv.prioritySort  from product_parameter as pp
                   LEFT JOIN parameter_value as pv on(pv.id = pp.parameterValueId)
				   where pp.productId = %i and pp.parameterId in %i[] and pv.isHidden = 0
				   ',
			$product->id,
			$parameterIds
		);

		return $result->fetchPairs('parameterValueId');
	}
	/**
	 * @return ICollection<ParameterValue>
	 */
	public function findValuesByIdsOrderNumeric(Parameter $parameter, array $valueIds): ICollection
	{
		$builder = $this->builder()->select('pv.*')->from($this->getTableName(), 'pv');

		$builder->andWhere('pv.parameterId = %i', $parameter->id);
		$builder->andWhere('pv.id IN %i[]', $valueIds);
		$builder->orderBy("pv.prioritySort DESC, CAST(REPLACE(pv.internalValue, ',', '.') AS DECIMAL(18,4)) ASC");

		return $this->toCollection($builder);
	}

	public function findRowsByParameterAndIds(Parameter $parameter, array $valueIds): Result
	{
		$builder = $this->builder()->select('pv.*')->from($this->getTableName(), 'pv');

		$builder->andWhere('pv.parameterId = %i', $parameter->id);
		$builder->andWhere('pv.id IN %i[]', $valueIds);
		$builder->orderBy('pv.sort ASC');

		return $this->connection->queryByQueryBuilder($builder);
	}

	public function findRowsByIds(array $valueIds): Result
	{
		$builder = $this->builder()->select('pv.*, p.uid as parameterUid')->from($this->getTableName(), 'pv');
		$builder->joinLeft('[parameter] AS [p]', '[p.id] = [pv.parameterId]');
		$builder->andWhere('pv.id IN %i[]', $valueIds);
		$builder->orderBy('pv.parameterSort ASC, pv.sort');

		return $this->connection->queryByQueryBuilder($builder);
	}


	public function findRowsByParameter(Parameter $parameter, ?array $valueIds = null, ?int $limit = null, ?array $ignoredIds = [], ?array $secondValueIds = []): Result
	{
		$builder = $this->builder()->select('pv.*')->from($this->getTableName(), 'pv');

		$builder->andWhere('pv.parameterId = %i AND pv.isHidden = %i', $parameter->id, 0);

		if ($ignoredIds !== []) {
			$builder->andWhere('pv.id not in %i[]', $ignoredIds);
		}

		if($valueIds !== null && $valueIds !== []){
			$builder->andWhere('pv.id IN %i[]', $valueIds);
		}
		if($secondValueIds !== []){
			$builder->andWhere('pv.id IN %i[]', $secondValueIds);
		}

		if ($parameter->typeSort === null) {
			$builder->orderBy('pv.prioritySort DESC, pv.sort ASC');
		} elseif ($parameter->typeSort === Parameter::SORT_NUMERIC_DESC) {
			$builder->orderBy('pv.prioritySort DESC, pv.internalValue DESC');
		} elseif ($parameter->typeSort === Parameter::SORT_NUMERIC || $parameter->typeSort === Parameter::SORT_ALPHABET) {
			$builder->orderBy('pv.prioritySort DESC, pv.internalValue ASC');
		}

		if ($limit !== null) {
			$builder->limitBy($limit);
		}

		return $this->connection->queryByQueryBuilder($builder);
	}

	public function initSortByParameter(Parameter $parameter, ?string $typeSort = null): void
	{
		$builder = $this->builder()->select('pv.id')->from($this->getTableName(), 'pv');

		$builder->andWhere('pv.parameterId = %i', $parameter->id);

		// add sorting by $typeSort
		if ($typeSort === null) {
			$builder->orderBy('pv.id ASC');
		} elseif ($typeSort === Parameter::SORT_NUMERIC_DESC) {
			$builder->orderBy('pv.internalValue DESC');
		} elseif ($typeSort === Parameter::SORT_NUMERIC || $parameter->typeSort === Parameter::SORT_ALPHABET) {
			$builder->orderBy('pv.internalValue ASC');
		}

		$i = 1;
		foreach ($this->connection->queryByQueryBuilder($builder) as $row) {
			$this->connection->query("UPDATE %table SET [sort] = %i WHERE id = %i", $this->getTableName(), $i, $row->id);
			$i++;
		}
	}

	public function findIdsByFulltext(Parameter $parameter, string $searchTerm = ''): array
	{
		$builder = $this->builder()->select('pv.id')->from($this->getTableName(), 'pv');
		$builder->andWhere('pv.parameterId = %i AND pv.isHidden = %i AND pv.internalValue LIKE %_like_', $parameter->id, 0, $searchTerm);


		return $this->connection->queryByQueryBuilder($builder)->fetchPairs('id', 'id');
	}

	/**
	 * @return ICollection<ParameterValue>
	 */
	public function findByExactOrder(array $ids): ICollection
	{
		if ($ids === []){
			return $this->toCollection([]);
		}

		$builder = $this->builder()
			->from($this->getTableName(), $this->getTableName())
			->andWhere('%table.id in %i[]', $this->getTableName(), $ids)
			->orderBy('%raw', 'FIELD(' . $this->getTableName() . '.id, ' . implode(',', $ids) . ')');

		return $this->toCollection($builder);
	}


	/**
	 * @return ICollection<ParameterValue>
	 */
	public function searchByName(string $q, array $excluded = []): ICollection
	{
		$builder = $this->builder()
			->from($this->getTableName(), $this->getTableName())
			->andWhere('internalValue LIKE %_like_', $q);

		if (count($excluded) > 0) {
			$builder->andWhere('%table.id not in %i[]', $this->getTableName(), $excluded);
		}

		return $this->toCollection($builder);
	}

	public function getProductParameterValueByUid(Product $product, string $uid): ?ParameterValue
	{
		$builder = $this->builder()->select("pv.*")->from($this->getTableName(), 'pv')
		                ->joinInner('[parameter] as p', '[pv.parameterId] = [p.id]')
		                ->joinInner('[product_parameter] as pp', '[pv.id] = [pp.parameterValueId]')
		                ->andWhere('pp.productId = %i', $product->id)
		                ->andWhere('p.uid = %s', $uid);
		return $this->toEntity($builder);
	}

	public function getLanguageFlags(Product $product): array
	{
		$builder = $this->builder()->select('pv.*')->from($this->getTableName(), 'pv')
			->joinInner('[parameter] as p', '[pv.parameterId] = [p.id]')
			->joinInner('[product_parameter] as pp', '[pv.id] = [pp.parameterValueId]')
			->andWhere('pp.productId = %i', $product->id)
			->andWhere('p.uid = %s', Parameter::UID_LANGUAGE);
			//->andWhere('pv.internalValue != %s', 'cesky');

		$parameterValuesCfJson = $this->toCollection($builder);

		if (!$parameterValuesCfJson->count()) {
			return [];
		}

		$flags = [];

		$hasCzechLanguage = false;

		/** @var ParameterValue $parameterValueCfJson */
		foreach ($parameterValuesCfJson as $parameterValueCfJson) {
			if (isset($parameterValueCfJson->cf->flagIcon->flagIcon) &&
				($flag = $parameterValueCfJson->cf->flagIcon->flagIcon->getEntity()) !== null) {
				if ($parameterValueCfJson->internalAlias === 'cesky') {
					$hasCzechLanguage = true;
				}
				$flags[] = $flag;
			}
		}

		if ($hasCzechLanguage) {
			return [];
		}

		return $flags;
	}

	public function moveUp(Parameter $parameter, int $siblingSort, int $sort): void
	{
		$b = $this->builder()->select('id, sort')->where('(sort <= %i AND sort > %i) AND parameterId = %i', $siblingSort, $sort, $parameter->id);
		foreach ($this->connection->queryByQueryBuilder($b) as $row) {
			$this->connection->query('UPDATE %table SET [sort] = %i WHERE [id] = %i', $this->getTableName(), ($row->sort - 1), $row->id);
		}
	}

	public function moveDown(Parameter $parameter, int $siblingSort, int $sort): void
	{
		$b = $this->builder()->select('id, sort')->where('(sort >= %i AND sort < %i) AND parameterId = %i', $siblingSort, $sort, $parameter->id);
		foreach ($this->connection->queryByQueryBuilder($b) as $row) {
			$this->connection->query('UPDATE %table SET [sort] = %i WHERE [id] = %i', $this->getTableName(), ($row->sort + 1), $row->id);
		}
	}

	public function getRawValues(Product $product, string $parameterUid): array
	{
		$builder = $this->builder()->select('pv.*')->from($this->getTableName(), 'pv')
			->joinInner('[parameter] as p', '[pv.parameterId] = [p.id]')
			->joinInner('[product_parameter] as pp', '[pv.id] = [pp.parameterValueId]')

			->andWhere('pp.productId = %i', $product->id)
			->andWhere('p.uid = %s', $parameterUid);

		return $this->connection->queryByQueryBuilder($builder)->fetchAll();
	}

	/**
	 * @return ICollection<ParameterValue>
	 */
	public function findValues(Product $product, string $parameterUid): ICollection
	{
		$builder = $this->builder()->select('pv.*')->from($this->getTableName(), 'pv')
			->joinInner('[parameter] as p', '[pv.parameterId] = [p.id]')
			->joinInner('[product_parameter] as pp', '[pv.id] = [pp.parameterValueId]')

			->andWhere('pp.productId = %i', $product->id)
			->andWhere('p.uid = %s', $parameterUid);

		return $this->toCollection($builder);
	}

}
