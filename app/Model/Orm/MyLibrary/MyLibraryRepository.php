<?php declare(strict_types = 1);

namespace App\Model\Orm\MyLibrary;

use App\Model\Orm\User\User;
use Nextras\Orm\Repository\Repository;

/**
 * @method MyLibrary getById($id)
 * @method array findUserProductIds(User $userEntity)
 *
 * @extends Repository<MyLibrary>
 */
final class MyLibraryRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [MyLibrary::class];
	}

}
