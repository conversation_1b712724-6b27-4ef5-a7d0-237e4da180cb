<?php declare(strict_types = 1);

namespace App\Model\Orm\MyLibrary;

use App\Model\Orm\MyLibraryProduct\MyLibraryProduct;
use App\Model\Orm\User\User;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property int                         $id                 {primary}
 * @property string                      $uid
 * @property User                        $user               {1:1 User, isMain=true, oneSided=true}
 * @property OneHasMany<MyLibraryProduct> $libraryProducts    {1:M MyLibraryProduct::$myLibrary}
 */
final class MyLibrary extends Entity
{

}
