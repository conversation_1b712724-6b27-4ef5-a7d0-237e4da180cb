<?php declare(strict_types = 1);

namespace App\Model\Orm\MyLibrary;

use App\Model\Orm\Product\Product;
use App\Model\Orm\Traits\HasStaticCache;
use App\Model\Orm\User\User;
use Nette\Utils\Strings;

final class MyLibraryModel
{

	use HasStaticCache;

	public function __construct(
		private readonly MyLibraryRepository $libraryRepository,
	)
	{
	}

	final public function create(User $user): MyLibrary
	{
		$myLibrary = new MyLibrary();
		$myLibrary->user = $user;
		$myLibrary->uid = $this->createUid($user);

		$this->libraryRepository->persistAndFlush($myLibrary);

		return $myLibrary;
	}

	private function createUid(User $user): string
	{
		$userNameWebalized = Strings::webalize($user->name);
		$libraryUniqueId = 0;

		do {
			$uid = $userNameWebalized;

			if ($libraryUniqueId > 0) {
				$uid .= '-' . $libraryUniqueId;
			}

			$library = $this->libraryRepository->getBy(['uid' => $uid]);
			$libraryUniqueId++;
		} while ($library !== null);

		return $uid;
	}

	public function isProductLibrary(User $userEntity, Product $product): bool
	{
		$generator = function () use ($userEntity) {
			return $this->libraryRepository->findUserProductIds($userEntity);
		};
		$productIds = $this->tryLoadCache($this->createCacheKey('isProductLibrary', $userEntity), $generator);

		return in_array($product->id, $productIds);
	}

	public function isProductLibraryInvalidate(): void
	{
		$this->flushCache();
	}

}
