<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch;

use App\Model\Orm\EsIndex\EsIndex;
use App\Model\Orm\Orm;
use Elastica\Document;
use Elastica\Exception\NotFoundException;
use Elastica\Exception\ResponseException;
use Elastica\Response;
use LogicException;
use App\Model\Mutation\MutationHolder;

class Service
{

	public function __construct(
		private readonly Orm $orm,
		private readonly MutationHolder $mutationHolder,
		private readonly IndexModel $indexModel,
	)
	{
	}

	public function replaceDoc(EsIndex $esIndex, Entity $elasticEntity): Response
	{
		try {
			return $this->updateDoc($esIndex, $elasticEntity);
		} catch (ResponseException) {
			return $this->createDoc($esIndex, $elasticEntity);
		}
	}

	public function createDoc(EsIndex $esIndex, Entity $elasticEntity): Response
	{
		return $this->indexModel->getIndex($esIndex)->addDocument(
			$this->getDocument($elasticEntity, $esIndex)
		);
	}

	public function updateDoc(EsIndex $esIndex, Entity $elasticEntity): Response
	{
		return $this->indexModel->getIndex($esIndex)->updateDocument(
			$this->getDocument($elasticEntity, $esIndex)
		);
	}

	public function deleteDoc(EsIndex $esIndex, Entity $elasticEntity): ?Response
	{
		try {
			return $this->indexModel->getIndex($esIndex)
				->deleteById($elasticEntity->getId());
		} catch (NotFoundException) {
			return null;
		}
	}


	protected function getDocument(Entity $elasticEntity, EsIndex $esIndex): Document
	{
		$originalMutationFromOrm = null;
		try {
			$originalMutationFromOrm = $this->orm->getMutation();
		} catch (LogicException) {

		}

		try {
			$originalMutationFromHolder = $this->mutationHolder->getMutation();
		} catch (LogicException) {
			// only for web presentation
			// ignored in command
			$originalMutationFromHolder = null;
		}


		$this->orm->setMutation($esIndex->mutation);
		$this->mutationHolder->setMutation($esIndex->mutation);

		$newEsDocument = new Document(
			$elasticEntity->getId(),
			$elasticEntity->getData($esIndex->mutation)
		);

		if ($originalMutationFromOrm !== null) {
			$this->orm->setMutation($originalMutationFromOrm);
		}

		if ($originalMutationFromHolder !== null) {
			$this->mutationHolder->setMutation($originalMutationFromHolder);
		}

		return $newEsDocument;
	}

}
