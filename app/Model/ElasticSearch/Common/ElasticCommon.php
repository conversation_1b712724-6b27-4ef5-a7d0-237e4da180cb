<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Common;

use App\Model\ElasticSearch\Entity;
use App\Model\Orm\Mutation\Mutation;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use LogicException;

/**
 * @param Convertor[] $convertors
 */
class ElasticCommon implements Entity
{

	public const string TYPE_TREE = 'tree';
	public const string TYPE_CALENDAR = 'calendar';

	public function __construct(
		private object $object,
		private array $convertors = [],
	)
	{
	}

	public function getId(): string
	{
		$class = get_class($this->object);
		if ( ! isset($this->object->id)) {
			throw new LogicException('Missing primary key for entity');
		}

		return $class . '-' . $this->object->id;
	}

	public function getData(Mutation $mutation): array
	{
		$convertedData = [];
		foreach ($this->convertors as $convertor) {
			$convertedData[] = $convertor->convert($this->object);
		}

		return array_merge(...$convertedData);
	}

}
