<?php declare(strict_types=1);

namespace App\Model\ElasticSearch\Common\Convertor;

use App\Model\ElasticSearch\Common\Convertor;
use App\Model\ElasticSearch\ConvertorHelper;
use App\PostType\Blog\Model\Orm\BlogLocalization;

class BlogData implements Convertor
{
	public function convert(object $object): array
	{
		assert($object instanceof BlogLocalization);
		return [
			'id' => $object->id,
			'isSystemPage' => false,
			'type' => 'blog',
			'langCode' => $object->mutation->langCode,
			'name' => $object->name . ' (' . $object->getParent()->getInternalName() . ')',
			'nameTitle' => $object->nameTitle,
			'description' => $object->description,
			'annotation' => $object->annotation,
			'path' => $object->path,
			'isPublic' => $object->getIsPublic(),
			'categories' => $object->categories->fetchPairs(null, 'id'),
			'publicFrom' => ConvertorHelper::convertTime($object->publicFrom),
			'publicTo' => ConvertorHelper::convertTime($object->publicTo),
			'datePublished' => ConvertorHelper::convertTime($object->getPublicFrom()), // From parameters (Datum vydání)
		];
	}
}
