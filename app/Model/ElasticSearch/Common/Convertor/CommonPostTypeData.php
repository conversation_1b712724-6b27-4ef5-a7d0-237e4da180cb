<?php declare(strict_types=1);

namespace App\Model\ElasticSearch\Common\Convertor;

use App\Model\ElasticSearch\Common\Convertor;
use App\Model\ElasticSearch\Common\ItemTypeHelper;
use App\Model\ElasticSearch\ConvertorHelper;
use App\Model\Orm\Routable;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\Publishable;
use App\PostType\Core\Model\Validatable;

class CommonPostTypeData implements Convertor
{
	public function __construct()
	{
	}

	public function convert(object $object): array
	{
		assert($object instanceof LocalizationEntity);

		$data = [
			'id' => $object->getId(),
			'name' => $object->getName(),
			'type' => ItemTypeHelper::getTypeByObject($object),
		];

		if (method_exists($object, 'getEsContent')) {
			$data['public'] = $object->getEsContent();
		}

		if ($object instanceof Validatable) {
			$data['publicFrom'] = $object->getPublicFrom() ? ConvertorHelper::convertTime($object->getPublicFrom()) : null;
			$data['publicTo'] = $object->getPublicTo() ? ConvertorHelper::convertTime( $object->getPublicTo()) : null;
		}

		if ($object instanceof Publishable) {
			$data['public'] = $object->getIsPublic();
		}

		if ($object instanceof Routable) {
			$data['nameTitle'] = $object->getNameTitle();
			$data['nameAnchor'] = $object->getNameAnchor();
		}

		if (isset($object->score)) {
			$data['score'] = $object->score;
		}

		return $data;

	}
}
