<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Common\Convertor;

use App\Model\ElasticSearch\Common\Convertor;
use App\Model\ElasticSearch\ConvertorHelper;
use App\PostType\Calendar\Model\Orm\CalendarLocalization;

class CalendarData implements Convertor
{

	public const TYPE = 'calendar';

	public function convert(object $object): array
	{
		assert($object instanceof CalendarLocalization);
		$parent = $object->getParent();

		$data = [
			'id' => $object->id,
			'public' => (bool) $object->public,
			'isPublic' => (bool) $object->public,
			'name' => $object->name,
			'fulltext-name' => $object->name,
			'nameTitle' => $object->nameTitle,
			'nameAnchor' => $object->nameAnchor,
			'content' => $object->getEsContent(),
			'type' => self::TYPE,
			'annotation' => $object->annotation,
			'publicFrom' => $object->publicFrom !== null ? ConvertorHelper::convertTime($object->publicFrom) : null,
			'publicTo' => $object->publicTo !== null ? ConvertorHelper::convertTime($object->publicTo) : null,
			'eventTo' => isset($object->cf->settings->to) ? ConvertorHelper::convertTime(ConvertorHelper::dateOnly($object->cf->settings->to)) : null,
		];

		$data['tags'] = [];
		if ($parent->tags->count()) {
			foreach ($object->getParent()->tags as $tag) {
				if ($tag->isPublished()) {
					$data['tags'][] = $tag->id;
				}
			}
		}

		return $data;
	}

}
