<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Common;

use App\Model\ElasticSearch\Service;
use App\PostType\Core\Model\LocalizationEntity;
use App\Model\Orm\EsIndex\EsIndex;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Messenger\Elasticsearch\Common\Message\DeleteCommonMessage;
use App\Model\Messenger\Elasticsearch\Common\Message\ReplaceCommonMessage;
use App\Model\Messenger\Elasticsearch\ElasticBusWrapper;
use App\Model\Orm\Mutation\Mutation;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Page\Model\Orm\TreeRepository;
use App\PostType\Tag\Model\Orm\TagLocalization\TagLocalization;
use App\PostType\Tag\Model\Orm\TagLocalization\TagLocalizationRepository;
use Elastica\Exception\NotFoundException;

readonly class Facade
{
	public function __construct(
		private Service $service,
		private EsIndexRepository $esIndexRepository,
		private ConvertorProvider $convertorProvider,
		private ElasticBusWrapper $elasticBusWrapper,
		private TreeRepository $treeRepository,
		private TagLocalizationRepository $tagLocalizationRepository,
	)
	{
	}

	public function deleteNow(LocalizationEntity|Tree $object, Mutation $mutation): void
	{
		if (($esIndex = $this->esIndexRepository->getCommonLastActive($mutation)) !== null) {
			try {
				$elasticEntity = new ElasticCommon($object);
				$this->service->deleteDoc($esIndex, $elasticEntity);
			} catch (NotFoundException) {
				// ignore
			}
		}
	}

	public function updateNow(LocalizationEntity|Tree $object, Mutation $mutation): void
	{
		if (($esAllIndex = $this->esIndexRepository->getCommonLastActive($mutation)) !== null) {
			$elasticEntity = new ElasticCommon($object, $this->convertorProvider->getAll(get_class($object)));
			$this->service->replaceDoc($esAllIndex, $elasticEntity);
		}
	}

	public function update(LocalizationEntity|Tree $object, Mutation $mutation): void
	{
		if (($esAllIndex = $this->esIndexRepository->getCommonLastActive($mutation)) !== null && isset($object->id)) {
			$convertors = $this->convertorProvider->getAllLikeStrings(get_class($object));
			$message = new ReplaceCommonMessage(get_class($object), $object->id, $esAllIndex, $convertors);
			$this->elasticBusWrapper->send($message);
		}
	}

	public function delete(LocalizationEntity|Tree $object, Mutation $mutation): void
	{
		if (($esAllIndex = $this->esIndexRepository->getCommonLastActive($mutation)) !== null && isset($object->id)) {
			if ($class = get_class($object)) {
				$message = new DeleteCommonMessage($class, $object->id, $esAllIndex);
				$this->elasticBusWrapper->send($message);
			}
		}
	}

	public function fill(EsIndex $esIndex, ?int $limit = null, bool $autoSwitch = false): void
	{
		$repositories = [
			Tree::class => $this->treeRepository,
			TagLocalization::class => $this->tagLocalizationRepository,
		];

		$lastClass = null;
		$lastItemId = null;
		$lastConvertorStrings = null;
		$sendFirstSignal = true;

		foreach ($repositories as $class => $repository) {
			$ids = $repository->findAllIdsInMutation($esIndex->mutation, $limit);
			$convertorStrings = $this->convertorProvider->getAllLikeStrings($class);

			foreach ($ids as $itemRow) {
				$itemId = $itemRow->id;

				$signals = [];
				if ($sendFirstSignal) {
					$signals[] = ElasticBusWrapper::SIGNAL_FIRST;
					$sendFirstSignal = false;
				}

				$this->elasticBusWrapper->send(
					new ReplaceCommonMessage($class, $itemId, $esIndex, $convertorStrings, $signals)
				);

				$lastClass = $class;
				$lastItemId = $itemId;
				$lastConvertorStrings = $convertorStrings;
			}
		}

		if ($lastClass !== null && $lastItemId !== null && $lastConvertorStrings !== null) {

			$signals = [];
			$signals[] = ElasticBusWrapper::SIGNAL_LAST;

			if ($autoSwitch) {
				$signals[] = ElasticBusWrapper::AUTO_SWITCH;
			}

			$this->elasticBusWrapper->send(
				new ReplaceCommonMessage($lastClass, $lastItemId, $esIndex, $lastConvertorStrings, $signals)
			);
		}
	}

}
