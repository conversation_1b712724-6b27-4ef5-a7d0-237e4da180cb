<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Common;

use App\Model\ElasticSearch\Common\Convertor\BlogData;
use App\Model\ElasticSearch\Common\Convertor\CommonPostTypeData;
use App\Model\ElasticSearch\Common\Convertor\TreeData;
use App\Model\ElasticSearch\Common\Convertor\CalendarData;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\Calendar\Model\Orm\CalendarLocalization;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Page\Model\Orm\Tree;
use LogicException;

class ConvertorProvider
{

	private array $map;

	public function __construct(
		private readonly TreeData $treeData,
		private readonly BlogData $blogData,
		private readonly CommonPostTypeData $commonPostTypeData,
		private readonly CalendarData $calendarData,

	)
	{
		$this->map = [];
		foreach (func_get_args() as $convertor) {
			$this->map[$convertor::class] = $convertor;
		}
	}


	public function get(string $class): Convertor
	{
		return $this->map[$class] ?? throw new LogicException(sprintf("Missing convertor for '%s' class", $class));
	}


	public function getAll(string $class): array
	{
		return match ($class) {
			CatalogTree::class, Tree::class, CommonTree::class => [
				$this->treeData,
			],
			BlogLocalization::class => [
				$this->blogData,
			],
			CalendarLocalization::class => [
				$this->calendarData,
			],
			default => [$this->commonPostTypeData],
		};
	}

	public function getAllLikeStrings(string $class): array
	{
		return array_map(fn(object $item) => $item::class, $this->getAll($class));
	}

}
