<?php declare(strict_types=1);

namespace App\Model\ElasticSearch\Common\AggregationParser;


use App\Model\ElasticSearch\Common\Dto\CommonDto;
use App\Model\ElasticSearch\Common\ItemTypeHelper;
use Elastica\ResultSet;

class AlphabetParser
{
	public function __construct()
	{
	}

	public function parse(array $firstLetterAgg): array
	{

		$alphabet = [];
		if (isset($firstLetterAgg['firstLetterAgg']['buckets'])) {
			$alphabetData = $firstLetterAgg['firstLetterAgg']['buckets'];
			$alphabet = array_map(fn(array $item) => $item['key'], $alphabetData);
		}

		usort($alphabet, $this->customSort(...));
		return $alphabet;
	}

	public function parseTopHits(ResultSet $resultSet): array
	{
		$aggregations = $resultSet->getAggregations();
		$totalHits = [];
		if (isset($aggregations['firstLetterAgg']['buckets'])) {
			$alphabetData = $aggregations['firstLetterAgg']['buckets'];

			foreach ($alphabetData as $item) {
				foreach($item['topHitsAgg']['hits']['hits'] as $entity) {
					$totalHits[$item['key']][] = new CommonDto($entity['_source']['id'], $entity['_source']['name'], $entity['_source']['url'], $entity['_source']['type']);
				}
			}

		}
		uksort($totalHits, $this->customSort(...));
		return $totalHits;
	}

	private function customSort(mixed $a, mixed $b): int {
		$getType = function ($value): int {
			$value = (string)$value;
			if (preg_match('/^\p{L}+$/u', $value)) {
				return ctype_alpha($value) ? 0 : 1; // Characters
			}
			if (is_numeric($value)) {
				return 2; // Numbers
			}
			return 3; // Others
		};


		// Normalization chars
		$collator = new \Collator('cs_CZ'); // Czech locale

		// Compare against type
		$typeA = $getType($a);
		$typeB = $getType($b);

		if ($typeA !== $typeB) {
			return $typeA <=> $typeB;
		}

		// If type is same, sort
		if ($typeA === 0) { // chars
			// First without accents, next with accents
			$normalizedA = $collator->getSortKey((string)$a);
			$normalizedB = $collator->getSortKey((string)$b);

			return $normalizedA <=> $normalizedB;
		}

		if ($typeA === 1) { // Numeric chars
			return $a <=> $b;
		}

		// Other characters
		return strcmp((string)$a, (string)$b);
	}
}
