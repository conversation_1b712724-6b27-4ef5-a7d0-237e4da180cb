<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Common;

use App\Model\ElasticSearch\Common\Dto\CommonDto;
use App\Model\Orm\CollectionById;
use App\Model\Orm\Orm;
use Elastica\Result;
use Elastica\ResultSet;
use LogicException;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\IEntity;

class ResultReader
{

	public function __construct(
		private readonly Orm $orm,
	)
	{
	}

	public function mapResultToEntityCollection(ResultSet $result, string $itemClass): ICollection // @phpstan-ignore-line
	{
		$ids = array_map(
			fn(Result $item) => $item->getSource()['id'],
			$result->getResults()
		);

		if ($ids === []) {
			return new EmptyCollection();
		}

		$repository = $this->orm->getRepositoryForEntity($itemClass); // @phpstan-ignore-line
		if ( ! $repository instanceof CollectionById) {

			throw new LogicException('Implement ' . CollectionById::class . ' for repository');
		}
		return $repository->findByIdOrder($ids);
	}

	public function mapResultToDto(ResultSet $result): array
	{
		return array_map(function (Result $item) {
			return new CommonDto($item->getSource()['id'], $item->getSource()['name'], $item->getSource()['url'], $item->getSource()['type']);
		}, $result->getResults());
	}

}
