<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch;

use App\Model\Orm\EsIndex\EsIndex;
use Closure;
use Elastica\Query;
use Elastica\Query\AbstractQuery;
use Elastica\Query\BoolQuery;
use Elastica\ResultSet;
use Generator;

class Repository
{

	public function __construct(
		private readonly IndexModel $indexModel,
	)
	{
	}

	protected function baseSearch(EsIndex $esIndex, Query $query): ResultSet
	{
		return $this->indexModel->getIndex($esIndex)->search($query);
	}

	public function findByQuery(
		EsIndex $esIndex,
		AbstractQuery $boolQuery,
		int $limit,
		?int $offset = 0,
		?bool $onlyIds = false,
		array $sort = [],
		?int $minScore = null,
		?array $fieldList = [],
	): ResultSet
	{
		$query = new Query();
		$query->setSize($limit);
		$query->setFrom($offset);
		$query->setQuery($boolQuery);
		$query->setTrackTotalHits();
		if ($minScore !== null) {
			$query->setMinScore($minScore);
		}

		if ($sort){
			$query->addSort($sort);
		}
		if ($onlyIds) {
			$fieldList[] = '_id';
		}
		if ($fieldList !== []) {
			$query->setSource($fieldList);
		}

		return $this->baseSearch($esIndex, $query);
	}


	/**
	 * @return Closure(): Generator
	 */
	public function getFinderClosure(EsIndex $index, AbstractQuery $query, bool $onlyIds = true): Closure
	{
		return function () use ($index, $query, $onlyIds): Generator {
			$offset = 0;
			$limit = 1000;
			$response = $this->findByQuery($index, $query, $limit, $offset, onlyIds: $onlyIds);

			while ($response->getResults() !== []) {
				foreach ($response->getResults() as $result) {
					if ($result->getSource() !== []) {
						if ($onlyIds) {
							yield (int) $result->getSource()['id'];
						} else {
							yield $result->getSource();
						}
					}
				}
				if ($offset + $limit >= $response->getTotalHits()) {
					break;
				}
				$offset += $limit;
				$response = $this->findByQuery($index, $query, $limit, $offset);
			}
		};
	}


	/**
	 * @return Closure(): Generator
	 */
	public function getOneBulkFinderClosure(EsIndex $index, AbstractQuery $query, int $limit = 1000, int $offset = 0, bool $onlyIds = true, ?array $fieldList = []): Closure
	{
		return function () use ($index, $query, $onlyIds, $limit, $offset, $fieldList): Generator {
			$response = $this->findByQuery($index, $query, $limit, $offset, onlyIds: $onlyIds, fieldList: $fieldList);
			foreach ($response->getResults() as $result) {
				if ($result->getSource() !== []) {
					if ($onlyIds) {
						yield (int) $result->getSource()['id'];
					} else {
						yield $result->getSource();
					}
				}
			}
		};
	}


}
