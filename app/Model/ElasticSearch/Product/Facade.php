<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Product;

use App\Model\ElasticSearch\Product\Convertor\AvailabilityData;
use App\Model\ElasticSearch\Product\Convertor\CategoryData;
use App\Model\ElasticSearch\Product\Convertor\DiscountData;
use App\Model\ElasticSearch\Product\Convertor\FlagData;
use App\Model\ElasticSearch\Product\Convertor\PriceData;
use App\Model\ElasticSearch\Product\Convertor\ScoreData;
use App\Model\ElasticSearch\Product\Convertor\StoreData;
use App\Model\ElasticSearch\Product\Convertor\TopScoreData;
use App\Model\ElasticSearch\Service;
use App\Model\Messenger\Elasticsearch\ElasticBusWrapper;
use App\Model\Messenger\Elasticsearch\Product\Message\DeleteProductMessage;
use App\Model\Messenger\Elasticsearch\Product\Message\QuickReplaceProductMessage;
use App\Model\Messenger\Elasticsearch\Product\Message\ReplaceProductMessage;
use App\Model\Orm\EsIndex\EsIndex;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductRepository;
use Elastica\Exception\NotFoundException;

class Facade
{

	public function __construct(
		private readonly Service $service,
		private readonly ProductRepository $productRepository,
		private readonly EsIndexRepository $esIndexRepository,
		private readonly ConvertorProvider $convertorProvider,
		private readonly ElasticBusWrapper $elasticBusWrapper,
	)
	{
	}

	public function deleteFromAllMutationNow(Product $product): void
	{
		foreach ($this->esIndexRepository->findActiveByType(EsIndex::TYPE_PRODUCT) as $esIndex) {
			assert($esIndex instanceof EsIndex);
			try {
				foreach ($product->variants as $variant) {
					$elasticEntity = new ElasticProduct($product, $variant, []);
					$this->service->deleteDoc($esIndex, $elasticEntity);
				}
			} catch (NotFoundException) {
				//ignore if not found
			}
		}
	}

	public function updateAllMutationsNow(Product $product, array $convertors = []): void
	{
		foreach ($this->esIndexRepository->findActiveByType(EsIndex::TYPE_PRODUCT) as $esIndex) {
			assert($esIndex instanceof EsIndex);

			$productLocalization = $product->getLocalization($esIndex->mutation);

			if ($convertors === []) {
				$convertors = $this->convertorProvider->getAll();
			}

			foreach ($product->variants as $variant) {
				$elasticEntity = new ElasticProduct($product, $variant, $convertors);

				if ( ! $variant->isOld && $productLocalization->public === 1) {
					$this->service->replaceDoc($esIndex, $elasticEntity);
				} else {
					try {
						$this->service->deleteDoc($esIndex, $elasticEntity);
					} catch (NotFoundException $e) {
						//just trying
						bd($e->getMessage());
					}
				}
			}
		}
	}

	public function updateAllMutations(Product $product, ?array $convertors = null): void
	{
		if ($convertors === null) {
			$convertors = $this->convertorProvider->getAllLikeStrings();
		}
		foreach ($this->esIndexRepository->findActiveByType(EsIndex::TYPE_PRODUCT) as $esIndex) {
			assert($esIndex instanceof EsIndex);
			try {
				$productLocalization = $product->getLocalization($esIndex->mutation);
				$message = new ReplaceProductMessage($product->id, $esIndex, $convertors);
				$this->elasticBusWrapper->send($message);
			} catch (NotFoundException) {
				// do nothing
			}
		}
	}

	public function updateOrDeleteAllMutations(Product $product, ?array $convertors = null): void
	{
		if ($convertors === null) {
			$convertors = $this->convertorProvider->getAllLikeStrings();
		}

		foreach ($this->esIndexRepository->findActiveByType(EsIndex::TYPE_PRODUCT) as $esIndex) {
			assert($esIndex instanceof EsIndex);
			try {
				$productLocalization = $product->getLocalization($esIndex->mutation);

				if ($product->syncWithElastic) {
					$message = new ReplaceProductMessage($product->id, $esIndex, $convertors);
				} else {
					$message = new DeleteProductMessage($product->id, $esIndex);
				}

				$this->elasticBusWrapper->send($message);
			} catch (NotFoundException) {
				// do nothing
			}
		}

	}


	public function update(Product $product, Mutation $mutation, array $convertorClasses = []): void
	{
		$esIndex = $this->esIndexRepository->findActiveByType(EsIndex::TYPE_PRODUCT)->getBy(['mutation' => $mutation]);
		if ($esIndex !== null) {
			assert($esIndex instanceof EsIndex);
			$convertorClasses = ($convertorClasses === []) ? $this->convertorProvider->getAllLikeStrings() : $convertorClasses;
			$this->elasticBusWrapper->send(
				new QuickReplaceProductMessage($product->id, $esIndex, $convertorClasses)
			//				new ReplaceProductMessage($product->id, $esIndex, $convertorClasses)
			);
		}
	}

	public function updateAfterStockProcess(Product $product, Mutation $mutation): void
	{
		$esIndex = $this->esIndexRepository->findActiveByType(EsIndex::TYPE_PRODUCT)->getBy(['mutation' => $mutation]);
		if ($esIndex !== null) {

			$convertors = [
				AvailabilityData::class,
				StoreData::class,
			];

			assert($esIndex instanceof EsIndex);

			if ($product->syncWithElastic) {
				$this->elasticBusWrapper->send(new QuickReplaceProductMessage($product->id, $esIndex, $convertors));
			} else {
				$this->elasticBusWrapper->send(new DeleteProductMessage($product->id, $esIndex));
			}
		}
	}

	public function updateAfterPriceProcess(Product $product, Mutation $mutation): void
	{
		$esIndex = $this->esIndexRepository->findActiveByType(EsIndex::TYPE_PRODUCT)->getBy(['mutation' => $mutation]);
		if ($esIndex !== null) {

			$convertors = [
				PriceData::class,
			];

			assert($esIndex instanceof EsIndex);

			if ($product->syncWithElastic) {
				$this->elasticBusWrapper->send(new QuickReplaceProductMessage($product->id, $esIndex, $convertors));
			} else {
				$this->elasticBusWrapper->send(new DeleteProductMessage($product->id, $esIndex));
			}
		}
	}

	public function updateAfterProductScoreCalculation(Product $product, Mutation $mutation): void
	{
		$esIndex = $this->esIndexRepository->findActiveByType(EsIndex::TYPE_PRODUCT)->getBy(['mutation' => $mutation]);
		if ($esIndex !== null) {

			$convertors = [
				ScoreData::class,
			];

			assert($esIndex instanceof EsIndex);

			$this->elasticBusWrapper->send(new ReplaceProductMessage($product->id, $esIndex, $convertors));
		}
	}


	public function fill(EsIndex $esIndex, ?int $limit = null, bool $autoSwitch = false, array $convertorStrings = []): void
	{
		$ids = $this->productRepository->findAllIdsForElasticSearch($limit, $esIndex->mutation, true);
		$idsCount = count($ids);
		if ($convertorStrings === []) {
			$convertorStrings = $this->convertorProvider->getAllLikeStrings();
		}

		foreach ($ids as $key => $productRow) {
			$key = (int)$key;
			$productId = $productRow->id;

			$signals = [];

			if ($key === 0) {
				$signals[] = ElasticBusWrapper::SIGNAL_FIRST;
			}

			if ($key + 1 === $idsCount) {
				$signals[] = ElasticBusWrapper::SIGNAL_LAST;
				if ($autoSwitch) {
					$signals[] = ElasticBusWrapper::AUTO_SWITCH;
				}
			}

			$this->elasticBusWrapper->send(
				new ReplaceProductMessage($productId, $esIndex, $convertorStrings, $signals)
			);
		}
	}


	public function fillQuickUpdate(EsIndex $esIndex, ?int $limit = null): void
	{
		$ids = $this->productRepository->findAllIdsForElasticSearch($limit, $esIndex->mutation, true);
		$convertorStrings = [
			PriceData::class,
			CategoryData::class,
		];
		foreach ($ids as $productRow) {
			$productId = $productRow->id;
			$this->elasticBusWrapper->send(
				new ReplaceProductMessage($productId, $esIndex, $convertorStrings)
			);
		}
	}


	public function fillPriceUpdate(EsIndex $esIndex, ?int $limit = null): void
	{
		$ids = $this->productRepository->findAllIdsForElasticSearch($limit, $esIndex->mutation, true);
		$convertorStrings = [
			PriceData::class,
		];
		foreach ($ids as $productRow) {
			$productId = $productRow->id;
			$this->elasticBusWrapper->send(
				new ReplaceProductMessage($productId, $esIndex, $convertorStrings)
			);
		}
	}


	public function fillCategoryUpdate(?EsIndex $esIndex, ?int $limit = null): void
	{
		$ids = $this->productRepository->findAllIdsForElasticSearch($limit, $esIndex->mutation, true);
		$convertorStrings = [
			CategoryData::class,
		];
		foreach ($ids as $productRow) {
			$productId = $productRow->id;
			$this->elasticBusWrapper->send(
				new ReplaceProductMessage($productId, $esIndex, $convertorStrings)
			);
		}
	}

}
