<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Product;

use App\Model\ElasticSearch\Entity;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;

/**
 * @param Convertor[] $convertors
 */
class ElasticProduct implements Entity
{

	public function __construct(
		private Product $product,
		private ProductVariant|null $variant = null,
		private array $convertors = [],
	)
	{
	}

	public function getId(): string
	{
		$id = $this->product->isTypePackage ? $this->product->id : sprintf('V%s_%s',$this->product->id, $this->variant->id);
		return (string) $id;
	}

	public function getData(Mutation $mutation): array
	{
		$this->product->setMutation($mutation);

		$convertedData = [];
		$convertedData[] = ['id' => $this->getId()];

		foreach ($this->convertors as $convertor) {
			$convertedData[] = $convertor->convert($this->product, $this->variant);
		}

		return array_merge(...$convertedData);
	}

}
