<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Product;

use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductRepository;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\ProductVariant\ProductVariantRepository;
use Elastica\ResultSet;
use Nette\Utils\ArrayList;
use Nextras\Orm\Collection\EmptyCollection;

readonly class MultiResultReader
{

	public function __construct(
		private ProductRepository $productRepository,
		private ProductVariantRepository $variantRepository,
	)
	{
	}

	/**
	 * @return ArrayList<Product|ProductVariant>|EmptyCollection<Product|ProductVariant> Product = type Package
	 */
	public function mapResultToEntityCollection(ResultSet $result): ArrayList|EmptyCollection
	{
		$resultList = $variantIdList = $packageIdList = $entityList = [];

		foreach ($result->getResults() as $r) {
			$docId = $r->getId();
			$isVariantId = str_starts_with($docId, 'V');

			if ($isVariantId) {
				if (preg_match('/V(\d+)_(\d+)/', $docId, $matches)) {
					// $productId = $matches[1]; unused
					$variantId = $matches[2];
				}

				if ( ! isset($variantId)) {
					continue;
				}

				$id = intval($variantId);
				$variantIdList[$id] = $id;

			} else {
				$id = intVal($docId); // package
				$packageIdList[$id] = $id;
			}

			$resultList[$docId] = $id;
		}

		$variants = $this->variantRepository->findByIds($variantIdList)->fetchPairs('id');
		$packages = $this->productRepository->findBy([
			'productTypeId' => Product::TYPE_PACKAGE,
			'id' => $packageIdList,
		])->fetchPairs('id');

		foreach ($resultList as $docId => $id) {
			$isVariantId = str_starts_with((string)$docId, 'V');

			if ($isVariantId && isset($variants[$id])) {
				$entityList[] = $variants[$id];
			} elseif (isset($packages[$id])) {
				$entityList[] = $packages[$id];
			}
		}

		if (count($entityList) > 0) {
			return ArrayList::from($entityList);
		} else {
			/** @var EmptyCollection<Product|ProductVariant> */
			return new EmptyCollection();
		}
	}
}
