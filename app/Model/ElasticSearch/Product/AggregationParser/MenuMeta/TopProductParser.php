<?php declare(strict_types=1);

namespace App\Model\ElasticSearch\Product\AggregationParser\MenuMeta;


class TopProductParser
{

	public function __construct(
	)
	{
	}

	public function parse(array $inPathAgg): array
	{
		$productIds = [];
		if (isset($inPathAgg['topProductFiltered']['topProductByScore']['hits']['hits'])) {
			$productDocs = $inPathAgg['topProductFiltered']['topProductByScore']['hits']['hits'];
			$productIds = array_map(fn (array $item) => (int) $item['_id'], $productDocs);
		}

		return $productIds;
	}
}
