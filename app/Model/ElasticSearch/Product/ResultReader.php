<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Product;

use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductRepository;
use Elastica\ResultSet;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;

class ResultReader
{

	public function __construct(
		private ProductRepository $productRepository,
	)
	{
	}


	/**
	 * @return ICollection<Product>
	 */
	public function mapResultToEntityCollection(ResultSet $result, ?array $exactOrder = null): ICollection
	{
		$ids = $this->resultToIdsArray($result, $exactOrder);
		return $this->idsToCollection($ids);
	}

	/**
	 * @return int[]
	 */
	public function resultToIdsArray(ResultSet $result, ?array $exactOrder = null): array
	{
		$ids = [];
		foreach ($result->getResults() as $r) {
			$id = (int) $r->getId();
			$ids[$id] = $id;
		}

		if ($exactOrder !== null) {
			uksort($ids, function ($key1, $key2) use ($exactOrder) {
				return array_search($key1, $exactOrder) > array_search($key2, $exactOrder) ? 1 : 0;
			});
		}

		return $ids;
	}


	/**
	 * @return ICollection<Product>
	 */
	public function idsToCollection(array $ids): ICollection
	{
		if (count($ids) > 0) {
			return $this->productRepository->findFilteredProducts($ids);
		} else {
			/** @var EmptyCollection<Product> $empty */
			$empty = new EmptyCollection();
			return $empty;
		}
	}

}
