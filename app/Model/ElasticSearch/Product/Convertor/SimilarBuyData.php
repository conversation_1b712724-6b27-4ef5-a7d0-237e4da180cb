<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Product\Convertor;

use App\Model\ElasticSearch\Product\Convertor;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;


class SimilarBuyData implements Convertor
{

	const COMMAND_SHORTCUT = 's';

	public function __construct(
	)
	{
	}

	public function convert(Product $product, ?ProductVariant $variant = null): array
	{
		$data['id'] = $product->id;
		$data['similarSaleProductIds'] = (array) $product->similarBuyProducts;
		return $data;
	}

}
