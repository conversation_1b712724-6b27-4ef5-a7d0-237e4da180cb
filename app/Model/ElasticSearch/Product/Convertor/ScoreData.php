<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Product\Convertor;

use App\Model\ElasticSearch\Product\Convertor;
use App\Model\Mutation\MutationHolder;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;

final class ScoreData implements Convertor
{
	public function __construct(
		private readonly MutationsHolder $mutationsHolder
	)
	{

	}
	public function convert(Product $product, ?ProductVariant $variant = null): array
	{
		$data = [];

		$productLocalization = $product->getLocalization($this->mutationsHolder->getDefault());

		$data['score'] = $productLocalization->score;

		return $data;
	}

}
