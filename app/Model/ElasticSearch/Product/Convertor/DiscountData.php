<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Product\Convertor;

use App\Model\ElasticSearch\Product\Convertor;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;


class DiscountData implements Convertor
{

	public function convert(Product $product, ?ProductVariant $variant = null): array
	{
		$data = [];

		$data['discounts'] = $product->discounts->toCollection()->fetchPairs(null, 'id');
		$data['isInDiscount'] = $data['discounts'] !== [];

		return $data;
	}

}
