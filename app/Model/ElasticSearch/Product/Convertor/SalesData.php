<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Product\Convertor;

use App\Model\ElasticSearch\Product\DelayedConvertor;
use App\Model\Orm\Order\Product\ProductItemRepository;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;

class SalesData implements DelayedConvertor
{

	const COMMAND_SHORTCUT = 'o';

	public function __construct(
		private readonly ProductItemRepository $productItemRepository
	)
	{
	}

	public function convert(Product $product, ?ProductVariant $variant = null): array
	{
		$data = [];
		$data['6MonthSale'] = $this->productItemRepository->getSalesDataFor6Month($product);

		return $data;
	}

}
