<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Product\Convertor;

use App\Model\ElasticSearch\Product\Convertor;
use App\Model\Link\LinkFactory;
use App\Model\Orm\DeliveryMethod\DeliveryMethodConfiguration;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Order\FakeOrder;
use App\Model\Orm\Order\Payment\PaymentType;
use App\Model\Orm\Orm;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductRepository;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\State\State;
use App\PostType\Page\Model\Orm\CatalogTreeModel;
use App\Utils\DateTime;
use Brick\Money\Currency;
use Nextras\Orm\Collection\ICollection;

final class CustomFeedData implements Convertor
{

	public const COMMAND_SHORTCUT = 'f';

	public function __construct(
		private readonly LinkFactory $linkFactory,
		private readonly Orm $orm,
		private readonly ProductRepository $productRepository,
		private readonly CatalogTreeModel $catalogTreeModel,
	)
	{
	}

	public function convert(Product $product, ?ProductVariant $variant = null): array
	{
		$mutation = $product->getMutation();

		$productLocalization = $product->getLocalization($mutation);

		$data = [];


		$categoryCodeHeureka = $this->catalogTreeModel->getFeedCategoryByFeedType($product->mainCategory, 'heureka');
		$categoryCodeZbozi = $this->catalogTreeModel->getFeedCategoryByFeedType($product->mainCategory, 'zbozi');
		$categoryCodeGoogle = $this->catalogTreeModel->getFeedCategoryByFeedType($product->mainCategory, 'google');

		$data['customFeed'] = [
			'code' => $product->firstVariant->code ?: '',
			'availabilityDate' => $this->getAvailabilityDate($product),
			'stockShop' => $product->suplyCountStockDefault,
			'stockSupplier' => $product->suplyCountStockSupplier,
			'shipmentGroup' => '',
			'vats' => $this->getVats($product),
			'categoryName' => $product->mainCategory?->getName(),
			'categoryCode' => $product->mainCategory?->uid,
			'categoryCodeHeureka' => $categoryCodeHeureka,
			'categoryCodeZbozi' => $categoryCodeZbozi,
			'categoryCodeGoogle' => $categoryCodeGoogle,
			'positionInCategory' => $product->categoryMainPosition,
			'image' => $this->getImage($product),
			'previewsUrl' => $this->getPreviewsUrl($product),
			'brand' => $this->getBrand($product),
			'productType' => $product->typeName,
			'productTypeId' => $product->productTypeId,
			'isGift' => $this->isGift($product),
			'deliveryFree' => $product->hasFreeTransport(),
			'isFreeTransit' => $product->hasFreeTransport(),
			'weight' => $this->getParameterValueInt($product, Parameter::UID_WEIGHT),
			'height' => $this->getParameterValueInt($product, Parameter::UID_HEIGHT),
			'width' => $this->getParameterValueInt($product, Parameter::UID_WIDTH),
			'depth' => $this->getParameterValueInt($product, Parameter::UID_DEPTH),
			'pageCount' => $this->getParameterValueInt($product, Parameter::UID_PAGE_COUNT),
			'alternativeProductsCode' => $this->getAlternativeProducts($product),
			'relatedProductsCode' => $this->getRelatedProducts($product),
			'gifts' => $this->getGifts($product),
			'publicationDate' => $this->getPublicationDate($product),
			'languages' => $this->getLangauges($product),
			'shipments' => $this->getShipmentPrices($product),
			'path' => $product->mainCategory?->getPathSentenceWithMyself(' | ', 1),
		];

		return $data;
	}

	private function getRelatedProducts(Product $product): array
	{
		if (!$parameterEdition = $product->getParameterByUid(Parameter::UID_EDITION)) {
			return [];
		}

		if (!isset($parameterEdition->valueObject->id)) {
			return [];
		}

		return $this->productRepository->findByParameterValue($parameterEdition->valueObject->id);
	}

	private function getAlternativeProducts(Product $product): array
	{
		if (!$product->relatedProducts->count()) {
			return [];
		}

		$alternativeProducts = [];

		foreach ($product->relatedProducts as $relatedProduct) {
			$alternativeProducts[] = $relatedProduct->getPersistedId();
		}

		return $alternativeProducts;
	}

	private function getShipmentPrices(Product $product): array
	{
		$shipmentPrices = [];

		$priceLevelDefault = $this->orm->priceLevel->getDefault();
		$mutation = $product->getMutation();
		$currency = $product->getMutation()->currency;

		foreach ($product->getMutation()->states as $country) {
			$deliveryMethods = $this->orm->deliveryMethod->getAvailable($mutation, $country, $priceLevelDefault, $currency)->fetchPairs('id', null);

			/** @var DeliveryMethodConfiguration $deliveryMethod */
			foreach ($deliveryMethods as $deliveryMethod) {
				if ($deliveryMethod->getDeliveryMethod()->isAvailableForOrder(new FakeOrder())) {
					continue;
				}

				$deliveryPrice = $deliveryMethod->getPrice($priceLevelDefault, $country, $currency);
				$productPrice = $product->price($mutation, $priceLevelDefault, $country);
				$isDeliveryFree = $deliveryPrice->freeFrom !== null && $productPrice->getAmount()->isGreaterThan($deliveryPrice->freeFrom);

				$shipmentPrices[$country->code][] = [
					'name' => $deliveryMethod->name,
					'price' => ($product->hasFreeTransport() || $isDeliveryFree) ? 0.0 : $deliveryPrice->getPrice()->getAmount()->toFloat(),
					'pickupPersonalPrice' => $this->getPickUpPrice($deliveryMethod, $mutation, $country, $priceLevelDefault, $currency),
				];
			}
		}

		return $shipmentPrices;
	}

	private function getPickUpPrice(DeliveryMethodConfiguration $deliveryMethod, Mutation $mutation, State $country, PriceLevel $priceLevel, Currency $currency): float|null
	{
		$pickupPersonalPrices = $this->orm->paymentMethod->getAvailableByDelivery($deliveryMethod, $mutation, $country, $priceLevel, $currency)->findBy(['paymentMethodUniqueIdentifier' => PaymentType::CashOnDelivery]);

		if (!$pickupPersonalPrices->count()) {
			return null;
		}

		$pricePickUp = null;

		foreach ($pickupPersonalPrices as $pickupPersonalPrice) {

			if (!$price = $pickupPersonalPrice->getPrice($priceLevel, $country, $currency)) {
				continue;
			}

			if ($pricePickUp === null) {
				$pricePickUp = $price->getPrice()->getAmount()->toFloat();
				continue;
			}

			if ($price->getPrice()->getAmount()->isLessThan($pricePickUp)) {
				$pricePickUp = $price->getPrice()->getAmount()->toFloat();
			}
		}

		return $pricePickUp;
	}

	private function getPublicationDate(Product $product): string|null
	{
		$publicationDateParameter = $product->getParameterByUid(Parameter::UID_PUBLISH_DATE);

		if ($publicationDateParameter === null) {
			return null;
		}

		if (!isset($publicationDateParameter->valueObject->internalValue)) {
			return null;
		}

		try {
			return DateTime::from(str_replace(' ', '',	$publicationDateParameter->valueObject->internalValue))->format('c');
		} catch (\Throwable $exception) {
			// do nothing - date time is in unknown format
		}

		return null;
	}

	private function getGifts(Product $product): array
	{
		$giftEntitites = $this->orm->gift->findBy(['product' => $product->getPersistedId()]);

		if (!$giftEntitites->count()) {
			return [];
		}

		$gifts = [];

		foreach ($giftEntitites as $gift) {

			$giftLocalization = $gift->getLocalization($product->getMutation());

			$gifts[] = [
				'name' => $giftLocalization->getName(),
				'code' => $gift->getPersistedId(),
			];
		}

		return $gifts;
	}

	private function getLangauges(Product $product): array
	{
		if (!$parameter = $product->getParameterByUid(Parameter::UID_LANGUAGE)) {
			return [];
		}

		if (!isset($parameter->valueObjects)) {
			return [];
		}

		$languages = [];

		foreach ($parameter->valueObjects as $parameterValue) {
			$languages[] = [
				'name' => $parameterValue->internalValue,
			];
		}

		return $languages;
	}

	private function getParameterValueInt(Product $product, string $parameterUid): int|null
	{
		if (!$parameter = $product->getParameterByUid($parameterUid)) {
			return null;
		}

		if (!isset($parameter->valueObject->internalValue)) {
			return null;
		}

		return (int) $parameter->valueObject->internalValue;
	}

	private function isGift(Product $product): bool
	{
		$presentDate = $product->getGiftDate($product->getMutation());

		if ($presentDate === null) {
			return false;
		}

		return !$presentDate->diff(\Nette\Utils\DateTime::from('now'))->days;
	}

	public function getEdition(Product $product): string|null
	{
		if (!$parameterEdition = $product->getParameterByUid(Parameter::UID_EDITION)) {
			return null;
		}

		if (!isset($parameterEdition->valueObject->internalValue)) {
			return null;
		}

		return $parameterEdition->valueObject->internalValue ?? null;
	}

	private function getPreviewsUrl(Product $product): array
	{
		if (!isset($product->cf->productImagePages)) {
			return [];
		}

		$previews = [];
		foreach ($product->cf->productImagePages as $preview) {

			if (!isset($preview->image->url)) {
				continue;
			}

			$previews[]['url'] = $product->getMutation()->getRealDomain() . $preview->image->url;
		}

		return $previews;
	}

	private function getBrand(Product $product): array
	{
		return [];
	}



	private function getImage(Product $product): array
	{
		if (!$product->firstImage) {
			return [];
		}

		$url = rtrim($this->linkFactory->linkTranslateToNette($product->getMutation()->pages->title), '/')  . '/' . $product->firstImage->url;

		return [
			'name' => $product->firstImage->name,
			'title' => $product->firstImage->data->cs->name ?? null,
			'url' => $url, //$product->getMutation()->getRealDomain() . $product->firstImage->url,
		];
	}

	private function getVats(Product $product): array
	{
		$vats = [];

		foreach ($product->getMutation()->states as $state) {
			$vats[$state->code] = [
				'type' => $product->vatRateType($state)->value,
				'absolute' => $product->vatRate($state)->toInt(),
			];
		}

		return $vats;
	}



	private function getAvailabilityDate(Product $product): array
	{
		if (!$product->variants->toCollection()->count()) {
			return [];
		}

		$priceLevelDefault = $this->orm->priceLevel->getDefault();
		$deliveryDate = [];

		foreach ($product->variants as $variant) {

			foreach ($product->getMutation()->states as $state) {
				$deliveryDate[$state->code] = $variant->productAvailability->getDeliveryDate($product->getMutation(), $state, $priceLevelDefault, $product->getMutation()->currency)?->from->format('c');
			}

		}

		return $deliveryDate;
	}

}
