<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Product\Convertor;

use App\Model\ElasticSearch\Product\Convertor;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;

class StoreData implements Convertor
{

	public function convert(Product $product, ?ProductVariant $variant = null): array
	{
		$data = [];

		if (isset($variant)) {
			$data['isInStore'] = $variant->isInStock;
			$data['totalSupplyCount'] = $variant->totalSupplyCount;
		} else {
			// prepare for package
			$data['isInStore'] = false;
			$data['totalSupplyCount'] = 0;
		}

		return $data;
	}

}
