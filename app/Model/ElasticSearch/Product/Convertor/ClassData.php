<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Product\Convertor;

use App\Model\ElasticSearch\ConvertorHelper;
use App\Model\ElasticSearch\Product\Convertor;
use App\Model\Orm\ClassEvent\ClassEvent;
use App\Model\Orm\ClassEventSectionMetadata\ClassEventSectionMetadataModel;
use App\Model\Orm\ClassSection\ClassSectionModel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\User\User;

class ClassData implements Convertor
{

	public function __construct(
		//private readonly ClassSectionModel $classSectionModel,
	)
	{
	}


	public const string CLASS_LECTORS = 'classLectors';

	public function convert(Product $product, ?ProductVariant $variant = null): array
	{
		$data = [];
		if (!$product->isCourse()) {
			return $data;
		}
		$data[self::CLASS_LECTORS] = array_map(
			fn(User $lector) => $lector->id,
			array_values($product->getLectors(true))
		);

		$daysCount = (int) ($product->mainCategory->cf->lastMinute->daysCount ?? 0);
		$capacityCount = (int) ($product->mainCategory->cf->lastMinute->capacityCount ?? 0);

		$data['classEvents'] = array_map(
			fn(ClassEvent $classEvent) => [
				'classEventId' => $classEvent->id,
				'from' => ConvertorHelper::convertTime($classEvent->from),
				'fromLastMinuteLimit' => $daysCount,
				'emptyCapacity' => $classEvent->getAvailableCapacity(),
				'emptyCapacityLastMinuteLimit' => $capacityCount
			],
			$product->classEventsPublic->fetchAll()
		);
//		$data['classDurationLevel'] = $this->getDurationLevel($product);
		return $data;
	}

//	private function getDurationLevel(Product $product): int
//	{
//		if ($product->getDuration() < 60*4) {
//			return 1;
//		}
//		if ($product->getDuration() < 60*8) {
//			return 2;
//		}
//		return 3;
//	}

}
