<?php declare(strict_types=1);

namespace App\Model\ElasticSearch\Product\Convertor;

use App\Model\ConfigService;
use App\Model\ElasticSearch\Product\Convertor;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\Parameter\ParameterRepository;
use App\Model\Orm\ParameterValue\ParameterValueRepository;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use Nextras\Orm\Collection\ICollection;

class ParameterData implements Convertor
{

	public const PARAMETER_SORT_NOT_PRIORITY = 10000000;

	private ?array $parametersForFilter = null;

	private ?array $parametersForFilterIds = null;

	public function __construct(
		private readonly ParameterRepository $parameterRepository,
		private readonly ParameterValueRepository $parameterValueRepository,
		private readonly ConfigService $configService,
	)
	{
	}

	public function convert(Product $product, ?ProductVariant $variant = null): array
	{
		return $this->addParameterFilter($product, $product->activeVariants);
	}

	/**
	 * @param ICollection<ProductVariant> $variants
	 */
	private function addParameterFilter(Product $product, ICollection $variants): array
	{
		$basicData   = [];
		$bucketSetup = $this->configService->get('bucketFilter');

		if (isset($bucketSetup['flags'])) {
			foreach ($bucketSetup['flags'] as $flag) {
				foreach ($variants as $variant) {
					if (isset($variant->$flag)) {
						$basicData[$flag] = (bool) $variant->$flag;

						if ($basicData[$flag]) {
							// read only first true
							break;
						}
					}
				}
			}
		}

		if (isset($bucketSetup['flagValues'])) {
			foreach ($bucketSetup['flagValues'] as $flagValues) {
				foreach ($variants as $variant) {
					if (isset($variant->$flagValues)) {
						$basicData[$flagValues][$variant->$flagValues] = $variant->$flagValues;
					}
				}
			}
		}

		$this->fillParametersForFilter();

		// add empty values for parameter
		// to enforce delete old values
		foreach ($this->parametersForFilter as $parameter) {
			$uid             = $parameter->uid;
			$basicData[$uid] = [];
		}

		if ($this->parametersForFilterIds !== [] && $this->parametersForFilterIds !== null) {
			$productParameterIds = $this->parameterValueRepository->findIdsForProductAllParameters(
				$product,
				$this->parametersForFilterIds
			);
		} else {
			$productParameterIds = [];
		}

		foreach ($productParameterIds as $parameterValueId => $parameterValueInfo) {
			$parameterId = $parameterValueInfo->parameterId;
			$esSort = ($parameterValueInfo->prioritySort) ? $parameterValueInfo->sort : self::PARAMETER_SORT_NOT_PRIORITY + $parameterValueInfo->sort;

			if (isset($this->parametersForFilter[$parameterId])) {
				$parameter = $this->parametersForFilter[$parameterId];
				$uid       = $parameter->uid;

				$basicData[$uid][] = [
					'id' => $parameterValueId,
					'sort' => $esSort,
				];

				if ($parameter->type === Parameter::TYPE_NUMBER) {
					// add value for "range" filter
					$value = $this->parameterValueRepository->getById($parameterValueId);
					if ($value !== null) {
						$fixedValue                 = str_replace(',', '.', $value->internalValue);
						$basicData[$uid . '_range'] = (float) $fixedValue;
					}
				}
			}
		}

		return $basicData;
	}


	private function fillParametersForFilter(): void
	{
		if ($this->parametersForFilter === null || current($this->parametersForFilter) && ! current($this->parametersForFilter)->isAttached()) {
			$this->parametersForFilter    = $this->parameterRepository->findBy(['isInFilter' => 1])->fetchPairs('id');
			$this->parametersForFilterIds = $this->parameterRepository->findBy(['isInFilter' => 1])->fetchPairs(
				null,
				'id'
			);
		}
	}

}
