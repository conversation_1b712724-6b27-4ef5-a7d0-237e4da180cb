<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Product\Convertor;

use App\Model\ElasticSearch\ConvertorHelper;
use App\Model\ElasticSearch\Product\Convertor;
use App\Model\Link\LinkFactory;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductProduct\ProductProduct;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Product\ProductText;
use App\PostType\Tag\Model\TagType;
use Nette\Utils\Strings;

class BaseData implements Convertor
{

	public function __construct(
		private readonly LinkFactory $linkFactory,
	)
	{
	}

	public function convert(Product $product, ?ProductVariant $variant = null): array
	{
		$mutation = $product->getMutation();
		$productLocalization = $product->getLocalization($mutation);

		$data = [
			'productId' => $product->getPersistedId(),
			'variantId' => $variant?->getPersistedId(),
			'url' => $this->linkFactory->linkTranslateToNette($productLocalization, ['mutation' => $mutation]),
			'lastModified' => new \DateTimeImmutable(),
			'nameSort' => $productLocalization->name,
			'name' => $productLocalization->name,
			'nameTitle' => $productLocalization->nameTitle,
			'nameAnchor' => $productLocalization->nameAnchor,
			'content' => ($productLocalization->content) ? strip_tags($productLocalization->content) : '',
			'annotation' => $productLocalization->annotation,
			'isPublic' => (bool)$productLocalization->public,
			'isPackage' => $product->isTypePackage,
			'soldCount' => $productLocalization->soldCount,
			'reviewAverage' => $product->reviewAverage,
			'productType' => $product->typeName,
			'productTypeId' => $product->productTypeId,
		];

		$data['eans'] = [];
		$data['productIds'] = [];
		$data['variantIds'] = [];

		if ($variant->ean) {
			$data['eans'][] = Strings::lower((string)$variant->ean);
		}

		return $data;
	}

}
