<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Product\Convertor;

use App\Model\ElasticSearch\ConvertorHelper;
use App\Model\ElasticSearch\Product\Convertor;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;

class DateTimeData implements Convertor
{
	public function __construct(
	)
	{
	}

	public function convert(Product $product, ?ProductVariant $variant = null): array
	{
		return [
			'publicFrom' => ConvertorHelper::convertTime($product->publicFrom),
			'publicTo' => ConvertorHelper::convertTime($product->publicTo),
			'dateCreated' => ConvertorHelper::convertTime($product->dateCreated),
		];
	}
}
