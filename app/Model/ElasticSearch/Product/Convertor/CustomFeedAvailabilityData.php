<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Product\Convertor;

use App\Infrastructure\Latte\Functions;
use App\Model\ElasticSearch\Product\Convertor;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\Stock\Stock;
use App\Model\Orm\Supply\Supply;
use App\Model\TranslatorDB;

final class CustomFeedAvailabilityData implements Convertor
{
	public function __construct(private readonly TranslatorDB $translator)
	{
	}

	public const COMMAND_SHORTCUT = 'a';
	public function convert(Product $product, ?ProductVariant $variant = null): array
	{
		return ['customFeedAvailability' => [
				'type' => $product->productAvailability->getType(),
				'text_front' => $this->translator->translate($product->productAvailability->getAvailabilityText()),
				'text_back' => $this->translator->translate($product->productAvailability->getAvailabilityStateText()),
				'dateStock' => $product->productAvailability->getStockDate()->setTime(0,0)->format('c'),
			]
		];
	}

}
