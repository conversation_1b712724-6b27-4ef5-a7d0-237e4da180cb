<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Product\Convertor;

use App\Model\ElasticSearch\Product\Convertor;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;

final class CustomFeedDataFlag implements Convertor
{

	public function convert(Product $product, ?ProductVariant $variant = null): array
	{
		return ['hasCustomFeedData' => true];
	}

}
