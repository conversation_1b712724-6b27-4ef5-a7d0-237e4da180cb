<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Product\Convertor;

use App\Model\ElasticSearch\Product\Convertor;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\CatalogTreeModel;
use App\PostType\Page\Model\Orm\TreeRepository;

class CategoryData implements Convertor
{

	public function __construct(
		private TreeRepository $treeRepository,
		private CatalogTreeModel $catalogTreeModel,
	)
	{
	}

	public function convert(Product $product, ?ProductVariant $variant = null): array
	{
		$mutation = $product->getMutation();

		$allCategories = $this->catalogTreeModel->getAllCatalogCategories($product, $mutation);
		$allCategoriesIds = array_values(array_map(function ($category) {
			return $category->id;
		}, $allCategories));

		// only public cats
		$allPublicCategories = $this->treeRepository->findByIds($allCategoriesIds)->findBy($this->treeRepository->getPublicOnlyWhereParams());

		// add all categories and their paths to path
		$path = []; $ignoreIds = [];
		/** @var CatalogTree $category */
		foreach ($allPublicCategories as $category) {
			if (!($category instanceof CatalogTree)) {
				$ignoreIds[] = $category->id;
			}
			if ($category instanceof CatalogTree) {
				$path[$category->id] = $category->id;
				foreach ($category->path as $pathId) {
					if (!in_array($pathId, $ignoreIds)) {
						$path[$pathId] = $pathId;
					}
				}
			}
		}

		$categories = $product->attachCategories->findBy(['mutation' => $mutation]);
		$data = [
			'path' => array_values($path), //$allPublicCategories->fetchPairs(null, 'id'),
			'categories' => $categories->fetchPairs(null, 'id'),
		];


		if ($mainCategory = $product->mainCategory) {
			$data['mainCategory'] = $mainCategory->id;
		}


		return $data;
	}

}
