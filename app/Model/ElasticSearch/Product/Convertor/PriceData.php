<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Product\Convertor;

use App\Model\Currency\CurrencyHelper;
use App\Model\ElasticSearch\Product\Convertor;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\PriceLevel\PriceLevelModel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\Availability\CustomProductAvailability;
use App\Model\Orm\ProductVariant\ProductVariant;
use Brick\Money\Currency;

class PriceData implements Convertor
{

	const COMMAND_SHORTCUT = 'p';

	public function __construct(
		private PriceLevelModel $priceLevelModel,
	)
	{
	}

	public function convert(Product $product, ?ProductVariant $variant = null): array
	{
		$mutation = $product->getMutation();
		$data = [];

		$data['statePricesWithVat'] = [];
		$priceLevels = $this->priceLevelModel->getAllPriceLevelByType([PriceLevel::TYPE_DEFAULT]);

		$data['showInPriceFilter'] = !in_array($product->productAvailability->getType(), [
			CustomProductAvailability::TYPE_OUT_OF_STOCK,
			CustomProductAvailability::TYPE_NOT_FOR_SALE,
		]);

		foreach ($mutation->states as $state) {
			foreach ($priceLevels as $priceLevel) {
				assert($priceLevel instanceof PriceLevel);
				foreach (CurrencyHelper::CURRENCIES as $currencyCode) {
					$currency = Currency::of($currencyCode);
					$mutation->setSelectedCurrency($currency);
					$price = $product->priceVat($mutation, $priceLevel, $state);

					$data['hasPrice'][$state->code][$priceLevel->type][$currencyCode] = !$price->isZero();

					if (!$price->isZero()) {
						$data['statePricesWithVat'][$state->code][$priceLevel->type][$currencyCode] = [$price->getAmount()->toFloat()];
					}
					$data['discountPercentage'][$state->code][$priceLevel->type][$currencyCode] = $product->getPriceInfo($mutation, $priceLevel, $state)->getDiscountPercentageSort();
				}
			}
		}


		$productLocalization = $product->getLocalization($mutation);
		$data['isPublic'] = (bool) $productLocalization->public;

		return $data;
	}

}
