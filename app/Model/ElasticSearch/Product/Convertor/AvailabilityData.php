<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Product\Convertor;

use App\Model\ElasticSearch\Product\Convertor;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;

class AvailabilityData implements Convertor
{

	public function convert(Product $product, ?ProductVariant $variant = null): array
	{
		$data = [];
		$data['availability'] = $this->getAvailabilities($product);

		return $data;
	}

	private function getAvailabilities(Product $product): string
	{
		return $product->productAvailability->getType();
	}
}
