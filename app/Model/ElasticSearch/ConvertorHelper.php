<?php

declare(strict_types=1);

namespace App\Model\ElasticSearch;


use DateTimeImmutable;
use DateTimeInterface;

class ConvertorHelper
{
	/**
	 * @see https://www.elastic.co/guide/en/elasticsearch/reference/current/mapping-date-format.html#built-in-date-formats
	 * @param DateTimeInterface $dateTime
	 * @param string $esFormat
	 * @return string|void
	 */
	public static function convertTime(DateTimeInterface $dateTime, string $esFormat = 'basic_date_time_no_millis')
	{
		switch ($esFormat) {
			case 'basic_date_time_no_millis':
				return $dateTime->format('Ymd').'T'.$dateTime->format('HisO');
		}
	}

	/**
	 * Returns datetime with no time values - reset to 0
	 * 29-09-2024 12:30 -> 29-09-2024 0:00
	 *
	 * @param DateTimeInterface $dateTime
	 * @return DateTimeImmutable
	 * @throws \DateMalformedStringException
	 */
	public static function dateOnly(DateTimeInterface $dateTime): DateTimeImmutable
	{
		return new DateTimeImmutable((clone $dateTime)->format('Y-m-d'));
	}
}
