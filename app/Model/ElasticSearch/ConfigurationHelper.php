<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch;

use App\Model\ConfigService;
use App\Model\Orm\EsIndex\EsIndex;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\Parameter\ParameterRepository;
use LogicException;
use Nette\Neon\Neon;

class ConfigurationHelper
{

	public function __construct(
		private readonly ConfigService $configService,
		private readonly ParameterRepository $parameterRepository,
	)
	{}

	public function readNeonConfig(Mutation $mutation, string $type): array
	{
		$mappingFile = APP_DIR . '/config/elasticSearchSetup/mapping/' . $mutation->langCode . '.neon';
		$settingFile = APP_DIR . '/config/elasticSearchSetup/settings/' . $mutation->langCode . '.neon';

		if (!file_exists($settingFile)
			|| !file_exists($mappingFile)
			|| ($mappingContent = file_get_contents($mappingFile)) === false
			|| ($settingContent = file_get_contents($settingFile)) === false

		) {
			throw new LogicException(sprintf('Missing Elasticsearch mapping or setting file for mutation \'%s\'', $mutation->langCode));
		}

		$mapping = Neon::decode($mappingContent)[$type];
		$setting = Neon::decode($settingContent);

		if (isset($setting['analysis']['filter']['fileSynonym'])) {
			$pathToFile = $this->configService->get('elasticSearch', 'pathToSynonyms');
			$setting['analysis']['filter']['fileSynonym']['synonyms_path'] = $pathToFile . $mutation->langCode . '.txt';
		}

		if ($type === EsIndex::TYPE_PRODUCT) {
			// add info for nester parameters
			foreach ($this->parameterRepository->findBy(['isInFilter' => true, 'type' => [Parameter::TYPE_SELECT, Parameter::TYPE_MULTISELECT]]) as $parameter) {
				$mapping['properties'][$parameter->uid] = ['type' => 'nested'];
			}
		}

		return [$setting, $mapping];
	}

}
