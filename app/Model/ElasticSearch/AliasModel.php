<?php declare(strict_types=1);

namespace App\Model\ElasticSearch;

use App\Model\Orm\EsIndex\EsIndex;
use LogicException;

class AliasModel
{

	public function __construct(
		private readonly array $elasticIndexesDefinitions,
	)
	{
	}


	public function getAliasName(EsIndex $esIndex): string
	{
		if (!isset($this->elasticIndexesDefinitions[$esIndex->type]['name'])) {
			throw new LogicException(sprintf('Missing type \'%s\' for elastic search alias in elasticSearch.neon', $esIndex->type));
		}

		return $this->elasticIndexesDefinitions[$esIndex->type]['name'] . '_' . $esIndex->mutation->langCode . '_active';
	}

}
