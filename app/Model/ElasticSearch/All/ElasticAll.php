<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\All;

use App\Model\ElasticSearch\Entity;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Product\Product;
use App\PostType\Banner\Model\Orm\BannerLocalization;
use App\PostType\Discount\Model\Orm\DiscountLocalization\DiscountLocalization;
use App\PostType\Gift\Model\Orm\Gift\GiftLocalization;
use App\PostType\Brand\Model\Orm\BrandLocalization;
use App\PostType\MenuMain\Model\Orm\MenuMainLocalization\MenuMainLocalization;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Author\Model\Orm\AuthorLocalization;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalization;
use App\PostType\Promotion\Model\Orm\Promotion\PromotionLocalization;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalization;
use App\PostType\Tag\Model\Orm\TagLocalization\TagLocalization;
use App\PostType\Calendar\Model\Orm\CalendarLocalization;
use App\PostType\CalendarTag\Model\Orm\CalendarTag\CalendarTagLocalization;
use LogicException;

/**
 * @param Convertor[] $convertors
 */
class ElasticAll implements Entity
{

	public const TYPE_TREE = 'tree';
	public const TYPE_BLOG = 'blogLocalization';
	public const TYPE_BLOG_TAG = 'blogTagLocalization';
	public const string TYPE_CALENDAR = 'calendarLocalization';
	public const string TYPE_CALENDAR_TAG = 'calendarTagLocalization';
	public const TYPE_BANNER = 'bannerLocalization';
	public const TYPE_AUTHOR = 'authorLocalization';
	public const TYPE_PRODUCT = 'product';
	public const TYPE_SEOLINK = 'seoLinkLocalization';

	public const TYPE_DISCOUNT = 'discountLocalization';
	public const TYPE_MENU_MAIN = 'menuMainLocalization';
	public const TYPE_TAG = 'TagLocalization';
	const TYPE_POMOTION = 'promotionLocalization';
	public const TYPE_GIFT = 'giftLocalization';
	public const TYPE_BRAND = 'brandLocalization';

	public function __construct(
		private object $object,
		private array $convertors = [],
	)
	{
	}

	public function getId(): string
	{
		$class = get_class($this->object);

		if (isset($this->object->id)) {
			return match ($class) {
				BlogLocalization::class => self::TYPE_BLOG . '-' . $this->object->id,
				BlogTagLocalization::class => self::TYPE_BLOG_TAG . '-' . $this->object->id,
				CalendarLocalization::class => self::TYPE_CALENDAR . '-' . $this->object->id,
				CalendarTagLocalization::class => self::TYPE_CALENDAR_TAG . '-' . $this->object->id,
				AuthorLocalization::class => self::TYPE_AUTHOR . '-' . $this->object->id,
				CommonTree::class, CatalogTree::class => self::TYPE_TREE . '-' . $this->object->id,
				Product::class => self::TYPE_PRODUCT . '-' . $this->object->id,
				SeoLinkLocalization::class => self::TYPE_SEOLINK . '-' . $this->object->id,
				DiscountLocalization::class => self::TYPE_DISCOUNT . '-' . $this->object->id,
				MenuMainLocalization::class => self::TYPE_MENU_MAIN . '-' . $this->object->id,
				TagLocalization::class => self::TYPE_TAG . '-' . $this->object->id,
				PromotionLocalization::class => self::TYPE_POMOTION . '-' . $this->object->id,
				GiftLocalization::class => self::TYPE_GIFT . '-' . $this->object->id,
				BannerLocalization::class => self::TYPE_BANNER . '-' . $this->object->id,
				BrandLocalization::class => self::TYPE_BRAND . '-' . $this->object->id,
				default => throw new LogicException(sprintf("Missing definition for '%s' class", $class))
			};
		} else {
			throw new LogicException('Missing primary key for entity');
		}
	}

	public function getData(Mutation $mutation): array
	{
		$convertedData = [];
		foreach ($this->convertors as $convertor) {
			$convertedData[] = $convertor->convert($this->object);
		}

		return array_merge(...$convertedData);
	}

}
