<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\All;

use App\Model\ElasticSearch\All\Convertor\ProductData;
use App\Model\ElasticSearch\IndexModel;
use App\Model\Orm\EsIndex\EsIndex;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductRepository;
use Elastica\Query;
use Elastica\QueryBuilder;
use Elastica\ResultSet;
use Nette\Utils\Strings;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;

class Repository extends \App\Model\ElasticSearch\Repository
{

	public function __construct(
		IndexModel $indexModel,
		private readonly ProductRepository $productRepository,
	)
	{
		parent::__construct($indexModel);
	}


	// BE search - products
	public function elasticSearchAdmin(EsIndex $esIndex, array $params): array
	{
		$query = new Query();
		$query->setSize(0);

		$b = new QueryBuilder();

		$must = [];
		$must[] = $b->query()->term(['kind' => 'product']);

		if (isset($params['isOld']) && $params['isOld']) {
			$must[] = $b->query()->term(['isOld' => $params['isOld']]);
		}
		if (isset($params['isNew']) && $params['isNew']) {
			$must[] = $b->query()->term(['isNew' => $params['isNew']]);
		}


		if (isset($params['fulltext'])) {
			$must[] = $b->query()->multi_match()
				->setType("phrase_prefix")
				->setQuery($params['fulltext'])
				->setFields([
					'name',
					'ean',
					'code',
				]);
		}

		$bool = $queryBool = $b->query()
			->bool();
		foreach ($must as $item) {
			$bool->addMust($item);
		}

		$query->setQuery(
			$queryBool
		);
		$query->addAggregation(
			$b->aggregation()
				->terms('productId')
				->setField('productId')
				->setSize(1000)
		);

		$result = $this->baseSearch($esIndex, $query); //ProductVariant::ELASTIC_TYPE

		$productIdAgg = $result->getAggregation('productId');

		$productids = [];
		foreach ($productIdAgg['buckets'] as $bucket) {
			$productids[] = $bucket['key'];
		}

		return $productids;
	}

	public function findByProductPrefix(EsIndex $esIndex, string $prefix, ?int $limit = 10, ?int $offset = 0): ResultSet
	{
		$b = new QueryBuilder();
		$query = $b->query()->bool()->addMust(
			$b->query()->match('name', $prefix)
		)->addMust(
			$b->query()->term(['type' => ProductData::KIND_PRODUCT])
		);

		return $this->findByQuery($esIndex, $query, $limit, $offset);
	}

	public function elasticSearchAdminSuggest(EsIndex $esIndex, string $q, int $size = 10, int $from = 0): ResultSet
	{
		$query = new Query();
		$query->setSize($size)
			->setFrom($from)
			->setExplain(true)
			->setVersion(true);

		$fields = [];
		$fields[] = 'name^40';
		$fields[] = 'nameTitle^30';
		$fields[] = 'annotation^20';
		$fields[] = 'description^20';

		$b = new QueryBuilder();

		$must = [];
		$should = $b->query()->bool()->addShould(
			$b->query()->multi_match()
				->setType('best_fields') //phrase_prefix phrase, cross_fields best_fields
				->setQuery($q)
				->setFuzziness(3)
				->setOperator('OR')
				->setFields($fields)
		);

		foreach ($this->prepareEans($q) as $ean){
			$should->addShould(
				$b->query()->term()->setTerm('filter.eans', $ean, 10000)
			);
		}

		$must[] = $should;

		$queryBool = $b->query()
			->bool();

		foreach ($must as $item) {
			$queryBool->addMust($item);
		}

		$query->setQuery(
			$queryBool
		);

		return $this->baseSearch($esIndex, $query);
	}

	private function prepareEans(string $eansAsString): array
	{
		$eanFiltered = [];

		foreach(Strings::split($eansAsString, '/\W/') as $ean){

			$eanTrimmed = Strings::trim($ean);

			if ($eanTrimmed === ''){
				continue;
			}

			$eanFiltered[] = $eanTrimmed;
		}

		return $eanFiltered;
	}


	/**
	 * @return ICollection<Product>
	 */
    public function searchProduct(EsIndex $esIndex, string $search): ICollection
    {
		$query = new Query();
		$query->setSize(10);

		$b = new QueryBuilder();

		$must = [];
		$must[] = $b->query()->term(['kind' => 'product']);

		$must[] = $b->query()->multi_match()
			->setType("phrase_prefix")
			->setQuery($search)
			->setFields([
				'name',
				'filter.eans',
				'filter.codes',
			]);

		$bool = $b->query()->bool();
		foreach ($must as $item) {
			$bool->addMust($item);
		}

		$query->setQuery($bool);
		$result = $this->baseSearch($esIndex, $query); //ProductVariant::ELASTIC_TYPE
		$productIds = [];
		foreach ($result->getResults() as $result) {
			$productIds[] = $result->getSource()['id'];
		}

		if ($productIds === []) {
			/** @var EmptyCollection<Product> $empty */
			$empty = new EmptyCollection();
			return $empty;
		}

		return $this->productRepository->findByIds($productIds);
    }


}
