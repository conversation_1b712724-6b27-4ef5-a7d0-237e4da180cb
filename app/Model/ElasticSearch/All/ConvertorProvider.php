<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\All;

use App\Model\ElasticSearch\All\Convertor\AuthorData;
use App\Model\ElasticSearch\All\Convertor\BannerData;
use App\Model\ElasticSearch\All\Convertor\BlogData;
use App\Model\ElasticSearch\All\Convertor\BlogTagData;
use App\Model\ElasticSearch\All\Convertor\CalendarData;
use App\Model\ElasticSearch\All\Convertor\CalendarTagData;
use App\Model\ElasticSearch\All\Convertor\DiscountData;
use App\Model\ElasticSearch\All\Convertor\GiftData;
use App\Model\ElasticSearch\All\Convertor\BrandData;
use App\Model\ElasticSearch\All\Convertor\MenuMainData;
use App\Model\ElasticSearch\All\Convertor\ProductData;
use App\Model\ElasticSearch\All\Convertor\PromotionData;
use App\Model\ElasticSearch\All\Convertor\SeoLinkData;
use App\Model\ElasticSearch\All\Convertor\TagData;
use App\Model\ElasticSearch\All\Convertor\TreeData;
use App\Model\Orm\Product\Product;
use App\PostType\Banner\Model\Orm\BannerLocalization;
use App\PostType\Calendar\Model\Orm\CalendarLocalization;
use App\PostType\CalendarTag\Model\Orm\CalendarTag\CalendarTagLocalization;
use App\PostType\Discount\Model\Orm\DiscountLocalization\DiscountLocalization;
use App\PostType\Gift\Model\Orm\Gift\GiftLocalization;
use App\PostType\Brand\Model\Orm\BrandLocalization;
use App\PostType\MenuMain\Model\Orm\MenuMainLocalization\MenuMainLocalization;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Author\Model\Orm\AuthorLocalization;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalization;
use App\PostType\Promotion\Model\Orm\Promotion\PromotionLocalization;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalization;
use App\PostType\Tag\Model\Orm\TagLocalization\TagLocalization;
use LogicException;

class ConvertorProvider
{

	private array $map;

	public function __construct(
		private readonly TreeData $treeData,
		private readonly BlogData $blogData,
		private readonly BlogTagData $blogTagData,
		private readonly CalendarData $calendarData,
		private readonly CalendarTagData $calendarTagData,
		private readonly AuthorData $authorData,
		private readonly ProductData $productData,
		private readonly SeoLinkData $seoLinkData,
		private readonly DiscountData $discountData,
		private readonly MenuMainData $menuMainData,
		private readonly TagData $tagData,
		private readonly PromotionData $promotionData,
		private readonly GiftData $giftData,
		private readonly BannerData $bannerData,
		private readonly BrandData $brandData,
	)
	{
		$this->map = [];
		foreach (func_get_args() as $convertor) {
			$this->map[$convertor::class] = $convertor;
		}
	}


	public function get(string $class): Convertor
	{
		return $this->map[$class] ?? throw new LogicException(sprintf("Missing convertor for '%s' class", $class));
	}


	public function getAll(string $class): array
	{
		return match ($class) {

			BlogLocalization::class => [
				$this->blogData,
			],
			BlogTagLocalization::class => [
				$this->blogTagData,
			],
			CalendarLocalization::class => [
				$this->calendarData,
			],
			CalendarTagLocalization::class => [
				$this->calendarTagData,
			],
			AuthorLocalization::class => [
				$this->authorData,
			],
			Product::class => [
				$this->productData,
			],
			SeoLinkLocalization::class => [
				$this->seoLinkData,
			],

			DiscountLocalization::class => [
				$this->discountData,
			],
			MenuMainLocalization::class => [
				$this->menuMainData,
			],
			TagLocalization::class => [
				$this->tagData,
			],
			PromotionLocalization::class => [
				$this->promotionData,
			],
			GiftLocalization::class => [
				$this->giftData,
			],
			Tree::class, CatalogTree::class, CommonTree::class => [
				$this->treeData,
			],
			BannerLocalization::class => [
				$this->bannerData,
			],
			BrandLocalization::class => [
				$this->brandData,
			],
			default => throw new LogicException(sprintf("Missing definition for '%s' class", $class))
		};
	}

	public function getAllLikeStrings(string $class): array
	{
		return array_map(fn(object $item) => $item::class, $this->getAll($class));
	}

}
