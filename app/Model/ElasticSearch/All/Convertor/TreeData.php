<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\All\Convertor;

use App\Model\ElasticSearch\All\Convertor;
use App\PostType\Page\Model\Orm\Tree;
use Nette\Utils\Strings;

class TreeData implements Convertor
{

	public function convert(object $object): array
	{
		assert($object instanceof Tree);
		$name = $object->name;
		if (Strings::length($object->uid) > 0) {
			$name .= ' - ' . $object->uid;
		}

		$ret = [
			'id' => $object->id,
			'parentId' => $object->parentId ?? 0,
			'isSystemPage' => $this->isSystemPage($object),
			'type' => $this->getTreeType($object),
			'langCode' => $object->mutation->langCode,
			'name' => $name,
			'nameTitle' => $object->nameTitle,
			'description' => $object->description,
			'annotation' => $object->annotation,
		];
		$ret['kind'] = 'tree';

		return $ret;
	}

	private function isSystemPage(Tree $tree): bool
	{
		// TODO
		// ziskani id skryte kategorie pro dany jazyk
		//podle rodice rozhodnuti zda jde o systm. stranku - hidePageId

		$systemPageId = 400; // TODO ziskani ID.... config, podle rootId z tree

		// oznacim jako systemoovu stranku vsechny potomky hlavní systémové stránky UID systemPageId

		if ($tree->id === $systemPageId) {
			return true;
		}

		if ($tree->parentId && $tree->parentId === $systemPageId) {
			return true;
		}

		if ($tree->path) {
			foreach ($tree->path as $pageId) {
				if ($pageId === $systemPageId) {
					return true;
				}
			}
		}

		return false;
	}

	private function getTreeType(Tree $tree): string
	{
		if ($tree->template === ':Front:Catalog:default') {
			return 'category';
		} else {
			return 'tree';
		}
	}

}
