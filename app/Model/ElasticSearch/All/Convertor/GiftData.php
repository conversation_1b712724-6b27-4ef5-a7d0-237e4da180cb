<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\All\Convertor;

use App\Model\ElasticSearch\All\Convertor;
use App\PostType\Gift\Model\Orm\Gift\GiftLocalization;

class GiftData implements Convertor
{

	public const TYPE = 'gift';

	public function convert(object $object): array
	{
		assert($object instanceof GiftLocalization);

		return [
			'id' => $object->id,
			'name' => $object->name,
			'type' => self::TYPE,
			'langCode' => $object->mutation->langCode,
		];
	}

}
