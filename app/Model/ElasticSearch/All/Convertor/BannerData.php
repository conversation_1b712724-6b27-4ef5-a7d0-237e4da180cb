<?php

namespace App\Model\ElasticSearch\All\Convertor;

use App\Model\ElasticSearch\All\Convertor;
use App\PostType\Banner\Model\Orm\BannerLocalization;

class BannerData implements Convertor
{

    public function convert(object $object): array
    {
		assert($object instanceof BannerLocalization);

		return [
			'id' => $object->id,
			'isSystemPage' => false,
			'type' => 'contact',
			'langCode' => $object->mutation->langCode,
			'name' => $object->name . ' (' . $object->getParent()->getInternalName() . ')',
			'annotation' => '',
			'kind' => 'contact',
		];
    }
}
