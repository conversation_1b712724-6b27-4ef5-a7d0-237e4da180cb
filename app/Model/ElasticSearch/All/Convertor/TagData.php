<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\All\Convertor;

use App\Model\ElasticSearch\All\Convertor;
use App\PostType\Tag\Model\Orm\TagLocalization\TagLocalization;

class TagData implements Convertor
{

	public const TYPE = 'tag';

	public function convert(object $object): array
	{
		assert($object instanceof TagLocalization);

		$ret = [
			'id' => $object->id,
			'isSystemPage' => false,
			'type' => self::TYPE,
			'langCode' => $object->mutation->langCode,

			'name' => $object->name . ' (' . $object->getParent()->getInternalName() . ')',
		];

		return $ret;
	}

}
