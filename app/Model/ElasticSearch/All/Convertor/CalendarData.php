<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\All\Convertor;

use App\Model\ElasticSearch\All\Convertor;
use App\PostType\Calendar\Model\Orm\CalendarLocalization;

class CalendarData implements Convertor
{

	public function convert(object $object): array
	{
		assert($object instanceof CalendarLocalization);
		$ret = [
			'id' => $object->id,
			'isSystemPage' => false,
			'type' => 'calendar',
			'langCode' => $object->mutation->langCode,
			'isPublic' => $object->getPublic(),
			'name' => $object->name . ' (' . $object->getParent()->getInternalName() . ')',
			'nameTitle' => $object->nameTitle,
			'description' => $object->description,
			'annotation' => $object->annotation,
		];
		$ret['kind'] = 'calendar';

		return $ret;
	}

}
