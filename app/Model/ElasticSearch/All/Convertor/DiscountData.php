<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\All\Convertor;

use App\Model\ElasticSearch\All\Convertor;
use App\PostType\Discount\Model\Orm\DiscountLocalization\DiscountLocalization;

class DiscountData implements Convertor
{

	public function convert(object $object): array
	{
		assert($object instanceof DiscountLocalization);
		$ret = [
			'id' => $object->id,
			'isSystemPage' => false,
			'type' => 'discount',
			'langCode' => $object->mutation->langCode,

			'name' => $object->name . ' (' . $object->getParent()->getInternalName() . ')',
			'nameTitle' => $object->nameTitle,
			'description' => $object->description,
		];

		return $ret;
	}

}
