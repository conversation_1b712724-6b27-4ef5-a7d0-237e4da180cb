<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\All\Convertor;

use App\PostType\Author\Model\Orm\AuthorLocalization;
use App\Model\ElasticSearch\All\Convertor;

class AuthorData implements Convertor
{

	public function convert(object $object): array
	{
		assert($object instanceof AuthorLocalization);
		$ret = [
			'id' => $object->id,
			'isSystemPage' => false,
			'type' => 'author',
			'langCode' => $object->mutation->langCode,
			'name' => $object->name . ' (' . $object->getParent()->getInternalName() . ')',
			'nameTitle' => $object->nameTitle,
			'description' => $object->description,
			'annotation' => '',
		];
		$ret['kind'] = 'author';

		return $ret;
	}

}
