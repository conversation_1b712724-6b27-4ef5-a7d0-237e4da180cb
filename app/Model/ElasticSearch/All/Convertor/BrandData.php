<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\All\Convertor;

use App\Model\ElasticSearch\All\Convertor;
use App\PostType\Brand\Model\Orm\BrandLocalization;

class BrandData implements Convertor
{

	public function convert(object $object): array
	{
		assert($object instanceof BrandLocalization);
		$ret = [
			'id' => $object->id,
			'isSystemPage' => false,
			'type' => 'brand',
			'langCode' => $object->mutation->langCode,
			'name' => $object->name . ' (' . $object->getParent()->getInternalName() . ')',
			'nameTitle' => $object->nameTitle,
			'description' => $object->description,
			'annotation' => '',
		];
		$ret['kind'] = 'brand';

		return $ret;
	}

}
