<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\All\Convertor;

use App\Model\ElasticSearch\All\Convertor;
use App\PostType\MenuMain\Model\Orm\MenuMainLocalization\MenuMainLocalization;

class MenuMainData implements Convertor
{
	public const TYPE = 'menuMain';

	public function convert(object $object): array
	{
		assert($object instanceof MenuMainLocalization);
		$ret = [
			'id' => $object->id,
			'isSystemPage' => false,
			'type' => self::TYPE,
			'langCode' => $object->mutation->langCode,
			'name' => $object->name . ' (' . $object->getParent()->getInternalName() . ')',
			'annotation' => '',
		];

		return $ret;
	}

}
