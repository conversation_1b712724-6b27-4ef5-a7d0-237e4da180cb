<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\All\Convertor;

use App\Model\ElasticSearch\All\Convertor;
use App\Model\Mutation\MutationHolder;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ParameterValue\ParameterValueRepository;
use App\Model\Orm\PriceLevel\PriceLevelRepository;
use App\Model\Orm\Product\Product;
use App\Model\Orm\State\StateRepository;
use App\Model\TranslatorDB;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Tag\Model\TagType;
use Nette\Utils\Strings;

class ProductData implements Convertor
{

	public const KIND_PRODUCT = 'product';

	public function __construct(
		private readonly MutationsHolder $mutationsHolder,
		private readonly MutationHolder $mutationHolder,
		private readonly PriceLevelRepository $priceLevelRepository,
		private readonly StateRepository $stateRepository,
		private readonly TranslatorDB $translatorDB,
		private readonly ParameterValueRepository $parameterValueRepository,
	)
	{
	}

	public function convert(object $object): array
	{
		assert($object instanceof Product);

		$product = $object;

		$this->mutationHolder->setMutation($this->mutationsHolder->getDefault());
		$mutation = $this->mutationHolder->getMutation();

		$product->setMutation($mutation);
		$this->translatorDB->setMutation($mutation);

		$priceGroup = $this->priceLevelRepository->getDefault();
		$state = $this->stateRepository->getDefault($mutation);

		$prices = [];
		$codes = [];
		$eans = [];
		foreach ($product->variants as $variant) {
			$prices[] = $variant->priceVat($mutation, $priceGroup, $state);

			if (!empty($variant->ean)) {
				$eans[] = (string) $variant->ean;
			}

			if (!empty($variant->extId)) {
				$codes[] = (string) $variant->extId;
			}
		}

		$categories = [];
		foreach ($product->inCategories as $category) {
			$categories[] = $category->id;
		}

		$category = null;
		foreach ($product->attachCategories as $categoryMain) {
			$categoryPath = [];
			foreach ($categoryMain->pathItems as $categoryItem) {
				if ($categoryItem->uid === Tree::UID_TITLE || $categoryItem->uid === Tree::UID_ESHOP) {
					continue;
				}
				$categoryPath[] = $categoryItem->name;
			}
			$categoryPath[] = $categoryMain->name;
			$category = implode(' > ', $categoryPath);
			break;
		}

		$ret = [
			'id' => $product->id,
			'name' => $product->name,
			'nameSort' => ($product->name !== null) ? mb_strtolower($product->name) : '',
			//'nameTitle' => $product->nameTitle,
			//'description' => $product->description,
			//'annotation' => $product->annotation,
			'type' => self::KIND_PRODUCT,
			'productType' => $product->typeName,
			'productTypeId' => $product->productTypeId,
		];

		$ret['bulkFilter'] = [];
		$ret['bulkFilter']['languageIds'] = $this->parameterValueRepository->getRawValuesIds($product, Parameter::UID_LANGUAGE);
		$ret['bulkFilter']['tagIds'] = $product->tags->toCollection()->fetchPairs(null, 'id');


		$filter = [
			'priceVat' => [
				$state->code => [
					$priceGroup->type => $prices,
				],
			],
			//Parameter::UID_MANUFACTURER => $brandParamValue,
			'categories' => $categories,
			'codes' => $codes,
			'eans' => $eans,
			'isOld' => (int) $product->isOld,
			'isReSale' => (int) $product->isReSale,
			'isNew' => (int) $product->hasTag(TagType::new),
			'isInStock' => (int) $product->isInStock,
			'publish' => $this->getPublishData($product),
			'publishDate' => [$product->publicFrom->getTimestamp(), $product->publicTo->getTimestamp()],
			'productType' => $product->typeName,
			'productTypeId' => $product->productTypeId,
			'category' => $category ?? '',
			'score' => $product->score,
			'availability' => $product->productAvailability->getAvailabilityStateText(),
		];

		$ret['filter'] = $filter;

		$ret['kind'] = self::KIND_PRODUCT;

		return $ret;
	}

	private function getPublishData(Product $product): array
	{
		$data = [];
		foreach ($product->productLocalizations as $productLocalization) {
			if ($productLocalization->public) {
				$data[] = sprintf('%s_public', $productLocalization->mutation->langCode);
			} else {
				$data[] = sprintf('%s_hide', $productLocalization->mutation->langCode);
			}
		}

		return $data;
	}

}
