<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\All\Convertor;

use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalization;
use App\Model\ElasticSearch\All\Convertor;

class BlogTagData implements Convertor
{

	public function convert(object $object): array
	{
		assert($object instanceof BlogTagLocalization);
		$ret = [
			'id' => $object->id,
			'isSystemPage' => false,
			'type' => 'blogTag',
			'langCode' => $object->mutation->langCode,
			'name' => $object->name . ' (' . $object->getParent()->getInternalName() . ')',
			'nameTitle' => $object->nameTitle,
			'description' => $object->description,
			'annotation' => '',
		];
		$ret['kind'] = 'blogTag';

		return $ret;
	}

}
