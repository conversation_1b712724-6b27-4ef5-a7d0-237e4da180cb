<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\All\Convertor;

use App\Model\ElasticSearch\All\Convertor;
use App\PostType\Promotion\Model\Orm\Promotion\PromotionLocalization;
use App\PostType\Tag\Model\Orm\TagLocalization\TagLocalization;

class PromotionData implements Convertor
{

	public const TYPE = 'promotion';

	public function convert(object $object): array
	{
		assert($object instanceof PromotionLocalization);

		return [
			'id' => $object->id,
			'isSystemPage' => false,
			'type' => self::TYPE,
			'langCode' => $object->mutation->langCode,
			'name' => $object->name . ' (' . $object->getParent()->getInternalName() . ')',
		];
	}

}
