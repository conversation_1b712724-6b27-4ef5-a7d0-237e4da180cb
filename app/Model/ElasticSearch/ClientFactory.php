<?php
namespace App\Model\ElasticSearch;


use Composer\CaBundle\CaBundle;
use Elasticsearch\Client;
use Elasticsearch\ClientBuilder;

final class ClientFactory
{
	public static function create(string $host = 'localhost', int $port = 9700): Client
	{
		error_reporting(E_ALL & ~E_DEPRECATED & ~E_USER_DEPRECATED);
		$clientBuilder = ClientBuilder::create();
		$clientBuilder->setHosts([$host . ':' . $port]);

		$client = $clientBuilder->build();
//		error_reporting(E_ALL);
		return $client;
	}
}
