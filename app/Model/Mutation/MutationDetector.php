<?php

declare(strict_types=1);

namespace App\Model\Mutation;

use App\Model\ConfigService;
use App\Model\Orm\Mutation\Mutation;
use Nette\Http\Request;

final class MutationDetector
{

	public function __construct(
		private Request $request,
		private ConfigService $configService,
		private MutationsHolder $mutationsHolder,
	) {}

	public function detect(): Mutation
	{
		$host = $this->request->getUrl()->host;

		$hostWithoutWWW = str_replace('www.', '', $host);
		return $this->detectByDomainAndPrefix($hostWithoutWWW)
			?? $this->detectByDomain($hostWithoutWWW)
			?? $this->getMain();
	}

	private function detectByDomain(string $hostWithoutWWW): ?Mutation
	{
		foreach ($this->mutationsHolder->findAll() as $mutation) {
			if ($mutation->getRealDomainWithoutWWW() == $hostWithoutWWW) {
				return $mutation;
			}
		}
		return null;
	}

	private function detectByDomainAndPrefix(string $hostWithoutWWW): ?Mutation
	{
		$path = $this->request->getUrl()->path;
		$pathParts = explode('/', $path);
		$possibleUrlPrefix = $pathParts[1];

		if ($possibleUrlPrefix) {
			foreach ($this->mutationsHolder->findAll() as $mutation) {
				if ($mutation->getRealDomainWithoutWWW() == $hostWithoutWWW
					&& $possibleUrlPrefix == $mutation->getRealUrlPrefix()) {
					return $mutation;
				}
			}
		}
		return null;
	}

	private function getMain(): Mutation
	{
		/** @var array<string, mixed> $muttationSetup */
		$muttationSetup = $this->configService->get('mutations');
		$firstLangCode = array_keys($muttationSetup)[0];
		return $this->mutationsHolder->getMutationByLangCode($firstLangCode);
	}
}
