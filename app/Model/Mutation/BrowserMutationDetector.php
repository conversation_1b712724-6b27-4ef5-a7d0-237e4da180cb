<?php declare(strict_types = 1);

namespace App\Model\Mutation;

use App\Model\Orm\Mutation\Mutation;
use App\PostType\Core\Model\LocalizationEntity;
use DateInterval;
use DateTimeImmutable;
use Nette\Http\Request;
use Nette\Http\Response;

class BrowserMutationDetector
{

	public const COOKIE_KEY = 'language_iso_prefix_detected';

	public function __construct(
		private MutationsHolder $mutationsHolder,
		private Request $request,
		private Response $response,
	)
	{
	}

	public function detect(LocalizationEntity $object, Mutation $currentMutation): ?LocalizationEntity
	{
		$futureMutation = $this->getMutationFromCookie();

		if ($futureMutation === null) {
			$futureMutation = $this->getMutationFromBrowser();
		}

		if ($currentMutation->id !== $futureMutation->id) {
			$this->setCookie($futureMutation);
			$sibling = $object->getParent()->getLocalization($futureMutation);
			assert($sibling instanceof LocalizationEntity);
			return $sibling;
		}

		return null;
	}


	private function getMutationFromBrowser(): Mutation
	{
		$acceptMutationsIsoCodes = $this->mutationsHolder->getMutationByLangIsoCode();

		$acceptIsoCodes = array_map(function (Mutation $mutation) {
			return $mutation->isoCodePrefix;
		}, $acceptMutationsIsoCodes);

		$lang = $this->request->detectLanguage($acceptIsoCodes);
		$acceptMutations = $this->mutationsHolder->findAll();

		return (isset($acceptMutations[$lang])) ? $acceptMutations[$lang] : $this->mutationsHolder->getDefault();
	}


	public function setCookie(Mutation $futureMutation): void
	{
		$now = new DateTimeImmutable();
		$nowPlus1Year = $now->add(DateInterval::createFromDateString('1 year'));
		$this->response->setCookie(self::COOKIE_KEY, $futureMutation->langCode, $nowPlus1Year);
	}

	private function getMutationFromCookie(): ?Mutation
	{
		$langCode = $this->request->getCookie(self::COOKIE_KEY);

		if ($langCode === null) {
			return null;
		}

		$acceptMutations = $this->mutationsHolder->findAll();
		return (isset($acceptMutations[$langCode])) ? $acceptMutations[$langCode] : null;
	}

}
