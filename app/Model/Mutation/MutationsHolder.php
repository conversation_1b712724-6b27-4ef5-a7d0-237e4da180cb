<?php declare(strict_types = 1);

namespace App\Model\Mutation;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use LogicException;
use Nextras\Orm\Exception\NoResultException;

final class MutationsHolder
{

	private Orm $orm;

	private array $cache = [];

	public function __construct(Orm $orm)
	{
		$this->orm = $orm;
	}

	private function init(): void
	{
		if (!isset($this->cache['mutationsByRootId']) || ! current($this->cache['mutationsByRootId'])->isAttached()) {
			$mutation = $this->orm->mutation->findAll();
			foreach ($mutation as $item) {
				$this->cache['mutationsByRootId'][$item->rootId] = $item;
				$this->cache['mutationsById'][$item->id] = $item;
				if (!isset($this->cache['mutationsByLangIsoCode'][$item->isoCodePrefix])) {
					$this->cache['mutationsByLangIsoCode'][$item->isoCodePrefix] = $item;
				}

				$this->cache['mutationsByLangCode'][$item->langCode] = $item;

				if ($item->public === 1) {
					$this->cache['mutationsByLangCodePublic'][$item->langCode] = $item;
					if (!isset($this->cache['mutationsByLangIsoCodePublic'][$item->isoCodePrefix])) {
						$this->cache['mutationsByLangIsoCodePublic'][$item->isoCodePrefix] = $item;
					}

					$this->cache['mutationsByRootIdPublic'][$item->rootId] = $item;
				}
			}
		}
	}

	public function flush(): void
	{
		$this->cache = [];
	}

	/**
	 * @return Mutation[]
	 */
	public function findAll(bool $publicOnly = true): array
	{
		$this->init();
		if ($publicOnly) {
			if (isset($this->cache['mutationsByLangCodePublic'])) {
				return $this->cache['mutationsByLangCodePublic'];
			}
		} else {
			if (isset($this->cache['mutationsByLangCode'])) {
				return $this->cache['mutationsByLangCode'];
			}
		}

		return [];
	}

	public function getMutationByRootId(int $rootId): Mutation
	{
		$this->init();
		if (isset($this->cache['mutationsByRootId'][$rootId])) {
			return $this->cache['mutationsByRootId'][$rootId];
		}

		throw new LogicException("Missing mutation definition for rootId '" . $rootId . "'");
	}

	public function getMutationByLangCode(string $lang): Mutation
	{
		$this->init();
		if (isset($this->cache['mutationsByLangCode'][$lang])) {
			return $this->cache['mutationsByLangCode'][$lang];
		}

		throw new LogicException("Missing mutation definition for lang '" . $lang . "'");
	}
	public function getMutationById(int $id): Mutation
	{
		$this->init();
		if (isset($this->cache['mutationsById'][$id])) {
			return $this->cache['mutationsById'][$id];
		}

		throw new LogicException("Missing mutation definition for id '" . $id . "'");
	}

	public function getDefault(): Mutation
	{
		return $this->getMutationByLangCode(Mutation::DEFAULT_CODE);
	}


	public function getDefaultRs(): Mutation
	{
		return $this->getMutationByLangCode(Mutation::DEFAULT_RS_CODE);
	}


	/**
	 * @return Mutation[]
	 */
	public function getMutationByLangCodes(array $langCodes): array
	{
		$this->init();

		$list = [];
		foreach ($langCodes as $langCode) {
			if (isset($this->cache['mutationsByLangCode'][$langCode])) {
				$list[$langCode] = $this->cache['mutationsByLangCode'][$langCode];
			} else {
				throw new LogicException("Missing mutation definition for lang '" . $langCode . "'");
			}
		}

		return $list;
	}


	/**
	 * @return Mutation[]
	 */
	public function getMutationByLangIsoCode(bool $publicOnly = true): array
	{
		$this->init();
		if ($publicOnly) {
			if (isset($this->cache['mutationsByLangIsoCodePublic'])) {
				return $this->cache['mutationsByLangIsoCodePublic'];
			}
		} else {
			if (isset($this->cache['mutationsByLangIsoCode'])) {
				return $this->cache['mutationsByLangIsoCode'];
			}
		}

		return [];
	}


	/**
	 * @throws NoResultException
	 */
	public function getByIdChecked(int $mutationId): Mutation
	{
		if (!isset($this->cache['mutationsById'])) {
			$this->cache['mutationsById'][$mutationId] = $this->orm->mutation->getByIdChecked($mutationId);
		}

		return $this->cache['mutationsById'][$mutationId];
	}

	/**
	 * @return array<int, Mutation>
	 */
	public function findAllById(): array
	{
		$this->init();
		return $this->cache['mutationsById'];
	}

}
