<?php declare(strict_types = 1);

namespace App\Model\Link;

use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\Tree;

class LinkSeo
{

	private LinkChecker $linkChecker;

	public function __construct(LinkChecker $linkChecker)
	{
		$this->linkChecker = $linkChecker;
	}


	public function hasNofollow(Tree $tree, array $params): bool
	{
		$ret = false;
		if (isset($params['filter'])) {
			if ($tree instanceof CatalogTree) {
				$ret = !$this->linkChecker->isCatalogTreeIndexable($tree, $params['filter']);
			} else {
				if ($tree->uid === 'search') {
					$ret = false;
				}
			}
		}

		return $ret;
	}

}
