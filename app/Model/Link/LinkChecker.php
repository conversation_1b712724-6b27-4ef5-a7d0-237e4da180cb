<?php declare(strict_types = 1);

namespace App\Model\Link;

use App\PostType\Page\Model\Orm\CatalogTree;

class LinkChecker
{

	public function isCatalogTreeIndexable(CatalogTree $object, array $filterParams): bool
	{
		if (isset($filterParams['flags'])) {
			return false;
		}

		if (isset($filterParams['flagValues'])) {
			return false;
		}

		if (isset($filterParams['ranges'])) {
			return false;
		}

		if (isset($filterParams['dials'])) {

			$pickedParameterCounter = 0;
			foreach ($filterParams['dials'] as $dialName => $itemValues) {
				if (!$object->isImportantParameter($dialName)) {
					return false;
				}

				if (count($itemValues) === 1) {
					$pickedParameterCounter++;
				} elseif (count($itemValues) > 1) {
					// only one parameter per parameter
					return false;
				}
			}

			if ($pickedParameterCounter > 4) {
				return false;
			}
		}

		return true;
	}

}
