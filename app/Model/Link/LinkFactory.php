<?php declare(strict_types = 1);

namespace App\Model\Link;

use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Routable;
use App\Model\Orm\RoutableEntity;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalization;
use Nette;

final class LinkFactory
{

	use Nette\SmartObject;

	public const BAD_LINK = ['Homepage:default', []];
	public const ABSOLUTE_LINK = 'absolute';

	public function __construct(
		private readonly Nette\Application\LinkGenerator $linkGenerator,
		private readonly MutationHolder $mutationHolder,
		private readonly Orm $orm,
	)
	{
	}


	public function linkInPresenter(Routable $input, array $args = []): array
	{
		$mutation = $args['mutation'] ?? $this->mutationHolder->getMutation();

		return $this->translate($input, $args, $mutation);
	}


	/**
	 * @throws Nette\Application\UI\InvalidLinkException
	 */
	public function linkTranslateToNette(Routable $input, array $args = [], bool $withDomainUrl = true): string
	{
		$mutation = $args['mutation'] ?? $this->mutationHolder->getMutation();

		[$destination, $args] = $this->translate($input, $args, $mutation);

		if (!$withDomainUrl) {
			return $this->linkGenerator->link(ltrim($destination, ':'), $args, mode: 'link');
		}

		$linkGeneratorMutation = $this->linkGenerator->withReferenceUrl('https://' . $mutation->domain);
		return $linkGeneratorMutation->link(ltrim($destination, ':'), $args);
	}


	protected function translate(Routable $input, array $args, Mutation $mutation): array
	{
		if (!isset($args['host'])) {
			$args['host'] = $mutation->getRealDomain();
		}

		if (!isset($args['urlPrefix'])) {
			$args['urlPrefix'] = $mutation->getRealUrlPrefix();
		}

		if ($input instanceof CommonTree) {
			$object = $input;
		} elseif ($input instanceof RoutableEntity) {
			$object = $input;
		} else {
			trigger_error(sprintf('BAD link - unsupported type of input object %s.', get_class($input)), E_USER_NOTICE);
			return self::BAD_LINK;
		}

		if (!$object->isPersisted()) {
			 // missing link from Pages is replaced with empty CommonTree to prevent error
			return [
				'this#',
				$args,
			];

		} elseif ($object instanceof Tree && $object->uid === 'userLogout') { // vyjimka pro odhlaseni = volani signalu na aktualni strance
			$args['do'] = 'logout';
			return [
				'this',
				$args,
			];
		} elseif ($object instanceof SeoLinkLocalization) {
			$args['object'] = $this->orm->tree->getByUid('eshop', $mutation);
			$destination = $args['object']->template;

			$args['seoLink'] = $object;
			$args['alias'] = $object->alias?->alias;

			return [
				$destination,
				$args,
			];

		} else {
			$args['object'] = $object;
			$destination = $object->template;

			if (isset($args[self::ABSOLUTE_LINK]) && $args[self::ABSOLUTE_LINK]) {
				unset($args[self::ABSOLUTE_LINK]);
				$destination = '//' . $destination;
			}

			return [
				$destination,
				$args,
			];
		}
	}

}
