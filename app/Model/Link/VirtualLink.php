<?php declare(strict_types=1);

namespace App\Model\Link;

use App\Model\CustomField\LazyValue;
use App\PostType\Page\Model\Orm\Tree;

class VirtualLink
{

	public function __construct(
		public readonly ?Tree $page,
		public readonly ?string $href,
		public readonly ?string $hrefName,
	)
	{
	}

	public static function fromTreeEntity(Tree $treeEntity): ?VirtualLink
	{
		if (!isset($treeEntity->cf->virtual_category->toggle)) {
			return null;
		}

		$hrefName = null;
		$page = null;
		$href = null;

		if ($treeEntity->cf->virtual_category->toggle === 'systemHref') {
			if (isset($treeEntity->cf->virtual_category->systemHref->page) && $treeEntity->cf->virtual_category->systemHref->page instanceof LazyValue && $treeEntity->cf->virtual_category->systemHref->page->getEntity()) {
				$page = $treeEntity->cf->virtual_category->systemHref->page->getEntity();
				assert($page instanceof Tree);
				$hrefName = $treeEntity->cf->virtual_category->systemHref->hrefName ?? $page->nameAnchor;
			}

		} else {
			if (isset($treeEntity->cf->virtual_category->customHref->href) && $treeEntity->cf->virtual_category->customHref->href !== '') {
				$href = $treeEntity->cf->virtual_category->customHref->href;
				$hrefName = $treeEntity->cf->virtual_category->customHref->hrefName ?? $href;
			}

		}

		if ($href === null && $page === null) {
			return null;
		}

		return new VirtualLink($page, $href, $hrefName);
	}

}
