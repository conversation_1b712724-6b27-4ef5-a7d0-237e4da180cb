<?php declare(strict_types = 1);

namespace App\Model\Currency;

use App\Exceptions\CurrencyException;
use Brick\Math\RoundingMode;
use Brick\Money\Currency;

final class CurrencyHelper
{

	private static ?CookieStorage $currencyStorage = null;

	private static string|null $defaultCurrencyCode = null;

	public const CURRENCY_CZK = 'CZK';

	public const CURRENCY_EUR = 'EUR';

	public const CURRENCIES = [
		self::CURRENCY_CZK,
		self::CURRENCY_EUR,
	];

	public const CURRENCIES_SELECT = [
		self::CURRENCY_CZK => 'Česká koruna CZK',
		self::CURRENCY_EUR => 'Euro EUR',
	];

	public const CURRENCY_SETTINGS = [
		self::CURRENCY_CZK => [
			'precision' => 0,
			'rounding' => RoundingMode::HALF_EVEN,
		],
		self::CURRENCY_EUR => [
			'precision' => 2,
			'rounding' => RoundingMode::HALF_EVEN,
		],
	];

	final public static function getCurrencyCode(): string
	{
		//$currencyCode = self::getCookieStorage()->get();
		//if ($currencyCode === null) {
			return self::getDefaultCurrencyCode();
		//}

		//return $currencyCode;
	}

	final public static function getCurrency(): Currency
	{
		return Currency::of(self::getCurrencyCode());
	}

	final public static function setCurrencyCode(string $currencyCode): void
	{
		self::validateCurrencyCode($currencyCode);
		self::getCookieStorage()->set($currencyCode);
	}

	private static function getDefaultCurrencyCode(): string
	{
		if (self::$defaultCurrencyCode === null) {
			throw new CurrencyException();
		}

		return self::$defaultCurrencyCode;
	}

	final public static function setDefaultCurrencyCode(string|null $currencyCode): void
	{
		self::validateCurrencyCode($currencyCode);

		self::$defaultCurrencyCode = $currencyCode;
	}

	private static function validateCurrencyCode(string|null $currencyCode): void
	{
		if ($currencyCode === null) {
			throw new CurrencyException(CurrencyException::CURRENCY_CANT_BE_NULL);
		}

		if (in_array($currencyCode, self::CURRENCIES, true)) {
			return;
		}

		throw new CurrencyException(CurrencyException::CURRENCY_INVALID . ' Code: ' . $currencyCode);
	}

	private static function getCookieStorage(): CookieStorage
	{
		if (self::$currencyStorage === null) {
			throw new CurrencyException(CurrencyException::CURRENCY_SESSION_NOT_SET);
		}

		return self::$currencyStorage;
	}

	final public static function setCookieStorage(CookieStorage $cookieStorage): void
	{
		self::$currencyStorage = $cookieStorage;
	}

}
