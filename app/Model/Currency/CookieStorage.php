<?php declare(strict_types=1);

namespace App\Model\Currency;

use Nette\Http\Request;
use Nette\Http\Response;

class CookieStorage
{

	private ?string $currencyCode = null;

	private const COOKIE_EXPIRE = '+1 month';
	public const COOKIE_NAME = 'currencyMetadata';

	public function __construct(
		private readonly Request $httpRequest,
		private readonly Response $httpResponse,
	)
	{
	}

	public function get(): ?string
	{
		if ($this->currencyCode === null) {
			$this->currencyCode = $this->loadCookie();
		}
		return $this->currencyCode;
	}

	public function set(string $currencyCode): void
	{
		$this->currencyCode = $currencyCode;
		$this->save();
	}


	private function loadCookie(): ?string
	{
		return $this->httpRequest->getCookie(self::COOKIE_NAME);
	}


	private function save(): void
	{
		$this->httpResponse->setCookie(self::COOKIE_NAME, $this->currencyCode, self::COOKIE_EXPIRE);
	}

}
