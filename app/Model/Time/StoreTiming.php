<?php declare(strict_types=1);

namespace App\Model\Time;

use App\Model\Orm\Product\Product;
use App\Model\Orm\Traits\HasStaticCache;
use Nextras\Dbal\Utils\DateTimeImmutable;

class StoreTiming
{

	use HasStaticCache;

	private ?array $storeOpeningHours = null;

	public function __construct()
	{
	}

	public function getFutureClosingTime(Product $product, DateTimeImmutable $date): ?DateTimeImmutable
	{
		$closingTime = $this->getStoreClosingTimeMetadata($product);
		$dayInWeek   = (int) $date->format('N') - 1;  // 0 is monday

		$passedDays = array_slice($closingTime, 0, $dayInWeek);
		$futureDays = array_slice($closingTime, $dayInWeek);

		$addDays          = 0;
		foreach (array_merge($futureDays, $passedDays) as $storeOpeningHours) {
			$storeClosingHour = $storeOpeningHours['to'] ?? null;
			if ($storeClosingHour !== null) {
				$storeClosingHour = TimeOperation::normalizeTimeInString($storeClosingHour);

				[$hours, $minutes] = explode(':', $storeClosingHour);

				$storeClosingDateTime = $date->modify('+' . $addDays . ' days')->setTime((int) $hours, (int) $minutes, 0);

				if ($date < $storeClosingDateTime) {
					return $storeClosingDateTime;
				}
			}
			$addDays++;
		}

		return null;
	}


	private function getStoreClosingTimeMetadata(Product $product): array
	{
		if ($this->storeOpeningHours === null) {

			$this->storeOpeningHours = $this->tryLoadCache('storeOpeningHours', function () use ($product) {
				$storeOpeningHours = [];
				$contact = $product->getMutation()->pages->contact; //$this->treeRepository->getByUid('contact', $product->getMutation());

				$openingHours = $contact->customFieldsJson->openning_hours_dataset[0] ?? [];

				foreach ((array) $openingHours as $dayIndex => $row) {
					$storeOpeningHours[$dayIndex] = (array) $row[0];
				}

				$isNull = true;
				for ($i = 0; $i < 7; $i++) {
					if ( ! isset($storeOpeningHours[$i])) {
						$storeOpeningHours[$i] = null;
					} else {
						$isNull = false;
					}
				}

				if ($isNull) {
					return [];
				} else {
					ksort($storeOpeningHours);
					return $storeOpeningHours;
				}
			});
		}
		return $this->storeOpeningHours;
	}

}
