<?php declare(strict_types=1);

namespace App\Model\Time;

use App\Model\Orm\DeliveryMethod\DeliveryMethodConfiguration;
use App\Model\Orm\ProductVariant\Availability\CustomProductAvailability;
use App\Model\Orm\Stock\Stock;
use App\Model\Setup;

class DeliveryTiming
{

	public function getCrucialTimeForBestDelivery(CustomProductAvailability $availability, Setup $setup): string
	{
		$quantityRequired = 1;
		$deliveryMethodConfiguration = $availability->getBestDeliveryMethod($setup->mutation, $setup->state, $setup->priceLevel, $setup->mutation->getSelectedCurrency());
		return $this->getCrucialTimeForDelivery($availability, $quantityRequired, $deliveryMethodConfiguration);
	}


	public function getCrucialTimeForDelivery(CustomProductAvailability $availability, int $quantityRequired, ?DeliveryMethodConfiguration $deliveryMethodConfiguration): string
	{
		if ($availability->getShopSupply() !== null && $quantityRequired <= $availability->getShopSupply()->amount) {
			$crucialTime = $deliveryMethodConfiguration->deliveryHourByStock->{Stock::ALIAS_SHOP} ?? '18:00';
		} else {
			$crucialTime = $deliveryMethodConfiguration->deliveryHourByStock->{Stock::ALIAS_SUPPLIER_STORE} ?? '18:00';
		}
		$crucialTime = TimeOperation::normalizeTimeInString($crucialTime);

		return $crucialTime;
	}

}
