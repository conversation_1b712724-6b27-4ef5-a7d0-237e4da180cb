<?php declare(strict_types=1);

namespace App\Model\Time;

class TimeOperation
{

	public function getDiffInSeconds(\DateTimeImmutable $timeA, \DateTimeImmutable $timeB): int
	{
		$interval = $timeA->diff($timeB);
		return ($interval->days * 24 * 60 * 60) +
			($interval->h * 60 * 60) +
			($interval->i * 60) +
			$interval->s;
	}


	public static function normalizeTimeInString(string $time): string
	{
		if (!str_contains($time, ':')) {
			$time = trim($time);
			$time .= ':00';
		}

		return $time;
	}

}
