<?php declare(strict_types=1);

namespace App\Model\Time;

use Nette\Http\Request;
use Nextras\Dbal\Utils\DateTimeImmutable;

class CurrentDateTimeProvider
{

	public function __construct(
		private readonly Request $httpRequest,
	)
	{
	}

	public function getCurrentDateTime(): DateTimeImmutable
	{
		$now = new DateTimeImmutable();
		if (($forceDate = $this->httpRequest->getQuery('forceDate')) !== null) {
			try {
				$now = new DateTimeImmutable($forceDate);
			} catch (\Throwable) {
				$now = new DateTimeImmutable();
			}
		}
		return $now;
	}

	public function forcedDateTimeIsUsed(): bool
	{
		return $this->httpRequest->getQuery('forceDate') !== null;
	}

}
