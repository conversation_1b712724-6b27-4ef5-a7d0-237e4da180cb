<?php

declare(strict_types=1);

namespace App\Model\Security;

use Nette;

final class User extends Nette\Security\User
{

	private ?array $allowedRoles = null;

	public function isDeveloper(): bool
	{
		return $this->isInRole(Acl::ROLE_DEVELOPER);
	}

	public function isAdmin(): bool
	{
		return $this->isInRole(Acl::ROLE_ADMIN);
	}

	public function isUser(): bool
	{
		return $this->isInRole(Acl::ROLE_USER);
	}

	public function isLector(): bool
	{
		return $this->isInRole(Acl::ROLE_LECTOR);
	}

	/**
	 * Vrati seznam roli, na ktere ma dany Admin user pravo
	 */
	public function getAllowedRoles(): array
	{
		if (is_null($this->allowedRoles)) {
			$authorizator = $this->getAuthorizator();
			\assert($authorizator instanceof Nette\Security\Permission);

			$roles = $authorizator->getRoles();

			$roles = array_combine($roles, $roles);
			unset($roles[Acl::ROLE_GUEST]);

			switch ($this->getRole()) {
				case Acl::ROLE_DEVELOPER:
					break;

				case Acl::ROLE_ADMIN:
					unset($roles[Acl::ROLE_DEVELOPER]);
					break;

				default:
					$roles = [Acl::ROLE_USER => Acl::ROLE_USER];
					break;
			}

			$this->allowedRoles = $roles;
		}
		return $this->allowedRoles;
	}

	public function getRole(): string
	{
		return $this->getRoles()[0];
	}
}
