<?php declare(strict_types = 1);

namespace App\Model\Security;

use App\Model\Orm\Orm;
use Nette;
use Nette\Security\Passwords;
use Nette\Security\SimpleIdentity;
use App\Model\Orm\User\UserProvider;

final class Authenticator implements Nette\Security\Authenticator
{

	use Nette\SmartObject;

	public function __construct(
		private readonly Orm $orm,
		private readonly Passwords $passwords,
	)
	{
	}

	/**
	 * Performs an authentication.
	 *
	 * @throws Nette\Security\AuthenticationException
	 */
	public function authenticate(string $user, string $password): SimpleIdentity
	{

		$mutation = $this->orm->hasMutation() ? $this->orm->getMutation() : $this->orm->mutation->getDefault();

		$userEntity = $this->orm->user->getByEmail($user, $mutation);

		if (!isset($userEntity)) {
			throw new Nette\Security\AuthenticationException('msg_error_valid_username', self::IdentityNotFound);
		}

		/*if ($userEntity->oldPasswordHash !== null && $userEntity->oldPasswordSalt !== null) {
			$passwordHash = sha1($password . $this->getSalt($userEntity->oldPasswordSalt));

			if ($passwordHash !== $userEntity->oldPasswordHash) {
				throw new Nette\Security\AuthenticationException('msg_error_valid_username', self::InvalidCredential);
			}

			$userEntity->oldPasswordHash = null;
			$userEntity->oldPasswordSalt = null;

			$userEntity->password = $this->passwords->hash($password);

			$this->orm->user->persistAndFlush($userEntity);

		}*/

		if ($userEntity->password === null) {
			throw new Nette\Security\AuthenticationException('msg_error_valid_username', self::InvalidCredential);
		}

		if (!$this->passwords->verify($password, $userEntity->password)) {
			throw new Nette\Security\AuthenticationException('msg_error_valid_username', self::InvalidCredential);
		}

		if ($userEntity->role === 'user' && !$userEntity->isActive) {
			throw new Nette\Security\AuthenticationException('msg_error_auth_user_is_not_active', self::InvalidCredential);
		}

		return new SimpleIdentity($userEntity->id, $userEntity->role);
	}


	/*private function getSalt(string $saltPart): string
	{
		$salt = '';

		//secret
		//IMPORTANT: do not change!!!
		$chars = array(0, 7, 4, 9, 14, 19, 27, 27, 4, 3);
		foreach ($chars as $key) {
			$salt .= substr($saltPart, $key, 1);
		}
		return $salt;
	}*/

}
