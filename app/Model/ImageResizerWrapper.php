<?php declare(strict_types = 1);

namespace App\Model;

use App\Model\Image\ImageObject;
use App\Model\Image\ImageObjectFactory;
use App\Model\Orm\ImageEntity;

final class ImageResizerWrapper
{

	public function __construct(
		private readonly ImageObjectFactory $imageObjectFactory,
	)
	{}

	public function getResizedImage(object $object, string $sizeName): ?ImageObject
	{
		if ($object instanceof ImageEntity) {
			return $this->imageObjectFactory->getByName($object->filename, $sizeName, $object->getTimestamp());
		}

		if (isset($object->firstImage) && $object->firstImage instanceof ImageEntity) {
			return $this->imageObjectFactory->getByName($object->firstImage->filename, $sizeName, $object->firstImage->getTimestamp());
		}

		return null;
	}

}
