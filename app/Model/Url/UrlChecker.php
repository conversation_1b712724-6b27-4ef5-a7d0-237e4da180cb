<?php declare(strict_types = 1);

namespace App\Model\Url;

use App\Model\ConfigService;
use Composer\CaBundle\CaBundle;
use GuzzleHttp\Client;

final class UrlChecker
{

	public function __construct(
		private readonly ConfigService $configService,
	)
	{
	}


	public function exist(string $url): bool
	{
		$client = new Client([
			'verify' => $this->configService->isEnvLocal() ? false : CaBundle::getSystemCaRootBundlePath(),
			//'debug' => true,
		]);

		$result = $client->get($url);
		return $result->getStatusCode() === 200;
	}

}
