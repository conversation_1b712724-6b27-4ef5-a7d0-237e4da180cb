<?php

declare(strict_types=1);

namespace App\Model\CustomField;

/**
 * @implements \ArrayAccess<string, SuggestUrl>
 */
final class SuggestUrls implements \ArrayAccess
{
	/**
	 * @param array<string, SuggestUrl> $urls
	 */
	public function __construct(
		private array $urls,
	) {}

	public function offsetExists(mixed $offset): bool
	{
		return \array_key_exists($offset, $this->urls);
	}

	public function offsetGet(mixed $offset): SuggestUrl
	{
		return $this->urls[$offset] ?? throw new \RuntimeException("SuggestUrl with key '$offset' not found.");
	}

	public function offsetSet(mixed $offset, mixed $value): void
	{
		throw new \LogicException('SuggestUrls is immutable.');
	}

	public function offsetUnset(mixed $offset): void
	{
		throw new \LogicException('SuggestUrls is immutable.');
	}
}
