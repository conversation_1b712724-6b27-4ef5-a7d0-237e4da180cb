<?php declare(strict_types = 1);

namespace App\Model\CustomField;

use App\Model\Orm\Alias\Alias;
use App\Model\Orm\Alias\AliasRepository;
use App\Model\Orm\CollectionById;
use App\Model\Orm\LibraryImage\LibraryImage;
use App\Model\Orm\LibraryImage\LibraryImageRepository;
use App\Model\Orm\Orm;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\Parameter\ParameterRepository;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\ParameterValue\ParameterValueRepository;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductRepository;
use App\Model\Orm\State\State;
use App\Model\Orm\State\StateRepository;
use App\Model\Orm\Usp\Usp;
use App\Model\Orm\Usp\UspRepository;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Page\Model\Orm\TreeAlternative;
use App\PostType\Page\Model\Orm\TreeAlternativeRepository;
use App\PostType\Page\Model\Orm\TreeRepository;
use App\PostType\Author\Model\Orm\Author;
use App\PostType\Author\Model\Orm\AuthorRepository;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTag;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagRepository;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use LogicException;
use Nextras\Orm\Collection\EmptyCollection;
use stdClass;
use function count;
use function current;
use function explode;

final class CustomFieldDefinition extends stdClass
{

	/**
	 * @param array<string, mixed> $definition
	 */
	public function __construct(array $definition)
	{
		foreach ($definition as $key => $value) {
			$this->$key = $value;
		}
	}

	public static function extend(
		CustomField|CustomFieldDefinition $parent,
		array $localDefinition,
	): self
	{
		if ($parent instanceof CustomField) {
			$parent = $parent->definition;
		}

		$self = clone $parent;
		foreach ($localDefinition as $key => $value) {
			$self->$key = $value;
		}

		return $self;
	}

	public function process(
		mixed $value,
		bool $inRs,
		Orm $orm,
		LazyValueFactory $lazyValueFactory,
	): mixed
	{
		$type = $this->type;
		return match ($type) {
			'list' => $this->processList($value, $inRs, $orm, $lazyValueFactory),
			'group' => $this->processGroup($value, $inRs, $orm, $lazyValueFactory),
			'suggest' => $this->processSuggest($value, $inRs, $orm, $lazyValueFactory),
			'image' => $this->processImage($value, $inRs, $orm, $lazyValueFactory),
			'file' => $this->processFile($value, $inRs, $orm, $lazyValueFactory),
			'select' => $this->processSelect($value, $inRs),
			'dateTime' => $this->processDateTime($value, $inRs),
			default => $value,
		};
	}

	private function processList(
		mixed $value,
		bool $inRs,
		Orm $orm,
		LazyValueFactory $lazyValueFactory,
	): mixed
	{
		if ( ! $inRs
			&& isset($this->items)
			&& count($this->items) === 1
			&& current($this->items)->type === 'suggest'
		) {
			$itemDefinition = current($this->items);
			$repository = $this->getRepositoryBySubType($itemDefinition->subType ?? '', $orm);

			$tmpIds = [];
			foreach ($value as $row) {
				$tmpIds[] = (int) current(is_array($row) ? $row : get_mangled_object_vars($row));
			}

			$tmpIds = array_filter($tmpIds, function ($item) {
				return $item !== '';  // @phpstan-ignore-line
			});

			if ($repository instanceof CollectionById) {
				$collection = $repository->findByIdOrder($tmpIds);
				if (method_exists($repository, 'getPublicOnlyWhere')) {
					$collection->findBy($repository->getPublicOnlyWhere());
				}

				return $collection;
			} else {
				throw new LogicException(sprintf('Add %s interface for class %s', 'CollectionById', get_class($repository)));
			}
		}

		$processedItems = [];
		foreach ($value as $item) {
			$processedItem = $this->processListItem($item, $inRs, $orm, $lazyValueFactory);
			if ($processedItem === new stdClass()) {
				continue;
			}

			$processedItems[] = $processedItem;
		}

		return $processedItems;
	}

	private function processGroup(
		mixed $value,
		bool $inRs,
		Orm $orm,
		LazyValueFactory $lazyValueFactory,
	): mixed
	{
		foreach ($value as $group) {
			$processedItem = $this->processListItem($group, $inRs, $orm, $lazyValueFactory);
			if ($processedItem === new stdClass()) {
				continue;
			}

			if ($inRs) {
				// only one item from items in RS mode
				return [$processedItem];
			} else {
				// return object instead of list with one value
				return $processedItem;
			}
		}

		return [];
	}

	private function processListItem(
		mixed $item,
		bool $inRs,
		Orm $orm,
		LazyValueFactory $lazyValueFactory,
	): mixed
	{
		$processed = new stdClass();

		foreach ($item as $key => $value) {
			if ( ! isset($this->items[$key])) {
				continue;
			}

			$definition = $this->items[$key];
			$processed->$key = $definition->process($value, $inRs, $orm, $lazyValueFactory);
		}

		return $processed;
	}

	private function processSuggest(
		mixed $value,
		bool $inRs,
		Orm $orm,
		LazyValueFactory $lazyValueFactory,
	): mixed
	{
		$repository = $this->getRepositoryBySubType($this->subType ?? '', $orm);

		$id = (int) $value;
		if ( ! $id) {
			return null;
		}

		if ( ! $inRs) {
			return $lazyValueFactory->create($repository, $id);
		}

		$entity = $repository->getById($id);
		if ($entity === null) {
			return null;
		}

		return [
			'id' => $id,
			'value' => $this->getValueByClass($entity),
		];
	}

	private function getRepositoryBySubType(
		string $subType,
		Orm $orm,
	): LibraryImageRepository|AliasRepository|TreeRepository|ProductRepository|ParameterRepository|ParameterValueRepository|AuthorRepository|BlogTagRepository|StateRepository|TreeAlternativeRepository|UspRepository
	{
		return match ($subType) {
			'image' => $orm->libraryImage,
			'alias' => $orm->alias,
			'tree' => $orm->tree,
			'author' => $orm->author,
			'tag' => $orm->blogTag,
			'product' => $orm->product,
			'parameter' => $orm->parameter,
			'parameterValue' => $orm->parameterValue,
			'state' => $orm->state,
			'treeAlternative' => $orm->treeAlternative,
			'usp' => $orm->usp,
			default => throw new LogicException('Missing \'subType\' in suggest definition'),
		};
	}

	private function processImage(
		mixed $value,
		bool $inRs,
		Orm $orm,
		LazyValueFactory $lazyValueFactory,
	): mixed
	{
		$repository = $orm->libraryImage;
		$multiple = $this->multiple ?? false;

		if ( ! $multiple) {
			$id = (int) $value;
			if ( ! $id) {
				return null;
			}

			if ( ! $inRs) {
				return $lazyValueFactory->create($repository, $id);
			}

			$image = $repository->getById($id);
			if ($image === null) {
				return null;
			}

			return [['id' => $id, 'src' => $image->getSize('s')->src]];
		}

		if ( ! $inRs) {
			if (count((array) $value) === 0) {
				return new EmptyCollection();
			}
			return $repository->findByIdOrder((array) $value);
		}

		$images = [];
		foreach ($value as $id) {
			$image = $repository->getById((int) $id);
			if ($image === null) {
				continue;
			}

			$images[] = ['id' => $id, 'src' => $image->getSize('s')->src];
		}

		return $images;
	}

	private function processFile(
		mixed $value,
		bool $inRs,
		Orm $orm,
		LazyValueFactory $lazyValueFactory,
	): mixed
	{
		$repository = $orm->file;
		$multiple = $this->multiple ?? false;

		if ( ! $multiple) {
			/** @var stdClass $value */
			if ( ! isset($value->id)) {
				return null;
			}

			$id = (int) $value->id;
			if ( ! $inRs) {
				$lazyValue = $lazyValueFactory->create($repository, $id);
				$lazyValue->cName = $value->name;
				return $lazyValue;
			}

			$entity = $repository->getById($id);
			if ($entity === null) {
				return null;
			}

			if ($value->name !== $entity->name) {
				$entity->name = $value->name;
				$orm->persistAndFlush($entity);
			}

			return [
				[
					'id' => $id,
					'name' => $entity->name,
					'size' => $entity->sizeMB,
					'type' => $entity->type,
				],
			];
		}

		if ( ! $inRs) {
			$ids = [];
			foreach ($value as $file) {
				$ids[] = $file->id;
			}

			return $repository->findByIdOrder($ids);
		}

		$files = [];
		/** @var stdClass[] $value */
		foreach ($value as $file) {
			if ( ! isset($file->id)) {
				continue;
			}

			$id = (int) $file->id;
			$entity = $repository->getById($id);
			if ($entity === null) {
				continue;
			}

			if ($file->name !== $entity->name) {
				$entity->name = $file->name;
				$orm->persistAndFlush($entity);
			}

			$files[] = [
				'id' => $entity->id,
				'name' => $entity->name,
				'size' => $entity->sizeMB,
				'type' => $entity->type,
			];
		}

		return $files;
	}

	private function processSelect(
		mixed $value,
		bool $inRs,
	): mixed
	{
		$multiple = $this->multiple ?? false;
		if ( ! $multiple) {
			return $value;
		}

		return $inRs ? $value : explode(',', $value);
	}

	private function processDateTime(
		mixed $value,
		bool $inRs,
	): mixed
	{
		return $inRs ? $value : \DateTimeImmutable::createFromFormat('Y-m-d\TH:i', $value);
	}

	private function getValueByClass(Alias|ParameterValue|BlogTag|Author|CommonTree|Product|Tree|CatalogTree|LibraryImage|Parameter|State|TreeAlternative|Usp $entity): string
	{
		if ($entity instanceof LocalizationEntity) {
			return $entity->getName();
		} elseif ($entity instanceof ParentEntity) {
			return $entity->getInternalName();
		} elseif (
			$entity instanceof Parameter ||
			$entity instanceof Alias ||
			$entity instanceof LibraryImage
		) {
			return $entity->name;
		} elseif (
			$entity instanceof ParameterValue
		) {
			return $entity->internalValue;
		} elseif ($entity instanceof State) {
			return $entity->name;
		} elseif ($entity instanceof TreeAlternative) {
			return $entity->alt_path;
		} elseif ($entity instanceof Usp) {
			return $entity->name;
		}

		throw new LogicException(sprintf('Add definition for Entity of %s', get_class($entity))); /** @phpstan-ignore-line */
	}

}
