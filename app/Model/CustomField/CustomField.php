<?php declare(strict_types = 1);

namespace App\Model\CustomField;

use App\Model\Orm\Orm;
use JsonSerializable;

final class CustomField implements JsonSerializable
{

	public function __construct(
		public readonly string $name,
		public readonly CustomFieldDefinition $definition,
	)
	{
	}

	public static function extend(
		CustomField $parent,
		array $localDefinition,
	): self
	{
		return new self(
			$parent->name,
			CustomFieldDefinition::extend($parent->definition, $localDefinition),
		);
	}

	public function process(
		mixed $value,
		bool $inRs,
		Orm $orm,
		LazyValueFactory $lazyValueFactory,
	): mixed
	{
		return $this->definition->process(
			$value,
			$inRs,
			$orm,
			$lazyValueFactory,
		);
	}

	public function jsonSerialize(): array
	{
		return (array) $this->definition;
	}

}
