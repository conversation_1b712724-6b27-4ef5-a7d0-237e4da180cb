<?php declare(strict_types = 1);

namespace App\Model\PriceLogger;


use App\Model\Orm\Orm;

use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\ProductVariantPriceLog\ProductVariantPriceLog;
use DateTimeImmutable;
use Exception;
use Nette\Utils\Floats;
use function max;

class ProductVariantPriceLogger
{

	public const SALE_FORGET_DAYS = 30;

	public function __construct(
		protected Orm $orm,
	)
	{
	}

	private function createLog(ProductVariant $productVariant, PriceLevel $priceLevel, float $realOrigPrice, float $origPrice, float $salePrice, ProductVariantPriceLog|null $lastProductVariantPriceLog = null): ProductVariantPriceLog
	{
		$productVariantPriceLog = new ProductVariantPriceLog();
		$productVariantPriceLog->mutation = $productVariant->product->getMutation();
		$productVariantPriceLog->productVariant = $productVariant;
		$productVariantPriceLog->priceLevel = $priceLevel;
		$productVariantPriceLog->realOrigPrice = $realOrigPrice;
		$productVariantPriceLog->salePrice = $salePrice;
		$productVariantPriceLog->origPrice = $origPrice;
		$productVariantPriceLog->createdAt = new DateTimeImmutable();

		if ($lastProductVariantPriceLog !== null && $lastProductVariantPriceLog->isInSale) {
			$productVariantPriceLog->lastSaleAt = $productVariantPriceLog->isInSale
				? $lastProductVariantPriceLog->lastSaleAt
				: null;
		} else {
			$productVariantPriceLog->lastSaleAt = $productVariantPriceLog->isInSale
				? $productVariantPriceLog->createdAt
				: null;
		}

		$this->orm->productVariantPriceLog->persist($productVariantPriceLog);

		return $productVariantPriceLog;
	}

	/**
	 * @throws Exception
	 */
	public function getLastProductVariantPriceLog(ProductVariant $productVariant, PriceLevel $priceLevel): ProductVariantPriceLog
	{
		$lastProductVariantPriceLog = $this->orm->productVariantPriceLog->findBy([
			'mutation' => $productVariant->product->getMutation(),
			'productVariant' => $productVariant,
			'priceLevel' => $priceLevel,
		])->orderBy('createdAt', 'DESC')->fetch();

		if ($lastProductVariantPriceLog !== null) {
			return $lastProductVariantPriceLog;
		} else {
			throw new Exception('Product variant price log for mutation ' . $productVariant->product->getMutation()->langCode . ', priceLevel ' . $priceLevel->type . ', productVariantId ' . $productVariant->id . ' not found.');
		}
	}

	private function getRealOrigPrice(ProductVariantPriceLog $lastLog, float $origPrice, float $salePrice): float
	{
		$priceChanged = !Floats::areEqual($lastLog->salePrice, $salePrice)
		                || !Floats::areEqual($lastLog->origPrice, $origPrice);
		if (!$priceChanged) {
			return $lastLog->realOrigPrice;
		}

		//from now on $priceChanged = true

		$isClaimForSale = Floats::isGreaterThan($origPrice, $salePrice);
		if (!$isClaimForSale) {
			return $origPrice;
		}

		//from now on $isClaimForSale = true && $priceChanged = true

		if (!$lastLog->isInSale) {
			$periodFrom = new DateTimeImmutable('-' . self::SALE_FORGET_DAYS . ' days');
			$minimumSalePrice = $this->orm->productVariantPriceLog->getMinimumSalePrice(
				$lastLog->productVariant->product->getMutation()->id,
				$lastLog->productVariant->id,
				$lastLog->priceLevel->id,
				$periodFrom
			);

			return max($minimumSalePrice ?? 0, $salePrice); //sale stars if the $minimumSalePrice > $salePrice
		} else { //was in sale
			return max($lastLog->realOrigPrice, $salePrice); //sale ends if $salePrice >= $lastProductVariantPriceLog->realOrigPrice
		}
	}

	/**
	 * @throws Exception
	 */
	public function createLogWithChangeCheck(ProductVariant $productVariant, PriceLevel $priceLevel, float $origPrice, float $salePrice, bool $computeRealOrigPrice = true): ProductVariantPriceLog|null
	{
		$lastProductVariantPriceLog = $this->getLastProductVariantPriceLog($productVariant, $priceLevel);

		if (!Floats::areEqual($lastProductVariantPriceLog->origPrice, $origPrice)
		    || !Floats::areEqual($lastProductVariantPriceLog->salePrice, $salePrice)) {
			$realOrigPrice = $computeRealOrigPrice
				? $this->getRealOrigPrice($lastProductVariantPriceLog, $origPrice, $salePrice)
				: $origPrice;

			return $this->createLog($productVariant, $priceLevel, $realOrigPrice, $origPrice, $salePrice, $lastProductVariantPriceLog);
		}

		return null;
	}

	/**
	 * @throws Exception
	 */
	public function initLog(ProductVariant $productVariant, PriceLevel $priceLevel, float $origPrice, float $salePrice): ProductVariantPriceLog
	{
		if (Floats::areEqual($origPrice, 0)) {
			$origPrice = $salePrice;
		}

		return $this->createLog($productVariant, $priceLevel, $origPrice, $origPrice, $salePrice, null);
	}

}
