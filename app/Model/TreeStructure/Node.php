<?php declare(strict_types = 1);

namespace App\Model\TreeStructure;

interface Node
{

	public function getNodeId(): int|string;
	public function getNodeParent(): ?Node;
	public function setNodeParent(Node $parentNode): void;
	public function isSelected(): bool;

	public function isPublic(): bool;
	public function getNodeItems(): array;
	public function setNodeItems(array $items): void;

	public function getNodeName(): string;

}
