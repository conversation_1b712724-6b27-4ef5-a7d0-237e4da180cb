<?php

namespace App\Model\TreeStructure;

use App\Model\Orm\Orm;
use App\Model\Orm\ConvertableToTree;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Page\Model\Orm\Tree;
use Nextras\Orm\Entity\IEntity;

class MovingModel
{
	public function __construct(
		private readonly Orm $orm,
	)
	{
	}


	public function move(ConvertableToTree $movedTree, ConvertableToTree $targetTree, string $action): void
	{
		$oldParent = $movedTree->getParentNode();
		$targetParent = $targetTree->getParentNode();

		match($action) {
			'after' , 'before' => $this->moveToSameLevel($movedTree, $targetTree, $action),
			'inside' , 'last' => $this->moveIntoTarget($movedTree, $targetTree),
			default => throw new \LogicException(sprintf('Missing action \'%s\'', $action))

		};


		if ($movedTree instanceof Tree) {
			$movedTree->rootId = $movedTree->parent->rootId;
		}

		if ($movedTree instanceof LocalizationEntity && $targetTree instanceof LocalizationEntity) {
			$movedTree->setMutation($targetTree->getMutation());
		}

		if ($oldParent instanceof ConvertableToTree) {
			$this->recalculateNode($oldParent);
			assert($oldParent instanceof IEntity);
			$this->orm->persist($oldParent);
		}


		if ($targetParent instanceof ConvertableToTree && in_array($action, ['after' , 'before'])) {

			$this->recalculateNode($targetParent);
			$this->recalculateTreePath($movedTree);
			assert($targetParent instanceof IEntity);
			$this->orm->persist($targetParent);

		} elseif ($targetTree instanceof ConvertableToTree && in_array($action, ['inside' , 'last'])) {
			$this->recalculateNode($targetTree);
			$this->recalculateTreePath($movedTree);
			assert($targetTree instanceof IEntity);
			$this->orm->persist($targetTree);

		} elseif ($targetParent === null && in_array($action, ['after' , 'before'])) {
			$this->recalculateTreePath($movedTree);
		}
		assert($movedTree instanceof IEntity);
		$this->orm->persist($movedTree);


		$this->orm->flush();
	}


	private function moveToSameLevel(ConvertableToTree $movedTree, ConvertableToTree $targetTree, string $action): void
	{
		$targetParent = $targetTree->getParentNode();
		$i = 0;

		if ($targetParent !== null) {
			$items = $targetParent->getTreeItems();

			$movedTree->setParentNode($targetParent);

		} else {
			$items = $targetTree->getTreeSiblings();
			$movedTree->setParentNode(null);
		}

		foreach ($items as $item) {
			assert($item instanceof ConvertableToTree);

			if ($item->getId() === $movedTree->getId()) {
				continue;
			}

			if ($action === 'before' && $item->getId() === $targetTree->getId()) {
				$i++;
				$movedTree->setSort($i);
			}

			$i++;
			$item->setSort($i);

			if ($action === 'after' && $item->getId() === $targetTree->getId()) {
				$i++;
				$movedTree->setSort($i);
			}

			assert($item instanceof IEntity);
			$this->orm->persist($item);
		}
	}


	private function moveIntoTarget(ConvertableToTree $movedTree, ConvertableToTree $targetTree): void
	{
		$i = 0;
		foreach ($targetTree->getTreeItems() as $item) {
			assert($item instanceof ConvertableToTree);
			$i++;
			$item->setSort($i);
		}

		$movedTree->setSort($i + 1);
		$movedTree->setParentNode($targetTree);
	}


	public function recalculateTreePath(ConvertableToTree $node): void
	{
		$this->recalculateNode($node);

		foreach ($node->getTreeItems() as $item) {
			$this->recalculateTreePath($item);
		}
	}


	private function recalculateNode(ConvertableToTree $node): void
	{
		if (count($node->getTreeItems()) !== 0) {
			$node->setLeafParameter(false);
		} else {
			$node->setLeafParameter(true);
		}

		if (($nodeParent = $node->getParentNode()) !== null) {
			$node->setLevel($nodeParent->getLevel() + 1);
			$tmpPath = $nodeParent->getPathNodes();
			$tmpPath[] = $nodeParent;
			$node->setPathNodes($tmpPath);
		} else {
			$node->setLevel(0);
			$node->setPathNodes([]);
		}
	}
}
