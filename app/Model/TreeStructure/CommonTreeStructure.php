<?php declare(strict_types = 1);

namespace App\Model\TreeStructure;

use App\Model\Orm\ConvertableToTree;

class CommonTreeStructure implements TreeStructure
{

	private array $rootNodes = [];


	/**
	 * @param array<ConvertableToTree> $firstLevelItems
	 */
	public function __construct(
        array $firstLevelItems,
		private readonly int $selectedId,
	)
	{
		foreach ($firstLevelItems as $firstLevelItem) {
			$this->rootNodes[] = $this->addPage($firstLevelItem);
		}
	}

	private function addPage(ConvertableToTree $item, ?CommonNode $itemParentNode = null): CommonNode
	{
		$itemNode = new CommonNode(
            $item->getId(),
            $item->getName(),
            $item->getId() === $this->selectedId,
            $item->isPublic()
        );

		$itemNode->setNodeParent($itemParentNode);

		$items = [];
		foreach ($item->getTreeItems() as $crossroadPage) {
			$items[] = $this->addPage($crossroadPage, $itemNode);
		}

		$itemNode->setNodeItems($items);

		return $itemNode;
	}


	public function getRootNodes(): array
	{
		return $this->rootNodes;
	}

}
