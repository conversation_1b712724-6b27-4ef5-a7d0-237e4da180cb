<?php declare(strict_types=1);

namespace App\Model\TreeStructure;

class CommonNode implements Node
{

	private ?Node $pageParentNode;

	private array $items;

	public function __construct(
		private readonly int $nodeId,
		private readonly string $name,
		private readonly bool $isSelected,
		private readonly bool $isPublic,
	)
	{
	}

	public function getNodeId(): int
	{
		return $this->nodeId;
	}

	public function getNodeParent(): ?Node
	{
		return $this->pageParentNode;
	}

	public function setNodeParent(?Node $parentNode): void
	{
		$this->pageParentNode = $parentNode;
	}

	public function setNodeItems(array $items): void
	{
		$this->items = $items;
	}

	public function getNodeItems(): array
	{
		return $this->items;
	}

	public function isSelected(): bool
	{
		return $this->isSelected;
	}

	public function isPublic(): bool
	{
		return $this->isPublic;
	}

	public function getNodeName(): string
	{
		return $this->name;
	}
}
