<?php
declare(strict_types = 1);

namespace App\Model;

use Contributte\Monolog\LoggerManager;
use Psr\Log\LoggerInterface;

trait HasLogger {
	private ?LoggerInterface $logger = null;

	protected function addLogger(string $loggerName): static
	{
		if (!property_exists($this, 'loggerManager')) {
			trigger_error('LoggerManager property must be defined in class ' . static::class, E_USER_WARNING);
			return $this;
		}

		assert($this->loggerManager instanceof LoggerManager);

		if (!$this->loggerManager->has($loggerName)) {
			trigger_error("Logger $loggerName does not exist", E_USER_WARNING);
			return $this;
		}

		$this->logger = $this->loggerManager->get($loggerName);

		return $this;
	}
}
