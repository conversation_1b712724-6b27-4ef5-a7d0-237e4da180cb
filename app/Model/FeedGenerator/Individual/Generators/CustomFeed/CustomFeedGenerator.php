<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Individual\Generators\CustomFeed;

use App\Model\FeedGenerator\Core\DTO\DTOInterface;
use App\Model\FeedGenerator\Core\Generators\Generator;
use App\Model\FeedGenerator\Core\Generators\GeneratorInterface;
use App\Model\FeedGenerator\Individual\DTO\CustomFeed\Country;
use App\Model\FeedGenerator\Individual\DTO\CustomFeed\Product;

final class CustomFeedGenerator extends Generator implements GeneratorInterface
{

	protected function createRootElementName(): void
	{
		$this->setRootElementName('PRODUCTS');
	}

	protected function createElementName(): void
	{
		$this->setElementName('PRODUCT');
	}

	/**
	 * @param Product $dto
	 */
	protected function createElement(DTOInterface $dto): void
	{
		$this->writeElement('CODE', $dto->getCode());
		$this->writeElement('PRODUCT_ID', $dto->getProductId());
		$this->writeElement('URL', $dto->getUrl());
		$this->writeElement('TITLE', $dto->getTitle());
		$this->writeElement('SHORT_DESCRIPTION', $dto->getDescriptionShort());
		$this->writeElement('LONG_DESCRIPTION', $dto->getDescriptionLong());
		$this->createElementManufacturer($dto);
		$this->writeElement('EAN', $dto->getEan());
		$this->writeElement('AVAILABILITY', $dto->getAvailability(Country::stateCzechRepublic())->getCode());
		$this->writeElement('AVAILABILITY_TEXT', $dto->getAvailability(Country::stateCzechRepublic())->getText());
		$this->writeElement('AVAILABILITY_PREORDER', $dto->getAvailability(Country::stateCzechRepublic())->getStockDate());
		$this->writeElement('STOCK_SHOP', $dto->getStock()->getStockShop());
		$this->writeElement('STOCK_SUM', $dto->getStock()->getStockAll());
		$this->createElementDelivery($dto);
		$this->writeElement('VAT_RATE_CZK', $dto->getPrice(Country::stateCzechRepublic())->getVatRateDefault());
		$this->writeElement('VAT_RATE_EUR', $dto->getPrice(Country::stateSlovakia())->getVatRateDefault());
		$this->writeElement('VAT_CZK', $dto->getPrice(Country::stateCzechRepublic())->getVatDefaultCzk());
		$this->writeElement('VAT_EUR', $dto->getPrice(Country::stateCzechRepublic())->getVatDefaultEur());
		$this->writeElement('CATEGORY', $dto->getCategory()->getPath());
		$this->writeElement('CATEGORY_CODE', $dto->getCategory()->getCode());
		$this->writeElement('CATEGORY_CODE_HEUREKA', $dto->getCategory()->getCodeHeureka());
		$this->writeElement('CATEGORY_CODE_ZBOZI', $dto->getCategory()->getCodeZbozi());
		$this->writeElement('CATEGORY_CODE_GOOGLE', $dto->getCategory()->getCodeGoogle());
		$this->writeElement('CATEGORY_POSITION', $dto->getCategory()->getPosition());
		$this->writeElement('PRICE_SHOP', $dto->getPrice(Country::stateCzechRepublic())->getCzkDefault());
//		$this->writeElement('PRICE_GMC', null);
//		$this->writeElement('PRICE_CLUB', null);
		$this->writeElement('PRICE_PURCHASE', $dto->getPrice(Country::stateCzechRepublic())->getCzkPurchase());
		$this->writeElement('PRICE_DPC', $dto->getPrice(Country::stateCzechRepublic())->getCzkRecomended());
//		$this->writeElement('PRICE_DISCOUNT', null);
//		$this->writeElement('PRICE_DISCOUNT_CZK', null);
		$this->writeElement('IMAGE_URL', $dto->getImage()->getUrl());
		$this->writeElement('IMAGE_TITLE', $dto->getImage()->getTitle() ?: $dto->getImage()->getName());
		$this->createElementPreviews($dto);
		$this->writeElement('PRODUCT_TYP', $dto->getType());
//		$this->writeElement('ADULT_YN', null);
		$this->writeElement('LABEL_NOVINKA', $dto->getIsNew());
		$this->writeElement('LABEL_ACTION', $dto->getIsAction());
		$this->writeElement('LABEL_TOP', $dto->getIsTop());
		$this->writeElement('LABEL_GIFT', $dto->getIsGift());
		$this->writeElement('LABEL_USED', $dto->getIsUsed());
		$this->writeElement('LABEL_DELIVERY_FREE', $dto->getIsDeliveryFree());
//		$this->writeElement('TERM_GIFT', null); //TODO etapa 2?
		$this->writeElement('WEIGHT', $dto->getWeight());
		$this->writeElement('HEIGHT', $dto->getHeight());
		$this->writeElement('WIDTH', $dto->getWidth());
		$this->writeElement('DEPTH', $dto->getDepth());
		$this->writeElement('PAGE', $dto->getPageCount());
		$this->createElementAlternativeProducts($dto);
		$this->createElementRelativeProducts($dto);
		$this->createElementGifts($dto);
		$this->writeElement('SCORE', $dto->getScore());
		$this->writeElement('PUBLICATION_DATE', $dto->getDatePublication());
		$this->createElementLanguage($dto);
	}

	private function createElementManufacturer(Product $productDto): void
	{
		if (!$productDto->getManufacturer()) {
			return;
		}

		$this->getWriter()->startElement('MANUFACTURER');
		$this->writeElement('CODE', $productDto->getManufacturer()->getId());
		$this->writeElement('NAME', $productDto->getManufacturer()->getName());
		$this->getWriter()->endElement();
	}



	private function createElementAlternativeProducts(Product $productDto): void
	{
		if (empty($productDto->getAlternativeProducts())) {
			return;
		}

		$this->getWriter()->startElement('ALTERNATIVE_PRODUCTS_CODES');

		foreach ($productDto->getAlternativeProducts() as $alternativeProduct) {
			$this->writeElement('ALTERNATIVE_PRODUCT_CODE', $alternativeProduct->getId());
		}

		$this->getWriter()->endElement();
	}

	private function createElementRelativeProducts(Product $productDto): void
	{
		if (empty($productDto->getRelatedProducts())) {
			return;
		}

		$this->getWriter()->startElement('ALTERNATIVE_PRODUCTS_CODES');

		foreach ($productDto->getRelatedProducts() as $relataedProduct) {
			$this->writeElement('ALTERNATIVE_PRODUCT_CODE', $relataedProduct->getId());
		}

		$this->getWriter()->endElement();
	}

	private function createElementDelivery(Product $productDto): void
	{
		if (empty($productDto->setCountry(Country::stateCzechRepublic())->getDeliveries())) {
			return;
		}

		$this->getWriter()->startElement('SHIPMENT_GROUPS');
		foreach ($productDto->getDeliveries() as $delivery) {
			$this->getWriter()->startElement('SHIPMENT_GROUP');
			$this->writeElement('NAME', $delivery->getName());
			$this->writeElement('DELIVERY_PRICE', $delivery->getPrice());
			$this->writeElement('DELIVERY_PRICE_COD', $delivery->getPriceWithPickUpFee());
			$this->getWriter()->endElement();
		}

		$this->getWriter()->endElement();
	}

	private function createElementLanguage(Product $productDto): void
	{
		if (empty($productDto->getLanguages())) {
			return;
		}
		$this->getWriter()->startElement('LANGUAGES');

		foreach ($productDto->getLanguages() as $gift) {
			$this->getWriter()->startElement('LANGUAGE');

			$this->writeElement('NAME', $gift->getName());

			$this->getWriter()->endElement();
		}

		$this->getWriter()->endElement();
	}

	private function createElementGifts(Product $productDto): void
	{
		if (empty($productDto->getGifts())) {
			return;
		}
		$this->getWriter()->startElement('GIFTS');

		foreach ($productDto->getGifts() as $gift) {
			$this->getWriter()->startElement('GIFT');

			$this->writeElement('NAME', $gift->getName());
			$this->writeElement('CODE', $gift->getCode());

			$this->getWriter()->endElement();
		}

		$this->getWriter()->endElement();
	}

	private function createElementPreviews(Product $productDto): void
	{
		if (empty($productDto->getPreviews())) {
			return;
		}

		$this->getWriter()->startElement('PREVIEWS');

		foreach ($productDto->getPreviews() as $preview) {
			$this->writeElement('PREVIEW_URL', $preview->getUrl());
		}

		$this->getWriter()->endElement();
	}

}
