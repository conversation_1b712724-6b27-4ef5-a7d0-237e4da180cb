<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Individual\Generators\Sitemap;

use App\Model\FeedGenerator\Core\DTO\DTOInterface;
use App\Model\FeedGenerator\Core\Generators\Generator;
use App\Model\FeedGenerator\Core\Generators\GeneratorInterface;
use App\Model\FeedGenerator\Individual\DTO\Sitemap\SitemapItem;

final class SitemapRootGenerator extends Generator implements GeneratorInterface
{

	protected function configureWriter(): void
	{
		if (($this->processId = getmypid()) === false) {
			$this->processId = 0;
		}
		$this->getWriter()->openUri($this->getOutputXml() . '.' . $this->processId . '.tmp');
		$this->getWriter()->setIndent(true);
	}

	protected function createRootElementName(): void
	{
		$this->setRootElementName('sitemapindex');
	}

	public function getRootElementAttributes(): array
	{
		return [
			'xmlns' => 'http://www.sitemaps.org/schemas/sitemap/0.9',
			'xmlns:xhtml' => 'http://www.w3.org/1999/xhtml',
		];
	}

	protected function createElementName(): void
	{
		$this->setElementName('sitemap');
	}

	/**
	 * @param SitemapItem $dto
	 */
	protected function createElement(DTOInterface $dto): void
	{
		$this->writeElement('loc', $dto->getUrl());
	}

}
