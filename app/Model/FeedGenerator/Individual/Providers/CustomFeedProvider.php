<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Individual\Providers;

use App\Model\ElasticSearch\Product\Repository;
use App\Model\FeedGenerator\Core\Providers\Provider;
use App\Model\FeedGenerator\Core\Providers\ProviderInterface;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\PostType\Page\Model\Orm\CatalogTree;

final class CustomFeedProvider extends Provider implements ProviderInterface
{

	private const BATCH_SIZE = 1000;

	private Mutation|null $mutation = null;

	private int $offset = 0;

	private CatalogTree|null $eshopTree = null;

	public function __construct(
		private readonly Repository $productRepositoryEs,
		private readonly Orm $orm,
	)
	{
	}

	public function provide(): iterable
	{
		$products = $this->productRepositoryEs->findAllPublicForFeed($this->getMutation(), $this->getEshopTree(), self::BATCH_SIZE, $this->getOffset());
		$this->setOffset($this->getOffset() + self::BATCH_SIZE);

		if ($products->count()) {
			return $products->getResults();
		}

		return [];
	}

	private function getEshopTree(): CatalogTree
	{
		if ($this->eshopTree === null) {
			$this->eshopTree = $this->getMutation()->pages->eshop;
		}

		return $this->eshopTree;
	}

	public function initMutation(): void
	{
		$this->mutation = $this->orm->mutation->getByCode($this->getMutationCode());
	}

	public function getMutation(): Mutation
	{
		if ($this->mutation === null) {
			$this->initMutation();
		}

		return $this->mutation;
	}

	public function getOffset(): int
	{
		return $this->offset;
	}

	public function setOffset(int $offset): void
	{
		$this->offset = $offset;
	}

}
