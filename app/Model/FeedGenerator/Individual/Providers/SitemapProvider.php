<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Individual\Providers;

use App\Model\FeedGenerator\Core\Providers\Provider;
use App\Model\FeedGenerator\Core\Providers\ProviderInterface;
use Generator;

final class SitemapProvider extends Provider implements ProviderInterface
{

	private Generator $yieldGenerator;

	public function __construct()
	{
	}

	public function provide(): iterable
	{
		return $this->yieldGenerator;
	}

	public function setYieldGenerator(Generator $yieldGenerator): void
	{
		$this->yieldGenerator = $yieldGenerator;
	}

}
