<?php declare(strict_types=1);

namespace App\Model\FeedGenerator\Individual\DTO\Sitemap;

use App\Model\FeedGenerator\Core\DTO\DTO;
use App\Model\FeedGenerator\Core\DTO\Sitemap\SitemapItemInterface;
use DateTimeZone;
use Nextras\Dbal\Utils\DateTimeImmutable;

final class SitemapItem extends DTO implements SitemapItemInterface
{

	final public function getUrl(): string
	{
		return $this->getData()['url'];
	}

	final public function getLastModified(): DateTimeImmutable
	{
		if (isset($this->getData()['lastModified'])) {
			if ($this->getData()['lastModified'] instanceof DateTimeImmutable) {
				return $this->getData()['lastModified'];
			} elseif (isset($this->getData()['lastModified']['date']) && isset($this->getData()['lastModified']['timezone'])) {
				return new DateTimeImmutable($this->getData()['lastModified']['date'], new DateTimeZone($this->getData()['lastModified']['timezone']));
			} elseif (is_int($this->getData()['lastModified']) || is_string($this->getData()['lastModified'])) {
				$lastModified = DateTimeImmutable::createFromFormat('U', (string) $this->getData()['lastModified']);
				if ($lastModified instanceof DateTimeImmutable) {
					return $lastModified;
				}
			}
		}

		return new DateTimeImmutable();
	}

}
