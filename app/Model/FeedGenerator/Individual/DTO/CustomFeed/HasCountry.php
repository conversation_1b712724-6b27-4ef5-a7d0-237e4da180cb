<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Individual\DTO\CustomFeed;

use App\Model\FeedGenerator\Core\DTO\CountryInterface;
use App\Model\FeedGenerator\Core\DTO\DTOInterface;
use App\Model\FeedGenerator\Core\Exceptions\DtoException;

trait HasCountry
{

	private CountryInterface $country;

	final protected function getCountry(): CountryInterface
	{
		return $this->country;
	}

	final public function setCountry(CountryInterface $country): DTOInterface
	{
		$this->country = $country;

		if (!($this instanceof DTOInterface)) {
			throw new DtoException(DtoException::NOT_DTO_OBJECT);
		}

		return $this;
	}

}
