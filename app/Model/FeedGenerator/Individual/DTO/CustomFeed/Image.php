<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Individual\DTO\CustomFeed;

use App\Model\FeedGenerator\Core\DTO\DTO;
use App\Model\FeedGenerator\Core\DTO\ImageInterface;

/**
 * @method static Image createInstance()
 * @method Image setData(mixed $data)
 */
final class Image extends DTO implements ImageInterface
{

	public function getName(): string
	{
		return $this->getData()['customFeed']['image']['name'] ?? '';
	}

	public function getUrl(): string
	{
		return $this->getData()['customFeed']['image']['url'] ?? '';
	}

	public function getTitle(): string
	{
		return $this->getData()['customFeed']['image']['title'] ?? '';
	}

}
