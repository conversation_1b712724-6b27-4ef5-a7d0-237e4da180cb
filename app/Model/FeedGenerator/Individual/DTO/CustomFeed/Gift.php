<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Individual\DTO\CustomFeed;

use App\Model\FeedGenerator\Core\DTO\DTO;
use App\Model\FeedGenerator\Core\DTO\DTOInterface;

/**
 * @method static Gift createInstance()
 * @method Gift setData(mixed $data)
 */
final class Gift extends DTO implements DTOInterface
{

	final public function getName(): string
	{
		return $this->getData()['name'] ?? '';
	}

	final public function getCode(): int|null
	{
		return $this->getData()['code'] ?? null;
	}

}
