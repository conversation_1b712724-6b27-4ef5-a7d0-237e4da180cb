<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Individual\DTO\CustomFeed;

use App\Model\FeedGenerator\Core\DTO\DTO;
use App\Model\FeedGenerator\Core\DTO\DTOInterface;

/**
 * @method static Manufacturer createInstance()
 * @method Manufacturer setData(mixed $data)
 */
final class Manufacturer extends DTO implements DTOInterface
{

	final public function getName(): string
	{
		return $this->getData()['name'] ?? '';
	}

	final public function getId(): int|null
	{
		return $this->getData()['id'] ?? null;
	}

}
