<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Individual\DTO\CustomFeed;

use App\Model\FeedGenerator\Core\DTO\AvailabilityInterface;
use App\Model\FeedGenerator\Core\DTO\CountryInterface;
use App\Model\FeedGenerator\Core\DTO\DTO;
use Nette\Utils\DateTime;

/**
 * @method static Availability createInstance()
 * @method Availability setData(mixed $data)
 * @method Availability setCountry(CountryInterface $country)
 * @method Country getCountry()
 */
final class Availability extends DTO implements AvailabilityInterface
{

	use HasCountry;

	final public function getCode(): string
	{
		return $this->getData()['customFeedAvailability']['type'] ?? '';
	}

	final public function getText(): string
	{
		return $this->getData()['customFeedAvailability']['text_front'] ?? '';
	}

	final public function getStockDate(): ?string
	{
		return $this->getData()['customFeedAvailability']['dateStock'] ?? null;
	}

	final public function getAvailableInDays(): int|null
	{
		if ($this->getAvailable() === null) {
			return null;
		}

		$availableInDays = $this->getAvailable()->diff(DateTime::from('c'))->days;

		if ($availableInDays === false) {
			return null;
		}

		return $availableInDays;
	}

	final public function getAvailable(): DateTime|null
	{
		if (!isset($this->getData()['customFeed']['availabilityDate'][$this->getCountry()->getCountyCode()])) {
			return null;
		}

		return DateTime::from($this->getData()['customFeed']['availabilityDate'][$this->getCountry()->getCountyCode()]);
	}

}
