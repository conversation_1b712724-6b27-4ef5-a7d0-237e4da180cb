<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Individual\DTO\CustomFeed;

use App\Model\FeedGenerator\Core\DTO\CategoryInterface;
use App\Model\FeedGenerator\Core\DTO\DTO;

/**
 * @method static Category createInstance()
 * @method Category setData(mixed $data)
 */
final class Category extends DTO implements CategoryInterface
{

	final public function getName(): string
	{
		return $this->getData()['customFeed']['categoryName'] ?? '';
	}
	final public function getPath(): string
	{
		return $this->getData()['customFeed']['path'] ?? '';
	}

	final public function getCode(): string
	{
		return $this->getData()['customFeed']['categoryCode'] ?? '';
	}

	final public function getPosition(): int|null
	{
		return $this->getData()['customFeed']['positionInCategory'] ?? null;
	}

	final public function getCodeHeureka(): string
	{
		return $this->getData()['customFeed']['categoryCodeHeureka'] ?? '';
	}

	final public function getCodeZbozi(): string
	{
		return $this->getData()['customFeed']['categoryCodeZbozi'] ?? '';
	}

	final public function getCodeGoogle(): string
	{
		return $this->getData()['customFeed']['categoryCodeGoogle'] ?? '';
	}

}
