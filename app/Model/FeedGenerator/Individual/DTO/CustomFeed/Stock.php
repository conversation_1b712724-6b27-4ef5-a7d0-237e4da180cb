<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Individual\DTO\CustomFeed;

use App\Model\FeedGenerator\Core\DTO\DTO;
use App\Model\FeedGenerator\Core\DTO\StockInterface;

/**
 * @method static Stock createInstance()
 * @method Stock setData(mixed $data)
 */
final class Stock extends DTO implements StockInterface
{

	final public function getStockShop(): int
	{
		return $this->getData()['customFeed']['stockShop'] ?? 0;
	}

	final public function getStockSupplier(): int
	{
		return $this->getData()['customFeed']['stockSupplier'] ?? 0;
	}

	final public function getStockAll(): int
	{
		return $this->getStockShop() + $this->getStockSupplier();
	}

}
