<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Individual\DTO\CustomFeed;

use App\Model\FeedGenerator\Core\DTO\DTO;
use App\Model\FeedGenerator\Core\DTO\DTOInterface;

/**
 * @method static Language createInstance()
 * @method Language setData(mixed $data)
 */
final class Language extends DTO implements DTOInterface
{

	final public function getName(): string
	{
		return $this->getData()['name'] ?? '';
	}

}
