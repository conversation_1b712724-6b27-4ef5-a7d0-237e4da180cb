<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Individual\DTO\CustomFeed;

use App\Model\FeedGenerator\Core\DTO\DeliveryInterface;
use App\Model\FeedGenerator\Core\DTO\DTO;

/**
 * @method static Delivery createInstance()
 * @method Delivery setData(mixed $data)
 */
final class Delivery extends DTO implements DeliveryInterface
{

	final public function getName(): string
	{
		return $this->getData()['name'] ?? '';
	}

	final public function getPrice(): float|null
	{
		return $this->getData()['price'] ?? null;
	}

	final public function getPriceWithPickUpFee(): float|null
	{
		if (!isset($this->getData()['pickupPersonalPrice'])) {
			return null;
		}

		if ($this->getPrice() === null) {
			return $this->getData()['pickupPersonalPrice'];
		}

		return $this->getPrice() + $this->getData()['pickupPersonalPrice'];
	}

}
