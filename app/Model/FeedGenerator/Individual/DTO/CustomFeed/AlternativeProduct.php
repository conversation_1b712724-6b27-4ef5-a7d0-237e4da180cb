<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Individual\DTO\CustomFeed;

use App\Model\FeedGenerator\Core\DTO\DTO;
use App\Model\FeedGenerator\Core\DTO\DTOInterface;

/**
 * @method static AlternativeProduct createInstance()
 * @method AlternativeProduct setData(mixed $data)
 */

final class AlternativeProduct extends DTO implements DTOInterface
{

	final public function getId(): int
	{
		return $this->getData();
	}

}
