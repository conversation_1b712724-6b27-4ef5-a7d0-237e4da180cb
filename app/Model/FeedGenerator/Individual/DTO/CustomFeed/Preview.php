<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Individual\DTO\CustomFeed;

use App\Model\FeedGenerator\Core\DTO\DTO;
use App\Model\FeedGenerator\Core\DTO\DTOInterface;

/**
 * @method static Preview createInstance()
 * @method Preview setData(mixed $data)
 */
final class Preview extends DTO implements DTOInterface
{

	final public function getUrl(): string
	{
		return $this->getData()['url'] ?? '';
	}

}
