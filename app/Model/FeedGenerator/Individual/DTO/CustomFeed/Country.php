<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Individual\DTO\CustomFeed;

use App\Model\FeedGenerator\Core\DTO\CountryInterface;
use App\Model\FeedGenerator\Core\DTO\DTO;

/**
 * @method static Country createInstance()
 * @method Country setData(mixed $data)
 * @method Country setCountry(CountryInterface $country)
 * @method Country getCountry()
 */
final class Country extends DTO implements CountryInterface
{

	private string $countyCode;

	public static function stateCzechRepublic(): Country
	{
		return self::createInstance()->setCountryCode('CZ');
	}

	public static function stateSlovakia(): Country
	{
		return self::createInstance()->setCountryCode('SK');
	}

	final public function setCountryCode(string $countryCode): Country
	{
		$this->countyCode = $countryCode;

		return $this;
	}

	final public function getCountyCode(): string
	{
		return $this->countyCode;
	}

}
