<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Individual\DTO\CustomFeed;

use App\Model\Currency\CurrencyHelper;
use App\Model\FeedGenerator\Core\DTO\CountryInterface;
use App\Model\FeedGenerator\Core\DTO\DTO;
use App\Model\FeedGenerator\Core\DTO\PriceInterface;
use App\Model\Orm\PriceLevel\PriceLevel;

/**
 * @method static Price createInstance();
 * @method Price setData(mixed $data);
 * @method Price setCountry(CountryInterface $country)
 * @method Country getCountry()
 */
final class Price extends DTO implements PriceInterface
{

	use HasCountry;

	public function getCzkDefault(): float
	{
		return $this->getPrice(PriceLevel::TYPE_DEFAULT, CurrencyHelper::CURRENCY_CZK);
	}

	public function getEurDefault(): float
	{
		return $this->getPrice(PriceLevel::TYPE_DEFAULT, CurrencyHelper::CURRENCY_EUR);
	}

	public function getCzkRecomended(): float
	{
		return $this->getPrice(PriceLevel::TYPE_RECOMMENDED, CurrencyHelper::CURRENCY_CZK);
	}

	public function getEurRecomended(): float
	{
		return $this->getPrice(PriceLevel::TYPE_RECOMMENDED, CurrencyHelper::CURRENCY_EUR);
	}

	public function getCzkPurchase(): float
	{
		return $this->getPrice(PriceLevel::TYPE_PURCHASE, CurrencyHelper::CURRENCY_CZK);
	}

	public function getEurPurchase(): float
	{
		return $this->getPrice(PriceLevel::TYPE_PURCHASE, CurrencyHelper::CURRENCY_EUR);
	}

	public function getCzkDiscount(): float
	{
		return $this->getPrice(PriceLevel::TYPE_SHELTER, CurrencyHelper::CURRENCY_CZK);
	}

	public function getEurDiscount(): float
	{
		return $this->getPrice(PriceLevel::TYPE_SHELTER, CurrencyHelper::CURRENCY_EUR);
	}

	final public function getVatDefaultCzk(): float|null
	{
		if (!$vat = $this->getVatRateDefault()) {
			return null;
		}

		$vatCoeficient = $vat / 100;

		return $this->getCzkDefault() * $vatCoeficient;
	}

	final public function getVatDefaultEur(): float|null
	{
		if (!$vat = $this->getVatRateDefault()) {
			return null;
		}

		$vatCoeficient = $vat / 100;

		return $this->getEurDefault() * $vatCoeficient;
	}

	final public function getVatRateTypeDefaultCode(): int|null
	{
		if (!isset($this->getData()['customFeed']['vats'][$this->getCountry()->getCountyCode()]['type'])) {
			return null;
		}

		return $this->getData()['customFeed']['vats'][$this->getCountry()->getCountyCode()]['type'];
	}

	final public function getVatRateDefault(): int|null
	{
		if (!isset($this->getData()['customFeed']['vats'][$this->getCountry()->getCountyCode()]['absolute'])) {
			return null;
		}

		return $this->getData()['customFeed']['vats'][$this->getCountry()->getCountyCode()]['absolute'];
	}

	private function getPrice(string $priceLevel, string $currencyCode): float
	{
		if (!isset($this->getData()['statePricesWithVat'][$this->getCountry()->getCountyCode()][$priceLevel][$currencyCode][0])) {
			return 0.0;
		}

		return $this->getData()['statePricesWithVat'][$this->getCountry()->getCountyCode()][$priceLevel][$currencyCode][0];
	}

}
