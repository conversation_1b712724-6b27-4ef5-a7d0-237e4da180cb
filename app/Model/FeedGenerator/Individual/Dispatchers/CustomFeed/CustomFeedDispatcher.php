<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Individual\Dispatchers\CustomFeed;

use App\Model\FeedGenerator\Core\Dispatchers\Dispatcher;
use App\Model\FeedGenerator\Core\Dispatchers\DispatcherInterface;
use App\Model\FeedGenerator\Core\DTO\DTOInterface;

final class CustomFeedDispatcher extends Dispatcher implements DispatcherInterface
{

	protected function setupDto(mixed $item): DTOInterface
	{
		return $this->getDtoFactory()
			->customFeedProduct()
			->setData($item->getSource());
	}

}
