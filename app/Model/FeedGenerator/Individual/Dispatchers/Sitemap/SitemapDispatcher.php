<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Individual\Dispatchers\Sitemap;

use App\Model\FeedGenerator\Core\Dispatchers\Dispatcher;
use App\Model\FeedGenerator\Core\Dispatchers\DispatcherInterface;
use App\Model\FeedGenerator\Core\DTO\DTOInterface;

final class SitemapDispatcher extends Di<PERSON>atch<PERSON> implements DispatcherInterface
{

	protected function setupDto(mixed $item): DTOInterface
	{
		return $this->getDtoFactory()
			->sitemapItem()
			->setData($item);
	}

	public function dispatch(): iterable
	{
		foreach ($this->getProvider()->provide() as $item) {
			$dto = $this->setupDto($item);

			if (!($dto instanceof DTOInterface)) {
				continue;
			}
			yield $dto;
		}
	}

}
