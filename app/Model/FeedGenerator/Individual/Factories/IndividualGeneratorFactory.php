<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Individual\Factories;

use App\Model\FeedGenerator\Core\Factories\GeneratorFactory;
use App\Model\FeedGenerator\Core\Generators\GeneratorInterface;
use App\Model\FeedGenerator\Individual\Generators\CustomFeed\CustomFeedGenerator;
use App\Model\FeedGenerator\Individual\Generators\Sitemap\SitemapGenerator;
use App\Model\FeedGenerator\Individual\Generators\Sitemap\SitemapRootGenerator;

final class IndividualGeneratorFactory extends GeneratorFactory
{

	public function __construct(
		private readonly CustomFeedGenerator $customFeedGenerator,
		private readonly SitemapGenerator $sitemapGenerator,
		private readonly SitemapRootGenerator $sitemapRootGenerator,
	)
	{
	}

	public function customFeedGenerator(): GeneratorInterface
	{
		return $this->customFeedGenerator
			->setDispatcher($this->getFactory()->dispatcherFactory()->customFeedDispatcher());
	}

	public function sitemapGenerator(): GeneratorInterface
	{
		return $this->sitemapGenerator
			->setDispatcher($this->getFactory()->dispatcherFactory()->sitemapDispatcher());
	}

	public function sitemapRootGenerator(): GeneratorInterface
	{
		return $this->sitemapRootGenerator
			->setDispatcher($this->getFactory()->dispatcherFactory()->sitemapDispatcher());
	}

}
