<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Individual\Factories;

use App\Model\FeedGenerator\Core\DTO\CustomFeed\ProductCustomFeedInterface;
use App\Model\FeedGenerator\Core\DTO\Heureka\ProductHeurekaInterface;
use App\Model\FeedGenerator\Core\DTO\Sitemap\SitemapItemInterface;
use App\Model\FeedGenerator\Core\Exceptions\DtoFactoryException;
use App\Model\FeedGenerator\Core\Factories\AbstractFactory;
use App\Model\FeedGenerator\Core\Factories\DtoFactoryInterface;
use App\Model\FeedGenerator\Individual\DTO\CustomFeed\Product;
use App\Model\FeedGenerator\Individual\DTO\Sitemap\SitemapItem;

final class DtoFactory extends AbstractFactory implements DtoFactoryInterface
{

	public function customFeedProduct(): ProductCustomFeedInterface
	{
		/**
		 * @var Product $product
		 */
		$product = Product::createInstance();

		return $product;
	}

	public function heurekaProduct(): ProductHeurekaInterface
	{
		throw new DtoFactoryException(DtoFactoryException::NOT_IMPLEMENTED);
	}

	public function sitemapItem(): SitemapItemInterface
	{
		/**
		 * @var SitemapItem $sitemapItem
		 */
		$sitemapItem = SitemapItem::createInstance();

		return $sitemapItem;
	}

}
