<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Individual\Factories;

use App\Model\FeedGenerator\Core\Dispatchers\DispatcherInterface;
use App\Model\FeedGenerator\Core\Factories\DispatcherFactory;
use App\Model\FeedGenerator\Core\Factories\DispatcherFactoryInterface;
use App\Model\FeedGenerator\Individual\Dispatchers\CustomFeed\CustomFeedDispatcher;
use App\Model\FeedGenerator\Individual\Dispatchers\Sitemap\SitemapDispatcher;

final class IndividualDispatcherFactory extends DispatcherFactory implements DispatcherFactoryInterface
{

	public function __construct(
		private readonly CustomFeedDispatcher $customFeedDispatcher,
		private readonly SitemapDispatcher $sitemapDispatcher,
	)
	{
	}

	public function customFeedDispatcher(): DispatcherInterface
	{
		return $this->customFeedDispatcher
			->setProvider($this->getFactory()->providerFactory()->customFeedProvider())
			->setDtoFactory($this->getFactory()->dtoFactory());
	}

	public function sitemapDispatcher(): DispatcherInterface
	{
		$providerFactory = $this->getFactory()->providerFactory();
		assert($providerFactory instanceof IndividualProviderFactory);
		return $this->sitemapDispatcher
			->setProvider($providerFactory->sitemapProvider())
			->setDtoFactory($this->getFactory()->dtoFactory());
	}

}
