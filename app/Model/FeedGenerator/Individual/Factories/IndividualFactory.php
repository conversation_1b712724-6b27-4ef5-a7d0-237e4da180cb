<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Individual\Factories;

use App\Model\FeedGenerator\Core\Factories\DispatcherFactoryInterface;
use App\Model\FeedGenerator\Core\Factories\DtoFactoryInterface;
use App\Model\FeedGenerator\Core\Factories\Factory;
use App\Model\FeedGenerator\Core\Factories\GeneratorFactoryInterface;
use App\Model\FeedGenerator\Core\Factories\ProviderFactoryInterface;

class IndividualFactory extends Factory implements IndividualFactoryInterface
{

	public function __construct(
		private readonly IndividualProviderFactory $individualProviderFactory,
		private readonly DtoFactory $dtoFactory,
		private readonly IndividualDispatcherFactory $individualDispatcherFactory,
		private readonly IndividualGeneratorFactory $individualGeneratorFactory,
	)
	{
	}

	public function dtoFactory(): DtoFactoryInterface
	{
		$this->dtoFactory->setFactory($this);

		return $this->dtoFactory;
	}

	public function providerFactory(): ProviderFactoryInterface
	{
		$this->individualProviderFactory->setFactory($this);

		return $this->individualProviderFactory;
	}

	public function dispatcherFactory(): DispatcherFactoryInterface
	{
		$this->individualDispatcherFactory->setFactory($this);

		return $this->individualDispatcherFactory;
	}

	public function generatorFactory(): GeneratorFactoryInterface
	{
		$this->individualGeneratorFactory->setFactory($this);

		return $this->individualGeneratorFactory;
	}

}
