<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Core;

use App\Model\FeedGenerator\Core\Factories\FactoryInterface;

class FeedGenerator
{

	public function __construct(private FactoryInterface $factory, string|null $mutationCode = null)
	{
		$this->factory->setMutationCode($mutationCode);
	}

	public function generateHeureka(string $outputFile): void
	{
		$this->getFactory()->generatorFactory()
			->heurekaGenerator()
			->setOutputXml($outputFile)
			->generate()
			->save()
			->moveTmpOutput();
	}

	public function getFactory(): FactoryInterface
	{
		return $this->factory;
	}

}
