<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Core\Generators\Heureka;

use App\Model\FeedGenerator\Core\DTO\DTOInterface;
use App\Model\FeedGenerator\Core\Generators\Generator;
use App\Model\FeedGenerator\Core\Generators\GeneratorInterface;

final class HeurekaGenerator extends Generator implements GeneratorInterface
{

	protected function createElementName(): void
	{
		// TODO: Implement createElementName() method.
	}

	protected function createElement(DTOInterface $dto): void
	{
		// TODO: Implement createElement() method.
	}

	protected function createRootElementName(): void
	{
		// TODO: Implement createRootElementName() method.
	}

}
