<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Core\Generators;

use App\Model\FeedGenerator\Core\Dispatchers\DispatcherInterface;
use App\Model\FeedGenerator\Core\DTO\DTOInterface;
use App\Model\FeedGenerator\Core\Exceptions\GeneratorException;
use App\Model\HasLogger;
use Contributte\Monolog\LoggerManager;
use Nette\Utils\FileSystem;
use Nette\Utils\Strings;
use Tracy\Debugger;
use XMLWriter;

abstract class Generator implements GeneratorInterface
{

	use HasLogger;

	private DispatcherInterface $dispatcher;

	private string $outputXml;

	private string $elementName;

	private string $rootElementName;

	protected mixed $processId = null;

	private int $dtosCount;

	public function __construct(
		private readonly XMLWriter $XMLWriter,
		private readonly LoggerManager $loggerManager,
	)
	{
		$this->addLogger('xmlGenerator');
	}

	protected function configureWriter(): void
	{
		if (($this->processId = getmypid()) === false) {
			$this->processId = 0;
		}
		$this->getWriter()->openUri($this->getOutputXml() . '.' . $this->processId . '.tmp');
		$this->getWriter()->startDocument('1.0', 'UTF-8');
		$this->getWriter()->setIndent(true);
	}

	abstract protected function createElementName(): void;

	abstract protected function createRootElementName(): void;

	abstract protected function createElement(DTOInterface $dto): void;

	final public function generate(): GeneratorInterface
	{
		$this->logger?->info(static::class . ' / Starting generating feed');

		$this->configureWriter();
		$this->createRootElementName();
		$this->createElementName();

		$this->getWriter()->startElement($this->getRootElementName());
		foreach ($this->getRootElementAttributes() as $name => $value) {
			$this->getWriter()->writeAttribute($name, $value);
		}

		$this->dtosCount = 0;
		while ($dtoObjects = $this->getDispatcher()->dispatch()) {
			$dtosBatchCount = 0;
			foreach ($dtoObjects as $dto) {
				$this->getWriter()->startElement($this->getElementName());
				$this->createElement($dto);
				$this->getWriter()->endElement();
				$this->getWriter()->flush();
				$dtosBatchCount++;
				$this->dtosCount++;
			}

			$this->logger?->info(static::class . ' / Batch of {count} products completed.', ['batchCount' => $dtosBatchCount, 'totalCount' => $this->dtosCount]);
			if ($dtoObjects instanceof \Generator) {
				break;
			}
		}

		$this->getWriter()->endElement();

		$this->logger?->info(static::class . ' / Finished generating feed');
		return $this;
	}

	final public function save(): GeneratorInterface
	{
		$this->getWriter()->endComment();
		$this->getWriter()->flush();

		return $this;
	}
	final public function moveTmpOutput(): GeneratorInterface
	{
		try {
			FileSystem::rename($this->getOutputXml() . '.' . $this->processId . '.tmp', $this->getOutputXml());
		} catch (\Throwable $e) {
			Debugger::log($e, Debugger::EXCEPTION);
		}

		return $this;
	}
	final public function removeTmpFile(): GeneratorInterface
	{
		try {
			FileSystem::delete($this->getOutputXml() . '.' . $this->processId . '.tmp');
		} catch (\Throwable $e) {
			Debugger::log($e, Debugger::EXCEPTION);
		}

		return $this;
	}

	final protected function writeElement(string $elementName, mixed $elementValue): void
	{
		if ($elementValue === null) {
			return;
		}

		$elementValue = (string) $elementValue;

		if (Strings::length($elementValue) === 0) {
			return;
		}

		$this->getWriter()->writeElement($elementName, $elementValue);
	}

	final public function setDispatcher(DispatcherInterface $dispatcher): Generator
	{
		$this->dispatcher = $dispatcher;
		return $this;
	}

	final public function getDispatcher(): DispatcherInterface
	{
		return $this->dispatcher;
	}

	final protected function getWriter(): XMLWriter
	{
		return $this->XMLWriter;
	}

	final public function setOutputXml(string $outputXml): Generator
	{
		if (Strings::length($outputXml) === 0) {
			throw new GeneratorException(GeneratorException::EMPTY_OUTPUT_XML_NAME);
		}

		$this->outputXml = $outputXml;

		return $this;
	}

	public function getOutputXml(): string
	{
		return $this->outputXml;
	}

	private function getElementName(): string
	{
		return $this->elementName;
	}

	final protected function setElementName(string $elementName): void
	{
		$this->elementName = $elementName;
	}

	private function getRootElementName(): string
	{
		return $this->rootElementName;
	}

	final protected function setRootElementName(string $rootElementName): void
	{
		$this->rootElementName = $rootElementName;
	}

	final public function getDtosCount(): int
	{
		return $this->dtosCount;
	}

	public function getRootElementAttributes(): array
	{
		return [];
	}

}
