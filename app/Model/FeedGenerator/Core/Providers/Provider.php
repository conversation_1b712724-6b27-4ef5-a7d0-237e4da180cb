<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Core\Providers;

abstract class Provider implements ProviderInterface
{

	private string|null $mutationCode = null;

	final public function setMutationCode(string|null $mutationCode): ProviderInterface
	{
		$this->mutationCode = $mutationCode;
		return $this;
	}

	protected function getMutationCode(): string|null
	{
		return $this->mutationCode;
	}

}
