<?php

namespace App\Model\FeedGenerator\Core\Factories;

interface FactoryInterface
{

	public function setMutationCode(string $mutationCode): FactoryInterface;
	public function getMutationCode(): string|null;

	public function dispatcherFactory(): DispatcherFactoryInterface;

	public function dtoFactory(): DtoFactoryInterface;

	public function generatorFactory(): GeneratorFactoryInterface;

	public function providerFactory(): ProviderFactoryInterface;

}
