<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Core\Factories;

use App\Model\FeedGenerator\Core\Dispatchers\DispatcherInterface;
use App\Model\FeedGenerator\Core\Dispatchers\Heureka\HeurekaDispatcher;
use App\Model\FeedGenerator\Core\Exceptions\DispatcherException;

class DispatcherFactory extends AbstractFactory implements DispatcherFactoryInterface
{

	public function __construct(
		private readonly HeurekaDispatcher $heurekaDispatcher,
	)
	{
	}

	public function heurekaDispatcher(): DispatcherInterface
	{
		return $this->heurekaDispatcher
			->setProvider($this->getFactory()->providerFactory()->heurekaProvider())
			->setDtoFactory($this->getFactory()->dtoFactory());
	}

	public function customFeedDispatcher(): DispatcherInterface
	{
		throw new DispatcherException(DispatcherException::NOT_IMPLEMENTED);
	}

	public function sitemapDispatcher(): DispatcherInterface
	{
		throw new DispatcherException(DispatcherException::NOT_IMPLEMENTED);
	}

}
