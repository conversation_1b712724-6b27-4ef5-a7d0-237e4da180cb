<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Core\DTO;

abstract class DTO implements DTOInterface
{

	final public function __construct()
	{
	}

	private mixed $data;

	public static function createInstance(): DTOInterface
	{
		return new static();
	}

	final protected function getData(): mixed
	{
		return $this->data;
	}

	final public function setData(mixed $data): DTOInterface
	{
		$this->data = $data;
		return $this;
	}

}
