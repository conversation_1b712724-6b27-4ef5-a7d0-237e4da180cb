<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Core\DTO\Heureka;

use App\Model\FeedGenerator\Core\DTO\AvailabilityInterface;
use App\Model\FeedGenerator\Core\DTO\CategoryInterface;
use App\Model\FeedGenerator\Core\DTO\ImageInterface;
use App\Model\FeedGenerator\Core\DTO\ParameterInterface;
use App\Model\FeedGenerator\Core\DTO\PriceInterface;
use App\Model\FeedGenerator\Core\DTO\ProductInterface;
use App\Model\FeedGenerator\Core\DTO\StockInterface;

interface ProductHeurekaInterface extends ProductInterface
{

	public function getAvailability(): AvailabilityInterface;

	public function getCategory(): CategoryInterface;

	public function getImage(): ImageInterface;

	public function getParameter(): ParameterInterface;

	public function getParametes(): iterable;

	public function getPrice(): PriceInterface;

	public function getStock(): StockInterface;

}
