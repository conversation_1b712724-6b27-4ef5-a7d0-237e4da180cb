<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Core\Dispatchers;

use App\Model\FeedGenerator\Core\DTO\DTOInterface;
use App\Model\FeedGenerator\Core\Factories\DtoFactoryInterface;
use App\Model\FeedGenerator\Core\Providers\ProviderInterface;

abstract class Dispatcher implements DispatcherInterface
{

	private ProviderInterface $provider;

	private DtoFactoryInterface $dtoFactory;

	public function dispatch(): iterable
	{
		$dtos = [];

		foreach ($this->getProvider()->provide() as $item) {
			$dto = $this->setupDto($item);

			if (!($dto instanceof DTOInterface)) {
				continue;
			}
			$dtos[] = $dto;
		}
		return $dtos;
	}

	final public function setDtoFactory(DtoFactoryInterface $dtoFactory): Dispatcher
	{
		$this->dtoFactory = $dtoFactory;
		return $this;
	}

	final protected function getDtoFactory(): DtoFactoryInterface
	{
		return $this->dtoFactory;
	}

	abstract protected function setupDto(mixed $item): DTOInterface|null;

	final public function setProvider(ProviderInterface $provider): Dispatcher
	{
		$this->provider = $provider;
		return $this;
	}

	final public function getProvider(): ProviderInterface
	{
		return $this->provider;
	}

}
