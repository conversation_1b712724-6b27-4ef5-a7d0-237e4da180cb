<?php

namespace App\Model\FeedGenerator\Core\Dispatchers;

use App\Model\FeedGenerator\Core\DTO\DTOInterface;
use App\Model\FeedGenerator\Core\Providers\ProviderInterface;

interface DispatcherInterface
{

	/**
	 * @return DTOInterface[]
	 */
	public function dispatch(): iterable;

	public function setProvider(ProviderInterface $provider): DispatcherInterface;
	public function getProvider(): ProviderInterface;

}
