<?php declare(strict_types = 1);

namespace App\Model\FeedGenerator\Core\Dispatchers\Heureka;

use App\Model\FeedGenerator\Core\Dispatchers\Dispatcher;
use App\Model\FeedGenerator\Core\Dispatchers\DispatcherInterface;
use App\Model\FeedGenerator\Core\DTO\Heureka\ProductHeurekaInterface;

final class HeurekaDispatcher extends Dispatcher implements DispatcherInterface
{

	protected function setupDto(mixed $item): ProductHeurekaInterface
	{
		/**
		 * @var ProductHeurekaInterface $dto
		 */
		$dto = $this->getDtoFactory()
			->heurekaProduct()
			->setData($item);

		return $dto;
	}

}
