<?php declare(strict_types=1);

namespace App\Model\FulltextSearch;

use App\Infrastructure\Latte\Functions;
use App\Model\CacheStorageService;
use App\Model\Consent\MarketingConsent;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\SearchLog\SearchLog;
use App\Model\Orm\SearchLog\SearchLogRepository;
use App\Model\Orm\User\User;
use Nette\Caching\Cache;
use Nette\Http\Request;

class UserHistory
{

	public function __construct(
		private readonly MarketingConsent $marketingConsent,
		private readonly UserHistoryCookie $userHistoryCookie,
		private readonly SearchLogRepository $searchLogRepository,
		private readonly Request $httpRequest,
		private readonly CacheStorageService $cacheStorageService,
	)
	{
	}

	/**
	 * @return array<string>
	 */
	public function findSearchedText(Mutation $mutation): array
	{
		$generator = function () use ($mutation) {
			return [
				$this->searchLogRepository->findBest($mutation),
				[
					Cache::Expire => '30 minutes',
				],
			];
		};
		return $this->cacheStorageService->getStoredDataWithOptions('findSearchedText', Functions::cacheKey($mutation), $generator);
	}

	/**
	 * @return array<string>
	 */
	public function findSearchedTextForUser(): array
	{
		if ( ! $this->marketingConsent->isPersonalizationGranted() || $this->userHistoryCookie->get() === []) {
			return [];
		}

		return $this->userHistoryCookie->get();
	}


	public function add(Mutation $mutation, string $textToSearch, ?User $userEntity): void
	{
		if ($this->marketingConsent->isPersonalizationGranted()) {
			$this->addToCookie($textToSearch);
		}
		$this->addToDatabase($mutation, $textToSearch, $userEntity);
	}

	private function addToCookie(string $textToSearch): void
	{
		$this->userHistoryCookie->add($textToSearch);
	}

	private function addToDatabase(Mutation $mutation, string $textToSearch, ?User $userEntity): void
	{
		$searchLog = new SearchLog();
		$this->searchLogRepository->attach($searchLog);

		$searchLog->userId = $userEntity?->id;
		$searchLog->text = $textToSearch;
		$searchLog->hasResults = true;
		$searchLog->agent = $this->httpRequest->getHeader('User-Agent');
		$searchLog->mutation = $mutation;

		$this->searchLogRepository->persistAndFlush($searchLog);
	}

	public function addNoResult(Mutation $mutation, string $textToSearch): void
	{
		$searchLog = new SearchLog();
		$this->searchLogRepository->attach($searchLog);

		$searchLog->userId = null;
		$searchLog->hasResults = false;
		$searchLog->text = $textToSearch;
		$searchLog->agent = $this->httpRequest->getHeader('User-Agent');
		$searchLog->mutation = $mutation;

		$this->searchLogRepository->persistAndFlush($searchLog);
	}

}
