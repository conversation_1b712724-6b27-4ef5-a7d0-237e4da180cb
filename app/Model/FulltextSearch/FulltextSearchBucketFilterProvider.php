<?php

namespace App\Model\FulltextSearch;

use App\Model\BucketFilter\BucketFilterBuilder;
use App\Model\BucketFilter\BucketFilterBuilderFactory;
use App\Model\BucketFilter\SortCreator;
use App\Model\ElasticSearch\Product\Repository;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Product\ProductRepository;
use App\Model\Orm\State\State;
use Nette\Application\UI\Presenter;

class FulltextSearchBucketFilterProvider
{

	public function __construct(
		private readonly BucketFilterBuilderFactory $bucketFilterBuilderFactory,
		private readonly ProductRepository $productRepository,
	)
	{
	}

	public function get(string $stringToSearch, Presenter $presenter, Mutation $mutation, State $state, PriceLevel $priceLevel, array $parameters): BucketFilterBuilder
	{
		$bucketFilterBuilder = $this->bucketFilterBuilderFactory->create($presenter, $mutation->pages->search, $state, $priceLevel, SortCreator::FULLTEXT, $parameters);
		$forcedIds = [];
		$minScore = Repository::MINIMUM_SCORE;
		if (strlen($stringToSearch) > 8) {
			// is ean || isbn
			$forcedIds = $this->productRepository->findByEanOrIsbn($stringToSearch)->fetchPairs(null, 'id');
			if ($forcedIds !== []) {
				$minScore = null;
			}
		}
		$bucketFilterBuilder->setSearchParameters($mutation->pages, $stringToSearch, $forcedIds, $minScore);

		return $bucketFilterBuilder;
	}

}
