<?php declare(strict_types=1);

namespace App\Model\FulltextSearch;

use App\Model\ElasticSearch\Common\ItemTypeHelper;
use App\Model\ElasticSearch\Common\Repository;
use App\Model\ElasticSearch\Common\ResultReader;
use App\Model\Orm\Mutation\Mutation;
use App\PostType\Core\Model\Publishable;
use App\PostType\Core\Model\Validatable;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\TreeRepository;
use App\PostType\Tag\Model\Orm\TagLocalization\TagLocalization;
use Elastica\Query\BoolQuery;
use Elastica\Query\Range;
use Elastica\Query\Term;
use Elastica\QueryBuilder;

class Search
{

	public function __construct(
		private readonly Mutation $mutation,
		private readonly string $textToSearch,
		private readonly Repository $esCommonRepository,
		private readonly ResultReader $resultReader,
		private readonly ?int $limit = 20,
	)
	{
	}

	/**
	 * @return array<Result>
	 */
	public function search(): array
	{
		$results = [];
		$results[CatalogTree::class] = $this->searchInCategories();
		$results[TagLocalization::class] = $this->searchInCommonPostType(TagLocalization::class);

		return array_filter($results, fn(?Result $result) => $result !== null);
	}

	/**
	 * @param class-string $class
	 */
	private function searchInCommonPostType(string $class): ?Result
	{
		$b = new QueryBuilder();
		$query = $b->query()->bool();
		return $this->searchResults($query, $class);
	}

	private function searchInCategories(): ?Result
	{
		$itemClass = CatalogTree::class;
		$b = new QueryBuilder();

		$query = $b->query()->bool()->addMust(
			new Term(['hide' => false]),
		);

		return $this->searchResults($query, $itemClass);
	}


	/**
	 * @param class-string $class
	 */
	private function searchResults(BoolQuery $query, string $class): ?Result
	{
		$shortClass = ItemTypeHelper::getTypeByClass($class);
		$query->addMust(
			new Term(['type' => $shortClass]),
		);

		$reflectionClass = new \ReflectionClass($class);
		if (
			($reflectionClass)->implementsInterface(Validatable::class)
			|| in_array($class, TreeRepository::getEntityClassNames())
		) {
			$query->addMust(
				new Range('publicFrom', [
					'lte' => 'now',
				])
			);
			$query->addMust(
				new Range('publicTo', [
					'gte' => 'now',
				])
			);
		}
		if (
			($reflectionClass)->implementsInterface(Publishable::class)
			|| in_array($class, TreeRepository::getEntityClassNames())
		) {
			$query->addMust(
				new Term(['public' => true])
			);
		}

		$result = $this->esCommonRepository->fulltextSearch($this->mutation, $query, $this->textToSearch, $this->limit);
		if ($result === null) {
			return new Result(
				$shortClass,
				[],
			);
		}

		$items = $this->resultReader->mapResultToEntityCollection(
			$result,
			$class,
		)->fetchAll();

		if ($items === []) {
			return null;
		}

		return new Result(
			$shortClass,
			$items,
		);
	}

}
