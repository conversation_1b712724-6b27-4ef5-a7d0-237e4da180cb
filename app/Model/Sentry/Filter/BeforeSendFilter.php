<?php declare(strict_types=1);

namespace App\Model\Sentry\Filter;

use App\Model\CacheFactory;
use Nette\Caching\Cache;
use Sentry\Event;
use Sentry\EventHint;
use Throwable;
use Tracy\Helpers;

class BeforeSendFilter
{

	public const EVENT_COUNT_LIMIT = 10;

	public function __construct(
		private readonly CacheFactory $cacheFactory,
	)
	{
	}

	public function filter(Event $event, ?EventHint $hint): ?Event
	{
		$eventKey = $this->getEventKey($event, $hint);
		try {
			$sentryEventCounter = $this->cacheFactory->create('sentry:' . $event->getLevel());
			$prevValue = (int) $sentryEventCounter->load($eventKey);
			$sentryEventCounter->save($eventKey, $prevValue + 1, [Cache::Expire => '1 day']);

			if ($prevValue > self::EVENT_COUNT_LIMIT) {
				return null;
			}
		} catch (Throwable) {
			// cant save event
		}

		return $event;
	}

	private function getEventKey(Event $event, ?EventHint $hint): string
	{
		$data = [];
		if ($hint !== null && $hint->exception !== null) {
			$data[] = $hint->exception->getFile();
			$data[] = $hint->exception->getLine();
		} else {
			$data[] = $event->getMessage();
		}

		return md5(serialize($data));
	}

}
