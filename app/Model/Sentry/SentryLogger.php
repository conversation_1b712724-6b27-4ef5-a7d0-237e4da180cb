<?php declare(strict_types = 1);

namespace App\Model\Sentry;

use Contributte\Logging\Sentry\SentryLogger as ContributteLogger;
use Contributte\Logging\ILogger;
use Nette\DI\Container;
use Throwable;

/**
 * todo replace with monolog/monolog + SentryHandler
 */
final class SentryLogger
{

	protected ?ContributteLogger $logger = null;

	public function __construct(Container $container)
	{
		try {
			$this->logger = $container->getByType(ContributteLogger::class);
		} catch (Throwable $e) {
			//just trying
		}
	}

	/**
	 * @param mixed $message
	 */
	public function log($message, string $priority = ILogger::INFO): void
	{
		if (isset($this->logger)) {
			$this->logger->log($message, $priority);
		}
	}

	/**
	 * @param string[] $allowedPriority
	 */
	public function setAllowedPriority(array $allowedPriority): void
	{
		if (isset($this->logger)) {
			$this->logger->setAllowedPriority($allowedPriority);
		}
	}

}
