<?php declare(strict_types = 1);

namespace App\Model\Consent;

use App\Exceptions\CurrencyException;
use Brick\Math\RoundingMode;
use Brick\Money\Currency;
use Nette\Http\Request;
use Nette\Http\Session;
use Nette\Http\SessionSection;
use Nette\Utils\Json;
use stdClass;

final class MarketingConsent {

	public const COOKIE_NAME = 'SKcookieConsent';

	private const TYPE_PERSONALIZATION = 'personalization_storage';

	private ?stdClass $data = null;

	private bool $forcePersonalizationGranted = false;

	public function __construct(
		private readonly Request $request,
	)
	{
	}

	private function getCookie(): stdClass
	{
		if ($this->data === null) {
			$this->data = new stdClass();
			try {
				$this->data = Json::decode($this->request->getCookie(self::COOKIE_NAME) ?? '{}');
			} catch (\Throwable) {
				// do nothing
			}
		}
		return $this->data;
	}

	public function forcePersonalizationGranted(): void
	{
		$this->forcePersonalizationGranted = true;
	}

	public function isPersonalizationGranted(): bool
	{
		if ($this->forcePersonalizationGranted) {
			return true;
		}
		return ($this->getCookie()->storages->{self::TYPE_PERSONALIZATION} ?? false) === 'granted';
	}

}
