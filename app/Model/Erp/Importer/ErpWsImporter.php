<?php declare(strict_types = 1);

namespace App\Model\Erp\Importer;

use App\Model\Erp\Connector\Query;
use App\Model\Erp\Processor\Batch\Result;
use App\Model\Orm\ImportCache\ImportCache;
use Nette\InvalidStateException;
use Nette\Utils\Json;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Throwable;

class ErpWsImporter extends CommonImporter
{

	protected string $primaryKey = 'idZbozi';

	protected string|null $configSection = null;

	public function import(string $importType, ?Query $query = null): Result
	{
		if ($this->configSection === null) {
			throw new InvalidStateException('Config section is required.');
		}

		$this->logger = $this->getLogger($importType);
		$items = $this->getDataSource($query);

		$this->logger->info('Import start | type: ' . $importType);
		$this->beforeImport($importType, $query);

		if ($this->config->{$this->configSection}->import->deleteImported ?? true) {
			$this->flushOldImports($importType);
		}

		$createdTime = new DateTimeImmutable();
		$batchSize = $this->config->{$this->configSection}->import->batchLimit ?? 10;

		$count = $errored = $skipped = 0;
		$inserting = [];
		$erpIds = [];

		$insertCallback = function () use (&$inserting) {
			try {
				$this->importCacheMapper->insert($inserting);
			} catch (Throwable $e) {
				$this->logger->error('Inserting rows failed: ' . $e->getMessage());
			}
			$inserting = [];
		};

		foreach ($items as $item) {
			$erpId = $item->{$this->primaryKey} ?? null;

			try {

				if ($erpId === null) {
					$this->logger->warning('Unknown ERP ID.', ['item' => $item]);
					$skipped++;
					continue;
				}

				if ($query !== null && $query->erpIds !== [] && ! in_array($erpId, $query->erpIds)) {
					$skipped++;
					continue;
				}

				$data = Json::encode($item);

				$rowData = [
					'type' => $importType,
					'status' => ImportCache::STATUS_READY,
					'extId' => $erpId,
					'extChecksum' => md5($data),
					'createdTime' => $createdTime,
					'data' => $data,
				];
				$inserting[] = $rowData;

				if ((count($inserting) % $batchSize) === 0) {
					$insertCallback();
				}

				$count++;
				$erpIds[] = $erpId;

			} catch (Throwable $e) {
				$this->logger->error($e->getMessage(), ['erpId' => $erpId]);
				$errored++;
			}
		}

		$insertCallback();

		$result = new Result();
		$result->count = $count;
		$result->erpIds = $erpIds;

		$this->logger->info('Import end.', ['imported' => $count, 'skipped' => $skipped, 'errored' => $errored]);
		$this->logger->info('*****************************');

		return $result;
	}

	protected function beforeImport(string $importType, ?Query $query = null): void
	{
	}

	public function afterImport(Result $result): void
	{
	}

}
