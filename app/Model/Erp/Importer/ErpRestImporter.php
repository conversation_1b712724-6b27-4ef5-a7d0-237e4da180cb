<?php declare(strict_types = 1);

namespace App\Model\Erp\Importer;

use App\Model\Erp\Connector\Query;
use App\Model\Erp\Processor\Batch\Result;
use App\Model\Orm\ImportCache\ImportCache;
use Nette\InvalidStateException;
use Nette\Utils\ArrayHash;
use Nette\Utils\Json;
use Nette\Utils\JsonException;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Throwable;

class ErpRestImporter extends CommonImporter
{

	protected string $primaryKey = 'id';

	protected string|null $configSection = null;

	public function import(string $importType, ?Query $query = null): Result
	{
		if ($this->configSection === null) {
			throw new InvalidStateException('Config section is required.');
		}

		$this->logger = $this->getLogger($importType); //$this->getLoggerByImportType($this->loggerManager, $importType);

		$jsonFileSource = $this->getJsonSource($query);

		$this->logger->info('Import start');

		$this->beforeImport($importType, $query);

		if ($this->config->{$this->configSection}->import->deleteImported ?? true) {
			$this->flushOldImports($importType);
		}

		$createdTime = new DateTimeImmutable();
		$batchSize = $this->config->{$this->configSection}->import->batchLimit ?? 10;

		$count = 0;
		$inserting = [];
		$erpIds = [];

		$insertCallback = function () use (&$inserting) {
			try {
				$this->importCacheMapper->insert($inserting);
			} catch (Throwable $e) {
				$this->logger->error('Inserting rows failed: ' . $e->getMessage());
			}
			$inserting = [];
		};

		$handle = fopen($jsonFileSource, 'r');
		if ($handle) {
			$i = 0;
			while (!feof($handle)) {
				$buffer = fgets($handle, null);
				if ($i === 0 && $buffer !== false && $query?->from !== null) {
					$firstRow = ArrayHash::from(Json::decode($buffer, forceArrays: true));
					if (isset($firstRow->generatedAt)) {
						$this->importCacheTimeModel->storeFromImport($importType, $firstRow);
					}
				}
				if ($i > 0 && $buffer !== false) { // skip first row - {"generatedAt":"2024-01-25 08:24:20","numRecords":"193205"}
					try {
						$row = ArrayHash::from(Json::decode($buffer, forceArrays: true));
						if (isset($row->{$this->primaryKey})) {
							if ($query !== null && $query->hasPassword === true && !isset($row->password)) {
								continue;
							}

							if ($query !== null && $query->erpIds !== [] && !in_array($row->{$this->primaryKey}, $query->erpIds)) {
								continue;
							}

							if ($query !== null && $query->hasImagesPages !== null && isset($row->images_pages) && count($row->images_pages) < 1) {
								continue;
							}

							$dataColumn = Json::encode($row);
							if ($query !== null && $query->contains !== null && !str_contains($dataColumn, $query->contains)) {
								continue;
							}
							$rowData     = [
								'type'        => $importType,
								'data'        => $dataColumn,
								'extId'       => $row->{$this->primaryKey},
								'extChecksum' => md5($dataColumn),
								'createdTime' => $createdTime,
								'status'      => ImportCache::STATUS_READY,
							];
							$inserting[] = $rowData;
							if ((count($inserting) % $batchSize) === 0) {
								$insertCallback();
							}
							$count++;
							$erpIds[] = $row->{$this->primaryKey};
						}
					} catch (JsonException) {
						// if JSON is invalid continue to next row
					}
				}
				$i++;
			}
			fclose($handle);
			$insertCallback();
		}

		$result = new Result();
		$result->count = $count;
		$result->erpIds = $erpIds;

		$this->logger->info('Import end.', ['imported' => $count]);

		if ($this->config->{$this->configSection}->import->deleteSourceFile ?? true) {
			$this->flushImportFile($jsonFileSource);
		}

		return $result;
	}

	protected function beforeImport(string $importType, ?Query $query = null): void
	{
	}

	public function afterImport(Result $result): void
	{
	}

}
