<?php declare(strict_types = 1);

namespace App\Model\Erp\Importer;

use App\Model\Erp\Connector\Query;
use App\Model\Erp\Processor\Batch\Result;
use App\Model\Orm\ImportCache\ImportCache;
use Nette\Utils\FileSystem;
use Nette\Utils\Json;
use Nette\Utils\JsonException;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Prewk\XmlStringStreamer;
use Prewk\XmlStringStreamer\Parser\StringWalker;
use Prewk\XmlStringStreamer\Stream\File;
use Throwable;

class HeurekaReviewImporter extends CommonImporter
{

	public function import(string $importType, ?Query $query = null): Result
	{
		$this->logger = $this->getLoggerByImportType($this->loggerManager, $importType);

		$batchSize = $this->config->review->import->batchLimit ?? 10;
		$createdTime = new DateTimeImmutable();

		$xmlFile = $this->commonConnector->getXmlSource();
		$count = 0;

		$this->logger->info('Import start');
		$deleted = $this->importCacheMapper->deleteByStatus([ImportCache::STATUS_IMPORTED, ImportCache::STATUS_SKIPPED/*, ImportCache::STATUS_ERROR*/], ImportCache::TYPE_HEUREKA_REVIEW);
		$this->logger->info('Import cache table flush', ['deleted' => $deleted]);

		$stream = new File($xmlFile, 1024);
		$parser = new StringWalker();

		$streamer = new XmlStringStreamer($parser, $stream);

		$inserting = [];
		$insertCallback = function () use (&$inserting) {
			try {
				$this->importCacheMapper->insert($inserting);
			} catch (Throwable $e) {
				$this->logger->error('Inserting rows failed: ' . $e->getMessage());
			}
			$inserting = [];
		};

		while ($node = $streamer->getNode()) {
			$xmlNode = simplexml_load_string((string) $node);
			try {
				assert($xmlNode instanceof \SimpleXMLElement);
				$xmlData = [
					'product_name' => (string) $xmlNode->product_name,
					'url' => (string) $xmlNode->url,
					'cena' => (float) $xmlNode->cena,
					'ean' => (string) $xmlNode->ean,
					'productno' => (string) $xmlNode->productno,
					'order_id' => (string) $xmlNode->order_id,
					'reviews' => [],
				];

				$ratingId = null;
				foreach ($xmlNode->reviews->review as $review) {
					$xmlData['reviews'][] = [
						'rating_id' => (int) $review->rating_id,
						'unix_timestamp' => (int) $review->unix_timestamp,
						'rating' => (float) $review->rating,
						'recommends' => isset($review->recommends) ? (int) $review->recommends : null,
						'pros' => isset($review->pros) && strlen(trim((string) $review->pros)) > 0 ? (string) $review->pros : null,
						'summary' => isset($review->summary) && strlen(trim((string) $review->summary)) > 0 ? (string) $review->summary : null,
						'cons' => isset($review->cons) && strlen(trim((string) $review->cons)) > 0 ? (string) $review->cons : null,
					];
					$ratingId = $review->rating_id . '-' . $xmlNode->productno;
				}

				$data = Json::encode($xmlData);

				$rowData     = [
					'type'        => $importType,
					'extChecksum' => md5($data),
					'data'        => $data,
					'extId'       => $ratingId,
					'createdTime' => $createdTime,
					'status'      => ImportCache::STATUS_READY,
				];
				$inserting[] = $rowData;
				if ((count($inserting) % $batchSize) === 0) {
					$insertCallback();
				}

				$count++;
			} catch (JsonException) {
				// if JSON is invalid continue to next row
			}
		}

		$result = new Result();
		$result->count = $count;

		$this->logger->info('Import end.', ['imported' => $count]);

		try {
			FileSystem::delete($xmlFile);
			$this->logger->info('XML file deleted.');
		} catch (Throwable $e) {
			$this->logger->error('XML file could not be deleted.', ['exception' => $e]);
		}

		return $result;
	}

}
