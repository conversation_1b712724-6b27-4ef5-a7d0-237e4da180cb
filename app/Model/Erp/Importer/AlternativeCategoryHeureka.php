<?php declare(strict_types = 1);

namespace App\Model\Erp\Importer;

use App\Model\Erp\Connector\Query;
use App\Model\Erp\Processor\Batch\Result;
use App\Model\Orm\ImportCache\ImportCache;
use App\PostType\Page\Model\Orm\TreeAlternative;
use Closure;
use Nette\Utils\FileSystem;
use Nette\Utils\Json;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Prewk\XmlStringStreamer;
use Prewk\XmlStringStreamer\Parser\StringWalker;
use Prewk\XmlStringStreamer\Stream\File;
use Throwable;

class AlternativeCategoryHeureka extends CommonImporter
{

	public function import(string $importType, ?Query $query = null): Result
	{
		$this->logger = $this->getLoggerByImportType($this->loggerManager, $importType);

		$batchSize = $this->config->alternative_category->import->batchLimit ?? 10;
		$createdTime = new DateTimeImmutable();

		$xmlFile = $this->commonConnector->getXmlSource();
		$count = 0;

		$this->logger->info('Import start');
		$deleted = $this->importCacheMapper->deleteByStatus([ImportCache::STATUS_IMPORTED, ImportCache::STATUS_SKIPPED/*, ImportCache::STATUS_ERROR*/], ImportCache::TYPE_ALTERNATIVE_CATEGORY);
		$this->logger->info('Import cache table flush', ['deleted' => $deleted]);

		$stream = new File($xmlFile, 1024);
		$parser = new StringWalker();

		$streamer = new XmlStringStreamer($parser, $stream);

		$inserting = [];
		$insertCallback = function () use (&$inserting) {
			try {
				$this->importCacheMapper->insert($inserting);
			} catch (Throwable $e) {
				$this->logger->error('Inserting rows failed: ' . $e->getMessage());
			}
			$inserting = [];
		};

		while ($node = $streamer->getNode()) {
			$xmlNode = simplexml_load_string((string) $node);
			assert($xmlNode instanceof \SimpleXMLElement);
			$this->iterateCategories($xmlNode, $importType, $batchSize, $createdTime, $insertCallback, $inserting);
			$count++;
		}

		$result = new Result();
		$result->count = $count;

		$this->logger->info('Import end.', ['imported' => $count]);

		try {
			FileSystem::delete($xmlFile);
			$this->logger->info('XML file deleted.');
		} catch (Throwable $e) {
			$this->logger->error('XML file could not be deleted.', ['exception' => $e]);
		}

		return $result;
	}

	/**
	 * @phpstan-param Closure(): (void) $insertCallback
	 */
	private function iterateCategories(\SimpleXMLElement $xmlNode, string $importType, int $batchSize, DateTimeImmutable $createdTime, Closure $insertCallback, array &$inserting): void
	{
		$xmlData = [
			'type' => TreeAlternative::TYPE_HEUREKA,
			'alt_id' => (string) $xmlNode->CATEGORY_ID,
			'alt_name' => (string) $xmlNode->CATEGORY_NAME,
			'alt_path' => (string) ($xmlNode->CATEGORY_FULLNAME ?? $xmlNode->CATEGORY_NAME),
		];

		$extId = TreeAlternative::TYPE_HEUREKA . '_' . $xmlNode->CATEGORY_ID;

		$data = Json::encode($xmlData);

		$rowData     = [
			'type'        => $importType,
			'extChecksum' => md5($data),
			'data'        => $data,
			'extId'       => $extId,
			'createdTime' => $createdTime,
			'status'      => ImportCache::STATUS_READY,
		];
		$inserting[] = $rowData;
		if ((count($inserting) % $batchSize) === 0) {
			$insertCallback();
		}

		if (isset($xmlNode->CATEGORY)) {
			foreach ($xmlNode->CATEGORY as $category) {
				$this->iterateCategories($category, $importType, $batchSize, $createdTime, $insertCallback, $inserting);
			}
		}
	}

	public function afterImport(Result $result): void
	{
		if (($this->config->alternative_category->import->checkSkipped ?? false) && ($this->config->alternative_category->import->removeSkipped ?? false)) {
			$exists = [];
			foreach ($this->connection->query('SELECT extId, syncChecksum FROM tree_alternative WHERE `type` = %s AND syncTime IS NOT NULL', TreeAlternative::TYPE_HEUREKA)->fetchPairs('extId', 'syncChecksum') as $erpId => $syncChecksum) {
				$exists[] = ['extId' => $erpId, 'extChecksum' => $syncChecksum];
			}

			if ($exists !== []) {
				$this->connection->query('DELETE FROM import_cache WHERE [type] = %s AND [status] = %s AND %multiOr', ImportCache::TYPE_ALTERNATIVE_CATEGORY, ImportCache::STATUS_READY, $exists);
			}
		}
	}

}
