<?php declare(strict_types = 1);

namespace App\Model\Erp\Importer;

use App\Model\ConfigService;
use App\Model\Erp;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Orm;
use App\Model\Orm\Parameter\ParameterModel;
use App\Model\Orm\ParameterValue\ParameterValueModel;
use App\Model\Orm\String\StringModel;
use Nette\Utils\ArrayHash;
use Psr\Log\LoggerInterface;
use Tracy\Dumper;

readonly class ProductParameterImporter
{

	public function __construct(
		protected ConfigService $configService,
		protected Orm $orm,
		protected ParameterModel $parameterModel,
		protected ParameterValueModel $parameterValueModel,
		protected StringModel $stringModel,
		protected MutationsHolder $mutationsHolder,
	)
	{
	}

	public function processResult(ArrayHash $result, LoggerInterface $logger, bool $verboseLog = false): void
	{
		$result = $this->groupResultByProduct($result);

		$mutation = $this->mutationsHolder->getDefault();
		$this->orm->setMutation($mutation);
		$this->orm->setPublicOnly(false);

		$counter = $inserted = $skipped = 0;

		foreach ($result->items as $variantExtId => $parameters) {

			$variant = $this->orm->productVariant->getbyExtId($variantExtId);

			if (isset($variant)) {
				if ($variant->product === null) {
					$list = [];

					foreach ($parameters as $parameter) {
						/** @var Erp\Entity\Parameter $parameter */
						$list[$parameter->extId][] = $parameter->valueExtId;
					}

					$variant->tempParameters = ArrayHash::from($list);
					$this->orm->productVariant->persistAndFlush($variant);
					$inserted++;

					if ($verboseLog) {
						$logger->info(sprintf('OK | SAVED temp Parameters to Variant "%s" | ID: %s', $variant->name, $variant->id), ['data' => Dumper::toText($list)]);
					}

				} else {
					$logger->info(sprintf('SKIPPED | Variant ID: %s "%s" has already been added to Product ID: %s', $variant->name, $variant->id, $variant->product->id));
					$skipped++;
				}

			} else {
				$logger->info(sprintf('SKIPPED | Unknown Variant ERP ID: %s', $variantExtId));
				$skipped++;
			}

			$counter++;
		}

		$logger->info(sprintf('== Total | Counter: %s | New Parameters: %s | Values: %s', $counter, $inserted, $skipped));
	}

	private function groupResultByProduct(ArrayHash $result): ArrayHash
	{
		$list = new ArrayHash();
		$list->items = [];

		foreach ($result as $item) {
			$extParameter = new Erp\Entity\Parameter($item);
			$list->items[strval($extParameter->variantExtId)][] = $extParameter;
		}

		return $list;
	}

}
