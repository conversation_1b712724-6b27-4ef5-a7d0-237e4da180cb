<?php declare(strict_types = 1);

namespace App\Model\Erp\Importer;

use App\Model\Erp\Processor\Batch\Result;
use App\Model\Messenger\Erp\Product\ProductMessage;
use App\Model\Orm\ImportCache\ImportCache;
use Nextras\Dbal\Drivers\Exception\QueryException;

class ProductImporter extends ErpWsImporter
{

	protected string|null $configSection = 'product';

	/**
	 * @throws QueryException
	 */
	public function afterImport(Result $result): void
	{
		if (($this->config->{$this->configSection}->import->checkSkipped ?? false) && ($this->config->{$this->configSection}->import->removeSkipped ?? false)) {
			$exists = [];
			foreach ($this->connection->query('SELECT extId, syncChecksum FROM product WHERE syncTime IS NOT NULL')->fetchPairs('extId', 'syncChecksum') as $erpId => $syncChecksum) {
				$exists[] = ['extId' => $erpId, 'extChecksum' => $syncChecksum];
			}

			if ($exists !== []) {
				$this->connection->query('DELETE FROM import_cache WHERE `type` = %s AND `status` = %s AND %multiOr', ImportCache::TYPE_PRODUCT, ImportCache::STATUS_READY, $exists);
			}
		}

		$this->dispatchReadyMessages();
	}

	/**
	 * @throws QueryException
	 */
	public function dispatchReadyMessages(): void
	{
		foreach ($this->connection->query('SELECT id FROM import_cache WHERE `type`=%s AND `status`=%s', ImportCache::TYPE_PRODUCT, ImportCache::STATUS_READY) as $row) {
			$this->messageBus->dispatch(new ProductMessage($row->id));
			$this->connection->query('UPDATE import_cache SET `status` = %s WHERE id = %i', ImportCache::STATUS_QUEUED, $row->id);
		}
	}

}
