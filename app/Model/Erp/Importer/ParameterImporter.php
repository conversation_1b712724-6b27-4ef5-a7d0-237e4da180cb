<?php declare(strict_types = 1);

namespace App\Model\Erp\Importer;

use App\Model\ConfigService;
use App\Model\Erp;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Orm;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\Parameter\ParameterModel;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\ParameterValue\ParameterValueModel;
use App\Model\Orm\String\StringModel;
use Nette\Utils\ArrayHash;
use Psr\Log\LoggerInterface;
use Tracy\Dumper;

readonly class ParameterImporter
{

	public function __construct(
		protected ConfigService $configService,
		protected Orm $orm,
		protected ParameterModel $parameterModel,
		protected ParameterValueModel $parameterValueModel,
		protected StringModel $stringModel,
		protected MutationsHolder $mutationsHolder,
	)
	{
	}

	public function processResult(ArrayHash $result, LoggerInterface $logger, bool $verboseLog = false): void
	{
		$mutation = $this->mutationsHolder->getDefault();
		$currentParameters = $this->parameterModel->getParameterWithValueByExtIdList();
		$counter = $insertedParameters = $insertedValues = 0;

		foreach ($result as $item) {
			$extParameter = new Erp\Entity\Parameter($item);

			if (isset($currentParameters[$extParameter->extId]) && $currentParameters[$extParameter->extId]['parameter'] instanceof Parameter) {
				/** @var Parameter $parameter */
				$parameter = $currentParameters[$extParameter->extId]['parameter'];

				if ($verboseLog) {
					$logger->info(sprintf('Already IMPORTED Parameter "%s" -> ID: %s | ERP: %s', $parameter->name, $parameter->id, $parameter->extId));
				}

			} else {
				$parameter = $this->parameterModel->crateNewFromErp($extParameter, $mutation);
				$currentParameters[$parameter->extId] = ArrayHash::from([
					'parameter' => $parameter,
					'values' => [],
				]);

				$insertedParameters++;
				$logger->info(sprintf('OK | CREATED new Parameter "%s" | ID: %s', $parameter->name, $parameter->id), ['data' => Dumper::toText($extParameter)]);
			}

			if (isset($currentParameters[$extParameter->extId]['values'][$extParameter->valueExtId]) && $currentParameters[$extParameter->extId]['values'][$extParameter->valueExtId] instanceof ParameterValue) {
				/** @var ParameterValue $parameterValue */
				$parameterValue = $currentParameters[$extParameter->extId]['values'][$extParameter->valueExtId];

				if ($verboseLog) {
					$logger->info(sprintf('Already IMPORTED Value "%s" -> Parameter ID: %s | ERP: %s | %s | Value ID: %s | ERP: %s', $parameterValue->internalValue, $parameter->id, $parameter->extId, $parameter->name, $parameterValue->id, $parameterValue->extId));
				}

			} else {

				$parameterValue = $this->parameterValueModel->crateNewValueFromErp($parameter, $extParameter, $mutation);
				$currentParameters[$parameter->extId]['values'][$parameterValue->extId] = $parameterValue;

				$insertedValues++;
				$logger->info(sprintf('OK | CREATED new Value "%s" | Parameter ID: %s | ERP: %s | %s', $parameterValue->internalValue, $parameter->id, $parameter->extId, $parameter->name), ['data' => Dumper::toText($extParameter)]);
			}

			$counter++;
		}

		$logger->info(sprintf('== Total | Counter: %s | New Parameters: %s | Values: %s', $counter, $insertedParameters, $insertedValues));
	}

}
