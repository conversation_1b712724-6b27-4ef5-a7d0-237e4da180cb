<?php declare(strict_types = 1);

namespace App\Model\Erp\Importer;

use App\Model\Erp\Connector\Query;
use App\Model\Erp\Processor\Batch\Result;
use App\Model\Orm\ImportCache\ImportCache;
use App\PostType\Page\Model\Orm\TreeAlternative;
use Nette\Utils\FileSystem;
use Nette\Utils\Json;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Throwable;

class AlternativeCategoryGoogle extends CommonImporter
{

	public function import(string $importType, ?Query $query = null): Result
	{
		$this->logger = $this->getLoggerByImportType($this->loggerManager, $importType);

		$batchSize = $this->config->alternative_category->import->batchLimit ?? 10;
		$createdTime = new DateTimeImmutable();

		$txtFile = $this->commonConnector->getXmlSource();
		$count = 0;

		$this->logger->info('Import start');
		$deleted = $this->importCacheMapper->deleteByStatus([ImportCache::STATUS_IMPORTED, ImportCache::STATUS_SKIPPED/*, ImportCache::STATUS_ERROR*/], ImportCache::TYPE_ALTERNATIVE_CATEGORY);
		$this->logger->info('Import cache table flush', ['deleted' => $deleted]);

		$inserting = [];
		$insertCallback = function () use (&$inserting) {
			try {
				$this->importCacheMapper->insert($inserting);
			} catch (Throwable $e) {
				$this->logger->error('Inserting rows failed: ' . $e->getMessage());
			}
			$inserting = [];
		};

		foreach ($this->readFile($txtFile) as $row) {
			[$extId, $path] = explode('-', $row);
			$extId = trim($extId);
			$path = trim($path);

			if (is_numeric($extId)) {

				$xmlData = [
					'type'     => TreeAlternative::TYPE_GOOGLE,
					'alt_id'   => $extId,
					'alt_name' => $path,
					'alt_path' => $path,
				];

				$data = Json::encode($xmlData);

				$rowData     = [
					'type'        => $importType,
					'extChecksum' => md5($data),
					'data'        => $data,
					'extId'       => TreeAlternative::TYPE_GOOGLE . '_' . $extId,
					'createdTime' => $createdTime,
					'status'      => ImportCache::STATUS_READY,
				];
				$inserting[] = $rowData;
				if ((count($inserting) % $batchSize) === 0) {
					$insertCallback();
				}
				$count++;
			}
		}

		$result = new Result();
		$result->count = $count;

		$this->logger->info('Import end.', ['imported' => $count]);

		try {
			FileSystem::delete($txtFile);
			$this->logger->info('XML file deleted.');
		} catch (Throwable $e) {
			$this->logger->error('XML file could not be deleted.', ['exception' => $e]);
		}

		return $result;
	}

	private function readFile(string $filePath): iterable
	{
		$handle = fopen($filePath, 'r');
		assert(is_resource($handle));
		while (!feof($handle)) {
			if (($row = fgets($handle)) === false) {
				break;
			}
			yield trim($row);
		}

		fclose($handle);
	}
	public function afterImport(Result $result): void
	{
		if (($this->config->alternative_category->import->checkSkipped ?? false) && ($this->config->alternative_category->import->removeSkipped ?? false)) {
			$exists = [];
			foreach ($this->connection->query('SELECT extId, syncChecksum FROM tree_alternative WHERE `type` = %s AND syncTime IS NOT NULL', TreeAlternative::TYPE_GOOGLE)->fetchPairs('extId', 'syncChecksum') as $erpId => $syncChecksum) {
				$exists[] = ['extId' => $erpId, 'extChecksum' => $syncChecksum];
			}

			if ($exists !== []) {
				$this->connection->query('DELETE FROM import_cache WHERE [type] = %s AND [status] = %s AND %multiOr', ImportCache::TYPE_ALTERNATIVE_CATEGORY, ImportCache::STATUS_READY, $exists);
			}
		}
	}

}
