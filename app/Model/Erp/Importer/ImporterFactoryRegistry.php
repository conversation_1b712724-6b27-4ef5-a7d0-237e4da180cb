<?php

declare(strict_types=1);

namespace App\Model\Erp\Importer;

use function array_values;
use function sprintf;

final class ImporterFactoryRegistry
{

	/** @var array<string, ImporterFactory> */
	private array $factories = [];

	/**
	 * @param ImporterFactory[] $factories
	 */
	public function __construct(
		array $factories,
	)
	{
		foreach ($factories as $factory) {
			[$name] = explode('@', $factory::class);
			$this->factories[$name] = $factory;
		}
	}

	/**
	 * @return list<ImporterFactory>
	 */
	public function list(): array
	{
		return array_values($this->factories);
	}

	/**
	 * @return ImporterFactory[]
	 */
	public function toArray(): array
	{
		return $this->factories;
	}

	public function get(string $factoryName): ImporterFactory
	{
		return $this->factories[$factoryName] ?? throw new \InvalidArgumentException(sprintf('Importer factory with name "%s" not found.', $factoryName));
	}

}
