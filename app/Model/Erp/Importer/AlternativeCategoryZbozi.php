<?php declare(strict_types = 1);

namespace App\Model\Erp\Importer;

use App\Model\Erp\Connector\Query;
use App\Model\Erp\Processor\Batch\Result;
use App\Model\Orm\ImportCache\ImportCache;
use App\PostType\Page\Model\Orm\TreeAlternative;
use Closure;
use Nette\Utils\FileSystem;
use Nette\Utils\Json;
use Nette\Utils\JsonException;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Throwable;

class AlternativeCategoryZbozi extends CommonImporter
{

	public function import(string $importType, ?Query $query = null): Result
	{
		$this->logger = $this->getLoggerByImportType($this->loggerManager, $importType);

		$batchSize = $this->config->alternative_category->import->batchLimit ?? 10;
		$createdTime = new DateTimeImmutable();

		$jsonFileSource = $this->getJsonSource($query);
		$count = 0;

		$this->logger->info('Import start');
		$deleted = $this->importCacheMapper->deleteByStatus([ImportCache::STATUS_IMPORTED, ImportCache::STATUS_SKIPPED/*, ImportCache::STATUS_ERROR*/], ImportCache::TYPE_ALTERNATIVE_CATEGORY);
		$this->logger->info('Import cache table flush', ['deleted' => $deleted]);

		$inserting = [];
		$insertCallback = function () use (&$inserting) {
			try {
				$this->importCacheMapper->insert($inserting);
			} catch (Throwable $e) {
				$this->logger->error('Inserting rows failed: ' . $e->getMessage());
			}
			$inserting = [];
		};

		try {
			$jsonContent = file_get_contents($jsonFileSource);
			assert($jsonContent !== false);
			$json = Json::decode($jsonContent, forceArrays: true);

			foreach ($json as $row) {
				$this->iterateCategories($row, $importType, $batchSize, $createdTime, $insertCallback, $inserting);
				$count++;
			}
		} catch (JsonException $e) {
			// do nothing
		}

		$result = new Result();
		$result->count = $count;

		$this->logger->info('Import end.', ['imported' => $count]);

		try {
			FileSystem::delete($jsonFileSource);
			$this->logger->info('XML file deleted.');
		} catch (Throwable $e) {
			$this->logger->error('XML file could not be deleted.', ['exception' => $e]);
		}

		return $result;
	}
	/**
	 * @phpstan-param Closure(): (void) $insertCallback
	 */
	private function iterateCategories(array $row, string $importType, int $batchSize, DateTimeImmutable $createdTime, Closure $insertCallback, array &$inserting): void
	{
		if (isset($row['id'])) {
			$xmlData = [
				'type'     => TreeAlternative::TYPE_ZBOZI,
				'alt_id'   => $row['id'],
				'alt_name' => $row['name'] ?? null,
				'alt_path' => $row['categoryText'] ?? null,
			];

			$extId = TreeAlternative::TYPE_ZBOZI . '_' . $row['id'];

			$data = Json::encode($xmlData);

			$rowData = [
				'type'        => $importType,
				'extChecksum' => md5($data),
				'data'        => $data,
				'extId'       => $extId,
				'createdTime' => $createdTime,
				'status'      => ImportCache::STATUS_READY,
			];

			$inserting[] = $rowData;
			if ((count($inserting) % $batchSize) === 0) {
				$insertCallback();
			}
		}
		if (isset($row['children'])) {
			foreach ($row['children'] as $category) {
				$this->iterateCategories($category, $importType, $batchSize, $createdTime, $insertCallback, $inserting);
			}
		}
	}

	public function afterImport(Result $result): void
	{
		if (($this->config->alternative_category->import->checkSkipped ?? false) && ($this->config->alternative_category->import->removeSkipped ?? false)) {
			$exists = [];
			foreach ($this->connection->query('SELECT extId, syncChecksum FROM tree_alternative WHERE `type` = %s AND syncTime IS NOT NULL', TreeAlternative::TYPE_ZBOZI)->fetchPairs('extId', 'syncChecksum') as $erpId => $syncChecksum) {
				$exists[] = ['extId' => $erpId, 'extChecksum' => $syncChecksum];
			}

			if ($exists !== []) {
				$this->connection->query('DELETE FROM import_cache WHERE [type] = %s AND [status] = %s AND %multiOr', ImportCache::TYPE_ALTERNATIVE_CATEGORY, ImportCache::STATUS_READY, $exists);
			}
		}
	}

}
