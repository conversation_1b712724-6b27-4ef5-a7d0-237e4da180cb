<?php declare(strict_types = 1);

namespace App\Model\Erp\Importer;

use App\Model\Erp\Processor\Batch\Result;
use App\Model\Messenger\Erp\Stock\StockMessage;
use App\Model\Orm\ImportCache\ImportCache;
use Nextras\Dbal\Drivers\Exception\QueryException;

class StockImporter extends ErpWsImporter
{

	protected string|null $configSection = 'stock';

	/**
	 * @throws QueryException
	 */
	public function afterImport(Result $result): void
	{
		foreach ($this->connection->query('SELECT id FROM import_cache WHERE `type`=%s AND `status`=%s', ImportCache::TYPE_STOCK, ImportCache::STATUS_READY) as $row) {
			$this->messageBus->dispatch(new StockMessage($row->id));
			$this->connection->query('UPDATE import_cache SET `status` = %s WHERE id = %i', ImportCache::STATUS_QUEUED, $row->id);
		}

		$this->handleVariantsIsOld($result);
	}

	private function handleVariantsIsOld(Result $result): void
	{
		$currentExtIdsList = $this->productVariantMapper->getExtIdList();
		$toOldList = array_diff($currentExtIdsList, $result->erpIds);

		if (count($toOldList) > 0) {
			$variants = $this->productVariantRepository->findBy([
				'extId' => $toOldList,
			]);

			foreach ($variants as $variant) {
				$variant->isOld = true;
				$this->productVariantRepository->persistAndFlush($variant);
				$this->logger->info('Variant has been set as OLD', ['ID' => $variant->id, 'erpId' => $variant->extId]);
			}

		} else {
			$this->logger->info('No OLD Variants');
		}
	}

}
