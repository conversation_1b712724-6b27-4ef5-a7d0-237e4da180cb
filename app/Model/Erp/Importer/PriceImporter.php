<?php declare(strict_types = 1);

namespace App\Model\Erp\Importer;

use App\Model\Erp\Processor\Batch\Result;
use App\Model\Messenger\Erp\Price\PriceMessage;
use App\Model\Orm\ImportCache\ImportCache;
use Nextras\Dbal\Drivers\Exception\QueryException;

class PriceImporter extends ErpWsImporter
{

	protected string|null $configSection = 'price';

	/**
	 * @throws QueryException
	 */
	public function afterImport(Result $result): void
	{
		foreach ($this->connection->query('SELECT id FROM import_cache WHERE `type`=%s AND `status`=%s', ImportCache::TYPE_PRICE, ImportCache::STATUS_READY) as $row) {
			$this->messageBus->dispatch(new PriceMessage($row->id));
			$this->connection->query('UPDATE import_cache SET `status` = %s WHERE id = %i', ImportCache::STATUS_QUEUED, $row->id);
		}
	}

}
