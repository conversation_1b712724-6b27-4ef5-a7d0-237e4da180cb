<?php declare(strict_types = 1);

namespace App\Model\Erp\Helper;

use App\Model\Orm\DeliveryMethod\CzechPost;
use App\Model\Orm\DeliveryMethod\CzechPostPickup;
use App\Model\Orm\DeliveryMethod\DeliveryMethod;
use App\Model\Orm\DeliveryMethod\DPD;
use App\Model\Orm\DeliveryMethod\DPDPickup;
use App\Model\Orm\DeliveryMethod\PPL;
use App\Model\Orm\DeliveryMethod\PPLPickup;
use App\Model\Orm\DeliveryMethod\Zasilkovna;
use App\Model\Orm\DeliveryMethod\ZasilkovnaPickup;

class NovikoTransportHelper
{

	public const int NOVIKO_ID_ZASILKOVNA = 41; // Zásilkovna
	public const int NOVIKO_ID_DPD = 6; // DPD
	public const int NOVIKO_ID_PPL = 50; // PPL
	public const int NOVIKO_ID_CESKA_POSTA = 12; // Ceska posta, Balikovna
	public const int NOVIKO_ID_DEFAULT = self::NOVIKO_ID_DPD; // DPD

	public static function getNovikoId(DeliveryMethod $deliveryMethod): int
	{
		return match ($deliveryMethod::class) {
			Zasilkovna::class, ZasilkovnaPickup::class => self::NOVIKO_ID_ZASILKOVNA,
			PPL::class, PPLPickup::class => self::NOVIKO_ID_PPL,
			DPD::class, DPDPickup::class => self::NOVIKO_ID_DPD,
			CzechPost::class, CzechPostPickup::class => self::NOVIKO_ID_CESKA_POSTA,
			default => throw new \LogicException('Unknown delivery method'),
		};
	}

}
