<?php declare(strict_types=1);

namespace App\Model\Erp\Processor\Reader;

use App\Model\CustomField\CustomFields;
use App\Model\Erp\Exception\SkippedException;
use App\Model\Mutation\MutationHolder;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\ImportCache\ImportCache;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductReview\ProductReview;
use App\Model\Orm\ProductReview\ProductReviewModel;
use Nette\Utils\ArrayHash;
use Nette\Utils\DateTime;
use Nette\Utils\Json;
use Nette\Utils\Strings;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Psr\Log\LoggerInterface;

final readonly class HeurekaReviewReader implements BaseReviewReader
{

	public function __construct(
		private Orm $orm,
		private MutationsHolder $mutationsHolder,
		private MutationHolder $mutationHolder,
		private CustomFields $customFields,
	)
	{
	}


	public function read(ImportCache $importCache, LoggerInterface $logger): void
	{
	}

	public function readReview(ImportCache $importCache, LoggerInterface $logger, array &$images = []): void
	{
		/*assert($importCache->type === ImportCache::TYPE_REVIEW);

		$defaultMutation = $this->mutationsHolder->getDefault();
		$this->mutationHolder->setMutation($defaultMutation);

		$this->orm->setMutation($defaultMutation);

		if (($review = $this->orm->review->getByExtId($importCache->data->id)) === null) {
			$review = new Review();
			$this->orm->review->attach($review);
			$review->extId = $importCache->data->id;
		}

		$updated = new DateTimeImmutable($importCache->data->updated);

		if ($review->isPersisted() && $review->updated->getTimestamp() === $updated->getTimestamp()) {
			throw new SkippedException('No updates.');
		}

		$review->name = $importCache->data->name;
		$review->perex = $importCache->data->perex;
		$review->url = $importCache->data->url;
		$review->date = new DateTimeImmutable($importCache->data->date);
		$review->updated = $updated;
		$review->author = $importCache->data->author;

		if ($review->libraryImage !== null) {
			$image = $review->libraryImage;
			$review->libraryImage = null;
			$this->orm->libraryImage->remove($image);
		}

		if ($review->products->count() > 0) {
			foreach ($review->products as $product) {
				$review->products->remove($product);
			}
		}

		$this->orm->review->persistAndFlush($review);

		$e = explode('?', $importCache->data->image);
		$hash = end($e);

		if (isset($images[$hash])) {
			if ($review->libraryImage === null || ($review->libraryImage !== null && $review->libraryImage->md5 !== $hash)) {
				$review->libraryImage = $images[$hash];
			}
		} else {
			$libraryImage = $this->libraryImageModel->addFromUrl(
				url: $importCache->data->image,
				cat: self::LIBRARY_TREE_CATEGORY,
				md5: $hash
			);
			if ($libraryImage->isPersisted()) {
				$review->libraryImage = $libraryImage;
				$images[$hash] = $libraryImage->id;
			}
		}

		$products = $this->orm->product->findBy(['extId' => (array) $importCache->data->products]);

		foreach ($products as $product) {
			$review->products->add($product);
		}

		$this->orm->persistAndFlush($review);*/
	}

	public function readHeurekaReview(ImportCache $importCache, LoggerInterface $logger): void
	{
		assert($importCache->type === ImportCache::TYPE_HEUREKA_REVIEW);

		$defaultMutation = $this->mutationsHolder->getDefault();
		$this->mutationHolder->setMutation($defaultMutation);
		$this->orm->setMutation($defaultMutation);

		if ($this->orm->importCache->findBy([
				'type'   => $importCache->type,
				'status' => [ImportCache::STATUS_READY],
				'extId'  => $importCache->extId,
			])->countStored() > 0) {
			throw new SkippedException('Newer import pending.');
		}

		$productReview = $this->orm->productReview->getBy(['importCode' => $importCache->extId]);

		if ($productReview?->syncChecksum === $this->createChecksum($importCache->data)) {
			throw new SkippedException('Checksum is same.');
		}

		$this->doImport($importCache, $defaultMutation, $productReview);
	}

	private function createChecksum(ArrayHash $data): string
	{
		return md5(Json::encode($data));
	}

	private function findProduct(ImportCache $importCache): ?Product
	{
		if ($product = $this->orm->product->getByExtCode($importCache->data->productno)) {
			return $product;
		}

		if ($productVariant = $this->orm->productVariant->getBy(['ean' => $importCache->data->ean])) {
			return $productVariant->product;
		}

		return null;
	}

	public function getArrayOfString(?string $string): array
	{
		if (empty($string)) {
			return [];
		}

		$string = explode(PHP_EOL, $string);

		foreach ($string as $index => &$pro) {
			$string[$index] = Strings::trim($pro);
		}

		return $string;
	}

	private function doImport(ImportCache $importCache, Mutation $mutation, ?ProductReview $productReview = null): void
	{
		$productEntity = $this->findProduct($importCache);
		if ($productEntity === null) {
			throw new SkippedException('Product not found.');
		}

		foreach ($importCache->data->reviews as $review) {
			if ($productReview === null) {
				$productReview                = new ProductReview();
				$productReview->isFromHeureka = true;

				$productReview->status = ProductReview::STATUS_APPROVED;
				$productReview->source = ProductReview::SOURCE_HEUREKA;
				$productReview->public = true;
				$productReview->importCode = $importCache->extId;
			}

			$order = $this->orm->order->getBy(['orderNumber' => $importCache->data->order_id]);

			$productReview->user    = $order?->user;
			$productReview->email   = $order?->email;
			$productReview->created = DateTimeImmutable::createFromInterface(DateTime::from($review->unix_timestamp)); // @phpstan-ignore-line

			$productReview->product = $productEntity;
			$productReview->name = $importCache->data->product_name;

			$productReview->stars = $review->rating;
			$productReview->text = $review->summary;
			$productReview->isSimple = empty($this->getArrayOfString($review->pros)) && empty($this->getArrayOfString($review->cons));
			$productReview->setCf(
				$this->customFields->prepareDataToSave(
					Json::encode(
						ProductReviewModel::buildCustomFields($this->getArrayOfString($review->pros), $this->getArrayOfString($review->cons))
					)
				)
			);

			$productReview->syncTime = new DateTimeImmutable();
			$productReview->syncChecksum = $this->createChecksum($importCache->data);

			$this->orm->persistAndFlush($productReview);
		}

		$productEntity->reviewAverage = $this->orm->productReview->getStatistic($productEntity)->fetch()->average ?? 0.0;
		$this->orm->persistAndFlush($productEntity);
	}

}
