<?php declare(strict_types=1);

namespace App\Model\Erp\Processor\Reader;

use App\Model\DTO\ShopReview\HeurekaShopReviewDto;
use App\Model\Erp\Exception\SkippedException;
use App\Model\Mutation\MutationHolder;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\ImportCache\ImportCache;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\ShopReview\ShopReview;
use Nette\Utils\ArrayHash;
use Nette\Utils\Json;
use Nette\Utils\Strings;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Psr\Log\LoggerInterface;

final readonly class HeurekaShopReviewReader implements BaseReviewReader
{

	public function __construct(
		private Orm $orm,
		private MutationsHolder $mutationsHolder,
		private MutationHolder $mutationHolder,
	)
	{
	}


	public function read(ImportCache $importCache, LoggerInterface $logger): void
	{
	}

	public function readReview(ImportCache $importCache, LoggerInterface $logger, array &$images = []): void
	{
	}

	public function readHeurekaReview(ImportCache $importCache, LoggerInterface $logger): void
	{
		assert($importCache->type === ImportCache::TYPE_HEUREKA_SHOP_REVIEW);

		$defaultMutation = $this->mutationsHolder->getDefault();
		$this->mutationHolder->setMutation($defaultMutation);
		$this->orm->setMutation($defaultMutation);

		if ($this->orm->importCache->findBy([
				'type'   => $importCache->type,
				'status' => [ImportCache::STATUS_READY],
				'extId'  => $importCache->extId,
			])->countStored() > 0) {
			throw new SkippedException('Newer import pending.');
		}

		$shopReview = $this->orm->shopReview->getBy(['importCode' => $importCache->extId]);

		if ($shopReview?->syncChecksum === $this->createChecksum($importCache->data)) {
			throw new SkippedException('Checksum is same.');
		}

		$this->doImport($importCache, $defaultMutation, $shopReview);
	}

	private function createChecksum(ArrayHash $data): string
	{
		return md5(Json::encode($data));
	}

	public function getArrayOfString(?string $string): array
	{
		if (empty($string)) {
			return [];
		}

		$string = explode(PHP_EOL, $string);

		foreach ($string as $index => &$pro) {
			$string[$index] = Strings::trim($pro);
		}

		return $string;
	}

	private function doImport(ImportCache $importCache, Mutation $mutation, ?ShopReview $shopReview = null): void
	{
		$reviewData = $importCache->data;
		$dto = HeurekaShopReviewDto::fromArray((array) $reviewData);

		if ($shopReview === null) {
			$shopReview = new ShopReview();
			$shopReview->importCode = $importCache->extId;
			$shopReview->extId = $importCache->extId;
			$shopReview->reviewData = Json::encode($reviewData);
			$shopReview->date = DateTimeImmutable::createFromTimestamp($dto->unixTimestamp);
			$shopReview->createdAt = DateTimeImmutable::createFromTimestamp(time());
			$shopReview->mutation = $mutation;
			$shopReview->source = ShopReview::SOURCE_HEUREKA;
		}

		$shopReview->syncTime = new DateTimeImmutable();
		$shopReview->syncChecksum = $this->createChecksum($importCache->data);

		$this->orm->persistAndFlush($shopReview);
	}

}
