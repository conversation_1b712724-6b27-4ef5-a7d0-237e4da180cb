<?php
declare(strict_types = 1);

namespace App\Model\Erp\Processor\Reader;

use App\Model\Orm\ImportCache\ImportCache;
use Psr\Log\LoggerInterface;

interface BaseReviewReader extends Reader
{

	public function read(ImportCache $importCache, LoggerInterface $logger): void;

	public function readReview(ImportCache $importCache, LoggerInterface $logger, array &$images = []): void;

	public function readHeurekaReview(ImportCache $importCache, LoggerInterface $logger): void;

}
