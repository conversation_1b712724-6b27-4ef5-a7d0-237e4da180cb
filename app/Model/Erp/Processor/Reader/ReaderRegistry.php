<?php

declare(strict_types=1);

namespace App\Model\Erp\Processor\Reader;

use function array_values;
use function sprintf;

final class ReaderRegistry
{

	/** @var array<string, Reader> */
	private array $readers = [];

	/**
	 * @param Reader[] $readers
	 */
	public function __construct(
		array $readers,
	)
	{
		foreach ($readers as $reader) {
			$this->readers[$reader::class] = $reader;
		}
	}

	/**
	 * @return list<Reader>
	 */
	public function list(): array
	{
		return array_values($this->readers);
	}

	/**
	 * @return Reader[]
	 */
	public function toArray(): array
	{
		return $this->readers;
	}

	public function get(string $readerName): Reader|BaseReviewReader
	{
		return $this->readers[$readerName] ?? throw new \InvalidArgumentException(sprintf('Reader with name "%s" not found.', $readerName));
	}

}
