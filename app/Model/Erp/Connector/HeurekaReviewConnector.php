<?php declare(strict_types = 1);

namespace App\Model\Erp\Connector;

use App\Console\Erp\CommandType;
use App\Model\Erp\Exception\LoggedException;
use Contributte\Monolog\LoggerManager;
use Nette\Utils\ArrayHash;

class HeurekaReviewConnector extends XmlConnector implements ReadCollection
{

	protected function startup(LoggerManager $loggerManager): void
	{
		if ($this->config->connectors->rest->verboseLog) {
			$this->logger = $loggerManager->get(CommandType::HEUREKA_REVIEW_IMPORT);
		}
		parent::startup($loggerManager);
	}

	public function getRows(Query $query): ArrayHash
	{
		return new ArrayHash();
	}


	public function getJsonSource(Query $query): ?string
	{
		return null;
	}

	public function getXmlSource(): string
	{
		if (!$xmlUrl = $this->configService->get('mutations', 'cs', 'heurekaReviewXmlUrl')) {
			throw new LoggedException('Heureka review url missing.');
		}
		return $this->downloadXml($xmlUrl);
	}

}
