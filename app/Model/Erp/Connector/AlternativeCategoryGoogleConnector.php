<?php declare(strict_types = 1);

namespace App\Model\Erp\Connector;

use App\Console\Erp\CommandType;
use Contributte\Monolog\LoggerManager;
use Nette\Utils\ArrayHash;

class AlternativeCategoryGoogleConnector extends XmlConnector implements ReadCollection
{

	protected function startup(LoggerManager $loggerManager): void
	{
		if ($this->config->connectors->rest->verboseLog) {
			$this->logger = $loggerManager->get(CommandType::ALTERNATIVE_CATEGORY_IMPORT);
		}
		parent::startup($loggerManager);
	}

	public function getRows(Query $query): ArrayHash
	{
		return new ArrayHash();
	}


	public function getJsonSource(Query $query): ?string
	{
		return null;
	}

	public function getXmlSource(): string
	{
		return $this->downloadXml('https://www.google.com/basepages/producttype/taxonomy-with-ids.cs-CZ.txt');
	}

}
