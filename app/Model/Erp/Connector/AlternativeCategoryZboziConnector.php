<?php declare(strict_types = 1);

namespace App\Model\Erp\Connector;

use App\Console\Erp\CommandType;
use Contributte\Monolog\LoggerManager;
use Nette\Utils\ArrayHash;

class AlternativeCategoryZboziConnector extends XmlConnector implements ReadCollection
{

	protected function startup(LoggerManager $loggerManager): void
	{
		if ($this->config->connectors->rest->verboseLog) {
			$this->logger = $loggerManager->get(CommandType::ALTERNATIVE_CATEGORY_IMPORT);
		}
		parent::startup($loggerManager);
	}

	public function getRows(Query $query): ArrayHash
	{
		return new ArrayHash();
	}


	public function getJsonSource(?Query $query = null): string
	{
		return $this->downloadSource('GET', 'https://www.zbozi.cz/static/categories.json');
	}

	public function getXmlSource(): ?string
	{
		return null;
	}

}
