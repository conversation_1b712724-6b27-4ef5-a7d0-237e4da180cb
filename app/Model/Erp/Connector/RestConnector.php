<?php declare(strict_types = 1);

namespace App\Model\Erp\Connector;

use App\Model\Erp\Exception\LoggedException;
use GuzzleHttp\Client;
use Nette\Caching\Cache;
use Nette\Utils\Json;
use Nette\Utils\Strings;
use Psr\Http\Message\ResponseInterface;
use Throwable;

abstract class RestConnector extends BaseConnector
{

	protected function authenticate(): ?string
	{
		$cache = $this->cacheFactory->create(self::class);

		return $cache->load('token', function (&$dependencies) {
			$dependencies[Cache::Expire] = $this->config['connectors']['rest']['tokenExpire'] ?? '5 minutes';

			$client = new Client();
			$path = $this->config['connectors']['rest']['endpoint'] . '/login';

			$formData = [
				'username' => $this->config['connectors']['rest']['credentials']['username'],
				'password' => $this->config['connectors']['rest']['credentials']['password'],
			];

			$options = ['form_params' => $formData];

			try {
				$response = $client->request('POST', $path, $options);
				$res = Json::decode((string) $response->getBody(), forceArrays: true);
				$token = $res['token'] ?? null;

				$this->logger->info('API call - generating new access token: ' . $token);

			} catch (Throwable $e) {
				$this->logger->error('Getting token failed: ' . $e->getMessage());
				throw new LoggedException($e->getMessage(), $e->getCode(), $e);
			}

			return $token ?? null;
		});
	}

	/**
	 * @throws LoggedException
	 */
	protected function callApi(string $method, string $path, array $params = []): ?string
	{
		$originalPath = $path;

		$fileNameCallback = function (string $path, array $params) use ($originalPath) {
			if ($params === []) {
				return getmypid() . '_' . $originalPath . '.json';
			}
			return getmypid() . '_' . $originalPath . '_' . Strings::webalize($params['formParams']['dateFrom'] ?? 'default') . '.json';
		};

		return $this->downloadSource($method, $this->config['connectors']['rest']['endpoint'] . '/' . $path, $params, $fileNameCallback, $this->getOptionsCallback(...));
	}

	protected function sendApi(string $method, string $path, array $params = []): ResponseInterface
	{
		return $this->doRequest($method, $this->config['connectors']['rest']['endpoint'] . '/' . $path, $this->getOptionsCallback($method, $path, $params));
	}

	private function getOptionsCallback(string $method, string $path, array $params): array
	{
		$options = [];
		if (count($params) > 0) {
			if ($method === 'get') {
				$options['query'] = $params;
			} else {
				if (isset($params['formParams'])) {
					$options['form_params'] = $params['formParams'];
				} else {
					$options['body'] = json_encode($params, JSON_UNESCAPED_UNICODE);
				}
			}
		}

		$options['headers']['Authorization'] = $this->authenticate();

		return $options;
	}

}
