<?php declare(strict_types = 1);

namespace App\Model\Erp\Connector;

abstract class XmlConnector extends BaseConnector
{

	public function downloadXml(string $xmlUrl, string $method = 'GET'): string
	{
		return $this->downloadSource($method, $xmlUrl, [], function (string $path) {
			$pathinfo = pathinfo($path);
			return getmypid() . '_ts-' . time() . '_' . $pathinfo['filename'] . (isset($pathinfo['extension']) ? '.' . $pathinfo['extension'] : '');
		});
	}

}
