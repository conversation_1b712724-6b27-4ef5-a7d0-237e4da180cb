<?php declare(strict_types = 1);

namespace App\Model\Erp\Connector;

use App\Model\CacheFactory;
use App\Model\ConfigService;
use App\Model\Erp\Exception\LoggedException;
use App\Model\Sentry\SentryLogger;
use Closure;
use Composer\CaBundle\CaBundle;
use Contributte\Monolog\LoggerManager;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Psr7\Utils;
use GuzzleHttp\RequestOptions;
use InvalidArgumentException;
use Nette\Http\UrlScript;
use Nette\Utils\ArrayHash;
use Nette\Utils\FileSystem;
use Nette\Utils\Strings;
use Psr\Http\Message\ResponseInterface;
use Psr\Log\LoggerInterface;
use Psr\Log\NullLogger;
use Throwable;
use Tracy\ILogger;

abstract class BaseConnector
{

	protected ?Client $client = null;
	protected const IMPORT_DIR = TEMP_DIR . '/import/';

	protected ArrayHash $config;

	protected LoggerInterface $logger;

	public function __construct(
		protected readonly ConfigService $configService,
		protected readonly SentryLogger $sentryLogger,
		protected readonly CacheFactory $cacheFactory,
		private readonly LoggerManager $loggerManager,
	)
	{
		$this->config = ArrayHash::from($this->configService->get('erp'));
		$this->logger = new NullLogger();

		if ( ! isset($this->config['connectors']['rest'])) {
			throw new InvalidArgumentException('Unknown REST connector.');
		}

		$this->startup($this->loggerManager);
	}

	protected function startup(LoggerManager $loggerManager): void
	{
		//$this->logger->info('====================================================');
	}

	/**
	 * @phpstan-param Closure(string $path, array $params): string $fileNameCallback
	 * @phpstan-param Closure(string $method, string $path, array $params): array $optionsCallback
	 */
	public function downloadSource(
		string $method,
		string $path,
		array $params = [],
		?Closure $fileNameCallback = null,
		?Closure $optionsCallback = null
	): string
	{
		$method = Strings::lower($method);
		$log = ['method' => $method, 'path' => $path];

		//$this->logger->info('API call', $log);

		$options = $optionsCallback !== null ? $optionsCallback($method, $path, $params) : [];

		if (isset($this->config['headers']) && is_array($this->config['headers'])) {
			foreach ($this->config['headers'] as $name => $header) {
				$options['headers'][$name] = $header;
			}
		}

		try {
			FileSystem::createDir(self::IMPORT_DIR);
			$urlPath = new UrlScript($path);
			$fileName = $fileNameCallback !== null ? $fileNameCallback((string) $urlPath->withQuery([]), $params) : getmypid() . '_ts-' . time() . '_' . md5(serialize($method . $path . json_encode($params)));
			$filePath = self::IMPORT_DIR . $fileName;

			// stream response file to resource
			$resource = Utils::tryFopen($filePath, 'w');
			$options['sink'] = $resource;

			$response = $this->doRequest($method, $path, $options);
			$this->logger->info('API response code: ' . $response->getStatusCode());
			$this->logger->info('API response content length: ' . $response->getBody()->getSize());
			$this->logger->info('Response file name: ' . $fileName);

		} catch (Throwable $e) {
			$log['code'] = $e->getCode();
			$log['response'] = $e instanceof ClientException ? (string) $e->getResponse()->getBody() : $e->getMessage();
			$this->logger->error('API call error.', $log);
			$this->sentryLogger->log($e, ILogger::EXCEPTION);

			throw new LoggedException($e->getMessage(), $e->getCode(), $e);
		}

		return $filePath;
	}

	protected function getClient(): Client
	{
		if ($this->client === null) {
			$this->client = new Client([
				RequestOptions::VERIFY => CaBundle::getSystemCaRootBundlePath(),
			]);
		}
		return $this->client;
	}

	/**
	 * @param string $method
	 * @param string $uri
	 * @param array $options
	 * @return ResponseInterface
	 * @throws GuzzleException
	 */
	protected function doRequest(string $method, string $uri, array $options = []): ResponseInterface
	{
		return $this->getClient()->request($method, $uri, $options);
	}

}
