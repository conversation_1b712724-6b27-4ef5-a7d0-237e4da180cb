<?php declare(strict_types = 1);

namespace App\Model\Erp\Connector;

use App\Model\CacheFactory;
use App\Model\ConfigService;
use App\Model\Erp\Entity\ErpOrder;
use App\Model\Erp\Exception\LoggedException;
use App\Model\Sentry\SentryLogger;
use Contributte\Monolog\LoggerManager;
use Nette\Utils\ArrayHash;
use Psr\Log\LoggerInterface;
use Psr\Log\NullLogger;
use SoapClient;
use SoapFault;
use stdClass;
use Throwable;
use Tracy\ILogger;

abstract class WsConnector
{

	public const string METHOD_CATEGORY_GLOBAL = 'global';
	public const string METHOD_CATEGORY_PRODUCT = 'product';
	public const string METHOD_CATEGORY_ORDER = 'order';

	public const string METHOD_GET_VERSION = 'getVersion';
	public const string METHOD_GET_ZBOZI_DETAIL_LIST = 'getZboziDetailList';
	public const string METHOD_GET_ZBOZI_PARAMETRY_LIST = 'getZboziParametryList';
	public const string METHOD_GET_ZBOZI_AKCE_LIST = 'getZboziAkceList';
	public const string METHOD_GET_ZBOZI_CS = 'getZboziCS';
	public const string METHOD_GET_ZBOZI_STAV = 'getZboziStav';
	public const string METHOD_GET_ZBOZI_FOTO = 'getZboziFoto';
	public const string METHOD_SAVE_OBJEDNAVKA_HD = 'saveObjednavkaHD';
	public const string METHOD_GET_OBJEDNAVKA_HD = 'getObjednavkaHD';
	public const string METHOD_GET_OBJEDNAVKA_ODB = 'getObjednavkaOdb';
	public const string METHOD_UPDATE_HLAVICKA_OBJEDNAVKA_HD = 'updateHlavickaObjednavkaHD';
	public const string METHOD_STORNO_OBJEDNAVKA_HD = 'stornoObjednavkaHD';

	public const array METHODS = [
		self::METHOD_CATEGORY_GLOBAL => [
			self::METHOD_GET_VERSION => true,
		],
		self::METHOD_CATEGORY_PRODUCT => [
			self::METHOD_GET_ZBOZI_DETAIL_LIST => true,
			self::METHOD_GET_ZBOZI_PARAMETRY_LIST => true,
			self::METHOD_GET_ZBOZI_AKCE_LIST => true,
			self::METHOD_GET_ZBOZI_CS => true,
			self::METHOD_GET_ZBOZI_STAV => true,
			self::METHOD_GET_ZBOZI_FOTO => true,
		],
		self::METHOD_CATEGORY_ORDER => [
			self::METHOD_SAVE_OBJEDNAVKA_HD => true,
			self::METHOD_UPDATE_HLAVICKA_OBJEDNAVKA_HD => true,
			self::METHOD_GET_OBJEDNAVKA_HD => true,
			self::METHOD_GET_OBJEDNAVKA_ODB => true,
			self::METHOD_STORNO_OBJEDNAVKA_HD => true,
		],
	];

	protected SoapClient|SoapClientMock|null $client = null;

	protected ArrayHash $config;

	protected LoggerInterface $logger;

	public function __construct(
		protected readonly ConfigService $configService,
		protected readonly SentryLogger $sentryLogger,
		protected readonly CacheFactory $cacheFactory,
		private readonly LoggerManager $loggerManager,
	)
	{
		$this->config = ArrayHash::from($this->configService->get('erp', 'connectors', 'ws'));
		$this->logger = new NullLogger();

		$this->startup($this->loggerManager);
	}

	protected function startup(LoggerManager $loggerManager): void
	{
	}

	/**
	 * @throws LoggedException
	 */
	public function callMethod(string $method, string $category, array $params = []): ?stdClass
	{
		set_time_limit(0);
		ini_set('memory_limit', '2048M');

		$this->infoLogByMethod($method, sprintf('======== START ============================ Max execution time: %s s | Memory limit: %s', ini_get('max_execution_time'), ini_get('memory_limit')));
		$log = ['method' => $method];

		if ( ! isset(self::METHODS[$category][$method])) {
			throw new LoggedException(sprintf('Unknown WS method "%s" in category "%s".', $method, $category));
		}
		/** @var Query|null $query */
		$query = $params['query'] ?? null;

		try {
			if (isset($query->erpId)) {
				$this->infoLogByMethod($method, sprintf('CALL WS method->%s(%s)', $method, $query->erpId));
				$log['erpId'] = $query->erpId;
				$result = $this->getSoapClient()->$method($query->erpId);
			} elseif ($method === self::METHOD_SAVE_OBJEDNAVKA_HD || $method === self::METHOD_UPDATE_HLAVICKA_OBJEDNAVKA_HD) {
				/** @var ErpOrder|null $erpOrder */
				$erpOrder = $params['data'] ?? null;
				if ($erpOrder === null) {
					throw new LoggedException('ErpOrder is null.');
				}
				$this->infoLogByMethod($method, sprintf('CALL WS method->%s', $method));
				$data = $erpOrder->getData();
				$result = $this->getSoapClient()->$method($data);
			} else {
				$this->logger->info(sprintf('CALL WS method->%s()', $method));
				$result = $this->getSoapClient()->$method();
			}

			$this->infoLogByMethod($method, sprintf('GET RESULT from WS method->%s()', $method));

			/**
			 * SoapFault object
			 * --------------------
			 * message protected => "Record not found!" (17)
			 * string private => ""
			 * code protected => 0
			 * faultstring => "Record not found!" (17)
			 * faultcode => "S:Server" (8)
			 * detail => stdClass #a445
			 *        HomeDeliveryException => stdClass #766c
			 *        errorCode => "101" (3)
			 *          message => "Record not found!" (17)
			 */
		} catch (SoapFault $e) {

			if ($e->getMessage() === 'Record not found!') {
				$this->logger->info(sprintf('RESULT WS method->%s(), SoapFault "Record not found"', $method), ['erpId' => $params['erpId'] ?? 'unknown']);
				return null;

			} else {
				throw new LoggedException($e->getMessage(), $e->getCode(), $e);
			}
		} catch (Throwable $e) {

			$log['code'] = $e->getCode();
			$log['response'] = $e->getMessage();
			$this->logger->error('API call error.', $log);
			$this->sentryLogger->log($e, ILogger::EXCEPTION);

			throw new LoggedException($e->getMessage(), $e->getCode(), $e);
		}

		return isset($result) && $result instanceof stdClass ? $result : null;
	}

	/**
	 * @throws SoapFault
	 */
	protected function getSoapClient(?string $timeout = null): SoapClient|SoapClientMock
	{
		if ($this->client === null) {

			if ($this->config->useSoapClientMock) {
				$this->client = new SoapClientMock();
				$this->logger->info('Using SoapClientMock');
				return $this->client;
			}

			$timeout = $this->config->timeout->{$timeout} ?? $this->config->timeout->default;
			$streamContext = [
				'ssl' => [
					'verify_peer' => false,
					'verify_peer_name' => false,
				],
			];

			if ($this->config->stream_context->socket_bindto) {
				$streamContext['socket'] = ['bindto' => $this->config->stream_context->socket_bindto];
			}

			$this->client = new SoapClient($this->config->endpoint, [
				'login' => $this->config->credentials->username,
				'password' => $this->config->credentials->password,
				'exceptions' => true,
				'cache_wsdl' => WSDL_CACHE_NONE,
				'connection_timeout' => $timeout,
				'stream_context' => stream_context_create($streamContext),
			]);

			ini_set('default_socket_timeout', $timeout); // connection_timeout is not enough, must be set like this
		}

		return $this->client;
	}

	protected function infoLogByMethod(string $method, string $msg): void
	{
		switch ($method) {
			case self::METHOD_GET_ZBOZI_CS:
			case self::METHOD_GET_ZBOZI_STAV:
			case self::METHOD_GET_ZBOZI_DETAIL_LIST:
			case self::METHOD_GET_ZBOZI_AKCE_LIST:
				$this->logger->info($msg);
				break;
		}
	}

}
