<?php declare(strict_types = 1);

namespace App\Model\Erp\Connector;

use App\Console\Erp\CommandType;
use App\Model\Erp\Exception\LoggedException;
use Contributte\Monolog\LoggerManager;
use Nette\Utils\ArrayHash;

class PriceConnector extends WsConnector implements ReadCollection
{

	protected function startup(LoggerManager $loggerManager): void
	{
		if ($this->config->verboseLog) {
			$this->logger = $loggerManager->get(CommandType::PRICE_IMPORT);
		}
		parent::startup($loggerManager);
	}

	/**
	 * @throws LoggedException
	 */
	public function getRows(Query $query): ArrayHash
	{
		$result = $this->callMethod(WsConnector::METHOD_GET_ZBOZI_CS, WsConnector::METHOD_CATEGORY_PRODUCT);
		return is_object($result) && isset($result->item) ? ArrayHash::from($result->item) : new ArrayHash();
	}

	public function getJsonSource(Query $query): ?string
	{
		return null;
	}

	public function getXmlSource(): ?string
	{
		return null;
	}

}
