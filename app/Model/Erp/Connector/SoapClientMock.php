<?php /** @noinspection HttpUrlsUsage */
/** @noinspection PhpUnused */
declare(strict_types = 1);

namespace App\Model\Erp\Connector;

use Exception;
use Nette\Utils\Json;
use RuntimeException;
use SimpleXMLElement;
use stdClass;

class SoapClientMock
{

	public function getVersion(): string
	{
		return 'ADAMiNT © HomeDelivery WebServices Interface v4.6.1 (User:TESThd)';
	}

	/**
	 * @throws Exception
	 */
	public function getZboziDetailList(): stdClass
	{
		return $this->loadXmlFromFile(__FUNCTION__);
	}

	/**
	 * @throws Exception
	 */
	public function getZboziParametryList(): stdClass
	{
		return $this->loadXmlFromFile(__FUNCTION__);
	}

	/**
	 * @throws Exception
	 */
	public function getZboziStav(): stdClass
	{
		return $this->loadXmlFromFile(__FUNCTION__);
	}

	/**
	 * @throws Exception
	 */
	public function getZboziCS(): stdClass // phpcs:ignore
	{
		return $this->loadXmlFromFile(__FUNCTION__);
	}

	/**
	 * @throws Exception
	 */
	public function getZboziFoto(): stdClass // phpcs:ignore
	{
		return $this->loadXmlFromFile(__FUNCTION__);
	}

	// *********************************************************************

	/**
	 * @throws Exception
	 */
	private function loadXmlFromFile(string $method): stdClass
	{
		$filePath = TEMP_DIR . '/erp/' . $method . '.xml';

		if (file_exists($filePath)) {
			$xmlContent = file_get_contents($filePath);
			$xml = new SimpleXMLElement(strval($xmlContent));

			$xml->registerXPathNamespace('S', 'http://schemas.xmlsoap.org/soap/envelope/');
			$xml->registerXPathNamespace('ns2', 'http://ws.hd.modules.adamint.noviko.cz/');

			$responseNode = $xml->xpath('//ns2:' . $method . 'Response/return')[0] ?? null;

			if ($responseNode) {
				$json = Json::encode($responseNode);
				return Json::decode($json);
			}

			throw new RuntimeException('Could not parse SOAP response from file: ' . $filePath);
		}

		throw new RuntimeException('XML file not found: ' . $filePath);
	}

}
