<?php declare(strict_types = 1);

namespace App\Model\Erp\Entity;

use App\Exceptions\LogicException;
use App\Model\Orm;
use Nette\Utils\ArrayHash;
use Nette\Utils\Strings;
use stdClass;

class Parameter
{

	/**
	 * Internal ERP item ID
	 */
	public int $id;

	/**
	 * ERP product ID
	 */
	public int $variantExtId;

	/**
	 * ERP Parameter ID
	 */
	public string $extId;

	public string $name;

	public string $type;

	public string $valueName;

	/**
	 * Internal ERP item Value ID [paramExtId]-[valueExtId]
	 */
	public string $valueExtId;

	public string|null $unit;

	public function __construct(ArrayHash|stdClass $values)
	{
		$this->setDataFromWsItem($values);
	}

	public function setDataFromWsItem(ArrayHash|stdClass $item): void
	{
		$typeRaw = $item->paramType;
		if (str_contains($typeRaw, '=')) {
			$parts = explode('=', $typeRaw, 2);
			$extType = $parts[0];
			$valueExtId = $parts[1];
		} else {
			$extType = $typeRaw;
		}

		$this->type = match ($extType) {
			'L' => Orm\Parameter\Parameter::TYPE_MULTISELECT,
			'N', 'I' => Orm\Parameter\Parameter::TYPE_NUMBER,
			'B' => Orm\Parameter\Parameter::TYPE_BOOL,
			'S', 'D' => Orm\Parameter\Parameter::TYPE_TEXT,
			default => throw new LogicException('Unsupported parameter type: ' . $extType),
		};

		$this->id = (int) $item->id;
		$this->variantExtId = (int) $item->idZbozi;
		$this->extId = (string) $item->paramId;
		$this->name = $item->paramName;
		$this->unit = isset($item->unit) ? strval($item->unit) : null;

		if ($this->type === Orm\Parameter\Parameter::TYPE_NUMBER) {
			$this->valueName = (string) intval($item->value); // temp
		} else {
			$this->valueName = (string) $item->value;
		}

		if ( ! isset($valueExtId)) {
			$valueExtId = Strings::webalize($this->valueName);
		}

		$this->valueExtId = $this->extId . '-' . $valueExtId;
	}

}
