<?php declare(strict_types = 1);

namespace App\Model\Erp\Entity;

use Nette\Utils\ArrayHash;

class Stock
{

	/**
	 * ERP product ID
	 */
	public int $variantExtId;

	/**
	 * pocet skladem
	 */
	public int $stock;

	/**
	 * pocet skladem HD
	 */
	public int $stockHD;

	public function __construct(ArrayHash $values)
	{
		$this->setDataFromWsItem($values);
	}

	public function setDataFromWsItem(ArrayHash $item): void
	{
		$this->variantExtId = (int) $item->idZbozi;
		$this->stock = isset($item->skladem) ? (int) $item->skladem : 0;
		$this->stockHD = isset($item->skladHD) ? (int) $item->skladHD : 0;
	}

}
