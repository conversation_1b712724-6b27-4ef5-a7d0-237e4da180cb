<?php declare(strict_types = 1);

namespace App\Model\Erp\Traits;

use App\Console\Erp\CommandType;
use Contributte\Monolog\LoggerManager;
use Nette\Utils\Strings;
use Psr\Log\LoggerInterface;
use Psr\Log\NullLogger;

trait HasLogger
{

	protected function getLoggerByImportType(
		LoggerManager $loggerManager,
		string $type,
		string $action = 'import'
	): LoggerInterface
	{
		$logger = new NullLogger();

		$verboseLog  = $this->config->{$type}->{$action}->verboseLog ?? false;
		$commandType = Strings::upper($type . '_' . $action);

		$reflectionClass = new \ReflectionClass(CommandType::class);

		if (($loggerName = $reflectionClass->getConstant($commandType)) !== false && $verboseLog) {
			$logger = $loggerManager->get($loggerName);
		}

		return $logger;
	}

}
