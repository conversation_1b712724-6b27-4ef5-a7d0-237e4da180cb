<?php declare(strict_types=1);

namespace App\Model\DTO\Product;

use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Setup;

class ProductDtoFactory
{

	public function __construct()
	{
	}

	public function create(Product $product, ?ProductVariant $variant, Setup $setup): ProductDto
	{
//		$writers = [];
//		foreach ($product->writers as $writer) {
//			$writers[] = $this->writerDtoFactory->create($writer);
//		}
		return new ProductDto(
			$product,
			$variant,
			$setup,
		);
	}

}
