<?php declare(strict_types=1);

namespace App\Model\DTO\Product;

use App\Infrastructure\Latte\Functions;
use App\Model\CacheStorageService;
use App\Model\Comparators\ComparatorModeProvider;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\Availability\CustomProductAvailability;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\Traits\HasStaticCache;
use App\Model\Orm\User\UserProvider;
use App\Model\Setup;
use App\Model\Time\CurrentDateTimeProvider;
use App\Model\Time\DeliveryTiming;
use App\Model\Time\StoreTiming;
use App\Model\Time\TimeOperation;
use Nette\Caching\Cache;
use Nextras\Dbal\Utils\DateTimeImmutable;

class ProductDtoProvider
{

	use HasStaticCache;

	private Setup $setup;

	public function __construct(
		private readonly CacheStorageService $cacheStorageService,
		private readonly ProductDtoFactory $productDtoFactory,
		private readonly CurrentDateTimeProvider $currentDateTimeProvider,
		private readonly TimeOperation $timeOperation,
		private readonly StoreTiming $storeTime,
		private readonly DeliveryTiming $deliveryTiming,
		private readonly ComparatorModeProvider $comparatorModeProvider,
		private readonly UserProvider $userProvider,
	)
	{
	}

	public function setSetup(Setup $setup): void
	{
		$this->setup = $setup;
	}

	public function get(Product $product, ?ProductVariant $productVariant): ProductDto
	{
		$generator = function () use ($product, $productVariant) {
			$productDto = $this->productDtoFactory->create($product, $productVariant, $this->setup);

			if ($this->currentDateTimeProvider->forcedDateTimeIsUsed()) {
				$ttl = 1;
			} else {
				$ttl = $this->minimumTtlInSeconds($product);
			}

			return [
				$productDto,
				[
					Cache::Tags => $product->getTemplateCacheTags(),
					Cache::Expire => $ttl . ' second',
				],
			];
		};

		$namespace = 'productDto';
		if ($this->currentDateTimeProvider->forcedDateTimeIsUsed()) {
			$namespace .= '-' . $this->currentDateTimeProvider->getCurrentDateTime()->getTimestamp();
		}
		$generatorStatic = fn() => $this->cacheStorageService->getStoredDataWithOptions(
			$namespace,
			Functions::cacheKey(
				$product,
				$productVariant,
				$this->setup->mutation,
				$this->setup->mutation->getSelectedCurrency(),
				$this->setup->priceLevel,
				$this->setup->state,
				$this->comparatorModeProvider->isActive,
				$this->userProvider->userEntity?->isClubMember
			),
			$generator
		);

		return $this->loadCache(Functions::cacheKey($product, $productVariant), $generatorStatic);
	}

	private function minimumTtlInSeconds(Product $product): int
	{
		$productAvailability = $product->productAvailability;
		$currentDateTime = $this->currentDateTimeProvider->getCurrentDateTime();
		assert($productAvailability instanceof CustomProductAvailability);
		$ttls = [0];
		$maximumTtl = $this->getSecondsToMidnight($currentDateTime);
		$secondsToNewDelivery = $this->getSecondsToNewDelivery($productAvailability, $this->setup, $currentDateTime);
		$secondsToNewPickUp = $this->getSecondsToNewPickup($product, $currentDateTime);

		$ttls[] = $maximumTtl;
		$ttls[] = $secondsToNewDelivery;
		if ($secondsToNewPickUp !== null) {
			$ttls[] = $secondsToNewPickUp;
		}

		return min(...$ttls);
	}

	private function getSecondsToNewDelivery(CustomProductAvailability $productAvailability, Setup $setup, DateTimeImmutable $currentDateTime): int
	{
		$crucialDeliveryTime = $this->deliveryTiming->getCrucialTimeForBestDelivery($productAvailability, $setup);
		[$hours, $minutes] = explode(':', $crucialDeliveryTime);
		$crucialDeliveryDateTime = $currentDateTime->setTime((int) $hours, (int) $minutes, 0);

		if ($currentDateTime > $crucialDeliveryDateTime) {
			$crucialDeliveryDateTime = $crucialDeliveryDateTime->modify('1 day');
		}
		return $this->timeOperation->getDiffInSeconds($crucialDeliveryDateTime, $currentDateTime) + 1; // add 1 second to be save
	}

	private function getSecondsToNewPickup(Product $product, DateTimeImmutable $currentDateTime): ?int
	{
		$crucialDeliveryDateTime = $this->storeTime->getFutureClosingTime($product, $currentDateTime);
		if ($crucialDeliveryDateTime === null) {
			return null;
		}

		return $this->timeOperation->getDiffInSeconds($crucialDeliveryDateTime, $currentDateTime) + 1; // add 1 second to be save
	}

	private function getSecondsToMidnight(DateTimeImmutable $currentDateTime): int
	{
		$midnightDateTime = $currentDateTime->modify('+1 day')->setTime(0, 0, 0);
		return $this->timeOperation->getDiffInSeconds($midnightDateTime, $currentDateTime) + 1;
	}

}
