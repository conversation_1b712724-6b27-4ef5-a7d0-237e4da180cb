<?php declare(strict_types=1);

namespace App\Model\DTO\Product;

use App\Model\Image\ImageObject;
use App\Model\Orm\Price;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\Availability\CustomProductAvailability;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Setup;
use App\PostType\Page\Model\Orm\CatalogTree;
use Brick\Money\Money;
use Stringable;

class ProductDto
{

	public readonly ?string $productAvailabilityType;

	public readonly string|Stringable|null $productAvailabilityText;

	public readonly string|Stringable|null $productAvailabilityShortText;

	public readonly string|Stringable|null $productAvailabilityInfoText;

	public readonly ?ImageObject $firstImageObjectMd;

	public readonly ?string $nameAnchor;

	public readonly int $productId;

	public readonly ?int $variantId;

	public readonly ?float $priceInfoDiscountPercentage;

	public readonly Money $price;

	public readonly array $discountDate;

	public readonly string $alias;

	public readonly ?Money $priceInfoDiscountAmount;

	public readonly ?Money $priceInfoOriginalPrice;

	public readonly bool $isOld;

	public readonly ?string $tooltipText;

	public readonly ?string $priceInfoTag;

	public readonly Money $priceVat;

	public readonly bool $productAvailabilityShowWatchdog;

	public readonly bool $productAvailabilityShowCartCatalog;

	public readonly string|Stringable|null $productAvailabilityStoreText;

	public readonly string|Stringable|null $productAvailabilityDeliveryText;

	public readonly bool $isElectronic;

	public readonly bool $productAvailabilityHasPrice;

	public readonly array $gtmItemCategories;

	public readonly string $firstImageAlt;

	public readonly bool $productAvailabilityIsShowSimilar;

	public readonly bool $productAvailabilityIsShowCartDetail;

	public readonly int $productAvailabilityGetMaxAvailableAmount;

	public readonly string $productAvailabilityStateText;

	public readonly Money $priceInfoSellingPrice;

	public readonly bool $productDirectBuyAllowed;

	public readonly bool $hasPriceFrom;

	public function __construct(
		Product $product,
		?ProductVariant $variant,
		Setup $setup,
	)
	{
		$productLocalization = $product->getLocalization($setup->mutation);

		$this->productId = $product->id;
		$this->variantId = $variant?->id;
		$this->nameAnchor = $product->nameAnchor;
		$this->isOld = (bool) $product->isOld;
		$this->isElectronic = (bool) $product->isElectronic;

		if ($variant === null) {
			$priceInfo = $product->getPriceInfo($setup->mutation, $setup->priceLevel, $setup->state);
			$productAvailability = $product->productAvailability;
		} else {
			$priceInfo = $variant->getPriceInfo($setup->mutation, $setup->priceLevel, $setup->state);
			$productAvailability = $variant->productAvailability;
		}

		$this->hasPriceFrom = $priceInfo->hasPriceFrom();
		$this->priceVat = $priceInfo->getSellingPriceVat();
		$this->price = Price::from($priceInfo->getSellingPrice())->asMoney(2);

		$this->tooltipText = $product->getTooltipText($setup->mutation, $setup->priceLevel, $setup->state); // todo check this out
		assert($productAvailability instanceof CustomProductAvailability);
		$this->productAvailabilityType = $productAvailability->getType();

//		{php $detailText = $variant->productAvailability->getAvailabilityText() ?? false}
		$this->productAvailabilityText = $productAvailability->getAvailabilityText();

//		{php $shortText = $variant->productAvailability->getAvailabilityShortText() ?? false}
		$this->productAvailabilityShortText = $productAvailability->getAvailabilityShortText();
//		{php $infoText = $variant->productAvailability->getInfoText() ?? false}
		$this->productAvailabilityInfoText = $productAvailability->getInfoText();
		$this->productAvailabilityStoreText = $productAvailability->getStoreText($setup->state);
		$this->productAvailabilityDeliveryText = $productAvailability->getDeliveryText($setup->mutation, $setup->state, $setup->priceLevel, $setup->mutation->getSelectedCurrency());

		$this->productAvailabilityShowWatchdog = $productAvailability->isShowWatchdog();
		$this->productAvailabilityShowCartCatalog = $productAvailability->isShowCartCatalog($setup->mutation, $setup->priceLevel, $setup->state);
		$this->productAvailabilityHasPrice = $productAvailability->hasPrice($setup->mutation, $setup->priceLevel, $setup->state);
		$this->productAvailabilityIsShowSimilar = $productAvailability->isShowSimilar();
		$this->productAvailabilityIsShowCartDetail = $productAvailability->isShowCartDetail($setup->mutation, $setup->priceLevel, $setup->state);
		$this->productAvailabilityGetMaxAvailableAmount = $productAvailability->getMaxAvailableAmount();
		$this->productAvailabilityStateText = $productAvailability->getAvailabilityStateText();

		if ($variant === null) {
			$productImage = $product->firstImage;
		} else {
			$productImage = $variant->firstImage ?? $product->firstImage;
		}
		$this->firstImageObjectMd = $productImage?->getSize('md');
		$this->firstImageAlt = $productImage?->getAlt($setup->mutation) ?? '';

		//$priceInfo->getDiscountPercentage()
		$this->priceInfoDiscountPercentage = $priceInfo->getOldPriceDiscountPercentage();

		$this->discountDate = $product->getDiscountDate($setup->mutation, $setup->priceLevel, $setup->state);

		if ($productLocalization->alias === null) {
			$this->alias = '#';
		} else {
			$this->alias = $productLocalization->alias->alias;
		}
//		{php $discountAmount = $priceInfo->getDiscountAmount() ?? false}
		$this->priceInfoDiscountAmount = $priceInfo->getDiscountAmount();
//		{php $originalPrice = $priceInfo->getOriginalPrice() ?? false}
		$this->priceInfoOriginalPrice = $priceInfo->getOriginalPrice();

		$this->priceInfoTag = $priceInfo->getTag();
		$this->priceInfoSellingPrice = $priceInfo->getSellingPrice();

		$gtmItemCategories = [];
		if ($productLocalization->product->mainCategory !== null) {
			$i = 1;
			foreach (array_merge($productLocalization->product->mainCategory->pathItems, [$productLocalization->product->mainCategory]) as $category) {
				if ($category instanceof CatalogTree && $category->level > 1) {
					$gtmItemCategories['item_category' . ($i > 1 ? $i : '')] = $category->getName();
					$i++;
				}
			}
		}
		$this->gtmItemCategories = $gtmItemCategories;

		$directBuyAllowed = false;
		if ($productLocalization->product->mainCategory !== null) {
			$directBuyLimitPrice = (float) $productLocalization->product->mainCategory->getFirstCfValueInCategoryPath('directBuy->limitPrice', true);
			//check if price is lower than limit price fro direct buy
			if ($directBuyLimitPrice > 0 && !$this->price->isZero() && $this->price->getAmount()->toFloat() < $directBuyLimitPrice) {
				//it is product or online course (courses with events -> you have to choose course date in product detail)
				$directBuyAllowed = !$productLocalization->product->isCourse() || $productLocalization->product->isOnlineCourse();
			}
		}
		$this->productDirectBuyAllowed = $directBuyAllowed;
	}

}
