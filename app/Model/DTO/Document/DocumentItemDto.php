<?php declare(strict_types=1);

namespace App\Model\DTO\Document;

use App\Model\Orm\Order\OrderItem;
use App\Model\Orm\Order\Product\ProductItem;

class DocumentItemDto
{

	public int $id;

	public int $extId;

	public int $amount;

	public string $name;

	public function __construct(
		OrderItem $orderItem,
	)
	{
		if ($orderItem instanceof ProductItem) {
			$this->id = $orderItem->id;
			$this->amount = $orderItem->amount;
			$this->name = $orderItem->getName();
			$this->extId = $orderItem->variant->extId;
		}
		//TODO - pokud se jed<PERSON> o balíček, tak rozbít na položky
	}

}
