<?php declare(strict_types = 1);

namespace App\Model\DTO\ShopReview;

class HeurekaShopReviewDto
{

	public readonly int $orderId;

	public readonly int $ordered;

	public readonly int $ratingId;

	public readonly ?int $recommends;

	public readonly string $source;

	public readonly int $unixTimestamp;

	public readonly ?string $pros;

	public readonly ?string $cons;

	public readonly ?int $pickupQuality;

	public readonly ?int $pickupTime;

	public readonly ?string $summary;

	public readonly ?int $transportQuality;

	public readonly ?int $deliveryTime;

	public readonly ?int $communication;

	public readonly float $totalRating;

	public function __construct(
		int $orderId,
		int $ordered,
		int $ratingId,
		?int $recommends,
		string $source,
		int $unixTimestamp,
		?string $pros,
		?string $cons,
		?int $pickupQuality,
		?int $pickupTime,
		?string $summary,
		?int $transportQuality,
		?int $deliveryTime,
		?int $communication,
		float $totalRating
	)
	{
		$this->orderId = $orderId;
		$this->ordered = $ordered;
		$this->ratingId = $ratingId;
		$this->recommends = $recommends;
		$this->source = $source;
		$this->unixTimestamp = $unixTimestamp;
		$this->pros = $pros;
		$this->cons = $cons;
		$this->pickupQuality = $pickupQuality;
		$this->pickupTime = $pickupTime;
		$this->summary = $summary;
		$this->transportQuality = $transportQuality;
		$this->deliveryTime = $deliveryTime;
		$this->communication = $communication;
		$this->totalRating = $totalRating;
	}

	public static function fromXmlElement(\SimpleXMLElement $xmlNode): self
	{
		return new self(
			orderId: (int) $xmlNode->order_id,
			ordered: (int) $xmlNode->ordered,
			ratingId: (int) $xmlNode->rating_id,
			recommends: isset($xmlNode->recommends) ? (int) $xmlNode->recommends : null,
			source: (string) $xmlNode->source,
			unixTimestamp: (int) $xmlNode->unix_timestamp,
			pros: isset($xmlNode->pros) && strlen(trim((string) $xmlNode->pros)) > 0 ? (string) $xmlNode->pros : null,
			cons: isset($xmlNode->cons) && strlen(trim((string) $xmlNode->cons)) > 0 ? (string) $xmlNode->cons : null,
			pickupQuality: isset($xmlNode->pickup_quality) ? (int) $xmlNode->pickup_quality : null,
			pickupTime: isset($xmlNode->pickup_time) ? (int) $xmlNode->pickup_time : null,
			summary: isset($xmlNode->summary) && strlen(trim((string) $xmlNode->summary)) > 0 ? (string) $xmlNode->summary : null,
			transportQuality: isset($xmlNode->transport_quality) ? (int) $xmlNode->transport_quality : null,
			deliveryTime: isset($xmlNode->delivery_time) ? (int) $xmlNode->delivery_time : null,
			communication: isset($xmlNode->communication) ? (int) $xmlNode->communication : null,
			totalRating: (float) $xmlNode->total_rating
		);
	}

	public function toArray(): array
	{
		return get_object_vars($this);
	}

	public static function fromArray(array $array): self
	{
		return new self(...array_values($array));
	}

}
