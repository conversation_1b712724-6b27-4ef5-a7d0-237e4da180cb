<?php declare(strict_types=1);

namespace App\Model\Price;

use App\Infrastructure\Latte\Functions;
use App\Model\CacheFactory;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\PriceLevel\PriceLevelModel;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\ProductVariantPrice\ProductVariantPrice;
use App\Model\Orm\ProductVariantPrice\ProductVariantPriceRepository;
use App\Model\Orm\Rate\Rate;
use App\Model\Orm\Rate\RateModel;
use App\Model\Orm\State\State;
use App\Model\PriceInfo;
use App\Model\PriceInfoFactory;
use App\Model\Time\CurrentDateTimeProvider;
use Brick\Math\BigDecimal;
use Brick\Money\Currency;
use Brick\Money\Money;
use Nette\Caching\Cache;
use Nextras\Dbal\Result\Row;
use Nextras\Dbal\Utils\DateTimeImmutable;

class ProductVariantPriceModel
{

	private Cache $cache;

	private array $allPriceLevels; // @phpstan-ignore-line

	public function __construct(
		private readonly CacheFactory $cacheFactory,
		private readonly RateModel $rateModel,
		private readonly PriceLevelModel $priceLevelModel,
		private readonly ProductVariantPriceRepository $productVariantPriceRepository,
		private readonly MoneyWrapperHelper $moneyWrapperHelper,
		private readonly CurrentDateTimeProvider $currentDateTimeProvider,
		private readonly PriceInfoFactory $priceInfoFactory,
	)
	{
		$this->cache = $this->cacheFactory->create('productPrices');
		$this->allPriceLevels = $this->priceLevelModel->getAllPriceLevelByType();
	}

	public function getPriceByPriceLevel(ProductVariant $productVariant, PriceLevel $priceLevel, Mutation $mutation, State $country, Currency $currency): Money
	{
		return $this->getPriceSummaryInfoCached($productVariant, $mutation, $priceLevel, $country, $currency)->money;
	}

	public function getVatByPriceLevel(ProductVariant $productVariant, PriceLevel $priceLevel, Mutation $mutation, State $country, Currency $currency): BigDecimal
	{
		return $this->getPriceSummaryInfoCached($productVariant, $mutation, $priceLevel, $country, $currency)->vat;
	}


	public function getPriceSummaryInfoCached(ProductVariant $productVariant, Mutation $mutation, PriceLevel $priceLevel, State $country, Currency $currency): MoneyWrapper
	{
		$cacheKey = Functions::cacheKey(
			'priceInfo',
			'PV-' . $productVariant->id,
			'M-' . $mutation->id,
			'PL-' . $priceLevel->id,
			'S-' . $country->id,
			'C-' . $currency->getCurrencyCode(),
			'FORCED-' . (int) $this->currentDateTimeProvider->forcedDateTimeIsUsed(),
		);

		$moneyWrappers = $this->cache->load($cacheKey);
		if ($moneyWrappers === null) {
			$moneyWrappers = $this->getPriceSummaryInfo($productVariant, $mutation, $priceLevel, $currency, $this->currentDateTimeProvider->getCurrentDateTime());
			$dependencies = [
				Cache::Tags => array_merge([
					ProductVariantPrice::class,
					Rate::class . '/' . $currency,
				], $productVariant->product->getTemplateCacheTags()),
			];
			if ($this->currentDateTimeProvider->forcedDateTimeIsUsed()) {
				$dependencies[Cache::Expire] = '5 second';
			}
			$this->cache->save($cacheKey, $moneyWrappers, $dependencies);
		}

		return $this->digestProductPrices($moneyWrappers, $currency,);
	}

	/**
	 * @return array<MoneyWrapper>
	 */
	private function getPriceSummaryInfo(ProductVariant $productVariant, Mutation $mutation, PriceLevel $priceLevel, Currency $currency, DateTimeImmutable $now): array
	{
		$selectedCurrencyCode = $currency->getCurrencyCode();
		$pricesData = $this->productVariantPriceRepository->getFutureActivePriceData($productVariant, $mutation, $priceLevel, $now);
		$convert = $selectedCurrencyCode !== $mutation->currency->getCurrencyCode();

		return array_map(function (Row $priceRow) use ($selectedCurrencyCode, $convert) {
			if ($convert) {
				$priceRow->price_currency = $selectedCurrencyCode;
				$priceRow->price_amount = round($this->rateModel->getAmount((float) $priceRow->price_amount, $selectedCurrencyCode), 2);
			}
			return $this->moneyWrapperHelper->createFromRow($priceRow, $selectedCurrencyCode);
		}, $pricesData);
	}


	public function getReferencePrice(ProductVariant $productVariant, PriceLevel $priceLevel, Mutation $mutation): ?Money
	{
		$selectedCurrency = $mutation->getSelectedCurrency()->getCurrencyCode();
		$floatPrice = $this->getLastProductVariantLogPrice($productVariant, $priceLevel);
		return Money::of(round($this->rateModel->getAmount($floatPrice, $selectedCurrency), 2), $selectedCurrency);
	}


	private function getLastProductVariantLogPrice(ProductVariant $productVariant, PriceLevel $priceLevel): float
	{
		$cacheKey = 'getLastProductVariantLogPrice-P-' . $productVariant->id . '-PL-' . $priceLevel->id;
		$lastProductVariantLogPrice = $this->cache->load($cacheKey);

		if ($lastProductVariantLogPrice === null) {
			$productVariantPriceLog = $productVariant->priceLogs->toCollection()->limitBy(1)->orderBy('id', 'DESC')->getBy(['priceLevel' => $priceLevel]);
			if ($productVariantPriceLog === null) {
				$lastProductVariantLogPrice = 0.0;
			} else {
				$lastProductVariantLogPrice = $productVariantPriceLog->realOrigPrice;
			}
			$this->cache->save($cacheKey, $lastProductVariantLogPrice, [Cache::Expire => '30 minutes']);
		}

		return $lastProductVariantLogPrice;
	}

	public function getPriceLevelByStock(ProductVariant $productVariant, PriceLevel $priceLevel): PriceLevel
	{
			return $priceLevel;
		/*if ($productVariant->suplyCountStockDefault < 1 && $productVariant->suplyCountStockSupplier > 0) {
			return $this->allPriceLevels[PriceLevel::TYPE_SUPPLIER];
		} else {
			return $priceLevel;
		}*/
	}


	public function getDiscountPriceWrapper(ProductVariant $productVariant, Money $activePrice, PriceLevel $activePriceLevel, Mutation $mutation, State $country, Currency $currency): ?MoneyWrapper
	{
		/*$discountPriceLevel = $activePriceLevel->discountPrice;
		if ($discountPriceLevel !== null) {
			$discountPriceWrapper = $this->getPriceSummaryInfoCached($productVariant, $mutation, $discountPriceLevel, $country, $currency);
			if (!$discountPriceWrapper->money->isZero() && $discountPriceWrapper->money->isLessThan($activePrice->getAmount())) {
				return $discountPriceWrapper;
			}
		}*/
		return null;
	}


	/**
	 * @param array<MoneyWrapper> $moneyWrappers
	 */
	public function digestProductPrices(array $moneyWrappers, Currency $currency): MoneyWrapper
	{
		$uniqMoneyWrappers = [];
		foreach ($moneyWrappers as $moneyWrapper) {
			if (isset($uniqMoneyWrappers[$moneyWrapper->variantId])) {
				continue;
			}
			if ($this->moneyWrapperHelper->isValid($moneyWrapper)) {
				$uniqMoneyWrappers[$moneyWrapper->variantId] = $moneyWrapper;
			}
		}
		$uniqMoneyWrappers = array_values($uniqMoneyWrappers);
		return $uniqMoneyWrappers[0] ?? $this->moneyWrapperHelper->createEmpty($currency);
	}

	public function getPrice(ProductVariant $productVariant, Mutation $mutation, PriceLevel $defaultPriceLevel, State $country): Money
	{
		$currency = $mutation->getSelectedCurrency();
		$stockPriceLevel = $this->getPriceLevelByStock($productVariant, $defaultPriceLevel);
		$stockPrice = $this->getPriceByPriceLevel($productVariant, $stockPriceLevel, $mutation, $country, $currency);

		$discountPriceWrapper = $this->getDiscountPriceWrapper($productVariant, $stockPrice, $stockPriceLevel, $mutation, $country, $currency);
		$discountPrice        = $discountPriceWrapper?->money;

		return $discountPrice ?? $stockPrice;
	}


	public function getPriceInfo(ProductVariant $productVariant, Mutation $mutation, PriceLevel $defaultPriceLevel, State $country): ?PriceInfo
	{
		$currency = $mutation->getSelectedCurrency();
		$stockPriceLevel = $this->getPriceLevelByStock($productVariant, $defaultPriceLevel);
		$stockPrice = $this->getPriceByPriceLevel($productVariant, $stockPriceLevel, $mutation, $country, $currency);

		$recommendedPrice = $this->getRecommendedPrice($productVariant, $mutation, $country, $currency);
		$discountPriceWrapper = $this->getDiscountPriceWrapper($productVariant, $stockPrice, $stockPriceLevel, $mutation, $country, $currency);

		$discountPricePeriod = ($discountPriceWrapper !== null) ? ['from' => $discountPriceWrapper->from, 'to' => $discountPriceWrapper->to] : ['from' => null, 'to' => null];
		$discountPrice = $discountPriceWrapper?->money;
		$referencePrice = $this->getReferencePrice($productVariant, $stockPriceLevel, $mutation);
		$vatRate = $this->getVatByPriceLevel($productVariant, $stockPriceLevel, $mutation, $country, $currency);

		return $this->priceInfoFactory->create(
			$productVariant,
			$mutation,
			$vatRate,
			$mutation->getSelectedCurrency()->getCurrencyCode(),
			$stockPrice,
			$recommendedPrice,
			$referencePrice ?? $stockPrice,
			$discountPricePeriod,
			$discountPrice,
		);
	}

	public function getRecommendedPrice(ProductVariant $productVariant, Mutation $mutation, State $country, Currency $currency): Money
	{
		$allPriceLevels = $this->priceLevelModel->getAllPriceLevelByType();
		$recommendedPriceLevel = $allPriceLevels[PriceLevel::TYPE_RECOMMENDED];
		return $this->getPriceByPriceLevel($productVariant, $recommendedPriceLevel, $mutation, $country, $currency);
	}

}
