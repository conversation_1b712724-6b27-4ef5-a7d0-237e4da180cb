<?php declare(strict_types=1);

namespace App\Model\Price;

use App\Exceptions\LogicException;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Price;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\PriceLevel\PriceLevelModel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\ProductVariantPrice\ProductVariantPrice;
use App\Model\Orm\ProductVariantPrice\ProductVariantPriceRepository;
use App\Model\Orm\State\State;
use App\Model\PriceInfo;
use App\Model\PriceInfoFactory;
use App\Model\VatCalculator;
use Brick\Math\BigDecimal;
use Brick\Math\Exception\MathException;
use Brick\Math\RoundingMode;
use Brick\Money\Money;

class ProductPriceModel
{

	public function __construct(
		private readonly PriceLevelModel $priceLevelModel,
		private readonly ProductVariantPriceRepository $productVariantPriceRepository,
		private readonly ProductVariantPriceModel $productVariantPriceModel,
		private readonly PriceInfoFactory $priceInfoFactory,
		private readonly MutationsHolder $mutationsHolder,
	)
	{
	}

	public function getPriceInfo(Product $product, Mutation $mutation, PriceLevel $priceLevel, State $country): PriceInfo
	{
		if ($product->isCourse()) {
			return $product->firstVariant->getPriceInfo($mutation, $priceLevel, $country);
		}

		$priceInfoWithVariants = [];
		if ($product->activeVariants->count() === 1) {
			return $this->productVariantPriceModel->getPriceInfo($product->activeVariants->fetch(), $mutation, $priceLevel, $country);
		}

		foreach ($product->activeVariants as $productVariant) {
			$priceInfoWithVariants[] = new VariantPriceInfoWrapper(
				$productVariant,
				$this->productVariantPriceModel->getPriceInfo($productVariant, $mutation, $priceLevel, $country),
			);
		}

		$bestEnrichedPriceInfo = $this->getBestEnrichedPriceInfo($priceInfoWithVariants);

		if ($bestEnrichedPriceInfo === null) {
			return $this->getZeroPriceInfo($product, $mutation, $country);
		}

		return $bestEnrichedPriceInfo;
	}

	/**
	 * @param array<VariantPriceInfoWrapper> $priceInfoWithVariants
	 */
	private function getBestEnrichedPriceInfo(array $priceInfoWithVariants): ?PriceInfo
	{
		$bestProductPriceInfo = null;
		$hasDifferentPrices = false;
		array_walk($priceInfoWithVariants, function (VariantPriceInfoWrapper $priceInfoWithVariant) use (&$bestProductPriceInfo, &$hasDifferentPrices) {
			$priceInfo = $priceInfoWithVariant->variantPriceInfo;

// 			NOTE: disabled for now
//			$variant = $priceInfoWithVariant->productVariant;
//			if ( ! $variant->isInStock) {
//				return;
//			}
			if ($bestProductPriceInfo instanceof PriceInfo
				&& ! $bestProductPriceInfo->getSellingPriceVat()->isEqualTo($priceInfo->getSellingPriceVat())
				&& ! $priceInfo->getSellingPriceVat()->isZero()
			) {
				$hasDifferentPrices = true;
				if ($bestProductPriceInfo->getSellingPriceVat()->isGreaterThan($priceInfo->getSellingPriceVat())) {
					$bestProductPriceInfo = $priceInfo;
				}

			} elseif ($bestProductPriceInfo === null
				&& ! $priceInfo->getSellingPriceVat()->isZero()) {
				// init value
				$bestProductPriceInfo = $priceInfo;
			}
		});

		if ($bestProductPriceInfo === null) {
			return null;
		}

		$priceInfoForProduct = clone $bestProductPriceInfo;
		$priceInfoForProduct->setHasPriceFrom($hasDifferentPrices);
		return $priceInfoForProduct;
	}

	private function getZeroPriceInfo(Product $product, Mutation $mutation, State $country): PriceInfo
	{
		return $this->priceInfoFactory->create(
			$product->firstVariant,
			$mutation,
			$product->vatRate($country),
			$mutation->getSelectedCurrency()->getCurrencyCode(),
			Money::zero($mutation->getSelectedCurrency()),
			Money::zero($mutation->getSelectedCurrency()),
			Money::zero($mutation->getSelectedCurrency()),
			discountPricePeriod: ['from' => null, 'to' => null],
			discountPrice: null
		);
	}

	/**
	 * @throws MathException
	 */
	public function recalculatePrices(ProductVariant $variant): void
	{
		bd($variant->name);
		$priceLevels = $this->priceLevelModel->getAllPriceLevelByType();
		$prices = $variant->pricesByLevelName;

		$purchasePriceLevel = $priceLevels[PriceLevel::TYPE_PURCHASE];
		$recommendedPriceLevel = $priceLevels[PriceLevel::TYPE_RECOMMENDED];

		if ( ! $purchasePriceLevel instanceof PriceLevel) {
			throw new LogicException(sprintf('Unknown price level type "%s"', PriceLevel::TYPE_PURCHASE));
		}

		if ( ! $recommendedPriceLevel instanceof PriceLevel) {
			throw new LogicException(sprintf('Unknown price level type "%s"', PriceLevel::TYPE_RECOMMENDED));
		}

		foreach ($this->mutationsHolder->findAll(false) as $mutation) {
			$currencyCode = $mutation->currency->getCurrencyCode();

			$purchasePrice = $prices[$mutation->id][$currencyCode][$purchasePriceLevel->type] ?? null;

			if ( ! $purchasePrice instanceof ProductVariantPrice) {
				throw new LogicException(sprintf('Unknown price of level type "%s" | Mutation: %s | Currency: %s | Variant ID: %s', PriceLevel::TYPE_PURCHASE, $mutation->id, $currencyCode, $variant->id));
			}
			assert($purchasePrice instanceof ProductVariantPrice);

			$recommendedPrice = $prices[$mutation->id][$currencyCode][$recommendedPriceLevel->type] ?? null;

			if ( ! $recommendedPrice instanceof ProductVariantPrice) {
				throw new LogicException(sprintf('Unknown price of level type "%s" | Mutation: %s | Currency: %s | Variant ID: %s', PriceLevel::TYPE_RECOMMENDED, $mutation->id, $currencyCode, $variant->id));
			}
			assert($recommendedPrice instanceof ProductVariantPrice);
			bd($purchasePrice->price->amount);

			$sourcePrice = match ($variant->usePrice) {
				ProductVariant::USE_PRICE_CATALOG => $recommendedPrice->price,
				ProductVariant::USE_PRICE_MARGIN => Price::from($purchasePrice->price->asMoney(4)->multipliedBy(1 + ($variant->margin / 100), RoundingMode::HALF_UP)),
				default => throw new LogicException(sprintf('Unknown source Price for "%s" | Mutation: %s | Currency: %s | Variant ID: %s', $variant->usePrice, $mutation->id, $currencyCode, $variant->id)),
			};

			foreach ($priceLevels as $priceLevel) {

				if ($priceLevel->isErpSource) {
					continue;
				}

				$price = $prices[$mutation->id][$currencyCode][$priceLevel->type] ?? null;

				if ($price === null) {
					$price = new ProductVariantPrice();
					$this->productVariantPriceRepository->attach($price);

					$price->mutation = $mutation;
					$price->priceLevel = $priceLevel;
					$price->productId = $variant->product->id;
					$price->productVariant = $variant;
					$price->vat = $variant->erpVat;
				}

				switch ($priceLevel->type) {

					case PriceLevel::TYPE_DEFAULT:
						$price->price = $this->getRealPriceWithoutVat($variant, $sourcePrice);
						break;

					case PriceLevel::TYPE_REGISTERED:
					case PriceLevel::TYPE_SHELTER:
						$discountPrice = Price::from($sourcePrice->asMoney(4)->multipliedBy(1 - ($priceLevel->discount / 100), RoundingMode::HALF_UP));
						$price->price = $this->getRealPriceWithoutVat($variant, $discountPrice);
						break;
				}

				$price->vat = $variant->erpVat;
				$this->productVariantPriceRepository->persist($price);
			}
		}

		$this->productVariantPriceRepository->flush();
	}

	/**
	 * @throws MathException
	 */
	public function getRealPriceWithoutVat(ProductVariant $variant, Price $priceWithoutVat): Price
	{
		$priceVat = Price::from(
			VatCalculator::priceWithVat($priceWithoutVat->asMoney(), BigDecimal::of($variant->erpVat))
		);
		return Price::from(
			VatCalculator::priceWithoutVat($priceVat->asMoney(4), BigDecimal::of($variant->erpVat))
		);
	}

}
