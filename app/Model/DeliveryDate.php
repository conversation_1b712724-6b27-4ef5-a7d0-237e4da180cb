<?php declare(strict_types = 1);

namespace App\Model;




use App\Utils\DateTime;

/**
 * <PERSON><PERSON> dodani s podporou od - do
 */
class DeliveryDate
{

	public function __construct(
		public readonly DateTime $from,
		public readonly ?DateTime $to = null,
		public readonly ?DateTime $expedition = null
	)
	{
	}

	public function isInterval(): bool
	{
		return $this->to !== null;
	}

	public function isToday(): bool
	{
		return !$this->isInterval() && $this->from->isToday();
	}

	public function isTomorrow(): bool
	{
		return !$this->isInterval() && $this->from->isTommorow();
	}

	public function isIntervalSameMonth(): bool
	{
		return isset($this->to) && $this->from->format('n') === $this->to->format('n');
	}

	public function getDayOfTheWeek(): int
	{
		return intval($this->from->format('N'));
	}

}
