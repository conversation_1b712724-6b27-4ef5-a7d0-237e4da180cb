<?php declare(strict_types=1);

namespace App\Model\Template;

use Nette\Http\Request;

class PartCondition
{

	public const PART_HEADER = 'header';
	public const PART_FOOTER = 'footer';
	public const PART_CONTENT = 'content';
	public const PART_PRODUCT_BOX = 'productBox';
	public const PART_SPECULATION_RULES = 'speculationRules';

	public const PART_STRUCTURED_DATA = 'structuredData';
	public const QUERY_KEY = 'disabledParts';

	public function __construct(
		private readonly Request $request
	)
	{}

	public function canShow(string $partName): bool
	{
		if (!in_array($partName, [
			self::PART_HEADER,
			self::PART_FOOTER,
			self::PART_CONTENT,
			self::PART_PRODUCT_BOX,
			self::PART_STRUCTURED_DATA,
			self::PART_SPECULATION_RULES,
		])) {
			return true;
		}
		$disabledParts = $this->request->getQuery(self::QUERY_KEY);

		if ($disabledParts === null) {
			return true;
		}

		return (! in_array($partName, explode(',', $disabledParts)));
	}

}
