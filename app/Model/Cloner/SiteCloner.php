<?php declare(strict_types = 1);

namespace App\Model\Cloner;

use App\Model\Messenger\Cloner\CloneMessage;
use App\Model\Messenger\Elasticsearch\ElasticBusWrapper;
use App\Model\Orm\EmailTemplate\EmailTemplateRepository;
use App\Model\Orm\EsIndex\EsIndex;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Mutation\MutationRepository;
use App\Model\Orm\ProductLocalization\ProductLocalizationRepository;
use App\Model\Orm\Repository\QueryForIdsByMutation;
use App\Model\Orm\String\StringRepository;
use App\PostType\Author\Model\Orm\AuthorLocalizationRepository;
use App\PostType\Blog\Model\Orm\BlogLocalizationRepository;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalizationRepository;
use App\PostType\Page\Model\Orm\TreeRepository;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalizationRepository;
use Nextras\Orm\Exception\NoResultException;

class SiteCloner
{

	public function __construct(
		private readonly MutationCloner $mutationCloner,
		private readonly EsIndexCommonCloner $esIndexCloner,
		private readonly MutationRepository $mutationRepository,
		private readonly StringRepository $stringRepository,
		private readonly EmailTemplateRepository $emailTemplateRepository,
		private readonly ElasticBusWrapper $elasticBusWrapper,
		private readonly TreeRepository $treeRepository,
		private readonly ProductLocalizationRepository $productLocalizationRepository,
		private readonly BlogTagLocalizationRepository $blogTagLocalizationRepository,
		private readonly AuthorLocalizationRepository $authorLocalizationRepository,
		private readonly SeoLinkLocalizationRepository $seoLinkLocalizationRepository,
		private readonly BlogLocalizationRepository $blogLocalizationRepository,
	)
	{
	}


	public function create(string $newMutationLangCode, Mutation $sourceMutation): void
	{
		$this->clone($newMutationLangCode, $sourceMutation);
	}


	public function replace(string $newMutationLangCode, Mutation $sourceMutation): void
	{
		$this->clone($newMutationLangCode, $sourceMutation, replace: true);
	}


	private function clone(string $newMutationLangCode, Mutation $sourceMutation, bool $replace = false): void
	{
		if ($replace) {
			$newMutation = $this->mutationRepository->getByCode($newMutationLangCode);
		} else {
			$existingMutation = $this->mutationRepository->getBy([
				'langCode' => $newMutationLangCode
			]);
			if ($existingMutation !== null) {
				throw new MutationExistsException($newMutationLangCode);
			}
			$newMutation = $this->mutationCloner->clone($newMutationLangCode, $sourceMutation);
		}

		$this->treeRepository->setPublicOnly(false);

		$sourceProductEsIndex = $sourceMutation->esIndexes->toCollection()->getBy(['type' => EsIndex::TYPE_PRODUCT]);
		if ($sourceProductEsIndex !== null) {
			$this->esIndexCloner->clone($sourceProductEsIndex, $newMutation);
		}

		$sourceCommonEsIndex = $sourceMutation->esIndexes->toCollection()->getBy(['type' => EsIndex::TYPE_COMMON]);
		if ($sourceCommonEsIndex !== null) {
			$this->esIndexCloner->clone($sourceCommonEsIndex, $newMutation);
		}

		$this->stringRepository->cloneAll($newMutation, $sourceMutation);
		$this->emailTemplateRepository->cloneAll($newMutation, $sourceMutation);
		$this->setStatesForMutation($newMutation, $sourceMutation);

		$postTypeRepositories = [
			$this->treeRepository,
			$this->productLocalizationRepository,
			$this->blogTagLocalizationRepository,
			$this->authorLocalizationRepository,
			$this->seoLinkLocalizationRepository,
			$this->blogLocalizationRepository,
		];

		foreach ($postTypeRepositories as $postTypeRepository) {
			assert($postTypeRepository instanceof QueryForIdsByMutation);
			$this->clonePostTypeLocalizations($newMutation, $sourceMutation, $postTypeRepository);
		}
	}


	private function setStatesForMutation(Mutation $targetMutation, Mutation $sourceMutation): void
	{
		$targetMutation->states->set($sourceMutation->states->toCollection()->fetchAll());
		$this->mutationRepository->persistAndFlush($targetMutation);
	}


	private function clonePostTypeLocalizations(Mutation $newMutation, Mutation $sourceMutation, QueryForIdsByMutation $repository): void
	{
		$postTypeLocalizations = $repository->findAllIdsInMutation($sourceMutation);
		foreach ($postTypeLocalizations as $postTypeLocalizations) {
			$this->elasticBusWrapper->send(
				new CloneMessage($repository::class, $postTypeLocalizations->id, $newMutation)
			);
		}
	}

}
