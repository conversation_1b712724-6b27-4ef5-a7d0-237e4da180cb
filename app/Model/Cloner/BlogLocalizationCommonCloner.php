<?php declare(strict_types = 1);

namespace App\Model\Cloner;

use App\Model\Orm\CopyMachine;
use App\Model\Orm\Creator;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Translator;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\Blog\Model\Orm\BlogLocalizationRepository;
use App\PostType\Blog\Model\Orm\BlogLocalizationTree;
use App\PostType\Blog\Model\Orm\BlogLocalizationTreeRepository;
use App\PostType\Blog\Model\Orm\BlogRepository;
use App\PostType\Page\Model\Orm\Tree;
use Nextras\Orm\Entity\IEntity;
use Nextras\Orm\Exception\NoResultException;

class BlogLocalizationCommonCloner extends PostTypeCommonCloner implements CommonCloner
{

	public function __construct(
		private readonly BlogLocalizationRepository $blogLocalizationRepository,
		private readonly BlogRepository $blogRepository,
		private readonly BlogLocalizationTreeRepository $blogLocalizationTreeRepository,
		private readonly Translator $translator,
	)
	{
	}


	public function clone(IEntity $entity, Mutation $targetMutation): BlogLocalization
	{
		assert($entity instanceof BlogLocalization);
		$newBlogLocalization = parent::clonePostType(
			$entity,
			$targetMutation,
			$this->blogLocalizationRepository,
			$this->blogRepository,
			'blog'
		);
		assert($newBlogLocalization instanceof BlogLocalization);

		$newBlogLocalization->public = false;

		if ($targetMutation === $entity->getMutation()) {
			$newBlogLocalization->name = sprintf('(%s) %s', $this->translator->translate('page_copy'), $newBlogLocalization->name);

		}

		foreach ($entity->blogLocalizationTrees as $blogLocalizationTree) {
			try {

				$newBlogLocalizationTree = Creator::createNewEntity($this->blogLocalizationTreeRepository, $blogLocalizationTree::class);
				$newBlogLocalizationTree = CopyMachine::makeShallowCopy($blogLocalizationTree, $newBlogLocalizationTree);

				assert($newBlogLocalizationTree instanceof BlogLocalizationTree);
				$category = $blogLocalizationTree->tree->getParent()->getLocalization($targetMutation);
				assert($category instanceof Tree);
				$newBlogLocalizationTree->tree = $category;
				$newBlogLocalization->blogLocalizationTrees->add($newBlogLocalizationTree);
			} catch (NoResultException) {
			}
		}

		return $newBlogLocalization;
	}

}
