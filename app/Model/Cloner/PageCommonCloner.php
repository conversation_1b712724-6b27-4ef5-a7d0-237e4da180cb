<?php declare(strict_types = 1);

namespace App\Model\Cloner;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Translator;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Page\Model\Orm\TreeParentRepository;
use App\PostType\Page\Model\Orm\TreeRepository;
use Nextras\Orm\Entity\IEntity;
use Nextras\Orm\Exception\NoResultException;

class PageCommonCloner extends PostTypeCommonCloner implements CommonCloner
{

	public function __construct(
		private readonly TreeRepository $treeRepository,
		private readonly TreeParentRepository $treeParentRepository,
		private readonly Translator $translator,
	)
	{
	}


	public function clone(IEntity $entity, Mutation $targetMutation): Tree
	{
		assert($entity instanceof Tree);
		$newPage = parent::clonePostType(
			$entity,
			$targetMutation,
			$this->treeRepository,
			$this->treeParentRepository,
			'treeParent'
		);

		assert($newPage instanceof Tree);

		$newPage->public = false;

		if ($entity->parent !== null) {
			// is not root page
			try {
				$newParentNode = $entity->parent->getParent()->getLocalization($targetMutation);
			} catch (NoResultException) {
				// missing parentNode in other mutation
				// pick root page
				$newParentNode = $targetMutation->rootPage;
			}

			assert($newParentNode instanceof Tree);

			$newPage->parent = $newParentNode;
			$newPath = $newParentNode->path;
			$newPath[] = $newParentNode->id;
			$newPage->path = $newPath;

			$newPage->rootId = $targetMutation->rootId;
		}



		if ($targetMutation === $entity->getMutation()) {
			$newPage->name = sprintf('(%s) %s', $this->translator->translate('page_copy'), $newPage->name);
		}

		if ($entity->getMetadata()->hasProperty('extId')) {
			$newPage->extId = null;
		}



		return $newPage;
	}

}
