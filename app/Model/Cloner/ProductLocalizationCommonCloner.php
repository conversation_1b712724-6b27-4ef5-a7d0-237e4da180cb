<?php declare(strict_types = 1);

namespace App\Model\Cloner;

use App\Model\Orm\CopyMachine;
use App\Model\Orm\Creator;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Product\ProductModel;
use App\Model\Orm\Product\ProductRepository;
use App\Model\Orm\ProductFile\ProductFile;
use App\Model\Orm\ProductFile\ProductFileRepository;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\ProductLocalization\ProductLocalizationRepository;
use App\Model\Orm\ProductTree\ProductTree;
use App\Model\Orm\ProductTree\ProductTreeRepository;
use App\Model\Orm\ProductVariantLocalization\ProductVariantLocalization;
use App\Model\Orm\ProductVariantPrice\ProductVariantPrice;
use App\Model\Orm\ProductVariantPrice\ProductVariantPriceRepository;
use App\PostType\Page\Model\Orm\CatalogTree;
use LogicException;
use Nextras\Orm\Entity\IEntity;
use Nextras\Orm\Exception\NoResultException;

class ProductLocalizationCommonCloner implements CommonCloner
{

	public function __construct(
		private readonly ProductModel $productModel,
		private readonly ProductRepository $productRepository,
		private readonly ProductLocalizationRepository $productLocalizationRepository,
		private readonly ProductFileRepository $productFileRepository,
		private readonly ProductVariantPriceRepository $productVariantPriceRepository,
		private readonly ProductTreeRepository $productTreeRepository,
	)
	{
	}


	public function clone(IEntity $entity, Mutation $targetMutation): IEntity
	{
		assert($entity instanceof ProductLocalization);

		$this->productRepository->setPublicOnly(false);
		$this->productLocalizationRepository->setPublicOnly(false);

		$this->productModel->normalizeProduct($entity->product);
		$newProductLocalization = $this->cloneLocalization($entity, $targetMutation);
		$this->cloneVariantLocalization($entity, $targetMutation);
		$this->cloneFiles($entity, $newProductLocalization);
		$this->cloneCategories($entity->product, $entity->getMutation(), $targetMutation);
		$this->productRepository->persistAndFlush($newProductLocalization->product);
		return $newProductLocalization;
	}

	private function cloneLocalization(ProductLocalization $entity, Mutation $targetMutation): ProductLocalization
	{
		$targetLocalization = $entity->getParent()->getLocalization($targetMutation);
		$targetLocalization = CopyMachine::makeShallowCopy($entity, $targetLocalization);
		//$targetLocalization = CopyMachine::trySetRawValues($entity, $targetLocalization, ['setup']);

		assert($targetLocalization instanceof ProductLocalization);
		$targetLocalization->public = 0;

		return $targetLocalization;
	}

	private function cloneVariantLocalization(ProductLocalization $entity, Mutation $targetMutation): void
	{
		foreach ($entity->getParent()->variants as $variant) {
			$sourceVariantLocalization = $variant->getLocalizationChecked($entity->getMutation());
			$targetVariantLocalization = $variant->getLocalizationChecked($targetMutation);

			$targetVariantLocalization = CopyMachine::makeShallowCopy($sourceVariantLocalization, $targetVariantLocalization);
			assert($targetVariantLocalization instanceof ProductVariantLocalization);

			$this->clonePrices($sourceVariantLocalization, $targetVariantLocalization);
		}
	}


	private function cloneFiles(ProductLocalization $entity, ProductLocalization $newProductLocalization): void
	{
		foreach ($entity->files as $productFile) {
			try {
				$newProductFile = $this->productFileRepository->getByChecked(['productLocalization' => $newProductLocalization, 'file' => $productFile->file]);
			} catch (NoResultException) {
				$newProductFile = Creator::createNewEntity($this->productFileRepository, $productFile::class);
			}

			$newProductFile = CopyMachine::makeShallowCopy($productFile, $newProductFile);

			assert($newProductFile instanceof ProductFile);
			$newProductFile->productLocalization = $newProductLocalization;
			$newProductFile->file = $productFile->file;

		}
	}

	private function clonePrices(ProductVariantLocalization $sourceVariantLocalization, ProductVariantLocalization $targetVariantLocalization): void
	{
		$productVariant = $sourceVariantLocalization->variant;
		foreach ($productVariant->prices as $price) {
			try {
				$newPrice = $this->productVariantPriceRepository->getByChecked([
					'productVariant' => $productVariant,
					'mutation' => $targetVariantLocalization->mutation,
				]);
			} catch (NoResultException) {
				$newPrice = Creator::createNewEntity($this->productVariantPriceRepository, $price::class);
			}

			$newPrice = CopyMachine::makeShallowCopy($price, $newPrice);
			assert($newPrice instanceof ProductVariantPrice);
			$newPrice->productVariant = $productVariant;
			$newPrice->productId = $productVariant->product->id;
			$newPrice->mutation = $targetVariantLocalization->mutation;
			$newPrice->priceLevel = $price->priceLevel;
			$newPrice->price = $price->price;
		}
	}

	private function cloneCategories(Product $product, Mutation $sourceMutation, Mutation $targetMutation): void
	{
		$productTrees = $this->productTreeRepository->findBy([
			'product' => $product,
			'tree->mutation' => $sourceMutation,
		]);
		foreach ($productTrees as $sourceProductTree) {
			$sourceProductCategory = $sourceProductTree->tree;
			$targetProductCategory = $sourceProductCategory->getParent()->getLocalization($targetMutation);

			assert($targetProductCategory instanceof CatalogTree);

			try {
				$newProductTree = $this->productTreeRepository->getByChecked([
					'product' => $product,
					'tree' => $targetProductCategory,
				]);
			} catch (NoResultException) {
				$newProductTree = Creator::createNewEntity($this->productVariantPriceRepository, ProductTree::class);
			}

			assert($newProductTree instanceof ProductTree);
			$newProductTree->product = $product;
			$newProductTree->tree = $targetProductCategory;
			$newProductTree->sort = $sourceProductTree->sort;

		}
	}

}
