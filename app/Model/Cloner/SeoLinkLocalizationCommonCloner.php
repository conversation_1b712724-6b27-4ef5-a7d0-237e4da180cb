<?php declare(strict_types = 1);

namespace App\Model\Cloner;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Translator;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalization;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalizationRepository;
use App\PostType\SeoLink\Model\Orm\SeoLinkRepository;
use Nextras\Orm\Entity\IEntity;

class SeoLinkLocalizationCommonCloner extends PostTypeCommonCloner implements CommonCloner
{

	public function __construct(
		private readonly SeoLinkLocalizationRepository $authorLocalizationRepository,
		private readonly SeoLinkRepository $seoLinkRepository,
		private readonly Translator $translator,
	)
	{
	}


	public function clone(IEntity $entity, Mutation $targetMutation): SeoLinkLocalization
	{
		assert($entity instanceof SeoLinkLocalization);
		$newSeoLinkLocalization = parent::clonePostType(
			$entity,
			$targetMutation,
			$this->authorLocalizationRepository,
			$this->seoLinkRepository,
			'seoLink'
		);

		assert($newSeoLinkLocalization instanceof SeoLinkLocalization);

		if ($targetMutation === $entity->getMutation()) {
			$newSeoLinkLocalization->name = sprintf('(%s) %s', $this->translator->translate('page_copy'), $newSeoLinkLocalization->name);
			$newSeoLinkLocalization->getParent()->parameterValues->set([]);
		}

		return $newSeoLinkLocalization;
	}

}
