<?php declare(strict_types = 1);

namespace App\Model\Cloner;

use App\Model\Orm\Mutation\Mutation;
use App\PostType\Author\Model\Orm\AuthorLocalization;
use App\PostType\Author\Model\Orm\AuthorLocalizationRepository;
use App\PostType\Author\Model\Orm\AuthorRepository;
use Nextras\Orm\Entity\IEntity;

class AuthorLocalizationCommonCloner extends PostTypeCommonCloner implements CommonCloner
{

	public function __construct(
		private readonly AuthorLocalizationRepository $authorLocalizationRepository,
		private readonly AuthorRepository $authorRepository,
	)
	{
	}


	public function clone(IEntity $entity, Mutation $targetMutation): AuthorLocalization
	{
		assert($entity instanceof AuthorLocalization);
		$newAuthorLocalization = parent::clonePostType(
			$entity,
			$targetMutation,
			$this->authorLocalizationRepository,
			$this->authorRepository,
			'author'
		);

		assert($newAuthorLocalization instanceof AuthorLocalization);

		$newAuthorLocalization->public = false;

		return $newAuthorLocalization;
	}

}
