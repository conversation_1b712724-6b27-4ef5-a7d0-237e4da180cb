<?php declare(strict_types = 1);

namespace App\Model\Cloner;

use App\Model\Orm\EsIndex\EsIndex;
use App\Model\Orm\EsIndex\EsIndexModel;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Orm\Mutation\Mutation;
use Nextras\Orm\Entity\IEntity;

class EsIndexCommonCloner implements CommonCloner
{

	public function __construct(
		private readonly EsIndexModel $esIndexModel,
		private readonly EsIndexRepository $esIndexRepository,
	)
	{
	}


	public function clone(IEntity $entity, Mutation $targetMutation): EsIndex
	{
		assert($entity instanceof EsIndex);
		$targetEntity = $this->esIndexRepository->getBy([
			'type' => $entity->type,
			'mutation' => $targetMutation,
		]);

		if ($targetEntity !== null) {
			$this->esIndexModel->deleteIndex($targetEntity);
		}

		$newEsIndex = $this->esIndexModel->creteNewWithIndex($entity->type, $targetMutation);
		//$this->esIndexModel->switchIndex($newEsIndex);
		return $newEsIndex;
	}

}
