<?php declare(strict_types = 1);

namespace App\Model\Cloner;

use App\Model\Orm\CopyMachine;
use App\Model\Orm\Creator;
use App\Model\Orm\Mutation\Mutation;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\SeoLink\Model\Orm\SeoLink;
use Nextras\Orm\Entity\IEntity;
use Nextras\Orm\Exception\NoResultException;
use Nextras\Orm\Repository\IRepository;

class PostTypeCommonCloner
{

	public function clonePostType(LocalizationEntity $entity, // @phpstan-ignore-line
		Mutation $targetMutation,
		IRepository $repository,
		IRepository $parentRepository,
		string $parentName): IEntity
	{
		try {
			if ($targetMutation !== $entity->getMutation()) {
				$newLocalizationEntity = $repository->getByChecked([$parentName => $entity->$parentName, 'mutation' => $targetMutation]);
			} else {
				// same source & target mutations --> make new duplicate
				$newLocalizationEntity = Creator::createNewEntity($repository, $entity::class);
			}
		} catch (NoResultException) {
			$newLocalizationEntity = Creator::createNewEntity($repository, $entity::class);
		}

		assert($newLocalizationEntity instanceof IEntity
			&& $entity instanceof IEntity);


		$newLocalizationEntity = CopyMachine::makeShallowCopy($entity, $newLocalizationEntity);
		assert($newLocalizationEntity instanceof LocalizationEntity
			&& $newLocalizationEntity instanceof IEntity
			&& $entity instanceof IEntity);


		if ($targetMutation !== $entity->getMutation()) {
			$newParent = $entity->getParent();
		} else {
			$parent = $entity->getParent();
			$newParent = Creator::createNewEntity($parentRepository, $parent::class);

			assert($parent instanceof IEntity);
			$newParent = CopyMachine::makeShallowCopy($parent, $newParent);
			assert($newParent instanceof ParentEntity);
		}

		$newLocalizationEntity->setParent($newParent);
		$newLocalizationEntity->setMutation($targetMutation);

		return $newLocalizationEntity;
	}

}
