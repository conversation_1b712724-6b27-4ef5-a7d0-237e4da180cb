<?php declare(strict_types = 1);

namespace App\Model\Cloner;

use App\Model\Orm\Mutation\Mutation;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalization;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalizationRepository;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagRepository;
use Nextras\Orm\Entity\IEntity;

class BlogTagLocalizationCommonCloner extends PostTypeCommonCloner implements CommonCloner
{

	public function __construct(
		private readonly BlogTagLocalizationRepository $blogTagLocalizationRepository,
		private readonly BlogTagRepository $blogTagRepository,
	)
	{}


	public function clone(IEntity $entity, Mutation $targetMutation): BlogTagLocalization
	{
		assert($entity instanceof BlogTagLocalization);
		$newBlogTagLocalization = parent::clonePostType($entity,
			$targetMutation,
			$this->blogTagLocalizationRepository,
			$this->blogTagRepository,
			'blogTag');
		assert($newBlogTagLocalization instanceof BlogTagLocalization);

		$newBlogTagLocalization->public = false;

		return $newBlogTagLocalization;
	}

}
