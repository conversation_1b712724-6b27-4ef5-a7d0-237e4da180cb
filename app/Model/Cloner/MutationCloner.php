<?php declare(strict_types = 1);

namespace App\Model\Cloner;

use App\Model\Orm\CopyMachine;
use App\Model\Orm\Creator;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Mutation\MutationRepository;
use Nextras\Orm\Exception\NoResultException;

class MutationCloner
{

	public function __construct(
		private readonly MutationRepository $mutationRepository,
	)
	{
	}

	public function clone(string $langCode, Mutation $sourceMutation): Mutation
	{
		try {
			$targetMutation = $this->mutationRepository->getByCode($langCode);
		} catch (NoResultException) {
			$targetMutation = Creator::createNewEntity($this->mutationRepository, Mutation::class);
		}

		$targetMutation = CopyMachine::makeShallowCopy($sourceMutation, $targetMutation);

		assert($targetMutation instanceof Mutation);
		$targetMutation->langCode = $langCode;
		$targetMutation->name = sprintf('(copy) %s', $sourceMutation->name);
		$targetMutation->langMenu = ($sourceMutation->langMenu !== null) ? sprintf('(copy) %s', $sourceMutation->langMenu) : null;

		$targetMutation->synonyms = $sourceMutation->synonyms;
		$targetMutation->currency = $sourceMutation->currency;
		$this->mutationRepository->persistAndFlush($targetMutation);
		return $targetMutation;
	}

}
