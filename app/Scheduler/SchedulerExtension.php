<?php
declare(strict_types=1);

namespace App\Scheduler;

use App\Scheduler\Handler\ServiceCallMessageHandler;
use Contributte\Console\Application;
use Contributte\Messenger\Container\NetteContainer;
use Contributte\Messenger\Container\ServiceProviderContainer;
use Nette;
use Nette\DI\CompilerExtension;
use Nette\DI\Definitions\Definition;
use Nette\DI\Definitions\Statement;
use Nette\Schema\Expect;
use Predis\Client;
use ReflectionClass;
use ReflectionException;
use stdClass;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Messenger\RunCommandMessage;
use Symfony\Component\Console\Messenger\RunCommandMessageHandler;
use Symfony\Component\Lock\LockFactory;
use Symfony\Component\Lock\Store\StoreFactory;
use Symfony\Component\Messenger\Handler\RedispatchMessageHandler;
use Symfony\Component\Messenger\Message\RedispatchMessage;
use Symfony\Component\Scheduler\Attribute\AsCronTask;
use Symfony\Component\Scheduler\Attribute\AsPeriodicTask;
use Symfony\Component\Scheduler\Attribute\AsSchedule;
use Symfony\Component\Scheduler\Command\DebugCommand;
use Symfony\Component\Scheduler\Generator\MessageGenerator;
use Symfony\Component\Scheduler\Messenger\SchedulerTransport;
use Symfony\Component\Scheduler\Messenger\SchedulerTransportFactory;
use Symfony\Component\Scheduler\Messenger\ServiceCallMessage;
use Symfony\Component\Scheduler\RecurringMessage;
use Symfony\Component\Scheduler\Schedule;

/**
 * @property-read stdClass $config
 */
class SchedulerExtension extends CompilerExtension
{

	private array $scheduleProviderIds = [];

	private array $providers = [];

	private array $attrMap = [];

	private array $allowedAttrs = [AsCommand::class, AsSchedule::class, AsCronTask::class, AsPeriodicTask::class];

	public function getConfigSchema(): Nette\Schema\Schema
	{
		return Expect::structure([
			'consoleMode' => Expect::bool()->default(false),
			'options' => Expect::arrayOf(Expect::structure([
				'lock' => Expect::anyOf(Expect::string(), Expect::type(Client::class)),
				'stateful' => Expect::structure([
					'adapter' => Expect::string(),
					'dsn' => Expect::anyOf(Expect::string(), Expect::type(Client::class)),
				]),
			])),
		]);
	}

	public function beforeCompile(): void
	{
		if (!$this->config->consoleMode) {
			return;
		}

		$this->mapAttributes();
		$this->registerCommands();

		$this->prepareSchedules(); // TODO: Own schedules
		$this->prepareServices();

		// add tasks definitions from DI container by tags
		$this->addTasks();

		// add missing handlers
		$this->addHandlers();

		// add ServiceProvider, DebugCommand etc.
		$this->addOthers();
	}

	private function mapAttributes(): void
	{
		foreach (get_declared_classes() as $class) {
			try {
				$rc = new ReflectionClass($class);
				foreach ($this->allowedAttrs as $attr) {
					$attrs = $rc->getAttributes($attr);
					if ($attrs === []) {
						continue;
					}
					foreach ($attrs as $attribute) {
						$this->attrMap[$attr][$rc->getName()][] = $attribute->newInstance();
					}

				}
			} catch (ReflectionException $e) {
				// do nothing
			}
		}
	}

	private function addDependencies(string $name, Definition $definition): void
	{
		$builder = $this->getContainerBuilder();
		$builder->addDefinition($this->prefix('message.generator.' . $name))
			->setFactory(MessageGenerator::class)
			->setAutowired(false)
			->setArguments([$definition, $name]);

		$builder->addDefinition($this->prefix('messenger.transport.' . $name))
			->setFactory(SchedulerTransport::class)
			->setArgument('messageGenerator', '@scheduler.message.generator.' . $name)
			->addTag('contributte.messenger.transport', 'scheduler_' . $name);
				//->addTag('contributte.messenger.failure_transport', 'failure');

		if (($statefulAdapter = $this->config->options[$name]->stateful->adapter ?? false) && ($stateful = $this->config->options[$name]->stateful->dsn ?? false)) {
			$statefulArgument = new Statement([$statefulAdapter, 'createConnection'], [$stateful]);
			if (str_starts_with($stateful, '@')) {
				$statefulArgument = new Statement($stateful);
			}
			$builder->addDefinition($this->prefix('symfony.cache.adapter.' . $name))
				->setType($statefulAdapter)
				->setArguments([
						$statefulArgument,
					])->setAutowired(false);
		}

		if (($lock = $this->config->options[$name]->lock ?? false)) {
			$lockArgument = $lock;
			if (str_starts_with($lock, '@')) {
				$lockArgument = new Statement($lock);
			}

			$builder->addDefinition($this->prefix('symfony.lockFactory.' . $name))
				->setType(LockFactory::class)
				->setArguments([
						new Statement([StoreFactory::class, 'createStore'], [$lockArgument]),
					])->setAutowired(false);
		}
	}
	private function prepareSchedules(): void
	{
		$builder = $this->getContainerBuilder();
		if (isset($this->attrMap[AsSchedule::class])) {
			foreach ($this->attrMap[AsSchedule::class] as $class => $attr) {
				foreach ($attr as $attribute) {
					$def = $builder->addDefinition($this->prefix('schedule_provider.' . $attribute->name))
						->setType($class)
						->setAutowired(false)
						->addTag('scheduler.schedule_provider', ['name' => $attribute->name]);

					$this->scheduleProviderIds[$attribute->name] = $def->getName();
					$this->providers[$attribute->name] = '@' . $def->getName();
				}
			}
		}

		$builder->addDefinition($this->prefix('messenger.transportFactory.schedule'))
			->setAutowired(false)
			->setFactory(SchedulerTransportFactory::class)
			->addTag('contributte.messenger.transport_factory', 'schedule')
			->setArguments(['@scheduler.providerContainer']);
	}

	public function addTasks(): void
	{
		$builder = $this->getContainerBuilder();
		$tasksPerSchedule = [];
		$i = 0;
		foreach ($builder->findByTag('scheduler.task') as $serviceId => $tagAttributesArray) {
			foreach ($tagAttributesArray as $tagAttributes) {
				$serviceDefinition = $builder->getDefinition($serviceId);
				$scheduleName      = $tagAttributes['schedule'] ?? 'default';

				if ($serviceDefinition->getTag('console.command') !== null) {
					$message = $builder->addDefinition($this->prefix('message.' . $i . '.' . $scheduleName))
						->setType(RunCommandMessage::class)
						->setArgument(
							'input',
							$serviceDefinition->getType()::getDefaultName() . (empty($tagAttributes['arguments']) ? '' : ' ' . $tagAttributes['arguments'])
						);
				} else {
					$message = $builder->addDefinition($this->prefix('message.' . $i . '.' . $scheduleName))
						->setType(ServiceCallMessage::class)
						->setArguments([
										   $serviceId,
										   $tagAttributes['method'] ?? '__invoke',
										   (array) ($tagAttributes['arguments'] ?? []),
									   ]);
				}

				if ($tagAttributes['transports'] ?? null) {
					$message = $builder->addDefinition($this->prefix('message.' . $i . '.' . $scheduleName . '.redispatch'))
						->setType(RedispatchMessage::class)
						->setArguments([$message, $tagAttributes['transports']]);
				}

				$filteredTaskArguments = array_filter(match ($tagAttributes['trigger'] ?? throw new Nette\InvalidArgumentException(sprintf(
					'Tag "scheduler.task" is missing attribute "trigger" on service "%s".',
					$serviceId
				))) {
					'every' => [
						'frequency' => $tagAttributes['frequency'] ?? throw new Nette\InvalidArgumentException(sprintf(
							'Tag "scheduler.task" is missing attribute "frequency" on service "%s".',
							$serviceId
						)),
						'from'      => $tagAttributes['from'] ?? null,
						'until'     => $tagAttributes['until'] ?? null,
					],
					'cron' => [
						'expression' => $tagAttributes['expression'] ?? throw new Nette\InvalidArgumentException(sprintf(
							'Tag "scheduler.task" is missing attribute "expression" on service "%s".',
							$serviceId
						)),
						'timezone'   => $tagAttributes['timezone'] ?? null,
					],
					default => throw new Nette\InvalidArgumentException('Unexpected trigger.')
				}, fn($value) => $value !== null);

				$taskArguments = ['message' => $message] + $filteredTaskArguments;

				$statement = new Statement([RecurringMessage::class, $tagAttributes['trigger']], $taskArguments);

				$tasksPerSchedule[$scheduleName][] = $taskDefinition = $builder->addDefinition($this->prefix('task.' . $i . '.' . $scheduleName))->setFactory($statement); //->setArguments($taskArguments);

				if ($tagAttributes['jitter'] ?? false) {
					$taskDefinition->addSetup('withJitter', [$tagAttributes['jitter']]);
				}

				$i++;
			}
		}

		foreach ($tasksPerSchedule as $scheduleName => $tasks) {
			$id = 'scheduler.schedule_provider.' . $scheduleName;
			$schedule = $builder->addDefinition($id . '_final')->setType(Schedule::class)->addSetup('add', $tasks);
			$this->providers[$scheduleName] = '@' . $schedule->getName();

			if (isset($this->scheduleProviderIds[$scheduleName])) {
				throw new Nette\NotImplementedException('This feature (own schedules with attribute #AsSchedule and schedule name \'' . $scheduleName . '\') is not implemented yet.');
			} else {
				$schedule->addTag('scheduler.schedule_provider', ['name' => $scheduleName]);
				$this->scheduleProviderIds[$scheduleName] = $id;
			}

			$this->addDependencies((string) $scheduleName, $schedule);

			if ($builder->hasDefinition($this->prefix('symfony.cache.adapter.' . $scheduleName))) {
				$schedule->addSetup('stateful', ['@' . $this->prefix('symfony.cache.adapter.' . $scheduleName)]);
			}

			if ($builder->hasDefinition($this->prefix('symfony.lockFactory.' . $scheduleName))) {
				$schedule->addSetup('lock', [new Statement(['@' . $this->prefix('symfony.lockFactory.' . $scheduleName), 'createLock'], [$scheduleName])]);
			}

			if ($builder->hasDefinition($id)) {
				$builder->removeDefinition($id);
				$builder->resolve();
			}

			$builder->addAlias($id, $id . '_final');
		}

		$builder->addDefinition($this->prefix('providerContainer'))
			->setFactory(NetteContainer::class)
			->setArgument('map', array_values($this->providers));
	}

	private function registerCommands(): void
	{
		$builder = $this->getContainerBuilder();

		foreach ($builder->findByType(Command::class) as $command) {
			$command->addTag('console.command');
		}
	}

	private function prepareServices(): void
	{
		$builder = $this->getContainerBuilder();

		foreach (array_merge($this->attrMap[AsPeriodicTask::class] ?? [], $this->attrMap[AsCronTask::class] ?? []) as $class => $tasks) {
			if (($def = $builder->getByType($class)) !== null) {
				foreach ($tasks as $task) {
					$trigger = match ($task::class) {
						AsPeriodicTask::class => 'every',
						AsCronTask::class => 'cron',
						default => throw new Nette\InvalidArgumentException(sprintf('Task "%s" is not implemented yet.', $task::class)),
					};

					$serviceDef = $builder->getDefinition($def);

					$tagAttributes = [];
					if (($tagHasAttributes = $serviceDef->getTag('scheduler.task')) !== null) {
						$tagAttributes = $tagHasAttributes;
					}

					$tagAttributes[] = get_object_vars($task) + ['trigger' => $trigger];
					$serviceDef->addTag('scheduler.task', $tagAttributes);
				}
			}
		}
	}

	private function addHandlers(): void
	{
		$builder = $this->getContainerBuilder();
		if ($builder->findByType(Application::class) !== []) {
			$builder->addDefinition($this->prefix('runCommandMessageHandler'))->setType(RunCommandMessageHandler::class)->addTag('contributte.messenger.handler');
			// $builder->addDefinition($this->prefix('runCommandMessageHandler'))->setType(\App\Scheduler\Handler\RunCommandMessageHandler::class)->addTag('contributte.messenger.handler');
		}
		$builder->addDefinition($this->prefix('serviceCallMessageHandler'))->setType(ServiceCallMessageHandler::class)->addTag('contributte.messenger.handler');
		$builder->addDefinition($this->prefix('redispatchMessageHandler'))->setType(RedispatchMessageHandler::class)->addTag('contributte.messenger.handler');
	}

	private function addOthers(): void
	{
		$builder = $this->getContainerBuilder();
		$builder->addDefinition($this->prefix('serviceProvider'))
			->setAutowired(false)
			->setFactory(ServiceProviderContainer::class)
			->setArguments([$this->scheduleProviderIds]);

		$builder->addDefinition($this->prefix('command.debug'))
			->setFactory(DebugCommand::class)
			->setArguments(['@' . $this->prefix('serviceProvider')])->addTag('console.command');
	}

}
