<?php

namespace App\Scheduler\Handler;

use App\Scheduler\Message\Test;
use App\Utils\DateTime;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
class TestHandler
{

	public function __construct()
	{
	}

	public function __invoke(Test $message): void
	{
		$path = LOG_DIR . '/crontest.log';
		dump((new DateTime())->format('Y-m-d H:i:s'));
		file_put_contents($path, (new DateTime())->format('Y-m-d H:i:s') . ($message->type !== null ? ' / ' . $message->type : '') . "\n", FILE_APPEND);
	}

}
