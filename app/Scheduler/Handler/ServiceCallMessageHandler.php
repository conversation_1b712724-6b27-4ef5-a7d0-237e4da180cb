<?php

declare(strict_types=1);

namespace App\Scheduler\Handler;

use Nette\DI\Container;
use Symfony\Component\Scheduler\Messenger\ServiceCallMessage;

readonly class ServiceCallMessageHandler
{

	public function __construct(private Container $serviceLocator)
	{
	}

	public function __invoke(ServiceCallMessage $message): void
	{
		$this->serviceLocator->getService($message->getServiceId())->{$message->getMethod()}(...$message->getArguments());
	}

}
