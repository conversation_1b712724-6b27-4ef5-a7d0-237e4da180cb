<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="consumer03" type="PhpLocalRunConfigurationType" factoryName="PHP Console" folderName="messenger" path="$PROJECT_DIR$/bin/console" scriptParameters="messenger:consume importStockFront importPriceFront importProductFront importOrderFront importProductImagesFront importCouponFront importCustomerFront failure">
    <CommandLine>
      <envs>
        <env name="MESSENGER_CONSUMER_NAME" value="03" />
      </envs>
    </CommandLine>
    <method v="2" />
  </configuration>
</component>
